#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : model.py
@Time    : 2025/7/28 17:18
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 定义模型结构
"""
from pydantic import BaseModel, Field


class MapDRModel(BaseModel):
    """规则元数据模型"""
    score: float = Field(..., description="变更点与测试大观点及DR类型的匹配得分")
    reason: str = Field(..., description="测试大观点及DR对应类型的匹配原因")


class FindFunctionModel(BaseModel):
    """规则元数据模型"""
    function_point: list[str] = Field(..., description="变更点中抽取到的功能点列表")
    change_content: str = Field(..., description="变更点的变更内容")
    change_type: str = Field(..., description="变更点的变更类型")
