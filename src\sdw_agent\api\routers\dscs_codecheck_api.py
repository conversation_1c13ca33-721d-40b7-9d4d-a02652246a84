"""
設計基準CS(コードチェック)工作流
"""
from fastapi import APIRouter
from loguru import logger
from sdw_agent.model.request_model import DscsCodeCheckRequest
from sdw_agent.model.response_model import DscsCodeCheckResponse
from sdw_agent.service.dscs_codecheck.workflow_dscs_codecheck import do_dscs_codecheck


# 路由配置
router = APIRouter(prefix="/api/sdw/dscs_codecheck", tags=["設計基準CS(コードチェック)"])


@router.post("/update",
             summary="设计基准CS(コードチェック)",
             description="基于设计基准CS(コードチェック)工作流生成检查成果物",
             response_description="",
             response_model=DscsCodeCheckResponse)
async def parse_guideline_file(request: DscsCodeCheckRequest):
    try:
        repo_path = request.repo.repo_path
        commit_id = request.repo.commit_id
        commit_id_base = request.repo.compared_commit_id
        
        if not repo_path or not repo_path.strip():
            return DscsCodeCheckResponse(
                code=1,
                msg="失败：仓库路径不能为空",
                data=[]
            )
        
        output_paths = do_dscs_codecheck(repo_path, commit_id, commit_id_base)
        return DscsCodeCheckResponse(
            code=0,
            msg="新变表更新成功",
            data=output_paths  # 直接返回路径列表
        )
    except Exception as e:
        logger.error(f"代码差分生成失败: {str(e)}")
        return DscsCodeCheckResponse(
            code=1,
            msg=f"新变表更新失败: {str(e)}",
            data=[]
        )
