"""
更新Excel文件中指定行的单元格值，并将修改的单元格设置为蓝色背景
主入口是 update_excel_cells
"""

import os
import traceback

from openpyxl.utils import get_column_letter
from sdw_agent.util.excel_util import check_file_exists


def _get_excel_processes():
    """
    获取所有Excel进程
    返回:
        list: Excel进程列表
    """
    try:
        import psutil
        excel_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == 'excel.exe':
                    excel_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return excel_processes
    except ImportError:
        return []


def _terminate_excel_processes_gracefully(excel_processes, timeout=3):
    """
    优雅地关闭Excel进程
    参数:
        excel_processes (list): Excel进程列表
        timeout (int): 等待超时时间
    """
    import time

    if not excel_processes:
        return

    print(f"发现 {len(excel_processes)} 个Excel进程，正在优雅关闭...")
    for proc in excel_processes:
        try:
            proc.terminate()
            if timeout > 0:
                proc.wait(timeout=timeout)
        except:
            pass

    time.sleep(1)


def _force_kill_excel_processes():
    """
    强制关闭Excel进程
    """
    try:
        import subprocess
        subprocess.run(['taskkill', '/f', '/im', 'excel.exe'],
                       capture_output=True, text=True, timeout=10)
    except:
        pass


def _configure_excel_app(excel_app, silent_mode=False):
    """
    配置Excel应用程序的通用设置
    参数:
        excel_app: Excel应用程序对象
        silent_mode (bool): 是否启用静默模式
    """
    try:
        excel_app.Visible = False
        excel_app.DisplayAlerts = False
        excel_app.EnableEvents = False  # 禁用事件，防止VBA脚本执行
        excel_app.ScreenUpdating = False  # 禁用屏幕更新，提高性能

        if silent_mode:
            try:
                excel_app.Interactive = False
                excel_app.UserControl = False
            except:
                pass

        # 尝试设置计算模式
        try:
            excel_app.Calculation = -4105  # xlCalculationManual - 手动计算模式
        except Exception:
            # 某些Excel版本或安全设置不允许修改计算模式，这是正常的，不影响功能
            pass

    except Exception as e:
        print(f"配置Excel应用程序失败: {e}")


def _check_file_lock_status(file_path):
    """
    检查文件是否被其他进程锁定
    返回:
        tuple: (is_locked, lock_info)
    """
    try:
        import psutil

        # 检查是否有Excel进程正在使用该文件
        excel_processes = _get_excel_processes()
        if excel_processes:
            print(f"检测到 {len(excel_processes)} 个Excel进程正在运行")

        # 尝试以独占模式打开文件来检测锁定状态
        try:
            with open(file_path, 'r+b') as f:
                pass
            return False, "文件未被锁定"
        except (PermissionError, OSError) as e:
            return True, f"文件被锁定: {e}"

    except Exception as e:
        return False, f"检查锁定状态时出错: {e}"


def _open_workbook_safely(excel_app, file_path):
    """
    安全地打开Excel工作簿，处理文件锁定情况
    参数:
        excel_app: Excel应用程序对象
        file_path (str): 文件路径
    返回:
        workbook: 工作簿对象
    """
    # 检查文件锁定状态
    is_locked, lock_info = _check_file_lock_status(file_path)
    if is_locked:
        print(f"警告: {lock_info}")
        print("尝试强制打开文件...")

    try:
        # 首先尝试正常打开
        workbook = excel_app.Workbooks.Open(
            os.path.abspath(file_path),
            UpdateLinks=0,  # 不更新链接
            ReadOnly=False,  # 非只读模式
            Format=None,  # 自动检测格式
            Password="",  # 空密码
            WriteResPassword="",  # 空写入密码
            IgnoreReadOnlyRecommended=True,  # 忽略只读建议
            Origin=None,  # 自动检测编码
            Delimiter=None,  # 分隔符
            Editable=False,  # 不可编辑（对于链接）
            Notify=False,  # 不通知
            Converter=None,  # 转换器
            AddToMru=False,  # 不添加到最近使用
            Local=False,  # 不使用本地设置
            CorruptLoad=0  # 正常加载
        )

        # 关键检查：验证文件是否真的可以写入
        if workbook.ReadOnly:
            workbook.Close(SaveChanges=False)
            raise Exception(f"文件被其他程序占用，只能以只读模式打开: {file_path}")

        # 额外验证：尝试一个小的测试写入来确认可写性
        try:
            # 保存一次来测试写入权限（不做任何修改）
            workbook.Save()
            print("文件写入权限验证成功")
        except Exception as save_error:
            workbook.Close(SaveChanges=False)
            raise Exception(f"文件无法保存，可能被其他程序锁定: {save_error}")

        return workbook

    except Exception as e:
        print(f"打开文件失败: {e}")
        # 检查是否是COM错误
        if "com_error" in str(type(e)) or "pywintypes" in str(type(e)):
            raise Exception(f"文件被其他程序占用或损坏，无法打开: {e}")
        else:
            raise Exception(f"无法以可写模式打开文件: {e}。请确保文件未被其他程序占用。")


def _cleanup_excel_resources(excel_app, workbook):
    """
    清理Excel资源
    参数:
        excel_app: Excel应用程序对象
        workbook: 工作簿对象
    """
    import time
    import gc

    # 清理工作簿
    if workbook is not None:
        try:
            workbook.Close(SaveChanges=False)
        except Exception as e:
            print(f"关闭工作簿时出错: {e}")
        except:
            # 捕获所有异常，包括COM异常
            pass

    # 清理Excel应用程序
    if excel_app is not None:
        try:
            excel_app.Quit()
        except Exception as e:
            print(f"退出Excel应用时出错: {e}")
        except:
            # 捕获所有异常，包括COM异常
            pass

    # 强制垃圾回收
    try:
        gc.collect()
        time.sleep(0.3)
    except:
        pass


def _update_cells_with_win32com(worksheet, row_updates):
    """
    使用win32com更新单元格的通用函数
    参数:
        worksheet: 工作表对象
        row_updates (dict): 要更新的数据
    返回:
        int: 更新的单元格数量
    """
    total_cells_updated = 0
    for row_number, row_data in row_updates.items():
        for col_identifier, value in row_data.items():
            try:
                # 处理列号
                if isinstance(col_identifier, str):
                    col_letter = col_identifier
                else:
                    col_letter = get_column_letter(col_identifier)

                # 构建单元格地址
                cell_address = f"{col_letter}{row_number}"
                cell = worksheet.Range(cell_address)

                # 设置值
                cell.Value = value

                # 设置蓝色背景
                cell.Interior.Color = 15128749  # RGB(173, 216, 230)

                total_cells_updated += 1
            except Exception as e:
                print(f"更新单元格 ({row_number}, {col_identifier}) 时出错: {e}")
                continue

    return total_cells_updated


def _save_and_verify_workbook(workbook, file_path):
    """
    保存工作簿并验证保存是否成功
    参数:
        workbook: Excel工作簿对象
        file_path: 文件路径
    """
    import os
    import time

    try:
        # 获取保存前的文件修改时间
        original_mtime = os.path.getmtime(file_path) if os.path.exists(file_path) else 0

        # 保存文件
        print("正在保存文件...")
        workbook.Save()

        # 等待文件系统更新
        time.sleep(0.5)

        # 验证文件是否真正被保存
        if os.path.exists(file_path):
            new_mtime = os.path.getmtime(file_path)
            if new_mtime > original_mtime:
                print("文件保存成功，修改时间已更新")
            else:
                print("警告: 文件修改时间未更新，保存可能未生效")
        else:
            raise Exception("保存后文件不存在")

    except Exception as e:
        print(f"保存文件时出错: {e}")
        # 尝试另存为
        try:
            print("尝试另存为...")
            backup_path = file_path.replace('.xlsx', '_backup.xlsx').replace('.xlsm', '_backup.xlsm')
            workbook.SaveAs(backup_path)
            print(f"文件已另存为: {backup_path}")
        except Exception as e2:
            raise Exception(f"保存和另存为都失败: {e}, {e2}")


def _handle_file_in_use_scenario(file_path: str, auto_cleanup: bool = True):
    """
    处理文件正在使用的情况
    参数:
        file_path: 文件路径
        auto_cleanup: 是否自动清理Excel进程
    返回:
        bool: 是否成功处理
    """
    print("检测到文件可能正在被其他程序使用")

    # 获取Excel进程
    excel_processes = _get_excel_processes()
    if excel_processes:
        print(f"发现 {len(excel_processes)} 个Excel进程")

        if auto_cleanup:
            print("自动清理Excel进程...")
            try:
                cleanup_excel_processes(force_cleanup=True)
                print("Excel进程已清理")

                # 等待一段时间让文件锁定释放
                import time
                time.sleep(2)

                # 再次检查文件锁定状态
                is_locked, _ = _check_file_lock_status(file_path)
                if not is_locked:
                    print("文件锁定已释放，可以继续操作")
                    return True
                else:
                    print("文件仍然被锁定")
                    return False

            except Exception as e:
                print(f"清理Excel进程失败: {e}")
                return False

    print("未发现Excel进程，文件可能被其他程序占用")
    return False


def update_excel_cells_with_win32com_silent(file_path: str, sheet_name: str, row_updates: dict):
    """
    使用win32com静默更新Excel文件，完全禁用VBA脚本执行
    这种方法专门用于包含VBA宏的文件，避免触发脚本错误
    """
    try:
        import win32com.client
        import pythoncom

        # 检查文件是否存在
        check_file_exists(file_path)

        # 检查文件锁定状态
        is_locked, lock_info = _check_file_lock_status(file_path)
        if is_locked:
            print(f"文件锁定检测: {lock_info}")
            if not _handle_file_in_use_scenario(file_path):
                raise Exception("文件被其他程序占用，无法进行修改")

        # 初始化COM
        pythoncom.CoInitialize()

        excel_app = None
        workbook = None

        try:
            # 创建Excel应用程序对象
            excel_app = win32com.client.DispatchEx("Excel.Application")

            # 配置Excel应用程序（静默模式）
            _configure_excel_app(excel_app, silent_mode=True)

            # 打开工作簿（这里会进行严格的可写性检查）
            workbook = _open_workbook_safely(excel_app, file_path)

            print(f"文件成功以可写模式打开: {file_path}")

            # 获取工作表
            try:
                worksheet = workbook.Sheets[sheet_name]
            except:
                raise ValueError(f"工作表 '{sheet_name}' 不存在")

            # 批量更新单元格值并设置蓝色背景
            total_cells_updated = _update_cells_with_win32com(worksheet, row_updates)

            # 保存文件并验证
            _save_and_verify_workbook(workbook, file_path)
            print(f"已成功更新 {total_cells_updated} 个单元格（涉及 {len(row_updates)} 行）并保存文件: {file_path}")

        finally:
            # 清理资源
            _cleanup_excel_resources(excel_app, workbook)

    except ImportError:
        raise ImportError("win32com模块未安装，请使用 pip install pywin32 安装")
    except Exception as e:
        # 检查是否是COM相关错误
        error_type = str(type(e))
        if "com_error" in error_type or "pywintypes" in error_type:
            try:
                import pythoncom
                # 尝试获取COM错误的详细信息
                if hasattr(e, 'excepinfo') and e.excepinfo:
                    error_desc = e.excepinfo[2] if len(e.excepinfo) > 2 else str(e)
                    raise Exception(f"Excel文件被其他程序占用或无法访问: {error_desc}")
                else:
                    raise Exception(f"Excel COM操作失败，文件可能被占用: {e}")
            except ImportError:
                raise Exception(f"Excel操作失败，文件可能被其他程序占用: {e}")
        else:
            raise Exception(f"使用win32com静默更新Excel文件时发生错误: {e}")
    finally:
        # 最后的COM清理
        try:
            import pythoncom
            pythoncom.CoUninitialize()
        except:
            pass

        # 额外的清理延迟
        try:
            import time
            time.sleep(0.5)
        except:
            pass


def check_vba_macro_status(file_path: str) -> tuple[bool, str]:
    """
    检查Excel文件是否包含VBA宏，并返回相关信息

    返回:
        tuple[bool, str]: (是否包含宏, 信息描述)
    """
    try:
        if file_path.lower().endswith('.xlsm'):
            return True, "文件是宏启用的Excel文件(.xlsm)"
        elif file_path.lower().endswith('.xls'):
            return True, "文件可能包含宏(.xls格式)"
        else:
            return False, "文件不包含宏"
    except Exception as e:
        return False, f"检查宏状态时出错: {e}"


def cleanup_excel_processes(force_cleanup=False, cleanup_com=False):
    """
    统一的Excel进程清理函数
    参数:
        force_cleanup (bool): 是否强制清理
        cleanup_com (bool): 是否清理COM对象
    """
    import time

    try:
        excel_processes = _get_excel_processes()

        if excel_processes:
            # 优雅关闭
            _terminate_excel_processes_gracefully(excel_processes, timeout=3 if not force_cleanup else 0)

            # 检查残留进程
            remaining_processes = _get_excel_processes()
            if remaining_processes:
                print(f"强制关闭 {len(remaining_processes)} 个残留Excel进程...")
                _force_kill_excel_processes()
                time.sleep(1)

            print("Excel进程清理完成")
        else:
            print("未发现Excel进程")

        # 清理COM对象
        if cleanup_com:
            try:
                import pythoncom
                pythoncom.CoUninitialize()
            except:
                pass
            time.sleep(1)

    except Exception as e:
        print(f"清理Excel进程时出错: {e}")
        # 最后的备用方案
        try:
            import subprocess
            subprocess.run(['taskkill', '/f', '/im', 'excel.exe'],
                           capture_output=True, text=True)
            print("已尝试使用备用方案关闭所有Excel进程")
        except Exception as backup_error:
            print(f"备用方案也失败: {backup_error}")


def update_excel_cells(file_path: str, sheet_name: str, row_updates: dict):
    """
    更新Excel文件中指定行的单元格值，并将修改的单元格设置为蓝色背景
    优先使用win32com（不会丢失图片等内容），如果不可用则使用openpyxl

    特别针对包含VBA宏的Excel文件进行了优化，避免破坏宏脚本的正常运行

    参数:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称
        row_updates (dict): 要更新的数据，key为行号（从1开始），value为该行的更新字典
                           内层字典的key为列号（可以是数字从1开始，或字母如'A','B','C'等），value为要设置的值

    示例:
        update_excel_cells("test.xlsx", "Sheet1", {
            2: {1: "第2行第1列", "C": "第2行C列"},
            5: {"B": "第5行B列", 4: "第5行第4列"}
        })
    """
    # 检查VBA宏状态
    has_macro, macro_info = check_vba_macro_status(file_path)
    if has_macro:
        print(f"检测到VBA宏文件: {macro_info}")
        print("将使用VBA友好的更新模式，保护宏脚本不被破坏")

    # 对于包含宏的文件，优先使用静默模式
    if has_macro:
        print("使用win32com静默模式处理宏文件（避免VBA脚本干扰）")
        try:
            update_excel_cells_with_win32com_silent(file_path, sheet_name, row_updates)
            return
        except Exception as e:
            traceback.print_exc()
            print(f"win32com静默模式失败: {e}")

    # 无论成功还是失败，都进行强制清理（特别是在接口环境下）
    try:
        cleanup_excel_processes(force_cleanup=True, cleanup_com=True)
    except Exception as cleanup_error:
        print(f"最终清理时出错: {cleanup_error}")
