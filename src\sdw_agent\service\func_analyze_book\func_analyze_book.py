"""
函数分析书工作流 - 基于Git提交生成函数变更分析书
"""
import os
from typing import List, Optional
from loguru import logger

from sdw_agent.service import BaseWorkflow, register_workflow
from sdw_agent.config.env import ENV
from sdw_agent.service.func_analyze_book.models import (FunctionDetailData, FunctionAnalyzeBookConfig,
                                                        FunctionAnalyzeBookResult)
from sdw_agent.util.function_util import extract_function_body
from sdw_agent.service.func_analyze_book.util.function_parser_util import (
    parse_function_declaration, enhance_variable_analysis,
)

from sdw_agent.service.func_analyze_book.util.func_analyze_util import (
    FunctionAnalyzeUtils, GitDiffAnalyzer, FunctionRelationshipAnalyzer,
    ExcelBookUtils, parse_func_body, parse_func_return_variavle, parse_func_Process, parse_func_param, ComponentUtils
)


@register_workflow("func_analyze_book")
class FuncAnalyzeBookWorkflow(BaseWorkflow):
    """函数分析书工作流"""

    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.workflow_name = "func_analyze_book"
        self.workflow_description = "基于Git提交生成函数变更分析书"

    def validate_config(self, config: FunctionAnalyzeBookConfig) -> tuple[bool, str]:
        """验证配置参数"""
        try:
            if not config.code_path:
                return False, "代码路径不能为空"
            if not config.commit_id_after:
                return False, "目标提交ID不能为空"
            if not os.path.exists(config.code_path):
                return False, f"代码路径不存在: {config.code_path}"
            return True, ""
        except Exception as e:
            return False, f"配置验证失败: {str(e)}"

    def execute(self, config: FunctionAnalyzeBookConfig) -> FunctionAnalyzeBookResult:
        """执行函数分析书工作流"""
        result = FunctionAnalyzeBookResult()

        try:
            # 验证配置
            is_valid, error_msg = self.validate_config(config)
            if not is_valid:
                result.message = error_msg
                return result

            logger.info(f"开始执行函数分析书工作流: {config.code_path}")

            # 生成函数变更信息
            func_change_info_list = self._generate_change_info_service(config)
            result.func_change_info_list = func_change_info_list

            if not func_change_info_list:
                result.message = "两次提交之间没有函数变更信息"
                return result

            # 生成函数分析书
            func_book_url = self._generate_function_book(func_change_info_list)
            result.func_book_url = func_book_url

            result.success = True
            result.message = "函数分析书工作流执行成功"
            logger.success(f"函数分析书工作流执行完成: {func_book_url}")

        except Exception as e:
            logger.error(f"函数分析书工作流执行失败: {str(e)}")
            result.message = f"执行失败: {str(e)}"
            result.error_details = str(e)

        return result

    def _generate_change_info_service(self, config: FunctionAnalyzeBookConfig) -> List[FunctionDetailData]:
        """生成变更信息服务"""
        # 获取变更名称列表
        if config.commit_id_before:
            func_change_info_list = self._gen_change_name_service_new(config)
        else:
            func_change_info_list = self._gen_change_name_service(config)

        # 遍历变更名称列表，对每个变更详情进行解析
        for func_detail in func_change_info_list:
            # 生成变更详情
            self._gen_change_detail_service(func_detail)
            logger.info(f"解析函数 {func_detail.func_sign} 的详情完成")

            # 生成并记录子函数信息
            func_parents = FunctionRelationshipAnalyzer.gen_calling_func_service(
                func_detail, config.code_path, config.component_book_url
            )
            func_detail.func_parents = func_parents
            logger.info(f"{func_detail.func_name} func_parents : {func_parents}")

            # 确定并记录函数类型
            func_detail.functionType = FunctionAnalyzeUtils.get_function_open_type(
                config.code_path, func_detail.func_name
            )

        return func_change_info_list

    def _gen_change_detail_service(self, func_detail: FunctionDetailData):
        """生成变更详情服务"""
        # 从源文件中提取函数体
        function_body = extract_function_body(func_detail.func_file_name, func_detail.func_name)
        logger.info(f"函数 {func_detail.func_name} 的完整函数体：\n{function_body}")
        func_detail.func_body = function_body

        if function_body:
            # 使用函数解析工具解析函数
            variable_result = enhance_variable_analysis(func_detail.func_name, func_detail)
            func_detail.allVariables = variable_result
            logger.info(f"{func_detail.func_sign} 解析出来的参数 {variable_result}")

            func_ret_var = FunctionAnalyzeUtils.gen_func_return_varname(function_body)
            func_detail.return_var = func_ret_var

            func_desc = parse_func_body(func_detail.func_name, function_body)
            func_detail.func_desc = func_desc

            if func_detail.return_type:
                func_return_desc = parse_func_return_variavle(func_detail.func_name, function_body)
                func_detail.return_desc = func_return_desc

            func_process_uml = parse_func_Process(func_detail.func_name, function_body)
            func_detail.func_process = func_process_uml

            for param in func_detail.parameters:
                param.desc = parse_func_param(func_detail.func_name, function_body, param.name)
        else:
            logger.error("无法从源文件中提取函数体")

    def _gen_change_name_service(self, config: FunctionAnalyzeBookConfig) -> List[FunctionDetailData]:
        """生成变更名称服务（Gerrit方式）"""
        change_info = GitDiffAnalyzer.get_gerrit_diff_code(
            config.gerrit_url, config.commit_id_after, config.username, config.password
        )
        return self._process_change_info(change_info, config.code_path)

    def _gen_change_name_service_new(self, config: FunctionAnalyzeBookConfig) -> List[FunctionDetailData]:
        """生成变更名称服务（本地Git方式）"""
        change_info = GitDiffAnalyzer.get_local_diff_code(
            config.code_path, config.commit_id_before, config.commit_id_after
        )
        return self._process_change_info(change_info, config.code_path)

    def _process_change_info(self, change_info: dict, code_path: str) -> List[FunctionDetailData]:
        """处理变更信息"""
        func_change_info_list = []

        for key, value in change_info.items():
            if not value:
                continue

            for func in value:
                func_detail = FunctionDetailData()
                func_detail.func_name = func["function_name"].strip(' \r\n')
                func_detail.func_sign = func["function_sign"].strip(' \r\n')
                func_detail.func_file_name = os.path.join(code_path, os.path.normpath(key))

                func_sign_info = parse_function_declaration(func_detail.func_sign)
                func_detail.return_type = func_sign_info.return_type
                func_detail.parameters = func_sign_info.parameters
                func_change_info_list.append(func_detail)

                logger.info(f"新增变更函数{func_detail.func_sign}")
                logger.info(f"文件{os.path.basename(func_detail.func_file_name)}")

        return func_change_info_list

    def _generate_function_book(self, func_change_info_list: List[FunctionDetailData]) -> str:
        """生成函数分析书"""
        pre_book_url = ExcelBookUtils.get_sample_book_path()
        return ExcelBookUtils.write_function_book(pre_book_url, func_change_info_list)


class FuncAnalyzeBookService:
    """函数分析书服务"""

    def __init__(self):
        self.workflow = FuncAnalyzeBookWorkflow()

    def gen_function_book_multi_commit_service(
            self,
            code_path: str,
            commit_id_after: str,
            commit_id_before: str,
            component_book_url: str
    ) -> str:
        """生成多提交函数分析书"""
        config = FunctionAnalyzeBookConfig(
            code_path=code_path,
            commit_id_after=commit_id_after,
            commit_id_before=commit_id_before,
            component_book_url=component_book_url
        )

        result = self.workflow.execute(config)

        if not result.success:
            raise Exception(f"函数分析失败: {result.message}")

        return result.func_book_url

    def gen_function_book_service(
            self,
            code_path: str,
            commit_id: str,
            component_book_url: str,
            gerrit_url: str
    ) -> str:
        """生成单提交函数分析书"""
        username = ENV.config.gerrit.username
        password = ENV.config.gerrit.password

        config = FunctionAnalyzeBookConfig(
            code_path=code_path,
            commit_id_after=commit_id,
            component_book_url=component_book_url,
            gerrit_url=gerrit_url,
            username=username,
            password=password
        )

        result = self.workflow.execute(config)

        if not result.success:
            raise Exception(f"函数分析失败: {result.message}")

        return result.func_book_url

    def get_function_analyze_book_service(
            self,
            code_path: str,
            commit_id: str,
            component_book_url: str,
            git_branch: str = 'develop'
    ) -> str:
        """获取函数分析书服务"""
        gerrit_url = ENV.config.gerrit.host
        return self.gen_function_book_service(code_path, commit_id, component_book_url, gerrit_url)


# 向后兼容的函数接口
def gen_function_book_multi_commit_service(code_path: str, commit_id_after: str, commit_id_before: str,
                                           component_book_url: str) -> str:
    """向后兼容接口"""
    service = FuncAnalyzeBookService()
    return service.gen_function_book_multi_commit_service(code_path, commit_id_after, commit_id_before,
                                                          component_book_url)


def gen_function_book_service(code_path: str, commit_id: str, component_book_url: str, gerrit_url: str) -> str:
    """向后兼容接口"""
    service = FuncAnalyzeBookService()
    return service.gen_function_book_service(code_path, commit_id, component_book_url, gerrit_url)


def get_function_analyze_book_service(code_path: str, commit_id: str, component_book_url: str,
                                      git_branch: str = 'develop') -> str:
    """向后兼容接口"""
    service = FuncAnalyzeBookService()
    return service.get_function_analyze_book_service(code_path, commit_id, component_book_url, git_branch)


def gen_change_info_service(code_path, component_book_url, commit_id_after, commit_id_before="", gerrit_url=""):
    """向后兼容接口 - 生成变更信息服务"""
    username = ENV.config.gerrit.username
    password = ENV.config.gerrit.password

    # 生成变更名称列表
    if commit_id_before:
        func_change_info_list = gen_change_name_service_new(code_path, commit_id_before, commit_id_after)
    else:
        func_change_info_list = gen_change_name_service(code_path, gerrit_url, commit_id_after, username, password)

    # 遍历变更名称列表，对每个变更详情进行解析
    for func_detail in func_change_info_list:
        # 生成变更详情
        gen_change_detail_service(func_detail.func_sign, func_detail)
        logger.info(f"解析函数 {func_detail.func_sign} 的详情完成 {func_detail}")

        # 生成并记录子函数信息
        func_parents = gen_calling_func_service(func_detail, code_path, component_book_url)
        func_detail.func_parents = func_parents
        logger.info(f"{func_detail.func_name} func_parents : {func_parents}")

        # 确定并记录函数类型
        func_detail.functionType = get_function_open_type(code_path, func_detail.func_name)

    return func_change_info_list


def gen_change_detail_service(func_name, func_info):
    """向后兼容接口 - 生成变更详情服务"""
    # 提取函数体
    function_body = extract_function_body(func_info.func_file_name, func_name)
    logger.info(f"函数 {func_name} 的完整函数体：\n{function_body}")
    func_info.func_body = function_body

    if function_body:
        # 步骤3：使用函数解析工具解析函数
        variable_result = enhance_variable_analysis(func_name, func_info)
        func_info.allVariables = variable_result
        logger.info(f"{func_info.func_sign} 解析出来的参数 {variable_result}")

        func_ret_var = gen_func_return_varname(function_body)
        func_info.return_var = func_ret_var

        func_desc = parse_func_body(func_name, function_body)
        func_info.func_desc = func_desc

        if func_info.return_type:
            func_return_desc = parse_func_return_variavle(func_name, function_body)
            func_info.return_desc = func_return_desc

        func_process_uml = parse_func_Process(func_name, function_body)
        func_info.func_process = func_process_uml

        for param in func_info.parameters:
            param.desc = parse_func_param(func_name, function_body, param.name)
    else:
        logger.error("无法从源文件中提取函数体")


def gen_change_name_service(code_path, gerrit_url, commit_id, username='', password=''):
    """向后兼容接口"""
    change_info = GitDiffAnalyzer.get_gerrit_diff_code(gerrit_url, commit_id, username, password)
    return _process_change_info_legacy(change_info, code_path)


def gen_change_name_service_new(code_path, commit_id_before, commit_id_after):
    """向后兼容接口"""
    change_info = GitDiffAnalyzer.get_local_diff_code(code_path, commit_id_before, commit_id_after)
    return _process_change_info_legacy(change_info, code_path)


def _process_change_info_legacy(change_info, code_path):
    """处理变更信息的遗留接口"""

    func_change_info_list = []
    for key, value in change_info.items():
        if len(value) == 0:
            continue

        for func in value:
            func_detail = FunctionDetailData()
            func_detail.func_name = func["function_name"].strip(' \r\n')
            func_detail.func_sign = func["function_sign"].strip(' \r\n')
            func_detail.func_file_name = os.path.join(code_path, os.path.normpath(key))
            func_sign_info = parse_function_declaration(func_detail.func_sign)
            func_detail.return_type = func_sign_info.return_type
            func_detail.parameters = func_sign_info.parameters
            func_change_info_list.append(func_detail)
            logger.info(f"新增变更函数{func_detail.func_sign}")
            logger.info(f"文件{os.path.basename(func_detail.func_file_name)}")

    return func_change_info_list


def extract_component_data(file_path, sheet_name):
    """向后兼容接口"""
    return ComponentUtils.extract_component_data(file_path, sheet_name)


def find_module_in_string(paths_and_modules, caller_path):
    """向后兼容接口"""
    return ComponentUtils.find_module_in_string(paths_and_modules, caller_path)


def gen_change_parents_service(func_detail, root_dir, component_book_url):
    """向后兼容接口"""
    return FunctionRelationshipAnalyzer.gen_change_parents_service(func_detail, root_dir, component_book_url)


def gen_calling_func_service(func_detail, root_dir, component_book_url):
    """向后兼容接口"""
    return FunctionRelationshipAnalyzer.gen_calling_func_service(func_detail, root_dir, component_book_url)


def get_function_open_type(code_path, func_name):
    """向后兼容接口"""
    return FunctionAnalyzeUtils.get_function_open_type(code_path, func_name)


def gen_func_return_varname(func_body):
    """向后兼容接口"""
    return FunctionAnalyzeUtils.gen_func_return_varname(func_body)


def find_function_by_line(content, line_numbers):
    """向后兼容接口"""
    return FunctionAnalyzeUtils.find_function_by_line(content, line_numbers)


def get_gerrit_diff_code(gerrit_url, commit_id, username='', password=''):
    """向后兼容接口"""
    return GitDiffAnalyzer.get_gerrit_diff_code(gerrit_url, commit_id, username, password)


def get_local_diff_code(local_folder_path, commit_id_before, commit_id_after):
    """向后兼容接口"""
    return GitDiffAnalyzer.get_local_diff_code(local_folder_path, commit_id_before, commit_id_after)


# 保持原有的main函数用于测试
if __name__ == '__main__':
    # 测试代码保持不变
    gen_function_book_multi_commit_service(
        r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\mte_code\gfx_agl_met",
        "54b961267f0796499e8c3b4586ebd5d15c0c6d68",
        "4c8234e9dfcbb699695ecd23ef8e9f45de816688",
        r"C:\Users\<USER>\Downloads\684D_Component_List (1) 3.xlsx"
    )
    # gen_function_book_multi_commit_service(
    #     r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\mte_code\gfx_agl_met",
    #     "87b270a38a1076888f463f9e03fa0d62388fc102",
    #     "cb0e1e57dddd50ca67fb040ea3c40a94a7e19fab",
    #     r"C:\Users\<USER>\Downloads\684D_Component_List (1) 3.xlsx"
    # )
