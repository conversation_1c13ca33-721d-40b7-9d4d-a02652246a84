"""
RAM干涉 Workflow

V字对应：
3.3.46. RAM干渉チェック実施＆結果検証

该模块提供RAM干涉检查功能，分析代码中的全局变量变更和RAM干涉情况。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量位置检索
3. RAM干涉确认
4. 生成RAM干涉检查报告
"""
from sdw_agent.service.ram_interfere_service.models import RAMInterfereRequest
from sdw_agent.service.ram_interfere_service.workflow_ram_interfere import RAMInterfereWorkflow, do_ram_interfere

__all__ = ['RAMInterfereWorkflow', 'do_ram_interfere', 'RAMInterfereRequest']
