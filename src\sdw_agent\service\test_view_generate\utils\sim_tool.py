#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : sim_tool.py
@Time    : 2025/7/30 17:20
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 相似度计算模块
"""
import re

import MeCab
from loguru import logger

from sdw_agent.llm.model import openai_embeddings
from scipy.spatial.distance import cosine


def _tokenize(text):
    """使用MeCab分词"""
    tagger = MeCab.Tagger("-Owakati")
    return set(tagger.parse(text).strip().split())


def standardize_punctuation(text: str) -> str:
    """
    标准化字符串中的冒号和引号格式

    Args:
        text: 待处理的字符串

    Returns:
        标准化后的字符串
    """
    # 标准化冒号（中文冒号 → 英文冒号）
    text = re.sub(r'：', ':', text)

    # 标准化引号
    text = re.sub(r'[“”‘]', '"', text)
    text = re.sub(r'[""’]', '"', text)
    text = text.replace(' ', '')
    text = text.replace('\n', '')

    return text

def embed_match_testcase(keywords, testcases, alpha=0.3):
    """使用embedding计算相似度，并过滤大于alpha阈值的结果"""
    try:
        logger.info(f"进入embedding相似度计算模块，输入为：{keywords}和{testcases}")
        # 对有效信息进行过滤
        testcase_texts = [(f"{testcases[i].get('确认点', '')}，"
                           f"{testcases[i].get('Precondition', '')}，"
                           f"{testcases[i].get('画面显示', '')}") for i in range(len(testcases))]
        # testcase_texts = testcases
        logger.info("开始请求embedding服务，用于相似计算准备")
        keyword_vectors = openai_embeddings.embed_documents(texts=keywords)
        testcase_vectors = openai_embeddings.embed_documents(texts=testcase_texts)
        logger.info("embedding生成结束，并开始计算相似度得分")
        cosine_similarity_matrix = [[1-cosine(keyword_vectors[i], testcase_vectors[j]) for j in range(len(testcase_vectors))] for i in range(len(keyword_vectors))]
        logger.info("相似度计算得分结束")
        results = [[testcases[j] for j in range(len(testcase_vectors)) if cosine_similarity_matrix[i][j] >= alpha] for i in range(len(keywords))]
        logger.info(f"embedding相似度计算结束输出，输出结果为：{results}")
        return results[0]
        # return cosine_similarity_matrix
    except Exception as e:
        logger.error(f"embedding相似度计算模块执行失败，失败原因为：{str(e)}")
        return []


def calculate_similarity_scores(keywords, testcases, alpha):
    """
    计算字符串列表L中每个元素与字符串A的字符交集相似度得分
    """
    try:
        logger.info(f"进入关键字计算相似度模块，输入为：{keywords}和{testcases}")
        # 对有效信息进行过滤
        testcase_texts = [(f"{testcases[i].get('确认点', '')}，"
                           f"{testcases[i].get('Precondition', '')}，"
                           f"{testcases[i].get('画面显示', '')}") for i in range(len(testcases))]
        # 遍历keywords列表
        score_res = []
        for one_key in keywords:
            # 转换为集合以获取唯一字符
            set_k = set(one_key)
            len_k = len(set_k)

            scores = []
            for one_case in testcase_texts:
                # 计算交集
                intersection = set(one_case) & set_k
                # 计算相似度得分
                score = len(intersection) / len_k if len_k > 0 else 0
                scores.append(score)
            score_res.append(scores)
        results = [[testcases[j] for j in range(len(testcases)) if score_res[i][j] >= alpha] for i in
                   range(len(keywords))]
        logger.info(f"关键字相似度计算结束输出，输出结果为：{results}")
        return results[0]
    except Exception as e:
        logger.error(f"关键字计算相似度得分模块执行失败，失败原因为：{str(e)}")
        return []


def key_match_sim(keywords, testcases):
    """使用硬匹配匹配对应测试用例"""
    try:
        logger.info(f"进入关键字硬匹配模块，输入为：{keywords}和{testcases}")
        # 过滤有效信息
        testcase_texts = [(f"{testcases[i].get('确认点', '')}，"
                           f"{testcases[i].get('Precondition', '')}，"
                           f"{testcases[i].get('画面显示', '')}") for i in range(len(testcases))]
        # 对关键词进行切割
        key_list = keywords.split('、')
        match_res = list()
        # 开始硬匹配
        for inx_, one_case in enumerate(testcase_texts):
            for one_key in key_list:
                # 一些特殊字符处理
                if 'X' in one_key:
                    one_key = one_key.split('X')[0]
                one_key = standardize_punctuation(one_key)
                one_case = standardize_punctuation(one_case)
                if one_key in one_case:
                    match_res.append(testcases[inx_])
        logger.info(f"硬匹配结束，输出结果为：{match_res}")
        return match_res
    except Exception as e:
        logger.error(f"硬匹配模块执行失败，失败原因为：{str(e)}")
        return []

def filter_map_case(case_list, filter_str):
    """针对易混淆的用例进行筛选"""
    testcase_texts = [(f"{case_list[i].get('确认点', '')}，"
                       f"{case_list[i].get('Precondition', '')}，"
                       f"{case_list[i].get('画面显示', '')}") for i in range(len(case_list))]
    filter_res = list()
    for inx_, one_case in enumerate(testcase_texts):
        if filter_str in str(one_case):
            filter_res.append(case_list[inx_])
    return filter_res
