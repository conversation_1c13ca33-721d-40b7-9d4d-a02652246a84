"""
Centralized error handling for document parsing.
"""

import logging
import sys
import traceback
from typing import Any, Callable, Dict, List, Optional, Type, Union
from functools import wraps

# Configure logging
logger = logging.getLogger('docparser')

# Define custom exceptions
class DocParserError(Exception):
    """Base exception for all document parser errors."""
    def __init__(self, message: str, *args, **kwargs):
        self.message = message
        super().__init__(message, *args, **kwargs)

class DocumentNotFoundError(DocParserError):
    """Exception raised when a document is not found."""
    pass

class UnsupportedDocumentTypeError(DocParserError):
    """Exception raised when a document type is not supported."""
    pass

class ParsingError(DocParserError):
    """Exception raised when there is an error parsing a document."""
    pass

class PluginError(DocParserError):
    """Exception raised when there is an error with a plugin."""
    pass

class ValidationError(DocParserError):
    """Exception raised when document validation fails."""
    pass

class ExportError(DocParserError):
    """Exception raised when there is an error exporting document data."""
    pass

# Error registry for mapping error codes to exceptions
ERROR_REGISTRY: Dict[int, Type[DocParserError]] = {
    1000: DocumentNotFoundError,
    1001: UnsupportedDocumentTypeError,
    1100: ParsingError,
    1200: PluginError,
    1300: ValidationError,
    1400: ExportError,
}

# Error code descriptions
ERROR_DESCRIPTIONS: Dict[int, str] = {
    1000: "Document not found",
    1001: "Unsupported document type",
    1100: "Error parsing document",
    1200: "Plugin error",
    1300: "Document validation failed",
    1400: "Error exporting document data",
}

def get_error_by_code(error_code: int) -> Type[DocParserError]:
    """
    Get exception class by error code.
    
    Args:
        error_code: Error code
        
    Returns:
        Exception class
        
    Raises:
        ValueError: If error code is not registered
    """
    if error_code not in ERROR_REGISTRY:
        raise ValueError(f"Unknown error code: {error_code}")
    
    return ERROR_REGISTRY[error_code]

def get_error_description(error_code: int) -> str:
    """
    Get error description by error code.
    
    Args:
        error_code: Error code
        
    Returns:
        Error description
        
    Raises:
        ValueError: If error code is not registered
    """
    if error_code not in ERROR_DESCRIPTIONS:
        raise ValueError(f"Unknown error code: {error_code}")
    
    return ERROR_DESCRIPTIONS[error_code]

def raise_error(error_code: int, message: Optional[str] = None) -> None:
    """
    Raise an exception by error code.
    
    Args:
        error_code: Error code
        message: Error message (optional)
        
    Raises:
        DocParserError: Exception corresponding to the error code
    """
    error_class = get_error_by_code(error_code)
    error_message = message or get_error_description(error_code)
    
    logger.error(f"Error {error_code}: {error_message}")
    raise error_class(error_message)

def handle_error(func: Callable) -> Callable:
    """
    Decorator for handling errors in functions.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DocParserError as e:
            logger.error(f"{e.__class__.__name__}: {e.message}")
            raise
        except FileNotFoundError as e:
            logger.error(f"File not found: {e}")
            raise DocumentNotFoundError(str(e))
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            logger.debug(traceback.format_exc())
            raise ParsingError(f"Unexpected error: {e}")
    
    return wrapper

def format_error_response(error: Exception) -> Dict[str, Any]:
    """
    Format error as a response dictionary.
    
    Args:
        error: Exception
        
    Returns:
        Error response dictionary
    """
    if isinstance(error, DocParserError):
        error_type = error.__class__.__name__
        error_message = error.message
    else:
        error_type = error.__class__.__name__
        error_message = str(error)
    
    return {
        "success": False,
        "error": {
            "type": error_type,
            "message": error_message,
            "traceback": traceback.format_exc() if logger.level <= logging.DEBUG else None
        }
    }

def format_success_response(data: Any = None) -> Dict[str, Any]:
    """
    Format success response dictionary.
    
    Args:
        data: Response data (optional)
        
    Returns:
        Success response dictionary
    """
    response = {"success": True}
    if data is not None:
        response["data"] = data
    
    return response

def try_except_decorator(error_code: int) -> Callable:
    """
    Decorator for handling specific error codes.
    
    Args:
        error_code: Error code to use when an exception occurs
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_message = f"{get_error_description(error_code)}: {str(e)}"
                logger.error(error_message)
                import traceback
                print(traceback.format_exc())
                raise get_error_by_code(error_code)(error_message)
        
        return wrapper
    
    return decorator
