"""
测试代理24工作流数据模型

定义工作流使用的数据模型和验证模式
"""

from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import numpy as np

from pydantic import BaseModel, Field


class TestAgent24ConfigModel(BaseModel):
    """测试代理24配置模型"""
    
    # 基本配置
    name: str = Field(default="测试代理24")
    description: str = Field(default="提供测试用例信息提取和自动填写CheckSheet功能")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")
    
    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)
    
    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)
    
    # 嵌入配置
    embedding: Dict[str, Any] = Field(default_factory=dict)
    
    # API配置
    azure: Dict[str, Any] = Field(default_factory=dict)
    
    # 日志配置
    logging: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        extra = "allow"


class StatusEnum(str, Enum):
    """状态枚举"""
    OK = "OK"
    NG = "NG"
    OTHER = "O"
    NONE = "-"


class TestCaseInfo(BaseModel):
    """测试用例信息"""
    row_index: int  # 行号（在Excel中）
    content: str    # 内容（C列和D列合并）


class SheetMatchInfo(BaseModel):
    """Sheet匹配信息"""
    sheet_name: str
    index: int
    line: str
    similarity: float


class MatchResult(BaseModel):
    """匹配结果"""
    matches: List[SheetMatchInfo] = Field(default_factory=list)
    
    def get_top_k(self, k: int) -> List[SheetMatchInfo]:
        """获取相似度最高的前k个结果"""
        return sorted(self.matches, key=lambda x: x.similarity, reverse=True)[:k]


class StatusDetail(BaseModel):
    """状态详情"""
    sheet_name: str
    status: StatusEnum
    indices: List[str]


class ResultOutput(BaseModel):
    """结果输出"""
    status: StatusEnum
    details: List[StatusDetail] = Field(default_factory=list)
    raw_matches: List[Tuple[str, int]] = Field(default_factory=list)
    
    @property
    def details_text(self) -> str:
        """生成详情文本"""
        lines = []
        for detail in self.details:
            lines.append(f"{detail.sheet_name}:")
            if detail.indices:
                lines.append(f"{detail.status.value}:{','.join(detail.indices)}")
        return "\n".join(lines).strip()


class ProcessingResult(BaseModel):
    """处理结果"""
    test_cases: List[TestCaseInfo]
    match_results: List[MatchResult]
    outputs: List[ResultOutput]
    output_file: str 