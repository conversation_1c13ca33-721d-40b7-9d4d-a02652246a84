import unittest
from unittest.mock import MagicMock, patch, call
from docparser.parsers.word.word_parser import WordParser
from docparser.models.table import TableObject, RowObject, CellObject
from docparser.models.text import TextObject
from docparser.models.position import Position
from docparser.models.document import DocumentBlockObject
from docx.table import Table as ProxyTable
from docx.oxml.table import CT_Tbl


class TestWordParserParseTable(unittest.TestCase):
    def setUp(self):
        self.parser = WordParser()
        # 初始化必要的内部状态
        self.parser._tbl_idx = 0
        self.parser._table_info = {
            0: {
                'position': Position(page_id="1", x=0, y=0),
                'cols': 2,
                'cell_positions': [Position(page_id="1", x=0, y=0), Position(page_id="1", x=0, y=1)],
                'cell_page_num': ['1', '1'],
                'cell_String_num': ['自动1', '自动2']
            }
        }

        # 构建 mock 的 CT_Tbl 对象
        self.tbl = MagicMock(spec=CT_Tbl)
        self.tbl.tr_lst = [
            MagicMock(tc_lst=[
                MagicMock(tbl_lst=[]),  # 第一个单元格无嵌套表格
                MagicMock(tbl_lst=[])   # 第二个单元格无嵌套表格
            ])
        ]

        # 构建 proxy_table
        self.proxy_table = MagicMock(spec=ProxyTable)
        row_mock = MagicMock()
        row_mock.cells = [MagicMock(), MagicMock()]
        self.proxy_table.rows = [row_mock]

        # Patch 依赖的方法
        self.parser._parse_cell = MagicMock()
        self.parser._parse_cell.return_value = CellObject()

        self.parser.is_empty_table = MagicMock(return_value=False)
        self.parser.table_row_align = MagicMock()
        self.parser.parse_merge_region = MagicMock()
        self.parser.parse_tbl_borders = MagicMock()
        self.parser.parse_tbl_layout = MagicMock()
        self.parser.parse_tbl_style = MagicMock()

        # 构建 parent 和 block
        self.parent = MagicMock()
        self.block = DocumentBlockObject()

    def test_parse_table_normal_case(self):
        """
        TC01 - 正常表格解析
        """
        result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertIsInstance(result, TableObject)
        self.assertEqual(len(result.rows), 1)
        self.assertEqual(result.label, "body")
        self.assertEqual(result.position.page_id, "1")

    def test_parse_table_with_nested_table(self):
        """
        TC02 - 嵌套表格解析
        """
        inner_tbl = MagicMock(spec=CT_Tbl)
        self.tbl.tr_lst[0].tc_lst[0].tbl_lst = [inner_tbl]
        self.parser._parse_table = MagicMock(side_effect=[TableObject(), TableObject()])

        result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertEqual(self.parser._parse_table.call_count, 2)
        self.assertEqual(self.parser._tbl_idx, 1)

    def test_parse_table_skip_empty(self):
        """
        TC03 - 空表格跳过
        """
        self.parser.is_empty_table = MagicMock(return_value=True)
        result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertEqual(self.parser._tbl_idx, 0)

    def test_parse_table_col_span(self):
        """
        TC04 - 列合并处理
        """
        cell_mock = MagicMock(grid_span=2)
        row_mock = MagicMock()
        row_mock.cells = [cell_mock]
        self.proxy_table.rows = [row_mock]
        self.parser._parse_cell.return_value = CellObject()

        with patch('docparser.parsers.word.word_parser.Table', return_value=self.proxy_table):
            result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        cell_obj = result.rows[0].cells[0]
        self.assertEqual(cell_obj.merged_ranges_2, [None, 0, None, 1])

    def test_parse_table_no_position_info(self):
        """
        TC05 - 无页码信息
        """
        self.parser._table_info = {}

        result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertIsNone(result.position)

    def test_parse_table_auto_number_insert(self):
        """
        TC06 - 自动编号插入
        """
        cell_mock = MagicMock(grid_span=1)
        row_mock = MagicMock()
        row_mock.cells = [cell_mock]
        self.proxy_table.rows = [row_mock]
        cell_obj = CellObject()
        cell_obj.col_index = 0
        self.parser._parse_cell.return_value = cell_obj

        with patch('docparser.parsers.word.word_parser.Table', return_value=self.proxy_table):
            result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertTrue(result.rows[0].cells[0].auto_number)
        self.assertEqual(result.rows[0].cells[0].text, '自动1')

    def test_parse_table_auto_number_replace(self):
        """
        TC07 - 自动编号替换
        """
        cell_mock = MagicMock(grid_span=1)
        row_mock = MagicMock()
        row_mock.cells = [cell_mock]
        self.proxy_table.rows = [row_mock]
        cell_obj = CellObject()
        cell_obj.col_index = 0
        text_obj = TextObject()
        text_obj.text = "旧内容"
        cell_obj.content.append(text_obj)
        self.parser._parse_cell.return_value = cell_obj

        with patch('docparser.parsers.word.word_parser.Table', return_value=self.proxy_table):
            result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertIn("自动1", result.rows[0].cells[0].text)

    def test_parse_table_index_error(self):
        """
        TC08 - 索引越界异常处理
        """
        self.parser._table_info[0]['cell_page_num'] = ['1']

        with patch('docparser.parsers.word.word_parser.Table', return_value=self.proxy_table):
            result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.assertIsNotNone(result)

    def test_parse_table_methods_called(self):
        """
        TC10 - 辅助解析方法是否调用
        """
        with patch('docparser.parsers.word.word_parser.Table', return_value=self.proxy_table):
            result = self.parser._parse_table(self.tbl, self.parent, block=self.block)

        self.parser.table_row_align.assert_called_once_with(result)
        self.parser.parse_merge_region.assert_called_once()
        self.parser.parse_tbl_borders.assert_called_once()
        self.parser.parse_tbl_layout.assert_called_once()
        self.parser.parse_tbl_style.assert_called_once()


if __name__ == '__main__':
    unittest.main()
