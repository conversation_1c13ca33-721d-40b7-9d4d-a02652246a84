import unittest
from unittest.mock import MagicMock
from docparser.parsers.word.word_parser import WordParser

class TestWordParser(unittest.TestCase):
    def setUp(self):
        """初始化测试数据和实例"""
        self.word_parser = WordParser()
        self.mock_picture_obj = MagicMock()
        self.mock_graphic_obj = MagicMock()

    def test_calculate_overlap(self):
        """测试图形是否正确计算重叠度"""
        obj1 = MagicMock()
        obj1.layout.page_id = "1"
        obj1.position.x, obj1.position.y = 5, 5
        obj1.px_width, obj1.px_height = 100, 50

        obj2 = MagicMock()
        obj2.layout.page_id = "1"
        obj2.position.x, obj2.position.y = 50, 25
        obj2.px_width, obj2.px_height = 100, 50

        result = self.word_parser._calculate_overlap(obj1, obj2, threshold=0.5)
        self.assertTrue(result, "重叠计算结果应该满足阈值条件")

    def test_find_overlapping_groups(self):
        """测试寻找重叠组逻辑"""
        picture_objs = [MagicMock(layout=MagicMock(page_id="1"), position=MagicMock(x=10, y=20), width=50, height=50)]
        graphic_objs = [MagicMock(layout=MagicMock(page_id="1"), position=MagicMock(x=15, y=25), width=50, height=50)]

        groups = self.word_parser._find_overlapping_groups(picture_objs, graphic_objs)
        self.assertEqual(len(groups), 1, "应该找到一个重叠组")
        self.assertTrue(all(obj in groups[0] for obj in picture_objs + graphic_objs), "重叠组应该包括所有重叠对象")

    def test_calculate_max_range(self):
        """测试计算多个矩形的最大外包矩形"""
        rectangles = [[10, 20, 100, 200], [50, 80, 150, 300], [30, 60, 120, 240]]
        max_range = self.word_parser._calculate_max_range(rectangles)
        self.assertEqual(max_range, [10, 20, 190, 360], "最大外包矩形的计算结果不正确")

    def test_add_shape_to_block(self):
        """测试将图形添加到块对象"""
        block_obj = MagicMock()
        shape_obj = MagicMock()
        self.word_parser.add_shape_to_block(shape_obj, block_obj)
        block_obj.add_graphic.assert_called()

    def test_is_empty_row(self):
        """测试是否为空行"""
        row_obj = MagicMock()
        cell1 = MagicMock(text="", content=[])
        cell2 = MagicMock(text="", content=[])
        row_obj.cells = [cell1, cell2]

        self.assertTrue(self.word_parser.is_empty_row(row_obj), "该行应该被判断为空行")

        cell1.text = "Not Empty"
        self.assertFalse(self.word_parser.is_empty_row(row_obj), "该行不应该被判断为空行")

    def test_parse_table_row_align(self):
        """测试表格行对齐逻辑"""
        tbl_obj = MagicMock(rows=[MagicMock(cells=[MagicMock(), MagicMock()]), MagicMock(cells=[MagicMock()])])
        self.word_parser.table_row_align(tbl_obj)
        self.assertEqual(len(tbl_obj.rows[0].cells), len(tbl_obj.rows[1].cells), "表格行中的单元格数量应该一致")

    def test_parse_document_content(self):
        """测试解析文档内容的功能"""
        file_path = "test.docx"
        self.word_parser._read_document = MagicMock()
        self.word_parser._extract_metadata = MagicMock()
        self.word_parser._parse_table = MagicMock()
        self.word_parser.parse_header_footer = MagicMock()

        try:
            self.word_parser._parse_document_content(file_path)
        except Exception as e:
            self.fail(f"解析文档内容失败：{str(e)}")

    def test_parse_inline_shape_object(self):
        """测试解析 Inline Shape 对象"""
        block_obj = MagicMock()
        self.word_parser.inline_shape_data = [
            (b'test_data', 100, 50, '1', {'x': 10, 'y': 20})
        ]
        self.word_parser.parse_inline_shape_object(block_obj)
        block_obj.add_graphic.assert_called_once()

    def test_parse_graphic_text(self):
        """测试解析图形的文字内容"""
        graphic_obj = MagicMock()
        shape_e = MagicMock()
        shape_e.__getitem__.return_value = [MagicMock(tag='w:r', text="Test Text")]
        self.word_parser._parse_graphic_text(graphic_obj, "body", shape_e)
        self.assertEqual(graphic_obj.text, "Test Text", "图形的文本内容解析不正确")



class TestWordParserGraphicComparison(unittest.TestCase):
    def setUp(self):
        # 设置测试数据：模拟图形对象
        self.graphic_obj = MagicMock()
        self.graphic = {
            "px_width": 150,
            "px_height": 200
        }

    def test_graphic_width_ratio_greater_than_1(self):
        """测试宽度比例大于 1 的情况"""
        self.graphic_obj.px_width = 300
        self.graphic_obj.px_height = 200

        abs_width_ratio = (
            float(self.graphic_obj.px_width) / float(self.graphic["px_width"])
            if self.graphic_obj.px_width > self.graphic["px_width"]
            else float(self.graphic["px_width"]) / float(self.graphic_obj.px_width)
        )

        self.assertGreater(abs_width_ratio, 1, "宽度比例应大于 1")

    def test_graphic_width_ratio_less_than_1(self):
        """测试宽度比例小于 1 的情况"""
        self.graphic_obj.px_width = 100
        self.graphic_obj.px_height = 200

        abs_width_ratio = (
            float(self.graphic_obj.px_width) / float(self.graphic["px_width"])
            if self.graphic_obj.px_width > self.graphic["px_width"]
            else float(self.graphic["px_width"]) / float(self.graphic_obj.px_width)
        )

        self.assertGreater(abs_width_ratio, 1, "宽度比例应大于 1")

    def test_graphic_height_ratio_greater_than_1(self):
        """测试高度比例大于 1 的情况"""
        self.graphic_obj.px_width = 150
        self.graphic_obj.px_height = 300

        abs_height_ratio = (
            float(self.graphic_obj.px_height) / float(self.graphic["px_height"])
            if self.graphic_obj.px_height > self.graphic["px_height"]
            else float(self.graphic["px_height"]) / float(self.graphic_obj.px_height)
        )

        self.assertGreater(abs_height_ratio, 1, "高度比例应大于 1")

    def test_graphic_match_success(self):
        """测试图形匹配成功的情况"""
        self.graphic_obj.px_width = 148
        self.graphic_obj.px_height = 198

        abs_width_ratio = (
            float(self.graphic_obj.px_width) / float(self.graphic["px_width"])
            if self.graphic_obj.px_width > self.graphic["px_width"]
            else float(self.graphic["px_width"]) / float(self.graphic_obj.px_width)
        )

        abs_height_ratio = (
            float(self.graphic_obj.px_height) / float(self.graphic["px_height"])
            if self.graphic_obj.px_height > self.graphic["px_height"]
            else float(self.graphic["px_height"]) / float(self.graphic_obj.px_height)
        )

        self.assertGreater(abs_width_ratio, 0.9, "宽度比例应大于 0.9")
        self.assertGreater(abs_height_ratio, 0.9, "高度比例应大于 0.9")
        self.assertTrue(abs_width_ratio > 0.9 and abs_height_ratio > 0.9, "匹配条件应满足")

    def test_graphic_match_failure(self):
        """测试图形匹配失败的情况"""
        self.graphic_obj.px_width = 50
        self.graphic_obj.px_height = 50

        abs_width_ratio = (
            float(self.graphic_obj.px_width) / float(self.graphic["px_width"])
            if self.graphic_obj.px_width > self.graphic["px_width"]
            else float(self.graphic["px_width"]) / float(self.graphic_obj.px_width)
        )

        abs_height_ratio = (
            float(self.graphic_obj.px_height) / float(self.graphic["px_height"])
            if self.graphic_obj.px_height > self.graphic["px_height"]
            else float(self.graphic["px_height"]) / float(self.graphic_obj.px_height)
        )

        self.assertLess(abs_width_ratio, 0.9, "宽度比例应小于 0.9")
        self.assertLess(abs_height_ratio, 0.9, "高度比例应小于 0.9")
        self.assertFalse(abs_width_ratio > 0.9 and abs_height_ratio > 0.9, "匹配条件不应满足")


class TestWordParserGetMostSimData(unittest.TestCase):
    def setUp(self):
        """初始化测试环境"""
        self.word_parser = WordParser()
        self.mock_shape = MagicMock()

    def test_no_images_data(self):
        """测试输入 images_data 为空的情况"""
        self.mock_shape.Width = 100
        self.mock_shape.Height = 200
        images_data = []
        result = self.word_parser.get_most_sim_data(images_data, self.mock_shape)

        self.assertIsNone(result, "当 images_data 为空时，结果应为 None")

    def test_one_image_matching(self):
        """测试只有一个匹配图像的情况"""
        self.mock_shape.Width = 100
        self.mock_shape.Height = 200

        mock_data = MagicMock(width=100 * (72 / 96), height=200 * (72 / 96))
        images_data = [{'data': mock_data}]

        self.word_parser.similar_judge = MagicMock(return_value=True)
        result = self.word_parser.get_most_sim_data(images_data, self.mock_shape)

        self.assertEqual(result, images_data[0], "结果应为唯一的匹配图像数据")
        self.assertEqual(len(images_data), 0, "图像数据应从列表中移除")

    def test_multiple_matching_images(self):
        """测试多个图像都匹配但返回相似度最高的情况"""
        self.mock_shape.Width = 100
        self.mock_shape.Height = 200

        # 模拟多张图像数据
        mock_data1 = MagicMock(width=100 * (72 / 96), height=200 * (72 / 96))
        mock_data2 = MagicMock(width=95 * (72 / 96), height=190 * (72 / 96))
        mock_data3 = MagicMock(width=102 * (72 / 96), height=202 * (72 / 96))
        images_data = [{'data': mock_data1}, {'data': mock_data2}, {'data': mock_data3}]

        self.word_parser.similar_judge = MagicMock(
            side_effect=lambda a, b, threshold: abs(a - b) < threshold * (72 / 96)
        )
        result = self.word_parser.get_most_sim_data(images_data, self.mock_shape)

        self.assertEqual(result['data'], mock_data3, "应返回具有最高相似度的图像")
        self.assertEqual(len(images_data), 2, "最匹配的图像应从列表中移除")

    def test_no_matching_images(self):
        """测试没有任何图像匹配的情况"""
        self.mock_shape.Width = 100
        self.mock_shape.Height = 200

        # 模拟图像数据
        mock_data1 = MagicMock(width=50 * (72 / 96), height=100 * (72 / 96))
        mock_data2 = MagicMock(width=300 * (72 / 96), height=500 * (72 / 96))
        images_data = [{'data': mock_data1}, {'data': mock_data2}]

        self.word_parser.similar_judge = MagicMock(return_value=False)
        result = self.word_parser.get_most_sim_data(images_data, self.mock_shape)

        self.assertIsNone(result, "没有任何匹配的图像时，结果应为 None")
        self.assertEqual(len(images_data), 2, "图像数据不应从列表中移除")

    def test_multiple_images_same_similarity(self):
        """测试多个图像拥有相同的最高相似度"""
        self.mock_shape.Width = 100
        self.mock_shape.Height = 200

        # 模拟图像数据
        mock_data1 = MagicMock(width=100 * (72 / 96), height=200 * (72 / 96))
        mock_data2 = MagicMock(width=100 * (72 / 96), height=200 * (72 / 96))
        images_data = [{'data': mock_data1}, {'data': mock_data2}]

        self.word_parser.similar_judge = MagicMock(return_value=True)
        result = self.word_parser.get_most_sim_data(images_data, self.mock_shape)

        self.assertIsNone(result, "多个图像拥有相同的最高相似度时，结果应为 None")
        self.assertEqual(len(images_data), 2, "图像数据不应从列表中移除")


import unittest
from unittest.mock import MagicMock, patch
from docparser.parsers.word.word_parser import WordParser
from docparser.models.position import Position # 假设 create_position_obj 返回的是此类

class TestWordParserParseParaInfo(unittest.TestCase):
    def setUp(self):
        """初始化测试环境"""
        self.word_parser = WordParser()
        self.word_parser._para_info = {}
        self.word_parser._picture_info = []

    def mock_create_position_obj(self, x, y, width, height):
        """模拟 create_position_obj 方法"""
        return Position(x=x, y=y, width=width, height=height)

    def test_no_tables_or_inline_shapes(self):
        """测试段落没有表格或内嵌图形的情况"""
        mock_doc = MagicMock()
        mock_para = MagicMock()
        mock_doc.Paragraphs = [mock_para]

        mocked_text = "This is a test paragraph."
        mock_para.Range.Tables.Count = 0
        mock_para.Range.InlineShapes.Count = 0
        mock_para.Range.Text.replace.return_value = mocked_text
        mock_para.Range.Information.side_effect = lambda info_type: 1 if info_type == 1 else 50

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            self.word_parser._parse_para_info(0, mock_doc)

        self.assertEqual(len(self.word_parser._para_info), 1, "段落信息长度应该为 1")
        self.assertEqual(self.word_parser._para_info[1]["content"], mocked_text.strip(" "), "段落内容解析不正确")
        self.assertEqual(self.word_parser._para_info[1]["page_id"], "1", "段落页码解析不正确")

    def test_inline_shapes_with_valid_page(self):
        """测试段落中存在内嵌图形的情况"""
        mock_doc = MagicMock()
        mock_para = MagicMock()
        mock_doc.Paragraphs = [mock_para]

        mock_para.Range.Tables.Count = 0
        mock_para.Range.InlineShapes.Count = 2
        mock_para.Range.Information.side_effect = lambda info_type: 1 if info_type == 1 else 50

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            self.word_parser._parse_para_info(0, mock_doc)

        self.assertEqual(len(self.word_parser._picture_info), 2, "图片信息长度应该为 2")
        self.assertEqual(self.word_parser._picture_info[0], "1", "图片的页码解析不正确")

    def test_auto_number_and_header(self):
        """测试段落中存在自动编号和标题的情况"""
        mock_doc = MagicMock()
        mock_para = MagicMock()
        mock_doc.Paragraphs = [mock_para]

        mocked_text = "Heading text with auto number."
        auto_number = ""

        mock_para.Range.Tables.Count = 0
        mock_para.Range.InlineShapes.Count = 0
        mock_para.Range.Text.replace.return_value = mocked_text
        mock_para.Range.Information.side_effect = [1, 50]  # 页码和位置模拟值
        mock_para.Range.ListFormat.ListType = 1
        mock_para.Range.ListFormat.ListString = auto_number
        mock_para.Range.Style.NameLocal = "Heading 1"

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            self.word_parser._parse_para_info(0, mock_doc)

        self.assertEqual(len(self.word_parser._para_info), 1, "段落信息长度应该为 1")
        self.assertEqual(self.word_parser._para_info[1]["auto_number"], ">", "自动编号内容解析转换不正确")
        self.assertEqual(self.word_parser._para_info[1]["auto_header"], auto_number, "标题解析不正确")

    def test_text_with_page_break(self):
        """测试段落文字中存在分页符的情况"""
        mock_doc = MagicMock()
        mock_para = MagicMock()
        mock_doc.Paragraphs = [mock_para]

        text_with_page_break = "This is page 1 content.\fThis is page 2 content."
        mock_para.Range.Tables.Count = 0
        mock_para.Range.Text.replace.return_value = text_with_page_break
        mock_para.Range.Information.side_effect = [2, 50]  # 当前页码和位置模拟值

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            self.word_parser._parse_para_info(0, mock_doc)

        self.assertEqual(len(self.word_parser._para_info), 2, "段落信息长度应该为 2")
        self.assertEqual(self.word_parser._para_info[1]["content"], "This is page 1 content.", "分页符前的内容解析不正确")
        self.assertEqual(self.word_parser._para_info[2]["content"], "This is page 2 content.", "分页符后的内容解析不正确")

    def test_error_handling(self):
        """测试错误处理逻辑"""
        mock_doc = MagicMock()
        mock_para = MagicMock()
        mock_doc.Paragraphs = [mock_para]

        mock_para.Range.Tables.Count = 0
        mock_para.Range.Text.replace.side_effect = Exception("Test Error")

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            try:
                self.word_parser._parse_para_info(0, mock_doc)
            except Exception as e:
                self.fail(f"解析段落时不应抛出异常。错误信息: {e}")


import unittest
from unittest.mock import MagicMock, patch
from docparser.parsers.word.word_parser import WordParser
from io import BytesIO


class TestWordParserParseShapeInfo(unittest.TestCase):
    def setUp(self):
        """初始化测试环境"""
        self.word_parser = WordParser()
        self.word_parser._graphic_info = {}

    def mock_create_position_obj(self, x, y, width, height):
        """模拟 create_position_obj 方法"""
        return {
            "x": x,
            "y": y,
            "width": width,
            "height": height
        }

    def test_shape_with_valid_anchor(self):
        """测试具有有效锚点的图形"""
        mock_doc = MagicMock()
        mock_shapes = [MagicMock()]
        mock_doc.Shapes = mock_shapes

        mock_shape = mock_shapes[0]
        mock_shape.Type = "ShapeType"
        mock_shape.Name = "ShapeName"
        mock_shape.Anchor.Information.side_effect = lambda info: {
            1: 1,  # 页码
            5: 100,  # 水平位置
            6: 200  # 垂直位置
        }.get(info)
        mock_shape.width = 300
        mock_shape.height = 150
        mock_shape.TextFrame.TextRange.Text.replace.return_value = "Shape Text"

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            with patch("docparser.parsers.word.word_parser.WordParser.read_bytes", return_value=(b"data", 300, 150)):
                with patch("docparser.parsers.word.word_parser.WordParser.get_most_sim_data", return_value=None):
                    self.word_parser._parse_shape_info(mock_doc, [], None)

        self.assertEqual(len(self.word_parser._graphic_info), 1, "图形信息数量应该为 1")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["text"], "Shape Text", "图形文本内容解析不正确")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["page_id"], "1", "图形的页码解析不正确")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["px_width"], 300, "图形宽度解析不正确")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["px_height"], 150, "图形高度解析不正确")

    def test_shape_without_anchor(self):
        """测试没有锚点的图形"""
        mock_doc = MagicMock()
        mock_shapes = [MagicMock()]
        mock_doc.Shapes = mock_shapes

        mock_shape = mock_shapes[0]
        mock_shape.Type = "ShapeType"
        mock_shape.Name = "ShapeName"
        mock_shape.Anchor = None
        mock_shape.left = 10
        mock_shape.top = 20
        mock_shape.width = 300
        mock_shape.height = 150
        mock_shape.TextFrame.TextRange.Text.replace.return_value = "Shape Text"

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            with patch("docparser.parsers.word.word_parser.WordParser.read_bytes", return_value=(b"data", 300, 150)):
                with patch("docparser.parsers.word.word_parser.WordParser.get_most_sim_data", return_value=None):
                    self.word_parser._parse_shape_info(mock_doc, [], None)

        self.assertEqual(len(self.word_parser._graphic_info), 1, "图形信息数量应该为 1")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["position"], self.mock_create_position_obj(10, 20, 300, 150), "图形位置解析不正确")

    def test_shape_with_invalid_dimensions(self):
        """测试图形具有非正数的宽度或高度"""
        mock_doc = MagicMock()
        mock_shapes = [MagicMock()]
        mock_doc.Shapes = mock_shapes

        mock_shape = mock_shapes[0]
        mock_shape.Type = "ShapeType"
        mock_shape.Name = "ShapeName"
        mock_shape.Anchor.Information.side_effect = lambda info: {1: 1}.get(info)
        mock_shape.width = 0
        mock_shape.height = -1
        mock_shape.TextFrame.TextRange.Text.replace.return_value = "Shape Text"

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            with patch("docparser.parsers.word.word_parser.WordParser.read_bytes", return_value=(b"", "0", "0")):
                self.word_parser._parse_shape_info(mock_doc, [], None)

        self.assertEqual(len(self.word_parser._graphic_info), 1, "图形信息数量应该为 1")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["px_width"], "0", "图形宽度解析不正确")
        self.assertEqual(self.word_parser._graphic_info["ShapeName_1"]["px_height"], "0", "图形高度解析不正确")

    def test_error_handling(self):
        """测试错误处理逻辑"""
        mock_doc = MagicMock()
        mock_shapes = [MagicMock()]
        mock_doc.Shapes = mock_shapes

        mock_shape = mock_shapes[0]
        mock_shape.Type = "ShapeType"
        mock_shape.Name = "ShapeName"
        mock_shape.Anchor.Information.side_effect = Exception("Error Simulated")

        with patch("docparser.parsers.word.word_parser.WordParser.create_position_obj", self.mock_create_position_obj):
            try:
                self.word_parser._parse_shape_info(mock_doc, [], None)
            except Exception as e:
                self.fail(f"解析图形时不应抛出异常。错误信息: {e}")


# AI generation start
import unittest
from docparser.parsers.word.word_parser import WordParser

class TestParseChapterId(unittest.TestCase):
    def setUp(self):
        """初始化测试环境"""
        self.parser = WordParser()

    def test_parse_chapter_id_valid_halfwidth(self):
        """半角数字章节号：有效"""
        text = "1.1 第一章"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "1.1", "半角章节号解析失败")

    def test_parse_chapter_id_valid_fullwidth(self):
        """全角数字章节号：有效"""
        text = "２ 引言"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "2", "全角章节号解析失败")

    def test_parse_chapter_id_no_match(self):
        """无章节号开头内容：应返回 None"""
        text = "第一章 主要讲解通信原理部分"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "非章节号文本解析结果应为 None")

    def test_parse_chapter_id_mixed_width(self):
        """混合全角/半角数字章节号：有效"""
        text = "１.1 第一章"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "１.1", "混合宽度的章节号解析失败")

    def test_parse_chapter_id_multiple_numbers(self):
        """多级章节号：有效"""
        text = "３.２.５、 第五章"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "３.２.５", "多级章节号解析失败")

    def test_parse_chapter_id_invalid_start_with_letter(self):
        """以字母开头的段落：应返回 None"""
        text = "7abc程度が望ましい。"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "以字母开头的内容不应被识别为合法章节标题")

    def test_parse_chapter_id_invalid_start_with_symbol(self):
        """以符号开头的段落：应返回 None"""
        text = "10～テスト"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "以符号开头的内容不应被识别为合法章节标题")

    def test_parse_chapter_id_with_leading_space(self):
        """章节号前有空格：有效"""
        text = "  3 要件仕様"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "3", "空格前缀不应影响章节号解析")

    def test_parse_chapter_id_with_fullwidth_space(self):
        """章节号前有全角空格：有效"""
        text = "　4 Introduction"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "4", "全角空格前缀不应影响章节号解析")

    def test_parse_chapter_id_with_ideographic_comma(self):
        """章节号后接顿号：有效"""
        text = "5、Requirement Specification"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "5", "顿号分隔不应影响章节号解析")

    def test_parse_chapter_id_with_invalid_keyword(self):
        """包含非法关键词（如 inch:）：应返回 None"""
        text = "2 inch: 示例标题"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "包含非法关键词时不应被识别为合法章节标题")

    def test_parse_chapter_id_with_number_only(self):
        """仅数字无语义内容：应返回 None"""
        text = "8 123abcxyz"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "纯数字+乱码内容不应被识别为合法章节标题")

    def test_parse_chapter_id_with_punctuation(self):
        """以标点开头的段落：应返回 None"""
        text = "9 ！？"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "以标点开头的内容不应被识别为合法章节标题")

    def test_parse_chapter_id_japanese_katakana(self):
        """日文片假名开头：有效"""
        text = "1.2 概要"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "1.2", "日文片假名开头的标题解析失败")

    def test_parse_chapter_id_chinese_title(self):
        """中文标题：有效"""
        text = "5 通信原理"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "5", "中文标题解析失败")

    def test_parse_chapter_id_japanese_title(self):
        """日文标题：有效"""
        text = "6　要件仕様"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "6", "日文标题解析失败")

    def test_parse_chapter_id_english_title(self):
        """英文标题：有效"""
        text = "4 Introduction"
        result = self.parser._parse_chapter_id(text)
        self.assertEqual(result, "4", "英文标题解析失败")

    def test_parse_chapter_id_mixed_language_invalid(self):
        """混合语言但无有效语义：应返回 None"""
        text = "7abc程度が望ましい。"
        result = self.parser._parse_chapter_id(text)
        self.assertIsNone(result, "混合语言但无实际语义不应被识别为合法章节标题")


# 运行测试案例
if __name__ == "__main__":
    unittest.main()