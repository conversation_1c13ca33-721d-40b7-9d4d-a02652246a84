#!/usr/bin/env python3
"""
最终版本：将 Draw.io 原始图像导出到 Excel
使用 Playwright + win32com，完美解决方案
"""

import os
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def drawio_to_excel(drawio_file: Optional[str] = None,
                   excel_file: str = "output/architecture_diagram.xlsx",
                   sheet_name: str = "架构图",
                   title: str = "系统架构图",
                   image_row: int = 3,
                   image_col: int = 1,
                   image_width: Optional[int] = None,
                   image_height: Optional[int] = None) -> Dict[str, Any]:
    """
    将 Draw.io 文件导出为原始图像并插入 Excel 的指定单元格

    Args:
        drawio_file: Draw.io 文件路径，如果为None则使用模板管理器
        excel_file: Excel 输出文件路径
        sheet_name: 工作表名称
        title: 图表标题
        image_row: 图像插入的行号（从1开始）
        image_col: 图像插入的列号（从1开始，或使用字母如'A','B'等）
        image_width: 图像宽度（像素），None表示自动
        image_height: 图像高度（像素），None表示自动

    Returns:
        操作结果字典
    """
    
    # 1. 检查依赖
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        return {
            "success": False,
            "message": "请先安装 Playwright: poetry add playwright && poetry run playwright install chromium"
        }
    
    # 2. 获取 Draw.io 文件
    if drawio_file is None:
        drawio_file = template_manager.get_template_path("block_diagram_file")
        if not drawio_file:
            return {"success": False, "message": "未找到 Draw.io 模板文件"}
    
    if not Path(drawio_file).exists():
        return {"success": False, "message": f"Draw.io 文件不存在: {drawio_file}"}
    
    # 3. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
    except Exception as e:
        return {"success": False, "message": f"读取文件失败: {e}"}
    
    # 4. 创建输出文件路径
    output_dir = Path(excel_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    png_file = output_dir / f"drawio_{Path(drawio_file).stem}.png"
    
    # 5. 使用 Playwright 导出图像
    try:
        encoded_content = urllib.parse.quote(xml_content)
        drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1#R{encoded_content}"
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.set_viewport_size({"width": 1920, "height": 1080})
            page.goto(drawio_url, timeout=60000)
            page.wait_for_timeout(8000)  # 等待加载
            page.screenshot(path=str(png_file), full_page=True, type="png")
            browser.close()
        
        if not png_file.exists() or png_file.stat().st_size == 0:
            return {"success": False, "message": "图像导出失败"}
            
    except Exception as e:
        return {"success": False, "message": f"Playwright 导出失败: {e}"}
    
    # 6. 插入到 Excel
    try:
        with ExcelUtil(excel_file, auto_create=True) as excel:
            # 创建工作表
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            # 添加标题
            excel.write_cell(sheet_name, 1, 1, title)
            excel.set_cell_style(sheet_name, 1, 1, CellStyle(
                font_size=16, font_bold=True, alignment_horizontal="center"
            ))
            
            # 插入图像到指定单元格
            # 处理列号（支持字母和数字）
            col_num = _convert_col_to_number(image_col) if isinstance(image_col, str) else image_col

            success = _insert_image(excel, sheet_name, str(png_file), image_row, col_num, image_width, image_height)
            
            if success:
                excel.save()
                return {
                    "success": True,
                    "message": "导出成功",
                    "excel_file": excel_file,
                    "png_file": str(png_file),
                    "sheet_name": sheet_name,
                    "image_position": f"行{image_row}, 列{image_col}"
                }
            else:
                return {
                    "success": False,
                    "message": "图像插入失败",
                    "png_file": str(png_file)  # PNG 文件仍然可用
                }
                
    except Exception as e:
        return {
            "success": False,
            "message": f"Excel 操作失败: {e}",
            "png_file": str(png_file)  # PNG 文件仍然可用
        }


def _insert_image(excel: ExcelUtil, sheet_name: str, image_path: str, row: int, col: int,
                 width: Optional[int] = None, height: Optional[int] = None) -> bool:
    """插入图像到 Excel"""
    try:
        from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import insert_image_to_excel
        
        # 方法1：使用现有工具
        if hasattr(excel, 'worksheet') and excel.worksheet:
            insert_image_to_excel(excel.worksheet, image_path, row, col)
            return True
        
        # 方法2：切换工作表后插入
        if hasattr(excel, 'workbook') and excel.workbook:
            excel.workbook.Worksheets(sheet_name).Activate()
            current_ws = excel.workbook.ActiveSheet
            insert_image_to_excel(current_ws, image_path, row, col)
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"图像插入失败: {e}")
        return False


def quick_export():
    """快速导出当前项目的 Draw.io 图表"""
    print("🚀 快速导出 Draw.io 图表到 Excel")
    print("=" * 40)
    
    result = drawio_to_excel(
        excel_file="output/quick_export.xlsx",
        title="项目架构图"
    )
    
    print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"消息: {result['message']}")
    
    if result['success']:
        print(f"📊 Excel文件: {result['excel_file']}")
        print(f"📸 PNG文件: {result['png_file']}")
        print(f"📋 工作表: {result['sheet_name']}")
        print("\n🎉 完成！请打开 Excel 文件查看原始图表")
    elif 'png_file' in result:
        print(f"💡 PNG 文件已生成: {result['png_file']}")
        print("   可以手动插入到 Excel 中")


def batch_export(drawio_files: list, output_dir: str = "output/batch_export"):
    """批量导出多个 Draw.io 文件"""
    print(f"📦 批量导出 {len(drawio_files)} 个文件")
    print("=" * 40)
    
    results = []
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    for i, drawio_file in enumerate(drawio_files, 1):
        print(f"\n{i}/{len(drawio_files)} 处理: {Path(drawio_file).name}")
        
        excel_file = output_path / f"{Path(drawio_file).stem}.xlsx"
        
        result = drawio_to_excel(
            drawio_file=drawio_file,
            excel_file=str(excel_file),
            title=f"架构图 - {Path(drawio_file).stem}"
        )
        
        results.append({
            "file": drawio_file,
            "result": result
        })
        
        print(f"   {'✅' if result['success'] else '❌'} {result['message']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['result']['success'])
    print(f"\n📊 批量导出完成:")
    print(f"   成功: {success_count}/{len(drawio_files)}")
    print(f"   输出目录: {output_dir}")
    
    return results


def demo():
    """演示所有功能"""
    print("🎯 Draw.io 到 Excel 导出工具演示")
    print("=" * 50)
    
    # 1. 快速导出
    print("\n1️⃣ 快速导出测试")
    quick_export()
    
    # 2. 自定义导出
    print("\n2️⃣ 自定义导出测试")
    result = drawio_to_excel(
        excel_file="output/custom_export.xlsx",
        sheet_name="自定义架构图",
        title="我的系统架构图"
    )
    print(f"自定义导出: {'✅' if result['success'] else '❌'} {result['message']}")
    
    print(f"\n🎉 演示完成！请查看 output 目录中的文件。")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_export()
        elif sys.argv[1] == "demo":
            demo()
        else:
            print("用法: python drawio_to_excel_final.py [quick|demo]")
    else:
        demo()
