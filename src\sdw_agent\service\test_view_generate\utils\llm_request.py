#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : llm_request.py
@Time    : 2025/7/28 17:04
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 主要用来构建大模型请求的入参
"""
from loguru import logger
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import AIMessage

from sdw_agent.llm.llm_util import get_ai_message_with_structured_output


def common_llm(system_str, prompt, model_type):
    """提供prompt以及填充到prompt中的信息，组织LLM调用方法入参、获取结果并返回"""
    try:
        logger.info(f"开始请求LLM，输入prompt为：{prompt}")
        # 组合成聊天提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
        ("system", system_str),
        ("user", "{question}"),
        ])
        invoke_dict = {
            "question": prompt
        }
        llm_response: AIMessage = get_ai_message_with_structured_output(chat_prompt, invoke_dict, model_type)
        logger.info(f"请求LLM成功，结果为：{llm_response}")
        return llm_response
    except Exception as e:
        logger.error(f"请求LLM失败，失败原因为：{str(e)}")
        return model_type
