import os
import sys
import subprocess  
import re
import json

from diffconfig import CONFIG_PATH, Config, BCompConfig
 
# 调用 Beyond Compare  
try:
    commitid = os.getenv('AD_GIT_COMMITID')
    if not commitid:      
        commitid ='last'
    
    commitid_previous = os.getenv('AD_GIT_COMMITID_PREVIOUS')
    if not commitid_previous:      
        commitid_previous ='last'

    config = Config(commitid)
    with open(config.logfile, 'a') as f:
        f.write(f'\n\n==={commitid}==\n')
        f.write('\n'.join(sys.argv))
                
    # 为了示例，我们直接在这里定义 arg1 和 arg2  
    left = sys.argv[1].replace("LOCAL=", "")  
    right = sys.argv[2].replace("REMOTE=", "")
    filepath = sys.argv[3].replace("MERGED=", "")
    
    if left == 'nul':
        left = BCompConfig.EMPTY_FILE
        type = Config.DIFF_NEW
    elif right == 'nul':
        right = BCompConfig.EMPTY_FILE
        type = Config.DIFF_DELETE
    else:
        type = Config.DIFF_MERGE
                
    bcconfig = BCompConfig()
    if not (bcconfig.is_ignored(filepath)):
        config.set_previous(commitid_previous)            
        diffpath = config.diffpath(filepath)
        if not os.path.exists(diffpath):
            print(diffpath, left, right)
            command = [
                bcconfig.get_path(), 
                '/silent /closescript',
                '@'+ bcconfig.get_config(filepath), diffpath, left, right] 
            subprocess.run(command, check=True) 
        config.add(filepath, diffpath, type)
    else:
        config.add(filepath, "", type)
        
    config.save() 

except subprocess.CalledProcessError as e:  
    print(f"Error occurred: {e}")
