"""
模块名称 实施代码文件比较

V字对应：【FID_DEV_0027】自动化生成FileCompare成果物

主要功能：
该功能是针对新旧两个版本的代码进行代码差分，生成FileCompare成果物，并结合两个版本之间每个Commit的Code 
Diff解析结果，更新FileCompare成果物中的代码差分确认结果。

"""

import os
import shutil
import re

from pathlib import Path
# project_root = Path(__file__).parent.parent.parent
# sys.path.append(str(project_root))
from openpyxl import load_workbook
from loguru import logger
from sdw_agent.config.env import ENV  # noqa
from sdw_agent.llm.model import dnkt_deepseekv2
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from sdw_agent.llm.llm_util import get_ai_message
from typing import Union, List, Tuple
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from typing import Optional  # Dict, List, Any, Optional


@register_workflow("FileCompare")
class FileCompareProcessor(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.output_dir = Path(ENV.config.output_data_path) / "filecompare"
        self.prompt = ENV.prompt.selfcheck_filecompare_prompt
        self.logger = logger.bind(name="FileCompareProcessor")

    def validate_input(self, input_paths: Union[str, List[str]]) -> bool:
        """
        验证输入路径是否为有效的 Excel 文件或目录

        Args:
            input_paths: 单个文件/目录路径或路径列表

        Returns:
            bool: 验证是否通过
        """
        try:
            # 1. 输入类型校验
            if not isinstance(input_paths, (str, list)):
                self.logger.error(f"输入类型错误: {type(input_paths)}（应为字符串或字符串列表）")
                return False

            # 2. 转换为统一列表格式
            if isinstance(input_paths, str):
                input_paths = [input_paths]

            # 3. 遍历每个路径进行校验
            for path in input_paths:
                # 3.1 非空校验
                if not path or not isinstance(path, str):
                    self.logger.error(f"路径为空或非字符串类型: {path}")
                    return False

                # 3.2 路径存在性校验
                if not os.path.exists(path):
                    self.logger.error(f"路径不存在: {path}")
                    return False

                # 3.3 目录校验（允许递归处理）
                if os.path.isdir(path):
                    self.logger.info(f"目录校验通过: {path}")
                    continue

                # 3.4 文件校验（必须为 Excel 格式）
                if os.path.isfile(path):
                    if not path.lower().endswith(('.xlsx', '.xls')):
                        self.logger.error(f"文件格式错误（非 Excel 文件）: {path}")
                        return False
                    self.logger.info(f"Excel 文件校验通过: {path}")
                else:
                    self.logger.error(f"路径既不是文件也不是目录: {path}")
                    return False

            self.logger.info("所有输入路径验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, input_paths: Union[str, List[str]]) -> WorkflowResult:
        """统一处理输入路径（文件/文件夹/列表）"""

        try:
            if isinstance(input_paths, str):
                input_paths = [input_paths]

            results = []
            for path in input_paths:
                if os.path.isfile(path) and path.lower().endswith(('.xlsx', '.xls')):
                    result = self.process_excel_file(path)
                    if result:
                        results.extend(result)
                elif os.path.isdir(path):
                    for root, dirs, files in os.walk(path):
                        for file in files:
                            if file.lower().endswith(('.xlsx', '.xls')):
                                file_path = os.path.join(root, file)
                                result = self.process_excel_file(file_path)
                                if result:
                                    results.extend(result)
                else:
                    self.logger.error(f"输入路径 {path} 不是有效的文件或目录")
            self.logger.info(f"FileCompare成果物生成成功: {results}")        
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="FileCompare成果物生成成功",
                data={"file_path": results}
            )
        except Exception as e:
            # 4. 捕获全局异常并返回失败状态
            self.logger.error(f"执行流程异常终止: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILURE,
                message=f"执行失败: {str(e)}",
                data={"file_path": []}
            )


    def read_column_data(self, file_path: str, sheet_name: str, start_row=7) -> List[int]:
        """读取指定列的非隐藏行"""
        try:
            wb = load_workbook(file_path, read_only=False, data_only=True)
            if sheet_name not in wb.sheetnames:
                raise ValueError(f"工作表 '{sheet_name}' 不存在")

            sheet = wb[sheet_name]
            data_array = []
            for row in sheet.iter_rows(min_row=start_row):
                current_row = row[0].row
                if sheet.row_dimensions[current_row].hidden:
                    continue
                data_array.append(current_row)

            self.logger.info(f"\n步骤1结果：从{sheet_name} 读取到 {len(data_array)} 条非隐藏行")
            return data_array
        except Exception as e:
            self.logger.error(f"读取列数据时出错：{str(e)}")
            return []

    def extract_context_data(
        self, file_path: str, sheet_name: str, target_rows: List[int], context_range=2
    ) -> List[Tuple[int, List[Tuple[int, List]]]]:
        """提取目标行的上下文数据"""
        try:
            wb = load_workbook(file_path, read_only=False, data_only=True, keep_links=False)
            if sheet_name not in wb.sheetnames:
                raise ValueError(f"工作表 '{sheet_name}' 不存在")

            sheet = wb[sheet_name]
            all_data = []

            for row_num in target_rows:
                if row_num < 1 or row_num > sheet.max_row:
                    self.logger.error(f"警告：行号 {row_num} 超出文件范围，已跳过")
                    continue

                start = max(1, row_num - context_range)
                end = min(sheet.max_row, row_num + context_range)

                context_data = []
                for r in range(start, end + 1):
                    row_data = [cell.value for cell in sheet[r]]
                    context_data.append((r, row_data))

                all_data.append((row_num, context_data))
                self.logger.info(f"\n目标行 {row_num} 的上下文数据 ({len(context_data)} 行)：")
                for row_num_inner, row_data_inner in context_data:
                    self.logger.info(f"行 {row_num_inner}: {row_data_inner}")

            return all_data
        except Exception as e:
            self.logger.error(f"提取上下文数据时出错：{str(e)}")
            return []

    def requests_llm(self, query: str, system_prompt: str) -> str:
        """调用 AI 模型处理查询"""
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", "{system_prompt}"),
            ("user", "{query}"),
        ])
        invoke_dict = {
            "system_prompt": system_prompt,
            "query": query
        }
        llm_response = get_ai_message(chat_prompt, invoke_dict)
        return llm_response.content

    def process_excel_file(self, file_path: str) -> List[str]:
        """处理单个 Excel 文件"""
        try:
            wb = load_workbook(file_path, read_only=True, data_only=True)
            sheet_names = wb.sheetnames

            if len(sheet_names) < 3:
                self.logger.error("filecompare成果物模版异常，跳过当前文件")
                return []

            if sheet_names[0] != "Cover" or sheet_names[1] != "FileList":
                self.logger.error("filecompare成果物模版异常，跳过当前文件")
                return []

            combined_results_all_sheets = []
            START_ROW = 7
            CONTEXT_RANGE = 2
            # 拷贝目标文件
            new_excel_file_path = self.copy_template_with_new_name(file_path, "AI", str(self.output_dir))
            combined_results_all_sheets.append(new_excel_file_path)

            for sheet_index in range(2, len(sheet_names)):
                target_sheet_name = sheet_names[sheet_index]
                self.logger.info(f"正在处理文件：{file_path} 中的工作表：{target_sheet_name}")

                data_array = self.read_column_data(file_path, target_sheet_name, START_ROW)
                combined_results = []

                if data_array:
                    target_rows = [int(x) for x in data_array if isinstance(x, (int, float))]
                    extract_result = self.extract_context_data(file_path, target_sheet_name, target_rows, CONTEXT_RANGE)

                    if extract_result:
                        self.logger.info("\n步骤3：开始向AI模型发送请求...")

                        for target_row, context in extract_result:
                            query = "\n".join([f"{row_data}" for _, row_data in context])
                            ai_response = self.requests_llm(query, self.prompt)
                            combined_results.append((target_row, [ai_response]))

                self.logger.info(combined_results)
                if combined_results:
                    self.process_excel_with_context_data(
                        combined_results,
                        target_sheet_name,
                        new_excel_file_path
                    )
            return combined_results_all_sheets
        except Exception as e:
            self.logger.error(f"处理文件时出错：{str(e)}")
            raise

    def write_context_data_to_excel(
        self, context_data: List[Tuple[int, List]], excel_file: str, target_sheet: str, target_cols=("G",)
    ) -> None:
        """写入上下文数据到 Excel"""
        try:
            wb = load_workbook(excel_file)
            if target_sheet not in wb.sheetnames:
                wb.create_sheet(target_sheet)
            sheet = wb[target_sheet]

            for row_num, row_values in context_data:
                if len(row_values) != len(target_cols):
                    raise ValueError(f"行 {row_num} 的数据长度 {len(row_values)} 与目标列数量 {len(target_cols)} 不匹配")

                for col_idx, col_letter in enumerate(target_cols):
                    cell = sheet[f"{col_letter}{row_num}"]
                    cell.value = row_values[col_idx]

            wb.save(excel_file)
            self.logger.info(f"成功写入 {len(context_data)} 行数据到工作表 '{target_sheet}' 的列 {target_cols}")
        except Exception as e:
            self.logger.error(f"写入 Excel 文件失败：{e}")

    def copy_template_with_new_name(
        self, template_file: str, new_name: str, output_directory: str = "."
    ) -> str:
        """复制模板文件并重命名"""
        template_path = Path(template_file)
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)

        if not template_path.exists():
            self.logger.error(f"模板文件 '{template_path}' 不存在。")
            return ""

        file_stem = template_path.stem
        file_suffix = template_path.suffix
        new_file_name = f"{file_stem}_{new_name}{file_suffix}"
        new_file_path = output_dir / new_file_name

        if new_file_path.exists():
            self.logger.info("目标文件已存在，跳过复制")
            return str(new_file_path)

        try:
            shutil.copy(str(template_path), str(new_file_path))
            self.logger.info(f"文件已复制为: {new_file_path}")
        except Exception as e:
            self.logger.error(f"复制文件时出错: {e}")

        return str(new_file_path)

    def process_excel_with_context_data(
        self, context_data: List[Tuple[int, List]], target_sheet: str, new_excel_file_path: str
    ) -> str:

        self.write_context_data_to_excel(context_data, new_excel_file_path, target_sheet, target_cols=("G",))

        jirano = get_jirano(new_excel_file_path)
        updated_context_data = [
            (row_num, [jirano]) for row_num, _ in context_data
        ]

        self.write_context_data_to_excel(updated_context_data, new_excel_file_path, target_sheet, target_cols=("F",))




def get_jirano(excel_file: str) -> str:
    """
    从指定 Excel 文件的 'Cover' 工作表 B21 单元格中提取 commitmsg，
    并使用正则表达式提取 jirano（格式如 MET19PFV3-\d+）
    如果有多个，用换行符拼接后返回，方便写入 Excel 单元格中实现多行显示
    """
    try:
        wb = load_workbook(excel_file)
        sheet = wb["Cover"]
        commitmsg = sheet["B21"].value or ""
        matches = re.findall(r"MET19PFV3-\d+", str(commitmsg))

        # 用换行符拼接所有匹配项
        return "\n".join(matches) if matches else ""

    except Exception as e:
        print(f"提取 jirano 失败：{e}")
        return ""

if __name__ == "__main__":

    # 创建处理器实例
    processor = FileCompareProcessor()

    # # 单个文件
    single_file = "./input/12 - 副本.xlsx"
    results = processor.process(single_file)
    print("处理结果：", results)

    # # 多个文件
    # multiple_files = ["./input/888.xlsx", "./input/999.xlsx"]
    # results = processor.process(multiple_files)
    # print("处理结果：", results)

    # 文件夹路径
    # folder_path = "./input"
    # results = processor.process(folder_path)
    # print("处理结果：", results)    