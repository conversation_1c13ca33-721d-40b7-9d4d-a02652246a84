io:
  input:
    base_dir: "性能测试手顺"           # Excel 数据库主目录
    allowed_extensions: [".xlsx", ".xls"]  # 允许的文件扩展名
    database_names:
      - "1-ROM&RAM测试"
      - "2-Stack统计"
      - "3-负荷测试相关资料"
      - "4-启动时间测试"
  output:
    md_filename: "output.md"         # markdown输出文件名
    output_dir: "."                  # 输出目录（当前目录）

llm:
  provider: "azure"
  api_base: "https://sdw-dev.openai.azure.com"
  api_version: "2025-01-01-preview"
  deployment: "gpt-4o"
  api_key: "请在此填写你的API KEY"
  model: "gpt-4o"
  max_tokens: 16384
  system_message: |
    你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题

prompts:
  database_decision: |
    ##角色：
    你是一个数据分析专家，现在根据用户输入，分析我们需要哪一个数据库。

    ##任务：
    现在需要你根据用户的输入分析我们需要什么数据库，只需要你返回对应的数据库名称。

    ##数据库名称：
    1-ROM&RAM测试
    2-Stack统计
    3-负荷测试相关资料
    4-启动时间测试

    ##需求:
    - 注意你的输出只能是完整的数据库名称：即包含数字-数据库名。
    - 你的输出中不能包含任何其他内容，有且仅有对应数据库名称
    - 如果用户输入和我们的数据库没有任何关系，则输出非功能需求
    - CANoe地图发送手顺相关内容也属于 3-负荷测试相关资料

  suggestion: |
    ## 角色：
    你是一个数据分析专家，现在根据用户输入，结合我们提供的相关文档内容，指引用户进行最后的生成。

    ## 相关文档内容：
    {excel_content}

    ## 参考文件路径
    {file_paths}

    ## 输出内容
    1. 你需要按步骤输出操作顺序方法
    2. 如果相关文档内容涉及到图片，你需要提示用户：注意操作步骤中有图片相关内容，请参照参考文档确认
    3. 你需要在最后提供参考文件路径

    ## 需求：
    - 注意你的输出必须是按照markdown的形式输出
    - 注意你的输出不能包含无关内容
    - 注意你的输出或者参考文档中涉及到图片，你需要在输出最后提示用户：
        注意操作步骤中有图片相关内容，请参照参考文档确认
    - 你需要在最后提供参考文件路径
