"""
Code Analysis Utilities
代码分析工具类
"""
import os
import re
from typing import <PERSON>ple
from loguru import logger

from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo
from sdw_agent.service.warning_code_gen_check.util.excel_validation_util import ExcelValidationUtils
from sdw_agent.util.function_util import find_file_path
import xlwings as xw


class CodeAnalysisUtils:
    """代码分析工具类"""

    @staticmethod
    def extract_macro_values(repo_path):
        """
        提取代码仓库中两个宏定义的值：
        - NUMCNTTS_DSPWRN: 来自 dspwrn_cfg.h 文件
        - DWRNRQ_ARRYSIZE: 来自 dspwrnctl.h 文件

        参数:
            repo_path (str): 代码仓库的本地路径。

        返回:
            dict: 包含宏定义值和文件路径的结果字典。
        """
        if not os.path.exists(repo_path):
            raise ValueError("路径无效，请检查输入的代码仓库路径！")

        # 初始化结果字典
        result = {}

        # 查找并提取 `NUMCNTTS_DSPWRN` 的值
        file_path_cfg, numcntts_dspwrn_value = CodeAnalysisUtils.search_and_extract_macro(repo_path, "dspwrn_cfg.h",
                                                                                          "NUMCNTTS_DSPWRN")
        result["NUMCNTTS_DSPWRN"] = {
            "file_path": file_path_cfg,
            "value": numcntts_dspwrn_value
        }

        # 查找并提取 `DWRNRQ_ARRYSIZE` 的值
        file_path_ctl, dwrnrq_arraysize_value = CodeAnalysisUtils.search_and_extract_macro(repo_path, "dspwrnctl.h",
                                                                                           "DWRNRQ_ARRYSIZE")
        result["DWRNRQ_ARRYSIZE"] = {
            "file_path": file_path_ctl,
            "value": dwrnrq_arraysize_value
        }

        return result

    @staticmethod
    def search_and_extract_macro(repo_path, file_name, macro_name):
        """
        在指定路径下查找文件，读取文件内容并提取指定宏定义的值。

        参数:
            repo_path (str): 代码仓库的本地路径。
            file_name (str): 要查找的文件名。
            macro_name (str): 宏定义名称。

        返回:
            tuple: (文件路径, 宏定义的值) 或 (None, None) 如果未找到。
        """
        # 遍历代码仓库路径，查找目标文件
        for root, dirs, files in os.walk(repo_path):
            if file_name in files:
                file_path = os.path.join(root, file_name)

                # 打开文件并读取内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                    # 使用正则表达式提取宏定义的值
                    pattern = rf"#define\s+{macro_name}\s+(\S+)"
                    match = re.search(pattern, content)
                    logger.debug(match.group(0))

                    if match:
                        return file_path, match.group(1).strip("()")  # 返回文件路径和宏定义的值

        return None, 0

    @staticmethod
    def is_warning_num_code_exceeds(repo_path, warning_info_dict: WarningChangeInfo):
        num_code_dict = CodeAnalysisUtils.extract_macro_values(repo_path)
        new_total_row = warning_info_dict.after_warning_num
        if new_total_row > int(num_code_dict["NUMCNTTS_DSPWRN"]["value"]):
            logger.error(
                f"Warning number exceeds the NUMCNTTS_DSPWRN: {new_total_row} > {num_code_dict['NUMCNTTS_DSPWRN']['value']}")
            return True, f"NUMCNTTS_DSPWRN: {new_total_row} > {num_code_dict['NUMCNTTS_DSPWRN']['value']}"
        if new_total_row > int(num_code_dict["DWRNRQ_ARRYSIZE"]["value"]) * 32:
            logger.error(
                f"Warning number exceeds the DWRNRQ_ARRYSIZE: {new_total_row} > 32 * {num_code_dict['DWRNRQ_ARRYSIZE']['value']}")
            return True, f"DWRNRQ_ARRYSIZE: {new_total_row} > 32 * {num_code_dict['DWRNRQ_ARRYSIZE']['value']}"
        return False, (
            f"检查无问题, DWRNRQ_ARRYSIZE {num_code_dict['DWRNRQ_ARRYSIZE']['value']} NUMCNTTS_DSPWRN {num_code_dict['NUMCNTTS_DSPWRN']['value']}\n"
            f"总告警量{new_total_row}")

    @staticmethod
    def is_wrndtcfggetreq_code_error(code_folder: str) -> Tuple[bool, str]:
        """
        检查wrndtcfggetreq函数代码错误
        
        Returns:
            (是否有错误, 错误描述)
        """
        try:
            from sdw_agent.util.function_util import find_file_path, extract_function_body

            code_path = find_file_path(code_folder, r"wrndtcfg.c")
            target_function = "vd_g_WrndtcfgGetReq00"

            wrndtcfggetreq_code = extract_function_body(code_path, target_function)
            CodeAnalysisUtils._count_matching_lines_in_function(wrndtcfggetreq_code)
            is_warning_id_inc = CodeAnalysisUtils._check_wrndtcfggetreq_function_inc(wrndtcfggetreq_code)

            if not is_warning_id_inc[0]:
                logger.error("vd_g_WrndtcfgGetReq00 函数存在ID没有递增的情况,请检查确认")
                return True, f"vd_g_WrndtcfgGetReq00 函数存在ID没有递增的情况,请检查确认: {is_warning_id_inc[1]}"

            return False, "函数代码检查通过"

        except Exception as e:
            logger.error(f"检查wrndtcfggetreq函数代码错误失败: {str(e)}")
            return True, f"检查失败: {str(e)}"

    @staticmethod
    def extract_array_definition(file_path, pattern):
        """
        从 .c 文件中提取指定数组定义内容。

        参数:
            file_path (str): .c 文件的路径。

        返回:
            str: 提取到的数组定义内容，如果未找到则返回 None。
        """
        # 打开并读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 正则表达式匹配数组定义
        match = re.search(pattern, content, re.DOTALL)  # 使用 re.DOTALL 处理换行

        # 返回匹配结果
        if match:
            return match.group(0)
        else:
            return None

    @staticmethod
    def is_u4_dwtt_lv3supbit_exceed_max_size(code_folder, warning_info_dict: WarningChangeInfo):
        code_path = find_file_path(code_folder, r"dwstoutgntr.prm")
        pattern = r"static\s+const\s+U4\s+u4_DWTT_LV3SUPBIT\[.*?\]\s*=\s*\{.*?\};"
        lv3supbit_code = CodeAnalysisUtils.extract_array_definition(code_path, pattern)
        is_exceed = CodeAnalysisUtils.check_bitmap_size(lv3supbit_code, warning_info_dict.after_warning_num)
        # logger.info(f"u4_DWTT_LV3SUPBIT exceed max size: {is_exceed}")
        return is_exceed

    @staticmethod
    def is_u4_dwtt_lv4supbit_exceed_max_size(code_folder, warning_info_dict: WarningChangeInfo):
        code_path = find_file_path(code_folder, r"dwstoutgntr.prm")
        pattern = r"static\s+const\s+U4\s+u4_DWTT_LV4SUPBIT\[.*?\]\s*=\s*\{.*?\};"
        lv4supbit_code = CodeAnalysisUtils.extract_array_definition(code_path, pattern)
        is_exceed = CodeAnalysisUtils.check_bitmap_size(lv4supbit_code, warning_info_dict.after_warning_num)
        logger.info(f"u4_DWTT_LV4SUPBIT exceed max size: {is_exceed}")
        return is_exceed

    @staticmethod
    def is_u4_dwtt_lv5supbit_exceed_max_size(code_folder, warning_info_dict: WarningChangeInfo):
        code_path = find_file_path(code_folder, r"dwstoutgntr.prm")
        pattern = r"static\s+const\s+U4\s+u4_DWTT_LV5SUPBIT\[.*?\]\s*=\s*\{.*?\};"
        lv5supbit_code = CodeAnalysisUtils.extract_array_definition(code_path, pattern)
        is_exceed = CodeAnalysisUtils.check_bitmap_size(lv5supbit_code, warning_info_dict.after_warning_num)
        # logger.info(f"u4_DWTT_LV5SUPBIT exceed max size: {is_exceed}")
        return is_exceed

    @staticmethod
    def is_u4_dwtt_exceed_max_size(code_folder, warning_info_dict: WarningChangeInfo):
        is_u4_err_l3 = CodeAnalysisUtils.is_u4_dwtt_lv3supbit_exceed_max_size(code_folder, warning_info_dict)
        is_u4_err_l4 = CodeAnalysisUtils.is_u4_dwtt_lv4supbit_exceed_max_size(code_folder, warning_info_dict)
        is_u4_err_l5 = CodeAnalysisUtils.is_u4_dwtt_lv5supbit_exceed_max_size(code_folder, warning_info_dict)
        is_u4_err = is_u4_err_l3[0] or is_u4_err_l4[0] or is_u4_err_l5[0]

        if is_u4_err:
            logger.error("u4_DWTT_LV3SUPBIT or u4_DWTT_LV4SUPBIT or u4_DWTT_LV5SUPBIT exceed max size")
            desc = (
                f"u4_dwtt_lv3 err: {is_u4_err_l3}  u4_dwtt_lv4 err: {is_u4_err_l4}  u4_dwtt_lv5 err: {is_u4_err_l5} \n "
                f"超过最大长度{warning_info_dict.after_warning_num}")
        else:
            desc = f"u4_dwtt_lv3 : {is_u4_err_l3[1]}  u4_dwtt_lv4 : {is_u4_err_l4[1]}  u4_dwtt_lv5 : {is_u4_err_l5[1]} \n 确认OK"

        return is_u4_err, desc

    @staticmethod
    def _count_matching_lines_in_function(function_body: str) -> int:
        """
        统计函数体中符合特定模式的代码行数
        
        Args:
            function_body: 函数体字符串
            
        Returns:
            符合条件的代码行数
        """
        try:
            # 正则表达式匹配以 *u4_p_req |= 开头，包含 (U4)u1_GETREQ_ID 的行
            line_pattern = r'\*u4_p_req\s*\|=\s*\(U4\).*?u1_GETREQ_ID\d+\(\).*?;'

            # 查找所有匹配的行
            matching_lines = re.findall(line_pattern, function_body)
            logger.debug(f"匹配的行数：{len(matching_lines)}")

            return len(matching_lines)

        except Exception as e:
            logger.error(f"统计匹配行数失败: {str(e)}")
            return 0

    @staticmethod
    def _check_wrndtcfggetreq_function_inc(func_body: str) -> Tuple[bool, str]:
        """
        检查函数体中的ID和偏移量是否递增
        
        Args:
            func_body: 函数体代码字符串
            
        Returns:
            (是否通过检查, 错误信息)
        """
        try:
            # 正则匹配 ID 和偏移量
            id_pattern = re.compile(r"u1_GETREQ_ID(\d+)\(\)")
            shift_pattern = re.compile(r"<<\s*(\d+)")

            # 分割代码块，按 u4_p_req++ 分组
            blocks = func_body.split("u4_p_req++")

            # 初始化结果
            is_valid = True
            error_messages = []

            # 遍历每个代码块
            for block_index, block in enumerate(blocks):
                # 提取 ID 和偏移量
                ids = [int(match) for match in id_pattern.findall(block)]
                shifts = [int(match) for match in shift_pattern.findall(block)]

                # 检查 ID 是否递增
                for i in range(1, len(ids)):
                    if ids[i] != ids[i - 1] + 1:
                        is_valid = False
                        error_messages.append(
                            f"块 {block_index + 1}: ID {ids[i - 1]} 后的 ID {ids[i]} 不是递增的"
                        )

                # 检查偏移量是否递增
                for i in range(1, len(shifts)):
                    if shifts[i] != shifts[i - 1] + 1:
                        is_valid = False
                        error_messages.append(
                            f"块 {block_index + 1}: 偏移量 {shifts[i - 1]} 后的偏移量 {shifts[i]} 不是递增的"
                        )

                # # 检查偏移量是否从 0 开始
                # if shifts and shifts[0] != 0:
                #     is_valid = False
                #     error_messages.append(
                #         f"块 {block_index + 1}: 偏移量没有从 0 开始"
                #     )

            # 返回结果
            if is_valid:
                return True, "检查通过"
            else:
                return False, "\n".join(error_messages)

        except Exception as e:
            return False, f"发生错误: {e}"

    @staticmethod
    def check_bitmap_size(bitmap_str, max_size):
        """
        检查 bitmap 的位数是否超过 max_size。

        参数:
            bitmap_str (str): bitmap 的字符串定义。
            max_size (int): 最大允许的位数。

        返回:
            bool: 如果位数超过 max_size，返回 True，否则返回 False。
        """
        # 正则表达式匹配 (U4)0x 开头的内容
        pattern = r'\(U4\)0x[0-9A-Fa-f]+'

        # 使用 re.findall 匹配所有符合条件的内容
        matches = re.findall(pattern, bitmap_str)

        array_len = (len(matches) + 1) * 32
        logger.info(f"array_len: {array_len} warning max_size {max_size}")

        # 比较总位数是否超过 max_size
        return array_len < max_size, array_len

    @staticmethod
    def check_msg2_code(file_path, code_folder):
        try:
            # 创建Excel应用
            app = xw.App(visible=False)
            app.display_alerts = False
            app.screen_updating = False

            # 打开工作簿
            wb = app.books.open(file_path)
            result = ExcelValidationUtils.check_continuous_text_in_column(wb, {"dspmnscrl_msg2.prm": 2})

            code_path = find_file_path(code_folder, r"dspmnscrl_msg2.prm")
            msg2_code_array = CodeAnalysisUtils.extract_msg2_code_array(code_path)
            is_exceed, array_len = CodeAnalysisUtils.check_bitmap_size(msg2_code_array, result["dspmnscrl_msg2.prm"][0])
            logger.info(f"msg2_code_array exceed max size: {is_exceed}")
            desc = f"msg2_code_array长度为 {array_len} 小于告警数量请检查" if is_exceed else f"msg2_code_array长度为 {array_len}, 确认OK"
            return is_exceed, desc
        finally:
            wb.close()
            app.quit()
    @staticmethod
    def extract_msg2_code_array(code_path):
        """
        从代码字符串中提取指定的 static const U4 数组定义。

        参数:
            code_str (str): 包含代码的字符串。

        返回:
            str: 提取到的数组定义内容，如果未找到则返回 None。
        """

        # 正则表达式匹配数组定义
        with open(code_path, "r", encoding="utf-8", errors="ignore") as file:
            code_str = file.read()

        pattern = r"static\s+const\s+U4\s+u4_DMNSCRLMSG2_SKPBITS\[.*?\]\s*=\s*\{.*?\};"

        # 使用 re.DOTALL 处理多行内容
        match = re.search(pattern, code_str, re.DOTALL)

        # 返回匹配结果
        if match:
            return match.group(0)
        else:
            return None
