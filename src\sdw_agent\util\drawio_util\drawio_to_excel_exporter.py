#!/usr/bin/env python3
"""
Draw.io 图表导出到 Excel 工具
支持将 Draw.io 文件直接作为图像插入到 Excel 中
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, Union

from loguru import logger
from openpyxl.drawing.image import Image as OpenpyxlImage

from sdw_agent.util.excel.core import ExcelUtil, CellStyle


class DrawioToExcelExporter:
    """Draw.io 图表导出到 Excel 的工具类"""
    
    def __init__(self, drawio_executable: Optional[str] = None):
        """
        初始化导出器
        
        Args:
            drawio_executable: drawio-desktop 可执行文件路径，如果为None则自动查找
        """
        self.drawio_executable = drawio_executable or self._find_drawio_executable()
        
    def _find_drawio_executable(self) -> Optional[str]:
        """自动查找 drawio-desktop 可执行文件"""
        possible_paths = [
            "drawio",  # 如果在PATH中
            "draw.io",
            r"C:\Program Files\draw.io\draw.io.exe",
            r"C:\Program Files (x86)\draw.io\draw.io.exe",
            "/Applications/draw.io.app/Contents/MacOS/draw.io",  # macOS
            "/usr/bin/drawio",  # Linux
            "/usr/local/bin/drawio"
        ]
        
        for path in possible_paths:
            if self._check_executable(path):
                logger.info(f"找到 draw.io 可执行文件: {path}")
                return path
                
        logger.warning("未找到 draw.io 可执行文件，请手动指定路径")
        return None
    
    def _check_executable(self, path: str) -> bool:
        """检查可执行文件是否存在且可用"""
        try:
            result = subprocess.run([path, "--version"], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False
    
    def export_drawio_to_image(self, 
                              drawio_file: str, 
                              output_image: Optional[str] = None,
                              image_format: str = "png",
                              page_index: int = 0,
                              scale: float = 1.0,
                              crop: bool = True) -> Optional[str]:
        """
        将 Draw.io 文件导出为图像
        
        Args:
            drawio_file: Draw.io 文件路径
            output_image: 输出图像路径，如果为None则自动生成
            image_format: 图像格式 (png, jpg, svg, pdf)
            page_index: 页面索引（从0开始）
            scale: 缩放比例
            crop: 是否裁剪空白区域
            
        Returns:
            导出的图像文件路径，失败返回None
        """
        if not self.drawio_executable:
            logger.error("未找到 draw.io 可执行文件")
            return None
            
        if not Path(drawio_file).exists():
            logger.error(f"Draw.io 文件不存在: {drawio_file}")
            return None
        
        # 生成输出文件路径
        if output_image is None:
            drawio_path = Path(drawio_file)
            output_image = str(drawio_path.parent / f"{drawio_path.stem}.{image_format}")
        
        # 构建命令行参数
        cmd = [
            self.drawio_executable,
            "--export",
            "--format", image_format,
            "--page-index", str(page_index),
            "--scale", str(scale),
            "--output", output_image
        ]
        
        if crop:
            cmd.append("--crop")
            
        cmd.append(drawio_file)
        
        try:
            logger.info(f"正在导出 Draw.io 图像: {drawio_file} -> {output_image}")
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=30)
            
            if result.returncode == 0:
                if Path(output_image).exists():
                    logger.success(f"Draw.io 图像导出成功: {output_image}")
                    return output_image
                else:
                    logger.error("导出命令执行成功但未找到输出文件")
                    return None
            else:
                logger.error(f"Draw.io 导出失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("Draw.io 导出超时")
            return None
        except Exception as e:
            logger.error(f"Draw.io 导出异常: {str(e)}")
            return None
    
    def insert_drawio_to_excel(self,
                              drawio_file: str,
                              excel_file: str,
                              sheet_name: str = "Sheet1",
                              start_row: int = 1,
                              start_col: int = 1,
                              image_format: str = "png",
                              scale: float = 1.0,
                              max_width: Optional[int] = None,
                              max_height: Optional[int] = None,
                              title: Optional[str] = None) -> Dict[str, Any]:
        """
        将 Draw.io 图表插入到 Excel 中
        
        Args:
            drawio_file: Draw.io 文件路径
            excel_file: Excel 文件路径
            sheet_name: 工作表名称
            start_row: 起始行（从1开始）
            start_col: 起始列（从1开始）
            image_format: 图像格式
            scale: 缩放比例
            max_width: 最大宽度（像素）
            max_height: 最大高度（像素）
            title: 图表标题
            
        Returns:
            包含操作结果的字典
        """
        try:
            # 1. 导出 Draw.io 为图像
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_image = os.path.join(temp_dir, f"drawio_export.{image_format}")
                
                exported_image = self.export_drawio_to_image(
                    drawio_file=drawio_file,
                    output_image=temp_image,
                    image_format=image_format,
                    scale=scale
                )
                
                if not exported_image:
                    return {
                        "success": False,
                        "message": "Draw.io 图像导出失败",
                        "excel_file": excel_file
                    }
                
                # 2. 插入图像到 Excel
                with ExcelUtil(excel_file, auto_create=True) as excel:
                    # 确保工作表存在
                    if sheet_name not in excel.get_sheet_names():
                        excel.create_sheet(sheet_name)
                    
                    # 添加标题（如果提供）
                    current_row = start_row
                    if title:
                        excel.write_cell(sheet_name, current_row, start_col, title)
                        title_style = CellStyle(
                            font_size=14,
                            font_bold=True,
                            alignment_horizontal="center"
                        )
                        excel.set_cell_style(sheet_name, current_row, start_col, title_style)
                        current_row += 2  # 留出空行
                    
                    # 插入图像
                    success = self._insert_image_to_excel_sheet(
                        excel=excel,
                        sheet_name=sheet_name,
                        image_path=exported_image,
                        row=current_row,
                        col=start_col,
                        max_width=max_width,
                        max_height=max_height
                    )
                    
                    if success:
                        excel.save()
                        logger.success(f"Draw.io 图表已成功插入到 Excel: {excel_file}")
                        return {
                            "success": True,
                            "message": "Draw.io 图表插入成功",
                            "excel_file": excel_file,
                            "sheet_name": sheet_name,
                            "image_position": f"行{current_row}, 列{start_col}"
                        }
                    else:
                        return {
                            "success": False,
                            "message": "图像插入 Excel 失败",
                            "excel_file": excel_file
                        }
                        
        except Exception as e:
            error_msg = f"插入 Draw.io 图表到 Excel 时发生异常: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "excel_file": excel_file
            }

    def _insert_image_to_excel_sheet(self,
                                   excel: ExcelUtil,
                                   sheet_name: str,
                                   image_path: str,
                                   row: int,
                                   col: int,
                                   max_width: Optional[int] = None,
                                   max_height: Optional[int] = None) -> bool:
        """
        将图像插入到 Excel 工作表中

        Args:
            excel: ExcelUtil 实例
            sheet_name: 工作表名称
            image_path: 图像文件路径
            row: 行号
            col: 列号
            max_width: 最大宽度
            max_height: 最大高度

        Returns:
            是否成功插入
        """
        try:
            # 使用 openpyxl 的方式插入图像
            if hasattr(excel, '_get_worksheet'):
                ws = excel._get_worksheet(sheet_name)
                if ws:
                    img = OpenpyxlImage(image_path)

                    # 调整图像大小
                    if max_width or max_height:
                        original_width = img.width
                        original_height = img.height

                        if max_width and original_width > max_width:
                            scale_factor = max_width / original_width
                            img.width = max_width
                            img.height = int(original_height * scale_factor)

                        if max_height and img.height > max_height:
                            scale_factor = max_height / img.height
                            img.height = max_height
                            img.width = int(img.width * scale_factor)

                    # 设置图像位置
                    target_cell = ws.cell(row=row, column=col)
                    img.anchor = target_cell.coordinate

                    # 添加图像
                    ws.add_image(img)

                    # 调整行高和列宽以适应图像
                    ws.row_dimensions[row].height = max(ws.row_dimensions[row].height or 15,
                                                       img.height * 0.75)  # 转换为点

                    return True

            logger.error("无法获取工作表对象")
            return False

        except Exception as e:
            logger.error(f"插入图像失败: {str(e)}")
            return False

    def batch_insert_drawio_to_excel(self,
                                   drawio_files: list,
                                   excel_file: str,
                                   sheet_name: str = "Diagrams",
                                   images_per_row: int = 2,
                                   image_spacing: int = 2,
                                   max_width: int = 400,
                                   max_height: int = 300) -> Dict[str, Any]:
        """
        批量将多个 Draw.io 图表插入到同一个 Excel 文件中

        Args:
            drawio_files: Draw.io 文件路径列表
            excel_file: Excel 文件路径
            sheet_name: 工作表名称
            images_per_row: 每行图像数量
            image_spacing: 图像间距（行/列数）
            max_width: 图像最大宽度
            max_height: 图像最大高度

        Returns:
            包含操作结果的字典
        """
        try:
            results = []
            current_row = 1
            current_col = 1

            with ExcelUtil(excel_file, auto_create=True) as excel:
                # 确保工作表存在
                if sheet_name not in excel.get_sheet_names():
                    excel.create_sheet(sheet_name)

                for i, drawio_file in enumerate(drawio_files):
                    # 计算当前图像的位置
                    if i > 0 and i % images_per_row == 0:
                        current_row += int(max_height / 20) + image_spacing  # 估算行数
                        current_col = 1
                    elif i > 0:
                        current_col += int(max_width / 64) + image_spacing  # 估算列数

                    # 导出并插入图像
                    with tempfile.TemporaryDirectory() as temp_dir:
                        temp_image = os.path.join(temp_dir, f"drawio_export_{i}.png")

                        exported_image = self.export_drawio_to_image(
                            drawio_file=drawio_file,
                            output_image=temp_image,
                            image_format="png",
                            scale=1.0
                        )

                        if exported_image:
                            success = self._insert_image_to_excel_sheet(
                                excel=excel,
                                sheet_name=sheet_name,
                                image_path=exported_image,
                                row=current_row,
                                col=current_col,
                                max_width=max_width,
                                max_height=max_height
                            )

                            results.append({
                                "file": drawio_file,
                                "success": success,
                                "position": f"行{current_row}, 列{current_col}"
                            })
                        else:
                            results.append({
                                "file": drawio_file,
                                "success": False,
                                "error": "图像导出失败"
                            })

                excel.save()

            success_count = sum(1 for r in results if r["success"])
            return {
                "success": success_count > 0,
                "message": f"成功插入 {success_count}/{len(drawio_files)} 个图表",
                "excel_file": excel_file,
                "sheet_name": sheet_name,
                "details": results
            }

        except Exception as e:
            error_msg = f"批量插入 Draw.io 图表时发生异常: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "excel_file": excel_file,
                "details": []
            }


def demo_drawio_to_excel():
    """演示如何使用 DrawioToExcelExporter"""
    print("🎯 Draw.io 导出到 Excel 演示")
    print("=" * 50)

    # 创建导出器
    exporter = DrawioToExcelExporter()

    # 示例：单个文件导出
    drawio_file = "example.drawio"  # 替换为实际的 drawio 文件路径
    excel_file = "output.xlsx"

    if Path(drawio_file).exists():
        result = exporter.insert_drawio_to_excel(
            drawio_file=drawio_file,
            excel_file=excel_file,
            sheet_name="架构图",
            title="系统架构图",
            max_width=800,
            max_height=600
        )

        print(f"📊 导出结果:")
        print(f"   成功: {result['success']}")
        print(f"   消息: {result['message']}")
        if result['success']:
            print(f"   Excel文件: {result['excel_file']}")
            print(f"   工作表: {result['sheet_name']}")
            print(f"   图像位置: {result['image_position']}")
    else:
        print(f"❌ Draw.io 文件不存在: {drawio_file}")


if __name__ == "__main__":
    demo_drawio_to_excel()
