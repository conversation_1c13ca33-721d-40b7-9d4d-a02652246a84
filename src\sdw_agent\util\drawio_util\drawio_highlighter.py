"""
Draw.io 模块高亮工具
支持根据路径对模块进行标红等高亮操作
"""

import xml.etree.ElementTree as ET
from typing import List, Dict, Optional
from sdw_agent.util.drawio_util.drawio_structure_reader import DrawioStructureReader, ModuleInfo


class DrawioHighlighter:
    """Draw.io模块高亮器"""

    def __init__(self, drawio_file_path: str, reader: DrawioStructureReader = None):
        self.file_path = drawio_file_path
        self.reader = reader if reader is not None else DrawioStructureReader(drawio_file_path)
        self.tree = None
        self.root = None
        
        # 预定义的高亮样式
        self.highlight_styles = {
            'red': {
                'fillColor': '#f8cecc',
                'strokeColor': '#b85450',
                'strokeWidth': '2',
                'fontStyle': '1'  # 粗体
            },
            'yellow': {
                'fillColor': '#fff2cc',
                'strokeColor': '#d6b656',
                'strokeWidth': '2'
            },
            'green': {
                'fillColor': '#d5e8d4',
                'strokeColor': '#82b366',
                'strokeWidth': '2'
            },
            'blue': {
                'fillColor': '#dae8fc',
                'strokeColor': '#6c8ebf',
                'strokeWidth': '2'
            },
            'orange': {
                'fillColor': '#ffe6cc',
                'strokeColor': '#d79b00',
                'strokeWidth': '2'
            }
        }
    
    def load(self, skip_parse: bool = False) -> bool:
        """加载Draw.io文件"""
        try:
            # 如果reader还没有解析过，或者强制要求解析
            if not skip_parse and not hasattr(self.reader, 'modules'):
                if not self.reader.parse():
                    return False
            elif not skip_parse and len(self.reader.modules) == 0:
                if not self.reader.parse():
                    return False

            # 加载XML树
            self.tree = ET.parse(self.file_path)
            self.root = self.tree.getroot()

            if not skip_parse:
                print(f"✅ 高亮器加载文件: {self.file_path}")
            return True

        except Exception as e:
            print(f"❌ 高亮器加载失败: {e}")
            return False
    
    def highlight_by_path(self, path: str, style: str = 'red') -> bool:
        """根据路径高亮模块"""
        module = self.reader.get_module_by_path(path)
        if not module:
            print(f"❌ 未找到路径: {path}")
            return False
        
        return self.highlight_module(module.id, style)
    
    def highlight_by_paths(self, paths: List[str], style: str = 'red') -> Dict[str, bool]:
        """批量根据路径高亮模块"""
        results = {}
        for path in paths:
            results[path] = self.highlight_by_path(path, style)
        return results
    
    def highlight_module(self, module_id: str, style: str = 'red') -> bool:
        """高亮指定模块"""
        if style not in self.highlight_styles:
            print(f"❌ 不支持的样式: {style}")
            return False
        
        # 找到对应的mxCell元素
        cell = self._find_cell_by_id(module_id)
        if cell is None:
            print(f"❌ 未找到模块: {module_id}")
            return False
        
        # 更新样式
        current_style = cell.get('style', '')
        new_style = self._merge_style(current_style, self.highlight_styles[style])
        cell.set('style', new_style)
        
        module = self.reader.modules[module_id]
        print(f"✅ 已高亮模块: {module.name} ({module.path}) - {style}")
        return True
    
    def highlight_path_chain(self, path: str, styles: List[str] = None) -> bool:
        """高亮路径链上的所有模块"""
        if styles is None:
            styles = ['red', 'orange', 'yellow', 'green', 'blue']
        
        # 分解路径
        path_parts = path.split('/')
        current_path = ""
        
        success_count = 0
        for i, part in enumerate(path_parts):
            if current_path:
                current_path += "/" + part
            else:
                current_path = part
            
            style = styles[i % len(styles)]
            if self.highlight_by_path(current_path, style):
                success_count += 1
        
        print(f"✅ 路径链高亮完成: {success_count}/{len(path_parts)} 个模块")
        return success_count == len(path_parts)
    
    def highlight_children(self, parent_path: str, style: str = 'yellow') -> int:
        """高亮指定模块的所有子模块"""
        parent_module = self.reader.get_module_by_path(parent_path)
        if not parent_module:
            print(f"❌ 未找到父模块: {parent_path}")
            return 0
        
        children = self.reader.get_children(parent_module.id)
        success_count = 0
        
        for child in children:
            if self.highlight_module(child.id, style):
                success_count += 1
        
        print(f"✅ 子模块高亮完成: {success_count}/{len(children)} 个")
        return success_count
    
    def highlight_descendants(self, parent_path: str, style: str = 'yellow') -> int:
        """高亮指定模块的所有后代模块"""
        parent_module = self.reader.get_module_by_path(parent_path)
        if not parent_module:
            print(f"❌ 未找到父模块: {parent_path}")
            return 0
        
        descendants = self.reader.get_all_descendants(parent_module.id)
        success_count = 0
        
        for descendant in descendants:
            if self.highlight_module(descendant.id, style):
                success_count += 1
        
        print(f"✅ 后代模块高亮完成: {success_count}/{len(descendants)} 个")
        return success_count
    
    def clear_highlights(self) -> int:
        """清除所有高亮"""
        count = 0
        for cell in self.root.findall('.//mxCell'):
            if cell.get('vertex') == '1':
                style = cell.get('style', '')
                if any(color in style for color in ['#f8cecc', '#fff2cc', '#ffe6cc']):
                    # 移除高亮样式，恢复默认
                    new_style = self._remove_highlight_style(style)
                    cell.set('style', new_style)
                    count += 1
        
        print(f"✅ 已清除 {count} 个模块的高亮")
        return count
    
    def save(self, output_path: str = None) -> bool:
        """保存修改后的文件"""
        if output_path is None:
            output_path = self.file_path.replace('.drawio', '_highlighted.drawio')
        
        try:
            self.tree.write(output_path, encoding='utf-8', xml_declaration=True)
            print(f"✅ 文件已保存: {output_path}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def _find_cell_by_id(self, cell_id: str) -> Optional[ET.Element]:
        """根据ID查找mxCell元素"""
        for cell in self.root.findall('.//mxCell'):
            if cell.get('id') == cell_id:
                return cell
        return None
    
    def _merge_style(self, current_style: str, new_properties: Dict[str, str]) -> str:
        """合并样式属性"""
        # 解析当前样式
        style_dict = {}
        if current_style:
            for item in current_style.split(';'):
                if '=' in item:
                    key, value = item.split('=', 1)
                    style_dict[key] = value
        
        # 添加新属性
        style_dict.update(new_properties)
        
        # 重新组合样式字符串
        style_parts = [f"{key}={value}" for key, value in style_dict.items() if value]
        return ';'.join(style_parts) + ';'
    
    def _remove_highlight_style(self, style: str) -> str:
        """移除高亮样式"""
        # 移除高亮相关的属性
        highlight_keys = ['fillColor', 'strokeColor', 'strokeWidth']
        style_dict = {}
        
        if style:
            for item in style.split(';'):
                if '=' in item:
                    key, value = item.split('=', 1)
                    if key not in highlight_keys:
                        style_dict[key] = value
        
        # 添加默认样式
        style_dict.update({
            'fillColor': '#ffffff',
            'strokeColor': '#000000',
            'strokeWidth': '1'
        })
        
        style_parts = [f"{key}={value}" for key, value in style_dict.items() if value]
        return ';'.join(style_parts) + ';'
    
    def get_highlighted_modules(self) -> List[ModuleInfo]:
        """获取所有已高亮的模块"""
        highlighted = []
        
        for module_id, module in self.reader.modules.items():
            cell = self._find_cell_by_id(module_id)
            if cell is not None:
                style = cell.get('style', '')
                if any(color in style for color in ['#f8cecc', '#fff2cc', '#ffe6cc']):
                    highlighted.append(module)
        
        return highlighted
    
    def print_highlighted_summary(self):
        """打印高亮摘要"""
        highlighted = self.get_highlighted_modules()
        
        print(f"\n🎨 高亮摘要:")
        print(f"   已高亮模块数: {len(highlighted)}")
        
        if highlighted:
            print(f"   高亮模块列表:")
            for module in highlighted:
                print(f"     - {module.path}")



