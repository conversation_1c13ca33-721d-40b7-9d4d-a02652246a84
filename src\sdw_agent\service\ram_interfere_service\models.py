"""
RAM干涉工作流数据模型

定义工作流使用的数据模型和验证模式
"""

from typing import List, Dict, Any, Optional, Set
from enum import Enum
from pathlib import Path

from pydantic import BaseModel, Field, field_validator


class RAMInterfereConfigModel(BaseModel):
    """RAM干涉配置模型"""
    
    # 基本配置
    name: str = Field(default="RAM干涉检查")
    description: str = Field(default="分析代码中的全局变量变更和RAM干涉情况")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")
    
    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)
    
    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)
    
    # 日志配置
    logging: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        extra = "allow"


class GlobalVarChangeInfo(BaseModel):
    """全局变量变更信息"""
    name: str = Field(..., description="全局变量名称")
    file_path: str = Field(..., description="文件路径")
    change_func: Optional[str] = Field(None, description="变更函数名")
    called_func: Optional[List] = Field(None, description="变更变量的所有调用函数名")
    line: int = Field(..., ge=1, description="行号")
    content: str = Field(..., description="代码内容")
    
    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("文件路径不能为空")
        return v
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("代码内容不能为空")
        return v.strip()


class InterruptInfo(BaseModel):
    """中断信息"""
    interrupt_func: str = Field(..., description="中断函数名")
    interrupt_addr: int = Field(..., ge=0, description="中断地址")



class RepositoryInfo(BaseModel):
    """代码仓库信息"""
    repo_path: str = Field(..., description="仓库路径")
    commit_id: str = Field(..., description="提交ID")
    compared_commit_id: Optional[str] = Field(None, description="对比提交ID")
    
    @field_validator('repo_path')
    @classmethod
    def validate_repo_path(cls, v):
        path = Path(v)
        if not path.exists():
            raise ValueError(f"仓库路径不存在: {v}")
        return str(path.resolve())
    
    @field_validator('commit_id')
    @classmethod
    def validate_commit_id(cls, v):
        if not v or len(v) < 7:
            raise ValueError("提交ID格式无效")
        return v

class RAMInterfereRequest(BaseModel):
    repoInfo: RepositoryInfo

class RAMInterfereResult(BaseModel):
    """RAM干涉结果"""
    global_var_name: str = Field(..., description="全局变量名")
    interrupt: Optional[str] = Field(None, description="中断函数")
    interrupt_addr: Optional[int] = Field(None, ge=0, description="中断地址")
    file_path: str = Field(..., description="文件路径")
    change_func: Optional[str] = Field(None, description="变更函数")
    line: int = Field(..., ge=1, description="行号")
    content: str = Field(..., description="代码内容")
    remark: str = Field(default="", description="备注")
    result: str = Field(default="", description="判定结果")


class ProcessingResult(BaseModel):
    """处理结果"""
    ram_interfere_data: List[List[str]] = Field(..., description="RAM干涉数据")
    output_file: str = Field(..., description="输出文件路径")
    statistics: Dict[str, int] = Field(default_factory=dict, description="统计信息")
