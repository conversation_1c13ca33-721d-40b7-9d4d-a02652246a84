from PIL import Image
from base64 import b64decode, b64encode
from io import BytesIO
from docparser.models.picture import PictureObject
from docparser.models.graphic import GraphicObject
from docparser.models.table import CellObject


def merge_cell_pics(cell_obj: CellObject):
    """ 合并一个word表格单元格中的两个图片 或 图形(有相对位置) """
    pics = []
    pic_idx = None
    for idx, item in enumerate(cell_obj.content):
        if isinstance(item, (GraphicObject, PictureObject)):
            pics.append(item)
            if pic_idx is None:
                pic_idx = idx
    if not pics:
        return cell_obj

    for pic_obj in pics:
        cell_obj.content.remove(pic_obj)

    merged_pic = merge_pics(pics)
    if merged_pic is not None:
        cell_obj.content.insert(pic_idx, merged_pic)

    return cell_obj


def merge_pics(pics: list):
    """ 合并图片 """
    max_width = max([int(pic.px_width) for pic in pics])
    max_height = max([int(pic.px_height) for pic in pics])
    # 创建一个透明图片
    base_image = Image.new("RGBA", (max_width, max_height), (0, 0, 0, 0))

    # 获取字节数据
    both_bytes = [b64decode(pic.data.encode()) for pic in pics]
    # 创建图片对象
    both_io = [BytesIO(byte) for byte in both_bytes]
    images = [Image.open(io_).convert("RGBA") for io_ in both_io]

    # 按照宽度降序
    pics.sort(key=lambda i: i.width, reverse=True)
    images.sort(key=lambda i: i.width, reverse=True)
    first_pic = pics[0]
    # 全部为图形
    if all([True if isinstance(pic, GraphicObject) else False for pic in pics]):
        # 全部为图形的合并
        for idx, img in enumerate(images):
            if idx == 0:
                base_image.paste(img, (0, 0), mask=img)
            else:
                # 计算距离左上角的偏移量
                try:
                    cur_pic = pics[idx]
                    offset_x = abs(int(cur_pic.coordinate.left) - int(first_pic.coordinate.left)) / int(first_pic.width) * int(first_pic.px_width)
                    offset_y = abs(int(cur_pic.coordinate.top) - int(first_pic.coordinate.top)) / int(first_pic.height) * int(first_pic.px_height)
                    base_image.paste(img, (int(offset_x) * 15, int(offset_y) * 15), mask=img)
                except ZeroDivisionError:
                    return None
    # 当前合并 图形 + 图片
    elif len(pics) == 2:
        for img in images:
            base_image.paste(img, (0, 0), mask=img)
    else:
        base_image = None
        return base_image
    # 保存合并后的图片数据
    first_pic.px_width = base_image.width
    first_pic.px_height = base_image.height
    base_io = BytesIO()
    base_image.save(base_io, format="png")
    first_pic.data = b64encode(base_io.getvalue()).decode()
    return first_pic

