#!/usr/bin/env python3
"""
Draw.io 导出到 Excel 的使用示例
展示如何将 Draw.io 图表快速插入到 Excel 中
"""

import os
from pathlib import Path
from loguru import logger

from sdw_agent.util.drawio_util.drawio_to_excel_exporter import DrawioToExcelExporter
from sdw_agent.service.template_manager import template_manager


def example_single_drawio_to_excel():
    """示例：将单个 Draw.io 文件插入到 Excel"""
    print("📊 单个 Draw.io 文件导出示例")
    print("-" * 40)
    
    # 1. 获取 Draw.io 文件路径（使用模板管理器）
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    # 2. 创建导出器
    exporter = DrawioToExcelExporter()
    
    # 3. 设置输出文件路径
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    excel_file = output_dir / "architecture_diagram.xlsx"
    
    # 4. 导出到 Excel
    result = exporter.insert_drawio_to_excel(
        drawio_file=drawio_file,
        excel_file=str(excel_file),
        sheet_name="系统架构图",
        title="项目架构图",
        max_width=1000,
        max_height=700,
        scale=1.2  # 稍微放大一点
    )
    
    # 5. 显示结果
    print(f"✅ 导出结果: {result['success']}")
    print(f"📝 消息: {result['message']}")
    if result['success']:
        print(f"📁 Excel文件: {result['excel_file']}")
        print(f"📋 工作表: {result['sheet_name']}")
        print(f"📍 位置: {result['image_position']}")


def example_multiple_drawio_to_excel():
    """示例：将多个 Draw.io 文件批量插入到 Excel"""
    print("\n📊 多个 Draw.io 文件批量导出示例")
    print("-" * 40)
    
    # 1. 准备多个 Draw.io 文件（这里使用同一个文件作为示例）
    base_drawio = template_manager.get_template_path("block_diagram_file")
    if not base_drawio or not Path(base_drawio).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    # 模拟多个文件（实际使用时替换为真实的文件列表）
    drawio_files = [base_drawio] * 3  # 使用同一个文件3次作为示例
    
    # 2. 创建导出器
    exporter = DrawioToExcelExporter()
    
    # 3. 设置输出文件路径
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    excel_file = output_dir / "multiple_diagrams.xlsx"
    
    # 4. 批量导出到 Excel
    result = exporter.batch_insert_drawio_to_excel(
        drawio_files=drawio_files,
        excel_file=str(excel_file),
        sheet_name="所有架构图",
        images_per_row=2,  # 每行2个图
        max_width=500,
        max_height=400
    )
    
    # 5. 显示结果
    print(f"✅ 批量导出结果: {result['success']}")
    print(f"📝 消息: {result['message']}")
    if result['success']:
        print(f"📁 Excel文件: {result['excel_file']}")
        print(f"📋 工作表: {result['sheet_name']}")
        print("📋 详细结果:")
        for i, detail in enumerate(result['details'], 1):
            status = "✅" if detail['success'] else "❌"
            print(f"   {i}. {status} {Path(detail['file']).name}")
            if detail['success']:
                print(f"      位置: {detail['position']}")
            else:
                print(f"      错误: {detail.get('error', '未知错误')}")


def example_with_highlighted_drawio():
    """示例：结合高亮功能，将高亮后的 Draw.io 图插入 Excel"""
    print("\n📊 高亮后的 Draw.io 文件导出示例")
    print("-" * 40)
    
    from sdw_agent.util.drawio_util.highlight_leaf_modules import highlight_leaf_modules
    
    # 1. 先高亮一些模块
    target_paths = [
        "Disp/Dsp_app/Mainscrl/dsp_mainscrlcore/msg",
        "Disp/Dsp_app/Wrndtct/dsp_wrndtctcore/wrndtcfg",
        "Graphic (A53)/App/AppObj/tftwarning"
    ]
    
    # 2. 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    highlighted_drawio = output_dir / "highlighted_diagram.drawio"
    
    # 3. 执行高亮
    highlight_result = highlight_leaf_modules(
        target_paths=target_paths,
        output_file_path=str(highlighted_drawio),
        highlight_color="red",
        verbose=True,
        use_template_manager=True
    )
    
    if not highlight_result['success']:
        print(f"❌ 高亮失败: {highlight_result['message']}")
        return
    
    print(f"✅ 高亮完成: {highlight_result['highlighted_count']} 个模块")
    
    # 4. 将高亮后的图表插入 Excel
    exporter = DrawioToExcelExporter()
    excel_file = output_dir / "highlighted_architecture.xlsx"
    
    result = exporter.insert_drawio_to_excel(
        drawio_file=str(highlighted_drawio),
        excel_file=str(excel_file),
        sheet_name="高亮架构图",
        title=f"高亮架构图 (已标记 {highlight_result['highlighted_count']} 个模块)",
        max_width=1200,
        max_height=800
    )
    
    # 5. 显示结果
    print(f"✅ 导出结果: {result['success']}")
    print(f"📝 消息: {result['message']}")
    if result['success']:
        print(f"📁 Excel文件: {result['excel_file']}")


def check_drawio_executable():
    """检查 draw.io 可执行文件是否可用"""
    print("🔍 检查 draw.io 可执行文件")
    print("-" * 40)
    
    exporter = DrawioToExcelExporter()
    if exporter.drawio_executable:
        print(f"✅ 找到 draw.io 可执行文件: {exporter.drawio_executable}")
        return True
    else:
        print("❌ 未找到 draw.io 可执行文件")
        print("💡 请安装 draw.io desktop 应用程序:")
        print("   - Windows: https://github.com/jgraph/drawio-desktop/releases")
        print("   - macOS: brew install --cask drawio")
        print("   - Linux: 下载 AppImage 或使用包管理器安装")
        return False


def main():
    """主函数：运行所有示例"""
    print("🎯 Draw.io 导出到 Excel 完整示例")
    print("=" * 50)
    
    # 1. 检查环境
    if not check_drawio_executable():
        print("\n⚠️  由于缺少 draw.io 可执行文件，示例可能无法正常运行")
        print("   请先安装 draw.io desktop 应用程序")
        return
    
    try:
        # 2. 运行示例
        example_single_drawio_to_excel()
        example_multiple_drawio_to_excel()
        example_with_highlighted_drawio()
        
        print("\n🎉 所有示例运行完成！")
        print("📁 请查看 output 目录中生成的 Excel 文件")
        
    except Exception as e:
        logger.error(f"运行示例时发生错误: {str(e)}")
        print(f"❌ 运行示例时发生错误: {str(e)}")


if __name__ == "__main__":
    main()
