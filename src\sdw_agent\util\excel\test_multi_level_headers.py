"""
多级表头提取功能测试

这个文件测试ExcelUtil的多级表头提取功能，验证其处理合并单元格和生成层次结构的能力。
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange, CommonStyles


# 配置loguru日志
logger.remove()  # 移除默认处理器
logger.add(sys.stdout, level="DEBUG", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")


def create_test_file_with_multi_level_headers():
    """创建测试用的多级表头Excel文件"""
    logger.info("创建测试用的多级表头Excel文件")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        with ExcelUtil(temp_path, engine="openpyxl", auto_create=True) as excel:
            # 创建多级表头
            # 第一行
            excel.write_cell("Sheet1", 1, 1, "基本信息")
            excel.merge_cells("Sheet1", "A1:C1")
            excel.write_cell("Sheet1", 1, 4, "详细信息")
            excel.merge_cells("Sheet1", "D1:F1")
            excel.write_cell("Sheet1", 1, 7, "其他")
            excel.merge_cells("Sheet1", "G1:H1")
            
            # 第二行
            excel.write_cell("Sheet1", 2, 1, "姓名")
            excel.write_cell("Sheet1", 2, 2, "年龄")
            excel.write_cell("Sheet1", 2, 3, "性别")
            excel.write_cell("Sheet1", 2, 4, "学历")
            excel.merge_cells("Sheet1", "D2:E2")
            excel.write_cell("Sheet1", 2, 6, "工作经验")
            excel.write_cell("Sheet1", 2, 7, "备注")
            excel.merge_cells("Sheet1", "G2:H2")
            
            # 第三行
            excel.write_cell("Sheet1", 3, 4, "本科")
            excel.write_cell("Sheet1", 3, 5, "研究生")
            
            # 设置样式
            header_style = CellStyle(
                font_bold=True,
                bg_color="D9E1F2",
                alignment_horizontal="center"
            )
            
            # 应用样式
            for row in range(1, 4):
                for col in range(1, 9):
                    excel.set_cell_style("Sheet1", row, col, header_style)
            
            # 添加数据
            data = [
                ["张三", 25, "男", "计算机", "", 3, "优秀", ""],
                ["李四", 30, "女", "", "经济学", 5, "良好", ""],
                ["王五", 28, "男", "物理", "", 2, "", "需要培训"]
            ]
            
            for i, row_data in enumerate(data, start=4):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", i, j, value)
            
            # 自动调整列宽
            excel.auto_fit_columns("Sheet1")
            
            # 保存文件
            excel.save()
        
        logger.success(f"测试文件创建成功: {temp_path}")
        return temp_path
        
    except Exception as e:
        logger.error(f"创建测试文件失败: {e}")
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        return None


def test_extract_multi_level_headers():
    """测试多级表头提取功能"""
    logger.info("测试多级表头提取功能")
    
    # 创建测试文件
    test_file = create_test_file_with_multi_level_headers()
    if not test_file:
        logger.error("测试文件创建失败，无法继续测试")
        return
    
    try:
        # 使用ExcelUtil提取多级表头
        with ExcelUtil(test_file, engine="openpyxl") as excel:
            # 提取多级表头
            header_info = excel.extract_multi_level_headers(
                sheet_name="Sheet1",
                start_row=1,
                end_row=3,
                separator=" | "
            )
            
            # 打印分析结果
            header_info.print_analysis()
            
            # 打印所有列名
            logger.info("所有列名:")
            for i, col_name in enumerate(header_info.flat_columns):
                logger.info(f"列{i+1}: {col_name}")
            
            # 读取数据
            df = excel.read_excel_with_multi_level_headers(
                sheet_name="Sheet1",
                header_start_row=1,
                header_end_row=3,
                data_start_row=4,
                separator=" | "
            )
            
            logger.info(f"读取到的DataFrame，形状: {df.shape}")
            logger.info(f"列名: {df.columns.tolist()}")
            logger.info(f"数据预览:\n{df.head()}")
            
            # 测试静态方法
            logger.info("测试静态方法")
            df2 = ExcelUtil.read_excel_smart(
                file_path=test_file,
                sheet_name="Sheet1",
                header_start_row=1,
                header_end_row=3,
                data_start_row=4,
                separator=" | "
            )
            
            logger.info(f"静态方法读取的DataFrame，形状: {df2.shape}")
            
            # 测试简化函数
            from sdw_agent.util.excel.core import read_excel_with_multi_level_headers_simple
            
            df3 = read_excel_with_multi_level_headers_simple(
                file_path=test_file,
                sheet_name="Sheet1",
                header_start_row=1,
                header_end_row=3,
                data_start_row=4
            )
            
            logger.info(f"简化函数读取的DataFrame，形状: {df3.shape}")
            
            # 测试结构分析
            from sdw_agent.util.excel.core import analyze_excel_structure
            
            structure = analyze_excel_structure(test_file, "Sheet1")
            logger.info(f"结构分析结果: {structure['suggested_header_end']}")
            
            # 测试关键词查找
            header_range = excel.find_header_range_by_keyword("Sheet1", "基本信息")
            logger.info(f"通过关键词查找的表头范围: {header_range}")
            
        logger.success("多级表头提取测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)


def test_with_win32com_engine():
    """测试win32com引擎的多级表头提取功能"""
    logger.info("测试win32com引擎的多级表头提取功能")
    
    # 创建测试文件
    test_file = create_test_file_with_multi_level_headers()
    if not test_file:
        logger.error("测试文件创建失败，无法继续测试")
        return
    
    try:
        # 使用win32com引擎
        with ExcelUtil(test_file, engine="win32com") as excel:
            # 提取多级表头
            header_info = excel.extract_multi_level_headers(
                sheet_name="Sheet1",
                start_row=1,
                end_row=3,
                separator=" | "
            )
            
            # 打印分析结果
            header_info.print_analysis()
            
            # 读取数据
            df = excel.read_excel_with_multi_level_headers(
                sheet_name="Sheet1",
                header_start_row=1,
                header_end_row=3,
                data_start_row=4,
                separator=" | "
            )
            
            logger.info(f"win32com引擎读取到的DataFrame，形状: {df.shape}")
            logger.info(f"列名: {df.columns.tolist()}")
            
        logger.success("win32com引擎多级表头提取测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)


def test_real_world_example():
    """测试真实世界的例子（如果有提供测试文件）"""
    logger.info("测试真实世界的例子")
    
    # 检查是否有测试文件
    test_file = "test_data/multi_level_header_example.xlsx"
    if not os.path.exists(test_file):
        logger.warning(f"测试文件不存在: {test_file}，跳过真实世界测试")
        return
    
    try:
        # 使用简化函数读取
        df = read_excel_with_multi_level_headers_simple(
            file_path=test_file,
            header_start_row=1,
            header_end_row=3,
            data_start_row=4
        )
        
        logger.info(f"读取到的DataFrame，形状: {df.shape}")
        logger.info(f"列名: {df.columns.tolist()[:5]}...")  # 只显示前5个列名
        
        logger.success("真实世界测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    logger.info("开始多级表头提取功能测试")
    
    try:
        # 运行测试
        test_extract_multi_level_headers()
        
        # 测试win32com引擎
        try:
            test_with_win32com_engine()
        except Exception as e:
            logger.warning(f"win32com引擎测试失败: {e}")
        
        # 测试真实世界的例子
        test_real_world_example()
        
        logger.success("所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
