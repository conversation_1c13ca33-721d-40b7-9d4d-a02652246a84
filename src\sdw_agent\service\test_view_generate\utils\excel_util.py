import json
import math
import os
import pathlib
import sys
from typing import Dict, Union, List

import yaml
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter

from sdw_agent.service.test_view_generate.config import ROOT_DIR, SUB_OUTPUT
from sdw_agent.config.env import ENV

input_path = ENV.config.input_data_path


class ReadExcel:
    """用来解析test agent的变更一览表、checklist、rule、测试用例文件"""

    @staticmethod
    def import_excel(file_path, sheet_name, header_index, path_type):
        # 异步读取文件内容
        if not os.path.isfile(file_path):
            return None
            # 加载工作簿
        read_data = ReadExcel.read_sheet(file_path, sheet_name, header_index)
        res = []
        i = 1
        for data in read_data:
            var = {key: value for key, value in data.items() if value is not None}
            if path_type == "testcases":
                var["sheet_name"] = sheet_name
            if path_type == "testcases" and "示例" not in var:
                continue
            if path_type == "testcases" and var["示例"] in ["说明", "Please Insert above this line"]:
                continue
            res.append(var)
            i = i + 1
        return res

    @staticmethod
    def read_sheet(file_path, sheet_name, header_index):
        # 加载 Excel 文件
        workbook = load_workbook(file_path, data_only=True)

        # 获取指定的工作表
        # 判断有无指定sheet
        if sheet_name not in workbook.sheetnames:
            return []
        sheet = workbook[sheet_name]

        # 遍历合并单元格，填充合并区域的值
        merged_ranges = list(sheet.merged_cells.ranges)  # 将集合转换为静态列表
        for merged_cell in merged_ranges:
            # 获取合并单元格左上角单元格的值
            top_left_cell_value = sheet.cell(row=merged_cell.min_row, column=merged_cell.min_col).value

            # 取消合并单元格
            sheet.unmerge_cells(str(merged_cell))

            # 遍历合并区域的所有单元格并填充值
            for row in range(merged_cell.min_row, merged_cell.max_row + 1):
                for col in range(merged_cell.min_col, merged_cell.max_col + 1):
                    sheet.cell(row=row, column=col).value = top_left_cell_value

        # 读取表头
        headers = []
        for cell in sheet[header_index]:  # 假设第一行是表头
            headers.append(cell.value if cell.value is not None else f"未命名列{len(headers) + 1}")

        # 获取数据行
        data = []
        for row in sheet.iter_rows(min_row=header_index + 1, values_only=True):  # 从第二行开始读取数据
            if any(row):  # 检查行是否完全为空
                record = {}
                for i in range(len(headers)):
                    if row[i] is not None:
                        if headers[i] in record:
                            if not str(record[headers[i]]).endswith(str(row[i])):
                                record[headers[i]] = str(record[headers[i]]) + "-" + str(row[i])
                        else:
                            record[headers[i]] = str(row[i])
                data.append(record)

        ## 去除掉所有的未命名列
        # 过滤掉包含未命名列的记录,并确保每条记录都是json格式
        for record in data:
            keys_to_delete = [key for key in record.keys() if key.startswith("未命名")]
            for key in keys_to_delete:
                del record[key]

        return data

    @staticmethod
    def data_save(data, path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    @staticmethod
    def read_multiple_sheets(file_path, sheet_names, header_index, path_type):
        res = []
        for sheet_name in sheet_names:
            data = ReadExcel.import_excel(file_path, sheet_name, header_index, path_type)
            res.extend(data)
        return res

    @staticmethod
    def parse_excel(path_type, excel_path):
        data_dir = os.path.join(input_path, SUB_OUTPUT)
        data_dir = os.path.abspath(data_dir)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        config = yaml.load(open(os.path.join(ROOT_DIR, "config.yaml"), "r", encoding="utf-8"),
                           Loader=yaml.FullLoader)
        test_agent_input_table = config["test_agent_input_table"][path_type]
        sheet_names = test_agent_input_table["sheet_name"]
        header_index = test_agent_input_table["header_index"]
        file_path = test_agent_input_table["name"]
        res = ReadExcel.read_multiple_sheets(excel_path, sheet_names, header_index, path_type)
        save_path = os.path.join(data_dir, file_path)
        ReadExcel.data_save(res, save_path)
        return save_path, res


class ExcelTemplateWriter:
    """用来将生成的测试观点回写到模板中"""

    def __init__(self, template_path: str):
        """
        升级版Excel模板写入工具（支持合并单元格）

        :param template_path: 模板文件路径
        """
        template_path = get_template_path(template_path)
        self.wb = load_workbook(template_path)
        self.merged_ranges = {}  # 缓存合并区域 {sheet_name: List[合并范围元组]}

        # 初始化时缓存所有合并区域
        for sheet_name in self.wb.sheetnames:
            sheet = self.wb[sheet_name]
            self.merged_ranges[sheet_name] = [
                (r.min_row, r.min_col, r.max_row, r.max_col)
                for r in sheet.merged_cells.ranges
            ]

    def _get_column_letter(self, index: int) -> str:
        """将数字索引转换为Excel列字母"""
        return get_column_letter(index + 1)  # openpyxl从1开始计数

    def _copy_style(self, source_cell, target_cell):
        """复制单元格样式"""
        if source_cell.has_style:
            target_cell.font = source_cell.font.copy()
            target_cell.border = source_cell.border.copy()
            target_cell.fill = source_cell.fill.copy()
            target_cell.number_format = source_cell.number_format
            target_cell.protection = source_cell.protection.copy()
            target_cell.alignment = source_cell.alignment.copy()

    def write_data(
            self,
            sheet_name: str,
            data: List[Union[Dict, object]],
            start_row: int = 2,
            style_sample_row: int = 2
    ) -> None:
        """
        通用数据写入方法

        :param sheet_name: 工作表名称
        :param data: 数据列表，支持字典列表或数据类实例
        :param start_row: 数据起始行
        :param style_sample_row: 样式参考行
        """
        if not data:
            return

        ws = self.wb[sheet_name]
        fields = ["id", "category", "checkpoint", "mse_category", "mse_content", "procedure", "source"]
        # 缓存样式样本
        sample_row = []
        for col_idx, _ in enumerate(fields):
            col_letter = self._get_column_letter(col_idx)
            sample_cell = ws[f"{col_letter}{style_sample_row}"]
            sample_row.append(sample_cell)

        # 写入数据
        for row_idx, item in enumerate(data, start=start_row):
            for col_idx, field in enumerate(fields):
                col_letter = self._get_column_letter(col_idx)
                cell = ws[f"{col_letter}{row_idx}"]

                # 设置值
                if isinstance(item, dict):
                    cell.value = item.get(field)
                else:
                    cell.value = getattr(item, field, None)

                # 复制样式
                self._copy_style(sample_row[col_idx], cell)

    def save(self, output_path: str) -> None:
        """保存文件时保留原始合并结构"""
        # 恢复原始合并区域
        for sheet_name in self.wb.sheetnames:
            sheet = self.wb[sheet_name]
            sheet.merged_cells.ranges = []
            for bounds in self.merged_ranges[sheet_name]:
                sheet.merge_cells(
                    start_row=bounds[0],
                    start_column=bounds[1],
                    end_row=bounds[2],
                    end_column=bounds[3]
                )

        self.wb.save(output_path)
        self.wb.close()


def get_template_path(file_name: str) -> str:
    """主要用来将测试用例写入测试用例模板时需要，获取资源目录下的模板文件绝对路径"""
    # 获取当前脚本的绝对路径（假设此函数写在 src/utils 目录中的一个文件里）
    current_script_path = pathlib.Path(__file__).resolve()

    # 回退到项目根目录（假设脚本在 src 子目录中）
    project_root = current_script_path.parent.parent  # 根据实际层级调整

    # 拼接资源路径
    template_path = project_root / "resources" / "templates" / file_name

    # 路径存在性检查
    if not template_path.exists():
        raise FileNotFoundError(f"模板文件不存在: {template_path}")

    return str(template_path)


def replace_nan_with_empty_string(d):
    for key, value in d.items():
        if isinstance(value, dict):  # 如果值是嵌套字典，递归处理
            replace_nan_with_empty_string(value)
        elif isinstance(value, float) and math.isnan(value):  # 检查是否为 NaN
            d[key] = ""  # 替换为空字符串


if __name__ == '__main__':
    file_path = r"C:\Users\<USER>\Downloads\19PFv3_TestCase_Auto_MET-G_CSTMLST-CSTD-A0-06-A-C0 (全集).xlsx"
    ReadExcel.parse_excel("testcases", file_path)
