"""本地文件的存取"""
import pickle
import json
from loguru import logger


def save_pkl(data, file_path):
    """保存树结构为pkl"""
    # 保存树结构到文件
    with open(file_path, 'wb') as f:
        pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)


def read_pkl(file_path):
    """读取pkl树结构"""
    try:
        with open(file_path, 'rb') as f:
            try:
                loaded_root = pickle.load(f)  # 自动重建完整树结构
                return loaded_root
            except Exception as e:
                logger.error(f"PKL解析错误: {e}")
    except FileNotFoundError:
        logger.error(f"文件未找到: {file_path}")
    except PermissionError:
        logger.error(f"无文件访问权限: {file_path}")
    except Exception as e:
        logger.error(f"未知错误: {e}")
    return None


def read_json(file_path):
    """读取json文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            try:
                return json.load(file)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
    except FileNotFoundError:
        logger.error(f"文件未找到: {file_path}")
    except PermissionError:
        logger.error(f"无文件访问权限: {file_path}")
    except Exception as e:
        logger.error(f"未知错误: {e}")
    return {}


def save_json(save_path, save_data):
    with open(save_path, "w", encoding="utf-8") as f:
        json.dump(save_data, f, ensure_ascii=False, indent=2)
