#!/usr/bin/env python3
"""
最终版本：将 Draw.io 原始图像导出到 Excel
使用 Playwright 实现，无需桌面软件
"""

import os
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def export_drawio_to_excel_final(drawio_file: Optional[str] = None,
                                excel_file: str = "output/final_architecture_diagram.xlsx",
                                sheet_name: str = "架构图",
                                title: str = "系统架构图",
                                max_width: int = 1400,
                                max_height: int = 1000) -> Dict[str, Any]:
    """
    最终版本：将 Draw.io 文件导出为原始图像并插入 Excel
    
    Args:
        drawio_file: Draw.io 文件路径，如果为None则使用模板管理器
        excel_file: Excel 输出文件路径
        sheet_name: 工作表名称
        title: 图表标题
        max_width: 图像最大宽度
        max_height: 图像最大高度
        
    Returns:
        操作结果
    """
    
    # 1. 检查 Playwright
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        return {
            "success": False,
            "message": "Playwright 未安装，请运行: poetry add playwright && poetry run playwright install chromium",
            "method": "playwright"
        }
    
    # 2. 确定 Draw.io 文件路径
    if drawio_file is None:
        drawio_file = template_manager.get_template_path("block_diagram_file")
        if not drawio_file:
            return {
                "success": False,
                "message": "未找到 Draw.io 模板文件",
                "method": "playwright"
            }
    
    if not Path(drawio_file).exists():
        return {
            "success": False,
            "message": f"Draw.io 文件不存在: {drawio_file}",
            "method": "playwright"
        }
    
    logger.info(f"使用 Draw.io 文件: {drawio_file}")
    
    # 3. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        logger.info(f"文件大小: {len(xml_content)} 字符")
        
    except Exception as e:
        return {
            "success": False,
            "message": f"读取 Draw.io 文件失败: {e}",
            "method": "playwright"
        }
    
    # 4. 创建临时 PNG 文件
    output_dir = Path(excel_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    temp_png = output_dir / f"temp_drawio_export.png"
    
    # 5. 使用 Playwright 导出图像
    try:
        # URL 编码
        encoded_content = urllib.parse.quote(xml_content)
        drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=diagram#R{encoded_content}"
        
        logger.info("正在启动 Playwright 导出...")
        
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 设置视口大小
            page.set_viewport_size({"width": 1920, "height": 1080})
            
            # 访问页面
            page.goto(drawio_url, timeout=60000)
            
            # 等待页面加载
            page.wait_for_timeout(8000)
            
            # 尝试等待图表元素
            try:
                page.wait_for_selector(".geDiagramContainer", timeout=15000)
                logger.info("检测到图表容器")
            except:
                logger.warning("未检测到图表容器，继续截图")
            
            # 截图
            page.screenshot(path=str(temp_png), full_page=True, type="png")
            browser.close()
        
        if not temp_png.exists() or temp_png.stat().st_size == 0:
            return {
                "success": False,
                "message": "Playwright 截图失败",
                "method": "playwright"
            }
        
        logger.success(f"Playwright 导出成功: {temp_png}")
        
    except Exception as e:
        return {
            "success": False,
            "message": f"Playwright 导出失败: {e}",
            "method": "playwright"
        }
    
    # 6. 插入图像到 Excel
    try:
        with ExcelUtil(excel_file, auto_create=True) as excel:
            # 确保工作表存在
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            current_row = 1
            
            # 添加标题
            excel.write_cell(sheet_name, current_row, 1, title)
            title_style = CellStyle(
                font_size=16,
                font_bold=True,
                alignment_horizontal="center"
            )
            excel.set_cell_style(sheet_name, current_row, 1, title_style)
            current_row += 2
            
            # 插入图像
            success = insert_image_to_excel_sheet(
                excel=excel,
                sheet_name=sheet_name,
                image_path=str(temp_png),
                row=current_row,
                col=1,
                max_width=max_width,
                max_height=max_height
            )
            
            if success:
                excel.save()
                logger.success(f"图像已成功插入到 Excel: {excel_file}")
                
                # 清理临时文件
                try:
                    temp_png.unlink()
                except:
                    pass
                
                return {
                    "success": True,
                    "message": "Draw.io 原始图像导出成功",
                    "method": "playwright",
                    "excel_file": excel_file,
                    "sheet_name": sheet_name,
                    "image_position": f"行{current_row}, 列1"
                }
            else:
                return {
                    "success": False,
                    "message": "图像插入 Excel 失败",
                    "method": "playwright"
                }
                
    except Exception as e:
        return {
            "success": False,
            "message": f"Excel 操作失败: {e}",
            "method": "playwright"
        }


def insert_image_to_excel_sheet(excel: ExcelUtil,
                               sheet_name: str,
                               image_path: str,
                               row: int,
                               col: int,
                               max_width: Optional[int] = None,
                               max_height: Optional[int] = None) -> bool:
    """
    将图像插入到 Excel 工作表中
    """
    try:
        # 使用现有的图像插入功能
        from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import insert_image_to_excel

        # 尝试获取工作表对象
        if hasattr(excel, 'workbook') and excel.workbook:
            # 使用 openpyxl 方式
            try:
                from openpyxl.drawing.image import Image as OpenpyxlImage

                # 获取工作表
                if sheet_name in excel.workbook.sheetnames:
                    ws = excel.workbook[sheet_name]
                else:
                    ws = excel.workbook.create_sheet(sheet_name)

                img = OpenpyxlImage(image_path)

                # 调整图像大小
                if max_width or max_height:
                    original_width = img.width
                    original_height = img.height

                    if max_width and original_width > max_width:
                        scale_factor = max_width / original_width
                        img.width = max_width
                        img.height = int(original_height * scale_factor)

                    if max_height and img.height > max_height:
                        scale_factor = max_height / img.height
                        img.height = max_height
                        img.width = int(img.width * scale_factor)

                # 设置图像位置
                target_cell = ws.cell(row=row, column=col)
                img.anchor = target_cell.coordinate

                # 添加图像
                ws.add_image(img)

                # 调整行高以适应图像
                ws.row_dimensions[row].height = max(ws.row_dimensions[row].height or 15,
                                                   img.height * 0.75)  # 转换为点

                logger.info(f"图像已插入到 {sheet_name} 工作表，位置: {target_cell.coordinate}")
                logger.info(f"图像尺寸: {img.width} x {img.height}")
                return True

            except Exception as e:
                logger.warning(f"openpyxl 方式插入失败: {e}")

        # 回退到 win32com 方式
        if hasattr(excel, 'worksheet') and excel.worksheet:
            try:
                insert_image_to_excel(excel.worksheet, image_path, row, col)
                logger.info(f"使用 win32com 方式插入图像成功")
                return True
            except Exception as e:
                logger.warning(f"win32com 方式插入失败: {e}")

        logger.error("所有图像插入方式都失败了")
        return False

    except Exception as e:
        logger.error(f"插入图像失败: {str(e)}")
        return False


def demo_final_export():
    """演示最终版本的导出功能"""
    print("🎯 最终版本：Draw.io 原始图像导出到 Excel")
    print("=" * 60)
    
    # 测试导出
    result = export_drawio_to_excel_final(
        excel_file="output/final_architecture_diagram.xlsx",
        title="系统架构图（最终版本）",
        max_width=1400,
        max_height=1000
    )
    
    print(f"📊 导出结果:")
    print(f"   方法: {result.get('method', 'unknown')}")
    print(f"   成功: {'✅' if result['success'] else '❌'}")
    print(f"   消息: {result['message']}")
    
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   图像位置: {result['image_position']}")
        print(f"\n🎉 成功！请打开 Excel 文件查看原始 Draw.io 图表！")
        print(f"\n💡 特点:")
        print(f"   ✅ 保持原始图表的完整视觉效果")
        print(f"   ✅ 包含所有线条、颜色、布局")
        print(f"   ✅ 无需安装桌面软件")
        print(f"   ✅ 完全自动化")
    else:
        print(f"\n❌ 导出失败: {result['message']}")
        if result.get('method') == 'playwright':
            print(f"💡 请确保已安装 Playwright:")
            print(f"   poetry add playwright")
            print(f"   poetry run playwright install chromium")


if __name__ == "__main__":
    demo_final_export()
