#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : bug_commit.py
@Time    : 2025/7/15 14:28
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 选择测试结果文件, 导入并分析得到可用于提交redmine的结果以及相关信息回写到测试结果表
"""
from fastapi import APIRouter

from sdw_agent.service.bug_commit.workflow import bug_commit_service
from sdw_agent.model.request_model import ChangePointImportRequest
from sdw_agent.model.response_model import UploadBugCommitResponse

router = APIRouter(prefix="/api/sdw", tags=["65.Bug提交確認"])

@router.post("/bug_commit",
             summary="导入并分析测试结果文件",
             description="选择测试结果文件, 导入并分析得到可用于提交redmine的结果以及相关信息回写到测试结果表",
             response_description="",
             response_model=UploadBugCommitResponse)
async def import_changepoint(request: ChangePointImportRequest):
    file_path = request.file
    res, data = bug_commit_service(file_path)
    if res:
        return {
            'code': 0,
            'message': "导入并分析测试结果文件成功",
            'data': data
        }
    return {
            'code': 500,
            'message': "导入并分析测试结果文件失败",
            'data': {}
        }