"""
Logging utilities for document parsing.
"""

import logging
import os
import sys
from typing import Optional

def configure_logging(log_level: int = logging.INFO, log_file: Optional[str] = None) -> None:
    """
    Configure logging for the document parser.
    
    Args:
        log_level: Logging level (default: INFO)
        log_file: Path to log file (optional)
    """
    # Create logger
    logger = logging.getLogger('docparser')
    logger.setLevel(log_level)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log file specified
    if log_file:
        # Create directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    logger.info(f"Logging configured with level {logging.getLevelName(log_level)}")
    if log_file:
        logger.info(f"Logging to file: {log_file}")

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(f'docparser.{name}')
