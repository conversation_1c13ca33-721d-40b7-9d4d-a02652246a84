"""
@File    : base_analyzer.py
@Time    : 2025/7/29 11:21
<AUTHOR> qiliang.zhou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 base 车辆选定
@Desc    : $ 1. 从式样DB中提取指定车型的式样No
             2. 不同车型式样No对比
"""

from typing import List, Dict, Any

from sdw_agent.service.base_car_select.model import InputDataModel, CarDocInfoModel, SelectedCarInfo
from sdw_agent.service.base_car_select.utils import CustomizeExcelUtil
from sdw_agent.service.base_car_select.utils.car_info_resolver import CarInfoResolver


class BaseAnalyzer:

    def __init__(self, input_data: InputDataModel, config_manager, logger ):
        self.config_manager = config_manager
        self.logger = logger
        self.base_db_file = input_data.base_db
        self.new_db_file = input_data.new_db
        self.new_car_name = input_data.new_car_name.split('$')[1] if '$' in input_data.new_car_name else input_data.new_car_name

    def _get_target_col_idx_v3(self, flat_headers:List[str], car_name:str, version:str):
        col_idx_doc_name = 4
        col_idx_doc_no = 5
        col_idx_car = 49
        version_list = version.split(',')
        # 定位出待解析列的列索引
        for col_idx, header in enumerate(flat_headers):
            clean_header = header.replace('　', '').replace(' ', '')
            if self.config_manager.get('workflow_config.v3_db.doc_col_name', 'メータ仕様書名') in clean_header:
                col_idx_doc_name = col_idx + 1
                continue
            if self.config_manager.get('workflow_config.v3_db.doc_no_col_name', 'メータ仕様書No') in clean_header:
                col_idx_doc_no = col_idx + 1
                continue
            if (
                    clean_header.startswith(self.config_manager.get('workflow_config.v3_db.car_resolver_col_prefix',
                                                                    '19ePFv3ソフトリリース'))
                    and all(v in clean_header for v in version_list)
                    and car_name in clean_header
                ):
                col_idx_car = col_idx + 1
                continue

        return col_idx_doc_name, col_idx_doc_no, col_idx_car

    def _get_multi_headers_v3(self, excel:CustomizeExcelUtil):
        # 解析多级表头
        multi_header = excel.extract_multi_level_headers(
            sheet_name='制御仕様書目次',
            start_row=self.config_manager.get('workflow_config.v3_db.header_range.start_row', 2),
            end_row=self.config_manager.get('workflow_config.v3_db.header_range.end_row', 3),
            start_col=CarInfoResolver.trans_col_name(
                self.config_manager.get('workflow_config.v3_db.header_range.start_col', 'A')),
            end_col=CarInfoResolver.trans_col_name(
                self.config_manager.get('workflow_config.v3_db.header_range.end_col', 'BK'))
        )
        flat_header = multi_header.flat_columns
        return flat_header

    def get_base_new_docs(self, selected_car_info:SelectedCarInfo, new_db_file:str, base_db_file:str):

        # 获取new 车辆式样DB要件文件
        excel_new = CustomizeExcelUtil(
            new_db_file,
            start_row=self.config_manager.get('workflow_config.v3_db.header_range.start_row', 2),
            start_col=CarInfoResolver.trans_col_name(
                self.config_manager.get('workflow_config.v3_db.header_range.start_col', 'A')),
            auto_create=False
        )
        # 获取多级表头信息
        flat_headers = self._get_multi_headers_v3(excel_new)
        new_excel_max_row, new_excel_data_max_col = excel_new.get_sheet_data_dimensions('制御仕様書目次', with_end_flag=True)
        self.logger.info(f"最大行数: {new_excel_max_row}, 最大列数:{new_excel_data_max_col}")

        new_car_doc_info = self.get_specified_doc_no_v3(selected_car_info.new_car_name, selected_car_info.new_car_version, excel_new, '制御仕様書目次', flat_headers, new_excel_max_row)

        # 获取所有base 车辆的样式书信息
        # Todo 这里后续兼容面企画需要识别DB要件类型，动态解析不同项目的样式书
        excel_base = excel_new
        base_excel_max_row = new_excel_max_row
        # 解析所有base车辆信息
        base_car_doc_info_list = []
        if not hasattr(selected_car_info, "base_car_name"):
            # 用户没有选择base car信息，从所有base car candidate中选择一个
            car_info_resolver = CarInfoResolver(db_path=new_db_file)
            base_cars = car_info_resolver.resolve_car_info()
            for base_version, base_cars in base_cars.items():
                for base_car in base_cars:
                    if selected_car_info.new_car_name==base_car and selected_car_info.new_car_version==base_version:
                        continue
                    base_car_doc_info_list.append(self.get_specified_doc_no_v3(base_car, base_version, excel_base, '制御仕様書目次', flat_headers, base_excel_max_row))
        else:
            # 用户在前端选择了一个需要对比的base car, 只需对比分析new car和指定base car 的式样信息
            base_car_doc_info_list.append(
                self.get_specified_doc_no_v3(selected_car_info.base_car_name, selected_car_info.base_car_version, excel_base, '制御仕様書目次', flat_headers,
                                             base_excel_max_row))

        return new_car_doc_info, base_car_doc_info_list

    def analyze_base_new_doc(self, input_data:InputDataModel):
        """
        对比分析式样差异，推荐base 车辆
        Args:
            input_data:
        """
        selected_car_info = SelectedCarInfo(input_data)
        new_car_doc_info, base_car_doc_info_list = self.get_base_new_docs(selected_car_info, input_data.new_db, input_data.base_db)

        res = []
        for base_car_doc_info in base_car_doc_info_list:
            # 获取完全相同的式样书编号
            same_doc_pairs, remaining_new_docs, remaining_base_docs = self._get_same_doc(new_car_doc_info.doc_no, base_car_doc_info.doc_no)
            # 获取名称相同且版本信息最相近的式样书编号
            similar_doc_pairs, remaining_new_docs, remaining_base_docs = self._get_most_similar_docs(remaining_new_docs, remaining_base_docs)
            # 新车中包含但是base中没有的式样书编号
            only_new_docs = []
            if remaining_new_docs:
                only_new_docs = [[doc, None] for doc in remaining_new_docs]
            # base车中包含但是new car中没有的式样书编号
            only_base_docs = []
            if remaining_base_docs:
                only_base_docs = [[None, doc] for doc in remaining_base_docs]

            res.append({
                "car_name": base_car_doc_info.car_name,
                "version": base_car_doc_info.version,
                "same_doc_pairs": same_doc_pairs,
                "similar_doc_pairs": similar_doc_pairs,
                "only_new_docs": only_new_docs,
                "only_base_docs": only_base_docs
            })

        # 从式样DB 中的所有base 车辆中选择出差异最小的前两个base 车辆信息
        recommend_base_info = self.recommend_base_car(new_car_doc_info, res)

        return new_car_doc_info, recommend_base_info

    def recommend_base_car(self, new_car_doc_info: CarDocInfoModel, candidate_base_cars: List[Dict[str, Any]]):
        """
        从式样DB 中的所有base 车辆中选择出差异最小的前两个base 车辆信息

        Args:
            new_car_doc_info: 新车型的文档信息模型
            candidate_base_cars: 候选base车辆信息列表，每个元素包含same_doc_pairs等信息

        Returns:
            差异最小的前两个base车辆信息，按相似度降序排列
        """
        if not candidate_base_cars:
            self.logger.warning("候选base车辆列表为空")
            return []
        
        if len(candidate_base_cars)==1:
            return candidate_base_cars

        # 1. 获取new_car_doc_info中式样数的数量
        new_doc_len = len(new_car_doc_info.doc_no) if new_car_doc_info.doc_no else 0

        if new_doc_len == 0:
            self.logger.warning("新车型文档信息中没有式样编号")
            return []

        # 2. 计算每个候选base车辆的相似度
        for base_car in candidate_base_cars:
            try:
                # 获取same_doc_pairs的长度
                same_doc_pairs = base_car.get('same_doc_pairs', [])
                similar_doc_pairs = base_car.get('similar_doc_pairs', [])

                same_doc_count = len(same_doc_pairs) if same_doc_pairs else 0
                similar_doc_count = len(similar_doc_pairs) if similar_doc_pairs else 0

                # 计算相似度比例
                # similarity_ratio = same_doc_count / new_doc_len if new_doc_len > 0 else 0
                similarity_ratio = same_doc_count + similar_doc_count*0.5

                # 存储相似度信息
                base_car['similarity_ratio'] = similarity_ratio

            except Exception as e:
                self.logger.warning(f"处理候选base车辆时出错: {str(e)}, 跳过该车辆")
                continue

        # 3. 按相似度降序排序，选择前两个
        candidate_base_cars.sort(key=lambda x: x['similarity_ratio'], reverse=True)

        # 获取前两个最相似的base车辆
        top_two = candidate_base_cars[:2]

        return top_two

    def _get_same_doc(self, new_docs: List[str], base_docs: List[str]):
        """
        从new_docs和base_docs中找到字符串相同的元素

        Args:
            new_docs: 新文档列表
            base_docs: 基础文档列表

        Returns:
            tuple: (相同元素对列表, 更新后的new_docs, 更新后的base_docs)
                  相同元素对列表格式: [(same_doc, same_doc), ...]
        """
        if not new_docs or not base_docs:
            return [], new_docs.copy(), base_docs.copy()

        # 创建列表副本，避免修改原列表
        remaining_new_docs = new_docs.copy()
        remaining_base_docs = base_docs.copy()
        same_doc_pairs = []

        # 找到相同的元素
        for new_doc in new_docs:
            if new_doc in remaining_base_docs:
                # 找到相同元素，添加到结果列表
                same_doc_pairs.append([new_doc, new_doc])

                # 从两个列表中移除该元素
                if new_doc in remaining_new_docs:
                    remaining_new_docs.remove(new_doc)
                if new_doc in remaining_base_docs:
                    remaining_base_docs.remove(new_doc)

        return same_doc_pairs, remaining_new_docs, remaining_base_docs

    def _get_most_similar_docs(self, new_docs: List[str], base_docs: List[str]):
        """
        从base_docs中找到与new_docs中每个元素最相近的元素

        Args:
            new_docs: 新文档列表
            base_docs: 基础文档列表

        Returns:
            tuple: (匹配结果字典, 更新后的base_docs列表)
                  匹配结果字典格式: {new_doc: matched_base_doc or None}
        """
        if not new_docs or not base_docs:
            return {doc: None for doc in new_docs}, base_docs.copy()

        # 创建base_docs的副本，避免修改原列表
        remaining_base_docs = base_docs.copy()
        remaining_new_docs = new_docs.copy()
        match_results = []

        for new_doc in new_docs:
            # 为当前new_doc找到最相似的base_doc
            best_match = self._find_most_similar_doc(new_doc, remaining_base_docs)

            if best_match is not None:
                # 找到匹配，从剩余列表中移除
                remaining_base_docs.remove(best_match)
                remaining_new_docs.remove(new_doc)
                match_results.append([new_doc, best_match])
            
        return match_results, remaining_new_docs, remaining_base_docs

    def _find_most_similar_doc(self, new_doc: str, base_docs: List[str]) -> str:
        """
        从base_docs中找到与new_doc最相似的文档

        Args:
            new_doc: 新文档字符串
            base_docs: 基础文档列表

        Returns:
            str: 最相似的文档，如果没有满足条件的则返回None
        """
        if not base_docs:
            return None

        # 获取new_doc的doc_name
        try:
            new_doc_name = self._get_doc_name(new_doc)
        except (IndexError, TypeError):
            # 如果new_doc长度不足或类型错误，返回None
            return None

        # 候选匹配列表：存储满足必要条件的文档及其前缀长度
        candidates = []

        for base_doc in base_docs:
            try:
                # 检查必要条件：doc_name必须相同
                base_doc_name = self._get_doc_name(base_doc)
                if base_doc_name != new_doc_name:
                    continue

                # 计算相同前缀的长度
                common_prefix_length = self._get_common_prefix_length(new_doc, base_doc)
                candidates.append((base_doc, common_prefix_length))

            except (IndexError, TypeError):
                # 如果base_doc长度不足或类型错误，跳过
                continue

        if not candidates:
            return None

        # 找到前缀最长的文档
        # 如果有多个文档前缀长度相同，返回第一个
        best_match = max(candidates, key=lambda x: x[1])
        return best_match[0]

    def _get_common_prefix_length(self, str1: str, str2: str) -> int:
        """
        计算两个字符串相同前缀的长度

        Args:
            str1: 第一个字符串
            str2: 第二个字符串

        Returns:
            int: 相同前缀的长度
        """
        if not str1 or not str2:
            return 0

        min_length = min(len(str1), len(str2))
        common_length = 0

        for i in range(min_length):
            if str1[i] == str2[i]:
                common_length += 1
            else:
                break

        return common_length


    @staticmethod
    def _get_doc_name(doc_no:str):
        return doc_no[:-10]

    def get_specified_doc_no_v3(self, car_name:str, version:str, excel:CustomizeExcelUtil, sheet_name:str, flat_headers:List[str], max_row:int=None):
        """
        获取指定车型，指定版本下的式样书信息
        Args:
            car_name: 车型名称
            version: 版本信息
            excel: 文件对象
            sheet_name: sheet 名称
            flat_headers: excel 多级表头信息
            max_row: excel 文件最大行索引

        Returns: CarDocInfoModel
        """
        # 寻找到目标列索引
        col_idx_doc_name, col_idx_doc_no, col_idx_car = self._get_target_col_idx_v3(flat_headers, car_name, version)
        car_doc_info = CarDocInfoModel()
        car_doc_info.car_name = car_name
        car_doc_info.version = version
        for row in range(self.config_manager.get('workflow_config.v3_db.header_range.end_row', 3)+1, max_row):
            flag = excel.read_cell(sheet_name, row, col_idx_car)
            if flag is not None and flag == '○':
                car_doc_info.doc_name.append(excel.read_cell(sheet_name, row, col_idx_doc_name))
                car_doc_info.doc_no.append(excel.read_cell(sheet_name, row, col_idx_doc_no))

        self.logger.info(f"解析出{version} {car_name} 式样书列表，长度{len(car_doc_info.doc_no)}")
        return car_doc_info
