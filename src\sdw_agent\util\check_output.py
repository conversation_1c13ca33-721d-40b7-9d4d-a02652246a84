import os
import re

import pandas as pd
import numpy as np
import xlwings as xw
from openpyxl.utils import range_boundaries
from openpyxl import load_workbook

from loguru import logger




def CheckIFCS():
    path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计/IF整合性確認/IF整合性CS兼結果報告書_LVDS_cn.xlsx"
    file = pd.ExcelFile(path)
    sheet_names = file.sheet_names
    LVDS_sheet = "LVDS_IF"
    sheet_names_exclude = [x for x in sheet_names if x not in LVDS_sheet]
    empty_sheets = check_empty_sheet(file, sheet_names_exclude)

    # 检查空表
    if len(empty_sheets) > 0:
        logger.error(f"empty sheets: {empty_sheets}")
        return False, f"发现空表: {empty_sheets}"
    else:
        logger.success("no empty sheets")
    
    # 检查LVDS_IF表
    sheet_LVDS_IF = pd.read_excel(file, sheet_name=LVDS_sheet)
    # C列第2行（pandas索引第0行第2列）
    c2_value = sheet_LVDS_IF.iloc[0, 2] if sheet_LVDS_IF.shape[0] > 0 and sheet_LVDS_IF.shape[1] > 2 else None
    if c2_value == "OK":
        logger.success("LVDS_IF中Judge结果为'OK'")
        return True, "LVDS_IF中Judge结果为'OK'"
    else:
        logger.error(f"LVDS_IF中Judge结果不是'OK'，实际值为: {c2_value}")
        return False, f"LVDS_IF中Judge结果不是'OK'，实际值为: {c2_value}"

def CheckDesignCS():
    path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计/ソフトウェア 設計基準CS（詳細設計）/ソフトウェア 設計基準CS.xlsm"
    file = pd.ExcelFile(path)
    sheet_names = file.sheet_names
    degisn_sheets = [
        "設計基準CS-要件分析",
        "設計基準CS-基本設計",
        "設計基準CS-詳細設計",
    ]
    # sheet_names_exclude = [x for x in sheet_names if x not in degisn_sheets]
    empty_sheets = check_empty_sheet(file, sheet_names)
    
    # 检查空表
    if len(empty_sheets) > 0:
        logger.error(f"empty sheets: {empty_sheets}")
        return False, f"发现空表: {empty_sheets}"
    else:
        logger.success("no empty sheets")
    
    # 检查各个设计表
    all_issues = []
    
    #設計基準CS-要件分析
    sheet_yjfx = pd.read_excel(file, sheet_name=degisn_sheets[0])
    issues_yjfx = check_cs_design_conditions(sheet_yjfx, degisn_sheets[0])
    if issues_yjfx:
        all_issues.extend(issues_yjfx)
    
    #設計基準CS-基本設計
    sheet_jbsj = pd.read_excel(file, sheet_name=degisn_sheets[1])
    issues_jbsj = check_cs_design_conditions(sheet_jbsj, degisn_sheets[1])
    if issues_jbsj:
        all_issues.extend(issues_jbsj)
    
    #設計基準CS-詳細設計
    sheet_xxsj = pd.read_excel(file, sheet_name=degisn_sheets[2])
    issues_xxsj = check_cs_design_conditions(sheet_xxsj, degisn_sheets[2])
    if issues_xxsj:
        all_issues.extend(issues_xxsj)
    
    # 返回结果
    if not all_issues:
        logger.success("所有检查通过，没有发现问题")
        return True, "所有检查通过"
    else:
        logger.error(f"发现问题: {len(all_issues)} 个")
        return False, all_issues

def CheckCANOutput():
    path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计/CAN入出力一覧確認/【MM連携】検査_(CAN入出力消込)178D_MM-SUB-BUS_MM連携シグナル確認_7inch_178D840B893B_量確.xlsm"
    file = pd.ExcelFile(path)
    
    empty_sheets = check_empty_sheet(file, file.sheet_names)
    # 检查空表
    if len(empty_sheets) > 0:
        logger.error(f"empty sheets: {empty_sheets}")
        return False, f"发现空表: {empty_sheets}"
    else:
        logger.success("no empty sheets")
    
    check_empty_sheet2 = check_empty_sheet(file, check_sheet="更新履歴", check_range="B5:E10", row_full_not_empty=True)
    if not check_empty_sheet2:
        logger.error(f"sheet 更新履歴 is empty")
        return False

    sheet3 = pd.read_excel(file, sheet_name="Transmit")
    check_sheet3 = check_bf_bh_ok(sheet3, "Transmit")
    if len(check_sheet3) > 0:
        logger.error(f"sheet Transmit is empty")
        return False

def CheckRAMBook():
    path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计/RAM設計書作成/RAM設計書_Warning(MET19PFV3-34734) .xls"
    file = pd.ExcelFile(path)
    sheet_names = file.sheet_names
    sheet5_name = "データ構造"
    sheet_names_exclude = [x for x in sheet_names if x not in sheet5_name]
    empty_sheets = check_empty_sheet(file, sheet_names_exclude)
    if len(empty_sheets) > 0:
        logger.error(f"empty sheets: {empty_sheets}")
        return False, f"发现空表: {empty_sheets}"
    else:
        logger.success("no empty sheets")
    
    sheet5 = pd.read_excel(file, sheet_name=sheet5_name)
    check_empty_from_row(sheet5, sheet5_name, start_row=5, except_cols=[1, 2])
    # print(sheet5)

def CheckFuncBook():
    path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计/関数仕様書作成/面企画_1A_DrvInd_関数仕様書_v1.0.0.xlsm"
    file = pd.ExcelFile(path)
    sheet_names = file.sheet_names  
    empty_sheets = check_empty_sheet(file, sheet_names)
    # 检查空表
    if len(empty_sheets) > 0:
        logger.error(f"empty sheets: {empty_sheets}")
        return False, f"发现空表: {empty_sheets}"  
    else:
        logger.info(f"没有发现空表")
    # sheet2 = pd.read_excel(file, sheet_name="変更符号")
    # res = check_empty_sheet(file, check_sheet = "変更符号", check_range = "B32:AI45", row_full_not_empty=True)
    # if not res:
    #     logger.error(f"sheet 变更符号版本列表存在存在非完整行")
    # res = check_empty_sheet(file, check_sheet = "変更符号", check_range = "Q18:Y20", row_full_not_empty=True)
    # if not res:
    #     logger.error(f"sheet 变更符号中部品名，部品バージョン，製品コード存在空行")
    df = pd.read_excel(file, sheet_name="関数一覧")
    print(df)
    res = check_empty_from_row(df, sheet_name = "関数一覧", start_row_index=4, start_col_index=1, end_col_index=6)
    if not res:
        logger.error(f"sheet 関数一覧存在存在非完整行")




def check_cs_design_conditions(df, sheet_name=""):
    """
    检查当J列为"該当"时的条件：
    1. N, O, P, Q列其中一列有值
    2. S, T列都有值
    从第7行开始分析
    返回问题列表，如果没有问题则返回空列表
    """
    issues = []
    
    # 获取列名（假设列名为N, O, P, Q, S, T）
    # 如果列名是数字索引，需要转换为实际的列名
    try:
        # 获取列名
        j_col = df.columns[9]  # 第10列
        n_col, o_col, p_col, q_col = df.columns[13], df.columns[14], df.columns[15], df.columns[16]  # 第14,15,16,17列
        s_col, t_col = df.columns[18], df.columns[19]  # 第19,20列
        
        # 从第7行开始分析
        start_row = 5  # 对应Excel的第7行(第一行为空，pandas不解析)
        df_from_row7 = df.iloc[start_row:]

        # 找到J列为"該当"的行
        applicable_rows = df_from_row7[df_from_row7[j_col] == "該当"]
        
        if applicable_rows.empty:
            logger.warning(f"{sheet_name}: 从第7行开始，没有找到J列为'該当'的行")
            return issues
        
        logger.info(f"{sheet_name}: 从第7行开始，找到 {len(applicable_rows)} 行J列为'該当'")
        
        # 检查每一行
        for index, row in applicable_rows.iterrows():
            row_num = index + 2  # Excel行号（从第7行开始，所以是index + 7）
            
            # 检查N, O, P, Q列是否至少有一列有值
            nopq_values = [row[n_col], row[o_col], row[p_col], row[q_col]]
            has_nopq_value = any(pd.notna(val) and str(val).strip() != '' for val in nopq_values)
            
            # 检查S, T列是否都有值
            s_value = row[s_col]
            t_value = row[t_col]
            has_s_value = pd.notna(s_value) and str(s_value).strip() != ''
            has_t_value = pd.notna(t_value) and str(t_value).strip() != ''
            both_st_have_values = has_s_value and has_t_value
            
            # 输出检查结果
            logger.info(f"{sheet_name} 第{row_num}行:")
            logger.info(f"  - N,O,P,Q列至少有一列有值: {'是' if has_nopq_value else '否'}")
            logger.info(f"  - S列有值: {'是' if has_s_value else '否'}")
            logger.info(f"  - T列有值: {'是' if has_t_value else '否'}")
            logger.info(f"  - S,T列都有值: {'是' if both_st_have_values else '否'}")
            
            # 检查是否符合条件
            if has_nopq_value and both_st_have_values:
                logger.success(f"  ✓ 第{row_num}行符合条件")
            else:
                logger.error(f"  ✗ 第{row_num}行不符合条件")
                issue_msg = f"{sheet_name} 第{row_num}行: "
                if not has_nopq_value:
                    logger.error(f"    原因: N,O,P,Q列都没有值")
                    issue_msg += "N,O,P,Q列都没有值; "
                if not both_st_have_values:
                    logger.error(f"    原因: S,T列不是都有值")
                    issue_msg += "S,T列不是都有值"
                issues.append(issue_msg)
            logger.info("")
        
        return issues
            
    except KeyError as e:
        error_msg = f"{sheet_name}: 列名错误: {e}"
        logger.error(error_msg)
        logger.error("请检查实际的列名，可能需要调整列名映射")
        return [error_msg]
    except Exception as e:
        error_msg = f"{sheet_name}: 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return [error_msg]

def check_empty_sheet(file, sheet_names=None, check_sheet=None, check_range=None, row_full_not_empty=False, col_full_not_empty=False):
    """
    检查指定sheet_names列表中的sheet是否为空。
    如果指定了check_sheet和check_range，则只检查该sheet的指定区域是否为空。
    check_range格式如"A1:C10"。
    row_full_not_empty: 只要区域内有一行全不为空就返回True。
    col_full_not_empty: 只要区域内有一列全不为空就返回True。
    返回：
      - 如果是批量检查，返回空sheet列表
      - 如果是区域检查，返回True/False及空单元格坐标列表
    """
    
    # 区域检查模式
    if check_sheet and check_range:
        # 用openpyxl读取区域
        wb = load_workbook(file._io)
        ws = wb[check_sheet]
        min_col, min_row, max_col, max_row = range_boundaries(check_range)
        region = list(ws.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col, values_only=True))
        empty_cells = []
        for r_idx, row in enumerate(region, start=min_row):
            for c_idx, cell in enumerate(row, start=min_col):
                if cell is None or str(cell).strip() == "":
                    empty_cells.append(ws.cell(row=r_idx, column=c_idx).coordinate)
        # 行全不为空判断
        if row_full_not_empty:
            for row in region:
                if all(cell is not None and str(cell).strip() != "" for cell in row):
                    logger.success(f"{check_sheet}的区域{check_range}存在一行全不为空")
                    return True, None
        # 列全不为空判断
        if col_full_not_empty:
            for col_idx in range(len(region[0])):
                if all(row[col_idx] is not None and str(row[col_idx]).strip() != "" for row in region):
                    logger.success(f"{check_sheet}的区域{check_range}存在一列全不为空")
                    return True, None
        # 全不为空判断
        if not empty_cells:
            logger.success(f"{check_sheet}的区域{check_range}全部不为空")
            return True, None
        else:
            logger.error(f"{check_sheet}的区域{check_range}存在空单元格: {empty_cells}")
            return False, empty_cells
    
    # 批量检查模式
    dfs = {sheet: file.parse(sheet) for sheet in sheet_names}
    empty_sheets = []
    for sheet_name, df in dfs.items():
        if df.empty:
            # 如果文字为空，则检查是否有图片
            has_images = check_images(file, sheet_name)
            if has_images:
                logger.info(f"have images: {sheet_name}")
                # 有图片不算空，不添加到空sheet列表
            else:
                logger.warning(f"empty sheet: {sheet_name}")
                empty_sheets.append(sheet_name)
        else:
            logger.info(f"not empty: {sheet_name}")
            # 有内容不算空，不添加到空sheet列表
    return empty_sheets

def check_images(file, sheet_name=None):
    try:
        # 启动Excel应用（后台运行）
        app = xw.App(visible=False, add_book=False)
        wb = app.books.open(file)
        
        # # 指定工作表
        # sheets = [wb.sheets[sheet_name]] if sheet_name else wb.sheets
        
        # result = {}
        shapes = wb.sheets[sheet_name].api.Shapes
        has_images = shapes.Count > 0
        # result[sheet_name] = has_images
        if has_images:
            wb.close()
            app.quit()
            return True
        else:
            wb.close()
            app.quit()
            return False
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return {}

def check_bf_bh_ok(df, sheet_name=""):
    """
    检查从第14行开始，BF列为"対象"时，BH列是否为"OK"。
    返回不满足条件的行号和内容列表。
    """
    issues = []
    try:
        # 获取BF和BH列的索引
        bf_col = 58
        bh_col = 60

        # 从第14行开始分析
        df_from_row14 = df.iloc[13:]
        for idx, row in df_from_row14.iterrows():
            excel_row_num = idx + 1  # Excel实际行号
            if str(row[bf_col]).strip() == "対象":
                if str(row[bh_col]).strip() != "OK":
                    msg = f"{sheet_name} 第{excel_row_num}行: BF列为'対象'但BH列不是'OK'，实际为: {row[bh_col]}"
                    logger.error(msg)
                    issues.append(msg)
        if not issues:
            logger.success(f"{sheet_name}: BF列为'対象'的行，BH列均为'OK'")
        return issues
    except Exception as e:
        error_msg = f"{sheet_name}: 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return [error_msg]

def check_empty_from_row(df, sheet_name="", start_row_index=None, start_col_index=None, end_col_index=None, except_cols_index=None):
    """
    从第start_row_index行开始检查，除except_cols列外其它列有NaN就报错，except_cols列只要有值即可。
    返回错误信息列表，全部非空返回空列表。
    """
    issues = []
    try:
        # 获取except_cols列的列名
        if except_cols_index is not None:
            except_cols_names = [df.columns[i] for i in except_cols_index]
        # 从第start_row_index行开始，从start_col_index列开始，到end_col_index列结束
        df_from_row = df.iloc[start_row_index - 1:, start_col_index:end_col_index]
        print(df_from_row)
        for idx, row in df_from_row.iterrows():
            excel_row_num = idx + 1  # Excel实际行号
            # 检查except_cols列有值（不是NaN）
            if except_cols_index is not None:
                if all(pd.isna(row[col]) for col in except_cols_names):
                    issues.append(f"{sheet_name} 第{excel_row_num}行: except_cols列至少有一列要有值")
            # 检查其它列
            for col in df.columns:
                if except_cols_index is not None:
                    if col in except_cols_names:
                        continue
                if pd.isna(row[col]):
                    issues.append(f"{sheet_name} 第{excel_row_num}行: {col}列为NaN")
        if not issues:
            logger.success(f"{sheet_name}: 从第{start_row_index + 1}行开始所有行（除except_cols列）均无NaN")
        else:
            for msg in issues:
                logger.error(msg)
        return issues
    except Exception as e:
        error_msg = f"{sheet_name}: 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return [error_msg]

if __name__ == "__main__":
    CheckFuncBook()
    pass