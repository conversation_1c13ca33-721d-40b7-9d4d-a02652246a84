# CSTM 配置工具工作流配置文件

# 基本配置
name: "CSTM Tool 工作流"
description: "分析变更点变更前后原始式样书中的内容，自动化更新CSTM Tool要件并生成变更后的代码"
version: "1.0.0"
author: "qiliang_zhou"

# 工作流特定配置
workflow_config:
  # 输入配置
  input:
    # 关联式样书存储路径
    linked_spec_path: "D:/SVN/Roc_Doc/01REQ/0102Report/REQ Robot/19PFV3需求整理/【19PFV3】CSTM需求文档"
  # 输出配置
  output:
    path: "C:/tdd_output"
    # 解析原始式样书结果
    req_resolve_dir: "req_jsons"
    # 对比分析变更前后原始式样书结果
    req_analyse: "req_analyse"

  scl_fields:
    fields:
      - "変更前\nタグ"
      - "変更後\nタグ"
      - "変更前\n仕様書No."
      - "変更後\n仕様書No."
      - "変更前\nセル"
      - "変更後\nセル"
      - "縦軸メインラベル"
      - "縦軸サブラベル"
      - "横軸メインラベル"
      - "横軸サブラベル"
      - "変更前"
      - "変更後"
      - "変更内容"
      - "差分種別"
    field_keys:
      - "change_before"
      - "change_after"
      - "change_before_no"
      - "change_after_no"
      - "change_before_cell"
      - "change_after_cell"
      - "y_label"
      - "y_secondary_label"
      - "x_label"
      - "x_secondary_label"
      - "change_content_before"
      - "change_content_after"
      - "change_content"
      - "change_type"

  # 要件配置
  req_config:
    start_row: 8
    start_col: 2
    max_level: 7
    target_sheet: "JP"

  # 屏幕层级映射配置
  screen_hierarchy:
    # 项目名称映射
    item_name_map:
      "表示設定": "Meter Settings"

    # 标题映射
    title_map:
      "メータータイプ設定": "メータタイプ"

  # 特殊处理关键字配置 用来判断是否需要特殊处理
  special_handling:
    keywords:
      - "設定値を"
      - "記憶する"
      - "初期値"
      - "ECU"
      - "RAM"


prompt:
  scl_extract:
    change_summary_prompt:
      - |-
        # Goal
        你是一个汽车电子软件的高级开发工程师
        ## Skill
        - 你熟悉各种软件开发流程（架构设计、详细设计、编码、测试）
        - 熟悉汽车电子领域的各种开发名词
        - 熟悉仪表中间件项目开发
        ## 变更内容概述
        {{change_summary}}  
        ## 变更内容列表
        {{change_mode_list}}
        遍历【变更内容列表】每一个值与【变更内容概述】语义匹配，输出【变更内容列表中】最接近【变更内容概述】内容的一个值
        - 注意一定输出的是【变更内容列表】中的值在变更内容列表中的索引

  generate_cstm_tool_prompts:
    generate_definition_prompt:
      - |-
        # Role
        你是一个汽车电子软件开发的高级工程师
        ## Skill
        - 擅长根据模块名生成对应宏定义函数名称
  
        ##【参考示例】
        {{few_shot}}
  
        ## 模块名
        {{label}}
  
        结合给出的示例，根据模块名生成该模块对应的宏定义函数名称。
  
        ## 要求
        函数名称语言类型为英文，其中的英文字母需要大写，如果是多个单词，则用"_"进行拼接
  
        ## 输出
        输出为宏定义函数名称

    identify_special_handling:
      - |-
        # Role
        你是一个汽车电子软件开发的高级工程师
        ## Skill
        - 擅长从文本片段中识别出指定的专业词汇或者与指定词汇含义相近的文本片段
        
        ### 文本片段
        {{special_handling_text}}
        
        ### 指定词汇如下
        {{specific_words}}
        
        ## 输出
        输出文本片段中是否包含指定词汇或者与指定词汇含义相近的文本片段，如果包含则输出True, 否则输出False.