import os
import json
import async<PERSON>
from typing import Op<PERSON>
from openai import AsyncOpenAI
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type
)
from tenacity import RetryCallState
from loguru import logger
from concurrent.futures import ThreadPoolExecutor


def log_retry_attempt(retry_state: RetryCallState):
    """记录每次重试尝试"""
    attempt_number = retry_state.attempt_number
    exception = retry_state.outcome.exception()
    logger.warning(f"第 {attempt_number} 次重试失败: {exception}")


def return_none_on_error(retry_state: RetryCallState):
    """所有重试失败后返回 None"""
    logger.error(f"所有重试均失败: {retry_state.outcome.exception()}")
    return {}


class OpenAIQwen:
    """OpenAI GPT implementation."""

    def __init__(
            self,
            base_url: str,
            api_key: Optional[str] = None,
            model: str = "qwen3",
    ):
        """Initialize OpenAI qwen client.

        Args:
            base_url: OpenAI base url
            api_key: OpenAI API key. If not provided, will try to get from environment
            model: Model to use, defaults to qwen3
        """
        self.base_url = base_url
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        self.model = model

    @retry(
        retry=retry_if_exception_type(Exception),
        stop=stop_after_attempt(4),
        wait=wait_exponential(multiplier=1, min=1, max=20),
        before_sleep=log_retry_attempt,
        retry_error_callback=return_none_on_error
    )
    async def generate(self, system_message, semaphore=asyncio.Semaphore(5)):
        """Generate text using OpenAI API."""
        async with semaphore:
            # await asyncio.sleep(random.uniform(1, 3))
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
            logger.info(f"QWEN开始处理指定输出：{system_message}")
            client = AsyncOpenAI(
                base_url=self.base_url,
                api_key=self.api_key
            )
            response = await client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                response_format={"type": "json_object"}
            )

            logger.info(f"QWEN处理指定输出完成：{response.choices[0].message.content}")
            generate_response = response.choices[0].message.content
            response_json = json.loads(generate_response)
            return response_json

    # @retry(retry=retry_if_exception_type(Exception), stop=stop_after_attempt(4), wait=wait_exponential(multiplier=1, min=1, max=20), before_sleep=log_retry_attempt, retry_error_callback=return_none_on_error)
    async def generate_text(self, system_message, temperature=0.7, top_p=0.7):
        """Generate text using OpenAI API."""
        # await asyncio.sleep(1)
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        # logger.info(f"QWEN开始处理指定输出：{system_message}")
        client = AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)
        response = await client.chat.completions.create(model=self.model, messages=messages,
                                                        temperature=temperature, top_p=top_p, extra_body={
                "chat_template_kwargs": {"enable_thinking": False}})
        # logger.info(f"QWEN处理指定输出完成：{response.choices[0].message.content}")
        generate_response = response.choices[0].message.content
        return generate_response

    async def get_multiple_llm_response(self, prompts):
        """Batch generation of text"""
        tasks = [self.generate_text(system_message=prompt) for prompt in prompts]
        responses = await asyncio.gather(*tasks)
        return responses

    @staticmethod
    def parse_content_from_response(responses, start_token, end_token):
        """Parse the content from the response."""
        results = []
        for response in responses:
            start_pos, end_pos = response.rfind(start_token), response.rfind(end_token) + 1
            if 0 <= start_pos < end_pos <= len(response):
                response = response[start_pos:end_pos]
            else:
                response = start_token + end_token
            results.append(response)
        return results

    @staticmethod
    def parse_content2json(responses, default='dict'):
        """Parse the JSON-formatted content from the text generated by LLM."""
        results = []
        for response in responses:
            try:
                result = json.loads(response)
            except Exception as e:
                result = {} if default == 'dict' else []
            results.append(result)
        return results

