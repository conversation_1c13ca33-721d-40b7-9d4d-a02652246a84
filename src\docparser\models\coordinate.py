from .base_object import BaseObject

class CoordinateObject(BaseObject):
    """坐标对象"""
    def __init__(self):
        self._left = ''  # 距离左边的位置
        self._top = ''  # 距离上边的位置
        self._right = ''  # 距离右边的位置
        self._bottom = ''  # 距离下边的位置
        self._relative = None  # 相对的位置，默认是空即屏幕。如果是相对于其他对象，则填写对象的引用
        self._desc = ''  # 自定义的坐标描述。例如Excel的A5，word的章节位置等

    def to_dict(self):
        """
        将 CoordinateObject 对象转换为字典
        """
        return {
            "left": self._left,
            "top": self._top,
            "right": self._right,
            "bottom": self._bottom,
            "relative": self._relative,  # 假设它是基本类型或可以直接序列化
            "desc": self._desc
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 CoordinateObject 实例
        """
        obj = cls()
        obj._left = data.get("left", '')
        obj._top = data.get("top", '')
        obj._right = data.get("right", '')
        obj._bottom = data.get("bottom", '')
        obj._relative = data.get("relative", None)  # 恢复相对位置
        obj._desc = data.get("desc", '')
        return obj

    @property
    def left(self):
        return self._left

    @left.setter
    def left(self, new_value):
        assert type(new_value) == str
        self._left = new_value

    @property
    def top(self):
        return self._top

    @top.setter
    def top(self, new_value):
        assert type(new_value) == str
        self._top = new_value

    @property
    def right(self):
        return self._right

    @right.setter
    def right(self, new_value):
        assert type(new_value) == str
        self._right = new_value

    @property
    def bottom(self):
        return self._bottom

    @bottom.setter
    def bottom(self, new_value):
        assert type(new_value) == str
        self._bottom = new_value

    @property
    def relative(self):
        return self._relative

    @relative.setter
    def relative(self, new_value):
        self._relative = new_value

    @property
    def desc(self):
        return self._desc

    @desc.setter
    def desc(self, new_value):
        assert type(new_value) == str
        self._desc = new_value


