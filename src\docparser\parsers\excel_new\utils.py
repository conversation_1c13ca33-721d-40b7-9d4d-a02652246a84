# -*- coding: utf-8 -*-
"""
@File    : scl_utils.py.py
<AUTHOR> zhenp
@Date    : 2025-06-19 15:12
@Desc    : Description of the file
"""
import re
from datetime import datetime
from typing import List, Dict, Tuple

from openpyxl.cell import Cell
from openpyxl.styles import Color

from docparser.models import StyleObject, FontStyleObject, RunObject, TextObject, Position
from docparser.common.theme_color import ThemeColor
from docparser.models.table import CellObject
from docparser.models.border import CellBorderObject, BorderObject
from openpyxl.cell.rich_text import TextBlock, CellRichText
from openpyxl.cell.text import InlineFont

from docparser.parsers.excel_new.constants import COLOR_INDEX, BLACK, WHITE


class CellUtil:
    def __init__(self, theme_colors):
        self.theme_colors = theme_colors

    def get_cell_info(self, cell: Cell) -> CellObject:
        cell_obj = CellObject()

        cell_obj.text = str(get_cell_value(cell))


        # TODO strike_ranges
        if cell.comment:
            cell_obj.comment.text = cell.comment.text

        cell_obj.border = self.get_cell_border(cell)
        cell_obj.coordinate.desc = cell.coordinate

        cell_obj.style = self.get_font_style(cell.font)
        # print(f"New Text: {cell_obj.text} New Style: {cell_obj.style}")

        current_text = TextObject()

        cell_obj.row_index = cell.row
        # print(cell_obj.row_index)
        cell_obj.col_index = cell.column

        bg_color = self.get_bg_color(cell)
        if bg_color:
            cell_obj.style.background_color = bg_color
        cell_obj.style.background_style = cell.fill.patternType or 'solid'

        run_objs = self.get_cell_text(cell)
        current_text._style = run_objs[0].style if run_objs else None
        current_text._runs = run_objs
        current_text._text = cell_obj.text
        current_text.coordinate.desc = cell.coordinate
        cell_obj.content.append(current_text)
        return cell_obj

    def get_cell_border(self, cell: Cell) -> CellBorderObject:
        cell_border = CellBorderObject()
        border_style = getattr(cell.border, "top").style
        border_color = self.get_rgb_color(getattr(cell.border, "top").color)
        cell_border.border_top = BorderObject(border_color, border_style)
        border_style = getattr(cell.border, "left").style
        border_color = self.get_rgb_color(getattr(cell.border, "left").color)
        cell_border.border_left = BorderObject(border_color, border_style)
        border_style = getattr(cell.border, "bottom").style
        border_color = self.get_rgb_color(getattr(cell.border, "bottom").color)
        cell_border.border_bottom = BorderObject(border_color, border_style)
        border_style = getattr(cell.border, "right").style
        border_color = self.get_rgb_color(getattr(cell.border, "right").color)
        cell_border.border_right = BorderObject(border_color, border_style)
        return cell_border

    def get_bg_color(self, cell: Cell) -> str:
        # 无填充
        if cell.fill.patternType is None:
            color_str = WHITE
        else:
            color = cell.fill.bgColor
            # 64 是系统保留的前景色
            if color.type == "indexed" and color.value == 64:
                color_str = self.get_rgb_color(cell.fill.fgColor)
            else:
                color_str = self.get_rgb_color(color)
        return color_str

    def get_font_style(self, font) -> StyleObject:
        """
        解析文本样式
        :param font:
        :return:
        """
        style_obj = StyleObject()
        style_obj._font_size = font.size
        if font.color:
            style_obj._font_color = self.get_rgb_color(font.color)
        elif font.color is None:
            style_obj._font_color = "#000000"
        style_obj._background_color = ''
        # 字体样式，如粗体、斜体、下划线、删除线
        font_style_ = FontStyleObject()
        if font.b:
            font_style_.bold = True  # 粗体
        if font.i:
            font_style_.italic = True  # 斜体
        if font.u:
            font_style_.underline = True  # 下划线
        if font.strike:
            font_style_.strikeout = True  # 删除线
        style_obj.font_style = font_style_

        # excel中font行内文本样式: InlineFont
        if isinstance(font, InlineFont):
            style_obj._font_family = font.rFont
        else:
            style_obj._font_family = font.name
        # print(style_obj._font_family)
        return style_obj

    def get_rgb_color(self, color: Color):
        """
        openpyxl的颜色对象解析对于的rgb颜色值
        :param color: Cell color
        :return:
        """
        if not color:
            return ""
        type_ = color.type
        value_ = color.value
        if type_ == 'rgb':
            return "#" + value_[2:] if len(value_) == 8 else "#" + value_
        elif type_ == 'indexed':
            # 索引颜色
            return get_color_by_index(value_)
        elif type_ == 'theme':
            # 主题颜色
            if color.theme >= len(self.theme_colors):
                return ""
            return "#" + ThemeColor().theme_and_tint_to_rgb(self.theme_colors, color.theme, color.tint)
        else:
            return ""

    def get_cell_text(self, cell: Cell) -> List[RunObject]:
        run_objs = []
        font = cell.font
        value = cell.value
        bg_color = self.get_bg_color(cell)
        if isinstance(value, TextBlock):
            style_obj = self.get_font_style(cell.value.font)
            style_obj.background_color = bg_color
            r = RunObject()
            r.coordinate.desc = cell.coordinate
            if cell.value.text:
                r._text = str(cell.value.text)
            r._style = style_obj
            run_objs.append(r)
        elif isinstance(value, CellRichText):
            # CellRichText 文本片段循环处理
            for item in value:
                item_font = font if isinstance(item, str) else item.font
                style_obj = self.get_font_style(item_font)
                style_obj.background_color = bg_color
                r = RunObject()
                r.coordinate.desc = cell.coordinate
                if item and isinstance(item, str):
                    r._text = item
                elif item.text:
                    r._text = str(item.text)
                r._style = style_obj
                run_objs.append(r)
        else:
            style_obj = self.get_font_style(font)
            style_obj.background_color = bg_color
            r = RunObject()
            if value:
                r._text = str(value)
            r.coordinate.desc = cell.coordinate
            r._style = style_obj
            run_objs.append(r)
        return run_objs


def get_cell_value(cell: Cell) -> str:
    v = format_number_value(cell)
    if isinstance(v, datetime):
        v = v.strftime('%Y/%m/%d')
    elif isinstance(v, TextBlock):
        v = v.text
    elif isinstance(v, CellRichText):
        v = str(v)
    elif v is None:
        v = ''
    return v

def format_number_value(cell: Cell):
    if cell.number_format.endswith("%") and isinstance(cell.value, (float, int)) and not isinstance(cell.value, bool):
        # 百分比数值
        decimal_places = cell.number_format.count("0") - 1
        format_value = round(cell.value * 100, 2)
        return f"%.{decimal_places}f%%" % format_value
    return cell.value

def get_color_by_index(value):
    """
    拼接颜色属性
    :param value:
    :return:
    """
    return BLACK if value >= len(COLOR_INDEX) else "#" + COLOR_INDEX[value][2:]

def get_positions(sheet) -> (Dict[Tuple[int, int], Position], int, int):
    total_width = .0
    width_dict = {}
    total_height = .0
    height_dict = {}
    cell_position_dict: Dict[Tuple[int, int], Position] = {}
    max_row = sheet.max_row
    max_col = sheet.max_column
    for col_idx in range(1, max_col + 1):
        # Convert column index to column letter
        current_cell = sheet.cell(row=1, column=col_idx)
        if hasattr(current_cell, "column_letter"):
            column_letter = current_cell.column_letter
        else:
            match = re.match(r"[A-Z]+",
                             current_cell.coordinate)  # Matches one or more alphabetic characters at the start
            column_letter = match.group()
        column_dimension = sheet.column_dimensions.get(column_letter, None)
        column_width = 8.38
        if column_dimension and column_dimension.width:
            column_width = column_dimension.width
        total_width += column_width
        width_dict[col_idx] = column_width

    for row_idx in range(1, max_row + 1):
        row_dimension = sheet.row_dimensions.get(row_idx, None)
        row_height = 13.5
        if row_dimension and row_dimension.height:
            row_height = row_dimension.height
        total_height += row_height
        height_dict[row_idx] = row_height

    accu_width = 0
    for col_idx in range(1, max_col + 1):
        accu_height = 0
        current_width = width_dict[col_idx]
        for row_idx in range(1, max_row + 1):
            current_height = height_dict[row_idx]
            position = Position()
            position.x = accu_width / total_width
            position.y = accu_height / total_height
            position.width = current_width / total_width
            position.height = current_height / total_height
            cell_position_dict[(row_idx, col_idx)] = position

            accu_height += current_height
        accu_width += current_width

    for merged_range in sheet.merged_cells.ranges:

        min_row = merged_range.min_row
        max_row = merged_range.max_row
        min_col = merged_range.min_col
        max_col = merged_range.max_col

        start_tuple = (min_row, min_col)
        start_position = cell_position_dict[start_tuple]

        for row in range(min_row + 1, max_row + 1):  # +1 to include the end_row
            current_position = cell_position_dict[(row, min_col)]
            start_position.height = start_position.height + current_position.height
            current_position.height = 0
        for col in range(min_col + 1, max_col + 1):  # +1 to include the end_col
            current_position = cell_position_dict[(min_row, col)]
            start_position.width = start_position.width + current_position.width
            current_position.width = 0
        for row in range(min_row + 1, max_row + 1):
            for col in range(min_col + 1, max_col + 1):
                cell_position_dict.get((row, col)).width = 0
                cell_position_dict.get((row, col)).height = 0

    return cell_position_dict, total_width, total_height

def shape_to_object(shape, obj):
    """
    shape json对象转换为对象模型
    :param shape:
    :param obj:
    :return:
    """
    obj._id = shape["id"]
    obj._name = shape["name"]
    obj._width = shape["width"]
    obj._height = shape["height"]
    obj.px_width = obj._width
    obj.px_height = obj._height
    obj._in_cell = shape.get("in_cell", True)
    obj._data = shape.get("data", "")
    obj._digest = shape.get("hash", "")
    obj.coordinate.desc = shape["index"]
    obj.coordinate.top = str(shape["top"])  # 坐标:距离顶边距离
    obj.coordinate.left = str(shape["left"])  # 坐标:距离左边距离
    obj._position = shape["position"]