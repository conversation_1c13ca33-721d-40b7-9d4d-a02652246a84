#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：TestcaseRAG
@File    ：testview_body.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2025/4/28 16:34
@Desc    : 说明
"""
from typing import List

class BaseModel:
    def __init__(self, data):
        for key, value in data.items():
            if isinstance(value, dict):
                setattr(self, key, BaseModel(value))
            else:
                setattr(self, key, value)

class TestViewMerge:
    id: int
    category: str
    checkpoint: str
    mse_category: str
    mse_content: str
    procedure: str
    source: str

class TestViewMergeList(BaseModel):
    """合并列表"""
    testview_list: List[TestViewMerge]
