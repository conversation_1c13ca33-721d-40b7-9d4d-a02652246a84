"""
新变表更新 Workflow

V字对应：
3.2 機能一覧と新規・変更内容 （DR・QA_MD）更新

该模块提供新变表更新功能，检查代码中新变表的更新内容。
1. 从Gerrit下载变更信息
2. 解析变更信息，更新新变表的内容(O列-AC列)
3. 输出新变表更新报告
"""
# 修正路径计算 - 需要向上追溯4层才能到达项目根目录
#import sys
#from pathlib import Path
#project_root = Path(__file__).parent.parent.parent.parent
#sys.path.insert(0, str(project_root))  # 使用insert(0)确保优先搜索

from pathlib import Path
from typing import Dict, List, Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.config.env import ENV

from sdw_agent.service.newchangelist_service.models import NclUpdateInput, NclUpdateResultItem
from sdw_agent.service.newchangelist_service.util.excel_util import load_excel_sheet, save_excel_results
from sdw_agent.service.newchangelist_service.util.llm_util import requests_llm
from sdw_agent.service.newchangelist_service.util.gerrit_util import GerritAPI

@register_workflow("ncl_update")
class NclUpdateWorkflow(BaseWorkflow):
    """新变更列表更新工作流"""
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """注册配置模型"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="ncl_update")

    def validate_input(self, input_data: NclUpdateInput) -> bool:
        """验证输入参数"""
        try:
            file_path = Path(input_data.file_path)
            if not file_path.exists():
                self.logger.error(f"文件不存在: {input_data.file_path}")
                return False
            return True
        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, input_data: NclUpdateInput) -> WorkflowResult:
        """执行工作流"""
        self.logger.info("开始执行NCL更新工作流")
        
        try:
            # 1. 初始化Gerrit API
            gerrit = self._init_gerrit()
            
            # 2. 处理Excel文件
            result_list = self._process_excel(input_data.file_path, gerrit)
            
            # 3. 生成结果文件
            output_file = self._generate_output(input_data.file_path, result_list)
            
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="NCL更新工作流执行成功",
                data={"output_file": output_file}
            )
        except Exception as e:
            self.logger.exception("NCL更新工作流执行失败")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"NCL更新工作流执行失败: {str(e)}",
                error=str(e)
            )

    def _init_gerrit(self) -> GerritAPI:
        """初始化Gerrit API"""
        return GerritAPI(
            base_url=self.config.get("user_config",{}).get("ncl_gerrit_url",{}),
            username=self.config.get("user_config",{}).get("ncl_gerrit_username",{}),
            password=self.config.get("user_config",{}).get("ncl_gerrit_password",{})
        )

    # 常量定义
    AW_COL = 49  # AV列索引(Excel从1开始计数)
    DEFAULT_VALUES = {
        "display_size": "12.3",
        "initial_status": "要help",
        "vehicle_code": "-",
        "sqa_status": "-",
        "change_scope": "要help",
        "pf_change": "無"
    }

    def _process_excel(self, file_path: str, gerrit: GerritAPI) -> List[NclUpdateResultItem]:
        """处理Excel文件内容 (优化后)"""
        excel_config = self.config.get("ncl_update", {}).get("excel", {})
        result_list = []
        
        try:
            for row in load_excel_sheet(
                file_path,
                excel_config.get("sheet_name", "機能一覧と新規・変更内容"),
                excel_config.get("start_row", 8)
            ):
                if self._should_skip_row(row):
                    break
                    
                av_value = row[self.AW_COL-1].value
                if av_value:
                    result_item = self._process_row(row, av_value, gerrit)
                    result_list.append(result_item)
                    
        except Exception as e:
            self.logger.error(f"处理Excel行时出错: {str(e)}")
            raise
            
        return result_list

    def _should_skip_row(self, row) -> bool:
        """判断是否跳过当前行"""
        return row[1].value == 'end' or row[1].value == None    # B列为'end'或空时跳过

    def _process_row(self, row, av_value: str, gerrit: GerritAPI) -> NclUpdateResultItem:
        """处理单行数据
        :param av_value: 可能包含单个或多个commit哈希值，多个哈希值用换行符分隔
        """
        # 初始化结果项
        result_item = NclUpdateResultItem(
            row=row,
            Display_size=self.DEFAULT_VALUES["display_size"],
            ソフトの変更内容="",
            変更対象コンポーネント="",
            新変定移植=self.DEFAULT_VALUES["initial_status"],
            車両コード=self.DEFAULT_VALUES["vehicle_code"],
            SQA完了状況=self.DEFAULT_VALUES["sqa_status"],
            行数=0,
            変更範囲=self.DEFAULT_VALUES["change_scope"],
            PF同時変更=self.DEFAULT_VALUES["pf_change"],
            PF同時変更2=self.DEFAULT_VALUES["pf_change"]
        )
        
        # 分割多个commit哈希值
        commit_hashes = [h.strip() for h in av_value.split('\n') if h.strip()]
        result = ''
        
        for commit_hash in commit_hashes:
            try:
                change_info = gerrit.get_change_by_commit(commit_hash)
                if not change_info:
                    continue
                    
                # 获取完整change_id格式 (project~branch~change_id)
                diff_code = gerrit.get_diff_code(change_info['id'], change_info['current_revision'])
                
                commit_msgs = []
                components = []
                
                for file_name, info in diff_code.items():
                    if file_name == 'COMMIT_MSG':
                        commit_msg = '\n'.join(line for i, line in enumerate(info['code_all']) if i >= 6)
                        commit_msgs.append(commit_msg.strip())
                        if len(info['code_all']) > 8:
                            components.append(info['code_all'][8])
                    else:
                        result += self._process_code_file(info, result_item)
                
                # 合并commit消息和组件信息
                if commit_msgs:
                    if result_item.ソフトの変更内容:  # 如果已有内容，先加换行符
                        result_item.ソフトの変更内容 += '\n\n\n'
                    result_item.ソフトの変更内容 += '\n'.join(commit_msgs)
                if components and components[0] not in result_item.変更対象コンポーネント:
                    if result_item.変更対象コンポーネント:  # 如果已有内容，先加换行符
                        result_item.変更対象コンポーネント += '\n'
                    result_item.変更対象コンポーネント += '\n'.join(components)
                        
            except Exception as e:
                self.logger.error(f"处理commit {commit_hash}时出错: {str(e)}")
                continue
                
        self._update_result_by_llm(result, result_item)
        return result_item

    def _process_code_file(self, info: Dict, result_item: NclUpdateResultItem):
        prompts = self.config.get("llm", {}).get("system_prompt", {})
        """处理代码文件"""
        if "mainline/ren_rcargen3/src/Platform/" in info['path']:
            result_item.PF同時変更 = "有"
            result_item.PF同時変更2 = "有"
            
        change_line = len(info['code_after']) + len(info['code_before'])
        result_item.行数 += change_line
        
        if info['code_after'] == info['code_all']:
            result = '新規'
        else:
            result = requests_llm('\n'.join(info['code_after']), '\n'.join(info['code_all']),prompts)
        return result

    def _update_result_by_llm(self, result: str, result_item: NclUpdateResultItem):
        """根据LLM结果更新分类"""
        if "新规" in result:
            result_item.新変定移植 = '新規'
        elif "变更" in result:
            result_item.新変定移植 = '变更'
            result_item.変更範囲 = 'ロジック'
        elif "定数" in result:
            result_item.新変定移植 = '定数'
            result_item.変更範囲 = '条件/定数'

    def _generate_output(self, file_path: str, result_list: List[Dict]) -> str:
        excel_config = self.config.get("ncl_update", {}).get("excel", {})
        return save_excel_results(
            file_path,
            excel_config.get("sheet_name", "機能一覧と新規・変更内容"),
            result_list,
            excel_config.get("field_mapping", {})
        )

def do_ncl_update(file_path: str) -> str:
    """执行NCL更新"""
    workflow = NclUpdateWorkflow()
    result = workflow.run(NclUpdateInput(file_path=file_path))
    
    if result.status == WorkflowStatus.SUCCESS:
        return result.data["output_file"]
    raise Exception(result.message)    


if __name__ == "__main__":
    FILE_PATH = 'D:/01.Roc/01.Doc/Roc_Doc/04INTEGRATION/0402IntegrationTest/DEV_Agent/0730功能测试结果汇总/新变表更新测试数据集/機能一覧と新規・変更内容_R5小（V6.0.3）.xlsx'

    results = do_ncl_update(FILE_PATH)
