import logging
import os.path
import re
import traceback

from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

import xlwings as xw
import pandas as pd

from dataclasses import dataclass
from typing import Dict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.warning_code_gen_check.models import RequirementChangeContentList, WarningChangeInfo
from sdw_agent.util.excel_util import click_buttons


def read_column_adas_as_list(file_path):
    """
    从指定 Excel 文件的 "MET_INTERRUPT" 工作表中读取 D 列内容，
    按换行符拆分每个单元格的内容并合并为一个列表。

    Args:
        file_path (str): Excel 文件路径。

    Returns:
        list: 包含所有拆分后内容的列表。
    """
    # 打开 Excel 文件
    app = xw.App(visible=False)
    app.display_alerts = False
    wb = app.books.open(file_path)
    sheet = wb.sheets["MET_INTERRUPT"]  # 读取指定工作表

    try:
        # 获取 D 列的所有内容 (假设 D 列从第 1 行开始)
        d_column = sheet.range("D1:D100").value  # 获取 D 列的所有非空单元格内容
        result = []

        # 遍历 D 列的每个单元格
        for cell in d_column:
            if cell:  # 如果单元格非空
                # 按换行符拆分并合并到结果列表
                result.extend(cell.splitlines())

        return result

    finally:
        # 关闭工作簿和 Excel 应用
        wb.close()
        app.quit()


def copy_data_between_xlsm(source_file, source_sheet, target_file, target_sheet, source_col=("B", "Z"),
                           target_col=("B", "Z"), row_offset=0):
    """
    将表 a.xlsm 的 sheet b 第 4-5 行的 A-Z 列数据复制到表 c.xlsm 的 sheet d 第 4-6 行的 B-AA 列。

    :param source_file: 源文件路径 (a.xlsm)
    :param source_sheet: 源 Sheet 名称 (b)
    :param target_file: 目标文件路径 (c.xlsm)
    :param target_sheet: 目标 Sheet 名称 (d)
    """
    try:
        # 打开 Excel 应用
        app = xw.App(visible=False)  # 设置 Excel 应用不可见
        app.display_alerts = False  # 禁用保存警告
        app.screen_updating = False  # 禁用屏幕更新

        # 打开源文件和目标文件
        source_wb = app.books.open(source_file)
        target_wb = app.books.open(target_file)

        # 获取源 Sheet 和目标 Sheet
        source_ws = source_wb.sheets[source_sheet]
        target_ws = target_wb.sheets[target_sheet]
        clear_sheet_range(target_wb, target_sheet)
        # 获取总列数
        last_cell = source_ws.used_range.last_cell  # 获取最后一个单元格
        total_rows = last_cell.row  # 获取最后一个单元格的行号
        logging.info(f"总数{total_rows}")

        # 读取源数据 (第 4-5 行的 A-Z 列)
        source_data = source_ws.range(f'{source_col[0]}5:{source_col[1]}{total_rows}').value
        # 写入目标文件 (第 4-6 行的 B-AA 列)
        target_ws.range(f'{target_col[0]}{5 + row_offset}:{target_col[1]}{total_rows + row_offset}').value = source_data

        # 保存目标文件
        # target_path = os.path.join(os.path.normpath(r"C:\Users\<USER>\Documents"), os.path.basename(target_file))
        target_wb.save()
        # target_wb.save(target_path)
        logger.info(f"数据已成功从 {source_file} 的 {source_sheet} 复制")
        # return target_path

    except Exception as e:
        logger.error(f"发生错误: {e}")

    finally:
        # 确保关闭文件和 Excel 应用
        source_wb.close()
        target_wb.close()
        app.quit()


def copy_data_between_workbooks(source_ws, target_ws, source_col=("B", "Z"), target_col=("B", "Z"), row_offset=0):
    """

    """
    try:
        # 获取源 Sheet 的最后一个单元格的行号
        last_cell = source_ws.used_range.last_cell
        total_rows = last_cell.row  # 获取最后一个单元格的行号
        logger.debug(f"源 Sheet 总行数: {total_rows}")

        # 读取源数据
        source_data = source_ws.range(f'{source_col[0]}5:{source_col[1]}{total_rows}').value

        # 写入目标数据
        target_ws.range(f'{target_col[0]}{5 + row_offset}:{target_col[1]}{total_rows + row_offset}').value = source_data

        logger.info(f"数据已成功从源工作簿的 Sheet 复制到目标工作簿的 Sheet。")

    except Exception as e:
        logger.error(f"发生错误: {e}")


def update_warning_dict(new_warning_dict, list_a):
    """
    遍历 new_warning_dict，检查 JSON 中 ①-⑧ 的 key 对应的值是否在 list_a 中，
    并添加 "adas": "Y" 或 "adas": "N"。

    Args:
        new_warning_dict (dict): 字典，key 是任意值，value 是一个 JSON 对象。
        list_a (list): 包含需要匹配的值的列表。

    Returns:
        dict: 更新后的 new_warning_dict。
    """
    # 定义需要检查的 key 列表
    keys_to_check = ["①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧"]

    # 遍历 new_warning_dict
    for key, value in new_warning_dict.items():
        # 初始化标志位，判断是否有匹配的值
        found_in_list_a = False

        # 遍历需要检查的 key
        for check_key in keys_to_check:
            # 如果 key 存在于 JSON 中，并且它的值在 list_a 中
            if check_key in value and value[check_key] in list_a:
                found_in_list_a = True
                break  # 如果找到一个匹配的值，可以提前退出

        # 根据标志位添加 "adas" 字段
        if found_in_list_a:
            value["adas"] = "Y"
        else:
            value["adas"] = "N"

    return new_warning_dict


def judge_new_adas_warning(new_warning_dict, adas_path):
    adas_list = read_column_adas_as_list(adas_path)
    logger.info(f"adas文件列表: {adas_list}")
    update_warning_dict(new_warning_dict, adas_list)
    return


def get_files_by_prefix(folder_path, prefix):
    """
    根据前缀递归获取文件夹中的文件名。

    Args:
        folder_path (str): 文件夹路径。
        prefix (str): 文件名前缀。

    Returns:
        list: 符合条件的文件路径列表。
    """
    prefix = prefix.replace("＿", "_")
    matching_files = []

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        raise FileNotFoundError(f"文件夹 {folder_path} 不存在！")

    # 递归遍历文件夹及其子文件夹
    for root, dirs, files in os.walk(folder_path):  # os.walk 遍历多级目录
        for file in files:
            if file.startswith(prefix):  # 筛选符合前缀的文件
                matching_files.append(os.path.join(root, file))  # 保存完整路径

    return matching_files



def analyze_channel_info(folder_path, new_warning_dict):
    """
    读取指定 Excel 文件中的多个表，在指定列中查找包含特定字符串的行，并将整行数据读取为字典。

    Returns:
        dict: 包含每个表的结果，结果是一个列表，每个元素是匹配行的字典。
    """
    # 遍历外层字典
    for warning_key, warning_info_dict in new_warning_dict.items():
        judge_new_dual_channel_warning_item(warning_info_dict, folder_path)
    return


def read_channel_info_book(file_path, warning_info_req):
    results = {}

    # 遍历每个表
    sheets = ["Variable_MID"]
    for sheet in sheets:
        try:
            # 读取当前工作表
            df = pd.read_excel(file_path, sheet_name=sheet)

            # 将列名转换为字母索引（例如 B -> 1）
            col_index = ord("B".upper()) - ord('A')

            # 查找 B 列中包含特定字符串的行
            matching_rows = df[df.iloc[:, col_index].astype(str).str.contains(warning_info_req["①"], na=False)]
            if matching_rows.empty:
                return "", ""

            # 将每行转换为字典
            results[sheet] = matching_rows.to_dict(orient='records')

            check_id = 0
            # 遍历每个工作表的结果
            for sheet_item, rows in results.items():
                logger.debug(f"工作表 {sheet_item} 的匹配结果:")
                # 遍历字典列表中的每个字典（即每行数据）
                check_id = rows[0]["C04"]
                spec_desc = rows[0]["C15"]
                match = re.search(r'「(.*?)」', spec_desc)
                if match:
                    detailed_spec = match.group(1)
                    break

            df_spec = pd.read_excel(file_path, sheet_name=detailed_spec)
            # 找出值为 "No.2" 的行和列
            # result_cur = df_spec[df_spec == f"基本画面No.{check_id}"]
            # result_next = df_spec[df_spec == f"基本画面No.{check_id + 1}"]
            # if result_next.empty:
            #     result_next = (df_spec.index[-1], 1)

            # 获取行索引和列索引
            # rows, cols = result_cur.where(result_cur.notna()).stack().index.tolist()
            # rows_next, cols_next = result_next.where(result_next.notna()).stack().index.tolist()

            # 查找包含 "No.2" 的行和列

            # filtered_rows = df_spec[df_spec["C04"].str.contains("No.2", na=False)].index
            row_indices = df_spec[df_spec["C04"].str.contains(f"基本画面No.{check_id}", na=False)].index.tolist()
            if len(row_indices) == 0:
                return None, None
            row_now = row_indices[0]

            row_indices = df_spec[df_spec["C04"].str.contains(f"基本画面No.{check_id + 1}", na=False)].index.tolist()
            if len(row_indices) == 0:
                # return None, None
                row_next = (df_spec.index[-1], 1)
            else:
                row_next = row_indices[0]

            g_text_list = df_spec.loc[row_now:row_next, "C07"].tolist()
            g_text_list = [g_text for g_text in g_text_list if isinstance(g_text, str)]

            h_text_list = df_spec.loc[row_now:row_next, "C08"].tolist()
            h_text_list = [h_text for h_text in h_text_list if isinstance(h_text, str)]

            return "".join(g_text_list), "".join(h_text_list)

        except Exception as e:
            traceback.print_exc()
            logger.error(f"读取工作表 {file_path} 时出错: {e}")
            return "", ""

    return results


def understand_warning_item_channel_spec(spec_list):
    if "表示中" in spec_list and "再度表示" in spec_list:
        return True
    return False

def understand_dms_spec(spec_list):
    if "【スポーツ切替】=(2d:GRor3d:GRMN)" in spec_list.replace(" ", ""):
        return True
    return False


def understand_warning_item_big_intr_spec(spec_list):
    if "全画面割り込み" in spec_list:
        return True
    return False


def updata_warning_item_control_info(warning_info_dict, folder_path, control_info_book):
    control_sample_book = get_files_by_prefix(folder_path, control_info_book)
    if len(control_sample_book) == 0:
        warning_info_dict.setdefault("dual_channel", "N")
        warning_info_dict.setdefault("big_intr", "N")
        warning_info_dict.setdefault("dms", "N")
        logger.info(f"未找到控制式样书{control_info_book},双通道信息设置为N")
        return False
    control_sample_book_path = os.path.join(folder_path, control_sample_book[0])
    channel_spec_list, big_intr_spec_list = read_channel_info_book(control_sample_book_path, warning_info_dict)
    if understand_warning_item_channel_spec(channel_spec_list):
        logger.info(f"根据控制式样书{warning_info_dict['メータ仕様書No（参考）']},双通道信息为Y")
        warning_info_dict["dual_channel"] = "Y"
    else:
        warning_info_dict.setdefault("dual_channel", "N")

    if understand_dms_spec(channel_spec_list):
        logger.info(f"根据控制式样书{warning_info_dict['メータ仕様書No（参考）']},dms信息为Y")
        warning_info_dict["dms"] = "Y"
    else:
        warning_info_dict.setdefault("dms", "N")

    if understand_warning_item_big_intr_spec(big_intr_spec_list):
        logger.info(f"根据控制式样书{warning_info_dict['メータ仕様書No（参考）']},大中断信息为Y")
        warning_info_dict["big_intr"] = "Y"
    else:
        warning_info_dict.setdefault("big_intr", "N")

    return


def judge_new_dual_channel_warning_item(warning_info_dict, folder_path):
    control_books = warning_info_dict['メータ仕様書No（参考）'].split("/")
    for control_book in control_books:
        control_book = control_book.strip("\n \r\t")
        updata_warning_item_control_info(warning_info_dict, folder_path, control_book)

    return


def gen_change_warning_row(compare_file):
    """
    读取 Excel 文件并处理数据。

    Args:
        compare_file (str): Excel 文件路径。

    Returns:
        dict: 以 B 列为 key，其他列为 JSON 的字典。
    """
    # 打开 Excel 文件
    app = xw.App(visible=False)
    app.display_alerts = False
    wb = app.books.open(compare_file)
    sheet = wb.sheets["new"]

    try:
        # 获取所有数据
        data = sheet.used_range.value  # 获取表格所有数据为二维数组
        third_row = data[2]  # 第三行作为备用 JSON 的 key
        headers = data[3]  # 第四行作为主 JSON 的 key
        result = {}
        change_before = {}

        # 找到最后一个非空行的索引
        last_row_index = 4  # 默认从第5行开始（索引4）
        for i in range(4, len(data)):  # 从第5行开始遍历
            if data[i][26] is not None and str(data[i][26]).strip() != '':  # Z列索引为26
                last_row_index = i
        while last_row_index >= 4 and all(cell is None or cell == '' for cell in data[last_row_index]):
            last_row_index -= 1

        logger.info(f"compare_file new 最后一行的索引: {last_row_index}")
        # 遍历数据（从第五行开始，索引为4）
        for row in data[4:last_row_index + 1]:
            if row[26] is None:
                break
            if row[26] != "无变更":  # Z 列为第27列，索引为26
                # if row[26] == "新增":  # Z 列为第27列，索引为26
                key = row[1]  # B 列的值作为 key，索引为1
                json_data = {}
                for col_idx, value in enumerate(row):
                    if col_idx != 1:  # 跳过 B 列
                        # 如果 headers[col_idx] 是空值，则使用第三行的值作为 JSON key
                        json_key = headers[col_idx] if headers[col_idx] else third_row[col_idx]
                        json_data[json_key] = value
                result[key] = json_data

            if "不匹配" in row[26]:  # Z 列为第27列，索引为26
                key = row[1]  # B 列的值作为 key，索引为1
                json_data = {}
                for col_idx, value in enumerate(row):
                    if col_idx != 1:  # 跳过 B 列
                        # 如果 headers[col_idx] 是空值，则使用第三行的值作为 JSON key
                        json_key = headers[col_idx] if headers[col_idx] else third_row[col_idx]
                        json_data[json_key] = value
                change_before[key] = json_data

        # 获取 contdisp 页签
        sheet = wb.sheets["contdisp"]
        sheet.api.Rows.Hidden = False

        # 获取最后一行
        last_row = sheet.range("B5").end("down").row
        last_row = min(last_row, 50)

        # 填充 G 到 O 列，从第 5 行到最后一行
        for row in range(5, last_row + 1):  # 从第 5 行到最后一行
            for col in range(2, 16):  # G=7, O=15
                sheet.cells(row, col).value = ''

        # 设置每一行的行高为 20
        for row in range(1, last_row + 1):  # 遍历从第 1 行到最后一行
            sheet.api.Rows(row).RowHeight = 20

        # 获取指定范围
        rng = sheet.range("G3:O30")

        # 清除背景色
        rng.color = None  # 设置为无背景色

        # 清除样式
        rng.api.Font.Name = None  # 恢复字体样式
        rng.api.Font.Size = None  # 恢复字体大小
        rng.api.Font.Bold = False  # 去掉加粗
        rng.api.Font.Italic = False  # 去掉斜体
        rng.api.Font.Underline = False  # 去掉下划线
        rng.api.Borders.LineStyle = None  # 去掉边框样式
        # 设置文本水平和垂直居中
        rng.api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter  # 水平居中
        rng.api.VerticalAlignment = xw.constants.VAlign.xlVAlignCenter  # 垂直居中

        output_data_path = os.path.normpath(ENV.config.output_data_path)
        if not os.path.exists(output_data_path):
            os.makedirs(output_data_path)
        ret_book = os.path.join(output_data_path, f"Compare_CONTDISP_OldvsNew.xlsm")
        wb.save(ret_book)
        return result, sheet.api.UsedRange.Rows.Count - 4, ret_book

    except Exception as e:
        traceback.print_exc()
        raise e

    finally:
        # 关闭工作簿和 Excel 应用
        wb.close()
        app.quit()


def write_change_warning_row_to_file(compare_file, warning_change_info: WarningChangeInfo, new_filepath):
    """
    读取 Excel 文件并处理数据。

    Args:
        compare_file (str): Excel 文件路径。

    Returns:
        dict: 以 B 列为 key，其他列为 JSON 的字典。
    """
    # 打开 Excel 文件
    app = xw.App(visible=False)
    app.display_alerts = False
    wb = app.books.open(compare_file)
    try:
        row_info = warning_change_info.change_row_result
        # 初始化两个字典
        dict_add = {}
        dict_change = {}

        # 遍历 row_info，根据 属性2 的值分配到不同字典
        for key, value in row_info.items():
            if value["比较结果"] == "新增":
                dict_add[key] = value
            elif value["比较结果"] != "新增":
                dict_change[key] = value

        logger.info(f"新增告警：{dict_add}")
        logger.info(f"改动告警：{dict_change}")
        # 获取 contdisp 页签
        sheet = wb.sheets["contdisp"]
        sheet.api.Rows.Hidden = False

        # 获取最后一行
        last_row = sheet.range("B5").end("down").row
        last_row = min(last_row, 20)
        # 填充 G 到 O 列，从第 5 行到最后一行
        for row in range(5, last_row + 1):  # 从第 5 行到最后一行
            for col in range(2, 16):  # G=7, O=15
                sheet.cells(row, col).value = ''

        # 设置每一行的行高为 20
        for row in range(1, last_row + 1):  # 遍历从第 1 行到最后一行
            sheet.api.Rows(row).RowHeight = 20

        # 获取指定范围
        rng = sheet.range("G3:O30")

        # 清除背景色
        rng.color = None  # 设置为无背景色

        # 清除样式
        rng.api.Font.Name = None  # 恢复字体样式
        rng.api.Font.Size = None  # 恢复字体大小
        rng.api.Font.Bold = False  # 去掉加粗
        rng.api.Font.Italic = False  # 去掉斜体
        rng.api.Font.Underline = False  # 去掉下划线
        rng.api.Borders.LineStyle = None  # 去掉边框样式
        # 设置文本水平和垂直居中
        rng.api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter  # 水平居中
        rng.api.VerticalAlignment = xw.constants.VAlign.xlVAlignCenter  # 垂直居中

        # 变更
        start_row = 3
        for i, (key, value) in enumerate(dict_change.items()):
            change_item = value.get("比较结果", "")
            change_all_col = re.findall("[A-Z]+", change_item)
            logger.debug(change_all_col)

            # 写入 key 到 B 列
            sheet.range(f"B{start_row + i}").value = key
            sheet.range(f"C{start_row + i}").value = "要"
            # 写入 value["1"] 到 D 列
            sheet.range(f"D{start_row + i}").value = value.get("①", "")  # 如果没有 "1"，写入空字符串
            sheet.range(f"E{start_row + i}").value = "变更"
            if "MEF" in value.get("①", ""):
                sheet.range(f"F{start_row + i}").value = "MID"  # MID/VMID
            else:
                sheet.range(f"F{start_row + i}").value = "VMID"  # MID/VMID
            sheet.range(f"G{start_row + i}").value = "○" if "R" in change_all_col else "×"  # 如果没有 "1"，写入空字符串
            sheet.range(f"H{start_row + i}").value = "×"
            sheet.range(f"I{start_row + i}").value = "○" if "P" in change_all_col else "×"
            sheet.range(f"J{start_row + i}").value = "○" if "M" in change_all_col else "×"
            # sheet.range(f"K{start_row + i}").value = "○" if "M" in change_all_col else "×"
            sheet.range(f"L{start_row + i}").value = "○" if "N" in change_all_col else "×"
            sheet.range(f"M{start_row + i}").value = "○" if "C" in change_all_col else "×"
            sheet.range(f"N{start_row + i}").value = "×"
            sheet.range(f"O{start_row + i}").value = "×"

        # 新规
        start_row = start_row + len(dict_change)
        for i, (key, value) in enumerate(dict_add.items()):
            # 写入 key 到 B 列
            sheet.range(f"B{start_row + i}").value = key
            sheet.range(f"C{start_row + i}").value = "要"
            # 写入 value["1"] 到 D 列
            sheet.range(f"D{start_row + i}").value = value.get("①", "")  # 如果没有 "1"，写入空字符串
            sheet.range(f"E{start_row + i}").value = "新规"
            if "MEF" in value.get("①", ""):
                sheet.range(f"F{start_row + i}").value = "MID"  # MID/VMID
            else:
                sheet.range(f"F{start_row + i}").value = "VMID"  # MID/VMID
            sheet.range(f"G{start_row + i}").value = value.get("メッセージタブ\n表示対象", "×")
            sheet.range(f"H{start_row + i}").value = "○"
            sheet.range(f"I{start_row + i}").value = "×"
            sheet.range(f"J{start_row + i}").value = value.get("ブザー\n○吹鳴", "×")
            sheet.range(f"K{start_row + i}").value = "×" if value.get("dual_channel", "×") == "N" else "○"
            sheet.range(f"L{start_row + i}").value = value.get("ソフトSWを伴う割込みメッセージ\n※1", "×")
            sheet.range(f"M{start_row + i}").value = "×" if value.get("adas", "×") == "N" else "○"
            sheet.range(f"N{start_row + i}").value = "×" if value.get("big_intr", "×") == "N" else "○"
            sheet.range(f"O{start_row + i}").value = "×"

        wb.save(new_filepath)
        return

    finally:
        # 关闭工作簿和 Excel 应用
        wb.close()
        app.quit()


def parse_requirement_change_content(warning_change_info: WarningChangeInfo):
    # 打开 Excel 文件
    app = xw.App(visible=False)
    app.display_alerts = False
    wb = app.books.open(warning_change_info.compare_tools_url)
    ws = wb.sheets["要件变更内容"]
    try:
        # 调用解析函数
        parsed_data = parse_sheet_to_dict_with_row_number(ws)
        gen_requirement_change_content(ws, parsed_data)
        # 打印解析结果
        for row_number, row_dict in parsed_data.items():
            logger.debug(f"{row_number}: {row_dict}")

        wb.save()
        return

    finally:
        # 关闭工作簿和 Excel 应用
        wb.close()
        app.quit()


def parse_sheet_to_dict_with_row_number(ws):
    """
    解析指定工作簿的指定页签，将每一行解析成一个以行号为键的字典，处理合并单元格的情况。

    :param sheet_name: 页签名称，默认为 "要件变更内容"。
    :return: 包含每一行数据的字典，格式为 {行号: {键值对}}。
    """
    try:
        # 获取工作表

        # 获取第一行作为键名
        header_row = ws.range("A1").expand("right").value
        if not header_row:
            raise ValueError(f"页签 的第一行为空，无法解析键名。")

        # 获取 A 列的最后一行，考虑合并单元格的情况
        last_row = get_actual_last_row(ws)

        # 获取数据范围
        data_range = ws.range(f"A1:{chr(64 + len(header_row))}{last_row}").value

        # 初始化结果字典
        result = {}

        # 遍历数据区域的每一行（跳过第一行）
        for row_idx, row_data in enumerate(data_range[1:], start=2):  # 从第2行开始
            row_dict = {}
            for col_idx, cell_value in enumerate(row_data):
                # 如果单元格为空且是合并单元格，获取合并单元格的值
                if cell_value is None:
                    cell_value = ws.range((row_idx, col_idx + 1)).merge_area.value
                row_dict[header_row[col_idx]] = cell_value
            result[row_idx] = row_dict

        return result

    except Exception:
        return {}


def get_actual_last_row(ws):
    """
    获取工作表的实际最后一行，考虑合并单元格的情况。

    :param ws: xlwings.Worksheet 工作表对象。
    :return: 实际最后一行的行号。
    """
    try:
        # 获取 UsedRange 的最后一行
        used_range_last_row = ws.api.UsedRange.Rows.Count

        # 遍历所有单元格，检查合并单元格的范围
        max_row = 1
        for row in range(1, used_range_last_row + 1):
            for col in range(1, ws.api.UsedRange.Columns.Count + 1):
                cell = ws.range((row, col))
                # 如果单元格是合并单元格，获取合并区域的最后一行
                if cell.api.MergeCells:
                    merge_area_last_row = cell.api.MergeArea.Row + cell.api.MergeArea.Rows.Count - 1
                    max_row = max(max_row, merge_area_last_row)
                else:
                    max_row = max(max_row, row)

        return max_row

    except Exception as e:
        logger.error(f"获取实际最后一行时发生错误: {e}")
        return 1


def llm_requirement_change_content(requirement_change_content):
    # 创建一个聊天提示模板，用于生成变更摘要
    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt.requirement_change_content_prompts.change_content_desc_prompts)
        ],
        template_format="mustache",
    )
    invoke_data = {
        "change_content": requirement_change_content["概要"],
        "warning_content_list": [],
        "lang": "中文"
    }

    resp: AIMessage = get_ai_message_with_structured_output(
        template,
        invoke_data,
        RequirementChangeContentList,
        llm_model=None
    )
    result = resp.requirement_change_content_list
    return result


def insert_rows_and_fill_data(ws, row_number, row_data):
    """
    在指定行号下方插入 len(row_data) - 1 行，并将 row_data 的数据依次填入每一行的 A 列。

    :param ws: xlwings.Worksheet 工作表对象。
    :param row_number: 插入行的起始位置（行号）。
    :param row_data: 要填入的数据列表。
    """
    try:
        # 计算需要插入的行数
        num_rows_to_insert = len(row_data) - 1

        if num_rows_to_insert <= 0:
            logger.info("无需插入行，数据长度不足。")
            return

        # 插入行
        ws.api.Rows(row_number + 1).Insert(Shift=1, CopyOrigin=1)  # 插入一行
        for _ in range(num_rows_to_insert - 1):  # 插入剩余行
            ws.api.Rows(row_number + 2).Insert(Shift=1, CopyOrigin=1)

        # 填充数据到 A 列
        for i, value in enumerate(row_data):
            ws.range(f"A{row_number + i}").value = value.contdsip_id
            ws.range(f"E{row_number + i}").value = value.contdsip_change_desc
            ws.range(f"F{row_number + i}").value = value.need_parse_contdsip

        for col in range(2,5):
            cell_range = ws.range((row_number, col), (row_number + i, col))
            # 合并单元格
            cell_range.merge()

        logger.debug(f"在第 {row_number} 行下方插入 {num_rows_to_insert} 行，并填充数据完成。")

    except Exception as e:
        logger.error(f"插入行并填充数据时发生错误: {e}")


def gen_requirement_change_content(ws, requirement_change_content_dict):
    # 使用多线程并行处理数据
    content_dict = {}
    offset = 0

    # 定义一个线程池
    with ThreadPoolExecutor() as executor:
        # 提交所有任务到线程池
        future_to_row = {
            executor.submit(llm_requirement_change_content, row_data): row_number
            for row_number, row_data in requirement_change_content_dict.items()
        }

        # 收集任务结果
        for future in as_completed(future_to_row):
            row_number = future_to_row[future]
            try:
                # 获取任务返回值
                requirement_change_content_list = future.result()
                content_dict[row_number] = requirement_change_content_list
                logger.debug(f"第 {row_number} 行数据处理完成")
            except Exception as e:
                logger.error(f"第 {row_number} 行数据处理时出错: {e}")

    # 遍历处理后的数据并写入 Excel
    for row_number in requirement_change_content_dict.keys():
        requirement_change_content_list = content_dict[row_number]
        if len(requirement_change_content_list) > 1:
            insert_rows_and_fill_data(ws, row_number + offset, requirement_change_content_list)
            offset = offset + len(requirement_change_content_list) - 1
        elif len(requirement_change_content_list) == 1:
            ws.range(f"A{row_number + offset}").value = requirement_change_content_list[0].contdsip_id
            ws.range(f"E{row_number + offset}").value = requirement_change_content_list[0].contdsip_change_desc
            ws.range(f"F{row_number + offset}").value = requirement_change_content_list[0].need_parse_contdsip


    return

def clear_sheet_range(wb, sheet_name: str, start_row: int = 5, end_row: str = "last",
                      start_col: str = "B", end_col: str = "AB"):
    """
    清空指定页签的指定范围

    Args:
        wb: 工作簿对象
        sheet_name: 页签名称
        start_row: 起始行号（默认1）
        end_row: 结束行号（"last"表示最后一行，或指定具体行号）
        start_col: 起始列（默认A）
        end_col: 结束列（默认Z）
    """
    try:
        # 获取工作表
        ws = wb.sheets[sheet_name]

        # 获取最后一行
        if end_row == "last":
            last_row = ws.used_range.last_cell.row
        else:
            last_row = int(end_row)

        # 构建清空范围
        clear_range = f"{start_col}{start_row}:{end_col}{last_row}"

        # 清空内容
        ws.range(clear_range).clear()

        logger.info(f"已清空 {sheet_name} 页签的 {clear_range} 范围")

    except Exception as e:
        logger.error(f"清空页签范围失败: {str(e)}")
        raise

def gen_warning_list_change_info(warning_change_info: WarningChangeInfo):
    warning_sheet_name = "一覧"
    compare_sheet_new_name = "new"
    compare_sheet_old_name = "old"
    # 如果没有外部没有给比较工具的比较工具路径，则使用内置的文件

    is_need_parse_requirement_change_content = True
    if warning_change_info.compare_tools_url == "":
        warning_change_info.compare_tools_url = os.path.join(os.path.dirname(__file__),
                                                             os.path.normpath('../data/Compare_CONTDISP_OldvsNew.xlsm'))
        is_need_parse_requirement_change_content = False

    copy_data_between_xlsm(warning_change_info.after_sample_book_url, warning_sheet_name,
                           warning_change_info.compare_tools_url, compare_sheet_new_name)
    copy_data_between_xlsm(warning_change_info.pre_sample_book_url, warning_sheet_name,
                           warning_change_info.compare_tools_url, compare_sheet_old_name)
    click_buttons(warning_change_info.compare_tools_url, ["sheet1.CommandButton2_Click"])
    change_result, all_rows_num, new_compare_book = gen_change_warning_row(warning_change_info.compare_tools_url)
    warning_change_info.compare_tools_url = new_compare_book
    judge_new_adas_warning(change_result, warning_change_info.adas_book_path)
    analyze_channel_info(warning_change_info.relate_book_folder, change_result)
    warning_change_info.change_row_result = change_result
    logger.info(f"change_result:{change_result}")
    warning_change_info.after_warning_num = all_rows_num
    # 生成新文件路径
    """生成输出文件路径"""
    directory, filename = os.path.split(warning_change_info.compare_tools_url)
    new_filename = f"本次对比_{filename}"
    new_filepath = os.path.join(directory, new_filename)
    write_change_warning_row_to_file(warning_change_info.compare_tools_url, warning_change_info, new_filepath)
    warning_change_info.compare_tools_url = new_filepath
    if is_need_parse_requirement_change_content:
        parse_requirement_change_content(warning_change_info)
    logger.success(f"已经生成了对比表格{warning_change_info.compare_tools_url}")
    return change_result


if __name__ == "__main__":
    pass
