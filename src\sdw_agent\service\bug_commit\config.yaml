# Bug提交工作流配置

# 基本配置
name: "Bug提交"
description: "分析测试用例中的NG结果，并生成标准化的Bug提交内容"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入文件格式
  input:
    excel_extensions: [".xlsx", ".xls"]  # 测试用例文件支持的格式
    
  # 输出文件配置
  output:
    default_output_dir: "bug_commit_path"  # 默认输出目录
    output_filename_redmine: "bug_commit_results.xlsx" # 输出文件名
    output_filename_testcase: "bug_id_to_testcase.xlsx"


# 处理参数
processing:
  # Bug严重度配置
  severity:
    levels:
      S: "非常严重，可能导致用户有生命危险"
      A: "仪表花屏、黑屏、死机、卡滞、不能休眠（24h内解决）"
      B: "功能不动作：TT不能点亮、ADAS功能不能触发、Customize中某功能未实现、警告不显示"
      C: "显示问题：显示不完整、文字内容超框、交互内容显示先后顺序错误"
      D: "建议修改类问题、字符不统一、大小不一致、排列不美观等不影响使用，但影响用户体验"
  
  # Bug影响度配置
  influence:
    levels:
      A0: "重大问题（reset、黑画）"
      A1: "对车完有重要影响的S级问题"
      A2: "对车完有影响的A级问题"
      B0: "功能完全不动作"
      B1: "功能部分动作"
      C: "显示问题，功能本身已实装"

  # 模块匹配配置
  module:
    candidates:
      - "Speedo"
      - "Tacho"
      - "Fuel"
      - "Dimmer"
      - "Temp"
      - "ODO/Trip"
      - "Shift"
      - "Indicator"
      - "Buzzer"
      - "Warning"
      - "SOC"
      - "Hybsys"
      - "Adas"
      - "HUD"
      - "共通"
      - "MM连携"
      - "按键"
      - "画面迁移"
  
  # Bug修复时间计算
  fix_time:
    cost_days:
      A: 1
      B: 2
      C: 3
      D: 7

  # Excel输出配置
  excel_output:
    columns:
      - "Bug ID"
      - "主题"
      - "优先级"
      - "状态"
      - "指派给"
      - "创建"
      - "开始日期"
      - "截止日期"
      - "预计耗时"
      - "跟踪"
      - "类别"
      - "目标版本"
      - "所属组件"
      - "涉及画面"
      - "Bug影响度"
      - "创建者"
      - "完成比例"
      - "备注"

# LLM配置
llm:
  model: "gpt4o"
  system_prompt: |
    你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {message}" 