import os
import shutil
import time
from collections import defaultdict

import pandas as pd
import yaml
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment

from loguru import logger
from openpyxl.utils import get_column_letter

from sdw_agent.util.local_file_read_save import read_json
from sdw_agent.service.test_view_generate.config import (ROOT_DIR, RESULT_OUTPUT, SUB_OUTPUT,
                                                         RESULT_JSON, TEST_VIEW, TEST_CASE)
from sdw_agent.config.env import ENV

output_path = ENV.config.output_data_path
input_path = ENV.config.input_data_path


class TestCase:
    @staticmethod
    def supplemental_generate():
        choose_case = TestCase.generate_testview()
        curr_dir = TestCase.copy_result()
        view_path = os.path.abspath(os.path.join(curr_dir,"testview.xlsx"))
        case_path = os.path.abspath(os.path.join(curr_dir, "testcase.xlsx"))
        write_to_case(case_path, choose_case)
        res = {
            "testview": view_path,
            "testcase": case_path
        }
        return res

    @staticmethod
    def copy_result():
        result_dir = os.path.join(output_path, RESULT_OUTPUT)
        if not os.path.exists(result_dir):
            os.mkdir(result_dir)
        current_changepoint = read_json(os.path.join(input_path, SUB_OUTPUT, "current_changepoint.json"))
        summary = current_changepoint.get("ARチケットNO")
        curr_dir = os.path.join(result_dir,f"result_{time.strftime('%Y%m%d%H%M%S', time.localtime())}_{summary}")
        try:
            # 如果目标文件夹已存在，先删除（可选）
            if os.path.exists(curr_dir):
                shutil.rmtree(curr_dir)
            shutil.copytree(os.path.join(input_path, SUB_OUTPUT), curr_dir)
            logger.info("文件夹复制成功")
        except Exception as e:
            logger.error(f"复制失败: {e}")
        return curr_dir


    @staticmethod
    def generate_testview():
        class_res = read_json(os.path.join(input_path, SUB_OUTPUT, RESULT_JSON))
        result_list = []
        i = 0
        # 保存被选中的测试用例
        check_cases = defaultdict(list)
        for item in class_res:
            i += 1
            if not item.get("testview_content_list"):
                tmp = {
                    "序号": i,
                    "MSEの想定分類": item.get("testview_class", ""),
                    "DNKT評価チームの\n検査項目": item.get("testview_item"),
                    "MSEが必要と思う\n検査項目": "",
                    "MSEが想定する内容": "",
                    "DNKTが実施した内容　※特定パターン": "",
                    "用例对应No号": "",
                    "观点来源": item["source"] if "source" in item else "",
                }
                result_list.append(tmp)
            else:
                for j, testview_content in enumerate(item.get("testview_content_list")):
                    tmp = {
                        "序号": i if j == 0 else "",
                        "MSEの想定分類": item.get("testview_class", "") if j == 0 else "",
                        "DNKT評価チームの\n検査項目": item.get("testview_item") if j == 0 else "",
                        "MSEが必要と思う\n検査項目": "",
                        "MSEが想定する内容": "",
                        "DNKTが実施した内容　※特定パターン": testview_content.get("testview_content_item"),
                        "用例对应No号": "、".join(
                            [f"{testcase['sheet_name']}-{testcase['示例']}" for testcase in
                             testview_content.get('test_case_list')]) if "test_case_list" in testview_content else "",
                        "观点来源": item["source"] if "source" in item else "",
                    }
                    result_list.append(tmp)

                    # 记录被选中的测试用例
                    for one_view in testview_content.get("test_case_list"):
                        case_id = one_view.get("示例")
                        case_sheet = one_view.get("sheet_name")
                        if case_sheet in check_cases.keys() and case_id in check_cases.get(case_sheet):
                            continue
                        check_cases[case_sheet].append([case_id, 1])

        df = pd.DataFrame(data=result_list)
        testview_path = os.path.join(input_path, SUB_OUTPUT, TEST_VIEW)
        df.to_excel(testview_path, index=False)
        try:
            wb = load_workbook(testview_path)  # 加载已有文件
        except FileNotFoundError:
            wb = Workbook()  # 创建新文件

        sheet = wb.active  # 选择活动工作表

        # 在第一行插入数据
        sheet.insert_rows(1)  # 在第一行前插入一行

        # 合并第2、3列（B1:C1）
        sheet.merge_cells('B1:C1')
        sheet['B1'] = '大項目(分母出し切る)'  # 合并后数据需写入左上角单元格
        sheet['B1'].alignment = Alignment(horizontal='center', vertical='center')  # 设置居中

        # 合并第4、5列（D1:E1）
        sheet.merge_cells('D1:E1')
        sheet['D1'] = '中項目(検査の範囲)'  # 合并后数据需写入左上角单元格
        sheet['D1'].alignment = Alignment(horizontal='center', vertical='center')  # 设置居中

        sheet['F1'] = '小項目(DNKTが検査実施した工程と、検査内容)'

        sheet.merge_cells('A1:A2')
        sheet['A1'] = '序号'
        sheet['A1'].alignment = Alignment(horizontal='center', vertical='center')
        sheet.merge_cells('G1:G2')
        sheet['G1'] = '用例对应No号'
        sheet['G1'].alignment = Alignment(horizontal='center', vertical='center')
        sheet.merge_cells('H1:H2')
        sheet['H1'] = '来源'
        sheet['H1'].alignment = Alignment(horizontal='center', vertical='center')

        # 保存文件
        wb.save(testview_path)
        return check_cases


def write_to_case(case_path, case_data):
    """将被选中的测试用例回填到全局的测试用例文件中"""
    # 获取测试用例的表头行号
    config = yaml.load(open(os.path.join(ROOT_DIR, "config.yaml"), "r", encoding="utf-8"),
                       Loader=yaml.FullLoader)
    test_agent_input_table = config["test_agent_input_table"]["testcases"]
    header_index = test_agent_input_table["header_index"]
    # 加载工作簿
    workbook = load_workbook(case_path, data_only=True)
    # 遍历每一个需要补充信息的sheet页
    for one_sheet, map_list in case_data.items():
        # 不存在待补充的信息时过滤
        if not map_list:
            continue
        # 获取指定 sheet
        sheet = workbook[one_sheet]
        # 获取表头行
        header_row = next(sheet.iter_rows(min_row=header_index, max_row=header_index, values_only=True))
        # 查找搜索列的索引
        search_col_idx = header_row.index("示例")
        # 获取最后一列的索引
        last_col_idx = sheet.max_column
        # 在最后一列之后添加新列，并设置表头
        new_col_idx = last_col_idx + 1
        new_col_letter = get_column_letter(new_col_idx)
        sheet[f"{new_col_letter}{header_index}"] = "被选中的测试用例"
        # 查找匹配的行并更新新列的值
        for one_map in map_list:
            for row in sheet.iter_rows(min_row=header_index + 1, values_only=False):
                row_id_num = row[search_col_idx].value
                if not row_id_num:
                    continue
                if str(row_id_num) == one_map[0]:
                    row[new_col_idx - 1].value = one_map[-1]
    # 保存工作簿（覆盖原文件）
    workbook.save(case_path)


if __name__ == '__main__':
    res = TestCase.supplemental_generate()
    print('ok')
