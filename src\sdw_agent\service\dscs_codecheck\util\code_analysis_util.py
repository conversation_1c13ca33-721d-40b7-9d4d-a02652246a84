from typing import Dict, List, Optional
from sdw_agent.service.dscs_codecheck.models import GitCommitChange
from sdw_agent.service.dscs_codecheck.util.llm_util import requests_llm_change
import openpyxl
from openpyxl.styles import Font, colors
import os
from sdw_agent.config.env import ENV

def run_static_analysis(code_changes:List[GitCommitChange],config:Dict)->Dict[str, Dict[str, List[Optional[any]]]]:
    """
    执行静态分析并返回所有检查结果
    
    Args:
        code_changes: 代码变更列表
        config: 配置字典，包含codereviewpoint检查点
        
    Returns:
        嵌套字典格式结果:
        {
            "提交人1": {
                "文件路径1": [检查结果列表],
                "文件路径2": [检查结果列表],
                ...
            },
            "提交人2": {
                ...
            }
        }
    """
    all_results = {}
    
    for change in code_changes:
        author_results = all_results.setdefault(change.author, {})
        
        for file_path, changes in change.changes.items():
            # 只处理.h/.c/.prm文件
            if not file_path.lower().endswith(('.h', '.c', '.prm')):
                continue
                
            # 初始化该文件的检查结果列表
            file_results = [None] * len(config['codereviewpoint'])
            
            for check_name, check_content in config['codereviewpoint'].items():
                if 'todo' not in check_content:
                    # 调用LLM进行分析
                    file_results[int(check_name)-1] = requests_llm_change(
                        changes['code_after'],
                        changes['code_before'],
                        check_content
                    )
                    #file_results[int(check_name)-1] = {
                        #'status': '不存在 ',
                        #'reason': 'No TODO found'
                    #}
            
            # 保存该文件的分析结果
            author_results[file_path] = file_results
    
    return all_results


def generate_report(all_results: Dict[str, Dict[str, List[Optional[any]]]]) -> List[str]:
    """
    生成代码检查报告Excel文件
    
    Args:
        all_results: 静态分析结果，格式为{提交人: {文件路径: [检查结果]}}
        change_id: 变更ID，用于生成报告文件名
        
    Returns:
        生成的报告文件路径列表
    """
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_file = os.path.join(current_dir, 'doc_temp', '設計基準CS(コードチェック).xlsx')

    report_paths = []  # 改为数组存储路径
    output_data_path = os.path.normpath(ENV.config.output_data_path)
    if not os.path.exists(output_data_path):
        os.makedirs(output_data_path)

    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"模板文件不存在: {excel_file}")

    i = 0

    for author, file_results in all_results.items():
        ret_book = os.path.join(output_data_path, f"設計基準CS(コードチェック)_{i}.xlsx")
        i += 1

        workbook = openpyxl.load_workbook(excel_file)
        sheet = workbook['設計基準CS-コードチェック']
        blue_font = Font(color=colors.BLUE)

        row = 2  # 从第2行开始写入数据
        
        # 收集同一作者的所有检查结果
        all_reasons = {}
        for file_path, results in file_results.items():
            for idx, check_result in enumerate(results):
                if check_result is not None:
                    reason_text = check_result.get('reason', '')
                    status = check_result.get('status', '')
                    # 按状态分类存储原因，并保留原始顺序信息
                    if status not in all_reasons:
                        all_reasons[status] = []
                    all_reasons[status].append({
                        'order': idx,  # 检查点顺序
                        'file_idx': len(all_reasons[status]),  # 添加文件顺序索引
                        'text': f"[{os.path.basename(file_path)}] {reason_text}"
                    })

        # 写入合并后的结果
        for status, reasons in all_reasons.items():     
            if '不存在' in status:
                for reason in reasons:
                    sheet.cell(row=reason['order']+2, column=13).value = '否'
                    sheet.cell(row=reason['order']+2, column=13).font = blue_font
                    if(sheet.cell(row=reason['order']+2, column=14).value==None):
                        sheet.cell(row=reason['order']+2, column=14).value = ''
                    sheet.cell(row=reason['order']+2, column=14).value += reason['text'] + '\n'
            else:
                for reason in reasons:
                    sheet.cell(row=reason['order']+2, column=13).value = '要'
                    sheet.cell(row=reason['order']+2, column=13).font = blue_font
                    if(sheet.cell(row=reason['order']+2, column=15).value==None):
                        sheet.cell(row=reason['order']+2, column=15).value = ''                    
                    sheet.cell(row=reason['order']+2, column=15).value += reason['text'] + '\n'

        workbook.save(ret_book)
        workbook.close()
        report_paths.append(os.path.abspath(ret_book))
    
    return report_paths