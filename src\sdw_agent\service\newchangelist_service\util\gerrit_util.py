from requests.auth import HTTP<PERSON>asicAuth
from typing import Dict, List, Optional
from urllib.parse import urljoin
import json
from urllib.parse import quote

class GerritAPI:
    def __init__(self, base_url: str, username: str = '', password: str = ''):
        """
        初始化Gerrit API客户端
        :param base_url: Gerrit服务器基础URL (如: http://gerrit.example.com:8080)
        :param username: Gerrit账号用户名
        :param password: Gerrit账号密码/访问令牌
        """
        self.base_url = base_url
        self.auth = HTTPBasicAuth(username, password) if username and password else None

    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """
        发送API请求
        :param endpoint: API端点路径 (如: /a/changes/)
        :param params: 查询参数
        """
        import requests
        url = urljoin(self.base_url, endpoint)
        headers = {'Accept': 'application/json'}
        response = requests.get(url, auth=self.auth, headers=headers, params=params)
        response.raise_for_status()
        # Gerrit API返回的JSON前面有)]}'前缀需要去除
        text = response.text.lstrip(")]}'")
        files_data = json.loads(text)
        return files_data

    def get_change_by_commit(self, commit_hash: str) -> Optional[Dict]:
        """
        通过commit hash获取变更信息
        :param commit_hash: Git提交哈希
        :return: 变更信息字典或None
        """
        params = {
            'q': f'commit:{commit_hash}',
            'o': ['CURRENT_REVISION', 'ALL_COMMITS', 'DETAILED_ACCOUNTS']
        }
        changes = self._make_request('/a/changes/', params)
        return changes[0] if changes else None

    def get_change_details(self, change_id: str) -> Dict:
        """
        获取变更详细信息
        :param change_id: Gerrit变更ID
        :return: 变更详细信息字典
        """
        return self._make_request(f'/a/changes/{change_id}/detail')

    def get_file_diff(self, change_id: str, revision: str, file_path: str) -> str:
        """
        获取文件差异内容
        :param change_id: Gerrit变更ID
        :param revision: 修订版本号
        :param file_path: 文件路径
        :return: diff文本
        """
        endpoint = f'/a/changes/{change_id}/revisions/{revision}/files/{file_path}/diff'
        return self._make_request(endpoint).get('content', '')

    def get_diff_code(self, change_id: str, revision: str) -> Dict:
        """
        获取变更中所有文件的差异代码内容
        :param change_id: Gerrit变更ID
        :param revision: 修订版本号
        :return: 文件信息字典，格式为:
            {
                "file_name": {
                    "path": "完整文件路径",
                    "code_all": [所有代码行],
                    "code_after": [修改后代码行],
                    "code_before": [修改前代码行]
                },
                ...
            }
        """
        endpoint = f"/a/changes/{change_id}/revisions/{revision}/files/"
        files_data = self._make_request(endpoint)
        
        file_info_dict = {}
        
        for file_path, file_meta in files_data.items():
            try:
                file_name = file_path.split('/')[-1]
            except:
                file_name = file_path
                
            file_info_dict[file_name] = {
                'path': file_path,
                'code_all': [],
                'code_after': [],
                'code_before': []
            }
            encoded_file_path = quote(file_path, safe="")
            # 获取文件差异内容
            diff_data = self.get_file_diff(change_id, revision, encoded_file_path)
            
            for item in diff_data:
                if 'b' in item:  # 修改后的代码
                    file_info_dict[file_name]['code_after'].extend(
                        self._clean_code_lines(item['b'])
                    )
                    file_info_dict[file_name]['code_all'].extend(
                        self._clean_code_lines(item['b'])
                    )
                elif 'ab' in item:  # 所有代码
                    file_info_dict[file_name]['code_all'].extend(
                        self._clean_code_lines(item['ab'])
                    )
                elif 'a' in item:  # 修改前的代码
                    file_info_dict[file_name]['code_before'].extend(
                        self._clean_code_lines(item['a'])
                    )
                    
        return file_info_dict

    def _clean_code_lines(self, lines: List[str]) -> List[str]:
        """清理代码行，去除特殊字符和格式"""
        cleaned = []
        for line in lines:
            line = line.rstrip(',')  # 去除末尾逗号
            if line.startswith("'") and line.endswith("'"):
                line = line[1:-1]  # 去除首尾单引号
            cleaned.append(line)
        return cleaned
