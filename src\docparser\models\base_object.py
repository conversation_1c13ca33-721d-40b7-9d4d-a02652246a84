import json
import pickle


class BaseObject:
    """
    基类
    """

    def __repr__(self):
        return f'{self.__class__.__name__}(){json.loads(self.to_json())}'

    def __str__(self):
        if hasattr(self, '_text'):
            return self._text
        return f'{json.loads(self.to_json())}'

    @staticmethod
    def __default(o):
        """
       json序列化通用函数
       :return:
       """
        if hasattr(o, '__dict__'):
            return {k: ('' if k.endswith('_ref') else v) for k, v in o.__dict__.items()}
        else:
            return ''

    def to_json(self, _sort_keys=True, indent_=4):
        """
        json序列化通用函数
        :return:
        """
        return json.dumps(
            self,
            default=self.__default,
            sort_keys=_sort_keys,
            indent=indent_,
            ensure_ascii=False)

    def to_dict(self):
        return json.loads(self.to_json())

    def serialize_to_file(self, save_path):
        with open(save_path, 'wb') as fp:
            pickle.dump(self, fp)

    @classmethod
    def deserialize_from_file(cls, load_path):
        with open(load_path, 'rb') as fp:
            return pickle.load(fp)

    def save_to_json(self, output_path: str) -> None:
        """Save document to JSON file"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(self.to_json())

