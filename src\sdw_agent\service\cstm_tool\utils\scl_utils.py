"""
CSTM 配置工具工作流

V字对应：
2.1 基本設計 ソフトウェア設計書作成（I/F含む） cstm 配置工具自动化生成

模块简介和主要功能说明：
用户从要件一览表中指定待分析的变更点，自动匹配相关SCL文件中与变更点相关的内容，解析变更前后CSTM原始式样书
从式样书中定位到与变更点相关的选项信息，通过对比变更前后式样书内容，得到CSTM 变更类型，根据变更内容自动更新CSTM配置工具文件
（目前支持：选项属性变更，选项位置变更，选项新增，选项删除，画面追加，画面消除场景）

主要功能：
1. 解析SCL 文件，并从中匹配出与变更点相关的信息
2. 解析原始式样书得到结构化json
"""


import os
import re
import traceback
from typing import Dict, Tuple, Any, List

import win32com.client as win32
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import  logger

from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.cstm_tool.model import ChangeSummaryFormat, CstmMetaInfo


class SCLUtils:
    def __init__(self, scl_path, config):
        self.scl_path = scl_path
        self.config = config
        self.logger = logger.bind(name="SclUtils")

        # 从配置中读取SCL字段配置
        scl_fields_config = self.config.get("workflow_config", {}).get("scl_fields", {})
        self.scl_fields = scl_fields_config.get("fields", [])
        self.scl_field_keys = scl_fields_config.get("field_keys", [])

        # 验证配置
        if not self.scl_fields or not self.scl_field_keys:
            raise ValueError("SCL字段配置不完整，请检查config.yaml中的scl_fields配置")

        if len(self.scl_fields) != len(self.scl_field_keys):
            raise ValueError("SCL字段和字段键的数量不匹配，请检查config.yaml中的scl_fields配置")

    def extract_info_from_scl(self, change_summary:str):
        '''
            根据概要从SCL中解析出变更前后的信息
            :param change_summary:
            :param scl_path:
            :return:
            '''
        # 根据变更概要信息从scl中获取变更信息
        fault_modes = self.get_scl_content(change_summary, self.scl_path)
        # 变更前后的单元格坐标
        change_cell = [[i.get('change_before_cell'), i.get('change_after_cell')] for i in fault_modes]
        # 变更前后式样书名称
        req_name = [[i.get('change_before_no'), i.get('change_after_no')] for i in fault_modes]
        # 变更前后的值
        value = [[i.get('change_content_before'), i.get('change_content_after')] for i in fault_modes]
        # 变更所在阶层
        changed_cstm_level = [i.get('x_label') for i in fault_modes]
        # 变更所在列名
        changed_col_name = [i.get('x_secondary_label') for i in fault_modes]
        # 差分种别
        change_type = [i.get('change_type') for i in fault_modes]

        self.logger.info(f"从SCL匹配的变更前后单元格坐标为：{change_cell}")

        return {
            "cell": change_cell,
            "req_name": req_name,
            "value": value,
            "changed_level": changed_cstm_level,
            "changed_col_name": changed_col_name,
            "change_type": change_type
        }

    def get_scl_content(self, change_summary: str, scl_excel_path: str):
        """
        根据变更摘要获取SCL内容。

        参数:
        change_summary (str): 变更摘要，用于在Excel中查找匹配的行。
        scl_excel_path (str): SCL Excel文件的路径。

        返回:
        list: 包含故障模式信息的列表。
        """
        # 检查Excel文件是否存在
        if not os.path.exists(self.scl_path):
            raise FileNotFoundError(
                f"Excel文件不存在：{self.scl_path}"
            )

        # 打开Excel文件
        try:
            excel = win32.Dispatch("Excel.Application")
            wb = excel.Workbooks.Open(os.path.abspath(scl_excel_path))
        except Exception as e:
            traceback.print_exc()
            raise Exception(
                f"打开SCL Excel文件失败，请确保已经正常关闭SCL文件: {str(e)}"
            )

        try:
            sheet_names = [sheet.Name for sheet in wb.Sheets]
            change_content_data = {}

            # 收集符合条件的工作表数据
            for sname in sheet_names:
                if re.fullmatch(r"比較結果_\w+", sname):
                    change_content_data[sname] = self._get_change_content_data(wb, sname)

            # 合并所有值并去重
            all_items = []
            for items in change_content_data.values():
                all_items.extend(items)
            change_mode_list = list(set(all_items))
            change_mode = {i: change_mode_list[i] for i in range(len(change_mode_list))}
            match_summary = self._ai_choose_change_mode(change_mode, change_summary)

            # 查找匹配的工作表
            matched_sheet = None
            for sheet_name, values in change_content_data.items():
                if match_summary in values:
                    matched_sheet = sheet_name
                    break

            if matched_sheet is None:
                raise KeyError(
                    f"未找到包含摘要的工作表:{match_summary}"
                )

            return self._get_sheet_info(wb, matched_sheet, match_summary)


        finally:
            # 关闭Excel文件和应用程序，清理COM对象
            wb.Close(False)
            excel.Quit()
            del wb
            del excel


    def _get_change_content_data(self, wb, sheet_name):
        """
        从给定的工作簿和工作表中提取变更内容数据。

        参数:
        wb: 工作簿对象，包含多个工作表。
        sheet_name: 字符串，指定要提取数据的工作表名称。

        返回:
        一个列表，包含指定工作表中的所有变更内容数据。
        """
        # 通过工作簿对象和工作表名称获取工作表对象
        ws = wb.Sheets(sheet_name)

        # 获取表头（第4行）
        header_row = ws.Rows(4)
        # 从表头行的每个单元格中提取值，构建表头列表
        headers = []
        for cell in header_row.Cells:
            if cell.Value is not None:  # 检查单元格是否有值
                headers.append(cell.Value)
            else:
                # 如果没有值表示遍历到表格最大列索引处
                break

        # 构建字段到列索引的映射，以便快速查找列位置
        header_map = {h: i for i, h in enumerate(headers)}  # 构建字段到列索引的映射
        # 获取工作表中已使用的行数
        used_rows = ws.UsedRange.Rows.Count

        # 提取并打印变更内容数据
        # 根据"変更内容"字段在表头的位置，提取对应列的数据
        return [ws.Cells(row, header_map.get("変更内容", -1) + 1).Value for row in range(5, used_rows + 1)]


    def _ai_choose_change_mode(self, change_mode, change_summary):
        """
        选择最匹配的变更模式

        本函数旨在从变更模式列表中选出与变更摘要最匹配的模式如果直接找到匹配项，则返回该摘要；
        否则，尝试通过AI模型生成一个匹配的变更摘要如果多次尝试均失败，则返回原始变更摘要

        参数:
        change_mode (dict): 可用的变更模式
        change_summary (str): 当前的变更摘要

        返回:
        str: 最匹配的变更模式摘要
        """
        # 检查变更摘要是否直接存在于变更模式列表中
        if change_summary in change_mode.values():
            return change_summary
        # 创建一个聊天提示模板，用于生成变更摘要
        prompt_config = self.config.get("prompt", {})
        change_summary_prompt = prompt_config.get("scl_extract",{}).get("change_summary_prompt", None)
        if change_summary_prompt is None:
            raise ValueError(
                "请检查配置文件，未找到从SCL匹配正确变更信息提示模板"
            )
        template = ChatPromptTemplate(
            [
                ("user", change_summary_prompt)
            ],
            template_format="mustache",
        )
        invoke_data = {
            "change_mode_list": change_mode,
            "change_summary": change_summary
        }
        try:
            resp: AIMessage = get_ai_message_with_structured_output(
                template,
                invoke_data,
                ChangeSummaryFormat,
                llm_model=None
            )
            return change_mode[resp.change_content_no]
        except:
            self.logger.error(f"无法通过AI模型生成匹配的变更摘要")
        # 如果多次尝试均失败，则返回原始变更摘要
        return change_summary

    def _get_sheet_info(self, wb, sheet_name, change_summary):
        """
        提取给定工作表中与变更摘要匹配的故障模式信息。

        参数:
        wb: Excel工作簿对象，包含多个工作表。
        sheet_name: 字符串，指定要提取信息的工作表名称。
        change_summary: 字符串，用于筛选行的变更摘要。

        返回:
        一个列表，包含与变更摘要匹配的所有故障模式信息的字典。
        """
        # 通过名称获取指定的工作表
        ws = wb.Sheets(sheet_name)
        # 获取表头（第4行）
        header_row = ws.Rows(4)
        # 提取表头中的字段名，创建字段到列索引的映射
        headers = []
        for cell in header_row.Cells:
            if cell.Value is not None:  # 检查单元格是否有值
                headers.append(cell.Value)
            else:
                # 如果没有值表示遍历到表格最大列索引处
                break

        header_map = {h: i for i, h in enumerate(headers)}  # 构建字段到列索引的映射

        # 初始化故障模式列表和用于记录有效行数的变量
        fault_modes = []
        used_rows = ws.UsedRange.Rows.Count

        # 遍历工作表的每一行，寻找与变更摘要匹配的行
        for row in range(5, used_rows + 1):
            # 获取当前行的变更内容，并与给定的变更摘要比较
            change_content = ws.Cells(row, header_map.get("変更内容", -1) + 1).Value
            if change_content != change_summary:
                continue

            # 初始化字段值列表，用于存储当前行的字段值
            field_values = []
            # 处理每个字段，根据字段名获取对应的单元格值
            for field in self.scl_fields:
                if field == "変更後":
                    # 对于"変更後"字段，提取格式化后的单元格值
                    cell = ws.Cells(row, header_map[field] + 1)
                    format_value = self._extract_format_value(cell)
                    field_values.append(format_value)
                else:
                    # 对于其他字段，直接获取单元格值
                    try:
                        cell_value = ws.Cells(row, header_map.get(field, -1) + 1).Value
                    except Exception as e:
                        self.logger.error(f"获取{field}列单元格值时发生错误: {e}")
                        cell_value = None
                    # cell_value = ws.Cells(row, header_map.get(field, -1) + 1).Value
                    field_values.append(cell_value)

            # 将字段值与对应的键组合成字典，并添加到故障模式列表中
            fault_mode_info = dict(zip(self.scl_field_keys, field_values))
            fault_modes.append(fault_mode_info)

        # 返回包含所有匹配故障模式信息的列表
        return fault_modes

    def _extract_format_value(self, cell):
        """
        从单元格中提取未被删除线格式化的字符。

        遍历单元格中的每个字符，检查其字体是否设置了删除线格式。如果没有设置删除线，
        则将该字符添加到结果字符串中。此函数用于处理那些可能被部分格式化为删除线的字符串，
        以便后续处理或显示。

        参数:
        cell - 单元格对象，包含要提取的文本值和格式信息。

        返回:
        format_value - 不包含删除线格式字符的字符串。
        """
        # 获取单元格的值
        value = cell.Value
        # 如果值为空，则返回空字符串
        if value is None:
            return ""
        # 将单元格的值转换为字符串
        value_str = str(value)
        # 初始化用于存储非删除线字符的字符串
        format_value = ""
        # 遍历字符串中的每个字符
        for i in range(1, len(value_str) + 1):
            # 获取当前字符的字体属性
            char_font = cell.GetCharacters(i, 1).Font
            # 如果当前字符的字体没有设置删除线格式，则将其添加到结果字符串中
            if not char_font.Strikethrough:
                format_value += value_str[i - 1]
        # 返回结果字符串
        return format_value

    def validate_cstm_spec(self, scl_info, before_cstm, after_cstm):
        '''
        验证用户上传的原始式样书版本是否与scl中的一致
        :param scl_info: SCL信息字典，包含req_name字段
        :param before_cstm: 变更前式样书文件路径
        :param after_cstm: 变更后式样书文件路径
        :return: tuple(验证结果: bool, scl中变更前文件名: str|None, scl中变更后文件名: str|None)
        '''
        try:
            # 输入验证
            if not all([before_cstm, after_cstm]):
                self.logger.warning("before_cstm或after_cstm参数为空")
                return False, None, None

            if not isinstance(before_cstm, str) or not isinstance(after_cstm, str):
                self.logger.error("before_cstm和after_cstm必须为字符串类型")
                return False, None, None

            # 从before_cstm 和 after_cstm中提取出文件名
            before_cstm_name = os.path.basename(before_cstm.strip())
            after_cstm_name = os.path.basename(after_cstm.strip())

            # 验证提取的文件名不为空
            if not before_cstm_name or not after_cstm_name:
                self.logger.error("无法从文件路径中提取有效的文件名")
                return False, None, None

            # 验证scl_info并提取文件名
            if not scl_info or not isinstance(scl_info, dict):
                self.logger.warning("scl_info为空或格式不正确")
                return False, None, None

            req_name = scl_info.get('req_name')
            if not req_name or not isinstance(req_name, list) or len(req_name) == 0:
                self.logger.warning("scl_info中缺少有效的req_name字段")
                return False, None, None

            # 验证req_name的第一个元素结构
            first_req = req_name[0]
            if not isinstance(first_req, (list, tuple)) or len(first_req) < 2:
                self.logger.error("scl_info中req_name格式不正确，期望包含至少2个元素的列表")
                return False, None, None

            # 安全提取SCL中的文件名
            before_cstm_name_in_scl = str(first_req[0]).strip() if first_req[0] else ""
            after_cstm_name_in_scl = str(first_req[1]).strip() if first_req[1] else ""

            # 验证SCL中的文件名不为空
            if not before_cstm_name_in_scl or not after_cstm_name_in_scl:
                self.logger.warning("SCL中的文件名为空")
                return False, before_cstm_name_in_scl or None, after_cstm_name_in_scl or None

            # 比较文件名
            is_match = (before_cstm_name == before_cstm_name_in_scl and
                        after_cstm_name == after_cstm_name_in_scl)

            if is_match:
                self.logger.info(f"文件名验证成功: {before_cstm_name} -> {after_cstm_name}")
            else:
                self.logger.warning(f"文件名不匹配 - 上传: {before_cstm_name} -> {after_cstm_name}, "
                               f"SCL: {before_cstm_name_in_scl} -> {after_cstm_name_in_scl}")

            return is_match, before_cstm_name_in_scl, after_cstm_name_in_scl

        except Exception as e:
            self.logger.error(f"验证CSTM式样书时发生异常: {str(e)}")
            return False, None, None
