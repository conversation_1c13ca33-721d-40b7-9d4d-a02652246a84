
select_checkitems_prompt = """\
你是一名汽车软件电装项目的测试人员，熟悉汽车软件测试的每个功能模块。现在需要你根据<input>中的功能变更信息，从<candidate>中选择与功能变更信息直接相关的检查项条目。

<input>
{input}
</input>

<candidate>
{checkitems}
</candidate>

**说明**
1.<input>中的内容是对某一个功能变更点的描述。
2.<candidate>中的内容是一些候选检查项。

**要求**
你需要对功能变更点进行分析，然后找到与功能变更点直接相关的检查项，返回一个json格式的列表，列表中的每个元素是一个字典对象，包括“编号”、“是否相关”、“原因”三个字段，其中“原因”字段的内容用一句话描述，尽可能简短，不超过20个字。列表中的元素个数应该与<candidate>中的检查项条目数相同。请保证最终的返回结果能够被json.loads函数解析。

**返回示例**
# 假设<candidate>中有20个检查项条目
[{{"编号": "0", "是否相关": "是", "原因": "......"}}, {{"编号": "1", "是否相关": "是", "原因": "......"}}, {{"编号": "2", "是否相关": "否", "原因": "......"}}, ..., , {{"编号": "19", "是否相关": "是", "原因": "......"}}]

# 假设<candidate>中有5个检查项条目
[{{"编号": "0", "是否相关": "否", "原因": "......"}}, {{"编号": "1", "是否相关": "否", "原因": "......"}}, {{"编号": "2", "是否相关": "是", "原因": "......"}}, ..., , {{"编号": "4", "是否相关": "否", "原因": "......"}}]
"""


select_checkitems_prompt_ICL = """\
你是一名汽车软件电装项目的测试人员，熟悉汽车软件测试的每个功能模块。现在需要你根据输入中的功能变更信息，从检查项候选集中选择与功能变更信息直接相关的检查项条目。

**检查项候选集**
{checkitems}

**要求**
你需要对功能变更点进行分析，然后找到与功能变更点直接相关的检查项，返回一个json格式的列表，列表中的每个元素是一个字典对象，包括“编号”、“是否相关”、“原因”三个字段，其中“原因”字段的内容用一句话描述，尽可能简短，不超过20个字。列表中的元素个数应该与<candidate>中的检查项条目数相同。请保证最终的返回结果能够被json.loads函数解析。

**示例**
{examples}

现在，请参考上面的示例，分析输入中的内容，并补全输出内容。
输入：{input}
输出："""


select_checkitems_prompt_rule = """\
你是一名汽车软件电装项目的测试人员，熟悉汽车软件测试的每个功能模块。现在的任务是对需求中的变更点进行分类。

**说明**
1.输入是变更点描述，输出是分类结果。
2.变更点由一段话描述，这段话可能与某个类别的提示中关键字有关。
3.某一些变更点类别描述后会附加提示，这些提示中的关键字可能出现在变更点描述中。

**变更点类别**
{changepoint_category}

**返回格式**
最终的输出结果必须是json格式的文本，保证可以被json.loads函数解析，例如{{"思考": "...", "变更点类别": "..."}}

**示例**
输入：MET-X_PSPWR-SYS_Volt R_第1階層⇒第2階層へ転送 100V電源,200V電源,400V電源
输出：{{"思考": "变更点中提及由第1阶层变更到第2阶层，符合“Option/Content追加”类别的提示“第XXX階層⇒第XXX階層へ移動”，因此变更点类别是“Option/Content追加”。", "变更点类别": "Option/Content追加"}}

输入：MET-G_CSTMLST-CSTD_SoC R_「データ処理強化」を追加
输出：{{"思考": "变更点的内容是在某个组件或模块中添加“数据处理增强”功能，符合“Option/Content追加”类别的提示“階層の項目変更”，因此变更点类别是“Option/Content追加”。", "变更点类别": "Option/Content追加"}}

输入：MET-G_CSTMLST-CSTD_SoC B_表示設定/インフォメーション設定/電力使用量グラフの補足説明変更
输出：{{"思考": "变更点的内容是“显示设定/信息设定/电力使用量图表”其中一个的补充说明发生了变更，因此其类别可以归为“特别注释”。", "变更点类别": "特别注释"}}

现在，请参考上面的示例，分析输入中的内容，并补全输出内容。
输入：{input}
输出："""
