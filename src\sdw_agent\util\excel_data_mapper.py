"""
Excel数据映射和写入工具
结合多级表头解析和pywin32写入，避免数据丢失
"""

import os
import traceback
from typing import Dict, List

import pandas as pd
import win32com.client as win32

from sdw_agent.util.excel.multi_level_header_extractor import MultiLevelHeaderExtractor


class ExcelDataMapper:
    """Excel数据映射和写入工具"""

    def __init__(self, target_file_path: str, target_sheet_name: str):
        """
        初始化
        
        Args:
            target_file_path: 目标Excel文件路径
            target_sheet_name: 目标工作表名称
        """
        self.target_file_path = os.path.abspath(target_file_path)
        self.target_sheet_name = target_sheet_name
        self.extractor = MultiLevelHeaderExtractor(target_file_path, target_sheet_name)
        self.column_mapping = {}  # 列名到列号的映射
        self.excel_app = None
        self.workbook = None
        self.worksheet = None
        self.header_end_row = None

    def analyze_target_structure(self, header_start_row: int, header_end_row: int,
                                 start_col=1, end_col=None):
        """
        分析目标表的结构

        Args:
            header_start_row: 表头开始行
            header_end_row: 表头结束行
            start_col: 开始列（支持数字如1或字母如'A'）
            end_col: 结束列（支持数字如10或字母如'J'）

        Returns:
            表头信息和列映射
        """
        print("=== 分析目标表结构 ===")

        # 解析列输入，支持字母和数字
        start_col_num = self._parse_column_input(start_col)
        end_col_num = self._parse_column_input(end_col) if end_col is not None else None
        self.header_end_row = header_end_row

        print(
            f"列范围: {start_col}({start_col_num}) 到 {end_col}({end_col_num})" if end_col else f"起始列: {start_col}({start_col_num})")

        # 提取表头信息
        header_info = self.extractor.extract_headers(
            start_row=header_start_row,
            end_row=header_end_row,
            start_col=start_col_num,
            end_col=end_col_num
        )

        # 生成列映射（列名 -> Excel列号）
        self.column_mapping = {}
        for i, col_name in enumerate(header_info['flat_columns']):
            excel_col_num = start_col_num + i  # Excel列号（1-based）
            excel_col_letter = self._number_to_column_letter(excel_col_num)
            self.column_mapping[col_name] = {
                'col_num': excel_col_num,
                'col_letter': excel_col_letter,
                'index': i
            }

        print(f"发现 {len(self.column_mapping)} 个列:")
        for col_name, info in list(self.column_mapping.items())[:999]:  # 只显示前10个
            print(f"  '{col_name}' -> 列{info['col_letter']}({info['col_num']})")

        # 保存信息供print_column_details使用
        self._last_header_info = header_info
        self._last_start_col = start_col_num

        return header_info, self.column_mapping

    def _number_to_column_letter(self, col_num: int) -> str:
        """将列号转换为Excel列字母"""
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def _column_letter_to_number(self, col_letter: str) -> int:
        """将Excel列字母转换为列号"""
        col_letter = col_letter.upper()
        result = 0
        for char in col_letter:
            result = result * 26 + (ord(char) - ord('A') + 1)
        return result

    def _parse_column_input(self, col_input) -> int:
        """解析列输入，支持数字或字母"""
        if isinstance(col_input, int):
            return col_input
        elif isinstance(col_input, str):
            # 如果是字符串，尝试转换为字母列号
            if col_input.isalpha():
                return self._column_letter_to_number(col_input)
            else:
                # 如果是数字字符串，转换为整数
                return int(col_input)
        else:
            raise ValueError(f"不支持的列输入类型: {type(col_input)}, 值: {col_input}")

    def create_field_mapping_rules(self, mapping_rules: Dict[str, str]) -> Dict[str, Dict]:
        """
        创建字段映射规则
        
        Args:
            mapping_rules: 源字段名 -> 目标列名的映射
            
        Returns:
            完整的映射规则（包含列位置信息）
        """
        print("\n=== 创建字段映射规则 ===")

        complete_mapping = {}
        for source_field, target_column in mapping_rules.items():
            if target_column in self.column_mapping:
                complete_mapping[source_field] = {
                    'target_column': target_column,
                    'col_num': self.column_mapping[target_column]['col_num'],
                    'col_letter': self.column_mapping[target_column]['col_letter']
                }
                print(f"  {source_field} -> {target_column} (列{self.column_mapping[target_column]['col_letter']})")
            else:
                print(f"  ⚠️  警告: 目标列 '{target_column}' 不存在")

        return complete_mapping

    def read_source_data(self, source_file_path: str, source_sheet_name: str = None,
                         source_config: Dict = None) -> pd.DataFrame:
        """
        读取源数据
        
        Args:
            source_file_path: 源文件路径
            source_sheet_name: 源工作表名称
            source_config: 源数据配置（如skiprows, header等）
            
        Returns:
            源数据DataFrame
        """
        print(f"\n=== 读取源数据: {source_file_path} ===")

        config = source_config or {}

        df = pd.read_excel(
            source_file_path,
            sheet_name=source_sheet_name,
            **config
        )

        # 检查返回值类型，确保是DataFrame
        if isinstance(df, dict):
            # 如果返回的是字典（多个sheet），取第一个sheet
            df = list(df.values())[0]

        if not isinstance(df, pd.DataFrame):
            raise TypeError(f"读取的数据不是DataFrame类型，而是: {type(df)}")

        print(f"读取到 {df.shape[0]} 行, {df.shape[1]} 列数据")
        print("源数据列名:", list(df.columns))

        return df

    def open_excel_with_pywin32(self):
        """使用pywin32打开Excel"""
        print("\n=== 使用pywin32打开Excel ===")

        try:
            # 初始化COM组件
            import pythoncom
            pythoncom.CoInitialize()

            self.excel_app = win32.Dispatch("Excel.Application")
            self.excel_app.Visible = False  # 不显示Excel界面
            self.excel_app.DisplayAlerts = False  # 不显示警告

            self.workbook = self.excel_app.Workbooks.Open(self.target_file_path)
            self.worksheet = self.workbook.Worksheets(self.target_sheet_name)

            print(f"成功打开: {self.target_file_path}")
            print(f"工作表: {self.target_sheet_name}")

        except Exception as e:
            print(f"打开Excel失败: {e}")
            # 如果打开失败，也要清理COM组件
            try:
                import pythoncom
                pythoncom.CoUninitialize()
            except:
                pass
            raise

    def write_data_to_excel_all_cover(self, source_data: pd.DataFrame, field_mapping: Dict[str, Dict],
                                      auto_wrap_text: bool = True):
        """
        将数据写入Excel

        Args:
            source_data: 源数据
            field_mapping: 字段映射规则
            auto_wrap_text: 是否自动换行，默认为True
        """
        data_start_row = self.header_end_row + 1
        print(f"\n=== 写入数据到Excel (从第{data_start_row}行开始) ===")

        if self.worksheet is None:
            raise Exception("Excel工作表未打开，请先调用open_excel_with_pywin32()")

        # 写入数据
        write_count = 0
        update_count = 0

        for index, row in source_data.iterrows():
            # 新增行
            target_row = data_start_row + write_count
            write_count += 1

            # 写入每个字段
            for source_field, mapping_info in field_mapping.items():
                if source_field in row:
                    value = row[source_field]
                    if pd.notna(value):  # 只写入非空值
                        cell_address = f"{mapping_info['col_letter']}{target_row}"
                        cell_range = self.worksheet.Range(cell_address)
                        cell_range.Value = value
                        # 根据参数设置自动换行
                        if auto_wrap_text:
                            cell_range.WrapText = True

            if (index + 1) % 100 == 0:
                print(f"  已处理 {index + 1} 行数据...")

        print(f"数据写入完成: 新增 {write_count} 行, 更新 {update_count} 行")

    def write_data_to_excel(self, source_data: pd.DataFrame, field_mapping: Dict[str, Dict],
                            data_start_row: int, key_column: str = None, auto_wrap_text: bool = True):
        """
        将数据写入Excel

        Args:
            source_data: 源数据
            field_mapping: 字段映射规则
            data_start_row: 数据开始行
            key_column: 用于匹配的关键列（如果需要更新现有数据）
            auto_wrap_text: 是否自动换行，默认为True
        """
        print(f"\n=== 写入数据到Excel (从第{data_start_row}行开始) ===")

        if self.worksheet is None:
            raise Exception("Excel工作表未打开，请先调用open_excel_with_pywin32()")

        # 如果指定了关键列，先读取现有数据进行匹配
        existing_data = {}
        if key_column and key_column in field_mapping:
            key_col_letter = field_mapping[key_column]['col_letter']
            print(f"读取现有数据，关键列: {key_col_letter}")

            # 读取现有的关键列数据
            last_row = self.worksheet.Cells(self.worksheet.Rows.Count, field_mapping[key_column]['col_num']).End(
                -4162).Row
            if last_row >= data_start_row:
                key_range = self.worksheet.Range(f"{key_col_letter}{data_start_row}:{key_col_letter}{last_row}")
                existing_keys = [cell.Value for cell in key_range]
                for i, key in enumerate(existing_keys):
                    if key:
                        existing_data[str(key)] = data_start_row + i

        # 写入数据
        write_count = 0
        update_count = 0

        for index, row in source_data.iterrows():
            # 确定写入行号
            if key_column and key_column in row and str(row[key_column]) in existing_data:
                # 更新现有行
                target_row = existing_data[str(row[key_column])]
                update_count += 1
            else:
                # 新增行
                target_row = data_start_row + write_count
                write_count += 1

            # 写入每个字段
            for source_field, mapping_info in field_mapping.items():
                if source_field in row:
                    value = row[source_field]
                    if pd.notna(value):  # 只写入非空值
                        cell_address = f"{mapping_info['col_letter']}{target_row}"
                        cell_range = self.worksheet.Range(cell_address)
                        cell_range.Value = value
                        # 根据参数设置自动换行
                        if auto_wrap_text:
                            cell_range.WrapText = True

            if (index + 1) % 100 == 0:
                print(f"  已处理 {index + 1} 行数据...")

        print(f"数据写入完成: 新增 {write_count} 行, 更新 {update_count} 行")

    def save_and_close_excel(self):
        """保存并关闭Excel"""
        print("\n=== 保存并关闭Excel ===")

        try:
            if self.workbook is not None:
                self.workbook.Save()
                self.workbook.Close()
                print("Excel文件已保存")

            if self.excel_app is not None:
                self.excel_app.Quit()
                print("Excel应用已关闭")

        except Exception as e:
            traceback.print_exc()
            print(f"关闭Excel时出错: {e}")
        finally:
            self.excel_app = None
            self.workbook = None
            self.worksheet = None

            # 清理COM组件
            try:
                import pythoncom
                pythoncom.CoUninitialize()
                print("COM组件已清理")
            except Exception as e:
                print(f"清理COM组件时出错: {e}")

    def find_target_columns_by_keywords(self, keywords: List[str]) -> Dict[str, str]:
        """
        根据关键词查找目标列
        
        Args:
            keywords: 关键词列表
            
        Returns:
            关键词到列名的映射
        """
        print(f"\n=== 根据关键词查找列: {keywords} ===")

        result = {}
        for keyword in keywords:
            matches = []
            for col_name in self.column_mapping.keys():
                if keyword in col_name:
                    matches.append(col_name)

            if matches:
                result[keyword] = matches[0]  # 取第一个匹配
                print(f"  '{keyword}' -> '{matches[0]}'")
                if len(matches) > 1:
                    print(f"    (还有其他匹配: {matches[1:]})")
            else:
                print(f"  ⚠️  未找到包含 '{keyword}' 的列")

        return result

    def print_column_details(self, show_hierarchy: bool = True, show_raw_data: bool = False):
        """
        打印详细的列信息，方便用户查看和编写映射规则

        Args:
            show_hierarchy: 是否显示层次结构详情
            show_raw_data: 是否显示原始表头数据
        """
        if not hasattr(self, '_last_header_info') or not self._last_header_info:
            print("❌ 请先调用 analyze_target_structure() 方法")
            return

        header_info = self._last_header_info

        print("=" * 80)
        print("📋 详细列信息 - 用于编写映射规则")
        print("=" * 80)

        # 1. 显示所有可用列名（用于复制到映射规则中）
        print(f"\n🎯 所有可用列名 (共 {len(self.column_mapping)} 个):")
        print("   (可直接复制到映射规则中)")
        for i, (col_name, info) in enumerate(self.column_mapping.items(), 1):
            print(f"   {i:2d}. '{col_name}' -> 列{info['col_letter']}")

        # 2. 显示层次结构详情
        if show_hierarchy:
            print(f"\n🔍 层次结构详情:")
            for i, col_info in enumerate(header_info['hierarchical']):
                col_name = header_info['flat_columns'][i]
                hierarchy = col_info['hierarchy']

                if hierarchy:  # 只显示有内容的列
                    excel_col = chr(ord('A') + col_info['column_index'] + (self._last_start_col - 1))
                    print(f"\n   列{excel_col}: {col_name}")
                    print(f"      层次: {' -> '.join(hierarchy)}")
                    print(f"      级数: {len(hierarchy)}")

        # 3. 显示原始数据（可选）
        if show_raw_data:
            print(f"\n📊 原始表头数据:")
            for i, row in enumerate(header_info['raw_data']):
                print(f"   第{header_info['visible_rows'][i]}行: {row}")

            print(f"\n📊 处理后数据:")
            for i, row in enumerate(header_info['merged_data']):
                print(f"   第{header_info['visible_rows'][i]}行: {row}")

        # 4. 提供映射模板
        print(f"\n📝 映射规则模板:")
        print("   mapping_rules = {")
        for i, col_name in enumerate(list(self.column_mapping.keys())[:5]):  # 只显示前5个作为示例
            print(f"       '你的源字段{i + 1}': '{col_name}',")
        if len(self.column_mapping) > 5:
            print(f"       # ... 还有 {len(self.column_mapping) - 5} 个列")
        print("   }")

        print("\n💡 使用提示:")
        print("   1. 复制上面的列名到你的映射规则中")
        print("   2. 注意列名要完全匹配（包括空格和特殊字符）")
        print("   3. 使用 ' | ' 分隔的完整层次名称")
        print("=" * 80)

    def process_data_mapping(self, source_file_path: str, mapping_rules: Dict[str, str],
                             data_start_row: int, source_config: Dict = None, key_column: str = None):
        """
        完整的数据映射流程（使用文件路径）

        Args:
            source_file_path: 源文件路径
            mapping_rules: 映射规则
            data_start_row: 数据开始行
            source_config: 源数据读取配置
            key_column: 关键列（用于更新）

        Note:
            需要先调用 analyze_target_structure() 方法分析目标表结构
        """
        try:
            # 1. 检查是否已经分析过目标表结构
            if not self.column_mapping:
                raise ValueError("请先调用 analyze_target_structure() 方法分析目标表结构")

            # 2. 创建字段映射
            field_mapping = self.create_field_mapping_rules(mapping_rules)

            # 3. 读取源数据
            source_data = self.read_source_data(source_file_path, source_sheet_name=None, source_config=source_config)

            # 4. 打开Excel
            self.open_excel_with_pywin32()

            # 5. 写入数据
            self.write_data_to_excel(source_data, field_mapping, data_start_row, key_column)

            # 6. 保存关闭
            self.save_and_close_excel()

            print("\n✅ 数据映射流程完成!")

        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")
            self.save_and_close_excel()  # 确保Excel被关闭
            raise

    def process_data_mapping_with_dataframe(self, source_data: pd.DataFrame, mapping_rules: Dict[str, str],
                                           data_start_row: int, key_column: str = None):
        """
        完整的数据映射流程（直接使用DataFrame）

        Args:
            source_data: 源数据DataFrame
            mapping_rules: 映射规则
            data_start_row: 数据开始行
            key_column: 关键列（用于更新）

        Note:
            需要先调用 analyze_target_structure() 方法分析目标表结构
        """
        try:
            # 1. 检查是否已经分析过目标表结构
            if not self.column_mapping:
                raise ValueError("请先调用 analyze_target_structure() 方法分析目标表结构")

            # 2. 创建字段映射
            field_mapping = self.create_field_mapping_rules(mapping_rules)

            # 3. 打开Excel
            self.open_excel_with_pywin32()

            # 4. 写入数据
            self.write_data_to_excel(source_data, field_mapping, data_start_row, key_column)

            # 5. 保存关闭
            self.save_and_close_excel()

            print("\n✅ 数据映射流程完成!")

        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")
            self.save_and_close_excel()  # 确保Excel被关闭
            raise
    def process_data_mapping_all_cover(self, source_data: pd.DataFrame, mapping_rules: Dict[str, str],
                                      auto_wrap_text: bool = True):
        """
        完整的数据映射流程（直接使用DataFrame）

        Args:
            source_data: 源数据DataFrame
            mapping_rules: 映射规则
            auto_wrap_text: 是否自动换行，默认为True

        Note:
            需要先调用 analyze_target_structure() 方法分析目标表结构
        """
        try:
            # 1. 检查是否已经分析过目标表结构
            if not self.column_mapping:
                raise ValueError("请先调用 analyze_target_structure() 方法分析目标表结构")

            # 2. 创建字段映射
            field_mapping = self.create_field_mapping_rules(mapping_rules)

            # 3. 打开Excel
            self.open_excel_with_pywin32()

            # 4. 写入数据
            self.write_data_to_excel_all_cover(source_data, field_mapping, auto_wrap_text)

            # 5. 保存关闭
            self.save_and_close_excel()

            print("\n✅ 数据映射流程完成!")

        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")
            self.save_and_close_excel()  # 确保Excel被关闭
            raise
