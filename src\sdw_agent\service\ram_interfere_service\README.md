# RAM干涉工作流

## V字对应
- 3.3.46. RAM干渉チェック実施＆結果検証

## 概述

RAM干涉工作流专门处理RAM干涉检查任务。通过分析代码中的全局变量变更，检查是否存在RAM干涉问题，并生成详细的检查报告。

## 主要功能

### 核心功能：RAM干涉检查
- **全局变量变更检测**: 根据Git差异分析全局变量的变更情况
- **中断表解析**: 解析代码中的中断表和调度任务信息
- **干涉分析**: 分析全局变量变更与中断函数的关系
- **报告生成**: 生成包含干涉分析结果的Excel报告

### 分析流程
1. **解析中断表**: 从代码中提取中断表和调度任务信息
2. **检测变量变更**: 分析Git差异，找出变更的全局变量
3. **干涉分析**: 分析变更函数与中断函数的调用关系
4. **生成报告**: 将分析结果整合到Excel模板中

## 使用方法

### 基本使用

```python
from sdw_agent.service.ram_interfere_service import RAMInterfereWorkflow
from sdw_agent.service.ram_interfere_service.models import RepositoryInfo

# 创建工作流实例
workflow = RAMInterfereWorkflow()

# 准备仓库信息
repo_info = RepositoryInfo(
    repo_path="/path/to/repository",
    commit_id="abc123def456",
    compared_commit_id="def456abc123"  # 可选
)

# 执行工作流
result = workflow.run(repo_info)

if result.status == WorkflowStatus.SUCCESS:
    # 获取输出文件路径
    output_file = result.data['output_file']
    logger.info(f"RAM干涉检查报告已生成: {output_file}")
    
    # 获取统计信息
    stats = result.data['statistics']
    logger.info(f"处理的全局变量数: {stats['total_global_vars']}")
    logger.info(f"干涉记录数: {stats['interfere_records']}")
else:
    logger.error(f"执行失败: {result.error}")
```

### 使用兼容接口

```python
from sdw_agent.service.ram_interfere_service import do_ram_interfere

# 使用旧版兼容接口
try:
    output_file = do_ram_interfere(repo_info)
    logger.info(f"报告已生成: {output_file}")
except Exception as e:
    logger.error(f"执行失败: {str(e)}")
```

## 数据模型

### RepositoryInfo
```python
class RepositoryInfo(BaseModel):
    repo_path: str              # 仓库路径
    commit_id: str              # 提交ID
    compared_commit_id: Optional[str] = None  # 对比提交ID
```

### RAMInterfereResult
```python
class RAMInterfereResult(BaseModel):
    global_var_name: str        # 全局变量名
    interrupt: Optional[str]    # 中断函数
    interrupt_addr: Optional[int]  # 中断地址
    file_path: str             # 文件路径
    change_func: Optional[str]  # 变更函数
    line: int                  # 行号
    content: str               # 代码内容
    remark: str = ""           # 备注
    result: str = ""           # 判定结果
```

## 配置说明

### 配置文件结构
```yaml
# 基本配置
name: "RAM干涉检查"
description: "分析代码中的全局变量变更和RAM干涉情况"
version: "1.0.0"

# 输入输出配置
io:
  input:
    repo_extensions: [".c", ".h", ".prm"]
  output:
    report_base_name: "RAM干涉"
    template_file: "templates/RAM干涉_模板.xlsx"

# 处理参数
processing:
  interference_check:
    scheduler_task_name: "st_gp_SCHDLR_RGLR_TASK"
  excel_output:
    default_sheet: "干渉CS（実施日DD.MM.YY）"
    data_start_row: 21
    data_start_col: 2
```

### 自定义配置
```python
# 使用自定义配置文件
workflow = RAMInterfereWorkflow(config_path="/path/to/custom_config.yaml")

# 或者在运行时修改配置
workflow.config.io.input.repo_extensions = [".c", ".h", ".cpp"]
```

## 输出格式

### Excel报告结构
生成的Excel文件包含以下列：
- **全局变量名**: 检测到的全局变量名称
- **中断函数**: 相关的中断函数名
- **中断地址**: 中断函数的地址
- **文件路径**: 变量所在的文件路径
- **变更函数**: 修改该变量的函数
- **行号**: 变更发生的行号
- **代码内容**: 具体的代码内容
- **备注**: 额外说明信息
- **结果**: 干涉检查结果

### 返回数据结构
```python
{
    "output_file": "/path/to/RAM干涉_20240101_120000.xlsx",
    "ram_interfere_data": [...],  # 原始数据
    "statistics": {
        "total_global_vars": 10,
        "interfere_records": 5,
        "interrupt_table_size": 20,
        "scheduler_tasks": 3
    },
    "global_vars_info": [...]  # 全局变量详细信息
}
```

## 核心工具函数

### 中断表和调度器解析
```python
from sdw_agent.service.ram_interfere_service.utils import get_interrupt_table_scheduler_regular

# 获取中断表和调度器信息
schdlr_rglr_tasks, interrupt_table = get_interrupt_table_scheduler_regular(
    repo_path="/path/to/repo",
    scheduler_task_name="st_gp_SCHDLR_RGLR_TASK"
)
```

### 全局变量搜索
```python
from sdw_agent.service.ram_interfere_service.utils import search_global_var

# 搜索全局变量信息
global_vars_info = search_global_var(
    repo_path="/path/to/repo",
    commit_id="abc123",
    compared_commit_id="def456"
)
```

### RAM干涉数据生成
```python
from sdw_agent.service.ram_interfere_service.utils import gen_ram_interfere_data

# 生成RAM干涉数据
ram_data = gen_ram_interfere_data(
    schdlr_rglr_tasks,
    interrupt_table,
    repo_path,
    global_vars_info
)
```

## 扩展功能

### 自定义文件扩展名
```python
# 支持更多文件类型
workflow.config.io.input.repo_extensions = [".c", ".h", ".cpp", ".hpp"]
```

### 自定义输出路径
```python
from sdw_agent.config.env import ENV
ENV.config.output_data_path = "/custom/output/path"
```

### 批量处理
```python
repos = [
    RepositoryInfo(repo_path="/repo1", commit_id="abc123"),
    RepositoryInfo(repo_path="/repo2", commit_id="def456")
]

results = []
for repo in repos:
    result = workflow.run(repo)
    results.append(result)
```

## 错误处理

### 常见错误及解决方案

1. **仓库路径不存在**
   ```
   错误: 仓库路径不存在: /invalid/path
   解决: 检查路径是否正确，确保目录存在
   ```

2. **不是有效的Git仓库**
   ```
   错误: 不是有效的Git仓库: /path/to/dir
   解决: 确保目录包含.git文件夹
   ```

3. **提交ID无效**
   ```
   错误: 无效的提交ID: abc
   解决: 提供完整的提交ID（至少7位字符）
   ```

4. **模板文件缺失**
   ```
   错误: 模板文件不存在: templates/RAM干涉_模板.xlsx
   解决: 确保模板文件存在于指定路径
   ```

## 性能优化

### 缓存机制
- 函数调用关系缓存
- 中断表信息缓存
- 文件内容缓存

### 批量处理
- 批量查找中断表信息
- 批量获取常量值
- 并行处理独立任务

## 注意事项

1. **文件编码**: 支持UTF-8编码，对其他编码使用错误替换策略
2. **内存使用**: 大型仓库可能消耗较多内存，建议分批处理
3. **权限要求**: 需要读取Git仓库和写入输出目录的权限
4. **模板文件**: 确保templates目录下存在RAM干涉_模板.xlsx文件
5. **日志记录**: 使用loguru进行日志记录，避免使用print输出

## 依赖要求

- Python 3.8+
- Git命令行工具
- 必需的Python包（通过poetry管理）

## 运行命令

```bash
# 使用poetry运行
poetry run python -m sdw_agent.service.ram_interfere_service

# 或者在项目中导入使用
poetry run python your_script.py
```

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志和堆栈跟踪
2. 输入文件和配置信息
3. 系统环境和版本信息
4. 复现步骤和预期结果
