"""
新规变化表作成工作流数据模型

定义输入、输出和配置模型
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from sdw_agent.model.request_model import SourceInfo


class NewRegTableInputModel(BaseModel):
    """新规变化表作成工作流输入模型"""
    requirement_source: SourceInfo = Field(description="要件一览表的SourceInfo")
    template_source: SourceInfo = Field(description="新规变化表模板的SourceInfo")
    
    # 可选配置参数
    target_sheet: Optional[str] = Field(default=None, description="目标工作表名称，不指定则使用配置默认值")
    custom_mapping_rules: Optional[Dict[str, str]] = Field(default=None, description="自定义映射规则")


class NewRegTableOutputModel(BaseModel):
    """新规变化表作成工作流输出模型"""
    success: bool = Field(description="处理是否成功")
    message: str = Field(description="处理结果消息")
    
    # 处理结果数据
    processed_items: int = Field(description="处理的项目数量")
    filtered_items: int = Field(description="过滤后的项目数量")
    mapped_items: int = Field(description="映射的项目数量")
    
    # 文件信息
    template_file: str = Field(description="模板文件路径")
    requirement_file: str = Field(description="要件一览表文件路径")
    
    # 处理详情
    processing_details: Dict[str, Any] = Field(default_factory=dict, description="处理详情信息")
    
    # 错误信息（如果有）
    error_details: Optional[str] = Field(default=None, description="错误详情")


class NewRegTableConfigModel(BaseModel):
    """新规变化表作成工作流配置模型"""
    
    class NewRegTableConfig(BaseModel):
        target_sheet: str = Field(default="機能一覧と新規・変更内容", description="目标工作表名称")
        header_start_row: int = Field(default=5, ge=1, description="表头开始行号")
        header_end_row: int = Field(default=7, ge=1, description="表头结束行号")
        start_col: str = Field(default="B", description="开始列")
        end_col: str = Field(default="J", description="结束列")
        
        mapping_rules: Dict[str, str] = Field(
            default_factory=lambda: {
                "req_id": "要件No.",
                "step_name": "イベント名",
                "req_type": "要件種別",
                "req_clean_file_name": "要件の根拠 | 仕様 | 対象仕様書名",
                "base_file_name": "ベース仕様書名",
                "ar_no": "その他 | チケットID (不具合/仕様QA/課題/要件管理用)",
                "req_change_content": "要件の内容"
            },
            description="数据映射规则"
        )
        
        class DefaultsConfig(BaseModel):
            req_type: str = Field(default="仕様変更", description="默认要件种别")
        
        defaults: DefaultsConfig = Field(default_factory=DefaultsConfig, description="默认值配置")
        
        class ProcessingConfig(BaseModel):
            cleanup_temp_files: bool = Field(default=True, description="是否清理临时文件")
            temp_file_prefix: str = Field(default="temp_new_reg_table_", description="临时文件前缀")
        
        processing: ProcessingConfig = Field(default_factory=ProcessingConfig, description="处理配置")
    
    new_reg_table: NewRegTableConfig = Field(default_factory=NewRegTableConfig, description="新规变化表配置")


def _get_new_reg_table_config() -> Dict[str, Any]:
    """获取新规变化表工作流配置"""
    from sdw_agent.config.env import ENV
    
    # 尝试从环境配置中获取，如果没有则使用默认值
    config = getattr(ENV.config, 'new_reg_table', {})
    if not config:
        # 使用默认配置
        default_config = NewRegTableConfigModel()
        config = default_config.new_reg_table.model_dump()
    
    return config
