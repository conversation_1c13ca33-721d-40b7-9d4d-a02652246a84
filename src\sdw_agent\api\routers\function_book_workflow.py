"""
函数式样书工作流路由 - 生成函数式样书
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any

from sdw_agent.api.core.database import get_db
from sdw_agent.service.func_analyze_book import gen_function_book_multi_commit_service

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/function_book", tags=["函数式样书工作流"])


# 定义请求和响应模型
class FuncBookRequest(BaseModel):
    """示例请求模型"""
    commit_id_after: str = ""
    commit_id_before: str = ""
    git_code_path: str = ""
    git_branch: str = ""
    component_book_path: str = ""



class ExampleResponse(BaseModel):
    """示例响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


# 示例API端点
@router.post("/getFunctionbook",
             summary="函数式样书接口",
             description="这是一个函数式样书接口API，",
             response_model=ExampleResponse)
async def get_functionbook_process(request: FuncBookRequest, db: Session = Depends(get_db)):
    """
    示例处理函数
    
    Args:
        request: 请求参数
        db: 数据库会话
        
    Returns:
        处理结果
    """
    try:
        # 这里可以添加具体的业务逻辑
        # result = get_function_analyze_book_service(request.git_code_path,
        #                                    request.commit_id, request.component_book_path, request.git_branch)
        result = gen_function_book_multi_commit_service(request.git_code_path,
                                           request.commit_id_after, request.commit_id_before, request.component_book_path)

        return {
            "code": 0,
            "msg": "处理成功",
            "data": {"book_path": result}
        }

    except Exception as e:
        return {
            "code": 1,
            "msg": f"处理失败: {str(e)}",
            "data": {}
        }


@router.get("/status",
            summary="获取工作流状态",
            description="获取当前工作流的状态信息",
            response_model=ExampleResponse)
async def get_workflow_status():
    """获取工作流状态"""
    return {
        "code": 0,
        "msg": "查询成功",
        "data": {
            "workflow_name": "示例工作流",
            "status": "active",
            "version": "1.0.0"
        }
    }
