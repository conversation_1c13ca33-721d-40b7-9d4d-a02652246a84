import json
from typing import Dict, List, Any, Optional
from jira import JIRA
import urllib3
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, NoEncryption
from cryptography.hazmat.primitives.serialization.pkcs12 import load_pkcs12
import os
from pathlib import Path
import re
from loguru import logger

from sdw_agent.config.env import ENV

# 禁用证书验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class JiraDataService:
    def __init__(self, server: str, cert_path: str, cert_password: str, pat: str = None,
                 username: str = None, password: str = None):
        """
        初始化JIRA数据服务
        :param server: JIRA服务器地址
        :param cert_path: P12证书路径
        :param cert_password: 证书密码
        :param pat: PAT token（可选）
        :param username: 用户名（可选）
        :param password: 密码（可选）
        """
        self.server = server
        self.cert_path = cert_path
        self.cert_password = cert_password
        self.pat = pat
        self.username = username
        self.password = password
        self.jira = None
        self.is_available = False
        self.error_message = ""
        self._init_connection()

    def _p12_to_pem(self) -> tuple[str, str]:
        """
        转换P12证书为PEM格式
        :return: (pem_path, key_path)的元组
        """
        cert_dir = os.path.dirname(self.cert_path)
        cert_name = Path(self.cert_path).stem
        key_path = os.path.join(cert_dir, f"{cert_name}.key")
        pem_path = os.path.join(cert_dir, f"{cert_name}.pem")

        if os.path.exists(key_path) and os.path.exists(pem_path):
            return pem_path, key_path

        with open(self.cert_path, 'rb') as f:
            p12_data = f.read()
            p12 = load_pkcs12(p12_data, self.cert_password.encode())

        private_key = p12.key.private_bytes(
            encoding=Encoding.PEM,
            format=PrivateFormat.PKCS8,
            encryption_algorithm=NoEncryption()
        )
        cert = p12.cert.certificate.public_bytes(Encoding.PEM)

        with open(key_path, 'wb') as f:
            f.write(private_key)
        with open(pem_path, 'wb') as f:
            f.write(cert)

        return pem_path, key_path

    def _init_connection(self):
        """初始化JIRA连接"""
        try:
            # 检查JIRA是否启用
            if not ENV.config.jira.enable:
                self.error_message = "JIRA服务已禁用"
                logger.info("JIRA服务已禁用，跳过连接初始化")
                return

            if not os.path.exists(self.cert_path):
                self.error_message = f"证书文件不存在: {self.cert_path}"
                logger.error(self.error_message)
                return

            cert_file, key_file = self._p12_to_pem()

            try:
                if self.pat:
                    self.jira = JIRA(
                        server=self.server,
                        token_auth=self.pat,
                        options={'verify': False, 'cert': (cert_file, key_file)}
                    )
                    self.jira.myself()
                    logger.info("使用PAT认证成功")
                    self.is_available = True
                    return
            except Exception as e:
                if not (self.username and self.password):
                    raise e

            self.jira = JIRA(
                server=self.server,
                basic_auth=(self.username, self.password),
                options={'verify': False, 'cert': (cert_file, key_file)}
            )
            self.jira.myself()
            self.is_available = True
        except Exception as e:
            self.error_message = f"JIRA连接初始化失败: {str(e)}"
            logger.error(self.error_message)

    def get_field_mappings(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有JIRA字段映射
        :return: 字段映射字典
        """
        fields = self.jira.fields()
        return {
            field['id']: {
                'name': field['name'],
                'custom': field.get('custom', False),
                'type': field.get('schema', {}).get('type', 'unknown')
            }
            for field in fields
        }

    def get_issue_basic_info(self, issue_key: str) -> Dict[str, Any]:
        """
        获取票据基本信息
        :param issue_key: 票据键
        :return: 基本信息字典
        """
        issue = self.jira.issue(issue_key)
        return {
            'key': issue.key,
            'summary': issue.fields.summary,
            'description': issue.fields.description or '',
            'status': issue.fields.status.name,
            'assignee': str(issue.fields.assignee) if issue.fields.assignee else None,
            'reporter': str(issue.fields.reporter) if issue.fields.reporter else None,
            'created': issue.fields.created,
            'updated': issue.fields.updated,
            'issue_type': issue.fields.issuetype.name,
            'priority': issue.fields.priority.name if issue.fields.priority else None,
            'components': [c.name for c in issue.fields.components] if issue.fields.components else [],
            'labels': issue.fields.labels if issue.fields.labels else [],
            'resolution': getattr(issue.fields, 'resolution', {}).name if getattr(issue.fields, 'resolution',
                                                                                  None) else None,
            'fix_versions': [v.name for v in getattr(issue.fields, 'fixVersions', [])]
        }

    def get_linked_issues(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        获取关联票据的基本信息
        :param issue_key: 票据键
        :return: 关联票据列表
        """
        issue = self.jira.issue(issue_key)
        linked_issues = []

        if hasattr(issue.fields, 'issuelinks') and issue.fields.issuelinks:
            for link in issue.fields.issuelinks:
                linked_issue = None
                link_type = ""
                relationship = ""

                if hasattr(link, "inwardIssue"):
                    linked_issue = link.inwardIssue
                    link_type = link.type.inward
                    relationship = "inward"
                elif hasattr(link, "outwardIssue"):
                    linked_issue = link.outwardIssue
                    link_type = link.type.outward
                    relationship = "outward"

                if linked_issue:
                    is_bidirectional = (
                            link_type.lower() == "relates to" or
                            (hasattr(link.type, 'inward') and link.type.inward.lower() == "relates to") or
                            (hasattr(link.type, 'outward') and link.type.outward.lower() == "relates to")
                    )

                    linked_issues.append({
                        'key': linked_issue.key,
                        'summary': linked_issue.fields.summary,
                        'status': linked_issue.fields.status.name,
                        'issue_type': linked_issue.fields.issuetype.name,
                        'link_type': link_type,
                        'relationship': relationship,
                        'is_bidirectional': is_bidirectional
                    })

        return linked_issues

    def get_subtasks(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        获取子任务信息
        :param issue_key: 票据键
        :return: 子任务列表
        """
        issue = self.jira.issue(issue_key)
        return [
            {
                'key': subtask.key,
                'summary': subtask.fields.summary,
                'status': subtask.fields.status.name,
                'issue_type': subtask.fields.issuetype.name,
                'assignee': str(subtask.fields.assignee) if subtask.fields.assignee else None,
                'created': subtask.fields.created,
                'updated': subtask.fields.updated
            }
            for subtask in (issue.fields.subtasks or [])
        ]

    def _get_dynamic_fields(self, issue) -> Dict[str, Dict[str, Any]]:
        """
        动态获取字段信息并按类别分组
        """
        issue_type = issue.fields.issuetype.name.lower()

        # 根据票据类型定义需要显示的字段
        field_configs = {
            'requirement': {
                '仕様書情報': {
                    'required': [
                        '仕様タイプ',
                        'ターゲット仕様',
                        'ベース仕様',
                        'シート名',
                        '章',
                        'ページ'
                    ],
                    'keywords': ['仕様', '仕様書', 'シート', '章']
                },
                '要件情報': {
                    'required': [
                        '要件有無',
                        '機能',
                        '分類(USPM)',
                        '要件内容',
                        'CSTMLST',
                        '機能安全',
                        '変更分類'
                    ],
                    'keywords': ['要件', '機能', 'CSTMLST', '分類(USPM)']
                },
                '変更情報': {
                    'required': [
                        '変更理由',
                        '変更内容',
                        '変更影響'
                    ],
                    'keywords': ['変更', '影響']
                }
            },
            'ar': {
                '発行部署入力部': {
                    'required': [
                        '出典文書番号',
                        '仕様書リンク',
                        'ベース仕様番号',
                        'ベース文書番号',
                        '特記事項',
                        'ベースタグ'
                    ],
                    'keywords': ['出典', '文書', 'リンク', 'ベース', '仕様', 'タグ', '実施合意', '特記事項', '重要性']
                },
                '要件定義部署入力部': {
                    'required': [],
                    'keywords': ['要件定義']
                },
                'ソフト入力部': {
                    'required': [
                        '担当チーム',
                        '開発項目未作成チーム'
                    ],
                    'keywords': ['担当', 'チーム', '開発', '項目']
                },
                '共通入力部': {
                    'required': [],
                    'keywords': ['共通']
                }
            },
            'epic': {
                'エピック情報': {
                    'required': [
                        'エピック名',
                        'プロジェクト名',
                        'バリエーション',
                        '輸入フェーズ',
                        '実行責任ドメイン',
                        'チーム外専門家',
                        'チーム外有識者',
                        'ストーリーポイント'
                    ],
                    'keywords': ['エピック', 'プロジェクト', 'チーム']
                }
            }
        }

        # 确定使用哪个配置
        if '要件' in issue_type or 'requirement' in issue_type:
            config = field_configs['requirement']
        elif 'ar' in issue_type or '缺陷' in issue_type or 'bug' in issue_type:
            config = field_configs['ar']
        elif 'epic' in issue_type or 'エピック' in issue_type:
            config = field_configs['epic']
        else:
            return {}

        # 获取所有字段定义
        fields_def = {
            field['id']: {
                'name': field['name'],
                'type': field.get('schema', {}).get('type', 'string')
            }
            for field in self.jira.fields()
        }

        # 初始化分组
        detail_info = {category: {} for category in config.keys()}

        # 遍历所有字段值
        for field_id, value in issue.raw['fields'].items():
            if field_id not in fields_def or value is None:
                continue

            field_name = fields_def[field_id]['name']
            field_type = fields_def[field_id]['type']

            # 处理字段值
            processed_value = self._process_field_value(value, field_type)
            if processed_value is None:
                continue

            # 根据字段名分类并过滤
            for category, category_config in config.items():
                if any(keyword in field_name for keyword in category_config['keywords']):
                    # 只保留必需字段或字段名完全匹配的字段
                    if (not category_config['required'] or  # 如果没有必需字段，保留所有相关字段
                            field_name in category_config['required'] or
                            any(req in field_name for req in category_config['required'])):
                        detail_info[category][field_name] = processed_value
                    break

        return detail_info

    def _process_field_value(self, value: Any, field_type: str) -> Any:
        """
        处理字段值
        """
        if value in [None, '', 'None', '無', '-', [], {}]:
            return None

        # 处理字典类型
        if isinstance(value, dict):
            if 'name' in value:
                return value['name']
            elif 'value' in value:
                return value['value']
            # 如果是复杂字典，只返回非空值
            return {k: v for k, v in value.items() if v not in [None, '', 'None', '無', '-']}

        # 处理列表类型
        if isinstance(value, list):
            if not value:
                return None
            # 去重
            processed = []
            seen = set()
            for item in value:
                if isinstance(item, dict):
                    if 'name' in item:
                        item_value = item['name']
                    elif 'value' in item:
                        item_value = item['value']
                    else:
                        item_value = str(item)
                else:
                    item_value = str(item)

                if item_value not in seen:
                    seen.add(item_value)
                    processed.append(item_value)
            return processed

        # 处理字符串类型
        if isinstance(value, str):
            # 去除重复的行
            if '\n' in value:
                lines = value.split('\n')
                unique_lines = []
                seen = set()
                for line in lines:
                    line = line.strip()
                    if line and line not in seen:
                        seen.add(line)
                        unique_lines.append(line)
                return '\n'.join(unique_lines)
            return value

        return value

    def get_complete_issue_data(self, issue_key: str) -> Dict[str, Any]:
        """
        获取票据的完整信息，结合固定映射和动态获取
        """
        # 获取基本信息
        issue = self.jira.issue(issue_key)
        issue_type = issue.fields.issuetype.name.lower()

        # 使用固定映射获取基本信息
        basic_info = {
            '编号': issue.key,
            '标题': issue.fields.summary,
            '状态': issue.fields.status.name,
            '经办人': str(issue.fields.assignee) if issue.fields.assignee else None,
            '报告人': str(issue.fields.reporter) if issue.fields.reporter else None,
            '创建时间': issue.fields.created,
            '更新时间': issue.fields.updated,
            '问题类型': issue.fields.issuetype.name,
            '优先级': issue.fields.priority.name if issue.fields.priority else None,
            '模块': [c.name for c in issue.fields.components] if issue.fields.components else [],
            '标签': issue.fields.labels if issue.fields.labels else [],
            '解决结果': getattr(issue.fields, 'resolution', {}).name if getattr(issue.fields, 'resolution',
                                                                                None) else None,
            '修复的版本': [v.name for v in getattr(issue.fields, 'fixVersions', [])]
        }

        # 如果有描述，添加到基本信息中
        if issue.fields.description:
            basic_info['描述'] = issue.fields.description

        # 动态获取详细信息
        detail_info = self._get_dynamic_fields(issue)

        # 构建关联信息
        relation_info = {
            'linked_issues': self.get_linked_issues(issue_key),
            'subtasks': self.get_subtasks(issue_key)
        }

        # 确定票据类型
        if issue_type == 'epic' or issue_type == 'エピック':
            ticket_type = 'epic'
        elif '要件' in issue_type or 'requirement' in issue_type:
            ticket_type = 'requirement'
        elif 'ar' in issue_type or '缺陷' in issue_type or 'bug' in issue_type:
            ticket_type = 'ar'
        else:
            ticket_type = 'unknown'

        return {
            'type': ticket_type,
            'basic_info': basic_info,
            'detail_info': detail_info,
            'relation_info': relation_info
        }

    def find_matching_epic(self, ar_key: str, ar_summary: str) -> Optional[Dict[str, str]]:
        """
        根据AR票号和概要，查找最匹配的Epic票据
        :param ar_key: AR票号
        :param ar_summary: AR概要
        :return: 匹配到的Epic信息，包含票号和标题，如果没有匹配返回None
        """
        try:
            # 获取AR票据的关联票据
            ar_issue = self.jira.issue(ar_key)
            linked_issues = ar_issue.fields.issuelinks

            epic_candidates = []
            # 收集所有关联的Epic票据
            for link in linked_issues:
                linked_issue = None
                if hasattr(link, "inwardIssue"):
                    linked_issue = link.inwardIssue
                elif hasattr(link, "outwardIssue"):
                    linked_issue = link.outwardIssue

                if linked_issue and linked_issue.fields.issuetype.name.lower() in ['epic', 'エピック']:
                    epic_candidates.append({
                        'key': linked_issue.key,
                        'summary': linked_issue.fields.summary,
                        'score': 0  # 初始化匹配分数
                    })

            if not epic_candidates:
                return None

            # 计算每个Epic的匹配分数
            for epic in epic_candidates:
                score = 0
                epic_summary = epic['summary'].lower()
                ar_summary_lower = ar_summary.lower()

                # 1. 完全匹配
                if ar_summary_lower in epic_summary or epic_summary in ar_summary_lower:
                    score += 100

                # 2. 关键词匹配
                keywords = ar_summary_lower.split()
                for keyword in keywords:
                    if len(keyword) > 1 and keyword in epic_summary:  # 忽略单字符关键词
                        score += 10

                # 3. 数字匹配（如 1500W, 2400W 等）
                ar_numbers = set(re.findall(r'\d+', ar_summary))
                epic_numbers = set(re.findall(r'\d+', epic_summary))
                matching_numbers = ar_numbers.intersection(epic_numbers)
                score += len(matching_numbers) * 15

                epic['score'] = score

            # 按分数排序并返回最匹配的Epic
            epic_candidates.sort(key=lambda x: x['score'], reverse=True)
            if epic_candidates[0]['score'] > 0:
                return {
                    'key': epic_candidates[0]['key'],
                    'summary': epic_candidates[0]['summary'],
                    'score': epic_candidates[0]['score']
                }

            return None

        except Exception as e:
            logger.info(f"Error finding matching epic for {ar_key}: {str(e)}")
            return None

    def find_matching_epics_batch(self, ar_tickets: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        批量处理AR票据和概要，查找匹配的Epic票据
        :param ar_tickets: AR票据列表，每个元素包含 {'key': 票号, 'summary': 概要}
        :return: 匹配结果列表
        """
        results = []
        for ar in ar_tickets:
            match = self.find_matching_epic(ar['key'], ar['summary'])
            results.append({
                'ar_key': ar['key'],
                'ar_summary': ar['summary'],
                'matching_epic': match
            })
        return results

    def check_health(self) -> bool:
        """
        检查 Jira 连接是否正常
        :return: bool 连接是否正常
        """
        try:
            self.jira.myself()
            return True
        except Exception as e:
            logger.error(f"Jira 连接异常: {str(e)}")
            return False


# JIRA配置
server = ENV.config.jira.server
pat = ENV.config.jira.pat
cert_path = ENV.config.jira.cert_path
cert_password = ENV.config.jira.cert_password

# 全局JIRA服务实例（懒加载）
_jira_service = None


def get_jira_service() -> JiraDataService:
    """
    获取JIRA服务实例（懒加载）

    Returns:
        JiraDataService: JIRA服务实例
    """
    global _jira_service
    if _jira_service is None:
        _jira_service = JiraDataService(server, cert_path, cert_password, pat)
    return _jira_service


# 为了保持向后兼容性，创建一个属性访问器
class JiraServiceProxy:
    """JIRA服务代理，实现懒加载"""

    def __init__(self):
        self._service = None

    def __getattr__(self, name):
        if self._service is None:
            self._service = get_jira_service()
        return getattr(self._service, name)


jira_service = JiraServiceProxy()


def get_epic_info(key, summary):
    """
    根据Ar票找到关联的Epic票的描述信息
    :param key: 票据键
    :param summary: 票据概要
    :return: 要件变更信息
    """
    require_info = {}
    try:
        # 检查JIRA是否可用
        if not check_jira_health():
            logger.warning("JIRA服务不可用，跳过Epic信息获取")
            return require_info

        # 批量查找匹配的Epic
        result = jira_service.find_matching_epic(key, summary)
        if result:
            epic_key = result['key']
            jira_info = jira_service.get_complete_issue_data(epic_key)
            epic_basic_info = jira_info['basic_info']
            # logger.info(f"jira的epic票完整信息：\n{json.dumps(jira_info, indent=4, ensure_ascii=False)}")
            require_info['key'] = epic_key
            require_info['title'] = epic_basic_info['标题']
            # 获取Epic的基础信息中的描述
            require_info['desc'] = "\n".join(epic_basic_info['描述'].split('\r\n')[:-1])
        else:
            logger.info("未找到匹配的Epic")
    except Exception as e:
        logger.error(f"获取Epic信息失败: {str(e)}")
    return require_info


def get_project_info(key):
    """
    获取P票的要件信息
    :param key: 票据键
    :return: 项目信息
    """
    try:
        # 检查JIRA是否可用
        if not check_jira_health():
            logger.warning("JIRA服务不可用，跳过P票信息获取")
            return {}

        jira_info = jira_service.get_complete_issue_data(key)
        project_info = jira_info['detail_info']
        logger.info(f"jira的p票完整信息：\n{json.dumps(project_info, indent=4, ensure_ascii=False)}")
        change_type = project_info['要件情報']['変更分類(USPM)']
        change_content = project_info['要件情報']['要件内容(USPM)']

        change_info = {
            'change_type': change_type,
            'change_content': change_content
        }
        return change_info
    except Exception as e:
        logger.error(f"获取P票信息失败: {str(e)}")
        return {}


def get_ar_info(ar_key: str) -> Dict[str, Any]:
    """
    根据AR票号获取AR票的相关字段信息
    :param ar_key: AR票号
    :return: AR票的详细信息字典
    """
    try:
        # 检查JIRA是否可用
        if not check_jira_health():
            logger.warning("JIRA服务不可用，跳过AR票信息获取")
            return {
                'ar_key': ar_key,
                'error': 'JIRA服务不可用',
                'basic_info': {},
                'ar_specific_fields': {},
                'linked_issues': [],
                'subtasks': []
            }

        # 获取AR票的完整信息
        jira_info = jira_service.get_complete_issue_data(ar_key)

        # 验证是否为AR票
        if jira_info['type'] != 'ar':
            logger.warning(f"票据 {ar_key} 不是AR类型票据，实际类型: {jira_info['type']}")

        # 提取AR票的关键信息
        basic_info = jira_info['basic_info']
        detail_info = jira_info['detail_info']

        # 构建AR票信息结构
        ar_info = {
            'ar_key': ar_key,
            'basic_info': {
                'title': basic_info.get('标题', ''),
                'status': basic_info.get('状态', ''),
                'labels': basic_info.get('标签', []),
            },
            'ar_specific_fields': {},
        }

        # 提取AR特有的字段信息，特别关注ベース仕様番号
        base_spec_number = None
        for category, fields in detail_info.items():
            if fields:  # 只包含有值的分类
                ar_info['ar_specific_fields'][category] = fields
                # 查找ベース仕様番号字段
                for field_name, field_value in fields.items():
                    if 'ベース仕様番号' in field_name or 'ベース仕様' in field_name:
                        base_spec_number = field_value
                        break
                    if 'ベース仕様' in field_value:
                        base_spec_number = field_value
                        break

        # 将ベース仕様番号单独提取出来方便访问
        ar_info['base_spec_number'] = base_spec_number
        return ar_info

    except Exception as e:
        logger.error(f"获取AR票 {ar_key} 信息失败: {str(e)}")
        return {
            'ar_key': ar_key,
            'error': str(e),
            'basic_info': {},
            'ar_specific_fields': {},
            'linked_issues': [],
            'subtasks': []
        }


def check_jira_health() -> bool:
    """
    检查 Jira 服务是否可用
    :return: bool 服务是否可用
    """
    try:
        # 检查JIRA是否启用
        if not ENV.config.jira.enable:
            return False
        return jira_service.is_available
    except Exception as e:
        logger.error(f"检查JIRA健康状态失败: {str(e)}")
        return False


def main():
    # 测试数据
    ar_key = 'MET19PFV3-35197'
    ar_summary = '第0階層⇒第一階層へ移動 1500Wコンセント,2400Wコンセント,7200Wコンセント'

    # 测试新的get_ar_info方法
    logger.info("=== 测试 get_ar_info 方法 ===")
    ar_info = get_ar_info(ar_key)
    logger.info(f"AR票 {ar_key} 的详细信息：\n{json.dumps(ar_info, indent=4, ensure_ascii=False)}")

    # 根据Ar票找到关联的Epic票的描述信息
    logger.info("=== 测试 get_epic_info 方法 ===")
    result = get_epic_info(ar_key, ar_summary)
    logger.info(f"找到关联的epic票的变更描述信息：\n{result}")

    # 测试P票信息获取
    logger.info("=== 测试 get_project_info 方法 ===")
    p_key = 'PROCMGT-5014'
    p_change_info = get_project_info(p_key)
    logger.info(f"找到关联的p票的变更描述信息：\n{p_change_info}")


if __name__ == '__main__':
    main()
