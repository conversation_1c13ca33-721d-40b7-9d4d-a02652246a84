import os
import logging
from typing import Dict, Type, List

from docparser.core.base_parser import BaseParser

# Configure logging
logger = logging.getLogger('docparser')

class ParserFactory:
    """
    Factory class for creating document parsers.
    Manages parser registration and creation based on file extension.
    """
    
    # Dictionary mapping file extensions to parser classes
    _parsers: Dict[str, Type[BaseParser]] = {}
    
    @classmethod
    def register_parser(cls, parser_class: Type[BaseParser]) -> None:
        """
        Register a parser class for its supported extensions.
        
        Args:
            parser_class: Parser class to register
        """
        # Create an instance to get supported extensions
        parser = parser_class()
        
        for extension in parser.supported_extensions:
            cls._parsers[extension] = parser_class
            logger.info(f"Registered parser {parser_class.__name__} for extension {extension}")
    
    @classmethod
    def create_parser(cls, file_path: str) -> BaseParser:
        """
        Create a parser instance for the given file path.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Parser instance for the file type
            
        Raises:
            ValueError: If no parser is registered for the file extension
        """
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension not in cls._parsers:
            supported = ", ".join(cls._parsers.keys())
            raise ValueError(f"No parser registered for extension {file_extension}. Supported extensions: {supported}")
        
        parser_class = cls._parsers[file_extension]
        logger.info(f"Creating parser {parser_class.__name__} for file {file_path}")
        
        return parser_class()
    
    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported file extensions
        """
        return list(cls._parsers.keys())
