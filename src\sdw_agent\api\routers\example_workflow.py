"""
示例工作流路由 - 展示如何添加新的工作流
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any

from sdw_agent.api.core.database import get_db

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/example", tags=["示例工作流"])


# 定义请求和响应模型
class ExampleRequest(BaseModel):
    """示例请求模型"""
    name: str
    description: str


class ExampleResponse(BaseModel):
    """示例响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


# 示例API端点
@router.post("/process",
             summary="示例处理接口",
             description="这是一个示例API，展示如何在新工作流中添加接口",
             response_model=ExampleResponse)
async def example_process(request: ExampleRequest, db: Session = Depends(get_db)):
    """
    示例处理函数
    
    Args:
        request: 请求参数
        db: 数据库会话
        
    Returns:
        处理结果
    """
    try:
        # 这里可以添加具体的业务逻辑
        result = {
            "processed_name": request.name,
            "processed_description": request.description,
            "status": "success"
        }
        
        return {
            "code": 0,
            "msg": "处理成功",
            "data": result
        }
        
    except Exception as e:
        return {
            "code": 1,
            "msg": f"处理失败: {str(e)}",
            "data": {}
        }


@router.get("/status",
            summary="获取工作流状态",
            description="获取当前工作流的状态信息",
            response_model=ExampleResponse)
async def get_workflow_status():
    """获取工作流状态"""
    return {
        "code": 0,
        "msg": "查询成功",
        "data": {
            "workflow_name": "示例工作流",
            "status": "active",
            "version": "1.0.0"
        }
    }
