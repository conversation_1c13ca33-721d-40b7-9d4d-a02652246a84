# Warning Code Generation Service Configuration
# 警告代码生成服务配置

# 基本信息
name: "开发环境确认检查"
description: "基于bin包/manifest/jenkins脚本生成开发环境检查的工作流服务"
version: "1.0.0"
author: "SDW-Team"

# V字对应
v_model:
  stage: ""
  phase: "基本设计"

# bin校验文件存储路径
bin_save_path: "C:\\Users\\<USER>\\Desktop\\SVN_NEW\\xh\\7月需求\\开发环境确认"

# bin校验结果填写格式
bin_check_cfg:
  bin_check_ok: "なし"
  bin_check_ng: "あり"
  bin_check_white_list_name: '^(Partition_table_Group.*\.bin|Certificate_[A-Za-z0-9]+_Group_[A-Za-z0-9]+\.bin|Certificate_RootfsGroup_[A-Za-z0-9]+\.bin|\w+_cert\.bin)$'

ssh_config:
  hostname: "************"
  username: "dnkt"
  password: "dnkt$88"
  port: 22
  timeout: 3000
  connect_timeout: 10

ftp_config:
  host: "************"
  user: "release"
  password: "release"
  port: 21

shell_script_cfg_map:
  Orca: "emmc_#1.sh"
  Orca2: "emmc_checkOrca2.sh"
  Orca2.5: "emmc_checkOrca2.sh"
  Orca3: "emmc_checkOrca2.sh"
  Orca4: "emmc_checkOrca2.sh"
  Orca4.5: "emmc_checkOrca2.sh"
  Panda: "emmc_checkPanda.sh"

manifest_git_cfg:
   agl_kernel: "http://************:8085/a/technical-department/agl805/agl8.0.5_manifest"
   agl_srv: "http://************:8085/a/technical-department/agl805/agl8.0.5_srv_manifest"
   agl_kanzi: "http://************:8085/a/technical-department/Orca/Orca_Code/PF/Orca_AGL_MET_GFX_2"
   onclick_tool_git_path: "http://************/technical-department/Orca/Orca_Code/PF/TOOLS/OneClickBuild.git"