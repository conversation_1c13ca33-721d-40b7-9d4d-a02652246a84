"""
通信故障安全CS工作流重构版本

V字对应：
2.1 基本設計 通信故障安全 CS

模块简介和主要功能说明：
通信故障安全CS工作流的重构版本，提供更规范的代码结构和更好的可维护性。
支持从BitAssign文件和SPI JSON文件中解析信号定义，并在代码变更中搜索匹配的信号。

主要功能：
1. 导出工作流类和相关模型
2. 提供统一的接口访问
3. 支持工作流注册和发现
"""

from .design_peer_review import Design_PeerreviewWorkflow
from .models import (
    BasicDesignInput,
    ConfirmFormat,
    CheckRangeIninfo
)

# 导出公共接口
__all__ = [
    "Design_PeerreviewWorkflow",

    "BasicDesignInput",
    "ConfirmFormat",
    "CheckRangeIninfo"
]

# 版本信息
__version__ = "1.0.0"
__author__ = "junwei_cai"
__description__ = "peerreview 开崔工作流，分基本设计review，详细设计review，codereview开崔"