# SDW Agent API 架构说明

## 概述

本项目已重构为模块化架构，支持多个工作流的路由分离。每个工作流都有独立的路由文件，便于维护和扩展。

## 目录结构

```
src/sdw_agent/api/
├── core/                        # 核心组件
│   ├── __init__.py
│   ├── database.py             # 数据库配置和模型
│   ├── config.py               # 应用配置和中间件
│   ├── static/                 # 静态文件
│   │   ├── swagger/            # Swagger UI 静态资源
│   │   └── redoc/              # ReDoc 静态资源
│   └── translations/           # 国际化文件
│       ├── en/                 # 英文翻译
│       ├── ja/                 # 日文翻译
│       └── zh/                 # 中文翻译
├── routers/                    # 路由模块
│   ├── __init__.py
│   ├── design_cs_workflow.py   # 设计基准工作流路由
│   ├── integration.py          # I/F整合工作流路由
│   └── example_workflow.py     # 示例工作流路由
├── server.py                   # 主入口文件
├── tasks.db                    # SQLite数据库文件
└── README.md                   # 本文档
```

## 核心组件说明

### 1. server.py - 主入口
- 作为应用的总入口
- 负责创建FastAPI实例
- 注册所有工作流路由
- 设置中间件和配置

### 2. core/database.py - 数据库层
- 数据库连接配置（SQLite）
- 数据模型定义（TaskModel等）
- 数据库依赖注入

### 3. core/config.py - 配置层
- 中间件设置（CORS、日志、国际化等）
- 静态文件配置（Swagger UI、ReDoc）
- 文档路由配置

### 4. core/static/ - 静态资源
- Swagger UI 静态文件
- ReDoc 静态文件

### 5. core/translations/ - 国际化
- 多语言翻译文件（中文、英文、日文）

### 6. routers/ - 路由层
每个工作流都有独立的路由文件，包含该工作流的所有API端点。系统会自动发现并注册所有路由。

## 如何添加新的工作流

### 只需一步：创建路由文件

在 `routers/` 目录下创建新的路由文件，例如 `new_workflow.py`：

```python
"""
新工作流相关的API路由
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel

from sdw_agent.api.core.database import get_db

# 创建路由器
router = APIRouter(prefix="/api/sdw/new_workflow", tags=["新工作流"])

# 定义请求/响应模型
class NewWorkflowRequest(BaseModel):
    name: str
    description: str

class NewWorkflowResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: dict

# 定义API端点
@router.post("/process")
async def process_workflow(request: NewWorkflowRequest, db: Session = Depends(get_db)):
    # 实现业务逻辑
    return {"code": 0, "msg": "success", "data": {"result": "processed"}}
```

**就这样！** 系统会自动发现并注册这个新路由，无需手动导入或修改其他文件。

## 现有工作流

### 1. 设计基准工作流 (design_cs_workflow.py)
- `/api/sdw/design/analyzeKeyList` - 获取要件一览表
- `/api/sdw/design/submit_check_sheet_task` - 异步提交check sheet任务
- `/api/sdw/design/check_sheet_task_status` - 查询任务状态
- `/api/sdw/design/check_sheet_generate_sync` - 同步生成check sheet

### 2. I/F整合工作流 (integration.py)
- `/api/sdw/integration/if_integration` - I/F整合性确认

### 3. 示例工作流 (example_workflow.py)
- `/api/sdw/example/process` - 示例处理接口
- `/api/sdw/example/status` - 获取工作流状态

## 架构特点

### 自动路由发现
- 无需手动导入或注册路由
- 只需在 `routers/` 目录下创建包含 `router` 变量的 `.py` 文件
- 系统启动时自动扫描并注册所有路由

### 静态资源管理
- 静态文件统一放在 `core/static/` 目录下
- 支持Swagger UI和ReDoc文档界面
- 自动配置静态文件服务

### 国际化支持
- 支持多语言（中文、英文、日文）
- 翻译文件位于 `core/translations/` 目录
- 通过请求头 `Lang` 自动切换语言

## 最佳实践

1. **路由命名**：使用描述性的前缀，如 `/api/sdw/workflow_name`
2. **标签分组**：为每个工作流设置合适的标签，便于API文档分组
3. **错误处理**：统一的错误响应格式
4. **依赖注入**：使用FastAPI的依赖注入系统
5. **类型注解**：使用Pydantic模型进行请求/响应验证

## 启动服务

```bash
python src/sdw_agent/api/server.py
```

服务将在 `http://localhost:8001` 启动，可以访问：
- API文档：`http://localhost:8001/docs`
- ReDoc文档：`http://localhost:8001/redoc`
