"""
新规变化表作成工作流

V字对应：
2.1 基本设计
新规变化表作成

根据要件一览表和模板创建新规变化表的工作流实现。

主要功能：
1. 要件一览表数据提取和过滤
2. 要件ID生成和数据预处理
3. 数据映射到新规变化表模板
4. 文件验证和错误处理
"""

import os
from typing import Optional

from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.util.excel.new_reg_table_excel import NewRegTableExcelUtil
from .models import (
    NewRegTableInputModel,
    NewRegTableOutputModel,
    NewRegTableConfigModel,
    _get_new_reg_table_config
)


@register_workflow("new_reg_table")
class NewRegTableWorkflow(BaseWorkflow):
    """
    新规变化表作成工作流
    
    负责根据要件一览表和模板创建新规变化表，提供数据提取、
    过滤、映射等功能。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化新规变化表作成工作流
        
        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        self.excel_util = None

    def validate_input(self, input_data: NewRegTableInputModel) -> bool:
        """
        验证输入参数
        
        Args:
            input_data: 输入数据模型
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证要件一览表SourceInfo
            if not input_data.requirement_source:
                self.logger.error("要件一览表SourceInfo不能为空")
                return False

            if not input_data.requirement_source.uri or not input_data.requirement_source.uri.strip():
                self.logger.error("要件一览表URI不能为空")
                return False

            # 对于local类型，检查文件是否存在
            if input_data.requirement_source.type == "local":
                if not os.path.exists(input_data.requirement_source.uri):
                    self.logger.error(f"要件一览表文件不存在: {input_data.requirement_source.uri}")
                    return False

                if not os.path.isfile(input_data.requirement_source.uri):
                    self.logger.error(f"要件一览表路径不是有效文件: {input_data.requirement_source.uri}")
                    return False

            # 验证新规变化表模板SourceInfo
            if not input_data.template_source:
                self.logger.error("新规变化表模板SourceInfo不能为空")
                return False

            if not input_data.template_source.uri or not input_data.template_source.uri.strip():
                self.logger.error("新规变化表模板URI不能为空")
                return False

            # 对于local类型，检查文件是否存在
            if input_data.template_source.type == "local":
                if not os.path.exists(input_data.template_source.uri):
                    self.logger.error(f"新规变化表模板文件不存在: {input_data.template_source.uri}")
                    return False

                if not os.path.isfile(input_data.template_source.uri):
                    self.logger.error(f"新规变化表模板路径不是有效文件: {input_data.template_source.uri}")
                    return False

            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def pre_execute(self, input_data: NewRegTableInputModel) -> bool:
        """
        执行前准备工作
        
        Args:
            input_data: 输入数据模型
            
        Returns:
            bool: 准备是否成功
        """
        try:
            # 获取配置
            config = _get_new_reg_table_config()
            target_sheet = input_data.target_sheet or config.get('target_sheet', '機能一覧と新規・変更内容')
            
            # 初始化Excel工具类
            self.excel_util = NewRegTableExcelUtil(
                file_path=input_data.template_source.uri,
                engine="openpyxl"
            )
            
            # 验证模板文件
            if not self.excel_util.validate_template_file(
                input_data.template_source.uri, 
                target_sheet
            ):
                self.logger.error("模板文件验证失败")
                return False
            
            self.logger.info("执行前准备工作完成")
            return True
            
        except Exception as e:
            self.logger.error(f"执行前准备失败: {str(e)}")
            return False

    def execute(self, input_data: NewRegTableInputModel) -> WorkflowResult:
        """
        执行新规变化表作成工作流核心逻辑
        
        Args:
            input_data: 输入数据模型
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.logger.info("开始执行新规变化表作成工作流")
            
            # 获取配置
            config = _get_new_reg_table_config()
            target_sheet = input_data.target_sheet or config.get('target_sheet', '機能一覧と新規・変更内容')
            mapping_rules = input_data.custom_mapping_rules or config.get('mapping_rules', {})
            
            # 1. 提取要件一览表数据
            self.logger.info("步骤1: 提取要件一览表数据")
            content_list = self.excel_util.extract_requirement_data(input_data.requirement_source)
            
            if not content_list:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="要件一览表中没有找到有效数据",
                    data={}
                )
            
            # 2. 过滤要件数据
            self.logger.info("步骤2: 过滤要件数据")
            filter_content_list = self.excel_util.filter_requirement_data(content_list)
            
            if not filter_content_list:
                return WorkflowResult(
                    status=WorkflowStatus.WARNING,
                    message="过滤后没有需要处理的要件项目",
                    data={
                        "processed_items": len(content_list),
                        "filtered_items": 0,
                        "mapped_items": 0
                    }
                )
            
            # 3. 生成要件ID和其他字段
            self.logger.info("步骤3: 生成要件ID和其他字段")
            new_table_list = self.excel_util.generate_requirement_ids(filter_content_list)
            
            # 4. 映射数据到模板
            self.logger.info("步骤4: 映射数据到模板")
            header_config = {
                'header_start_row': config.get('header_start_row', 5),
                'header_end_row': config.get('header_end_row', 7),
                'start_col': config.get('start_col', 'B'),
                'end_col': config.get('end_col', 'J')
            }
            
            mapping_success = self.excel_util.map_data_to_template(
                new_table_list=new_table_list,
                template_file=input_data.template_source.uri,
                target_sheet=target_sheet,
                mapping_rules=mapping_rules,
                header_config=header_config
            )
            
            if not mapping_success:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="数据映射到模板失败",
                    data={
                        "processed_items": len(content_list),
                        "filtered_items": len(filter_content_list),
                        "mapped_items": 0
                    }
                )
            
            # 5. 构建输出结果
            output_data = NewRegTableOutputModel(
                success=True,
                message="新规变化表作成完成",
                processed_items=len(content_list),
                filtered_items=len(filter_content_list),
                mapped_items=len(new_table_list),
                template_file=input_data.template_source.uri,
                requirement_file=input_data.requirement_source.uri,
                processing_details={
                    "target_sheet": target_sheet,
                    "mapping_rules": mapping_rules,
                    "header_config": header_config
                }
            )
            
            self.logger.info(f"新规变化表作成成功完成，处理了 {len(new_table_list)} 个项目")
            
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="新规变化表作成成功",
                data=output_data.model_dump()
            )
            
        except Exception as e:
            self.logger.exception(f"新规变化表作成工作流执行失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"新规变化表作成失败: {str(e)}",
                error=str(e)
            )

    def post_execute(self, result: WorkflowResult) -> None:
        """
        执行后清理工作
        
        Args:
            result: 工作流执行结果
        """
        try:
            # 关闭Excel工具类资源
            if self.excel_util:
                self.excel_util.close()
                self.excel_util = None
                
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.warning(f"资源清理时发生错误: {str(e)}")

    def register_config_model(self):
        """注册配置模型"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="new_reg_table")
        config_manager.register_schema("new_reg_table", NewRegTableConfigModel)
