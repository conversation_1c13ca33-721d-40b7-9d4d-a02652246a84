# IF整合性确认工作流配置

# 基本配置
name: "IF整合性确认"
description: "检查代码中接口使用与IF设计书的一致性"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入文件格式
  input:
    if_stylebook_extensions: [ ".xlsx", ".xls" ]  # IF设计书支持的文件格式

  # 输出文件配置
  output:
    default_output_dir: "C:\\sdw_output\\if_integration"  # 默认输出目录
    report_base_name: "IF整合性CS兼結果報告書" # 报告文件名基础
    template_file: "templates/IF整合性CS兼結果報告書_模板.xlsx"  # 报告模板文件


# 处理参数
processing:
  # IF设计书解析配置
  if_stylebook:
    sheet_name: "IF一覧"  # IF设计书工作表名称
    if_name_keyword: "I/F名"  # 用于定位起始行的关键字

  # 集成确认配置
  integration:
    status_values: [ "OK", "NG", "Pending" ]
    default_status: "Pending"

  # Excel输出配置
  output_excel_style:
    default_sheet: "DEFAULT_IF"  # 报告模板文件默认工作表名
    new_sheet: "IF"  # 报告模板文件默认工作表名
    data_start_row: 14
    data_start_col: 2
    special_cols: [ 7, 8, 9, 12 ]     # 不合并单元格

# 安全配置
security:
  allowed_extensions: [ ".xlsx", ".xls", ".c", ".h", ".cpp", ".hpp" ]

# LLM配置
llm:
  model: "gpt4o"
  system_prompt: |
    你是一个专业的软件工程师，负责分析软件接口使用的正确性。
    你需要根据接口设计规范检查代码中的接口调用是否符合要求。

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {message}" 