gerrit:
  url: http://************:8080 #"http://************:8085" "http://************:8080"
  username: "cr_robot"
  password: "ocsa@2024!"

ftp_host: "************"
ftp_user: "ai"
ftp_password: "dnkt$88"
ftp_remote_dir: "/gerrit23/"  # /gerrit19/
ftp_remote_dir_dscscc: "/gerrit23/dscscc/"

openai:
  base_url: "http://*************:11434/v1/"
  api_key: "ollama"


codereviewpoint:
  0: 
    name: 2.1检查扩展名、类型
    rule: >
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      扩展名类型
      禁止使用 .prm、.tbl 作为新源文件或头文件的扩展名（2018年1月后）。
      仅允许使用 .c、.h、.asm、.inc、.ld 等标准扩展名。
      例外：特定CPU或开发工具可能允许自定义扩展名（如 .arm、.850），需在说明中明确。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  1:
    name: 2.2C语言头文件的注意事项
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      头文件注意事项
      头文件必须包含防止重复包含的 #ifndef ... #endif。
      头文件内仅允许以下内容：
      #include 指令
      typedef 类型定义
      #define 宏定义
      函数原型声明（void func(...);）
      extern 变量声明（extern U1 var;）
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  2:
    name: 2.3.文字编码
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      文字编码
      所有文件必须使用 Shift_JIS-DOS 编码。
      禁止使用日语注释（需使用英文注释）。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  3:
    name: 2.4.缩进类型
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      缩进类型
      禁止使用制表符 \t 缩进，必须使用空格。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  4:
    name: 2.5.源文件和头文件内的定义、声明等顺序的规定
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      文件结构顺序
      源文件/头文件内需按以下顺序排列：
      #include
      #define
      类型定义（typedef）
      变量声明
      函数声明
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  5: 
    name: 3.1标准变量类型
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      标准变量类型
      禁止使用 C 语言原生类型（如 int、char），必须使用共通头文件 aip_common.h 中定义的类型。
      示例类型：
      U1（unsigned char）、S1（signed char）、U4（unsigned long）等。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  6:
    name: 3.2.结构体-共用体-枚举类型
    rule: >  
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      结构体需按typedef struct{...} ST_[module]_[variable];
      共用体类型禁止使用共用体，除非是微控制器厂商提供的寄存器定义,
      枚举类型typedef enum {...} EN_[module]_[variable];
      标识符总长≤32字符，模块名与变量名全大写。
      成员变量需符合3.3节规范，禁用位域类型，
      仅允许微控制器厂商的控制寄存器定义例外。
      命名需保持模块化与可读性，禁止非规范修改
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  7:
    name: 3.3.1.Version定义
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      Version 定义
      格式：
      [file name]_MAJOR([value])  
      [file name]_MINOR([value])  
      [file name]_PATCH([value])
      规则：
      MAJOR 从1开始，MINOR 和 PATCH 从0开始。
      MAJOR 更新需重置 MINOR 和 PATCH。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  8:
    name: 3.3.2字面量定义
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      字面量需按[module]_[literal] ([value])格式定义，
      标识符总长≤32字符，模块名与字面量名全大写。
      值类型要求：有符号整数禁用"U"后缀，无符号建议加"U"，浮点数必须加"F"。
      表达式需用括号明确计算顺序。注释需对齐至#define位置，数值定义前后必须加括号。
      若变量作为字面量使用，建议用const修饰。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  9:
    name: 3.3.3.#if…#endif 判定宏定义
    rule: >   
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      宏定义需按 __[module]_[option]__ ([value]) 格式，标识符总长≤32字符，模块名与选项名全大写。值类型要求：
      有符号整数禁止定义，无符号建议加“U”后缀
      浮点数禁止定义，表达式需用括号明确计算顺序
      禁止定义为0（考虑未定义场景）
      宏名需以双下划线包裹，仅头文件防重复包含宏可例外。注释需对齐至 #define 位置，数值定义前后加括号。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  10:
    name: 3.3.4.函数宏定义
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      函数宏定义规范
      函数宏仅允许以下场景使用：
      多处复用的字面量计算值（定义为literal/const）
      函数调用替代
      微控制器寄存器读写访问
      禁止其他函数宏使用。若仅在单C文件内使用，推荐用inline static函数替代。需确认编译器是否支持inline函数，参考编译器文档或与供应商确认。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  11:
    name: 3.4变量
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      变量格式：_type_ [dt]_[s][a]_[module]_[variable];
      type：标准类型（u1/u2/u4/u8/s1/s2/s4/s8）、结构体（st）、枚举（en）、函数指针（fp）、指针（u1p/stp等）、空指针（vdp）
      [dt]：变量类型（如u1、st等）
      [s]：作用域（t/函数域自动变量、a/参数变量、s/static变量、g/extern变量）
      [a]：数组（维数=0无修饰，维数=1用p，n维用p[n]）
      [module]：模块名（全大写）
      [variable]：变量名（有const时全大写，无const时小写）
      结构体成员省略[s]和[a]修饰符，标识符总长≤32字符。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  12:
    name: 3.5函数
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      函数格式：_type_ [dt]_[s]_[module][function]([parameter]…);
      type：返回值类型（u1/u2/u4/u8/s1/s2/s4/s8/st/en/u1p/stp/vdp）
      [dt]：返回值类型（如u1、st等）
      [s]：作用域（s/文件内static函数，g/文件内外extern函数）
      [module]：模块名（全大写）
      [function]：函数名（大小写字母）
      [parameter]：参数名需符合变量命名规则
      要求：
      函数原型声明必须定义形参
      不修改参数需加const
      头文件声明不加extern（允许使用）
      禁止MISRA-C共用体类型
      标识符总长≤32字节
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  13:
    name: 4.注释
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      代码注释
      1.不要使用ASCII以外的文字。禁止使用日语注释。
      2.不要使用“//”注释。
      3.不要嵌套注释。
      4.不要跨行使用注释。在每一行，分别使用“/*”和“*/”来注释。
      5.“/*”后面和“*/”的前面，插入空格。
      6.在定义static/global变量声明、结构体时，要在变量和结构体成员声明所在行，以行末注释的形式，追加数值的单位、含义等描述。推荐对函数的参数、函数内的auto变量，追加注释。
      7.每个函数定义，都需要加入注释。
      8.在源代码的最后，记录变更履历、变更理由等。
      9.添加注释的最低限，是不降低代码的可维护性。
      10.对代码的注释不限于在该行的行尾，可以在该行的前面数行进行缩排叙述。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
# 15: >
# 16: >
# 17: >
# 18: >
# 19: >
