# 测试代理24工作流配置

# 基本配置
name: "测试代理24"
description: "提供测试用例信息提取和自动填写CheckSheet功能"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入文件格式
  input:
    checksheet_extensions: [".xlsx", ".xls"]  # CheckSheet支持的文件格式
    target_file_extensions: [".xlsx", ".xls"] # 测试用例文件支持的文件格式
    
  # 输出文件配置
  output:
    default_output_dir: "./output"  # 默认输出目录
    output_suffix: "_output"         # 输出文件名后缀

# 处理参数
processing:
  # CheckSheet解析配置
  checksheet:
    extract_start_row: 11            # 开始提取内容的行号
    content_columns: [3, 4]          # 提取内容的列号 (C, D)

  # 向量检索配置
  vector_search:
    similarity_threshold: 0.5        # 相似度阈值
    top_k_global: 7                  # 全局前K个结果
    each_file_top_k: 10              # 每个文件前K个结果

  # 状态检测配置
  status:
    status_priority: ["O", "NG", "OK", "-"]  # 状态优先级顺序
    status_indicator_keywords:               # 状态指示关键词
      ok: ["OK"]
      ng: ["NG"]
      default: "O"                           # 默认状态

  # Excel输出配置
  excel_output:
    result_column: 5                 # 结果列号 (E)
    detail_column: 6                 # 详情列号 (F)
    filename_row: 8                  # 文件名行号
    filename_column: 5               # 文件名列号 (E)

# 嵌入配置
embedding:
  model: "text-embedding-3-large"    # 嵌入模型
  batch_size: 5                      # 批处理大小
  cache_dir: "./embeddings_cache"    # 嵌入缓存目录

# API配置
azure:
  openai_api_key: "8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1"
  openai_api_version: "2025-02-01-preview"
  openai_endpoint: "https://sdw-openai.openai.azure.com"
  embedding_deployment: "text-embedding-3-large"
  embedding_api_version: "2023-05-15"

# 日志配置
logging:
  level: "INFO"                      # 日志级别
  format: "{time} | {level} | {message}"  # 日志格式 