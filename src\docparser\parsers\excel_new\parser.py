# -*- coding: utf-8 -*-
"""
@File    : parser.py
<AUTHOR> zhenp
@Date    : 2025-06-19 16:42
@Desc    : Description of the file
"""
import os
import re
import tempfile
import time
from base64 import b64encode
from dataclasses import dataclass
from io import BytesIO
from pathlib import Path
from typing import Dict, Tuple, List

from docparser import DocumentObject
from docparser.core.base_parser import BaseParser
import webcolors

import pythoncom  # 导入 pythoncom 库
import win32com.client

import openpyxl
from openpyxl import Workbook
from PIL import Image, ImageGrab


from docparser.models import CellObject, DocumentBlockObject, Position,PictureObject, TableObject, LayoutObject, RowObject, GraphicObject, TextObject
from docparser.common.base import is_shape_in_cell, get_img_hash, calculate_longest_line_length
from docparser.common.theme_color import ThemeColor
from docparser.core.base_parser import BaseParser
from docparser.models.border import BorderObject
from docparser.parsers.excel.utils import point_to_character
from docparser.parsers.excel_new.base import get_no_border_cells_and_table_cells, get_table_range_from_boundary_set, \
    find_all_boundaries
from docparser.parsers.excel_new.utils import get_positions, CellUtil, shape_to_object
from docparser.utils.logging_utils import get_logger

logger = get_logger("excel.parser")

@dataclass
class SheetContext:
    max_row: int
    max_col: int
    total_width: int
    total_height: int
    cells: Dict[Tuple[int, int], CellObject]
    positions: Dict[Tuple[int, int], Position]
    left_top_position: Position

    def __init__(
            self
    ):
        self.max_row = 0
        self.max_col = 0
        self.total_width = 0
        self.total_height = 0
        self.cells = {}
        self.positions = {}
        self.left_top_position = Position()

class ExcelParser(BaseParser):
    _openpyxl_wb: Workbook
    _cell_util: CellUtil

    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.xlsx', '.xls', '.xlsm']
        self._win32com_wb = None
        self._excel_app = None
        self._shapes = []
        self._sheet_context = SheetContext()

    def _parse_document_content(self, file_path) -> None:
        self._init_excel_app()
        # Convert .xls to .xlsx if needed
        if file_path.lower().endswith('.xls'):
            file_path = self._convert_xls_to_xlsx(file_path)
        self._init_workbook(file_path)

        try:
            blocks = self._parse_document()
            self.document._document = blocks
        except Exception as e:
            logger.error("parse error", e)
        finally:
            self._release_resources()
            if file_path.startswith(str(tempfile.tempdir)):
                os.remove(file_path)

    def _parse_document(self) -> List[DocumentBlockObject]:
        theme_colors = ThemeColor().get_theme_colors(self._openpyxl_wb)
        self._cell_util = CellUtil(theme_colors)
        self._shapes = self._parse_shapes()
        blocks = []
        for name in self._openpyxl_wb.sheetnames:
            sheet = self._openpyxl_wb[name]
            block = self._parse_sheet(sheet)
            # print(block)
            blocks.append(block)
        return blocks

    def _parse_sheet(self, sheet) -> DocumentBlockObject:
        """
           将 Excel 工作表解析为结构化的文档块对象（DocumentBlockObject）。

           该方法从给定的工作表中提取单元格内容、表格区域、自由文本、图形和图片等元素，并将其组织成结构化的文档对象模型。
           解析过程包括：

           1. 初始化工作表上下文信息（最大行数、最大列数、总宽度、总高度、单元格位置等）；
           2. 解析单元格内容并存储到 `_sheet_context.cells` 中；
           3. 对图形对象进行归一化坐标处理，并将其绑定到对应的单元格；
           4. 检测并解析表格区域，构建 TableObject 列表；
           5. 解析非表格区域的自由文本内容；
           6. 调整超长文本的显示宽度；
           7. 提取工作表中的图片和图形对象；
           8. 合并同一单元格内的多个图形、图片或两者的组合；
           9. 最终构建成一个完整的 DocumentBlockObject 并返回。

           Args:
               sheet: openpyxl 的 Worksheet 对象，表示当前要解析的工作表。

           Returns:
               DocumentBlockObject: 包含表格（tables）、文本（texts）、图片（pictures）、图形（graphics）等内容的结构化文档块对象。

           Raises:
               无显式异常抛出，但内部可能记录日志错误。
           """
        self._sheet_context = SheetContext()
        self._sheet_context.max_row = sheet.max_row
        self._sheet_context.max_col = sheet.max_column
        positions, total_width, total_height = get_positions(sheet)
        self._sheet_context.total_width = total_width
        self._sheet_context.total_height = total_height
        self._sheet_context.positions = positions

        sheet_name = sheet.title
        block = DocumentBlockObject()

        self._parse_cells(sheet)
        # handle shapes position
        shapes = []
        for shape in self._shapes:
            if shape["sheet_name"] != sheet.title:
                continue
            position = shape["position"]
            position.x = position.x / total_width
            position.y = position.y / total_height
            position.width = position.width / total_width
            position.height = position.height / total_height
            shape["position"] = position
            if not shape["in_cell"]:
                continue
            row = shape["from_row"]
            col = shape["from_col"]
            obj = PictureObject()
            shape_to_object(shape, obj)
            cell_obj = self._sheet_context.cells[(row, col)]
            cell_obj.content.append(obj)
            shapes.append(shape)

        table_ranges, free_cells = self._get_start_end_of_table()
        tables = self._parse_tables(table_ranges, block)
        block.tables = tables

        texts = self._parse_text(block, free_cells)

        # 处理文字超长的cell宽度
        for current_text in texts:
            length = calculate_longest_line_length(current_text.text)
            width = length / self._sheet_context.total_width
            if width > current_text.position.width:
                current_text.position.width = width
        block.texts = texts
        pictures = self._parse_picture(sheet_name, block, shapes)
        block.pictures = pictures
        graphics = self._parse_graphic(sheet_name, block, shapes)
        block.graphics = graphics

        block._name = sheet_name

        # 合并虚拟单元格中的图形、图片
        # self._merge_graphic_pic_cross_multi_cell(block, ws_with_pywin32, sheet)
        # 合并同一个单元格中的图形
        self._merge_graphics(block, self._win32com_wb)
        # 合并同一个单元格图片
        self._merge_pictures(block, self._win32com_wb)
        # 合并同一个单元格中的图形 + 图片
        self._merge_graphics_and_pictures(block, self._win32com_wb)
        # 关闭

        block._position = self._sheet_context.left_top_position
        return block

    def _parse_cells(self, sheet):
        # left_top cell with content
        min_col = float('inf')
        min_row = float('inf')
        # parse info
        for row in sheet.rows:
            for cell in row:
                # if cell.value is None:
                #     continue
                if cell.column < min_col:
                    min_col = cell.column
                if cell.row < min_row:
                    min_row = cell.row
                cell_obj = self._cell_util.get_cell_info(cell)
                cell_obj.position = self._sheet_context.positions[(cell.row, cell.column)]
                self._sheet_context.cells[(cell.row, cell.column)] = cell_obj

        self._sheet_context.left_top_position = self._sheet_context.positions[(min_row, min_col)]
        # handle merged cell
        for merged_range in sheet.merged_cells.ranges:
            # 获取合并范围的边界
            start_col, start_row, end_col, end_row = list(merged_range.bounds)
            if (start_row, start_col) not in self._sheet_context.cells:
                continue
            start_cell = self._sheet_context.cells[(start_row, start_col)]
            start_cell._is_merged = True
            start_cell._is_merged_start = True
            start_cell.merged_ranges = [start_row, start_col, end_row, end_col]
            top = start_cell.border.border_top
            bottom = start_cell.border.border_bottom
            left = start_cell.border.border_left
            right = start_cell.border.border_right
            empty_border = BorderObject()
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    current_cell = self._sheet_context.cells[(row, col)]
                    current_cell.border.border_top = empty_border
                    current_cell.border.border_bottom = empty_border
                    current_cell.border.border_left = empty_border
                    current_cell.border.border_right = empty_border
                    if row == start_row:
                        current_cell.border.border_top = BorderObject(top.border_color, top.border_style)
                    if row == end_row:
                        current_cell.border.border_bottom = BorderObject(bottom.border_color, bottom.border_style)
                    if col == start_col:
                        current_cell.border.border_left = BorderObject(left.border_color, left.border_style)
                    if col == end_col:
                        current_cell.border.border_right = BorderObject(right.border_color, right.border_style)
                    # print(f"{(row, col)}: {current_cell.border}")
                    if (row, col) == (start_row, start_col):
                        continue
                    current_cell._is_merged = True
                    current_cell._is_merged_start = False
                    current_cell.style = start_cell.style
                    # print(f"{(row, col)}: {current_cell.style}")
                    current_cell.merged_ranges = start_cell.merged_ranges
                    current_cell.text = start_cell.text
                    current_cell.comment = start_cell.comment
                    for index, content in enumerate(start_cell.content):
                        if getattr(content, "_runs"):
                            current_cell.content[index]._runs = content._runs
                        if getattr(content, "text"):
                            current_cell.content[index].text = content.text

    def _parse_shapes(self, exclude_type_list=[4]):
        """
        使用win32com解析图形+图片
        :param file_path:
        :return:
        """
        results = []
        for sheet in self._win32com_wb.Sheets:
            # Sheet 隐藏或者特别隐藏
            if sheet.Visible in [0, 2]:
                continue

            # 遍历工作表中的所有形状
            for idx, shape in enumerate(sheet.Shapes):
                # type 4: 点; 1: 形状; 17:文本内容; 6: 形状的分组 13: 图片 ....
                if shape.Type in exclude_type_list:
                    logger.debug(f"img save continue, shape.Name: {shape.Name}")
                    continue

                # 判断图形图像是否在单元格内
                shape_in_cell = is_shape_in_cell(shape)

                position = Position()
                position.x = point_to_character(shape.left)
                position.y = shape.top
                position.width = point_to_character(shape.width)
                position.height = shape.height

                result = {"type": "shape", "sheet_name": sheet.Name,
                          "id": shape.ID, "shape_type": shape.Type, "name": shape.Name,
                          "width": int(shape.Width), "height": int(shape.Height),
                          "from_row": int(shape.TopLeftCell.Row), "from_col": int(shape.TopLeftCell.Column),
                          "top": int(shape.Top), "left": int(shape.Left),
                          "from_row_off": 0, "from_col_off": 0,
                          "to_col": 0, "to_col_off": 0, "to_row": 0, "to_row_off": 0,
                          "index": shape.TopLeftCell.Address.replace("$", ""),
                          "in_cell": shape_in_cell,
                          "position": position
                          }
                # 获取文本内容
                if shape.Type in [1, 17]:
                    try:
                        tt = shape.TextFrame2.TextRange.Text
                        result["content"] = shape.TextFrame2.TextRange.Text.strip('\r')
                        # parse_textbox_properties(shape)
                        # result["content_style"] =
                    except Exception as ex:
                        logger.warn(f"not found com shape.TextFrame2 sheet_name: {result['sheet_name']}"
                                     f"id: {result['id']} name: {result['name']} "
                                     f"type: {result['shape_type']}")
                # # 将形状导出为图片
                # output_path = os.path.join(output_folder, get_shapes_image_name(sheet.Name, shape.ID))
                try:
                    if float(shape.Height) <= 0 or float(shape.Width) <= 0:
                        img = None
                    else:
                        shape.CopyPicture(Appearance=1, Format=2)  # 复制形状为图片
                        time.sleep(0.2)
                        # 从剪贴板获取图像
                        img = ImageGrab.grabclipboard()

                    msg = f"id:{result['id']} name:{result['name']} shape_type:{result['shape_type']}"
                    # 如果剪贴板中有图像，保存它
                    if isinstance(img, Image.Image):
                        # img.save(output_path)
                        # result["path"] = os.path.abspath(output_path)

                        # 计算图片的base64
                        # 假设你已经有一个 PIL.Image.Image 对象
                        # image = Image.open(output_path)
                        # 创建一个字节流对象
                        buffered = BytesIO()
                        # 将图像保存到字节流中
                        img.save(buffered, format="PNG")
                        # 获取字节流的字节数据
                        result["data"] = b64encode(buffered.getvalue()).decode()

                        # 计算图片的hash
                        img_hash = get_img_hash(img)
                        result["hash"] = img_hash[0]
                        # time.sleep(0.1)
                        # logging.info(f"Saved image: {msg}")
                    else:
                        logger.warn(f"not get image from CopyPicture {msg}")
                except Exception as ex:
                    logger.error(f"img save error {ex}")
                results.append(result)
        return results

    def _parse_tables(self, table_ranges, document_block: DocumentBlockObject) -> List[TableObject]:
        tables: List[TableObject] = []
        table_ranges = sorted(table_ranges) # Sort the input coordinates to make sure two table_ranges are the same order
        print(f"new: {table_ranges}")
        total_cells = self._sheet_context.cells
        # print(total_cells)
        for index in range(len(table_ranges)):
            table_range = table_ranges[index]
            table = TableObject()
            table.data_id = index + 1
            table.layout = LayoutObject.new_with_parent_ref(document_block)
            (min_row, min_col), (max_row, max_col) = table_range
            for row_index in range(min_row, max_row + 1):
                row_obj = RowObject()
                row_obj.data_id = row_index
                # print(row_index)
                row_obj.row_index = row_index - min_row
                cells: List[CellObject] = []
                for col_index in range(min_col, max_col + 1):
                    cell_obj = total_cells.get((row_index, col_index))
                    cell_obj.layout = LayoutObject.new_with_parent_ref(row_obj)
                    # 处理表格边框
                    if not cell_obj.border.border_top.border_style:
                        if (row_index - 1, col_index) in total_cells:
                            cell_obj.border.border_top.border_style = total_cells[(row_index - 1, col_index)].border.border_bottom.border_style
                    if not cell_obj.border.border_bottom.border_style:
                        if (row_index + 1, col_index) in total_cells:
                            cell_obj.border.border_bottom.border_style = total_cells[(row_index + 1, col_index)].border.border_top.border_style
                    if not cell_obj.border.border_left.border_style:
                        if (row_index, col_index - 1) in total_cells:
                            cell_obj.border.border_left.border_style = total_cells[(row_index, col_index - 1)].border.border_right.border_style
                    if not cell_obj.border.border_right.border_style:
                        if (row_index, col_index + 1) in total_cells:
                            cell_obj.border.border_right.border_style = total_cells[(row_index, col_index + 1)].border.border_left.border_style

                    cell_obj.data_id = 10 * cell_obj.row_index + cell_obj.col_index
                    # 将 row 和 column 改为当前table的索引
                    cell_obj.row_index = cell_obj.row_index - min_row
                    #print(cell_obj.row_index)
                    cell_obj.col_index = cell_obj.col_index - min_col
                    if hasattr(cell_obj, "merged_ranges") and cell_obj.merged_ranges:
                        cell_obj.merged_ranges = [cell_obj.merged_ranges[0] - min_row,
                                                    cell_obj.merged_ranges[1] - min_col,
                                                    cell_obj.merged_ranges[2] - min_row,
                                                    cell_obj.merged_ranges[3] - min_col,
                                                    ]
                    cells.append(cell_obj)
                # print(cells)
                row_obj.layout = LayoutObject.new_with_parent_ref(table)
                row_obj.cells = cells
                table.rows.append(row_obj)
                # print(table.rows)
                if table.rows:
                    table.coordinate.desc = table.rows[0].cells[0].coordinate.desc
                    last_row = table.rows[len(table.rows) - 1]
                    table._last_coordinate = last_row.cells[len(last_row.cells) - 1].coordinate.desc
                    # print(table.last_coordinate)
            tables.append(table)
        return tables
    @staticmethod
    def _parse_picture(sheet_name, block, shapes) -> (List[PictureObject]):
        """
        解析文档对象的图片，组织成通用结构的对象结构
        :param sheet_name: sheet名称
        :param block:
        :param shapes: 图形列表
        :return: 图片对象
        """
        results = []
        for shape in shapes:
            if shape["sheet_name"] == sheet_name and shape["shape_type"] == 13 and not shape["in_cell"]:
                if shape["position"].width < 1 or shape["position"].height < 1:
                    continue
                # 构建图形对象
                g = PictureObject()
                shape_to_object(shape, g)
                g._layout = LayoutObject.new_with_parent_ref(block)
                results.append(g)
        return results
    @staticmethod
    def _parse_graphic(sheet_name, block, shapes) -> (List[GraphicObject]):
        """
        解析文档对象的图形，组织成通用结构的对象结构
        :param sheet_name: sheet名称
        :param block:
        :param shapes: 图形列表
        :return: 图形对象
        """
        results = []
        for shape in shapes:
            if (shape["sheet_name"] == sheet_name and not shape["in_cell"]
                    # 图片对象
                    and shape["shape_type"] != 13
                    # 文本框对象
                    # and shape["shape_type"] != 17
            ):
                if shape["position"].width < 1 or shape["position"].height < 1:
                    continue
                # 构建图形对象
                g = GraphicObject()
                shape_to_object(shape, g)
                g._layout = LayoutObject.new_with_parent_ref(block)
                # 赋值shape_type
                g.shape_type = int(shape["shape_type"])
                # 填充色
                fill_color = shape.get("fillcolor").split()[0] if shape.get("fillcolor") else None
                if fill_color is not None and re.match(r'^#[0-9A-Fa-f]{6}$', fill_color):
                    g.style.background_color = str(fill_color)
                elif fill_color is not None:
                    try:
                        fill_color = webcolors.name_to_hex(fill_color)
                        g.style.background_color = str(fill_color)
                    except:
                        logger.error(f"webcolors name to hex error-{fill_color}")

                g._text = shape.get("content", "")
                g._graphic_type = shape["type"]
                results.append(g)
        return results

    def _parse_text(self, block, out_table_cells) -> (List[TextObject]):
        """
        解析文档对象的文本，连续单元格作为一个TextObject对象
        :param sheet:
        :param block:
        :param table_range_list: 表格对象的范围
        :param out_table_cells: 表格外对象,跟表格的范围可能存在重复,需要先进行清晰
        :param shapes: shapes集合, 从shapes中查找文本框对象
        :return: 文本对象
        """
        # 按照excel的自然行列排序
        sorted_out_table_cells = sorted(out_table_cells, key=lambda cell: (cell[0], cell[1]))
        texts: List[TextObject] = []

        for text_cell in sorted_out_table_cells:
            # 当前单元格在表格范围内,则跳过
            cell_obj = self._sheet_context.cells[text_cell]
            v = cell_obj.text
            if not v or (isinstance(v, str) and v.strip() == ""):
                continue
            # 合并单元格只处理start的cell
            if cell_obj._is_merged == True and  cell_obj._is_merged_start == False:
                continue
            # content 都是 TextObject,通过 content.runs 获取run_object
            run_objs = cell_obj.content[0].runs
            # 检查当前单元格是否与之前的单元格连续
            current_text = self._find_text_object(text_cell, texts)
            if current_text:
                # 如果连续，则将当前单元格的内容添加到当前的 TextObject 中
                current_text.text += "\n" + cell_obj.text
                current_text.runs.extend(run_objs)
                current_text._cell_list.add(text_cell)
                current_position = cell_obj.position
                # 如果行号相同，则增加 width
                if current_text.coordinate.desc[1:] == f"{cell_obj.row_index}":
                    width = current_text.position.width
                    current_text.position.width = width + current_position.width
                    if current_position.height > current_text.position.height:
                        current_text.position.height = current_position.height
                else: # 列相同，增加height,如果新的cell宽度大于原with，则替换
                    height =  current_text.position.height
                    current_text.position.height = height + current_position.height
                    if current_position.width > current_text.position.width:
                        current_text.position.width = current_position.width
            else:
                # 如果不连续，则创建一个新的 TextObject
                current_text = TextObject()
                current_text._layout = LayoutObject.new_with_parent_ref(block)
                current_text._text = cell_obj.text
                current_text._style = run_objs[0].style if run_objs else None
                current_text._runs = run_objs
                current_text.coordinate.desc = cell_obj.coordinate.desc
                if not hasattr(current_text, '_cell_list'):
                    current_text._cell_list = set()
                current_text._cell_list.add(text_cell)
                current_text.position = cell_obj.position
                texts.append(current_text)
                current_text.data_id = len(block.tables) + len(texts)
        return texts

    def _merge_graphics(self, block, ws_with_pywin32):
        """ 合并同一个单元格内的图形 """
        pos_dict = {}
        pos_list = []  # 保持位置有序性

        # 统计每个位置的图形个数
        for graphic in block.graphics:
            pos = graphic.coordinate.desc
            if not pos:
                continue
            if pos not in pos_dict:
                pos_dict[pos] = [graphic]
                pos_list.append(pos)
            else:
                pos_dict[pos].append(graphic)

        # 逆序处理（便于删除、插入）有图形的坐标位置
        for pos in reversed(pos_list):
            g_list = pos_dict.get(pos)  # 获取当前位置的图形
            if len(g_list) == 1:
                # 当前位置只有一个图形，不做处理
                continue
            try:
                # 当前位置有多个图形，则进行图形的合并
                # 记录elements的插入位置
                ele_insert_idx = block.elements.index(g_list[0])
                # 记录graphics的插入位置
                graphic_insert_idx = block.graphics.index(g_list[0])
                # 开始处理
                merged_graphic = g_list[0]
                for g in g_list:
                    # 从列表中删除待合并的图形对象
                    block.elements.remove(g)
                    block.graphics.remove(g)
                # 获取合并的图形数据
                self._get_merged_graphic(ws_with_pywin32, pos, pos, merged_graphic)
                # 在指定的位置插入合并的图形
                block.elements.insert(ele_insert_idx, merged_graphic)
                block.graphics.insert(graphic_insert_idx, merged_graphic)
            except (ValueError, IndexError, pythoncom.com_error):
                logger.error("merge graphics inside same cell failed.")

    def _merge_pictures(self, block, ws_with_pywin32):
        """ 合并同一个单元格内的图片 """
        pos_dict = {}
        pos_list = []  # 保持位置有序性

        # 统计每个pos位置的图片个数
        for pic in block.pictures:
            pos = pic.coordinate.desc
            if not pos:
                continue
            if pos not in pos_dict:
                pos_dict[pos] = [pic]
                pos_list.append(pos)
            else:
                pos_dict[pos].append(pic)

        # 逆序处理（便于删除、插入）有图片的坐标位置
        for pos in reversed(pos_list):
            pic_list = pos_dict.get(pos)  # 获取当前位置的图片列表
            if len(pic_list) == 1:
                # 当前位置只有一个图片，不做处理
                continue
            try:
                # 当前位置有多个图片，则进行图片的合并
                # 记录elements的插入位置
                ele_insert_idx = block.elements.index(pic_list[0])
                # 记录pictures的插入位置
                picture_insert_idx = block.pictures.index(pic_list[0])
                # 开始处理
                merged_pic = pic_list[0]
                for cur_pic in pic_list:
                    # 从列表中删除待合并的图片对象
                    block.elements.remove(cur_pic)
                    block.pictures.remove(cur_pic)
                # 获取合并的图片数据
                self._get_merged_graphic(ws_with_pywin32, pos, pos, merged_pic)
                # 在指定的位置插入合并的图形
                block.elements.insert(ele_insert_idx, merged_pic)
                block.pictures.insert(picture_insert_idx, merged_pic)
            except (ValueError, IndexError, pythoncom.com_error):
                logger.error("merge pictures inside same cell failed.")

    def _merge_graphics_and_pictures(self, block, ws_with_pywin32):
        """ 合并同一个单元格的图形 + 图片 """
        pos_dict = {}
        pos_list = []  # 保持位置有序性

        # 统计每个pos位置的图片个数
        for pic in block.pictures:
            pos = pic.coordinate.desc
            if not pos:
                continue
            if pos not in pos_dict:
                pos_dict[pos] = [pic]
                pos_list.append(pos)
            else:
                pos_dict[pos].append(pic)
        # 统计每个pos位置的图形个数
        for graphic in block.graphics:
            pos = graphic.coordinate.desc
            if not pos or pos not in pos_dict:
                continue
            else:
                # 同一个cell中有图片 + 图形
                pos_dict[pos].append(graphic)

        # 逆序处理（便于删除、插入）有图片的坐标位置
        for pos in reversed(pos_list):
            pic_list = pos_dict.get(pos)  # 获取当前位置的图片列表
            if len(pic_list) == 1:
                # 当前位置只有一个图片，不做处理
                continue
            try:
                # 当前位置有图片+图形，则进行图片+图形的合并
                # 记录elements的插入位置
                ele_insert_idx = block.elements.index(pic_list[0])
                # 记录pictures的插入位置
                picture_insert_idx = block.pictures.index(pic_list[0])
                # 开始处理
                merged_pic = pic_list[0]
                for cur_pic in pic_list:
                    # 从列表中删除待合并的图片、图形对象
                    block.elements.remove(cur_pic)
                    if isinstance(cur_pic, PictureObject):
                        block.pictures.remove(cur_pic)
                    elif isinstance(cur_pic, GraphicObject):
                        block.graphics.remove(cur_pic)
                # 获取合并的图片数据
                self._get_merged_graphic(ws_with_pywin32, pos, pos, merged_pic)
                # 在指定的位置插入合并的图形
                block.elements.insert(ele_insert_idx, merged_pic)
                block.pictures.insert(picture_insert_idx, merged_pic)
            except (ValueError, IndexError, pythoncom.com_error):
                logger.error("merge pictures inside same cell failed.")

    def _get_merged_graphic(self, ws_with_pywin32, start_pos, end_pos, graphic_obj):
        """ 从当前worksheet中对应的pos坐标位置截取图片，图片数据存入graphic_obj中 """
        try:
            # 隐藏excel单元格的网格
            ws_with_pywin32.Application.ActiveWindow.DisplayGridlines = False
            # 获取当前范围的单元格
            cell_range = ws_with_pywin32.Range(f"{start_pos}:{end_pos}")
            cell_num = cell_range.Cells.Count
            temp_list = []
            for i in range(cell_num):
                cur_cell = cell_range.Cells(i+1)
                # 记录单元格内容
                temp_list.append((cur_cell, cur_cell.Value))
                cur_cell.Value = None
            # ws_with_pywin32.Range(f"{start_pos}:{end_pos}").CopyPicture()
            cell_range.CopyPicture()
            img = ImageGrab.grabclipboard()

            # 单元格内容恢复
            for c, v in temp_list:
                c.Value = v
        except Exception:
            img = None
        if img is not None:
            if start_pos != end_pos:
                # 去除图片中可能存在的边框
                img = self.remove_verbose_border(img)
            bio = BytesIO()
            img.save(bio, format="png")
            graphic_obj.data = b64encode(bio.getvalue()).decode()

    @staticmethod
    def remove_verbose_border(img, border_color=(0, 0, 0, 255)):
        """ 处理图片去除边框像素 """
        # 转为RGBA格式
        if img.mode != "RGBA":
            img = img.convert("RGBA")
        img_data = img.load()
        width, height = img.size

        # 去除上下黑色边框
        for r in [0, height - 1]:
            for c in range(width):
                if img_data[c, r] == border_color:
                    # 设置为透明
                    img_data[c, r] = (0, 0, 0, 0)

        # 去除左右的黑色边框
        for c in [0, width - 1]:
            for r in range(height):
                if img_data[c, r] == border_color:
                    img_data[c, r] = (0, 0, 0, 0)

        # 去除其他的横线边框
        for r in range(height):
            # 边框占用了1px，已改为透明，所以从第二个像素开始
            cur_pixel = img_data[1, r]
            if cur_pixel != (0, 0, 0, 0):
                for c in range(width):
                    # 暂时简化处理
                    if img_data[c, r] == cur_pixel:
                        img_data[c, r] = (0, 0, 0, 0)
        return img

    def _get_start_end_of_table(self):
        res = get_no_border_cells_and_table_cells(self._sheet_context.cells, self._sheet_context.max_row,
                                                  self._sheet_context.max_col)
        table_ranges = find_all_boundaries(res.table)
        return table_ranges, res.free

    @staticmethod
    def _find_text_object(current_cell, texts):
        """检查当前单元格是否连续, 并返回TextObject对象 """
        if not texts:
            return None
        (curr_row, curr_col) = current_cell
        return next(
            (text_ for text_ in texts
             for prev_row, prev_col in text_._cell_list
             if (curr_row == prev_row and curr_col == prev_col + 1)
             or (curr_row == prev_row + 1 and curr_col == prev_col)),
            None
        )

    def _release_resources(self):
        """释放资源，确保 openpyxl 和 COM 对象正确关闭."""
        try:
            # 关闭 openpyxl 的 Workbook 对象
            if self._openpyxl_wb is not None:
                self._openpyxl_wb.close()

            # 关闭 win32com 的 Workbook 和 Excel.Application
            if self._win32com_wb is not None:
                self._win32com_wb.Close(SaveChanges=False)
            if self._excel_app is not None:
                self._excel_app.Quit()

        finally:
            # 解除对 COM 对象的引用
            self._openpyxl_wb = None
            self._win32com_wb = None
            self._excel_app = None

            # 解除 COM 库的初始化
            pythoncom.CoUninitialize()

    def _init_excel_app(self):
        if self._excel_app is not None:
            return
        try:
            # 初始化 openpyxl 的 Workbook
            # 初始化 COM 环境和 Workbook
            pythoncom.CoInitialize()  # 初始化 COM 库
            self._excel_app = win32com.client.DispatchEx("Excel.Application")
            self._excel_app.Visible = False  # 隐藏 Excel GUI
            self._excel_app.DisplayAlerts = False  # 禁用提示框
        except pythoncom.com_error as com_error:
            logger.error(f"错误来源: {com_error.source}")  # 错误来源（通常是 COM 组件）
            logger.error(f"错误描述: {com_error.excepinfo[2]}")  # COM 返回的错误描述
            # 如果 COM 初始化失败，释放资源
            self._release_resources()
            raise
        except Exception as e:
            print(f"发生未知错误: {e}")
            # 如果任何异常发生，释放资源
            self._release_resources()
            raise

    def _init_workbook(self, file_path):
        if self._excel_app is None:
            self._init_excel_app()
        try:
            # 初始化 openpyxl 的 Workbook
            self._openpyxl_wb = openpyxl.load_workbook(file_path, rich_text=True)
            # 打开 Excel 文件作为 COM 对象
            self._win32com_wb = self._excel_app.Workbooks.Open(file_path)
        except pythoncom.com_error as com_error:
            logger.error(f"错误来源: {com_error.source}")  # 错误来源（通常是 COM 组件）
            logger.error(f"错误描述: {com_error.excepinfo[2]}")  # COM 返回的错误描述
            # 如果 COM 初始化失败，释放资源
            self._release_resources()
            raise
        except Exception as e:
            print(f"发生未知错误: {e}")
            # 如果任何异常发生，释放资源
            self._release_resources()
            raise

    def _convert_xls_to_xlsx(self, file_path):
        path = Path(file_path)

        temp_dir = Path(tempfile.gettempdir())  # 获取临时目录路径

        target_path = os.path.join(temp_dir, f"{path.stem}.xlsx")
        # 如果目标文件存在，先删除它
        if Path(target_path).exists():
            os.remove(target_path)

        wb = self._excel_app.Workbooks.Open(path.absolute())

        # FileFormat=51 is for .xlsx extension
        target_path = str(target_path)
        wb.SaveAs(target_path, FileFormat=51)
        # 关闭工作簿，不保存更改
        wb.Close(False)
        return target_path

if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/1220演示数据变更前.xlsx")
    file_path = os.path.normpath(file_path)
    # file_path = r"D:\work\RRM\REQ_Compare_Document\01REQ\0102Report\00 业务数据\fengtian\line5\line5变更前.xlsx"
    file_path = 'D:\\work\\Req_Diff_Test_Doc\\DHCP188源文件\\before\\3211_01101-HCU-新改3_各機能仕様(TFT専用項目)_11.割込み表示一覧.xlsx'
    new_parser = ExcelParser()
    new_parser._parse_document_content(file_path)
    new_res = new_parser.document.document