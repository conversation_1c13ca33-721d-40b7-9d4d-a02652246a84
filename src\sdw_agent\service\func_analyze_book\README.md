# 函数分析书工作流

## V字对应
-2.5.28 函数式样书作成

## 概述

函数分析书工作流专门处理函数变更分析任务。通过分析Git提交差异，提取变更的函数信息，使用AI分析函数的详细信息，并生成包含函数分析结果的Excel报告书。

## 主要功能

### 核心功能：函数变更分析
- **Git差异分析**: 分析Git提交间的代码差异，识别变更的函数
- **函数信息提取**: 从源代码中提取函数体、参数、返回值等详细信息
- **AI智能分析**: 使用大模型分析函数功能、参数含义、返回值描述等
- **关系分析**: 分析函数的调用关系和依赖关系
- **报告生成**: 生成包含完整函数分析信息的Excel报告书

### 分析流程
1. **Git差异分析**: 比较两个提交间的代码差异，识别变更的函数
2. **函数信息提取**: 从源文件中提取完整的函数体和签名信息
3. **AI功能分析**: 使用大模型分析函数的功能描述、参数含义等
4. **关系分析**: 分析函数的父子调用关系和模块归属
5. **类型判定**: 确定函数的开放类型（内部函数、域内公开、域外公开）
6. **报告书生成**: 将所有分析结果整合到Excel模板中

## 使用方法

### 基本使用

```python
from sdw_agent.service.func_analyze_book_service import FuncAnalyzeBookWorkflow
from sdw_agent.service.func_analyze_book_service.models import FunctionAnalyzeBookConfig

# 创建工作流实例
workflow = FuncAnalyzeBookWorkflow()

# 准备配置信息
config = FunctionAnalyzeBookConfig(
    code_path="/path/to/repository",
    commit_id_after="abc123def456",
    commit_id_before="def456abc123",  # 可选，用于本地Git比较
    component_book_url="/path/to/component_list.xlsx"
)

# 执行函数分析
result = workflow.execute(config)

print(f"执行状态: {result.success}")
print(f"函数分析书路径: {result.func_book_url}")
print(f"分析的函数数量: {len(result.func_change_info_list)}")
```

### 高级使用

```python
# 使用自定义配置
workflow = FuncAnalyzeBookWorkflow(config_path="/path/to/custom_config.yaml")

# 执行并获取详细结果
result = workflow.execute(config)

if result.success:
    # 获取函数变更信息列表
    func_list = result.func_change_info_list
    for func_detail in func_list:
        print(f"函数名: {func_detail.func_name}")
        print(f"函数类型: {func_detail.functionType}")
        print(f"函数描述: {func_detail.func_desc}")
        print(f"返回值描述: {func_detail.return_desc}")
        print(f"参数信息: {[p.name for p in func_detail.parameters]}")
        print(f"调用关系: {len(func_detail.func_parents)} 个父函数")
        print("-" * 50)
    
    print(f"函数分析书已生成: {result.func_book_url}")
else:
    print(f"执行失败: {result.message}")
    if result.error_details:
        print(f"错误详情: {result.error_details}")
```

### 使用兼容接口

```python
from sdw_agent.service.func_analyze_book_service import gen_function_book_multi_commit_service

# 使用旧版兼容接口
try:
    func_book_url = gen_function_book_multi_commit_service(
        code_path="/path/to/repository",
        commit_id_after="abc123def456",
        commit_id_before="def456abc123",
        component_book_url="/path/to/component_list.xlsx"
    )
    print(f"函数分析书已生成: {func_book_url}")
except Exception as e:
    print(f"执行失败: {str(e)}")
```

### Gerrit集成使用

```python
from sdw_agent.service.func_analyze_book_service import gen_function_book_service

# 使用Gerrit方式分析
try:
    func_book_url = gen_function_book_service(
        code_path="/path/to/repository",
        commit_id="abc123def456",
        component_book_url="/path/to/component_list.xlsx",
        gerrit_url="https://gerrit.example.com"
    )
    print(f"函数分析书已生成: {func_book_url}")
except Exception as e:
    print(f"执行失败: {str(e)}")
```

## 输入参数说明

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `config` | FunctionAnalyzeBookConfig | 是 | 函数分析配置对象 |

### FunctionAnalyzeBookConfig 字段

| 字段名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `code_path` | str | 是 | - | 代码仓库路径 |
| `commit_id_after` | str | 是 | - | 目标提交ID |
| `commit_id_before` | str | 否 | None | 基准提交ID（本地Git比较时使用） |
| `component_book_url` | str | 是 | - | 组件列表Excel文件路径 |
| `gerrit_url` | str | 否 | None | Gerrit服务器URL（Gerrit方式时使用） |
| `username` | str | 否 | None | 用户名（Gerrit认证） |
| `password` | str | 否 | None | 密码（Gerrit认证） |

## 配置说明

工作流配置文件 `config.yaml` 包含以下配置项：

```yaml
# 基本配置
name: "函数分析书生成"
description: "基于Git提交生成函数变更分析书"
version: "1.0.0"
author: "SDW-Team"

# 处理参数
processing:
  git:
    diff_timeout: 30  # Git差异分析超时时间（秒）
  llm:
    max_workers: 5  # 并行处理的最大线程数
    timeout: 30  # LLM调用超时时间（秒）
    retry_attempts: 3  # 重试次数
  function:
    extract_timeout: 10  # 函数提取超时时间（秒）

# 输出配置
output:
  default_output_dir: "C:\\sdw_output"  # 默认输出目录
  template_file: "templates/func_book_sample.xlsx"  # 函数分析书模板
```

## 输入文件格式

### 组件列表文件
组件列表应为Excel格式，包含"SW-C List_R-Car"工作表，至少包含以下列：

| 列名 | 必需 | 描述 |
|------|------|------|
| `文件路径` | 是 | 源文件的相对路径 |
| `コンポーネント` | 是 | 组件名称 |

**示例格式：**
```
| 文件路径                    | コンポーネント |
|---------------------------|-------------|
| src/module1/file1.c       | Module1     |
| src/module2/file2.c       | Module2     |
```

### 代码仓库
支持标准的Git仓库，包括：
- **本地仓库**: 本地文件系统中的Git仓库
- **Gerrit集成**: 通过Gerrit API获取代码差异
- **提交比较**: 支持两个提交ID之间的差异分析

## 输出文件格式

生成的函数分析书为Excel格式，包含以下信息：

| 列名 | 描述 |
|------|------|
| `函数名` | 函数名称 |
| `函数签名` | 完整的函数声明 |
| `函数描述` | AI生成的函数功能描述 |
| `返回值类型` | 函数返回值类型 |
| `返回值描述` | AI生成的返回值含义描述 |
| `参数列表` | 函数参数信息 |
| `参数描述` | 每个参数的AI生成描述 |
| `函数类型` | 内部函数/域内公开/域外公开 |
| `调用关系` | 父函数和子函数调用关系 |
| `所属模块` | 函数所属的组件模块 |
| `函数体` | 完整的函数实现代码 |
| `流程描述` | AI生成的函数执行流程 |

**输出文件命名规则：**
```
函数分析书_YYYYMMDD_HHMMSS.xlsx
```

## 错误处理

工作流包含完善的错误处理机制：

### 输入验证错误
```python
# 代码路径不存在
ValidationError: 代码路径不存在: /path/to/repository

# 提交ID格式错误
ValidationError: 目标提交ID不能为空

# 组件书文件不存在
ValidationError: 组件书文件不存在: /path/to/component_list.xlsx
```

### 运行时错误
```python
# Git操作失败
RuntimeError: Git差异分析失败: fatal: bad revision 'abc123'

# 函数提取失败
RuntimeError: 函数体提取失败: 文件不存在或无法读取

# LLM调用失败
RuntimeError: AI分析失败: API调用超时
```

### 错误日志示例
```
2024-01-15 10:30:15 | ERROR | 函数分析书工作流执行失败: Git差异分析失败
2024-01-15 10:30:15 | INFO  | 开始清理临时文件
```

## 注意事项

### 环境要求
1. **Python版本**: 需要Python 3.8+
2. **依赖包**: 确保安装所有必需的依赖包
3. **Git工具**: 系统需要安装Git命令行工具
4. **网络连接**: 需要稳定的网络连接用于AI调用

### 文件要求
1. **代码仓库**: 必须是有效的Git仓库
2. **组件列表**: 必须是有效的Excel格式（.xlsx或.xls）
3. **工作表名称**: 组件列表必须包含"SW-C List_R-Car"工作表
4. **文件权限**: 确保对输入文件有读取权限，对输出目录有写入权限

### 性能考虑
1. **大型仓库**: 大型代码仓库分析可能需要较长时间
2. **函数数量**: 大量函数分析会增加处理时间
3. **并发限制**: 默认最大5个并发LLM调用，可通过配置调整
4. **内存使用**: 大文件处理可能消耗较多内存

## 性能优化

### 1. 并行处理优化
```python
# 配置文件中调整并发数
processing:
  llm:
    max_workers: 10  # 增加并发线程数（根据系统性能调整）
    timeout: 60      # 增加超时时间
```

### 2. 缓存机制
- **函数体缓存**: 缓存已提取的函数体
- **分析结果缓存**: 缓存相同函数的AI分析结果
- **Git差异缓存**: 缓存Git差异分析结果

### 3. 批量处理
```python
# 批量处理多个提交
commits = ["commit1", "commit2", "commit3"]
results = []

for commit in commits:
    config = FunctionAnalyzeBookConfig(
        code_path="/path/to/repo",
        commit_id_after=commit,
        component_book_url="/path/to/component_list.xlsx"
    )
    result = workflow.execute(config)
    results.append(result)
```

### 4. 资源管理
- **临时文件清理**: 自动清理临时文件和缓存
- **内存管理**: 及时释放大对象的内存
- **连接池**: 复用HTTP连接以提高效率

## 扩展功能

### 自定义模板
```python
# 使用自定义函数分析书模板
from sdw_agent.service.func_analyze_book_service.util import ExcelBookUtils

custom_template = "/path/to/custom_template.xlsx"
func_book_url = ExcelBookUtils.write_function_book(custom_template, func_change_info_list)
```

### 自定义分析逻辑
```python
# 扩展函数分析逻辑
class CustomFuncAnalyzeWorkflow(FuncAnalyzeBookWorkflow):
    def _gen_change_detail_service(self, func_detail, config):
        # 自定义函数详情分析逻辑
        super()._gen_change_detail_service(func_detail, config)
        
        # 添加自定义分析
        func_detail.custom_analysis = self._custom_analysis(func_detail)
```

### 多仓库支持
```python
# 分析多个代码仓库
repos = [
    "/path/to/repo1",
    "/path/to/repo2",
    "/path/to/repo3"
]

for repo_path in repos:
    config = FunctionAnalyzeBookConfig(
        code_path=repo_path,
        commit_id_after="latest_commit",
        component_book_url="/path/to/component_list.xlsx"
    )
    result = workflow.execute(config)
    print(f"仓库 {repo_path} 分析完成: {result.func_book_url}")
```

### 结果合并
```python
# 合并多次分析的结果
from sdw_agent.service.func_analyze_book_service.util import ExcelBookUtils

all_func_details = []
for result in results:
    if result.success:
        all_func_details.extend(result.func_change_info_list)

# 生成合并的函数分析书
template_path = ExcelBookUtils.get_sample_book_path()
merged_book = ExcelBookUtils.write_function_book(template_path, all_func_details)
```

## 故障排除

### 常见问题

**Q: Git差异分析失败**
```
A: 检查Git仓库状态、提交ID是否存在、Git工具是否正确安装
   确保有足够的权限访问仓库
```

**Q: 函数体提取失败**
```
A: 检查源文件是否存在、文件编码是否正确
   确保函数名称和文件路径匹配
```

**Q: AI分析超时**
```
A: 增加timeout配置值，检查网络连接稳定性
   减少max_workers数量以降低并发压力
```

**Q: Excel报告生成失败**
```
A: 检查输出目录权限、磁盘空间是否充足
   确保模板文件存在且格式正确
```

### 调试模式
```python
# 启用详细日志
import logging
logging.getLogger("sdw_agent").setLevel(logging.DEBUG)

# 使用调试配置
workflow = FuncAnalyzeBookWorkflow()
workflow.logger.setLevel(logging.DEBUG)
```

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志和堆栈跟踪
2. 输入文件和配置信息
3. 系统环境和版本信息
4. 复现步骤和预期结果

**开发团队**: SDW-Team  
**版本**: 1.0.0  
**最后更新**: 2024年1月