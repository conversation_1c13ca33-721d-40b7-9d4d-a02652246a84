import os
import pathlib
from typing import List, Optional, Dict, Any
from openpyxl import load_workbook
from loguru import logger
import yaml

from sdw_agent.service import BaseWorkflow, register_workflow
from sdw_agent.service.auto_action.models import (
    QueryStatus, QueryConfigModel, QueryResult, AutoActionResult
)
from sdw_agent.llm.llm_util import get_ai_message
from langchain.prompts import ChatPromptTemplate

@register_workflow("auto_action")
class AutoActionWorkflow(BaseWorkflow):
    """
    自动化操作工作流
    
    提供基于用户输入的智能文档查询和建议生成功能。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.register_config_model()
        # 加载 config.yaml
        config_file = os.path.join(os.path.dirname(__file__), "config.yaml")
        with open(config_file, "r", encoding="utf-8") as f:
            self._config_yaml = yaml.safe_load(f)
        # 初始化状态变量
        self.base_dir = None
        self.user_input = None
        self.output_dir = None
        
    def register_config_model(self):
        """注册配置模型"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="auto_action")
        config_manager.register_schema("auto_action", QueryConfigModel)
        
    def validate_input(self, user_input: str, base_dir: Optional[str] = None) -> bool:
        """验证输入参数"""
        if not user_input:
            self.logger.error("用户输入不能为空")
            return False
            
        # 设置基础目录
        if base_dir is None:
            base_dir = os.path.join(os.path.dirname(__file__), '性能测试手顺')
            
        base_dir_obj = pathlib.Path(base_dir)
        if not base_dir_obj.exists():
            self.logger.error(f"基础目录不存在: {base_dir}")
            return False
            
        # 保存参数
        self.base_dir = str(base_dir_obj)
        self.user_input = user_input
        self.output_dir = os.path.dirname(__file__)
        
        return True
        
    def query_model(self, content: str, prompt_template: str) -> str:
        """调用模型获取结果"""
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题"),
            ("user", "{question}"),
        ])
        
        invoke_dict = {
            "question": prompt_template.format(content=content)
        }
        
        llm_response = get_ai_message(chat_prompt, invoke_dict)
        return llm_response.content
        
    def extract_text_from_excel(self, file_path: str) -> str:
        """从Excel文件中提取文字内容"""
        wb = load_workbook(file_path, data_only=True)
        text_content = []
        
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            sheet_text = []
            
            for row in ws.iter_rows(values_only=True):
                row_text = []
                for cell_value in row:
                    if cell_value is not None:
                        cell_text = str(cell_value).strip()
                        if cell_text:
                            row_text.append(cell_text)
                if row_text:
                    sheet_text.append(' '.join(row_text))
                    
            if sheet_text:
                text_content.append(f"=== {sheet_name} ===")
                text_content.extend(sheet_text)
                
        wb.close()
        return '\n'.join(text_content)
        
    async def execute(self, user_input: str, base_dir: Optional[str] = None) -> AutoActionResult:
        """执行工作流"""
        if not self.validate_input(user_input, base_dir):
            return AutoActionResult(
                status=QueryStatus.FAILED,
                message="参数校验失败",
                error="输入参数无效"
            )
            
        try:
            # 1. 确定数据库
            self.logger.info("步骤1: 确定相关数据库")
            db_prompt = self._get_database_prompt()
            decide_kb = self.query_model(self.user_input, db_prompt)
            
            if not decide_kb or decide_kb == "非功能需求":
                return AutoActionResult(
                    status=QueryStatus.NO_RESULT,
                    message="未找到相关数据库或为非功能需求"
                )
                
            # 2. 提取相关文件内容
            self.logger.info("步骤2: 提取相关文件内容")
            query_result = await self._process_files(decide_kb)
            
            if not query_result.file_paths:
                return AutoActionResult(
                    status=QueryStatus.NO_RESULT,
                    message="未找到相关文件",
                    data=query_result
                )
                
            # 3. 生成建议
            self.logger.info("步骤3: 生成建议")
            suggest_prompt = self._get_suggestion_prompt(query_result)
            suggest_info = self.query_model(self.user_input, suggest_prompt)
            
            # 4. 保存结果
            output_md_path = os.path.join(self.output_dir, "output.md")
            with open(output_md_path, "w", encoding="utf-8") as f:
                f.write(suggest_info)
                
            query_result.md_path = output_md_path
            
            return AutoActionResult(
                status=QueryStatus.SUCCESS,
                message="查询完成",
                data=query_result
            )
            
        except Exception as e:
            self.logger.exception("执行过程发生异常")
            return AutoActionResult(
                status=QueryStatus.FAILED,
                message=f"执行失败: {str(e)}",
                error=str(e)
            )
            
    def _get_database_prompt(self) -> str:
        """从 config.yaml 获取数据库选择的prompt"""
        return self._config_yaml["prompts"]["database_decision"]

    def _get_suggestion_prompt(self, query_result: QueryResult) -> str:
        """从 config.yaml 获取建议生成的prompt，并格式化内容"""
        prompt_template = self._config_yaml["prompts"]["suggestion"]
        return prompt_template.format(
            excel_content=query_result.content,
            file_paths="; ".join(query_result.file_paths)
        )
        
    async def _process_files(self, decide_kb: str) -> QueryResult:
        """处理相关文件"""
        path = os.path.join(self.base_dir, decide_kb)
        file_paths = []
        all_content = ""
        
        if os.path.isdir(path):
            excel_contents = {}
            for fname in os.listdir(path):
                if fname.endswith((".xlsx", ".xls")) and not fname.startswith('~$'):
                    file_path = os.path.join(path, fname)
                    file_paths.append(file_path)
                    text_content = self.extract_text_from_excel(file_path)
                    if text_content:
                        excel_contents[fname] = text_content
                        
            for filename, content in excel_contents.items():
                all_content += f"\n--- {filename} ---\n"
                all_content += content
                all_content += "\n" + "-" * 50
                
        return QueryResult(
            file_paths=file_paths,
            content=all_content
        )

async def main(user_input: str, base_dir: Optional[str] = None):
    """主函数入口"""
    workflow = AutoActionWorkflow()
    result = await workflow.execute(user_input, base_dir)
    
    if result.status == QueryStatus.SUCCESS:
        print(f"查询成功")
        print(f"输出文件: {result.data.md_path}")
        print(f"相关文件数: {len(result.data.file_paths)}")
    else:
        print(f"查询失败: {result.message}")
        if result.error:
            print(f"错误详情: {result.error}")
    return result

if __name__ == "__main__":
    import asyncio
    user_input = "如何进行RAM测试?"
    asyncio.run(main(user_input))