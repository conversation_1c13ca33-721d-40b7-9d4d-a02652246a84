"""
要件分析工作流工具类

V字对应：
1.3 要件分析
2. 要求仕様読み合わせ（客先、部内、チーム内）

提供Excel操作、文件生成等工具功能。
"""

import pathlib
from datetime import datetime
from typing import Dict, Any

from loguru import logger
from sdw_agent.config.env import ENV
from sdw_agent.service.guideline_manager import GuidelineManager
from sdw_agent.util.excel.core import ExcelUtil
from sdw_agent.util.select_util import select_one
from sdw_agent.util.file_util import design_police_to_excel
from sdw_agent.service.workflow_config import WorkflowConfigManager
from sdw_agent.service.request_analyze.models import EpicWriteData, DesignPolicyData


def _get_config():
    """获取要件分析工作流配置"""
    try:
        config_manager = WorkflowConfigManager(workflow_name="request_analyze")
        return config_manager.get_config()
    except Exception:
        return {}


class EpicWriter:
    """要件一览表写入工具类"""

    def __init__(self):
        self.logger = logger.bind(component="EpicWriter")
        self.config = _get_config()

    def write_epic(self, data: EpicWriteData) -> None:
        """
        写入要件一览表
        
        Args:
            data: 要件一览表写入数据
            
        Raises:
            Exception: 当找不到匹配的要件或写入失败时
        """
        try:
            guideline_manager = GuidelineManager()
            role_name = guideline_manager.get_rule(data.guideline_key).role_name

            with ExcelUtil(data.epic_path, engine='openpyxl') as excel:
                sheet_name_config = self.config.get('excel_constants', {}).get('sheet_name', '要件一览')
                sheet_name = excel.get_sheet_name_fuzzy(sheet_name_config)
                res = excel.read_range(sheet_name, 'A1:A8')
                res = [i[0] for i in res]
                header_end_row = 1
                while True:
                    if res[header_end_row - 1]:
                        break
                    header_end_row += 1

                res = excel.read_excel_with_multi_level_headers(
                    sheet_name=sheet_name,
                    header_start_row=3,
                    header_end_row=header_end_row,
                    data_start_row=header_end_row + 1,
                )

                # 获取配置值
                excel_config = self.config.get('excel_constants', {})
                summary_col_name = excel_config.get('summary_col', '概要')
                role_col_prefix = excel_config.get('role_col_prefix', 'HMI组 开发担当')
                comprehend_col_name = excel_config.get('comprehend_col', 'HMI 变更点的理解')
                need_notify_col_name = excel_config.get('need_notify_col', 'HMI 对应要否')
                not_notify_reason_name = excel_config.get('not_notify_reason', 'HMI 对应否理由')
                influence_col_name = excel_config.get('influence_col', 'HMI 对应要变更点影响分析')

                summary_col = select_one(summary_col_name, list(res.columns))
                epic_row_index_list = list(res[res[summary_col] == data.change_summary].index)

                if not epic_row_index_list:
                    raise Exception('根据概要信息无法找出匹配的要件')

                # 获取各列索引
                role_col = select_one(f'{role_col_prefix} {role_name}', list(res.columns))
                role_col_index = res.columns.get_loc(role_col)

                comprehend_col = select_one(comprehend_col_name, list(res.columns))
                comprehend_col_index = res.columns.get_loc(comprehend_col)

                need_notify_col = select_one(need_notify_col_name, list(res.columns))
                need_notify_col_index = res.columns.get_loc(need_notify_col)

                reason_col = select_one(not_notify_reason_name, list(res.columns))
                reason_col_index = res.columns.get_loc(reason_col)

                influence_col = select_one(influence_col_name, list(res.columns))
                influence_col_index = res.columns.get_loc(influence_col)

            # 写入数据
            # 获取配置值
            need_notify_yes = excel_config.get('need_notify_yes', '要')
            need_notify_no = excel_config.get('need_notify_no', '否')
            workflow_config = self.config.get('workflow_constants', {})
            default_role_name = workflow_config.get('default_role_name', 'SDW Agent')
            default_dash = workflow_config.get('default_dash_value', '-')

            with ExcelUtil(data.epic_path) as excel:
                with excel.worksheet(sheet_name) as sheet:
                    for i in epic_row_index_list:
                        row_offset = header_end_row + 1 + i
                        sheet.write_cell(row_offset, role_col_index + 1, default_role_name)

                        if data.need_notify:
                            sheet.write_cell(row_offset, need_notify_col_index + 1, need_notify_yes)
                            sheet.write_cell(row_offset, reason_col_index + 1, default_dash)
                            sheet.write_cell(row_offset, comprehend_col_index + 1, data.comprehend)
                            sheet.write_cell(row_offset, influence_col_index + 1, data.influence)
                        else:
                            sheet.write_cell(row_offset, need_notify_col_index + 1, need_notify_no)
                            sheet.write_cell(row_offset, reason_col_index + 1, data.reason)
                            sheet.write_cell(row_offset, comprehend_col_index + 1, default_dash)
                            sheet.write_cell(row_offset, influence_col_index + 1, default_dash)
                excel.save()

            self.logger.info(f"成功写入要件一览表: {data.epic_path}")

        except Exception as e:
            self.logger.error(f"写入要件一览表失败: {str(e)}")
            raise


class DesignPolicyWriter:
    """设计评价方针写入工具类"""

    def __init__(self):
        self.logger = logger.bind(component="DesignPolicyWriter")
        self.config = _get_config()

    def write_design_policy(self, data: DesignPolicyData) -> str:
        """
        生成设计评价方针文件
        
        Args:
            data: 设计评价方针数据
            
        Returns:
            str: 生成的文件路径
        """
        try:
            # 获取配置值
            workflow_config = self.config.get('workflow_constants', {})
            failure_modes = workflow_config.get('failure_modes', [
                ["実行されない", "-", ""],
                ["実行順序が間違っている", "-", ""],
                ["処理時間がオーバーしている", "-", ""],
                ["処理が間違っている", "○", ""],
                ["データのTimingが間違っている", "-", ""],
                ["Memoryに誤って書き込まれている", "-", ""],
                ["入力データが間違っている", "-", ""]
            ])
            excel_config = self.config.get('excel_constants', {})
            design_policy_prefix = excel_config.get('design_policy_sheet_prefix', 'design_policy_')

            content = {
                data.change_summary: [
                    {
                        "設計方針": data.design_police,
                        "心配な点（該当する故障モードに○を記入）": failure_modes,
                        "担心点具体内容": data.failure_mode + data.concerns,
                        "設計": data.design_way,
                        "評価": data.evaluation_way,
                    }
                ]
            }

            design_police_file = pathlib.Path(
                f"{ENV.config.output_data_path}/design_policy/"
                f"{design_policy_prefix}{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            design_police_file.parent.mkdir(parents=True, exist_ok=True)

            design_police_to_excel(content, str(design_police_file))

            self.logger.info(f"成功生成设计评价方针文件: {design_police_file}")
            return str(design_police_file)

        except Exception as e:
            self.logger.error(f"生成设计评价方针文件失败: {str(e)}")
            raise


class RequirementAnalyzer:
    """需求分析工具类"""

    def __init__(self):
        self.logger = logger.bind(component="RequirementAnalyzer")
        self.config = _get_config()

    def process_p_no(self, p_no: str) -> str:
        """
        处理要件票号
        
        Args:
            p_no: 原始要件票号
            
        Returns:
            str: 处理后的要件票号，如果不符合格式则返回None
        """
        if not p_no or not p_no.strip().startswith("PROCMGT"):
            return None
        return p_no.strip()

    def create_other_category_result(self, high_score: bool = True) -> Dict[str, Any]:
        """
        创建"其它"分类结果
        
        Args:
            high_score: 是否为高分（当其他分类都不匹配时）
            
        Returns:
            Dict[str, Any]: 其它分类结果
        """
        # 获取配置值
        workflow_config = self.config.get('workflow_constants', {})
        other_category_name = workflow_config.get('other_category_name', '其它')
        other_category_reason = workflow_config.get('other_category_reason', '其它类型均不匹配')
        score_high = workflow_config.get('other_category_score_high', 1)
        score_low = workflow_config.get('other_category_score_low', 0)

        return {
            "name": other_category_name,
            "score": score_high if high_score else score_low,
            "reason": other_category_reason,
        }
