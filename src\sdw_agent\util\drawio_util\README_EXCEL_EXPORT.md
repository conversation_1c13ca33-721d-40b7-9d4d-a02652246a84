# Draw.io 导出到 Excel 工具

本工具包提供了将 Draw.io 图表快速插入到 Excel 中的功能，支持多种导出方式。

## 功能特性

### 🎯 主要功能
- **直接图像插入**: 将 Draw.io 图表作为图像直接插入到 Excel 中
- **结构化数据导出**: 将 Draw.io 的模块结构导出为 Excel 表格
- **批量处理**: 支持多个 Draw.io 文件批量导出
- **高亮集成**: 与现有的高亮功能无缝集成

### 📋 支持的导出方式
1. **完整图像导出** - 需要 draw.io desktop 应用
2. **简化结构导出** - 不依赖外部应用，生成结构图和数据表

## 快速开始

### 方式1：完整图像导出（推荐）

```python
from sdw_agent.util.drawio_util import DrawioToExcelExporter

# 创建导出器
exporter = DrawioToExcelExporter()

# 导出单个文件
result = exporter.insert_drawio_to_excel(
    drawio_file="architecture.drawio",
    excel_file="output.xlsx",
    sheet_name="架构图",
    title="系统架构图",
    max_width=1000,
    max_height=700
)

print(f"导出结果: {result['success']}")
```

### 方式2：简化结构导出

```python
from sdw_agent.util.drawio_util.simple_drawio_to_excel import SimpleDrawioToExcelExporter

# 创建简化导出器
exporter = SimpleDrawioToExcelExporter()

# 导出结构分析
result = exporter.insert_drawio_structure_to_excel(
    drawio_file="architecture.drawio",
    excel_file="structure_analysis.xlsx",
    include_structure_image=True,
    include_module_list=True
)
```

## 详细使用说明

### 环境要求

#### 完整图像导出
- **必需**: draw.io desktop 应用程序
- **安装方法**:
  - Windows: 从 [GitHub Releases](https://github.com/jgraph/drawio-desktop/releases) 下载
  - macOS: `brew install --cask drawio`
  - Linux: 下载 AppImage 或使用包管理器

#### 简化结构导出
- **无需外部依赖**，使用项目内置功能

### 使用示例

#### 1. 单个文件导出

```python
from sdw_agent.util.drawio_util import DrawioToExcelExporter

exporter = DrawioToExcelExporter()

# 基本导出
result = exporter.insert_drawio_to_excel(
    drawio_file="diagram.drawio",
    excel_file="output.xlsx"
)

# 高级选项
result = exporter.insert_drawio_to_excel(
    drawio_file="diagram.drawio",
    excel_file="output.xlsx",
    sheet_name="自定义工作表",
    start_row=3,
    start_col=2,
    title="图表标题",
    max_width=800,
    max_height=600,
    scale=1.5
)
```

#### 2. 批量文件导出

```python
# 准备文件列表
drawio_files = [
    "diagram1.drawio",
    "diagram2.drawio", 
    "diagram3.drawio"
]

# 批量导出
result = exporter.batch_insert_drawio_to_excel(
    drawio_files=drawio_files,
    excel_file="all_diagrams.xlsx",
    sheet_name="所有图表",
    images_per_row=2,
    max_width=500,
    max_height=400
)
```

#### 3. 结合高亮功能

```python
from sdw_agent.util.drawio_util import highlight_leaf_modules, DrawioToExcelExporter

# 1. 先高亮模块
highlight_result = highlight_leaf_modules(
    target_paths=[
        "Disp/Dsp_app/Mainscrl/dsp_mainscrlcore/msg",
        "Graphic (A53)/App/AppObj/tftwarning"
    ],
    output_file_path="highlighted.drawio",
    highlight_color="red"
)

# 2. 导出高亮后的图表
if highlight_result['success']:
    exporter = DrawioToExcelExporter()
    result = exporter.insert_drawio_to_excel(
        drawio_file="highlighted.drawio",
        excel_file="highlighted_diagram.xlsx",
        title=f"高亮图表 ({highlight_result['highlighted_count']} 个模块)"
    )
```

#### 4. 结构化数据导出

```python
from sdw_agent.util.drawio_util.simple_drawio_to_excel import SimpleDrawioToExcelExporter

exporter = SimpleDrawioToExcelExporter()

# 导出完整结构分析
result = exporter.insert_drawio_structure_to_excel(
    drawio_file="architecture.drawio",
    excel_file="structure_analysis.xlsx",
    sheet_name="结构分析",
    include_structure_image=True,  # 包含结构图
    include_module_list=True       # 包含模块列表
)
```

## 参数说明

### DrawioToExcelExporter 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `drawio_file` | str | - | Draw.io 文件路径 |
| `excel_file` | str | - | Excel 输出文件路径 |
| `sheet_name` | str | "Sheet1" | 工作表名称 |
| `start_row` | int | 1 | 起始行号 |
| `start_col` | int | 1 | 起始列号 |
| `image_format` | str | "png" | 图像格式 (png/jpg/svg) |
| `scale` | float | 1.0 | 缩放比例 |
| `max_width` | int | None | 最大宽度（像素） |
| `max_height` | int | None | 最大高度（像素） |
| `title` | str | None | 图表标题 |

### 批量导出参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `drawio_files` | list | - | Draw.io 文件路径列表 |
| `images_per_row` | int | 2 | 每行图像数量 |
| `image_spacing` | int | 2 | 图像间距 |

## 运行示例

### 完整示例
```bash
# 运行完整功能示例
poetry run python src/sdw_agent/util/drawio_util/example_drawio_to_excel.py
```

### 简化示例
```bash
# 运行简化功能示例
poetry run python src/sdw_agent/util/drawio_util/simple_drawio_to_excel.py
```

## 输出结果

### Excel 文件内容
- **图像**: Draw.io 图表作为图像插入
- **标题**: 可选的图表标题
- **数据表**: 模块结构信息（简化模式）
- **统计**: 模块数量统计

### 返回结果格式
```python
{
    "success": True,
    "message": "操作成功消息",
    "excel_file": "输出文件路径",
    "sheet_name": "工作表名称",
    "image_position": "图像位置信息"
}
```

## 故障排除

### 常见问题

1. **找不到 draw.io 可执行文件**
   - 确保已安装 draw.io desktop
   - 检查是否在 PATH 环境变量中
   - 手动指定可执行文件路径

2. **图像导出失败**
   - 检查 Draw.io 文件是否损坏
   - 尝试使用简化导出模式
   - 检查磁盘空间是否充足

3. **Excel 插入失败**
   - 确保 Excel 文件未被其他程序占用
   - 检查输出目录权限
   - 尝试使用不同的工作表名称

### 日志调试
```python
from loguru import logger

# 启用详细日志
logger.add("drawio_export.log", level="DEBUG")
```

## 扩展功能

### 自定义导出器
```python
class CustomDrawioExporter(DrawioToExcelExporter):
    def custom_export_method(self):
        # 自定义导出逻辑
        pass
```

### 集成到工作流
```python
# 在现有工作流中使用
from sdw_agent.util.drawio_util import DrawioToExcelExporter

def export_architecture_diagrams(project_path):
    exporter = DrawioToExcelExporter()
    # 批量处理项目中的所有 drawio 文件
    # ...
```
