{"_data_id": 1, "_elements": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 1, "_head_list": [], "_head_type": "horizontal", "_last_coordinate": "C4", "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_rows": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 0, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 0, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 0, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 1, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 1, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 1, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 2, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "var(--ds-font-family-code)", "_font_size": 10.5, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "\n", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 2, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 2, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}], "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table"}], "_file_name": "1220演示数据变更前.xlsx", "_footer": [], "_graphics": [], "_header": [], "_layouts": [], "_name": "附录", "_pictures": [], "_settings": [], "_styles": [], "_tables": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 1, "_head_list": [], "_head_type": "horizontal", "_last_coordinate": "C4", "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_rows": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 0, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "项目", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 0, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "内容", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B2", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 0, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 1, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "术语解释", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 1, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 1, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": ""}, "_border_left": {"_border_color": "", "_border_style": ""}, "_border_right": {"_border_color": "", "_border_style": ""}, "_border_top": {"_border_color": "", "_border_style": ""}}, "_cells": [{"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 0, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 2, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "参考文献", "_type": "table_cell"}, {"_border": {"_border_bottom": {"_border_color": "", "_border_style": "thin"}, "_border_left": {"_border_color": "", "_border_style": "thin"}, "_border_right": {"_border_color": "", "_border_style": "thin"}, "_border_top": {"_border_color": "", "_border_style": "thin"}}, "_col_index": 1, "_comment": {"_coordinate": {"_bottom": "", "_desc": "", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": ""}, "_content": [{"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "var(--ds-font-family-code)", "_font_size": 10.5, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "\n", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": "text"}], "_coordinate": {"_bottom": "", "_desc": "C4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_merged_ranges": [], "_row_index": 2, "_style": {"_background_color": "#FFFFFF", "_background_style": "solid", "_font_color": "#404040", "_font_family": "Segoe UI", "_font_size": 12.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.", "_type": "table_cell"}], "_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": null, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_row_index": 2, "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table_row"}], "_style": {"_background_color": "", "_background_style": "", "_font_color": "", "_font_family": "", "_font_size": "", "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_type": "table"}], "_texts": [], "_timing_texts": [], "_timing_waves": [], "_type": "block"}