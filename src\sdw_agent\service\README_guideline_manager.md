# 分类规则管理系统

## 概述

分类规则管理系统提供了完整的CRUD操作来管理分类规则，支持规则的创建、查询、更新和删除。系统会自动管理规则文件的存储和元数据信息。

## 目录结构

```
~/.sdw/rules/
├── rules_metadata.json          # 规则元数据文件
├── rule_key_1/                  # 规则1的目录
│   └── rule_file.xlsx          # 规则1的Excel文件
├── rule_key_2/                  # 规则2的目录
│   └── another_rule.xlsx       # 规则2的Excel文件
└── ...
```

## API接口

### 1. 创建分类规则

**POST** `/api/sdw/guideline/rules`

创建新的分类规则，系统会自动将Excel文件复制到规则目录中。

**请求体：**
```json
{
  "rule_key": "example_rule",
  "rule_name": "示例规则",
  "rule_description": "这是一个示例分类规则",
  "role_name": "测试工程师",
  "excel_file_path": "C:/path/to/rule.xlsx",
  "auto_enhance": true
}
```

**响应：**
```json
{
  "code": 0,
  "msg": "规则创建成功",
  "data": {
    "rule_key": "example_rule",
    "rule_name": "示例规则",
    "excel_file_path": "example_rule/rule.xlsx",
    "created_at": "2025-07-22 14:30:00",
    "ai_enhancement": {
      "enabled": true,
      "enhanced_count": 3,
      "total_count": 5,
      "message": "AI增强完成，共处理 5 个分类，增强 3 个描述"
    }
  }
}
```

### 2. 获取单个分类规则

**GET** `/api/sdw/guideline/rules/{rule_key}`

根据规则key获取指定的分类规则信息。

**响应：**
```json
{
  "code": 0,
  "msg": "获取规则成功",
  "data": {
    "rule_key": "example_rule",
    "rule_name": "示例规则",
    "rule_description": "这是一个示例分类规则",
    "role_name": "测试工程师",
    "excel_file_path": "example_rule/rule.xlsx",
    "created_at": "2025-07-22 14:30:00",
    "updated_at": "2025-07-22 14:30:00"
  }
}
```

### 3. 获取分类规则列表

**GET** `/api/sdw/guideline/rules`

获取分类规则列表，支持分页和条件查询。

**查询参数：**
- `rule_key` (可选): 规则key
- `rule_name` (可选): 规则名称（支持模糊查询）
- `page` (默认1): 页码
- `page_size` (默认10): 每页大小

**响应：**
```json
{
  "code": 0,
  "msg": "获取规则列表成功",
  "data": {
    "rules": [
      {
        "rule_key": "example_rule",
        "rule_name": "示例规则",
        "rule_description": "这是一个示例分类规则",
        "excel_file_path": "example_rule/rule.xlsx",
        "created_at": "2025-07-22 14:30:00",
        "updated_at": "2025-07-22 14:30:00"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 4. 更新分类规则

**PUT** `/api/sdw/guideline/rules/{rule_key}`

更新指定的分类规则信息。所有字段都是可选的。

**请求体：**
```json
{
  "rule_name": "更新后的规则名称",
  "rule_description": "更新后的规则描述",
  "role_name": "高级测试工程师",
  "excel_file_path": "C:/path/to/new_rule.xlsx"
}
```

**响应：**
```json
{
  "code": 0,
  "msg": "规则更新成功",
  "data": {
    "rule_key": "example_rule",
    "updated_at": "2025-07-22 15:00:00"
  }
}
```

### 5. 删除分类规则

**DELETE** `/api/sdw/guideline/rules/{rule_key}`

删除指定的分类规则，包括规则目录和所有相关文件。

**响应：**
```json
{
  "code": 0,
  "msg": "规则删除成功",
  "data": {
    "rule_key": "example_rule",
    "deleted_at": "2025-07-22 15:30:00"
  }
}
```

### 6. AI增强分类规则描述

**POST** `/api/sdw/guideline/rules/{rule_key}/enhance`

使用AI自动生成和补全规则文件中空白的分类描述。

**请求体：**
```json
{
  "auto_enhance": true
}
```

**响应：**
```json
{
  "code": 0,
  "msg": "AI增强完成",
  "data": {
    "rule_key": "example_rule",
    "message": "AI增强完成，共处理 5 个分类，增强 3 个描述",
    "enhanced_count": 3,
    "total_count": 5,
    "empty_count": 3
  }
}
```

### 7. 预览AI增强结果

**GET** `/api/sdw/guideline/rules/{rule_key}/enhance/preview`

预览AI增强的分类描述，不会写入Excel文件。

**响应：**
```json
{
  "code": 0,
  "msg": "预览生成成功",
  "data": {
    "rule_key": "example_rule",
    "message": "AI增强完成，共处理 5 个分类，增强 3 个描述",
    "enhanced_count": 0,
    "total_count": 5,
    "empty_count": 3,
    "ai_descriptions": [
      {
        "category_name": "性能测试",
        "description": "评估系统性能指标和响应时间",
        "reasoning": "性能测试主要关注系统的响应时间、吞吐量等指标"
      }
    ]
  }
}
```

## 业务逻辑类

### GuidelineManager

主要的业务逻辑类，提供所有规则管理功能。

**主要方法：**

- `create_rule(rule_key, rule_name, rule_description, role_name, excel_file_path, auto_enhance=True)`: 创建规则
- `get_rule(rule_key)`: 获取单个规则
- `list_rules(rule_key=None, rule_name=None, page=1, page_size=10)`: 获取规则列表
- `update_rule(rule_key, rule_name=None, rule_description=None, role_name=None, excel_file_path=None)`: 更新规则
- `delete_rule(rule_key)`: 删除规则
- `get_rule_excel_path(rule_key)`: 获取规则Excel文件的绝对路径
- `enhance_rule_descriptions(rule_key, auto_enhance=True)`: AI增强规则描述
- `preview_ai_enhancements(rule_key)`: 预览AI增强结果

**使用示例：**

```python
from sdw_agent.service.guideline_manager import GuidelineManager

# 创建管理器实例
manager = GuidelineManager()

# 创建规则
result = manager.create_rule(
    rule_key="my_rule",
    rule_name="我的规则",
    rule_description="这是我的分类规则",
    role_name="测试工程师",
    excel_file_path="/path/to/my_rule.xlsx"
)

# 获取规则
rule = manager.get_rule("my_rule")

# 获取Excel文件路径
excel_path = manager.get_rule_excel_path("my_rule")

# AI增强规则描述
enhance_result = manager.enhance_rule_descriptions("my_rule")

# 预览AI增强结果
preview_result = manager.preview_ai_enhancements("my_rule")
```

## 特性

✅ **自动文件管理**: 自动复制和管理Excel文件
✅ **元数据存储**: 使用JSON文件存储规则元数据
✅ **完整CRUD**: 支持创建、读取、更新、删除操作
✅ **分页查询**: 支持分页和条件过滤
✅ **AI智能增强**: 自动生成和补全分类描述
✅ **预览功能**: 支持预览AI增强结果
✅ **Excel集成**: 使用openpyxl引擎读写Excel文件
✅ **错误处理**: 完善的异常处理和错误信息
✅ **日志记录**: 使用loguru进行详细日志记录
✅ **数据验证**: 使用Pydantic进行数据验证
✅ **测试覆盖**: 完整的单元测试覆盖

## 配置

系统使用ENV配置中的`rule_dir`路径，默认为`~/.sdw/rules`。

## AI增强功能

### Excel文件格式要求

AI增强功能要求Excel文件具有特定的格式：

- **第1列**: 分类名称（必填）
- **第2列**: 分类描述（可选，空白时会被AI自动补全）
- **第1行**: 表头行（会被跳过）

### AI增强流程

1. **读取Excel**: 自动读取Excel文件中的分类信息
2. **识别空白**: 找出描述为空的分类，保持原有顺序
3. **AI生成**: 使用专业的汽车软件开发知识生成描述，严格按顺序返回
4. **顺序匹配**: 按顺序将AI生成的描述与空白分类匹配（第1个生成的描述对应第1个空白分类）
5. **写入文件**: 将生成的描述写回Excel文件
6. **更新元数据**: 更新规则的修改时间

### AI增强特点

- 🤖 **专业知识**: 基于汽车软件开发领域的专业知识
- 📝 **智能生成**: 自动生成20-50字符的专业描述
- 🔢 **顺序匹配**: 按顺序匹配AI生成的描述与空白分类，避免名称不一致问题
- 🔍 **预览功能**: 支持预览生成结果，确认后再写入
- 🛡️ **安全保护**: 只补全空白描述，不覆盖现有内容
- 📊 **详细统计**: 提供增强统计信息和处理结果

## 注意事项

1. 创建规则时，Excel文件会被复制到规则目录中，原文件不会被修改
2. 删除规则时，会删除整个规则目录及其所有文件
3. 规则key必须唯一，不能重复
4. 更新Excel文件时，旧文件会被删除，新文件会被复制
5. AI增强只会补全空白的描述，不会覆盖现有描述
6. AI生成的描述按顺序与空白分类匹配，不依赖分类名称的完全一致
7. 建议先使用预览功能查看AI生成的结果，确认无误后再应用
8. 所有操作都有详细的日志记录，便于调试和监控
