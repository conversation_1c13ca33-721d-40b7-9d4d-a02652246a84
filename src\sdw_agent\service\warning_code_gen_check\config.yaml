# Warning Code Generation Service Configuration
# 警告代码生成服务配置

# 基本信息
name: "警告代码生成服务"
description: "基于Excel样书生成警告代码的工作流服务"
version: "1.0.0"
author: "SDW-Team"

# V字对应
v_model:
  stage: "V2.1"
  phase: "基本设计"

# 处理参数
processing:
  excel:
    visible: false  # Excel应用程序是否可见
    display_alerts: false  # 是否显示Excel警告
    screen_updating: false  # 是否更新屏幕显示
    timeout: 300  # Excel操作超时时间（秒）
  
  validation:
    check_file_exists: true  # 是否检查文件存在性
    check_sheet_exists: true  # 是否检查工作表存在性
  
  logging:
    level: "INFO"  # 日志级别
    enable_debug: false  # 是否启用调试模式

# 工作表配置
sheets:
  warning_sheet_name: "一覧"  # 警告工作表名称
  warning_codetool_sheet_name: "CONTDISP (源)"  # 警告代码工具工作表名称
  warning_property_sheet: "警告属性"  # 警告属性工作表
  function_sheet: "函数"  # 函数工作表
  interface_popup_sheet: "Interface_Popup"  # 接口弹窗工作表
  contdisp_met_sheet: "CONTDISP（MET）"  # CONTDISP MET工作表
  setting_sheet: "設定シート①"  # 设定工作表
  toyota_sheet: Toyota

# 列配置
columns:
  target_column: ["A", "Y"]  # 目标列范围
  row_offset: -3  # 行偏移量
  warning_update:
    target_value: 1276  # 目标值
    target_column: "Q"  # 目标列
    update_value: "×"  # 更新值

# 按钮配置
buttons:
  source_process:
    - "sheet26.CommandButton2_Click"
    - "sheet26.CommandButton3_Click" 
    - "sheet26.CommandButton1_Click"
  final_process:
    - "sheet11.CommandButton2_Click"
    - "sheet11.CommandButton1_Click"
    - "sheet10.CommandButton1_Click"

# 输出配置
output:
  default_output_dir: "C:\\sdw_output"  # 默认输出目录
  file_prefix:
    new_version: "new"  # 新版本文件前缀
    old_version: "old"  # 老版本文件前缀
  
# 错误处理
error_handling:
  max_retries: 3  # 最大重试次数
  retry_delay: 1  # 重试延迟（秒）
  continue_on_error: false  # 遇到错误是否继续


check_config:
  data_type_columns:
    - E
    - F
    - G
    - H
  correction_rules:
    O: "○"
    II: "Ⅱ"
  sort_columns:
    - No.
  msg_sheets: dspmnscrl_msg.prm;dspmnscrl_msg2.prm

code_files:
  wrndtcfg_file: wrndtcfg.c
  dspwrn_cfg_h: dspwrn_cfg.h
  dspwrnctl_h: dspwrnctl.h
  msg2_prm: dspmnscrl_msg2.prm

macros:
  NUMCNTTS_DSPWRN:
    file: dspwrn_cfg.h
    description: 警告数量上限
  DWRNRQ_ARRYSIZE:
    file: dspwrnctl.h
    description: 警告请求数组大小

functions:
  target_function: vd_g_WrndtcfgGetReq00
  line_pattern: '\*u4_p_req\s*\|=\s*\(U4\).*?u1_GETREQ_ID\d+\(\).*?;'