from langchain_openai import AzureOpenAIEmbeddings
from langchain_openai import AzureChatOpenAI
from langchain_openai import ChatOpenAI
from langchain_openai import OpenAIEmbeddings

__all__ = [
    'kt_azure_embeddings',
    'kt_azure_azure_gpt4o',
    'dnkt_deepseekv2',
]

kt_azure_embeddings = AzureOpenAIEmbeddings(
    azure_endpoint="https://sdw-openai.openai.azure.com",
    azure_deployment='text-embedding-3-large',
    api_key='8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1',
    api_version='2025-02-01-preview',
    model='text-embedding-3-large',
)

openai_embeddings = OpenAIEmbeddings(
    openai_api_base="http://172.30.19.111:9997/v1",
    api_key='xxx',
    model='bge-m3',
    dimensions=1024
)

kt_azure_azure_gpt4o = AzureChatOpenAI(
    azure_endpoint="https://sdw-openai.openai.azure.com",
    azure_deployment='gpt-4o',
    api_key='8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1',
    api_version='2025-02-01-preview',
    model='gpt-4o',
    temperature=0.1,
    max_retries=3
)

dnkt_deepseekv2 = ChatOpenAI(
    api_key="aaa",
    base_url="http://172.30.19.113:11400/v1/",
    model="deepseekv2",
    temperature=0,
)
dnkt_deepseekv2 = kt_azure_azure_gpt4o

if __name__ == '__main__':
    for i in range(10):
        result = dnkt_deepseekv2.invoke('你好')
        print(result)
    pass
