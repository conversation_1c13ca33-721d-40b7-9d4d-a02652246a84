import base64

# Base64 字符串
base64_str = """
iVBORw0KGgoAAAANSUhEUgAAAEIAAAAZCAYAAACFHfjcAAAA6klEQVR4nO2Wyw7EIAhFpen//zKzqQkhFwRHpovhJMZK8QFF6BhN0zRNE4Weng25fmfp6zlyLoExB/XHQsc7d4obyCiwuJaxM2Ywx3MGWs/Sn/KvuZMGRr0uv5ilj/aioKOOO+MObJKFk2NvDxRNJUhHkHMQT0eDcoI1tvbx8kBJVOxGBDtzrAiIXIXXuBbvWTUJqSblqI8aLCvLz6jIEZNpSNagnYR5NCI4GRErUERUGcjVV2OA0M9GwqpqeOugK1fCddCz/PQ6b8hnVEWstaKGl/xQRQ+7k8yiVSLzx/lqpWmapmma8d98AOfxSSfHW4+ZAAAAAElFTkSuQmCC
"""

# 解码 Base64 字符串
image_data = base64.b64decode(base64_str)

# 保存为图片文件
with open("output_image.png", "wb") as f:
    f.write(image_data)

print("图片已保存为 output_image.png")
