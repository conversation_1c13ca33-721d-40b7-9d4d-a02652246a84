"""汽车软件开发AI辅助工作流自动化系统 - 主程序"""
import logging
import yaml
from typing import Dict, Any
import os

from sdw_agent.core import (
    WorkflowEngine,
    WorkflowScheduler,
    WorkflowDefinition,
    TaskPlanner
)
from sdw_agent.steps import create_all_steps


class AutoWorkflowSystem:
    """汽车软件开发AI辅助系统主类"""
    
    def __init__(self, config_path: str = "config/system.yaml"):
        # 加载系统配置
        self.config = self._load_config(config_path)
        
        # 初始化日志
        self._setup_logging()
        
        # 创建引擎
        self.engine = WorkflowEngine(
            max_parallel_workers=self.config['engine']['max_parallel_workers']
        )
        
        # 创建调度器
        self.scheduler = WorkflowScheduler(self.engine)
        
        # 创建任务规划器
        self.task_planner = TaskPlanner(self.engine)
        
        # 注册所有步骤
        self._register_all_steps()
        
        # 加载预定义工作流
        self._load_workflows()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("系统初始化完成")
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
            
    def _setup_logging(self):
        """设置日志系统"""
        log_config = self.config['logging']
        
        # 创建日志目录
        if log_config['file']['enabled']:
            os.makedirs(os.path.dirname(log_config['file']['path']), exist_ok=True)
            
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config['format'],
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_config['file']['path']) if log_config['file']['enabled'] else None
            ]
        )
        
    def _register_all_steps(self):
        """注册所有步骤"""
        steps = create_all_steps()
        for step in steps:
            self.engine.register_step(step)
            
    def _load_workflows(self):
        """加载预定义工作流"""
        with open("config/workflows.yaml", 'r', encoding='utf-8') as f:
            workflows_config = yaml.safe_load(f)
            
        for workflow_id, workflow_data in workflows_config['workflows'].items():
            workflow = WorkflowDefinition(
                workflow_id=workflow_data['id'],
                name=workflow_data['name'],
                description=workflow_data['description'],
                steps=workflow_data['steps']
            )
            self.engine.register_workflow(workflow)
            
    def process_query(self, query: str) -> Dict[str, Any]:
        """处理用户查询"""
        self.logger.info(f"处理用户查询: {query}")
        
        # 生成任务计划
        plan = self.task_planner.plan_from_query(query)
        
        # 显示计划信息
        self.logger.info(f"识别意图: {plan.intent.intent_type} (置信度: {plan.intent.confidence})")
        self.logger.info(f"选定工作流: {plan.workflow_id}")
        self.logger.info(f"执行步骤: {plan.selected_steps}")
        
        # 如果置信度太低，请求确认
        if plan.intent.confidence < self.config['nlp']['query_parser']['min_confidence']:
            self.logger.warning(f"置信度较低 ({plan.intent.confidence})，建议人工确认")
            
        # 执行计划
        result = self.task_planner.execute_plan(plan)
        
        return result
        
    def execute_workflow(self, workflow_id: str, initial_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """直接执行指定工作流"""
        self.logger.info(f"执行工作流: {workflow_id}")
        
        # 创建执行计划
        plan = self.engine.create_execution_plan(workflow_id)
        
        # 执行工作流
        result = self.engine.execute_workflow(plan, initial_data)
        
        # 格式化结果
        return self._format_result(result)
        
    def _format_result(self, result) -> Dict[str, Any]:
        """格式化执行结果"""
        formatted = {
            "workflow_id": result.workflow_id,
            "status": result.status.value,
            "duration": result.duration,
            "summary": {
                "total_steps": len(result.step_results),
                "successful": sum(1 for r in result.step_results.values() if r.status.value == "success"),
                "failed": sum(1 for r in result.step_results.values() if r.status.value == "failed"),
                "skipped": sum(1 for r in result.step_results.values() if r.status.value == "skipped")
            },
            "steps": {}
        }
        
        for step_id, step_result in result.step_results.items():
            formatted["steps"][step_id] = {
                "name": step_result.step_name,
                "status": step_result.status.value,
                "duration": step_result.duration,
                "output": step_result.output.data if step_result.output else None
            }
            
        return formatted


def main():
    """主函数"""
    # 创建系统实例
    system = AutoWorkflowSystem()
    
    # 示例1：通过自然语言查询执行
    print("\n=== 示例1：自然语言查询 ===")
    query = "给出变更需求xxx的设计标准CS与法规确认结果"
    result = system.process_query(query)
    print(f"执行结果: {result['status']}")
    print(f"耗时: {result['duration']:.2f}秒")
    
    # 示例2：执行通信检查流程
    print("\n=== 示例2：通信检查流程 ===")
    query = "生成变更点xxx的通信检查测试用例"
    result = system.process_query(query)
    print(f"执行结果: {result['status']}")
    
    # 示例3：直接执行完整工作流
    print("\n=== 示例3：完整工作流 ===")
    initial_data = {
        "scl_file": "path/to/scl.xlsx",
        "requirements_list": "path/to/requirements.xlsx",
        "change_request": "CHG-2024-001"
    }
    result = system.execute_workflow("full_workflow", initial_data)
    print(f"执行结果: {result['status']}")
    print(f"成功步骤: {result['summary']['successful']}")
    print(f"失败步骤: {result['summary']['failed']}")
    
    # 示例4：条件执行
    print("\n=== 示例4：条件执行 ===")
    conditions = {
        "dev_2": lambda data: True,  # 总是执行法规确认
        "test_5": lambda data: "communication_change" in data.get("initial_data", {})  # 有通信变更时才执行
    }
    result = system.scheduler.schedule_conditional("dev_workflow", conditions, initial_data)
    print(f"执行结果: {result.status.value}")


if __name__ == "__main__":
    main() 