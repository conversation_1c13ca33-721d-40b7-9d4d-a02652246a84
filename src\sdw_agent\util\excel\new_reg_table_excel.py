"""
新规变化表Excel工具类

继承自ExcelUtil，提供新规变化表特定的Excel操作功能。
"""

import os
import re
from typing import Dict, Any, List

import pandas as pd
from loguru import logger

from sdw_agent.util.excel.core import ExcelUtil
from sdw_agent.util.excel_data_mapper import ExcelDataMapper
from sdw_agent.util.import_doc_util import ImportDocUtil
from sdw_agent.util.jira_util import get_ar_info


class NewRegTableExcelUtil(ExcelUtil):
    """
    新规变化表Excel工具类
    
    提供新规变化表创建和数据映射的专用功能。
    """

    def __init__(self, file_path: str, engine: str = "openpyxl"):
        """
        初始化新规变化表Excel工具类
        
        Args:
            file_path: Excel文件路径
            engine: Excel引擎类型
        """
        super().__init__(file_path, engine)
        self.import_util = ImportDocUtil()
        self.logger = logger.bind(name="NewRegTableExcelUtil")

    def extract_requirement_data(self, requirement_source) -> List[Dict[str, Any]]:
        """
        从要件一览表中提取数据
        
        Args:
            requirement_source: 要件一览表的SourceInfo
            
        Returns:
            List[Dict[str, Any]]: 提取的要件数据列表
        """
        self.logger.info(f"开始提取要件一览表数据: {requirement_source.uri}")
        
        # 使用ImportDocUtil提取数据
        content_list = self.import_util.get_task_content_multi_level_header(requirement_source)
        
        self.logger.info(f"提取到 {len(content_list)} 个原始项目")
        return content_list

    def filter_requirement_data(self, content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤要件数据，处理"同上"情况并筛选需要的项目
        
        Args:
            content_list: 原始要件数据列表
            
        Returns:
            List[Dict[str, Any]]: 过滤后的要件数据列表
        """
        self.logger.info("开始过滤要件数据")
        
        filter_content_list = []
        last_if_check_value = None  # 记录上一次的if_check值

        for item in content_list:
            current_if_check = item.get("if_check", "").strip()

            # 处理"同上"的情况
            if current_if_check == "同上":
                # 如果是"同上"，使用上一次的值
                if last_if_check_value is not None:
                    effective_if_check = last_if_check_value
                else:
                    # 如果没有上一次的值，默认为"否"
                    effective_if_check = "否"
                    self.logger.warning(f"行 {item.get('row_idx', 'unknown')} 的if_check为'同上'，但没有前置值，默认为'否'")
            else:
                # 不是"同上"，使用当前值
                effective_if_check = current_if_check
                # 更新last_if_check_value，但只有当值为"要"或"否"时才更新
                if current_if_check in ["要", "否"]:
                    last_if_check_value = current_if_check

            # 根据有效的if_check值进行过滤
            if effective_if_check == "要":
                # 创建一个副本，更新if_check为实际有效值
                filtered_item = item.copy()
                filtered_item["if_check"] = effective_if_check
                filter_content_list.append(filtered_item)

        self.logger.info(f"过滤后保留 {len(filter_content_list)} 个项目")
        return filter_content_list

    def generate_requirement_ids(self, filter_content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为要件数据生成ID和其他必要字段
        
        Args:
            filter_content_list: 过滤后的要件数据列表
            
        Returns:
            List[Dict[str, Any]]: 添加了ID和其他字段的要件数据列表
        """
        self.logger.info("开始生成要件ID和其他字段")
        
        new_table_list = []
        p_no_to_id = {}  # p_no值到要件ID的映射
        current_id = 1  # 当前要件ID序号

        # 优化：批量获取AR信息，避免重复查询
        ar_info_cache = {}  # AR编号到AR信息的缓存
        unique_ar_nos = set()  # 收集所有唯一的AR编号

        # 第一步：收集所有唯一的AR编号
        for item in filter_content_list:
            ar_no = item.get('ar_no')
            if ar_no and str(ar_no).strip() and str(ar_no) != 'None':
                unique_ar_nos.add(str(ar_no).strip())

        # 第二步：批量查询AR信息并缓存
        self.logger.info(f"发现 {len(unique_ar_nos)} 个唯一的AR编号，开始批量查询AR信息")
        for ar_no in unique_ar_nos:
            try:
                ar_info = get_ar_info(ar_no)
                ar_info_cache[ar_no] = ar_info
                self.logger.debug(f"成功获取AR {ar_no} 的信息")
            except Exception as e:
                self.logger.warning(f"获取AR {ar_no} 的基础式样书信息失败: {e}")
                ar_info_cache[ar_no] = None  # 缓存失败结果，避免重复查询

        # 第三步：处理每个要件项目
        for item in filter_content_list:
            # 要件id映射处理
            p_no_value = item.get('p_no')

            # 如果p_no为空或None，跳过或使用默认处理
            if p_no_value is None or str(p_no_value).strip() == '' or str(p_no_value) == 'None' or str(p_no_value).strip() == '-':
                # 对于空值，可以选择跳过或分配新ID
                req_id = current_id
                current_id += 1
            else:
                # 如果p_no值已经见过，使用相同的ID
                if p_no_value in p_no_to_id:
                    req_id = p_no_to_id[p_no_value]
                else:
                    # 新的p_no值，分配新的ID
                    req_id = current_id
                    p_no_to_id[p_no_value] = req_id
                    current_id += 1

            # 创建新的数据项，添加生成的要件ID
            new_item = item.copy()  # 复制原始数据
            new_item['req_id'] = req_id  # 添加生成的要件ID
            new_item['req_type'] = "仕様変更"
            new_item['req_clean_file_name'] = re.sub(r'【.*?】', '', item['req_file_name'])

            # 从缓存中获取基础式样书信息
            ar_no = str(item.get('ar_no', '')).strip()
            if ar_no and ar_no in ar_info_cache and ar_info_cache[ar_no]:
                try:
                    base_spec_number = ar_info_cache[ar_no]['base_spec_number']
                    if base_spec_number is None:
                        self.logger.warning(f"没有提取到AR {ar_no} 的基础式样书信息!")
                    new_item['base_file_name'] = base_spec_number  # 基础式样书id
                except (KeyError, TypeError) as e:
                    self.logger.warning(f"AR {ar_no} 的信息中缺少base_spec_number字段: {e}")
                    new_item['base_file_name'] = ""  # 设置默认值
            else:
                new_item['base_file_name'] = ""  # 设置默认值

            new_table_list.append(new_item)

        self.logger.info(f"生成了 {len(new_table_list)} 个要件项目，查询了 {len(unique_ar_nos)} 个唯一AR编号")
        return new_table_list

    def map_data_to_template(self, 
                           new_table_list: List[Dict[str, Any]], 
                           template_file: str,
                           target_sheet: str,
                           mapping_rules: Dict[str, str],
                           header_config: Dict[str, Any]) -> bool:
        """
        将数据映射到模板文件
        
        Args:
            new_table_list: 要映射的数据列表
            template_file: 模板文件路径
            target_sheet: 目标工作表名称
            mapping_rules: 映射规则
            header_config: 表头配置
            
        Returns:
            bool: 映射是否成功
        """
        self.logger.info(f"开始将数据映射到模板文件: {template_file}")
        
        try:
            # 初始化映射器
            mapper = ExcelDataMapper(template_file, target_sheet)

            # 分析目标表结构
            mapper.analyze_target_structure(
                header_start_row=header_config.get('header_start_row', 5),
                header_end_row=header_config.get('header_end_row', 7),
                start_col=header_config.get('start_col', 'B'),
                end_col=header_config.get('end_col', 'J')
            )

            # 获取源数据并转换为DataFrame
            source_df = pd.DataFrame(new_table_list)

            # 直接使用DataFrame进行数据映射，无需创建临时文件
            mapper.process_data_mapping_all_cover(
                source_data=source_df,
                mapping_rules=mapping_rules
            )

            self.logger.info("✅ 新规变化表数据映射完成!")
            return True

        except Exception as e:
            self.logger.error(f"❌ 数据映射失败: {e}")
            return False

    def validate_template_file(self, template_file: str, target_sheet: str) -> bool:
        """
        验证模板文件的有效性
        
        Args:
            template_file: 模板文件路径
            target_sheet: 目标工作表名称
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if not os.path.exists(template_file):
                self.logger.error(f"模板文件不存在: {template_file}")
                return False
                
            if not os.path.isfile(template_file):
                self.logger.error(f"模板路径不是有效文件: {template_file}")
                return False
                
            # 尝试打开文件检查工作表是否存在
            temp_util = ExcelUtil(template_file)
            try:
                sheets = temp_util.get_sheet_names()
                if target_sheet not in sheets:
                    self.logger.error(f"目标工作表 '{target_sheet}' 不存在，可用工作表: {sheets}")
                    return False
            finally:
                temp_util.close()

            self.logger.info(f"模板文件验证通过: {template_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"验证模板文件失败: {e}")
            return False
