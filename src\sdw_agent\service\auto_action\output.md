以下是根据您提供的文档内容生成的操作步骤：

---

# 操作步骤

## **CANoe 地图发送方法**

### **前提条件**
1. 确保实机已通电，并启动CANoe。
2. 将ACC和IG打开，并确保SUB-BUS处于通信状态。

### **Level3请求(NAV地图用)的启动方法**
1. 打开 `[NAV Status Notification no.1 / Navi Tab Activate Notification]` 面板，按如下步骤操作：
   - 将 `NAV_MODE` 设置为连动状态，并修改 `[NAV_MODE1]` 值为 `3`。
   - 将 `NAV_COUNT` 设置为连动状态，并修改 `[NAV_COUNT1]` 值为大于0的正整数。
   - 修改 `GD_STS` 的值为 `2` 或 `3`。
2. 打开 `[Ethernet_NAV Status Notification]` 面板，并将 `MAPSTS` 的值修改为 `010b`。
3. Level3通信将启动，NAV地图图像的发送将完成。如果未修改发送图像资源，则显示默认图像。

### **图像资源的修改方法**
1. 打开 `[OTA]` 面板，点击红框中的文件夹图标。
2. 在弹出的文件选择窗口中选择想要发送的图片文件。
3. 确认文件路径显示正确，勾选“指定要发送的PNG图像”的复选框。
4. 指定图像后，将发送新的PNG图像。

---

# **负荷测试 (Toyota)**

### **Case2: `1 dial (普通)` 显示场景**
1. 场景描述：
   - 显示左 `mid TBT` + 右 `mid ADAS` + 基础信息(basic info) + `ONS` + 左侧 `widget` 和 `eco fuel widget` 。
2. 测试数据：
   - **峰值条件**:
     - CPU负荷: MET: 0.82%; HUD: 0.48%
     - 绘图负荷: 18 fps
   - **定常条件**:
     - CPU负荷: MET: 0.45%; HUD: 0.09%
     - 绘图负荷: 29 fps
3. OK标准：
   - FPS高于20。
   - MET CPU负荷低于60%。
   - HUD CPU负荷低于15%。
4. 结果：**OK**。

### **Case4: `0 dial` 显示场景**
1. 场景描述：
   - 显示左 `mid TBT` + `popup` + 右 `mid ADAS` + 基础信息(basic info) + `ONS` + 左侧 `widget` 和 `eco fuel widget`。
2. 测试数据:
   - **峰值条件**:
     - CPU负荷: MET: 0.81%; HUD: 0.52%
     - 绘图负荷: 14 fps
   - **定常条件**:
     - CPU负荷: MET: 0.45%; HUD: 0.09%
     - 绘图负荷: 33 fps
3. OK标准：
   - FPS高于20。
   - MET CPU负荷低于60%。
   - HUD CPU负荷低于15%。
4. 结果：**OK**。

### **Case6: `PAView` 显示场景**
1. 场景描述：
   - 显示 `PAView` + 左 `mid TBT` + `popup` + 右 `mid ADAS` + 基础信息(basic info) + `ONS` + 左侧 `widget` 和 `eco fuel widget`。
2. 测试数据:
   - **峰值条件**:
     - CPU负荷: MET: 0.82%; HUD: 0.56%
     - 绘图负荷: 15 fps
   - **定常条件**:
     - CPU负荷: MET: 0.48%; HUD: 0.09%
     - 绘图负荷: 23 fps
3. OK标准：
   - FPS高于20。
   - MET CPU负荷低于60%。
   - HUD CPU负荷低于15%。
4. 结果：**OK**。

---

# 注意事项
- 操作步骤中涉及图片内容，请从以下参考文件中查看相关图示及说明。

---

# 参考文件路径
- `c:\Users\<USER>\DEV_Agent\src\sdw_agent\service\auto_action\性能测试手顺\3-负荷测试相关资料\CANoe地图发送手顺.xlsx`
- `c:\Users\<USER>\DEV_Agent\src\sdw_agent\service\auto_action\性能测试手顺\3-负荷测试相关资料\负荷测试_toyota.xlsx`

---