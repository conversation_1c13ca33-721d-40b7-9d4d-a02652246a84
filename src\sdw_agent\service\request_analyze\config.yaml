# 要件分析工作流配置文件

# 基本配置
name: "要件分析工作流"
description: "基于guideline对变更需求进行分类打分和内容优化"
version: "1.0.0"
author: "SDW Agent"

# 模块特定配置
module_specific:
  # 默认参数
  default_guideline_key: "CSTM"
  min_score_threshold: 0.5
  max_category_results: 100
  timeout: 300

  # Excel相关配置
  excel_engine: "openpyxl"
  header_start_row: 3
  data_start_row_offset: 7

  # 文件路径配置
  output_data_path: "output/request_analyze"
  design_policy_prefix: "design_policy_"

  # 验证配置
  max_summary_length: 1000
  max_reason_length: 500
  max_path_length: 260

  # 日志配置
  log_level: "INFO"
  enable_debug: false

  # 性能配置
  enable_cache: true
  cache_ttl: 3600  # 缓存时间（秒）
  max_concurrent_tasks: 5

# Excel操作常量
excel_constants:
  # 要件一览表相关
  sheet_name: "要件一览"
  summary_col: "概要"
  role_col_prefix: "HMI组 开发担当"
  comprehend_col: "HMI 变更点的理解"
  need_notify_col: "HMI 对应要否"
  need_notify_yes: "要"
  need_notify_no: "否"
  not_notify_reason: "HMI 对应否理由"
  influence_col: "HMI 对应要变更点影响分析"

  # 设计评价方针相关
  design_policy_sheet_prefix: "design_policy_"

# 工作流业务逻辑常量
workflow_constants:
  # 默认值
  default_role_name: "SDW Agent"

  # 分类打分相关
  other_category_name: "其它"
  other_category_reason: "其它类型均不匹配"
  other_category_score_high: 1
  other_category_score_low: 0

  # 优化相关
  not_applicable_reason: "当前ブランド内グレード不适用"
  default_dash_value: "-"

  # 故障模式相关
  failure_modes:
    - ["実行されない", "-", ""]
    - ["実行順序が間違っている", "-", ""]
    - ["処理時間がオーバーしている", "-", ""]
    - ["処理が間違っている", "○", ""]
    - ["データのTimingが間違っている", "-", ""]
    - ["Memoryに誤って書き込まれている", "-", ""]
    - ["入力データが間違っている", "-", ""]

# 验证相关常量
validation_constants:
  # 要件票号前缀
  p_no_prefix: "PROCMGT"

# 消息常量
message_constants:
  # 成功消息
  success_classify: "成功对AR票进行分类打分"
  success_optimize: "成功生成优化后的分析结果"
  success_write_epic: "成功写入要件一览表"
  success_write_design_policy: "成功生成设计评价方针"

  # 错误消息
  error_no_matching_guideline: "当前没有匹配的guideline"
  error_no_matching_epic: "根据概要信息无法找出匹配的要件"
  error_invalid_file_path: "文件路径无效"
  error_empty_content: "内容不能为空"

  # 警告消息
  warning_low_score: "所有分类得分都较低，建议人工确认"
