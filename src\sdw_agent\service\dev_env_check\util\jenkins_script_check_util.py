"""
Jenkins工具脚本检查工具类

提供Jenkins脚本文件下载、对比和Excel报告生成功能，类似git diff的可视化效果
支持manifest文件的tag信息提取和对比分析
"""
import difflib
import os
import tempfile
from datetime import datetime

import requests
from typing import List, Dict, Optional, Tuple
from loguru import logger
from urllib.parse import urlparse

from sdw_agent.config.env import ENV
from sdw_agent.service.dev_env_check.config import jenkins_script_compare_styles
from sdw_agent.service.dev_env_check.models import PackageFileCompareInfo
from sdw_agent.util.excel.core import ExcelUtil


class JenkinsScriptCheckUtil:
    """Jenkins工具脚本检查工具类"""

    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化工具类

        Args:
            temp_dir: 临时目录，用于存储下载的文件
        """
        self.temp_dir = temp_dir or tempfile.mkdtemp()
        os.makedirs(self.temp_dir, exist_ok=True)

        # 定义样式 - 使用CellStyle类
        self.styles = jenkins_script_compare_styles

    def download_file(self, url: str, local_path: str) -> bool:
        """
        从URL下载文件到本地

        Args:
            url: 文件URL
            local_path: 本地保存路径

        Returns:
            bool: 下载是否成功
        """
        try:
            logger.info(f"正在下载Jenkins脚本文件: {url}")

            # 添加常见的请求头，避免被服务器拒绝
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/plain,text/xml,application/xml,*/*'
            }

            response = requests.get(url, timeout=60, headers=headers)
            response.raise_for_status()

            # 自动检测编码
            content = response.content
            try:
                # 尝试UTF-8解码
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    # 尝试GBK解码
                    text_content = content.decode('gbk')
                except UnicodeDecodeError:
                    # 使用latin-1作为最后的备选
                    text_content = content.decode('latin-1')

            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(text_content)

            logger.info(f"Jenkins脚本文件下载成功: {local_path}")
            return True

        except Exception as e:
            logger.error(f"下载Jenkins脚本文件失败 {url}: {str(e)}")
            return False

    def get_file_content(self, file_path_or_url: str) -> Tuple[List[str], str]:
        """
        获取文件内容，支持本地文件路径或URL

        Args:
            file_path_or_url: 文件路径或URL

        Returns:
            Tuple[List[str], str]: (文件行列表, 文件标识)
        """
        # 判断是否为URL
        if self._is_url(file_path_or_url):
            # 从URL下载
            filename = os.path.basename(urlparse(file_path_or_url).path) or "jenkins_script.xml"
            # 确保文件名唯一
            timestamp = str(int(os.path.getmtime(__file__) if os.path.exists(__file__) else 0))
            filename = f"{timestamp}_{filename}"
            local_path = os.path.join(self.temp_dir, filename)

            if self.download_file(file_path_or_url, local_path):
                with open(local_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                return lines, os.path.basename(file_path_or_url)
            else:
                raise RuntimeError(f"无法下载Jenkins脚本文件: {file_path_or_url}")
        else:
            # 本地文件
            if not os.path.exists(file_path_or_url):
                raise FileNotFoundError(f"Jenkins脚本文件不存在: {file_path_or_url}")

            with open(file_path_or_url, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            return lines, os.path.basename(file_path_or_url)

    def _is_url(self, path: str) -> bool:
        """判断是否为URL"""
        return path.startswith(('http://', 'https://'))

    def compare_jenkins_scripts(self, file1_path_or_url: str, file2_path_or_url: str) -> Dict:
        """
        对比两个Jenkins脚本文件

        Args:
            file1_path_or_url: 第一个文件路径或URL
            file2_path_or_url: 第二个文件路径或URL

        Returns:
            Dict: 对比结果
        """
        logger.info("开始对比Jenkins脚本文件")

        # 获取文件内容
        lines1, name1 = self.get_file_content(file1_path_or_url)
        lines2, name2 = self.get_file_content(file2_path_or_url)

        # 使用difflib进行对比
        differ = difflib.unified_diff(
            lines1, lines2,
            fromfile=name1,
            tofile=name2,
            lineterm=''
        )

        # 获取详细差异
        detailed_diff = list(difflib.ndiff(lines1, lines2))

        result = {
            'file1_url': file1_path_or_url,
            'file2_url': file2_path_or_url,
            'file1_name': name1,
            'file2_name': name2,
            'file1_lines': lines1,
            'file2_lines': lines2,
            'unified_diff': list(differ),
            'detailed_diff': detailed_diff,
        }

        logger.info("Jenkins脚本文件对比完成")
        return result

    def _create_git_style_diff_sheet(self, excel: ExcelUtil, result: Dict):
        """
        创建类似git diff的并排对比sheet，确保相同行对齐，变更行平齐
        """
        sheet_name = " Jenkin配置比較"

        # 使用difflib的SequenceMatcher来获取更精确的对比
        lines1 = [line.rstrip() for line in result['file1_lines']]
        lines2 = [line.rstrip() for line in result['file2_lines']]

        matcher = difflib.SequenceMatcher(None, lines1, lines2)

        row = 18
        line1_num = 0
        line2_num = 0

        excel.write_cell(sheet_name, row - 2, 1, f"左边文件:{result['file1_url']}")
        excel.write_cell(sheet_name, row - 1, 1, f"右边文件:{result['file2_url']}")

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # 相同的行
                for k in range(i2 - i1):
                    line1_num += 1
                    line2_num += 1
                    line_content = lines1[i1 + k]

                    excel.write_cell(sheet_name, row, 1, line_content)
                    excel.write_cell(sheet_name, row, 3, line_content)
                    excel.write_cell(sheet_name, row, 4, "-")
                    excel.write_cell(sheet_name, row, 5, "-")

                    excel.set_cell_style(sheet_name, row, 1, self.styles['same'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['same'])
                    row += 1

            elif tag == 'delete':
                # 删除的行（只在原文件中存在）
                for k in range(i2 - i1):
                    line1_num += 1
                    line_content = lines1[i1 + k]

                    excel.write_cell(sheet_name, row, 1, f"- {line_content}")
                    excel.write_cell(sheet_name, row, 2, "删除")
                    excel.write_cell(sheet_name, row, 3, "")

                    excel.set_cell_style(sheet_name, row, 1, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 2, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 4, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['deleted'])
                    row += 1

            elif tag == 'insert':
                # 新增的行（只在新文件中存在）
                for k in range(j2 - j1):
                    line2_num += 1
                    line_content = lines2[j1 + k]

                    excel.write_cell(sheet_name, row, 1, "")
                    excel.write_cell(sheet_name, row, 2, "新增")
                    excel.write_cell(sheet_name, row, 3, f"+ {line_content}")

                    excel.set_cell_style(sheet_name, row, 1, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 2, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 4, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['deleted'])
                    row += 1

            elif tag == 'replace':
                # 替换的行（修改）- 确保左右对齐
                max_lines = max(i2 - i1, j2 - j1)

                for k in range(max_lines):
                    # 左侧（删除的行）
                    if k < (i2 - i1):
                        line1_num += 1
                        left_content = lines1[i1 + k]
                        excel.write_cell(sheet_name, row, 1, f"- {left_content}")
                        excel.set_cell_style(sheet_name, row, 1, self.styles['deleted'])
                    else:
                        excel.write_cell(sheet_name, row, 1, "")
                        excel.set_cell_style(sheet_name, row, 1, self.styles['modified'])
                    excel.write_cell(sheet_name, row, 2, "变更")
                    excel.set_cell_style(sheet_name, row, 2, self.styles['modified'])
                    excel.set_cell_style(sheet_name, row, 4, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['deleted'])
                    # 右侧（新增的行）
                    if k < (j2 - j1):
                        line2_num += 1
                        right_content = lines2[j1 + k]
                        excel.write_cell(sheet_name, row, 3, f"+ {right_content}")
                        excel.set_cell_style(sheet_name, row, 3, self.styles['added'])
                    else:
                        excel.write_cell(sheet_name, row, 3, "")
                        excel.set_cell_style(sheet_name, row, 3, self.styles['modified'])

                    row += 1

    def generate_excel_report(self, compare_result: Dict, output_path: str) -> str:
        """
        生成Excel对比报告

        Args:
            compare_result: 对比结果
            output_path: 输出文件路径

        Returns:
            str: 生成的Excel文件路径
        """
        logger.info(f"开始生成Jenkins脚本检查Excel报告: {output_path}")

        with ExcelUtil(output_path, auto_create=True) as excel:
            # 创建git风格的对比sheet
            self._create_git_style_diff_sheet(excel, compare_result)
            current_time = datetime.now()
            timestamp = current_time.strftime("%Y%m%d_%H%M%S")
            output_data_path = os.path.normpath(ENV.config.output_data_path)
            if not os.path.exists(output_data_path):
                os.makedirs(output_data_path)
            ret_book = os.path.join(output_data_path, f"Jenkins_Check_{timestamp}.xlsx")
            excel.save(ret_book)

        logger.success(f"Jenkins脚本检查Excel报告生成完成: {ret_book}")
        return ret_book

    def get_jenkins_script_compare_book_path(self) -> str:
        """获取样本书路径"""
        return os.path.join(os.path.dirname(__file__), os.path.normpath('../book_data/Jenkins_Check_template.xlsx'))

    def check_and_generate_report(self, file1_path_or_url: str, file2_path_or_url: str,
                                  output_path: str) -> str:
        """
        一键检查并生成报告

        Args:
            file1_path_or_url: 第一个文件路径或URL
            file2_path_or_url: 第二个文件路径或URL
            output_path: 输出Excel文件路径

        Returns:
            str: 生成的Excel文件路径
        """
        logger.info("开始Jenkins脚本文件检查和报告生成")

        try:
            # 对比文件
            compare_result = self.compare_jenkins_scripts(file1_path_or_url, file2_path_or_url)

            # 生成Excel报告
            report_path = self.generate_excel_report(compare_result, output_path)

            logger.info("Jenkins脚本文件检查和报告生成完成")
            return report_path

        except Exception as e:
            logger.error(f"Jenkins脚本检查和报告生成失败: {str(e)}")
            raise

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时目录失败: {str(e)}")

def compare_jenkins_scripts(config: PackageFileCompareInfo):
    with JenkinsScriptCheckUtil() as util:
        result = util.check_and_generate_report(
            file1_path_or_url=config.pre_jenkins_script_path,
            file2_path_or_url=config.after_jenkins_script_path,
            output_path=util.get_jenkins_script_compare_book_path()
        )
        return result


# 使用示例
if __name__ == "__main__":
    # 示例1: 对比本地Jenkins脚本文件
    with JenkinsScriptCheckUtil() as util:
        result = util.check_and_generate_report(
            file1_path_or_url=r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-22-06.xml",
            file2_path_or_url=r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-24-02.xml",
            output_path=r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\429D_JenkinsCheck.xlsx"
        )
        print(f"Jenkins脚本检查报告生成完成: {result}")

    # # 示例2: 对比在线Jenkins脚本文件
    # with JenkinsScriptCheckUtil() as util:
    #     result = util.check_and_generate_report(
    #         file1_path_or_url="https://jenkins.example.com/job/project/config.xml",
    #         file2_path_or_url="https://jenkins.example.com/job/project-new/config.xml",
    #         output_path="online_jenkins_script_check.xlsx"
    #     )
    #     print(f"在线Jenkins脚本检查报告: {result}")
    #
    # # 示例3: 对比manifest文件（包含tag信息）
    # with JenkinsScriptCheckUtil() as util:
    #     result = util.check_and_generate_report(
    #         file1_path_or_url="path/to/old_manifest.xml",
    #         file2_path_or_url="path/to/new_manifest.xml",
    #         output_path="manifest_jenkins_check.xlsx"
    #     )
    #     print(f"Manifest文件Jenkins检查报告: {result}")
