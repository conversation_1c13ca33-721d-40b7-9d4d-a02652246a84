"""
测试代理 24 Workflow

V字对应：
4.1 检查実施
24. CheckList記載

该模块提供测试代理功能，支持测试用例信息提取和自动填写CheckSheet。
"""

"""
SDW Agent服务模块基础设施

提供workflow基类和通用功能，用于规范化各类workflow的实现。
"""
from .workflow import TestAgent24Workflow

__all__ = ['TestAgent24Workflow']

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import os
import yaml
import importlib
import inspect

from loguru import logger
from pydantic import BaseModel

# 定义工作流状态枚举
class WorkflowStatus(Enum):
    """工作流状态枚举"""
    INIT = "初始化"
    RUNNING = "运行中"
    SUCCESS = "成功"
    FAILED = "失败"
    WARNING = "警告"


class WorkflowResult(BaseModel):
    """工作流执行结果模型"""
    status: WorkflowStatus
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BaseWorkflow(ABC):
    """
    工作流基类

    所有服务工作流都应继承此基类，实现标准接口。
    提供配置加载、日志记录、错误处理等通用功能。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        self.name = self.__class__.__name__
        self.status = WorkflowStatus.INIT
        self.config = {}
        self.logger = logger.bind(workflow=self.name)

        # 如果提供了配置文件路径，则加载配置
        if config_path:
            self.load_config(config_path)
        else:
            # 尝试从默认位置加载配置
            default_config_path = self._get_default_config_path()
            if os.path.exists(default_config_path):
                self.load_config(default_config_path)

        self.logger.info(f"{self.name} 初始化完成")

    def load_config(self, config_path: str) -> None:
        """
        加载配置文件

        Args:
            config_path: 配置文件路径

        Raises:
            FileNotFoundError: 当配置文件不存在时
            ValueError: 当配置文件格式无效时
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            self.logger.info(f"从 {config_path} 加载配置成功")
        except FileNotFoundError:
            self.logger.error(f"配置文件不存在: {config_path}")
            raise
        except yaml.YAMLError as e:
            self.logger.error(f"配置文件格式无效: {e}")
            raise ValueError(f"配置文件格式无效: {e}")

    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        # 获取子类的模块路径
        module_path = inspect.getmodule(self).__file__
        module_dir = os.path.dirname(module_path)
        # 默认配置文件命名为 config.yaml
        return os.path.join(module_dir, "config.yaml")

    @abstractmethod
    def execute(self, *args, **kwargs) -> WorkflowResult:
        """
        执行工作流

        子类必须实现此方法，提供工作流的核心逻辑。

        Returns:
            WorkflowResult: 工作流执行结果
        """
        pass

    def validate_input(self, *args, **kwargs) -> bool:
        """
        验证输入参数

        子类可以重写此方法，提供输入验证逻辑。

        Returns:
            bool: 验证是否通过
        """
        return True

    def pre_execute(self, *args, **kwargs) -> None:
        """
        执行前处理

        子类可以重写此方法，提供执行前准备工作。
        """
        self.status = WorkflowStatus.RUNNING
        self.logger.info(f"{self.name} 开始执行")

    def post_execute(self, result: WorkflowResult) -> None:
        """
        执行后处理

        子类可以重写此方法，提供执行后清理工作。

        Args:
            result: 工作流执行结果
        """
        self.status = result.status
        self.logger.info(f"{self.name} 执行完成，状态: {result.status.value}")

    def run(self, *args, **kwargs) -> WorkflowResult:
        """
        运行工作流

        包含完整的工作流生命周期，执行前处理、核心逻辑、错误处理和执行后处理。

        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            # 输入验证
            if not self.validate_input(*args, **kwargs):
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message=f"{self.name} 输入参数验证失败",
                    error="输入参数验证失败"
                )

            # 执行前处理
            self.pre_execute(*args, **kwargs)

            # 核心逻辑
            result = self.execute(*args, **kwargs)

            # 执行后处理
            self.post_execute(result)

            return result

        except Exception as e:
            self.logger.exception(f"{self.name} 执行异常")
            self.status = WorkflowStatus.FAILED

            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"{self.name} 执行失败: {str(e)}",
                error=str(e)
            )


class WorkflowRegistry:
    """
    工作流注册表

    管理所有已注册的工作流，提供动态加载和实例化工作流的能力。
    """

    _registry = {}

    @classmethod
    def register(cls, workflow_name: str, workflow_class: type) -> None:
        """
        注册工作流类

        Args:
            workflow_name: 工作流名称
            workflow_class: 工作流类
        """
        if not issubclass(workflow_class, BaseWorkflow):
            raise TypeError(f"工作流类 {workflow_class.__name__} 必须继承 BaseWorkflow")

        cls._registry[workflow_name] = workflow_class
        logger.info(f"工作流 {workflow_name} 注册成功")

    @classmethod
    def get(cls, workflow_name: str) -> Optional[type]:
        """
        获取工作流类

        Args:
            workflow_name: 工作流名称

        Returns:
            工作流类，如果不存在则返回None
        """
        return cls._registry.get(workflow_name)

    @classmethod
    def create(cls, workflow_name: str, **kwargs) -> Optional[BaseWorkflow]:
        """
        创建工作流实例

        Args:
            workflow_name: 工作流名称
            **kwargs: 传递给工作流构造函数的参数

        Returns:
            工作流实例，如果不存在则返回None
        """
        workflow_class = cls.get(workflow_name)
        if workflow_class:
            return workflow_class(**kwargs)
        return None

    @classmethod
    def list_workflows(cls) -> List[str]:
        """
        列出所有已注册的工作流

        Returns:
            工作流名称列表
        """
        return list(cls._registry.keys())


def register_workflow(workflow_name: Optional[str] = None):
    """
    工作流注册装饰器

    使用方法:
    @register_workflow("my_workflow")
    class MyWorkflow(BaseWorkflow):
        pass

    如果不指定workflow_name，则使用类名

    Args:
        workflow_name: 可选，工作流名称
    """
    def decorator(cls):
        name = workflow_name or cls.__name__
        WorkflowRegistry.register(name, cls)
        return cls
    return decorator


def scan_and_register_workflows(package_path: str = "sdw_agent.service") -> None:
    """
    扫描并注册指定包路径下的所有工作流

    Args:
        package_path: 包路径
    """
    try:
        package = importlib.import_module(package_path)
        package_dir = os.path.dirname(package.__file__)

        # 遍历目录
        for item in os.listdir(package_dir):
            item_path = os.path.join(package_dir, item)

            # 如果是子目录且不是以_开头
            if os.path.isdir(item_path) and not item.startswith("_"):
                try:
                    # 尝试导入模块
                    module_path = f"{package_path}.{item}"
                    importlib.import_module(module_path)
                    logger.debug(f"导入模块 {module_path}")
                except ImportError as e:
                    logger.warning(f"导入模块 {module_path} 失败: {e}")

        logger.info(f"扫描并注册工作流完成，共注册 {len(WorkflowRegistry.list_workflows())} 个工作流")
    except Exception as e:
        logger.error(f"扫描并注册工作流失败: {e}")
