"""
要件设计书照合实施服务
负责处理Excel文件读取、分析和新表格生成的业务逻辑
"""
import os
from typing import Dict, Any
from datetime import datetime
from openpyxl.styles import Font, PatternFill, Alignment

from loguru import logger

from sdw_agent.model.request_model import SourceInfo
from sdw_agent.util.excel.multi_level_header_extractor import MultiLevelHeaderExtractor


def read_req_design_data(key_source: SourceInfo):
    file_path = key_source.uri
    sheet_name = "機能一覧と新規・変更内容"
    extractor = MultiLevelHeaderExtractor(file_path, sheet_name)

    # 提取完整数据
    print("\n=== 提取完整数据 ===")
    df, _ = extractor.extract_data_with_headers(
        header_start_row=5,
        header_end_row=7,
        data_start_row=8,
        start_col=1,
        end_col=100,  # 只看前10列
        exclude_hidden_rows=True
    )

    content_list = []
    data_start_row = 8  # 数据开始的实际Excel行号

    # 获取实际的Excel行号映射
    # 由于exclude_hidden_rows=True，需要考虑隐藏行的影响
    actual_row_numbers = []

    # 遍历从data_start_row开始的所有行，跳过隐藏行
    for row_num in range(data_start_row, extractor.ws.max_row + 1):
        if not extractor._is_row_hidden(row_num):
            actual_row_numbers.append(row_num)
            if len(actual_row_numbers) >= len(df):
                break

    for idx, row in df.iterrows():
        # 使用映射获取实际的Excel行号
        if idx < len(actual_row_numbers):
            actual_excel_row = actual_row_numbers[idx]
        else:
            # 备用方案：简单计算
            actual_excel_row = data_start_row + idx

        # 准备数据
        row_data = {
            "row_idx": actual_excel_row,  # 使用实际的Excel行号
            "req_info": str(row.get('要件の内容')),
            "design_book": str(row.get('パラメータ設計書')),
        }
        content_list.append(row_data)
    return content_list


def check_design_book_completeness(data_list: list) -> Dict[str, Any]:
    """
    检查设计书信息的完整性

    Args:
        data_list: 包含行号、要件内容、设计书信息的数据列表

    Returns:
        检查结果统计
    """
    check_results = []
    statistics = {
        "total_rows": len(data_list),
        "skipped_rows": 0,  # req_info为None的行数
        "checked_rows": 0,  # 实际检查的行数
        "ok_count": 0,      # 设计书信息完整的行数
        "ng_count": 0,      # 设计书信息缺失的行数
        "break_at_row": None  # 在哪一行break的
    }

    consecutive_empty_count = 0  # 连续空要件内容的计数

    for item in data_list:
        row_idx = item.get("row_idx")
        req_info = item.get("req_info")
        design_book = item.get("design_book")

        # 检查req_info是否为None或空字符串
        if req_info is None or str(req_info).strip() in ['None', '', 'nan']:
            consecutive_empty_count += 1
            statistics["skipped_rows"] += 1

            # 如果连续5行要件内容都是空的，则break
            if consecutive_empty_count >= 5:
                statistics["break_at_row"] = row_idx
                logger.info(f"连续5行要件内容为空，在第{row_idx}行停止处理")
                break
            continue
        else:
            # 重置连续空行计数
            consecutive_empty_count = 0

        # req_info不为None，需要检查design_book
        statistics["checked_rows"] += 1

        # 检查design_book是否为None、空字符串、"-"或长度小于3
        design_book_str = str(design_book).strip() if design_book is not None else ""
        if (design_book is None or
            design_book_str in ['None', '', 'nan', '-'] or
            len(design_book_str) < 3):
            check_status = "NG"
            statistics["ng_count"] += 1
        else:
            check_status = "OK"
            statistics["ok_count"] += 1

        check_results.append({
            "行号": row_idx,
            "要件内容": str(req_info),
            "设计书信息": str(design_book) if design_book is not None else "",
            "检查结果": check_status
        })


    return {
        "check_results": check_results,
        "statistics": statistics
    }


def export_check_results_to_excel(check_data: Dict[str, Any], output_path: str) -> str:
    """
    将检查结果导出到Excel文件，包含汇总信息和样式

    Args:
        check_data: 检查结果数据
        output_path: 输出文件路径

    Returns:
        实际的输出文件路径
    """
    check_results = check_data["check_results"]
    statistics = check_data["statistics"]

    # 生成输出文件名（如果没有指定具体文件名）
    if not output_path.endswith('.xlsx'):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(output_path, f"要件设计书照合结果_{timestamp}.xlsx")

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 创建工作簿
    from openpyxl import Workbook
    wb = Workbook()

    # 删除默认工作表
    wb.remove(wb.active)

    # 创建检查结果工作表
    ws_results = wb.create_sheet("检查结果")

    # 添加汇总信息到检查结果工作表顶部
    _add_summary_section(ws_results, statistics)

    # 添加检查结果数据
    _add_results_data(ws_results, check_results, start_row=15)

    # 创建统计信息工作表
    ws_stats = wb.create_sheet("统计信息")
    _add_statistics_sheet(ws_stats, statistics)

    # 保存文件
    wb.save(output_path)

    logger.info(f"检查结果已导出到: {output_path}")
    return output_path


def _add_summary_section(ws, statistics):
    """添加汇总信息到工作表顶部"""
    # 标题
    ws['A1'] = "要件设计书照合实施结果汇总"
    ws['A1'].font = Font(size=16, bold=True)

    # 汇总信息
    summary_data = [
        ["检查时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
        ["总行数", statistics["total_rows"]],
        ["检查行数", statistics["checked_rows"]],
        ["OK数量", statistics["ok_count"]],
        ["NG数量", statistics["ng_count"]],
        ["完整率", f"{statistics['ok_count']}/{statistics['checked_rows']} ({statistics['ok_count']/statistics['checked_rows']*100:.1f}%)" if statistics['checked_rows'] > 0 else "0%"]
    ]

    if statistics.get("break_at_row"):
        summary_data.append(["提前结束", f"在第{statistics['break_at_row']}行因连续空行停止"])

    for i, (key, value) in enumerate(summary_data, start=2):
        ws[f'A{i}'] = key
        ws[f'B{i}'] = value
        ws[f'A{i}'].font = Font(bold=True)

        # 为OK/NG数量添加颜色
        if key == "OK数量":
            ws[f'B{i}'].fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
        elif key == "NG数量" and statistics["ng_count"] > 0:
            ws[f'B{i}'].fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

    # 添加检查规则说明
    rule_start_row = len(summary_data) + 4
    ws[f'A{rule_start_row}'] = "检查规则说明:"
    ws[f'A{rule_start_row}'].font = Font(bold=True, size=12)

    rules = [
        "1. 要件内容为空的行会被跳过",
        "2. 连续5行要件内容为空时停止检查",
        "3. 设计书信息判断规则:",
        "   - OK: 有内容且长度≥3字符",
        "   - NG: 为空、'-'或长度<3字符"
    ]

    for i, rule in enumerate(rules, start=rule_start_row+1):
        ws[f'A{i}'] = rule
        if rule.startswith("   "):
            ws[f'A{i}'].font = Font(italic=True)


def _add_results_data(ws, check_results, start_row=8):
    """添加检查结果数据"""
    # 添加表头
    headers = ["行号", "要件内容", "设计书信息", "检查结果"]
    for col, header in enumerate(headers, start=1):
        cell = ws.cell(row=start_row, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")

    # 添加数据
    for row_idx, result in enumerate(check_results, start=start_row+1):
        ws.cell(row=row_idx, column=1, value=result["行号"])
        ws.cell(row=row_idx, column=2, value=result["要件内容"])
        ws.cell(row=row_idx, column=3, value=result["设计书信息"])

        # 检查结果列添加颜色
        result_cell = ws.cell(row=row_idx, column=4, value=result["检查结果"])
        if result["检查结果"] == "OK":
            result_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
        else:
            result_cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")
        result_cell.alignment = Alignment(horizontal="center")

    # 调整列宽
    ws.column_dimensions['A'].width = 8
    ws.column_dimensions['B'].width = 50
    ws.column_dimensions['C'].width = 30
    ws.column_dimensions['D'].width = 12


def _add_statistics_sheet(ws, statistics):
    """添加统计信息工作表"""
    ws['A1'] = "详细统计信息"
    ws['A1'].font = Font(size=14, bold=True)

    stats_data = [
        ["项目", "数值", "说明"],
        ["总行数", statistics["total_rows"], "Excel中读取的总行数"],
        ["跳过行数", statistics["skipped_rows"], "要件内容为空的行数"],
        ["检查行数", statistics["checked_rows"], "实际进行检查的行数"],
        ["OK数量", statistics["ok_count"], "设计书信息完整的行数(长度≥3且非空)"],
        ["NG数量", statistics["ng_count"], "设计书信息缺失的行数(为空、'-'或长度<3)"],
        ["完整率", f"{statistics['ok_count']}/{statistics['checked_rows']} ({statistics['ok_count']/statistics['checked_rows']*100:.1f}%)" if statistics['checked_rows'] > 0 else "0%", "设计书信息完整的比例"]
    ]

    if statistics.get("break_at_row"):
        stats_data.append(["提前结束行", statistics["break_at_row"], "因连续5行空要件内容而停止的行号"])

    for row_idx, row_data in enumerate(stats_data, start=3):
        for col_idx, value in enumerate(row_data, start=1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            if row_idx == 3:  # 表头
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")

    # 调整列宽
    ws.column_dimensions['A'].width = 15
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 30


def process_requirement_design_verification(excel_source: SourceInfo) -> Dict[str, Any]:
    """
    要件设计书照合实施的主要业务逻辑

    Args:
        excel_source: Excel文件的SourceInfo

    Returns:
        Dict[str, Any]: 处理结果

    Raises:
        FileNotFoundError: 当文件不存在时
        ValueError: 当参数无效时
        Exception: 其他处理错误
    """
    logger.info(f"Excel文件SourceInfo: type={excel_source.type}, uri={excel_source.uri}")

    try:
        # 验证输入参数
        _validate_excel_source_input(excel_source)

        # 读取Excel文件内容进行分析
        excel_file_path = excel_source.uri

        # 使用自定义函数读取数据，获取实际的Excel行号
        data_list = read_req_design_data(excel_source)

        # 基本文件信息
        file_info = {
            "file_path": excel_file_path,
            "file_exists": os.path.exists(excel_file_path),
            "file_size": os.path.getsize(excel_file_path) if os.path.exists(excel_file_path) else 0
        }

        logger.info(f"Excel文件信息: {file_info}")
        logger.info(f"读取到 {len(data_list)} 行数据")

        # 执行设计书完整性检查
        check_data = check_design_book_completeness(data_list)

        # 生成输出文件路径
        input_dir = os.path.dirname(excel_file_path)
        output_dir = os.path.join(input_dir, "check_results")

        # 导出检查结果到Excel
        output_file = export_check_results_to_excel(check_data, output_dir)

        # 返回处理结果
        result = {
            "status": "success",
            "message": "要件设计书照合实施处理完成",
            "output_file": output_file,
        }

        logger.info("要件设计书照合实施处理完成")
        return result

    except Exception as e:
        logger.exception(f"要件设计书照合实施处理失败: {str(e)}")
        raise


def _validate_excel_source_input(excel_source: SourceInfo) -> None:
    """
    验证Excel SourceInfo输入参数的有效性

    Args:
        excel_source: Excel文件的SourceInfo

    Raises:
        ValueError: 当参数无效时
        FileNotFoundError: 当文件不存在时
    """
    # 验证Excel SourceInfo
    if not excel_source:
        raise ValueError("Excel SourceInfo不能为空")

    if not excel_source.uri or not excel_source.uri.strip():
        raise ValueError("Excel文件URI不能为空")

    # 对于local类型，检查文件是否存在
    if excel_source.type == "local":
        if not os.path.exists(excel_source.uri):
            raise FileNotFoundError(f"Excel文件不存在: {excel_source.uri}")

        if not os.path.isfile(excel_source.uri):
            raise ValueError(f"Excel路径不是有效文件: {excel_source.uri}")

        # 检查文件扩展名
        file_ext = os.path.splitext(excel_source.uri)[1].lower()
        if file_ext not in ['.xlsx', '.xls']:
            raise ValueError(f"不支持的文件格式: {file_ext}，仅支持.xlsx和.xls文件")

    logger.info("Excel SourceInfo输入参数验证通过")
