
selected_testcase_results = [
{'ARチケットNO': 'MET19PFV3-21732', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_第0階層⇒第一階層へ移動 1500Wコンセント,2400Wコンセント,7200Wコンセント', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '是', '原因': '涉及阶层移动', 'testcases': [{'示例': '1444', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-はい\n【#476】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择はい\n-检查切替Type', '画面显示': 'Type切替（D）', 'sheet_name': '車両設定', 'number': '1444'}, {'示例': '1445', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-はい\n【#476】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '重置油维护信息-短压“ENTER”后发送：B_OMWI2=3\n重置成功', '画面显示': '割入显示：オイルメンテナンス情報のリセットが完了しました\n【#528】', 'sheet_name': '車両設定', 'number': '1445'}, {'示例': '1446', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-はい\n【#476】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '重置油维护信息-重置成功等待6s', '画面显示': '回到第一阶层，光标选中オイルメンテナンス', 'sheet_name': '車両設定', 'number': '1446'}, {'示例': '1460', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在オイルメンテナンス第二阶层\n发送车速8km/h-查看内容显示', '画面显示': '灰显', 'sheet_name': '車両設定', 'number': '1460'}, {'示例': '1461', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在オイルメンテナンス第二阶层\n发送车速8km/h-短按“ENTER”', '画面显示': '无响应', 'sheet_name': '車両設定', 'number': '1461'}, {'示例': '1462', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在オイルメンテナンス第二阶层\n发送车速8km/h-长按“ENTER”', '画面显示': '无响应', 'sheet_name': '車両設定', 'number': '1462'}, {'示例': '1464', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '表示设定初期化', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '设定时间为保持信号发送，手动进行表示设定初始化-查看オイルメンテナンス显示', '画面显示': '显示内容与送信值保持一致', 'sheet_name': '車両設定', 'number': '1464'}, {'示例': '1471', '模块名称': 'マイセッティング\n【#3565】', '确认点': '第一阶层-通常显示', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定第一阶层\n光标选择"マイセッティング"-未按下“ENTER”', '画面显示': '光标保持在マイセッティング', 'sheet_name': '車両設定', 'number': '1471'}, {'示例': '1479', '模块名称': 'マイセッティング\n【#3565】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定第一阶层光标选择"マイセッティング"\n发送车速7.9km/h-点击“BACK”', '画面显示': '返回第0阶层，光标选择车辆设定。', 'sheet_name': '車両設定', 'number': '1479'}, {'示例': '1574', '确认点': '第一阶层-通常显示', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中       菜单项-短按“ENTER”', '画面显示': '按下时立即响应\n进入下一阶层，光标选中当前设置值', 'sheet_name': '車両設定', 'number': '1574'}, {'示例': '1575', '确认点': '第一阶层-通常显示', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中       菜单项-长按“ENTER”', '画面显示': '按下时立即响应\n进入下一阶层，光标选中当前设置值', 'sheet_name': '車両設定', 'number': '1575'}, {'示例': '1663', '模块名称': '"★"(セキュリティセンサ)', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中"★"(セキュリティセンサ)菜单项\n发送车速8km/h-长按“ENTER”', '画面显示': '按下无响应', 'sheet_name': '車両設定', 'number': '1663'}, {'示例': '1664', '模块名称': '"★"(セキュリティセンサ)', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中"★"(セキュリティセンサ)菜单项\n发送车速8km/h-点击“BACK”', '画面显示': '返回第0阶层，光标选择车辆设定。', 'sheet_name': '車両設定', 'number': '1664'}, {'示例': '1665', '模块名称': '"★"(セキュリティセンサ)', '确认点': '表示设定初期化', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '设定时间为保持信号发送，手动进行表示设定初始化-查看"★"(セキュリティセンサ)显示', '画面显示': '显示内容与送信值保持一致', 'sheet_name': '車両設定', 'number': '1665'}, {'示例': '1666', '模块名称': '"★"(セキュリティセンサ)', '确认点': 'EEPROM記憶-IG', '+B': 'ON', 'IG': 'ON', '电压': '13.160V', 'Precondition': '保持信号发送，IG ON→OFF→ON-查看"★"(セキュリティセンサ)显示', '画面显示': '显示内容与送信值保持一致', 'sheet_name': '車両設定', 'number': '1666'}, {'示例': '1667', '模块名称': '"★"(セキュリティセンサ)', '确认点': 'EEPROM記憶-+B', '+B': 'ON', 'IG': 'ON', '电压': '13.161V', 'Precondition': '保持信号发送，+B ON→OFF→ON,IG ON→OFF→ON-查看"★"(セキュリティセンサ)显示', '画面显示': '显示内容与送信值保持一致', 'sheet_name': '車両設定', 'number': '1667'}, {'示例': '1668', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-设定有无', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号：\nSRP_PRE = 未受信-进入车辆设定查看显示', '画面显示': '不显示SRP', 'sheet_name': '車両設定', 'number': '1668'}, {'示例': '1669', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-设定有无-有', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号：\nSRP_PRE = 0-进入车辆设定查看显示', '画面显示': '不显示SRP', 'sheet_name': '車両設定', 'number': '1669'}, {'示例': '1682', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项-短按“BACK“', '画面显示': '返回第0阶层，光标选择车辆设定。', 'sheet_name': '車両設定', 'number': '1682'}, {'示例': '1683', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项\n发送车速7.9km/h-查看内容显示', '画面显示': '正常显示', 'sheet_name': '車両設定', 'number': '1683'}, {'示例': '1684', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项\n发送车速7.9km/h-选中不同项目短按“ENTER”', '画面显示': '按下时立即响应\nMINTRSOF = 1', 'sheet_name': '車両設定', 'number': '1684'}, {'示例': '1685', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项\n发送车速7.9km/h-选中不同项目长按“ENTER”', '画面显示': '按下时立即响应\nMINTRSOF = 1', 'sheet_name': '車両設定', 'number': '1685'}, {'示例': '1688', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项\n发送车速8km/h-短按“ENTER”', '画面显示': '按下无响应', 'sheet_name': '車両設定', 'number': '1688'}, {'示例': '1701', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量-短按“BACK“', '画面显示': '返回第0阶层，光标选择车辆设定。', 'sheet_name': '車両設定', 'number': '1701'}, {'示例': '1702', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速7.9km/h-查看内容显示', '画面显示': '正常显示', 'sheet_name': '車両設定', 'number': '1702'}, {'示例': '1703', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速7.9km/h-选中不同项目短按“ENTER”', '画面显示': '按下时立即响应\n进入下一阶层，光标选中警報音量', 'sheet_name': '車両設定', 'number': '1703'}, {'示例': '1704', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速7.9km/h-选中不同项目长按“ENTER”', '画面显示': '按下时立即响应\n进入下一阶层，光标选中警報音量', 'sheet_name': '車両設定', 'number': '1704'}, {'示例': '1705', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速7.9km/h-点击“BACK”', '画面显示': '返回第0阶层，光标选择车辆设定。', 'sheet_name': '車両設定', 'number': '1705'}, {'示例': '1706', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速8km/h-查看内容显示', '画面显示': '灰显', 'sheet_name': '車両設定', 'number': '1706'}, {'示例': '1708', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量\n发送车速8km/h-长按“ENTER”', '画面显示': '按下无响应', 'sheet_name': '車両設定', 'number': '1708'}, {'示例': '1787', '模块名称': '7200Wコンセント\n【#5964】', '确认点': '第0阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中“7200Wコンセント”菜单项\n发送车速8km/h-查看内容显示', '画面显示': '项目灰显--', 'sheet_name': '車両設定', 'number': '1787'}, {'示例': '86', '模块名称': '単位\n【#461】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在“単位”第二阶层\n发送车速为7km/h；', '画面显示': '按下立即响应切换单位', 'sheet_name': '表示設定', 'number': '86'}, {'示例': '89', '模块名称': '単位\n【#461】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在“単位”第二阶层\n发送车速为8km/h；', '画面显示': '所有项目置灰', 'sheet_name': '表示設定', 'number': '89'}, {'示例': '103', '模块名称': '単位\n【#461】', '确认点': '单位显示项目确认-ｺﾝﾍ/HV车辆单位确认-欧州パターン\nパターンC', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '1.send PTSYS=1,DEST_BDB=56h\n2.进入単位第二阶层', '画面显示': '1.km(km/L)\n2.km(L/100km)', 'sheet_name': '表示設定', 'number': '103'}, {'示例': '107', '模块名称': '単位\n【#461】', '确认点': '单位显示项目确认-ｺﾝﾍ/HV车辆单位确认-欧州パターン\nパターンC', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '1.send PTSYS=1,C_CODE1=7,C_CODE1=0,C_CODE1=8\n2.进入単位第二阶层', '画面显示': '1.km(km/L)\n2.km(L/100km)', 'sheet_name': '表示設定', 'number': '107'}, {'示例': '118', '模块名称': '単位\n【#461】', '确认点': '单位显示项目确认-PHEV车辆单位确认-欧州パターン\nパターンF', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '1.send PTSYS=4,DEST_BDB=56h\n2.进入単位第二阶层', '画面显示': '1.km(km/L, km/kWh)\n2.km(L/100km, kWh/100km)', 'sheet_name': '表示設定', 'number': '118'}, {'示例': '134', '模块名称': '単位\n【#461】', '确认点': '单位显示项目确认-BEV车辆单位确认-欧州パターン\nパターンg', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '1.send PTSYS=5,C_CODE1=7,C_CODE1=0,C_CODE1=9\n2.进入単位第二阶层', '画面显示': '1.km(km/kWh)\n2.km(kWh/100km)', 'sheet_name': '表示設定', 'number': '134'}, {'示例': '209', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-表示\n【#6054】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中表示', '画面显示': 'Type（AⅠ）按下立即生效', 'sheet_name': '表示設定', 'number': '209'}, {'示例': '333', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第四阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入アナログメーター切替第四阶层\n发送车速为7km/h；', '画面显示': '正常显示', 'sheet_name': '表示設定', 'number': '333'}, {'示例': '357', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-ウイジェット\n【#4636】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中ウイジェット', '画面显示': '响应ON/OFF切换', 'sheet_name': '表示設定', 'number': '357'}, {'示例': '393', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前进入運転支援第三阶层\n发送车速为7km/h；', '画面显示': '按下立即响应\nON/OFF切换', 'sheet_name': '表示設定', 'number': '393'}, {'示例': '493', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第五阶层-履歴を消去しますか？\n【#5935】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入燃費推移消去第五阶层', '画面显示': '-', 'sheet_name': '表示設定', 'number': '493'}, {'示例': '529', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第五阶层-過去燃費消去 第五阶层画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在グラフ情報切替去第五阶层', '画面显示': '返回第四阶层，光标选中過去燃費更新 ', 'sheet_name': '表示設定', 'number': '529'}, {'示例': '660', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第五阶层-履歴を更新しますか？\n【#6064】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中はい', '画面显示': '6s后返回第四阶层，光标选中過去電費更新', 'sheet_name': '表示設定', 'number': '660'}, {'示例': '875', '模块名称': '"★"(EVインジケーター表示)', '确认点': '第一阶层-"★"(EVインジケーター表示)', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“EVインジケーター表示”', 'sheet_name': '表示設定', 'number': '875'}, {'示例': '941', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-燃費グラフ\n【#568】\n(PHEV,BEV以外)', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中燃費グラフ', '画面显示': '◎：設定を変更できます\n【#496】', 'sheet_name': '表示設定', 'number': '941'}, {'示例': '998', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入グラフ情報切替第四阶层', '画面显示': '回到第0阶层光标选中第一个项目', 'sheet_name': '表示設定', 'number': '998'}, {'示例': '1028', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第三阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入電費第三阶层', '画面显示': '回到第0阶层光标选中第一个项目', 'sheet_name': '表示設定', 'number': '1028'}, {'示例': '1063', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-燃費グラフ\n【#568】\n(PHEV)', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中燃費グラフ', '画面显示': '燃費グラフ\n【#568】', 'sheet_name': '表示設定', 'number': '1063'}], 'reason': '車両設定_1444的语义匹配度为0.7254431665937785；車両設定_1445的语义匹配度为0.7299748247320886；車両設定_1446的语义匹配度为0.7446415150442867；車両設定_1460的语义匹配度为0.7274953600848117；車両設定_1461的语义匹配度为0.7441628569761517；車両設定_1462的语义匹配度为0.7263905889352388；車両設定_1464的语义匹配度为0.7327623552178969；車両設定_1471的语义匹配度为0.7250477083677102；車両設定_1479的语义匹配度为0.7359399070445896；車両設定_1574的语义匹配度为0.7259829822048723；車両設定_1575的语义匹配度为0.7291731284837527；車両設定_1663的语义匹配度为0.7454728895071302；車両設定_1664的语义匹配度为0.7458063345738298；車両設定_1665的语义匹配度为0.7647284301680757；車両設定_1666的语义匹配度为0.7657223861715324；車両設定_1667的语义匹配度为0.7375911311770919；車両設定_1668的语义匹配度为0.7319354950053076；車両設定_1669的语义匹配度为0.7745723078830836；車両設定_1682的语义匹配度为0.7393472084507872；車両設定_1683的语义匹配度为0.7281498071742327；車両設定_1684的语义匹配度为0.7483732619721969；車両設定_1685的语义匹配度为0.7490745796838999；車両設定_1688的语义匹配度为0.7612042824113422；車両設定_1701的语义匹配度为0.7371998853686409；車両設定_1702的语义匹配度为0.7409048950748817；車両設定_1703的语义匹配度为0.7578391428965253；車両設定_1704的语义匹配度为0.7558560392660669；車両設定_1705的语义匹配度为0.7545141371416818；車両設定_1706的语义匹配度为0.7300980021088974；車両設定_1708的语义匹配度为0.7666618530718938；車両設定_1787的语义匹配度为0.7283009367039517；表示設定_86的语义匹配度为0.7257097721484668；表示設定_89的语义匹配度为0.7324484589134953；表示設定_103的语义匹配度为0.7283606105647347；表示設定_107的语义匹配度为0.7340048784516948；表示設定_118的语义匹配度为0.7257027243478865；表示設定_134的语义匹配度为0.7296353692836318；表示設定_209的语义匹配度为0.725676218698103；表示設定_333的语义匹配度为0.7305454389070937；表示設定_357的语义匹配度为0.7388774241741188；表示設定_393的语义匹配度为0.7288115142009853；表示設定_493的语义匹配度为0.7365193078157576；表示設定_529的语义匹配度为0.7288115142009853；表示設定_660的语义匹配度为0.7260645002192071；表示設定_875的语义匹配度为0.7250814390761477；表示設定_941的语义匹配度为0.7270944693994644；表示設定_998的语义匹配度为0.7250814390761477；表示設定_1028的语义匹配度为0.7293249872686159；表示設定_1063的语义匹配度为0.7342809833760419；'}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行状态无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与切替动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与控制选项无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与默认选项无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与ST点火无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储等待无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-21517', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_警報音量の階層修正', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行置灰无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画切换无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与显示控制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与画面切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键操作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应延迟无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与画面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与走形迟滞无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-35197', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_「表示シーン拡張」を追加\u3000', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列顺序无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显一致性无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行中置灰无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画跳动无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '是', '原因': '涉及显示联动逻辑', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6331584106462634'}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与CSTM设定无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与按键无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与页面项目数无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与切替动作无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无直接关系', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-28447', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_メータタイプ設定\u30006dial-ViewにウィジェットON/OFF追加', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容一致性无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标颜色位置无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显一致性无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与置灰逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '是', '原因': '涉及ON/OFF切换效果', 'testcases': [{'示例': '700', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-スポーツ\n【#5921】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在メーターデザイン第二阶层光标选中スポーツ', '画面显示': 'スポーツ\n【#5921】', 'sheet_name': '表示設定', 'number': '700'}, {'示例': '701', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-スポーツ\n【#5921】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在メーターデザイン第二阶层光标选中スポーツ', '画面显示': '◎：設定を変更できます\n【#496】', 'sheet_name': '表示設定', 'number': '701'}], 'reason': '表示設定_700的语义匹配度为0.7259328934392001；表示設定_701的语义匹配度为0.7259328934392001；'}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '是', '原因': '涉及设置切换动画', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.716305628552972'}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '是', '原因': '涉及表盘选项设置', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.7158806968023048'}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与特定功能控制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与特殊画面无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号输入无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '是', '原因': '涉及切替动作正确性', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.7175940272194801'}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显文言无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与默认选项无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储等待无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-37059', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_Speed Limiter\u3000運転状態により自動起動\u3000が削除', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与文字行数无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行中置灰无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与切替动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画跳动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与CSTM设定无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形中灰显', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6814976352840423'}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '是', '原因': '涉及灰显与选项显示', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6752499450711879'}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形迟滞曲线', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6777224443983408'}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '是', '原因': '涉及灰显状态出力', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6772614323062096'}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形置灰现象', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6593395706453956'}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与ST点火无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储等待无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-35446', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_6メーター(表示設定 - メータータイプ設定)のカスタマイズ項目削除', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示设置无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与置灰逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画切换无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画跳动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与CSTM控制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应延迟无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显文言无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与默认选项无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储等待无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-21733', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_1500Wコンセント的メータEEPROM記憶由"しない"变为“する”', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与文字行数无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '是', '原因': '涉及EEPROM存储变更', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.7091266105438396'}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与CSTM设定无直接关系', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与页面项目数无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与切替动作无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与走形状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化设定无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面交互无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与界面交互无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与走形状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启测试无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无直接关系', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '是', '原因': '涉及EEPROM存储变更', 'testcases': [{'示例': '44', '模块名称': '時計設定\n【#450】', '确认点': 'EEPROM記憶-IG', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '设定时间为2:00，IG ON→OFF→ON', '画面显示': '基于设定时间累计', 'sheet_name': 'カスタマイズ項目一覧', 'number': '44'}, {'示例': '45', '模块名称': '時計設定\n【#450】', '确认点': 'EEPROM記憶-+B', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '设定时间，+B ON→OFF→ON,IG ON→OFF→ON,', '画面显示': '显示初始值', 'sheet_name': 'カスタマイズ項目一覧', 'number': '45'}, {'示例': '604', '模块名称': '"★BSM"\n【#487】', '确认点': 'EEPROM記憶-+B', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '保持信号发送，+B ON→OFF→ON,IG ON→OFF→ON', '画面显示': '显示内容与送信值保持一致', 'sheet_name': 'カスタマイズ項目一覧', 'number': '604'}, {'示例': '1107', '模块名称': '"★RCTA"\n【#4514】', '确认点': '第0阶层-设定有无', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号：RCTA_SWR = 未受信', '画面显示': '不显示RCTA功能', 'sheet_name': 'カスタマイズ項目一覧', 'number': '1107'}, {'示例': '104', '模块名称': '充電設定\n【#1841】', '确认点': '第二阶层-急速充電電力\n【#3935】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': 'send P_DPWAVA=10b光标选择“急速充電電力”-查看辅助文言', '画面显示': '◎：設定を変更できます\n【#496】-◎：設定を変更できます', 'sheet_name': '車両設定', 'number': '104'}, {'示例': '925', '模块名称': '"★発進遅れ告知"\n【#6046】', '确认点': '第三阶层-信号切替り告知\n【#6028】-標準\n【#3004】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“標準”-查看辅助文言', '画面显示': '◎：決定\n【#420】-◎：決定', 'sheet_name': '車両設定', 'number': '925'}, {'示例': '926', '模块名称': '"★発進遅れ告知"\n【#6046】', '确认点': '第三阶层-信号切替り告知\n【#6028】-標準\n【#3004】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“標準”-检查切替Type', '画面显示': '切替Type（BⅠ）', 'sheet_name': '車両設定', 'number': '926'}, {'示例': '1192', '模块名称': 'ハイビームライト\n【#6032】', '确认点': '第三阶层-ややぼんやり\n【#6038】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“ややぼんやり”-检查切替Type', '画面显示': '切替Type（BⅠ）', 'sheet_name': '車両設定', 'number': '1192'}, {'示例': '1394', '模块名称': 'Scheduled Maintenance\n【#2480】', '确认点': '第二阶层-はい\n【#476】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择Yes\n-查看辅助文言', '画面显示': '""◎"":Select\n【#420】', 'sheet_name': '車両設定', 'number': '1394'}, {'示例': '1442', '模块名称': 'オイルメンテナンス\n【#480】', '确认点': '第二阶层-はい\n【#476】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择はい\n-短压“ENTER”', '画面显示': '按下立即响应\nOMRS_2=1\n重置油维护信息', 'sheet_name': '車両設定', 'number': '1442'}, {'示例': '1490', '模块名称': 'マイセッティング\n【#3565】', '确认点': '第二阶层-設定\n【#2243】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择設定\n发送信号：PSEN = 0-长按“ENTER”', '画面显示': '无响应\n出力：PSR_PSSR=0b', 'sheet_name': '車両設定', 'number': '1490'}, {'示例': '1491', '模块名称': 'マイセッティング\n【#3565】', '确认点': '第二阶层-設定\n【#2243】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择設定\n发送信号：PSEN = 1-短按“ENTER”', '画面显示': '按下立即响应\n出力：PSR_PSSR=1\n进入下一阶层，光标选中第一个项目-无出力', 'sheet_name': '車両設定', 'number': '1491'}, {'示例': '1569', '确认点': '第一阶层-设定有无-有', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号：\nPTSYS=2-查看项目', 'sheet_name': '車両設定', 'number': '1569'}, {'示例': '1594', '确认点': '第二阶层-Normal\n【#459】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择Normal\n-未按下“ENTER”', '画面显示': 'SSMMODE=0', 'sheet_name': '車両設定', 'number': '1594'}, {'示例': '1595', '确认点': '第二阶层-Normal\n【#459】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择Normal\n-短按“ENTER”', '画面显示': '按下立即响应\nSSMMODE=1', 'sheet_name': '車両設定', 'number': '1595'}, {'示例': '1680', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-通常显示', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送信号SRP_IND = 1-短按“ENTER”', '画面显示': '按下时立即响应\nSRPMSW = 1', 'sheet_name': '車両設定', 'number': '1680'}, {'示例': '1681', '模块名称': 'SRP\n【#5534】', '确认点': '第一阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前已选中SRP菜单项-长按“BACK“', '画面显示': '返回0阶层，光标选择第1项。', 'sheet_name': '車両設定', 'number': '1681'}, {'示例': '1700', '模块名称': '警報音量\n【#5669】', '确认点': '第一阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入车辆设定光标选中警報音量-长按“BACK“', '画面显示': '返回0阶层，光标选择第1项。', 'sheet_name': '車両設定', 'number': '1700'}, {'示例': '21', '模块名称': '言語\n【#460】', '确认点': '第一阶层-言語\n【#460】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“言語”', '画面显示': '可以进入下一阶层', 'sheet_name': '表示設定', 'number': '21'}, {'示例': '22', '模块名称': '言語\n【#460】', '确认点': '第一阶层-言語\n【#460】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“言語”', '画面显示': '进入言語第二阶层，光标选中设定项', 'sheet_name': '表示設定', 'number': '22'}, {'示例': '751', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-表示\n【#6054】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入Off-Road-View第三阶层', '画面显示': '按下立即响应ON/OFF切换', 'sheet_name': '表示設定', 'number': '751'}, {'示例': '752', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-表示\n【#6054】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入Off-Road-View第三阶层', '画面显示': '按下立即响应ON/OFF切换', 'sheet_name': '表示設定', 'number': '752'}, {'示例': '766', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-6dial-View\n【#5924】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在メーターデザイン第二阶层光标选中6dial-View', '画面显示': '进入6メーター第二阶层，光标选中表示', 'sheet_name': '表示設定', 'number': '766'}, {'示例': '767', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-6dial-View\n【#5924】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在メーターデザイン第二阶层光标选中6dial-View', '画面显示': '进入6メーター第二阶层，光标选中表示', 'sheet_name': '表示設定', 'number': '767'}, {'示例': '782', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入6dial-View第三阶层\n发送车速为8km/h；', '画面显示': '无响应', 'sheet_name': '表示設定', 'number': '782'}, {'示例': '783', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第三阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入6dial-View第三阶层\n发送车速为8km/h；', '画面显示': '回到上一阶层光标选中\n6dial-View', 'sheet_name': '表示設定', 'number': '783'}, {'示例': '814', '模块名称': 'REVインジケーター\n【#1659】', '确认点': '第二阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前在REVインジケーター第二阶层', '画面显示': '可进行循环移动', 'sheet_name': '表示設定', 'number': '814'}, {'示例': '815', '模块名称': 'REVインジケーター\n【#1659】', '确认点': '第二阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送车速为10km/h；\n当前在“単位”第二阶层', '画面显示': '第二阶层所有项目灰显', 'sheet_name': '表示設定', 'number': '815'}, {'示例': '933', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第一阶层-画面迁移', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择“インフォメーション設定”', '画面显示': '回到第0阶层光标选中第一个项目', 'sheet_name': '表示設定', 'number': '933'}, {'示例': '990', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-給油後平均燃費\n【#388】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择給油後平均燃費', '画面显示': '◎：決定\n【#420】', 'sheet_name': '表示設定', 'number': '990'}, {'示例': '1061', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-EEPROM記憶', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': 'IG ON→OFF→ON', '画面显示': '显示内容与前回保持一致', 'sheet_name': '表示設定', 'number': '1061'}, {'示例': '1088', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第三阶层-電費\n【#5939】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择電費', '画面显示': '按下立即响应进入下一阶层，光标选中前回设定项', 'sheet_name': '表示設定', 'number': '1088'}, {'示例': '1134', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-通常显示', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '进入電費第四阶层', '画面显示': '電費\n【#5939】', 'sheet_name': '表示設定', 'number': '1134'}, {'示例': '1324', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-エンジン/AT油温\n【#5236】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中エンジン/AT油温', '画面显示': '响应ON/OFF切换', 'sheet_name': '表示設定', 'number': '1324'}, {'示例': '1325', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-エンジン/AT油温\n【#5236】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中エンジン/AT油温', 'sheet_name': '表示設定', 'number': '1325'}, {'示例': '1602', '模块名称': 'TRIPB項目選択\n【#4939】', '确认点': '第三阶层-(下段：現在の設定値)-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送车速为10km/h；\n当前在(下段：現在の設定値)第三阶层', '画面显示': '可进行循环移动', 'sheet_name': '表示設定', 'number': '1602'}, {'示例': '1603', '模块名称': 'TRIPB項目選択\n【#4939】', '确认点': 'EEPROM記憶', '+B': 'IG', 'IG': 'ON', '电压': '13.5V', 'Precondition': 'IG ON→OFF→ON', '画面显示': '显示内容与前回保持一致', 'sheet_name': '表示設定', 'number': '1603'}], 'reason': 'カスタマイズ項目一覧_44的语义匹配度为0.7292056708925632；カスタマイズ項目一覧_45的语义匹配度为0.732972892221639；カスタマイズ項目一覧_604的语义匹配度为0.7271634071446356；カスタマイズ項目一覧_1107的语义匹配度为0.7272998055985649；車両設定_104的语义匹配度为0.7270792968288815；車両設定_925的语义匹配度为0.7279152615097493；車両設定_926的语义匹配度为0.7322416572871937；車両設定_1192的语义匹配度为0.7258491293707896；車両設定_1394的语义匹配度为0.7412617462629866；車両設定_1442的语义匹配度为0.7359725620749816；車両設定_1490的语义匹配度为0.7335258283444626；車両設定_1491的语义匹配度为0.747316314257073；車両設定_1569的语义匹配度为0.7350576492878641；車両設定_1594的语义匹配度为0.7481583371469622；車両設定_1595的语义匹配度为0.7573966352453799；車両設定_1680的语义匹配度为0.747179144247716；車両設定_1681的语义匹配度为0.7590523472616697；車両設定_1700的语义匹配度为0.7276440606917688；表示設定_21的语义匹配度为0.7270427857861377；表示設定_22的语义匹配度为0.7365985556758322；表示設定_751的语义匹配度为0.727636574794647；表示設定_752的语义匹配度为0.7425282997209334；表示設定_766的语义匹配度为0.7256296948085128；表示設定_767的语义匹配度为0.7402953922797635；表示設定_782的语义匹配度为0.7251357030684994；表示設定_783的语义匹配度为0.7399590602365486；表示設定_814的语义匹配度为0.7259691228714269；表示設定_815的语义匹配度为0.7381431884067743；表示設定_933的语义匹配度为0.7263444441371416；表示設定_990的语义匹配度为0.7263444441371416；表示設定_1061的语义匹配度为0.7263444441371416；表示設定_1088的语义匹配度为0.7263444441371416；表示設定_1134的语义匹配度为0.727692423896719；表示設定_1324的语义匹配度为0.7279428696468359；表示設定_1325的语义匹配度为0.7298218904830083；表示設定_1602的语义匹配度为0.7295804212158894；表示設定_1603的语义匹配度为0.7294300284258843；'}]}
,{'ARチケットNO': 'MET19PFV3-3624', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_パワトレ判定シートの削除', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行中置灰无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画切换无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画跳动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与CSTM设定无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应延迟无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形中灰显', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6541450813920044'}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '是', '原因': '涉及灰显与选项', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6481732751123883'}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形置灰', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6368523998602844'}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '是', '原因': '涉及灰显出力', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6416023375142544'}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '是', '原因': '涉及走形置灰', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6591593536161561'}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与ST点火无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储等待无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-453', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC R_燃費グラフはFCVの時、非表示から表示に変更', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与置灰逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画切换无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画跳动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '是', '原因': '涉及显示联动逻辑', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6823549345601915'}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '是', '原因': '涉及SOC显示逻辑', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.7158347645372679'}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回功能无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应延迟无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '是', '原因': '涉及信号控制显示', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6951731921391564'}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '是', '原因': '涉及信号显示逻辑', 'testcases': [], 'reason': '所有的测试用例中语义匹配度最高为0.6791471229986447'}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与切替现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-5410', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_表示設定/メータタイプ設定/ECOに「電費」を追加', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与显示内容无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与置灰逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '是', '原因': '涉及ECO相关显示控制', 'testcases': [{'示例': '328', '模块名称': 'メータータイプ設定\n【#5926】', '确认点': '第二阶层-第四阶层-ドライブモード連動\n【#4941】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选中ドライブモード連動', '画面显示': 'Type（BII）按下立即高亮', 'sheet_name': '表示設定', 'number': '328'}], 'reason': '表示設定_328的语义匹配度为0.7316628459266131；'}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与界面卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键响应无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-5410', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_表示設定/インフォメーション設定/電費グラフの補足説明変更', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与文言显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '与标题无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行置灰无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与ECO相关设置无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与辅助文言无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与表盘无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-5410', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_表示設定/インフォメーション設定/エコジャッジの階層構造を変更(第三階層を定義）表示設定/インフォメーション設定/エコジャッジのON/OFFの項目名を「表示」へ変更', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与文言灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '与图标无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '与文字排列无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '与行数限制无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '是', '原因': '涉及第三阶层定义', 'testcases': [{'示例': '1111', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-リセット間平均燃費\n【#385】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择リセット間平均燃費', '画面显示': '-', 'sheet_name': '表示設定', 'number': '1111'}, {'示例': '1112', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-リセット間平均燃費\n【#385】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择リセット間平均燃費', '画面显示': 'Type（B I）', 'sheet_name': '表示設定', 'number': '1112'}, {'示例': '1127', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-走行中操作', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '发送车速为10km/h；', '画面显示': '燃費第四阶层所有项目灰显', 'sheet_name': '表示設定', 'number': '1127'}], 'reason': '表示設定_1111的语义匹配度为0.73079894983766；表示設定_1112的语义匹配度为0.7375597566649114；表示設定_1127的语义匹配度为0.728465802403991；'}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '与亮度无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '与排列顺序无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '与文言灰显无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '与走行中置灰无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '与按键时长无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '是', '原因': '涉及ON/OFF切换显示', 'testcases': [{'示例': '1107', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-始動後平均燃費\n【#386】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择始動後平均燃費', '画面显示': '返回上一阶层光标选中燃費', 'sheet_name': '表示設定', 'number': '1107'}, {'示例': '1108', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-始動後平均燃費\n【#386】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '当前选择始動後平均燃費', '画面显示': '光标选中始動後平均燃費', 'sheet_name': '表示設定', 'number': '1108'}, {'示例': '1111', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-リセット間平均燃費\n【#385】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择リセット間平均燃費', '画面显示': '-', 'sheet_name': '表示設定', 'number': '1111'}, {'示例': '1112', '模块名称': 'インフォメーション設定\n【#5928】', '确认点': '第二阶层-第四阶层-リセット間平均燃費\n【#385】', '+B': 'ON', 'IG': 'ON', '电压': '13.5V', 'Precondition': '光标选择リセット間平均燃費', '画面显示': 'Type（B I）', 'sheet_name': '表示設定', 'number': '1112'}], 'reason': '表示設定_1107的语义匹配度为0.7374596237780336；表示設定_1108的语义匹配度为0.7374596237780336；表示設定_1111的语义匹配度为0.731433033917738；表示設定_1112的语义匹配度为0.7250117965679623；'}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '与动画无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '与特殊逻辑无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '与联动显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '与ECO相关显示无关', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '与卡顿无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '与循环显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '与按键异常无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '与返回按键无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '与响应延迟无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与单选项无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '与灰显项目无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '与诊断清除无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与按键出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '与信号切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '与信号显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '与信号关联无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '与信号途绝无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与信号复归无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '与信号跳变无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '与出力时机无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '与项目数无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '与迁移动作无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '与计时打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '与灰显文言无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '与语言单位无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '与互斥性无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '与数值上限无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '与初始化无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '与界面打断无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '与返回现象无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '与迟滞曲线无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '与灰显出力无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '与单双眼切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '与走形置灰无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '与语言切换无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '与电源状态无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '与异常测试无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '与重启显示无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '与高负载无关', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '与存储无关', 'testcases': [], 'reason': ''}]}
,{'ARチケットNO': 'MET19PFV3-32740', 'エピック名': 'MET-G_CSTMLST-CSTD_SoC A_文言No.修正\n\n充電スケジュール【#1856】\u3000→\u3000【#5945】\n外部給電【#1975】\u3000→\u3000【#5813】\n±0【#2629】\u3000→\u30000【#6110】\n先行車発進告知【#680】\u3000→\u3000【#6123】\n◎：決定【#420】\u3000→\u3000◎：設定を変更できます【#496】', 'checkitems': [{'类型': '基本内容测试', 'NO.': '1', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '文言内容未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '2', '检查项分类': 'SETTING的通常显示清查-图标ICON的颜色位置，图标内容', '是否要求': '〇', '是否相关': '否', '原因': '图标未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '3', '检查项分类': 'SETTING的通常显示清查-文字排列', '是否要求': '〇', '是否相关': '否', '原因': '文字排列未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '4', '检查项分类': 'SETTING的通常显示清查-文言的行数最大值（参照SETTING式样书）', '是否要求': '〇', '是否相关': '否', '原因': '文言行数未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '5', '检查项分类': 'SETTING的通常显示清查-非第一阶层需要核对标题', '是否要求': '〇', '是否相关': '否', '原因': '标题未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '6', '检查项分类': 'SETTING的通常显示清查-文言的亮度一致性', '是否要求': '〇', '是否相关': '否', '原因': '亮度未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '7', '检查项分类': 'SETTING的通常显示清查-文言内容的排列（需要与式样书的排列一致）', '是否要求': '〇', '是否相关': '否', '原因': '文言排列未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '8', '检查项分类': 'SETTING的通常显示清查-文言灰显一致性', '是否要求': '〇', '是否相关': '否', '原因': '文言灰显未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '9', '检查项分类': '显示逻辑检查-第零阶层走行中无需置灰；其他阶层走行中，需要判定是否置灰', '是否要求': '〇', '是否相关': '否', '原因': '显示逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '10', '检查项分类': '显示逻辑检查-短按的判定时长，长按的判定时长', '是否要求': '〇', '是否相关': '否', '原因': '按键时长未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '11', '检查项分类': '显示逻辑检查-选项ONOFF（包含按键或者信号实现切替动作）表示的切替效果和动画', '是否要求': '〇', '是否相关': '否', '原因': '切替动画未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '12', '检查项分类': '显示逻辑检查-进入setting后的切替动画（检查是否跳动,ONOFF设置是否发生变化）', '是否要求': '〇', '是否相关': '否', '原因': '切替动画未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '13', '检查项分类': '显示逻辑检查-存在特殊逻辑的模块：PCS的设置选项，某些互斥设置选项，单双眼下表盘的选项设置,表盘的风格设定选项', '是否要求': '〇', '是否相关': '否', '原因': '无特殊逻辑变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '14', '检查项分类': '显示逻辑检查-同一个机能但是显示在不同地方显示不同或者功能存在联动：日历，调光，SOC残量显示,HUD调光等', '是否要求': '〇', '是否相关': '否', '原因': '无联动显示变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '15', '检查项分类': '显示逻辑检查-设定后查看是否需要进行ERROM存储', '是否要求': '〇', '是否相关': '否', '原因': '存储逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '16', '检查项分类': '显示逻辑检查-具体机能受到CSTM设定有无控制的选项：HUD ON/OFF,ECO灯和EV灯，ECO ZONE条，表示设定里的ECO RUN的ONOFF设定以及其他机能设定', '是否要求': '〇', '是否相关': '否', '原因': '无CSTM设定变更', 'testcases': [], 'reason': ''}, {'类型': '基本内容测试', 'NO.': '17', '检查项分类': '显示逻辑检查-特殊画面进入退出界面是否存在卡顿，默认的选项是否指向NO', '是否要求': '〇', '是否相关': '否', '原因': '无特殊画面变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '18', '检查项分类': '按键(SW)功能的测试-按键短压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '19', '检查项分类': '按键(SW)功能的测试-按键长压出力是否正确', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '20', '检查项分类': '按键(SW)功能的测试-短按上下左右键显示是否正确，长按上下左右键是否正确', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '21', '检查项分类': '按键(SW)功能的测试-在需要循环的界面能否进行循环显示', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '22', '检查项分类': '按键(SW)功能的测试-先长按上键不松手再长按下键是否存在异常显示', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '23', '检查项分类': '按键(SW)功能的测试-长按BACK按键是否正常返回', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '24', '检查项分类': '按键(SW)功能的测试-短按BACK按键是否及时响应', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '25', '检查项分类': '按键(SW)功能的测试-长时间压下任意按键是否存在卡顿或者响应延迟', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '26', '检查项分类': '按键(SW)功能的测试-在仅有一个选项的情况下按下任意按键看响应是否正确', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '27', '检查项分类': '按键(SW)功能的测试-按键是否可以选中灰显的项目', '是否要求': '〇', '是否相关': '否', '原因': '按键功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '28', '检查项分类': '信号的测试-机能有无信号是否能让功能正常显示', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '29', '检查项分类': '信号的测试-诊断清除能否清除机能有无的装配', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '30', '检查项分类': '信号的测试-按下按键是否存在出力，出力的时机是否正确', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '31', '检查项分类': '信号的测试-在发送机能有的信号后，机能显示是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '32', '检查项分类': '信号的测试-选项框停留在某一个机能时，发送该机能无的信号，选项框切换动作是否满足式样需求', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '33', '检查项分类': '信号的测试-将选项框放在当前页面的最后一个选项，发送最后一个选项机能有，查看切换动作是否满足式样', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '34', '检查项分类': '信号的测试-发送对应信号后对应项目机能是否符合预期现象（存在某些需要下一阶层先有机能才会显示的情况）', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '35', '检查项分类': '信号的测试-发送对应信号能否导致其他选项灰显或者跳变', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '36', '检查项分类': '信号的测试-在发送信号后确认关联现象是否改变，如时间，日历或者调光', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '37', '检查项分类': '信号的测试-对应信号途绝的显示', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '38', '检查项分类': '信号的测试-信号途绝复归是否正确', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '39', '检查项分类': '信号的测试-发送信号改变显示值是否存在右边的选项标志跳变，或者导致背景的高亮', '是否要求': '〇', '是否相关': '否', '原因': '信号逻辑未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '40', '检查项分类': 'SETTING的功能测试-出力时机的确认', '是否要求': '〇', '是否相关': '否', '原因': '出力时机未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '41', '检查项分类': 'SETTING的功能测试-每个页面最大项目数', '是否要求': '〇', '是否相关': '否', '原因': '页面项目数未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '42', '检查项分类': 'SETTING的功能测试-每个切替TYPE的迁移动作正确性', '是否要求': '〇', '是否相关': '否', '原因': '切替动作未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '43', '检查项分类': 'SETTING的功能测试-切替F的计时打断正确性', '是否要求': '〇', '是否相关': '否', '原因': '计时打断未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '44', '检查项分类': 'SETTING的功能测试-走形过程中灰显的迟滞曲线的显示', '是否要求': '〇', '是否相关': '否', '原因': '迟滞曲线未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '45', '检查项分类': 'SETTING的功能测试-某些机能在灰显过程中辅助文言的正确性，以及选项显示的正确性', '是否要求': '〇', '是否相关': '否', '原因': '灰显文言未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '46', '检查项分类': 'SETTING的功能测试-语言单位的设定的默认显示顺序', '是否要求': '〇', '是否相关': '否', '原因': '语言单位未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '47', '检查项分类': 'SETTING的功能测试-初始化上电后表盘默认的选项设定', '是否要求': '〇', '是否相关': '否', '原因': '初始化设定未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '48', '检查项分类': 'SETTING的功能测试-某些机能选项在设定时存在互斥性', '是否要求': '〇', '是否相关': '否', '原因': '无互斥性变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '49', '检查项分类': 'SETTING的功能测试-特殊机能存在设定数值上限的前提下，看到达上限后是否可以切换', '是否要求': '〇', '是否相关': '否', '原因': '无设定上限变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '50', '检查项分类': 'SETTING的功能测试-在车辆设定下的设置是否可以全部进行初始化', '是否要求': '〇', '是否相关': '否', '原因': '初始化功能未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '51', '检查项分类': '画面交互的测试观点-SETTING界面与WARNING，大中断，以及特殊画面的打断', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '52', '检查项分类': '画面交互的测试观点-在SETTING显示过程中长按BACK返回现象', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '53', '检查项分类': '画面交互的测试观点-走行迟滞曲线是否会导致选项界面灰显', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '54', '检查项分类': '画面交互的测试观点-灰显状态下是否存在出力', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '55', '检查项分类': '画面交互的测试观点-单双零眼的切换是否流畅', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '56', '检查项分类': '画面交互的测试观点-在设定界面进行走形置灰并将上层功能取消查看切替现象', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '57', '检查项分类': '画面交互的测试观点-单位语言切换显示是否正确，是否能够成功同步', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '58', '检查项分类': '画面交互的测试观点-电源状态切换后是否保持显示，例如IGN切换，BAT切换', '是否要求': '〇', '是否相关': '否', '原因': '界面交互未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '59', '检查项分类': '其他异常测试-ST点火画面测试', '是否要求': '〇', '是否相关': '否', '原因': '异常测试未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '60', '检查项分类': '其他异常测试-BAT/IGN 重启后，显示是否正确', '是否要求': '〇', '是否相关': '否', '原因': '异常测试未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '61', '检查项分类': '其他异常测试-高负载测试', '是否要求': '〇', '是否相关': '否', '原因': '异常测试未变更', 'testcases': [], 'reason': ''}, {'类型': '功能测试', 'NO.': '62', '检查项分类': 'EEPROM存储-保存与否时验证IG/+B OFF的等待时间', '是否要求': '〇', '是否相关': '否', '原因': '存储逻辑未变更', 'testcases': [], 'reason': ''}]}
]