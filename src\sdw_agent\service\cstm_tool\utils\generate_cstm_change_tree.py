import os
import pathlib
import json
import re
import traceback

from loguru import logger

from sdw_agent.service.cstm_tool.utils.req_resolver import ReqResolver
from sdw_agent.service.cstm_tool.utils.resolve_cstm import init_cstmlist, process_sheet, look_up_menucnttinfo, \
    look_up_othercnttinfo, trans_obj_to_json
from sdw_agent.service.cstm_tool.utils.scl_utils import SCLUtils


logger.bind(name="CSTM_TOOL")

class CstmUtils:
    '''
    cstm 工具类
    '''

    @staticmethod
    def extract_row_index(cell_coordinate:str):
        '''
        从单元格坐标中抽取行索引
        :param cell_coordinate:
        :return:
        '''
        # 使用正则表达式提取数字部分
        row_index = re.search(r'\d+', cell_coordinate)
        if row_index:
            row_index = int(row_index.group())
        else:
            row_index = None
        return  row_index

    @staticmethod
    def trans_col_name(col_name):
        """
        将字母表示的列名转换成列索引
        :param col_name:
        :return:
        """
        row_idx = CstmUtils.extract_row_index(col_name)
        if row_idx:
            # 将col_name 中的数字替换成''
            col_name = re.sub(r'\d+', '', col_name)
        
        col_idx = 0
        for char in col_name:
            col_idx = col_idx * 26 + (ord(char.upper()) - ord('A') + 1)

        return row_idx, col_idx


def req_2_json(file_path: str, config):

    # 从文件路径中提取文件名（不包含扩展名）
    file_name = os.path.splitext(os.path.basename(file_path))[0].strip()
    # 在output_path中创建以file_name命名的文件夹
    output_path = os.path.join(pathlib.Path(config.get("workflow_config",{}).get("output",{}).get("path")))
    folder_path = os.path.join(output_path, config.get("workflow_config",{}).get("output",{}).get("req_resolve_dir"), file_name)
    os.makedirs(folder_path, exist_ok=True)
    # 生成JSON文件路径
    json_file_path = os.path.join(folder_path, f"{file_name}.json")
    if os.path.exists(json_file_path):
        cstm_json = None
        with open(json_file_path, 'r', encoding='utf-8') as f:
            cstm_json = json.load(f)

        logger.info(f"本地已经存在解析文件：{json_file_path}")
        return json_file_path, cstm_json

    logger.info(f"待解析CSTMLST原始式样书：{file_name}")

    # 创建ReqResolver实例来处理式样书解析

    cntt_list = []

    # # 由于是解析固定格式的原始式样书 指定开始行，列信息
    # req_resolver = ReqResolver(file_path, config)
    # req_file_meta = req_resolver.init_cstmlist()
    # index_dict = req_resolver.process_sheet(req_file_meta.resolve_sheet)
    # req_resolver.look_up_menucnttinfo(req_file_meta, cntt_list, index_dict)
    # req_resolver.look_up_othercnttinfo(req_file_meta, cntt_list, index_dict)

    # 由于是解析固定格式的原始式样书 指定开始行，列信息
    req_file_meta = init_cstmlist(file_path, config.get("req_config",{}).get('target_sheet', 'JP'), start_row=8, start_col=2, max_level=7)
    index_dict = process_sheet(req_file_meta.resolve_sheet)
    look_up_menucnttinfo(req_file_meta, cntt_list, index_dict)
    look_up_othercnttinfo(req_file_meta, cntt_list, index_dict)


    if not cntt_list:
        logger.error(f"CSTMLST原始式样书解析失败：{file_name}")

    # cstm_json = req_resolver.trans_obj_to_json(cntt_list)
    cstm_json = trans_obj_to_json(cntt_list)

    # 将json存入本地
    # 将JSON数据写入文件
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(cstm_json, f, indent=4, ensure_ascii=False)

    logger.info(f"CSTMLST原始式样书JSON文件已生成: {json_file_path}")

    return json_file_path, cstm_json


def generate_cstm_json_tree(validated_input, config):

    # 结构参数
    change_summary = validated_input.change_summary
    scl_path = validated_input.scl_path
    before_cstm = validated_input.before_cstm
    after_cstm = validated_input.after_cstm

    # 从scl解析信息
    scl_utils = SCLUtils(scl_path, config)
    scl_info = scl_utils.extract_info_from_scl(change_summary)

    # 验证用户上传的原始式样书版本是否与scl中的一致
    is_valid, before_cstm_name_in_scl, after_cstm_name_in_scl = scl_utils.validate_cstm_spec(scl_info, before_cstm,
                                                                                             after_cstm)
    if not is_valid:
        raise ValueError(
            # f"{gettext('原始式样书版本与SCL中信息不一致，请选择变更前文件名：')}{before_cstm_name_in_scl}','{gettext('变更后文件名：')}{after_cstm_name_in_scl}"
            f"原始式样书版本与SCL中信息不一致，请选择变更前文件名：{before_cstm_name_in_scl}, 变更后文件名：{after_cstm_name_in_scl}"
        )

    # 解析式样书成json
    before_cstm_json_path, before_cstm_json = req_2_json(before_cstm, config)

    # 为after_cstm创建另一个实例
    after_cstm_json_path, after_cstm_json = req_2_json(after_cstm, config)

    # 根据SCL变更内容从json中解析出发生变更的目录树
    pair_tree = extract_change_tree(before_cstm_json, after_cstm_json, scl_info)

    return before_cstm_json_path, after_cstm_json_path, pair_tree

def validate_cstm_spec(scl_info, before_cstm, after_cstm):
    '''
    验证用户上传的原始式样书版本是否与scl中的一致
    :param scl_info: SCL信息字典，包含req_name字段
    :param before_cstm: 变更前式样书文件路径
    :param after_cstm: 变更后式样书文件路径
    :return: tuple(验证结果: bool, scl中变更前文件名: str|None, scl中变更后文件名: str|None)
    '''
    try:
        # 输入验证
        if not all([before_cstm, after_cstm]):
            logger.warning("before_cstm或after_cstm参数为空")
            return False, None, None

        if not isinstance(before_cstm, str) or not isinstance(after_cstm, str):
            logger.error("before_cstm和after_cstm必须为字符串类型")
            return False, None, None

        # 从before_cstm 和 after_cstm中提取出文件名
        before_cstm_name = os.path.basename(before_cstm.strip())
        after_cstm_name = os.path.basename(after_cstm.strip())

        # 验证提取的文件名不为空
        if not before_cstm_name or not after_cstm_name:
            logger.error("无法从文件路径中提取有效的文件名")
            return False, None, None

        # 验证scl_info并提取文件名
        if not scl_info or not isinstance(scl_info, dict):
            logger.warning("scl_info为空或格式不正确")
            return False, None, None

        req_name = scl_info.get('req_name')
        if not req_name or not isinstance(req_name, list) or len(req_name) == 0:
            logger.warning("scl_info中缺少有效的req_name字段")
            return False, None, None

        # 验证req_name的第一个元素结构
        first_req = req_name[0]
        if not isinstance(first_req, (list, tuple)) or len(first_req) < 2:
            logger.error("scl_info中req_name格式不正确，期望包含至少2个元素的列表")
            return False, None, None

        # 安全提取SCL中的文件名
        before_cstm_name_in_scl = str(first_req[0]).strip() if first_req[0] else ""
        after_cstm_name_in_scl = str(first_req[1]).strip() if first_req[1] else ""


        # 验证SCL中的文件名不为空
        if not before_cstm_name_in_scl or not after_cstm_name_in_scl:
            logger.warning("SCL中的文件名为空")
            return False, before_cstm_name_in_scl or None, after_cstm_name_in_scl or None

        # 比较文件名
        is_match = (before_cstm_name == before_cstm_name_in_scl and
                   after_cstm_name == after_cstm_name_in_scl)

        if is_match:
            logger.info(f"文件名验证成功: {before_cstm_name} -> {after_cstm_name}")
        else:
            logger.warning(f"文件名不匹配 - 上传: {before_cstm_name} -> {after_cstm_name}, "
                           f"SCL: {before_cstm_name_in_scl} -> {after_cstm_name_in_scl}")

        return is_match, before_cstm_name_in_scl, after_cstm_name_in_scl

    except Exception as e:
            logger.error(f"验证CSTM式样书时发生异常: {str(e)}")
            return False, None, None


def extract_change_tree(before_cstm_json, after_cstm_json, scl_info):
    '''
    根据SCL变更内容从json中解析出发生变更的目录树
    :param cstm_json:
    :param scl_info:
    :return:
    '''
    before_cntt = []
    after_cntt = []
    change_rows = len(scl_info.get('req_name')) #scl 中发生变更的行数
    for i in range(0, change_rows):
        # 定位发生变更前/后的画面
        cntt_bf_af = find_change_cntt(i, scl_info, before_cstm_json, after_cstm_json)
        if cntt_bf_af:
            before_cntt.append(cntt_bf_af[0])
            after_cntt.append(cntt_bf_af[1])

    # 抽取出所有变更前后的画面信息
    unique_pairs = merge_changed_cntt(before_cntt, after_cntt)
    # 分析变更前后的画面信息
    merged_pairs,move_opt_names = analyze_pairs(unique_pairs)
    # 根据画面信息得到目录树
    pair_tree = generate_cntt_tree(merged_pairs,move_opt_names, before_cstm_json, after_cstm_json)
    # 去除pair_tree中不相关的选项信息
    for i in range(0, len(pair_tree)):
        if move_opt_names:
            processed_bf, processed_af = remove_unchanged_opt(pair_tree[i][0], pair_tree[i][1], move_opt_names[i])
        else:
            processed_bf, processed_af = remove_unchanged_opt(pair_tree[i][0], pair_tree[i][1], [])
        pair_tree[i] = tuple([processed_bf, processed_af])

    return pair_tree

@logger.catch()
def find_change_cntt(idx, scl_info, before_cstm_json, after_cstm_json):
    '''
    根据发生变更cell的coordinate和阶层level 从cstm_json中定位到对应画面
    :param level:
    :param cell_coordinate:
    :param cntt_level:
    :param cstm_json:
    :return: 发生变更的画面信息
    '''
    before_coordinate = scl_info.get('cell')[idx][0]
    after_coordinate = scl_info.get('cell')[idx][1]

    # 将cell_coordinate转换成列索引
    bf_row_index, bf_col_index = CstmUtils.trans_col_name(before_coordinate)
    af_row_index, af_col_index = CstmUtils.trans_col_name(after_coordinate)
    change_type = scl_info.get('change_type')[idx]

    cntt_bf_af = []
    if change_type=='行追加':
        # before
        bf_cntts = []
        # 通过画面的col_range和row_range去判断
        for i in before_cstm_json:
            if i.get('opt_names') and len(i.get('opt_names')) > 0:
                # for j in i.get('opt_names'):
                if i.get('row_range')[0] <= af_row_index <= i.get('row_range')[1]:
                    bf_cntts.append(i)

        if not bf_cntts:
            bf_cntts.append(before_cstm_json[0])
        # 先计算after
        af_cntts = []
        # 通过画面选项的start_row和end_row属性去判断
        for i in after_cstm_json:
            if i.get('opt_names') and len(i.get('opt_names'))>0:
                # for j in i.get('opt_names'):
                if i.get('row_range')[0] <= af_row_index <= i.get('row_range')[1]:
                    af_cntts.append(i)
        af_cntt = find_max_level_cntt(af_cntts)

        # 根据af_cntt从变更前json中逆向去寻找存在的且最接近的画面
        # 先找到两者的公共父路径部分
        common_cntts = []
        for bf, af in zip(bf_cntts, af_cntts):
            if bf and af and bf.get('cntt_name')==af.get('cntt_name'):
                common_cntts.append(bf)
            else:
                break

        common_num = len(common_cntts)
        # 如果是画面新增
        if len(af_cntts)>common_num and common_num:
            for i in range(common_num, len(af_cntts)):
                if af_cntts[i].get('cntt_name') in common_cntts[-1].get('next_cntt'):
                    # 对应情况 画面新增
                    # 变更前bf_cntts Menu/表示设定/メータータイプ設定/1dial track-View
                    # 变更后af_cntts Menu/表示設定/メータータイプ設定/ECO/燃费/**
                    # 上述例子中燃费/**是新增画面， 在变更前式样书json 里面其实包含ECO，bf_cntts中没有包含是因为SCL中的坐标定位到了1dial track-View选项，是在变更前原始式样书这个位置开始添加
                    filter_cntt = [j for j in before_cstm_json if
                                       j.get('cntt_name')==af_cntts[i].get('cntt_name') and \
                                       j.get('cntt_pre')==common_cntts[-1].get('cntt_name') and \
                                       j.get('row_range')[0]>=common_cntts[-1].get('row_range')[0] and \
                                       j.get('row_range')[1]<=common_cntts[-1].get('row_range')[1]
                                   ]
                    if filter_cntt:
                        common_cntts.append(filter_cntt[0])
                    else:
                        break
        if common_cntts:
            cntt_bf_af.append(common_cntts[-1])
        else:
            cntt_bf_af.append(None)

        if af_cntt:
            cntt_bf_af.append(af_cntt)
        else:
            cntt_bf_af.append(None)
    else:
        # before
        bf_cntts = []
        # 通过画面的col_range和row_range去判断
        for i in before_cstm_json:
            if i.get('col_range') and i.get('row_range'):
                # if i.get('col_range')[0] <= bf_col_index <= i.get('col_range')[1] and \
                #         i.get('row_range')[0] <= bf_row_index <= i.get('row_range')[1]:
                if i.get('row_range')[0] <= bf_row_index <= i.get('row_range')[1]:
                    bf_cntts.append(i)
        bf_cntt = find_max_level_cntt(bf_cntts)
        if bf_cntt:
            cntt_bf_af.append(bf_cntt)
        else:
            cntt_bf_af.append(None)
        # after
        af_cntts = []
        # 通过画面的col_range和row_range去判断
        for i in after_cstm_json:
            if i.get('col_range') and i.get('row_range'):
                # if i.get('col_range')[0] <= af_col_index <= i.get('col_range')[1] and \
                #         i.get('row_range')[0] <= af_row_index <= i.get('row_range')[1]:
                if i.get('row_range')[0] <= af_row_index <= i.get('row_range')[1]:
                    af_cntts.append(i)
        af_cntt = find_max_level_cntt(af_cntts)
        if af_cntt:
            cntt_bf_af.append(af_cntt)
        else:
            cntt_bf_af.append(None)

    return cntt_bf_af
    
def find_max_level_cntt(cntts):
    # 从cntt 中找到level最大的画面
    result = None
    if cntts:
        if len(cntts) > 0:
            max_level_cntt = max(cntts, key=lambda x: x.get('cstm_level'))
            result = max_level_cntt
        else:
            result = cntts[0]
    return result

def find_min_level_cnnt(cntts):
    '''
    从cntt 中找到level最小的画面
    :param cntts:
    :return:
    '''
    result = None
    if cntts:
        if len(cntts) > 0:
            min_level_cntt = min(cntts, key=lambda x: x.get('cstm_level'))
            result = min_level_cntt
        else:
            result = cntts[0]
    return result

def merge_changed_cntt(before_cntt, after_cntt):
    """
    将两个列表中相同索引的元素配对，根据uuid属性去重，返回不重复的pair
    返回值结构：[(before_cnnt_1, after_cnnt_1),...]
    """
    # 根据uuid属性去重
    seen_uuids = set()
    unique_pairs = []
    
    for before_item, after_item in zip(before_cntt, after_cntt):
        # 获取before_item的uuid
        before_uuid = before_item.get('uuid') if before_item else None
        # 获取after_item的uuid
        after_uuid = after_item.get('uuid') if after_item else None
        
        # 创建uuid组合作为唯一标识
        uuid_pair = (before_uuid, after_uuid)
        
        # 如果这个uuid组合还没有出现过，则添加到结果中
        if uuid_pair not in seen_uuids:
            seen_uuids.add(uuid_pair)
            if not (before_item is None and after_item is None):
                # 如果before_item, after_item都为None 则不添加
                unique_pairs.append((before_item, after_item))
    
    return unique_pairs

def analyze_pairs(unique_pairs):
    '''
    分析变更前后的画面信息，识别并聚合move操作
    :param unique_pairs:
    :return:
    '''
    bf_af_pairs = [list(item) for item in unique_pairs]
    len_pairs = len(bf_af_pairs)
    for idx,pair in enumerate(bf_af_pairs):
        bf = pair[0]
        af = pair[1]
        if af and bf:
            bf_opts = set(re.sub(r'★', '', opt.get('opt_name')).strip() for opt in bf.get('opt_names'))
            af_opts = set(re.sub(r'★', '', opt.get('opt_name')).strip() for opt in af.get('opt_names'))

            diff = {'add': list(set(af_opts) - set(bf_opts)), 'delete': list(set(bf_opts) - set(af_opts))}
            bf_af_pairs[idx].append(diff)
    
    # 对比分析 找出move情况
    move_pairs = []
    move_opt_names = []
    move_idx = []
    for i in range(0, len_pairs):
        for j in range(0, len_pairs):
            if bf_af_pairs[i] and bf_af_pairs[i][-1] and bf_af_pairs[j] and bf_af_pairs[j][-1]:
                try:
                    if i != j  and bf_af_pairs[i][-1].get('add') and all(item in bf_af_pairs[i][-1].get('add') for item in bf_af_pairs[j][-1].get('delete')):
                        # 放入 删除操作变更前的画面信息/ 新增操作变更后的画面信息
                        move_pairs.append([bf_af_pairs[j][0], bf_af_pairs[1][1]])
                        # 记录move的选项信息
                        cur_move = []
                        for add_opt in bf_af_pairs[i][-1].get('add'):
                            if add_opt in bf_af_pairs[j][-1].get('delete'):
                                cur_move.append(add_opt)
                        move_opt_names.append(cur_move)
                        # 记录move的idx 信息
                        move_idx.append([j, i])
                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"analyze_pairs发生异常：{str(e)}", exc_info=True)

    res = unique_pairs
    # 移除被合并的pair
    for idx,k in enumerate(move_idx):
        res[k[0]] = None
        res[k[1]] = None
        res = [item for item in res if item]
        # 添加新的合并后的pair
        # res.append(tuple(move_pairs[idx]))
        res.insert(idx, tuple(move_pairs[idx]))

    return res, move_opt_names

def generate_cntt_tree(merged_pairs,move_opt_names,before_cstm_json, after_cstm_json):
    '''
    根据变更的画面信息计算目录树
    :param before_cntt:
    :param after_cntt:
    :return:
    '''
    move_len = len(move_opt_names)
    tree_pair = []
    for idx, pair in enumerate(merged_pairs):
        if move_opt_names and idx<move_len:
            # 处理move 情况
            # before
            before_tree = find_cntt_tree(pair[0],before_cstm_json)
            trans_before_tree = []
            if before_tree is not None:
                # 最后的画面选项中只包含发生move的选项信息
                bf_added_opts = [i for i in before_tree[-1].get('opt_names') if i.get('opt_name') in move_opt_names[idx]]
                # 把画面转换成进入画面的子项信息
                trans_before_tree = trans_cntt_2_enter_opt(before_tree, before_cstm_json)
                trans_before_tree.extend(bf_added_opts)
            # after
            after_tree = find_cntt_tree(pair[1], after_cstm_json)
            trans_after_tree = []
            if after_tree is not None:
                af_added_opts = [i for i in after_tree[-1].get('opt_names') if i.get('opt_name') in move_opt_names[idx]]
                # 把画面转换成进入画面的子项信息
                trans_after_tree = trans_cntt_2_enter_opt(after_tree, after_cstm_json)
                trans_after_tree.extend(af_added_opts)

            tree_pair.append(tuple([trans_before_tree, trans_after_tree]))
        else:
            before_tree = find_cntt_tree(pair[0], before_cstm_json)
            trans_before_tree = []
            if before_tree is not None:
                bf_added_opts = [i for i in before_tree[-1].get('opt_names')]
                # 把画面转换成进入画面的子项信息
                trans_before_tree = trans_cntt_2_enter_opt(before_tree, before_cstm_json)
                trans_before_tree.extend(bf_added_opts)

            after_tree = find_cntt_tree(pair[1], after_cstm_json)
            is_delete_cntt = False
            if before_tree is not None and after_tree is None:
                # 如果变更前不为None，变更后为None， 表示存在画面删除情况
                # 可以自上向下遍历before_tree，将after_cstm_json中存在的画面放入after_tree中
                is_delete_cntt = True
                common_cntt = []
                for item in before_tree:
                    filter_cntt = [k for k in after_cstm_json
                                    if k.get('cntt_name')==item.get('cntt_name') \
                                       and k.get('cntt_pre')==item.get('cntt_pre')]
                    if len(filter_cntt)>0:
                        common_cntt.append(filter_cntt[0])
                    else:
                        break
                after_tree = common_cntt

            trans_after_tree = []
            if after_tree is not None:
                af_added_opts = []
                if is_delete_cntt:
                    # 对于画面删除情况
                    if after_tree and len(after_tree) < len(before_tree):
                        change_opt_name = before_tree[len(after_tree)].get('opt_pre')
                        af_added_opts = [i for i in after_tree[-1].get('opt_names') if i.get('opt_name')==change_opt_name]
                else:
                    af_added_opts = [i for i in after_tree[-1].get('opt_names')]

                # 把画面转换成进入画面的子项信息
                trans_after_tree = trans_cntt_2_enter_opt(after_tree, after_cstm_json)
                trans_after_tree.extend(af_added_opts)

            tree_pair.append(tuple([trans_before_tree, trans_after_tree]))

    return tree_pair

def find_cntt_tree(cntt_info, cstm_json):
    '''
    根据某个子画面寻找画面树
    :param cntt_info:
    :return:
    '''
    res = None
    if cntt_info is not None:
        cur_level = cntt_info.get('cstm_level')
        cur_cntt = cntt_info
        res = []
        # 向上追溯画面信息直到menu
        while cur_level>=0:
            res.insert(0,cur_cntt)
            if cur_level==0:
                break
            pre_cntt_name = cur_cntt.get('cntt_pre')
            pre_cntt = [item for item in cstm_json if item.get('cntt_name')==pre_cntt_name][0]
            cur_level = pre_cntt.get('cstm_level')
            cur_cntt = pre_cntt

    return res

def trans_cntt_2_enter_opt(cntt_tree, cstm_json):
    '''
    获取上一阶层中进入指定画面的选项信息
    :param cntt_tree:
    :param cstm_json:
    :return:
    '''
    trans_tree = []
    for i in cntt_tree:
        cntt_name = re.sub(r'★', '',i.get('cntt_name')).strip()
        # 画面的切入信息
        if cntt_name != "MENU":
            cntt_pre = [cntt for cntt in cstm_json if re.sub(r'★', '', cntt.get('cntt_name')).strip()==re.sub(r'★', '', i.get('cntt_pre')).strip()]
            if cntt_pre:
                cntt_pre = cntt_pre[0]
                opt_info = [opt for opt in cntt_pre.get('opt_names') if re.sub(r'★', '', opt.get('opt_name')).strip()==cntt_name]
                if opt_info:
                    trans_tree.append(opt_info[0])
    return trans_tree

def remove_unchanged_opt(before_tree, after_tree, move_opt_names):
    """
    去除before_tree和after_tree中最大cstm_level下重复的dict。
    :param before_tree: 画面树的列表，每个元素为dict，包含'cstm_level'键
    :param after_tree: 画面树的列表，每个元素为dict，包含'cstm_level'键
    :return: (filtered_before_tree, filtered_after_tree) 过滤后的before_tree和after_tree
    """
    # 比较时忽略['opt_index', 'start_row', 'end_row', 'coordinate']这四个属性
    def dict_without_keys(d, keys):
        return {k: v for k, v in d.items() if k not in keys}

    if not before_tree or not after_tree:
        return before_tree, after_tree

    # 找到最大cstm_level
    max_bf_level = max(item.get('cstm_level', -1) for item in before_tree)
    max_af_level = max(item.get('cstm_level', -1) for item in after_tree)

    # 找到最大level的元素
    bf_max_level_items = [item for item in before_tree if item.get('cstm_level', -1) == max_bf_level]
    af_max_level_items = [item for item in after_tree if item.get('cstm_level', -1) == max_bf_level]
    # 去除before_tree中与after_tree重复的dict（以所有键值对为准）
    bf_to_remove = []
    # 比较时排除这些字段
    exclude_fields = ['opt_index', 'start_row', 'end_row', 'coordinate', 'name_has_changed', 'spec_document']
    for bf_item in bf_max_level_items:
        for af_item in af_max_level_items:
            if dict_without_keys(bf_item, exclude_fields) == dict_without_keys(af_item, exclude_fields):
                bf_to_remove.append(bf_item)
                break
    filtered_before_tree = [item for item in before_tree if item not in bf_to_remove]

    # 对after_tree做同样处理
    af_max_level_items = [item for item in after_tree if item.get('cstm_level', -1) == max_af_level]
    bf_max_level_items = [item for item in before_tree if item.get('cstm_level', -1) == max_af_level]
    af_to_remove = []
    for af_item in af_max_level_items:
        for bf_item in bf_max_level_items:
            if dict_without_keys(bf_item, exclude_fields) == dict_without_keys(af_item, exclude_fields):
                af_to_remove.append(af_item)
                break
    filtered_after_tree = [item for item in after_tree if item not in af_to_remove]

    # max_bf_level < max_af_level
    if max_bf_level < max_af_level:
        # 得到filtered_after_tree中cstm_level为(max_bf_level+1)的元素中第一个元素的belong_cntt属性的值命名为bf_cntt
        af_next_level_items = [item for item in filtered_after_tree if item.get('cstm_level', -1) == (max_bf_level + 1)]
        if af_next_level_items:
            bf_cntt = af_next_level_items[0].get('belong_cntt')
            # 删除filtered_before_tree中cstm_level属性为max_bf_level且opt_name属性的值不等于bf_cntt的元素
            filtered_before_tree = [
                item for item in filtered_before_tree
                if not (
                    item.get('cstm_level', -1) == max_bf_level and
                    item.get('opt_name') != bf_cntt and
                    item.get('opt_name') not in move_opt_names
                )
            ]

    if max_bf_level > max_af_level:
        # 得到filtered_before_tree中cstm_level为(max_af_level+1)的元素中第一个元素的belong_cntt属性的值命名为af_cntt
        bf_next_level_items = [item for item in filtered_before_tree if item.get('cstm_level', -1) == (max_af_level + 1)]
        if bf_next_level_items:
            af_cntt = bf_next_level_items[0].get('belong_cntt')
            # 删除filtered_after_tree中cstm_level属性为max_af_level且opt_name属性的值不等于af_cntt的元素
            filtered_after_tree = [
                item for item in filtered_after_tree
                if not (
                    item.get('cstm_level', -1) == max_af_level and
                    item.get('opt_name') != af_cntt and
                    item.get('opt_name') not in move_opt_names
                )
            ]

    return filtered_before_tree, filtered_after_tree

if __name__ == "__main__":

    # 变更
    # change_summary = "警報音量の切替Type BⅠ→AⅠに変更 (QA対応)"
    # scl_path = r"C:\Users\<USER>\Desktop\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\SVN_NEW\02DESIGN\Sample_DATA_20250408\01_SoftwareREQ\原始式样书\MET19PFV3-5410\变更后\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # after_cstm = r"D:\SVN_NEW\02DESIGN\Sample_DATA_20250408\01_SoftwareREQ\原始式样书\MET19PFV3-12703\变更后\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # base_cstm = r"C:\Users\<USER>\Desktop\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)

    # # 选项位置变更
    # change_summary = "第0階層⇒第一階層へ移動\n1500Wコンセント,2400Wコンセント,7200Wコンセント"
    # scl_path = r"C:\Users\<USER>\Desktop\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # after_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # base_cstm = r"C:\Users\<USER>\Desktop\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)

    # # 画面追加
    # change_summary = "・表示設定/メータタイプ設定/ECOに「燃費」を追加"
    # scl_path = r"C:\Users\<USER>\Desktop\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-01-A-C0.xlsx"
    # after_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # base_cstm = r"C:\Users\<USER>\Desktop\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)

    # # 选项追加
    # change_summary = "「表示シーン拡張」を追加"
    # scl_path = r"C:\Users\<USER>\Desktop\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # after_cstm = r"C:\Users\<USER>\Desktop\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-04-A-C0.xlsx"
    # base_cstm = r"C:\Users\<USER>\Desktop\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)


    # # 画面削除
    change_summary = "警報音量の階層修正"
    scl_path = r"C:\tdd_input\SCL\SCL_TEST.xlsx"
    before_cstm = r"C:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    after_cstm = r"C:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-04-A-C0.xlsx"
    # base_cstm = r"C:\Users\<USER>\Desktop\tdd_input\CSTM Tool配置文件-演示.xlsm"
    tree = generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)





