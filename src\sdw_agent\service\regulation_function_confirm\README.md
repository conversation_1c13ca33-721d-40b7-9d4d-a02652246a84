# 需求分析工作流

## V字对应
- 1.3 要件分析
- 4 法规确认模块

## 概述

该模块实现法规确认的核心流程，集成LLM能力进行自动化评估。

## 主要功能

### 核心功能：实现法规功能变更分类
- **法规预处理**：提取法规概要文本并生成向量表示
- **变更分类**：通过LLM评估变更是否涉及法规功能自身/间接影响/优先级调整等
- **结果输出**：将分类结果写入Excel文件对应列

### 分析流程
1. **预处理法规库**：从知识库文件提取「規制概要」并生成嵌入向量
2. **读取变更需求**：从Excel文件读取「要件の内容」和「ソフトの変更内容」
3. **执行分类评估**：调用LLM将法规变更进行分类
4. **结果写入**：将分类结果写入Excel的「法規評価の分類」和「法規機能の変更」列

## 使用方法

### 基本使用

```python
from sdw_agent.service.regulation_function_confirm import RegulationFunctionWorkflow

# 创建工作流实例
workflow = RegulationFunctionWorkflow()

# 处理要件一览表
result = workflow.execute(
            origin_file_path=request.input_file_path,
            kb_file_path=request.kb_file_path
        )

```

## 输入参数说明

| 参数名 | 类型 | 必需 | 描述        |
|--------|------|------|-----------|
| `origin_file_path` | str | 是 | 变更一览表文件名  |
| `kb_file_path` | str | 否 | 法规知识库文件路径 |


## 配置说明

工作流配置文件 `config.yaml` 包含以下配置项：

```yaml
# 基本配置
name: "法规变更评估"
description: "分析软件变更内容与法规功能的关联性"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入文件格式
  excel_extensions: [".xlsx", ".xls", ".xlsm"] # 支持的文件格式
llm: 
  system_prompt: "你是一个专业的汽车电子测试工程师，请以专业严谨的态度回答我的问题" 
  temperature: 0.7 
  ```

## 输入文件格式

测试用例应为Excel格式，至少包含以下列：
- `要件の内容`与`ソフトの変更内容`: 软件变更的具体描述

## 输入文件格式

在原文件基础上新增以下列：
- **法規評価の分類**：变更分类结果（①法規機能の全評価/②変更点と法規機能の全評価等）
- **法規機能の変更**：变更影响
- 
## 错误处理

工作流包含完善的错误处理机制：
- 输入参数验证
- 文件操作异常处理
- LLM调用失败处理
- 全局异常捕获

## 注意事项

1. 测试用例文件必须是有效的Excel格式
2. 所有文件操作都有安全检查和异常处理
