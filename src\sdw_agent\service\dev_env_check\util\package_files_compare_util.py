"""
软件包文件对比工具类

提供远程服务器软件包文件下载和对比功能
"""
import difflib
import filecmp
import re
import subprocess
import tempfile
import shutil
from datetime import datetime
from typing import List, Dict, Optional
from openpyxl.cell.text import InlineFont
from loguru import logger
import time
import os
import traceback
from bs4 import BeautifulSoup
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.cell.rich_text import TextBlock, CellRichText

from sdw_agent.config.env import ENV
from sdw_agent.service.dev_env_check.config import bin_compare_excel_sheet, bin_compare_styles, \
    spec_compare_excel_sheet_list
from sdw_agent.service.dev_env_check.models import PackageFileInfo, PackageFileCompareInfo
from sdw_agent.service.dev_env_check.util.config_manager import config_manager_dev_env_check
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from src.sdw_agent.util.ssh_util import SSHUtil, SSHConfig, CommandResult


class FTPDownloadUtil:
    """FTP下载工具类"""

    def __init__(self, ssh_config: SSHConfig, ftp_host: str, ftp_user: str,
                 ftp_password: str, ftp_port: int = 21):
        """
        初始化FTP下载工具

        Args:
            ssh_config: SSH连接配置
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_port: FTP端口（默认21）
        """
        self.ssh_config = ssh_config
        self.ftp_host = ftp_host
        self.ftp_user = ftp_user
        self.ftp_password = ftp_password
        self.ftp_port = ftp_port

    def download_file(self, ftp_remote_path: str, linux_local_path: str,
                      method: str = "wget", create_dirs: bool = True) -> CommandResult:
        """
        从FTP服务器下载单个文件到远程Linux服务器

        Args:
            ftp_remote_path: FTP服务器上的文件路径
            linux_local_path: Linux服务器上的本地路径
            method: 下载方法 ("wget", "curl", "lftp")
            create_dirs: 是否自动创建目录

        Returns:
            CommandResult: 下载命令执行结果
        """
        logger.info(f"从FTP下载文件: {self.ftp_host}{ftp_remote_path} -> {linux_local_path}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                # 如果需要，先创建目录
                if create_dirs:
                    dir_path = os.path.dirname(linux_local_path)
                    if dir_path:
                        mkdir_cmd = f"mkdir -p {dir_path}"
                        mkdir_result = ssh.execute_command(mkdir_cmd)
                        if not mkdir_result.success:
                            logger.warning(f"创建目录失败: {mkdir_result.stderr}")

                # 根据方法选择下载命令
                if method == "wget":
                    return ssh.download_from_ftp(
                        self.ftp_host, self.ftp_user, self.ftp_password,
                        ftp_remote_path, linux_local_path, self.ftp_port
                    )
                elif method == "curl":
                    return ssh.download_from_ftp_with_curl(
                        self.ftp_host, self.ftp_user, self.ftp_password,
                        ftp_remote_path, linux_local_path, self.ftp_port
                    )
                elif method == "lftp":
                    return ssh.download_from_ftp_with_lftp(
                        self.ftp_host, self.ftp_user, self.ftp_password,
                        ftp_remote_path, linux_local_path, self.ftp_port
                    )
                else:
                    raise ValueError(f"不支持的下载方法: {method}")

        except Exception as e:
            logger.error(f"FTP下载失败: {str(e)}")
            raise

    def batch_download_files(self, download_list: List[Dict[str, str]],
                             method: str = "wget", max_retries: int = 3) -> List[CommandResult]:
        """
        批量从FTP服务器下载文件到远程Linux服务器

        Args:
            download_list: 下载列表，格式: [{"ftp_path": "远程路径", "local_path": "本地路径"}]
            method: 下载方法
            max_retries: 最大重试次数

        Returns:
            List[CommandResult]: 下载结果列表
        """
        logger.info(f"批量从FTP下载 {len(download_list)} 个文件")

        results = []
        for i, item in enumerate(download_list):
            ftp_path = item.get("ftp_path", "")
            local_path = item.get("local_path", "")

            if not ftp_path or not local_path:
                logger.warning(f"跳过无效的下载项 {i + 1}: {item}")
                continue

            logger.info(f"下载文件 {i + 1}/{len(download_list)}: {ftp_path}")

            # 重试机制
            success = False
            last_result = None

            for retry in range(max_retries):
                try:
                    result = self.download_file(ftp_path, local_path, method)
                    last_result = result

                    if result.success:
                        logger.info(f"✓ 文件下载成功: {ftp_path}")
                        success = True
                        break
                    else:
                        logger.warning(f"下载失败 (尝试 {retry + 1}/{max_retries}): {result.stderr}")

                except Exception as e:
                    logger.error(f"下载异常 (尝试 {retry + 1}/{max_retries}): {str(e)}")

            if not success:
                logger.error(f"✗ 文件下载最终失败: {ftp_path}")

            if last_result:
                results.append(last_result)

        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量下载完成，成功: {success_count}/{len(results)}")
        return results

    def download_directory(self, ftp_remote_dir: str, linux_local_dir: str,
                           file_patterns: Optional[List[str]] = None) -> CommandResult:
        """
        从FTP服务器下载整个目录到远程Linux服务器

        Args:
            ftp_remote_dir: FTP服务器上的目录路径
            linux_local_dir: Linux服务器上的本地目录路径
            file_patterns: 文件过滤模式列表，如 ["*.zip", "*.tar.gz"]

        Returns:
            CommandResult: 下载命令执行结果
        """
        logger.info(f"从FTP下载目录: {self.ftp_host}{ftp_remote_dir} -> {linux_local_dir}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                # 创建本地目录
                mkdir_cmd = f"mkdir -p {linux_local_dir}"
                mkdir_result = ssh.execute_command(mkdir_cmd)
                if not mkdir_result.success:
                    logger.error(f"创建目录失败: {mkdir_result.stderr}")
                    return mkdir_result

                # 构建lftp命令来下载整个目录
                if file_patterns:
                    # 如果有文件过滤，使用find + mget
                    patterns_str = " -o ".join([f"-name '{pattern}'" for pattern in file_patterns])
                    lftp_script = f"""
                    open -u {self.ftp_user},{self.ftp_password} -p {self.ftp_port} {self.ftp_host}
                    cd {ftp_remote_dir}
                    lcd {linux_local_dir}
                    find . \\( {patterns_str} \\) -exec get {{}} \\;
                    quit
                    """
                else:
                    # 下载整个目录
                    lftp_script = f"""
                    open -u {self.ftp_user},{self.ftp_password} -p {self.ftp_port} {self.ftp_host}
                    mirror {ftp_remote_dir} {linux_local_dir}
                    quit
                    """

                # 将脚本写入临时文件
                script_file = f"/tmp/lftp_script_{int(time.time())}.txt"
                write_script_cmd = f"cat > {script_file} << 'EOF'\n{lftp_script.strip()}\nEOF"

                write_result = ssh.execute_command(write_script_cmd)
                if not write_result.success:
                    logger.error(f"创建lftp脚本失败: {write_result.stderr}")
                    return write_result

                # 执行lftp脚本
                lftp_cmd = f"lftp -f {script_file}"
                result = ssh.execute_command(lftp_cmd, timeout=1800)  # 30分钟超时

                # 清理脚本文件
                cleanup_cmd = f"rm -f {script_file}"
                ssh.execute_command(cleanup_cmd)

                if result.success:
                    logger.info(f"FTP目录下载成功: {ftp_remote_dir} -> {linux_local_dir}")
                else:
                    logger.error(f"FTP目录下载失败: {result.stderr}")

                return result

        except Exception as e:
            logger.error(f"FTP目录下载失败: {str(e)}")
            raise


class PackageFilesCompareUtil:
    """软件包文件对比工具类"""

    def __init__(self, ssh_config: SSHConfig, ftp_util: FTPDownloadUtil):
        """
        初始化对比工具

        Args:
            ssh_config: SSH连接配置
        """
        self.ssh_config = ssh_config
        self.ftp_util = ftp_util
        self.temp_dir = None
        self.bin_save_path = config_manager_dev_env_check.config['bin_save_path']
        self.bin_check_config = config_manager_dev_env_check.config
        self.pre_package_save_path = os.path.join(self.bin_save_path, "pre_package_save_path")
        self.after_package_save_path = os.path.join(self.bin_save_path, "after_package_save_path")
        self.pre_package_md5_path = os.path.join(self.pre_package_save_path, "result_md5")
        self.after_package_md5_path = os.path.join(self.after_package_save_path, "result_md5")
        self.temp_dir = None
        self.compare_result = None
        self.compare_excel_file = None
        self.out_compare_excel_file = None
        self.beyond_compare_tool_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\BeyondCompare4\BCompare.exe"

    def __enter__(self):
        """上下文管理器入口"""
        self.temp_dir = tempfile.mkdtemp(prefix="package_compare_")
        logger.info(f"创建临时目录: {self.temp_dir}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info(f"清理临时目录: {self.temp_dir}")

    def download_package_files(self, remote_package_dir: str,
                               local_save_dir: Optional[str] = None,
                               file_patterns: Optional[List[str]] = None) -> str:
        """
        从远程服务器下载软件包文件

        Args:
            remote_package_dir: 远程软件包目录路径
            local_save_dir: 本地保存目录（可选，默认使用临时目录）
            file_patterns: 文件过滤模式列表，如 ["*.rpm", "*.deb", "*.tar.gz"]

        Returns:
            str: 本地保存目录路径
        """
        if not local_save_dir:
            local_save_dir = os.path.join(self.temp_dir, "downloaded_packages")

        # 确保本地目录存在
        os.makedirs(local_save_dir, exist_ok=True)

        logger.info(f"开始下载软件包文件: {remote_package_dir} -> {local_save_dir}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                # 如果指定了文件模式，先获取文件列表进行过滤
                if file_patterns:
                    success = self._download_filtered_files(
                        ssh, remote_package_dir, local_save_dir, file_patterns
                    )
                else:
                    # 下载整个目录
                    success = ssh.download_directory(
                        remote_dir=remote_package_dir,
                        local_dir=local_save_dir,
                        exclude_patterns=["*.log", "*.tmp", "__pycache__"]
                    )

                if success:
                    logger.info(f"软件包文件下载成功: {local_save_dir}")
                    return local_save_dir
                else:
                    raise RuntimeError("软件包文件下载失败")

        except Exception as e:
            logger.error(f"下载软件包文件时出错: {str(e)}")
            raise

    def select_shell_script(self, after_pack_path: str):
        """
        选择shell脚本
        """
        file_forder = after_pack_path.strip("/").split("/")[0]
        shell_script_cfg_map = self.bin_check_config["shell_script_cfg_map"]
        if file_forder in shell_script_cfg_map:
            return shell_script_cfg_map[file_forder]
        else:
            if "Orca" in file_forder:
                return "emmc_checkOrca2.sh"
            else:
                return "emmc_checkPanda.sh"

    def download_and_parse_all_package_files(self, pre_pack_path: str,
                                             after_pack_path: str):
        # 创建FTP下载器实例
        # linux_pre_pack_path = f"/home/<USER>/xhtest/{os.path.basename(pre_pack_path.path)}"
        # linux_after_pack_path = f"/home/<USER>/xhtest/{os.path.basename(after_pack_path.path)}"
        base_path = "/home/<USER>/xhtest/"
        linux_pre_pack_path = f"/home/<USER>/xhtest/linux_pre_pack.zip"
        linux_after_pack_path = f"/home/<USER>/xhtest/linux_after_pack.zip"

        self.ftp_util.download_file(
            ftp_remote_path=pre_pack_path,
            linux_local_path=linux_pre_pack_path,
            method="wget"
        )
        self.ftp_util.download_file(
            ftp_remote_path=after_pack_path,
            linux_local_path=linux_after_pack_path,
            method="wget"
        )
        shell_name = self.select_shell_script(after_pack_path)
        unzip_parse_commond = [
            f"echo '{self.ssh_config.password}' | sudo -S rm -rf /home/<USER>/xhtest/bin_pack_path_dir",
            f"unzip {linux_pre_pack_path} -d  /home/<USER>/xhtest/bin_pack_path_dir",
            f"cp {base_path + shell_name} {base_path + 'bin_pack_path_dir'}",
            f"cp {base_path + 'emmc_raw_4g_combine_panda'} {base_path + 'bin_pack_path_dir'}",
            f"chmod 777 {base_path + 'bin_pack_path_dir/' + shell_name}",
            f"chmod 777 {base_path + 'bin_pack_path_dir/emmc_raw_4g_combine_panda'}",
            f"cd {base_path + 'bin_pack_path_dir'} && echo '{self.ssh_config.password}' | sudo -S ./{shell_name} E3/",
        ]
        self.execute_remote_commands(unzip_parse_commond, 10000)

        self.download_package_files(remote_package_dir="/home/<USER>/xhtest/bin_pack_path_dir/RESULT",
                                    local_save_dir=self.pre_package_save_path)

        unzip_parse_commond = [
            f"echo '{self.ssh_config.password}' | sudo -S rm -rf /home/<USER>/xhtest/bin_pack_path_dir",
            f"unzip {linux_after_pack_path} -d  /home/<USER>/xhtest/bin_pack_path_dir",
            f"cp {base_path + shell_name} {base_path + 'bin_pack_path_dir'}",
            f"cp {base_path + 'emmc_raw_4g_combine_panda'} {base_path + 'bin_pack_path_dir'}",
            f"chmod 777 {base_path + 'bin_pack_path_dir/' + shell_name}",
            f"chmod 777 {base_path + 'bin_pack_path_dir/emmc_raw_4g_combine_panda'}",
            f"cd {base_path + 'bin_pack_path_dir'} && echo '{self.ssh_config.password}' | sudo -S ./{shell_name} E3/",
        ]
        self.execute_remote_commands(unzip_parse_commond, 10000)
        self.download_package_files(remote_package_dir="/home/<USER>/xhtest/bin_pack_path_dir/RESULT",
                                    local_save_dir=self.after_package_save_path)
        return

    def get_all_files(self, folder):
        """
        获取文件夹中所有文件的相对路径
        """
        file_paths = []
        for root, _, files in os.walk(folder):
            for file in files:
                # 获取相对路径
                relative_path = os.path.relpath(os.path.join(root, file), folder)
                file_paths.append(relative_path)
        return file_paths

    def compare_files(self, file_a, file_b):
        """
        对比两个文件内容，返回差异行，包括增删改的情况。
        Args:
            file_a (str): 文件 A 的路径。
            file_b (str): 文件 B 的路径。
        Returns:
            list[tuple]: 差异列表，每个元素是一个元组，包含差异类型和内容。
                         差异类型包括:
                         - "+" 表示文件 B 中新增的行。
                         - "-" 表示文件 A 中删除的行。
                         - " " 表示文件 A 和文件 B 中相同的行。
        """

        # 读取文件内容
        with open(file_a, 'r', encoding='utf-8', errors='ignore') as f_a, open(file_b, 'r', encoding='utf-8',
                                                                               errors='ignore') as f_b:
            lines_a = [line.strip() for line in f_a.readlines()]  # 去除行末空白符
            lines_b = [line.strip() for line in f_b.readlines()]  # 去除行末空白符

        # 使用 difflib 进行对比
        diff = difflib.ndiff(lines_a, lines_b)

        return list(diff)

    def compare_folders(self, pre_package_save_path, after_package_save_path):
        """
        对比两个文件夹中的文件，提取差异行
        """
        result = {}
        # 获取两个文件夹中的所有文件相对路径
        pre_package_files = self.get_all_files(pre_package_save_path)
        after_package_files = self.get_all_files(after_package_save_path)

        # 遍历文件夹 a 中的文件
        for relative_path in pre_package_files:
            if relative_path in after_package_files:  # 确保文件夹 b 中也存在同路径文件
                pre_package_file = os.path.join(pre_package_save_path, relative_path)
                after_package_file = os.path.join(after_package_save_path, relative_path)
                differences = self.compare_files(pre_package_file, after_package_file)
                result[relative_path] = differences
        return result

    def compare_local_bin_file(self, pre_package_local_path, after_package_local_path):
        self.compare_result = self.compare_folders(pre_package_local_path, after_package_local_path)
        logger.info(f"新老二进制bin对比结果: {self.compare_result['agl_app_a.txt']}")
        return self.compare_result

    def is_whitelist_filename(self, filename):
        """
        判断文件名是否符合以下格式：
        - Partition_table_Group_Backup.bin
        - Certificate_XX_Group_X.bin
        - Certificate_RootfsGroup_xx.bin
        """
        # 定义正则表达式模式
        bin_check_white_list_name = self.bin_check_config["bin_check_cfg"]["bin_check_white_list_name"]
        pattern = re.compile(bin_check_white_list_name)
        # 使用正则表达式匹配文件名
        return bool(pattern.match(filename))

    def compare_md5_folders_content(self, folder1, folder2, excel):
        """
        对比两个文件夹中相同路径下的文件内容是否一致，并将结果保存到 Excel 表格中。
        使用 ExcelUtil 进行 Excel 操作，并以并排格式显示。
        """

        def get_all_files(folder):
            """
            获取文件夹中所有文件的相对路径
            """
            file_paths = []
            for root, _, files in os.walk(folder):
                for file in files:
                    file_paths.append(os.path.relpath(os.path.join(root, file), folder))
            return file_paths

        # 获取两个文件夹中的所有文件
        files1 = get_all_files(folder1)
        files2 = get_all_files(folder2)

        # 找到两个文件夹中共有的文件
        common_files = set(files1).intersection(set(files2))

        # 对比文件内容并记录结果
        comparison_results = []
        for file in common_files:
            file1_path = os.path.join(folder1, file)
            file2_path = os.path.join(folder2, file)

            # 文件都存在，检查内容是否相同
            is_same = filecmp.cmp(file1_path, file2_path, shallow=False)
            comparison_results.append({
                "文件名": file,
                "内容是否相同": "是" if is_same else "否",
                "左边文件": file1_path,
                "右边文件": file2_path
            })

        # 使用 ExcelUtil 写入对比结果到 Excel
        sheet_name = "一覧"

        # 写入表头
        headers = ["左边文件", "内容是否相同", "右边文件"]
        for col_num, header in enumerate(headers, start=2):
            excel.write_cell(sheet_name, 40, col_num, header)
            excel.set_cell_style(sheet_name, 40, col_num, bin_compare_styles['header'])

        # 写入对比结果
        for row_num, result in enumerate(comparison_results, start=41):
            left_content = result["左边文件"]
            is_same = result["内容是否相同"]
            right_content = result["右边文件"]

            # 写入文件名和对比结果
            excel.write_cell(sheet_name, row_num, 2, left_content)
            excel.write_cell(sheet_name, row_num, 3, is_same)
            excel.write_cell(sheet_name, row_num, 4, right_content)

            # 应用样式
            if is_same == "是":
                style = bin_compare_styles['same']
            else:
                style = bin_compare_styles['deleted']
                # 特殊处理文件名
                file_name = result["文件名"]
                if file_name == "com1.txt":
                    file_name = "com1%00.txt"
                elif file_name == "com2.txt":
                    file_name = "com2%00.txt"

                # 搜索文件名并标红字体
                # 搜索文件名并标红字体
                for row in range(13, 17):  # 搜索行范围
                    for col in range(1, 19):  # 搜索列范围 (A-R)
                        cell_value = excel.read_cell(sheet_name, row, col)
                        if cell_value and file_name in str(cell_value):
                            excel.set_cell_style(sheet_name, row, col, CellStyle(font_color="FF0000"))
            excel.set_cell_style(sheet_name, row_num, 2, style)
            excel.set_cell_style(sheet_name, row_num, 3, style)
            excel.set_cell_style(sheet_name, row_num, 4, style)

    def write_compare_result_to_sheet(self, excel: ExcelUtil, file_name, differences):
        try:

            self.compare_md5_folders_content(self.pre_package_md5_path, self.after_package_md5_path, excel)
            sheet_name = bin_compare_excel_sheet[file_name][0]

            # 从ndiff结果中重建原始文件内容
            lines1 = []  # 源文件内容
            lines2 = []  # 目标文件内容
            need_check_bin_list = []

            # 从ndiff结果中分离出两个文件的内容
            for diff_line in differences:
                if not diff_line or len(diff_line) < 2:
                    continue

                status = diff_line[0]
                content = diff_line[2:] if len(diff_line) > 2 else ""

                if status == ' ':  # 相同行，两个文件都有
                    lines1.append(content)
                    lines2.append(content)
                elif status == '-':  # 只在源文件中存在（被删除的行）
                    lines1.append(content)
                elif status == '+':  # 只在目标文件中存在（新增的行）
                    lines2.append(content)
                    need_check_bin_list.append(content.split("RESULT")[-1].strip("/"))
                # 忽略 '?' 行（ndiff的提示行）

            # 使用SequenceMatcher进行精确对比
            matcher = difflib.SequenceMatcher(None, lines1, lines2)

            if bin_compare_excel_sheet[file_name][1] > 0:
                begin_index = excel._find_data_index_in_row(file_name, sheet_name, 1, 2, 30) + 2
            else:
                begin_index = 7

            write_col = excel._find_data_index_in_col("文本比较", sheet_name, 1, 30, 10)
            if write_col > 1:
                offset = 1
            else:
                offset = 0
            left_md5 = os.path.join(self.pre_package_md5_path, file_name)
            right_md5 = os.path.join(self.after_package_md5_path, file_name)
            excel.write_cell(sheet_name, begin_index - 2, write_col, f"左边文件: {left_md5}")
            excel.write_cell(sheet_name, begin_index - 1, write_col, f"右边文件: {right_md5}")

            # 计算需要插入的行数（源文件的总行数）
            count = len(lines1)
            with excel.worksheet(sheet_name) as ws:
                # 插入行
                ws.insert_rows_with_format_copy(begin_index + 1, count)

            row = begin_index
            line1_num = 0
            line2_num = 0
            bin_check_ok = self.bin_check_config["bin_check_cfg"]["bin_check_ok"]
            bin_check_ng = self.bin_check_config["bin_check_cfg"]["bin_check_ng"]

            for tag, i1, i2, j1, j2 in matcher.get_opcodes():
                if tag == 'equal':
                    # 相同的行
                    for k in range(i2 - i1):
                        line1_num += 1
                        line2_num += 1
                        line_content = lines1[i1 + k]

                        excel.write_cell(sheet_name, row, write_col, line_content)
                        excel.write_cell(sheet_name, row, write_col + 2 + offset, line_content)
                        excel.write_cell(sheet_name, row, write_col + 3 + offset, bin_check_ok)
                        excel.write_cell(sheet_name, row, write_col + 4 + offset, "OK")
                        excel.write_cell(sheet_name, row, write_col + 5 + offset, "-")

                        excel.set_cell_style(sheet_name, row, write_col, bin_compare_styles['same'])
                        excel.set_cell_style(sheet_name, row, write_col + 2 + offset, bin_compare_styles['same'])
                        row += 1

                elif tag == 'delete':
                    # 删除的行（只在原文件中存在）
                    for k in range(i2 - i1):
                        line1_num += 1
                        line_content = lines1[i1 + k]

                        excel.write_cell(sheet_name, row, write_col, f"- {line_content}")
                        excel.write_cell(sheet_name, row, write_col + 1, "删除")
                        excel.write_cell(sheet_name, row, write_col + 2 + offset, "")
                        excel.write_cell(sheet_name, row, write_col + 3 + offset, bin_check_ng)

                        excel.set_cell_style(sheet_name, row, write_col, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 1, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 2 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 3 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 4 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 5 + offset, bin_compare_styles['deleted'])
                        row += 1

                elif tag == 'insert':
                    # 新增的行（只在新文件中存在）
                    for k in range(j2 - j1):
                        line2_num += 1
                        line_content = lines2[j1 + k]

                        excel.write_cell(sheet_name, row, write_col, "")
                        excel.write_cell(sheet_name, row, write_col + 1, "")
                        excel.write_cell(sheet_name, row, write_col + 2 + offset, f"+ {line_content}")
                        excel.write_cell(sheet_name, row, write_col + 3 + offset, bin_check_ng)

                        excel.set_cell_style(sheet_name, row, write_col, bin_compare_styles['added'])
                        excel.set_cell_style(sheet_name, row, write_col + 1, bin_compare_styles['added'])
                        excel.set_cell_style(sheet_name, row, write_col + 2 + offset, bin_compare_styles['added'])
                        excel.set_cell_style(sheet_name, row, write_col + 3 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 4 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 5 + offset, bin_compare_styles['deleted'])

                        row += 1

                elif tag == 'replace':
                    # 替换的行（修改）- 确保左右对齐
                    max_lines = max(i2 - i1, j2 - j1)

                    for k in range(max_lines):
                        # 左侧（删除的行）
                        if k < (i2 - i1):
                            line1_num += 1
                            left_content = lines1[i1 + k]
                            excel.write_cell(sheet_name, row, write_col, f"- {left_content}")
                        else:
                            excel.write_cell(sheet_name, row, write_col, "")
                        excel.set_cell_style(sheet_name, row, write_col, bin_compare_styles['deleted'])
                        excel.write_cell(sheet_name, row, write_col + 1, "变更")
                        excel.write_cell(sheet_name, row, write_col + 3 + offset, bin_check_ng)

                        excel.set_cell_style(sheet_name, row, write_col + 1, bin_compare_styles['modified'])
                        excel.set_cell_style(sheet_name, row, write_col + 2 + offset, bin_compare_styles['added'])
                        excel.set_cell_style(sheet_name, row, write_col + 3 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 4 + offset, bin_compare_styles['deleted'])
                        excel.set_cell_style(sheet_name, row, write_col + 5 + offset, bin_compare_styles['deleted'])

                        # 右侧（新增的行）
                        if k < (j2 - j1):
                            line2_num += 1
                            right_content = lines2[j1 + k]
                            excel.write_cell(sheet_name, row, write_col + 2 + offset, f"+ {right_content}")
                            if self.is_whitelist_filename(right_content.split("/")[-1]):
                                excel.write_cell(sheet_name, row, write_col + 4 + offset, "OK")
                                excel.write_cell(sheet_name, row, write_col + 5 + offset, "詳細は下図に参照")
                        else:
                            excel.write_cell(sheet_name, row, write_col + 2 + offset, "")
                        row += 1
            check_bin_row = row + 20
            self.insert_bin_compare_pic(need_check_bin_list, excel.engine.workbook[sheet_name], check_bin_row)
        except Exception as e:
            traceback.print_exc()
            raise e

    def write_compare_result_to_file(self, compare_excel_file):
        self.compare_excel_file = compare_excel_file
        current_time = datetime.now()
        # 格式化时间戳：年_月_日_时_分_秒
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")
        # 拼接文件名
        output_data_path = os.path.normpath(ENV.config.output_data_path)
        if not os.path.exists(output_data_path):
            os.makedirs(output_data_path)
        ret_book = os.path.join(output_data_path, f"EMMCベース確認_{timestamp}.xlsx")

        with ExcelUtil(self.compare_excel_file, engine="openpyxl", auto_create=True) as excel:
            try:
                for file_name, differences in self.compare_result.items():
                    self.write_compare_result_to_sheet(excel, file_name, differences)
                excel.save(ret_book)
                self.out_compare_excel_file = ret_book
            except Exception as e:
                traceback.print_exc()
                raise e

    def _download_filtered_files(self, ssh: SSHUtil, remote_dir: str,
                                 local_dir: str, patterns: List[str]) -> bool:
        """
        下载符合模式的文件
        Args:
            ssh: SSH连接实例
            remote_dir: 远程目录
            local_dir: 本地目录
            patterns: 文件模式列表
        Returns:
            bool: 下载是否成功
        """
        try:
            # 构建find命令来查找符合模式的文件
            find_conditions = []
            for pattern in patterns:
                find_conditions.append(f"-name '{pattern}'")

            find_cmd = f"find {remote_dir} -type f \\( {' -o '.join(find_conditions)} \\)"

            # 执行find命令获取文件列表
            result = ssh.execute_command(find_cmd)
            if not result.success:
                logger.error(f"获取文件列表失败: {result.stderr}")
                return False

            file_list = [f.strip() for f in result.stdout.splitlines() if f.strip()]
            logger.info(f"找到 {len(file_list)} 个匹配的文件")

            # 逐个下载文件
            success_count = 0
            for remote_file in file_list:
                try:
                    # 计算相对路径
                    rel_path = os.path.relpath(remote_file, remote_dir)
                    local_file = os.path.join(local_dir, rel_path)

                    # 确保本地目录存在
                    os.makedirs(os.path.dirname(local_file), exist_ok=True)

                    # 下载文件
                    if ssh.download_file(remote_file, local_file):
                        success_count += 1
                        logger.debug(f"下载成功: {remote_file}")
                    else:
                        logger.warning(f"下载失败: {remote_file}")

                except Exception as e:
                    logger.error(f"下载文件 {remote_file} 时出错: {str(e)}")

            logger.info(f"成功下载 {success_count}/{len(file_list)} 个文件")
            return success_count > 0

        except Exception as e:
            logger.error(f"过滤下载文件时出错: {str(e)}")
            return False

    def download_package_as_archive(self, remote_package_dir: str,
                                    local_archive_path: Optional[str] = None,
                                    archive_format: str = "tar.gz") -> str:
        """
        将远程软件包目录打包下载

        Args:
            remote_package_dir: 远程软件包目录
            local_archive_path: 本地压缩包路径（可选）
            archive_format: 压缩格式

        Returns:
            str: 本地压缩包路径
        """
        if not local_archive_path:
            package_name = os.path.basename(remote_package_dir.rstrip('/'))
            local_archive_path = os.path.join(
                self.temp_dir, f"{package_name}.{archive_format}"
            )

        logger.info(f"打包下载软件包: {remote_package_dir} -> {local_archive_path}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                success = ssh.download_directory_as_archive(
                    remote_dir=remote_package_dir,
                    local_path=local_archive_path,
                    archive_format=archive_format
                )

                if success:
                    logger.info(f"软件包打包下载成功: {local_archive_path}")
                    return local_archive_path
                else:
                    raise RuntimeError("软件包打包下载失败")

        except Exception as e:
            logger.error(f"打包下载软件包时出错: {str(e)}")
            raise

    def get_EMMC_compare_book_path(self) -> str:
        """获取样本书路径"""
        return os.path.join(os.path.dirname(__file__), os.path.normpath('../book_data/Bin_Compare_EMMC_template.xlsx'))

    def execute_remote_commands(self, commands: List[str],
                                timeout: Optional[int] = None,
                                stop_on_error: bool = False) -> List[CommandResult]:
        """
        在远程服务器上执行命令列表

        Args:
            commands: 要执行的命令列表
            timeout: 每个命令的超时时间（秒）
            stop_on_error: 是否在遇到错误时停止执行后续命令

        Returns:
            List[CommandResult]: 命令执行结果列表
        """
        logger.info(f"开始执行 {len(commands)} 个远程命令")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                results = []

                for i, command in enumerate(commands):
                    logger.debug(f"执行命令 {i + 1}/{len(commands)}: {command}")

                    result = ssh.execute_remote_command(hostname=self.ssh_config.hostname,
                                                        username=self.ssh_config.username,
                                                        password=self.ssh_config.password,
                                                        command=command, timeout=timeout)
                    results.append(result)

                    # 记录执行结果
                    if result.success:
                        logger.debug(f"命令执行成功: {command}")
                    else:
                        logger.warning(f"命令执行失败: {command}, 错误: {result.stderr}")

                        # 如果设置了遇错停止，则中断执行
                        if stop_on_error:
                            logger.error(f"命令执行失败，停止后续命令执行")
                            break

                logger.info(f"命令执行完成，成功: {sum(1 for r in results if r.success)}/{len(results)}")
                return results

        except Exception as e:
            logger.error(f"执行远程命令时出错: {str(e)}")
            raise

    def download_from_ftp_to_remote(self, ftp_host: str, ftp_user: str, ftp_password: str,
                                    ftp_remote_path: str, linux_local_path: str,
                                    ftp_port: int = 21, method: str = "wget",
                                    create_dirs: bool = True) -> CommandResult:
        """
        从FTP服务器下载文件到远程Linux服务器

        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_path: FTP服务器上的文件路径
            linux_local_path: Linux服务器上的本地路径
            ftp_port: FTP端口（默认21）
            method: 下载方法 ("wget", "curl", "lftp")
            create_dirs: 是否自动创建目录

        Returns:
            CommandResult: 下载命令执行结果
        """
        logger.info(f"从FTP下载文件到远程Linux: {ftp_host}{ftp_remote_path} -> {linux_local_path}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                # 如果需要，先创建目录
                if create_dirs:
                    dir_path = os.path.dirname(linux_local_path)
                    if dir_path:
                        mkdir_cmd = f"mkdir -p {dir_path}"
                        mkdir_result = ssh.execute_command(mkdir_cmd)
                        if not mkdir_result.success:
                            logger.warning(f"创建目录失败: {mkdir_result.stderr}")

                # 根据方法选择下载命令
                if method == "wget":
                    return ssh.download_from_ftp(
                        ftp_host, ftp_user, ftp_password, ftp_remote_path, linux_local_path, ftp_port
                    )
                elif method == "curl":
                    return ssh.download_from_ftp_with_curl(
                        ftp_host, ftp_user, ftp_password, ftp_remote_path, linux_local_path, ftp_port
                    )
                elif method == "lftp":
                    return ssh.download_from_ftp_with_lftp(
                        ftp_host, ftp_user, ftp_password, ftp_remote_path, linux_local_path, ftp_port
                    )
                else:
                    raise ValueError(f"不支持的下载方法: {method}")

        except Exception as e:
            logger.error(f"FTP下载失败: {str(e)}")
            raise

    def batch_download_from_ftp(self, ftp_host: str, ftp_user: str, ftp_password: str,
                                download_list: List[Dict[str, str]], ftp_port: int = 21,
                                method: str = "wget", max_retries: int = 3) -> List[CommandResult]:
        """
        批量从FTP服务器下载文件到远程Linux服务器

        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            download_list: 下载列表，格式: [{"ftp_path": "远程路径", "local_path": "本地路径"}]
            ftp_port: FTP端口
            method: 下载方法
            max_retries: 最大重试次数

        Returns:
            List[CommandResult]: 下载结果列表
        """
        logger.info(f"批量从FTP下载 {len(download_list)} 个文件")

        results = []
        for i, item in enumerate(download_list):
            ftp_path = item.get("ftp_path", "")
            local_path = item.get("local_path", "")

            if not ftp_path or not local_path:
                logger.warning(f"跳过无效的下载项 {i + 1}: {item}")
                continue

            logger.info(f"下载文件 {i + 1}/{len(download_list)}: {ftp_path}")

            # 重试机制
            success = False
            last_result = None

            for retry in range(max_retries):
                try:
                    result = self.download_from_ftp_to_remote(
                        ftp_host, ftp_user, ftp_password, ftp_path, local_path, ftp_port, method
                    )
                    last_result = result

                    if result.success:
                        logger.info(f"✓ 文件下载成功: {ftp_path}")
                        success = True
                        break
                    else:
                        logger.warning(f"下载失败 (尝试 {retry + 1}/{max_retries}): {result.stderr}")

                except Exception as e:
                    logger.error(f"下载异常 (尝试 {retry + 1}/{max_retries}): {str(e)}")

            if not success:
                logger.error(f"✗ 文件下载最终失败: {ftp_path}")

            if last_result:
                results.append(last_result)

        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量下载完成，成功: {success_count}/{len(results)}")
        return results

    def download_ftp_directory_to_remote(self, ftp_host: str, ftp_user: str, ftp_password: str,
                                         ftp_remote_dir: str, linux_local_dir: str,
                                         ftp_port: int = 21,
                                         file_patterns: Optional[List[str]] = None) -> CommandResult:
        """
        从FTP服务器下载整个目录到远程Linux服务器

        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_dir: FTP服务器上的目录路径
            linux_local_dir: Linux服务器上的本地目录路径
            ftp_port: FTP端口
            file_patterns: 文件过滤模式列表，如 ["*.zip", "*.tar.gz"]

        Returns:
            CommandResult: 下载命令执行结果
        """
        logger.info(f"从FTP下载目录到远程Linux: {ftp_host}{ftp_remote_dir} -> {linux_local_dir}")

        try:
            with SSHUtil(self.ssh_config) as ssh:
                # 创建本地目录
                mkdir_cmd = f"mkdir -p {linux_local_dir}"
                mkdir_result = ssh.execute_command(mkdir_cmd)
                if not mkdir_result.success:
                    logger.error(f"创建目录失败: {mkdir_result.stderr}")
                    return mkdir_result

                # 构建lftp命令来下载整个目录
                if file_patterns:
                    # 如果有文件过滤，使用find + mget
                    patterns_str = " -o ".join([f"-name '{pattern}'" for pattern in file_patterns])
                    lftp_script = f"""
                    open -u {ftp_user},{ftp_password} -p {ftp_port} {ftp_host}
                    cd {ftp_remote_dir}
                    lcd {linux_local_dir}
                    find . \\( {patterns_str} \\) -exec get {{}} \\;
                    quit
                    """
                else:
                    # 下载整个目录
                    lftp_script = f"""
                    open -u {ftp_user},{ftp_password} -p {ftp_port} {ftp_host}
                    mirror {ftp_remote_dir} {linux_local_dir}
                    quit
                    """

                # 将脚本写入临时文件
                script_file = f"/tmp/lftp_script_{int(time.time())}.txt"
                write_script_cmd = f"cat > {script_file} << 'EOF'\n{lftp_script.strip()}\nEOF"

                write_result = ssh.execute_command(write_script_cmd)
                if not write_result.success:
                    logger.error(f"创建lftp脚本失败: {write_result.stderr}")
                    return write_result

                # 执行lftp脚本
                lftp_cmd = f"lftp -f {script_file}"
                result = ssh.execute_command(lftp_cmd, timeout=1800)  # 30分钟超时

                # 清理脚本文件
                cleanup_cmd = f"rm -f {script_file}"
                ssh.execute_command(cleanup_cmd)

                if result.success:
                    logger.info(f"FTP目录下载成功: {ftp_remote_dir} -> {linux_local_dir}")
                else:
                    logger.error(f"FTP目录下载失败: {result.stderr}")

                return result

        except Exception as e:
            logger.error(f"FTP目录下载失败: {str(e)}")
            raise

    def generate_bc_hex_report_v2(self, file1, file2, report_path):
        """使用Beyond Compare生成十六进制差异报告 - 直接输出HTML"""
        if not os.path.exists(file1):
            logger.error(f"错误: 文件 '{file1}' 不存在")
            return False

        if not os.path.exists(file2):
            logger.error(f"错误: 文件 '{file2}' 不存在")
            return False
        bc_path = self.beyond_compare_tool_path
        if bc_path is None:
            logger.error("错误: 未找到Beyond Compare。请指定其安装路径。")
            return False

        # 检查文件大小
        file1_size = os.path.getsize(file1) / (1024 * 1024)  # MB
        file2_size = os.path.getsize(file2) / (1024 * 1024)  # MB
        logger.debug(f"文件大小: {file1_size:.1f}MB, {file2_size:.1f}MB")

        try:
            # 确保输出路径是绝对路径
            report_path = os.path.abspath(report_path)

            # 确保输出目录存在
            output_dir = os.path.dirname(report_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 使用Beyond Compare脚本生成HTML报告
            script_content = f'hex-report layout:side-by-side options:line-numbers,display-mismatches output-to:"{report_path}" output-options:html-color "{file1}" "{file2}"'

            # 创建脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='ansi') as script_file:
                script_file.write(script_content)
                script_path = script_file.name

            logger.debug(f"脚本文件: {script_path}")
            logger.debug(f"脚本内容: {script_content}")
            logger.info(f"执行命令: {bc_path} /silent /closescript @{script_path}")

            # 执行Beyond Compare
            command = [bc_path, "/silent", "/closescript", f"@{script_path}"]

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300,
                cwd=os.path.dirname(bc_path)
            )

            # 清理脚本文件
            try:
                os.remove(script_path)
            except:
                pass

            logger.info(f"Beyond Compare 返回码: {result.returncode}")

            # 等待一下文件生成
            time.sleep(1)

            # 检查是否生成了报告文件
            if os.path.exists(report_path):
                logger.info(f"文件:{file1} 报告生成成功: {report_path}")
            else:
                logger.error(f"报告文件未生成: {report_path}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Beyond Compare 执行超时")
            traceback.print_exc()
            return False
        except Exception as e:
            traceback.print_exc()
            raise e

    def parse_bc_html_with_byte_diff(self, html_file, ws, check_insert_row):
        """解析BC HTML，偏移量和内容合并，差异标红"""
        logger.info(f"开始解析HTML文件: {html_file}")
        try:
            # 读取HTML文件
            with open(html_file, 'r', encoding='gb2312', errors='ignore') as f:
                html_content = f.read()

            soup = BeautifulSoup(html_content, 'html.parser')

            # 设置样式
            normal_font = Font(name="Consolas", size=10)

            center_align = Alignment(horizontal="center", vertical="center")
            left_align = Alignment(horizontal="left", vertical="center")
            border = Border(
                left=Side(style="thin"),
                right=Side(style="thin"),
                top=Side(style="thin"),
                bottom=Side(style="thin")
            )

            # 查找表格
            table = soup.find('table', class_='fc')
            if not table:
                logger.error("❌ 未找到表格")
                return False

            def extract_content_with_diff_info(td_cell):
                """提取td内容，返回文本片段列表，每个片段包含(文本, 是否差异)"""
                segments = []
                current_text = ""
                is_diff = False

                # 遍历所有子元素
                for element in td_cell.descendants:
                    if element.name == 'span':
                        # 如果有累积的文本，先保存
                        if current_text:
                            segments.append((current_text, is_diff))
                            current_text = ""

                        # 处理span内容
                        span_classes = element.get('class', [])
                        text = element.get_text()

                        if text:
                            text = text.replace('\xa0', ' ')
                            is_span_diff = 'HexSegDiff' in span_classes
                            segments.append((text, is_span_diff))

                    elif isinstance(element, str) and element.parent.name != 'span':
                        # 直接文本节点，保留空格
                        text = str(element)
                        if text:
                            text = text.replace('&nbsp;', ' ').replace('\xa0', ' ')
                            current_text += text

                # 保存最后的文本
                if current_text:
                    segments.append((current_text, False))

                # 如果没有找到任何内容，回退到简单提取
                if not segments:
                    text = td_cell.get_text()
                    text = text.replace('&nbsp;', ' ').replace('\xa0', ' ')
                    if text.strip():
                        segments.append((text, False))

                return segments

            def create_rich_text_cell(offset, content_segments):
                """创建富文本单元格内容"""
                if not offset and not content_segments:
                    return ""

                # 检查是否有差异
                has_diff = any(is_diff for _, is_diff in content_segments)

                # 构建完整文本
                full_text = ""
                if offset:
                    full_text += offset + "        "  # 8个空格

                for text, _ in content_segments:
                    full_text += text

                # 如果没有差异，返回黑色富文本
                if not has_diff:
                    try:
                        rich_parts = []

                        # 创建黑色字体
                        normal_inline_font = InlineFont(rFont="Consolas", sz=10, color="000000")

                        # 添加偏移量（黑色）
                        if offset:
                            rich_parts.append(TextBlock(normal_inline_font, offset + "        "))

                        # 添加内容部分（黑色）
                        for text, _ in content_segments:
                            if text.strip():  # 忽略纯空格片段
                                rich_parts.append(TextBlock(normal_inline_font, text))
                            else:
                                # 如果是纯空格，合并到前一个片段
                                if rich_parts:
                                    rich_parts[-1].text += text

                        return CellRichText(rich_parts)
                    except Exception as e:
                        logger.warning(f"富文本创建失败，使用普通文本: {e}")
                        traceback.print_exc()
                        return full_text

                # 有差异，创建富文本
                try:
                    rich_parts = []

                    # 创建InlineFont对象
                    normal_inline_font = InlineFont(rFont="Consolas", sz=10, color="000000")
                    red_inline_font = InlineFont(rFont="Consolas", sz=10, color="FF0000")

                    # 添加偏移量（黑色）
                    if offset:
                        rich_parts.append(TextBlock(normal_inline_font, offset + "        "))

                    # 添加内容部分
                    for text, is_diff in content_segments:
                        if text.strip():  # 忽略纯空格片段
                            if is_diff:
                                rich_parts.append(TextBlock(red_inline_font, text))
                            else:
                                rich_parts.append(TextBlock(normal_inline_font, text))
                        else:
                            # 如果是纯空格，合并到前一个片段
                            if rich_parts:
                                rich_parts[-1].text += text

                    return CellRichText(rich_parts)

                except Exception as e:
                    logger.warning(f"富文本创建失败，使用普通文本: {e}")
                    traceback.print_exc()
                    return full_text

            # 查找所有表格行
            rows = table.find_all('tr')
            excel_row = check_insert_row
            processed_count = 0

            offset = 1 if ws.title in spec_compare_excel_sheet_list else 0

            max_row = 20000
            max_column = 1

            # 从最后一行向上遍历
            for row in range(max_row, 0, -1):  # 从 max_row 到 1（向上遍历）
                # 检查当前行是否有任何非空单元格
                if any(ws.cell(row=row, column=col).value is not None for col in range(1, max_column + 1)):
                    excel_row = row + 10  # 返回行号
                    logger.info(f"# 从最后一行向上遍历找到行 {row}，返回行号 {excel_row}")
                    break

            # 使用正则表达式匹配“左边文件”和“右边文件”
            left_pattern = r"左边文件：\s*(.+?)\s*;"
            right_pattern = r"右边文件：\s*(.+?)\s*;"

            # 提取左边文件路径
            left_match = re.search(left_pattern, html_content)
            left_file = left_match.group(1) if left_match else None

            # 提取右边文件路径
            right_match = re.search(right_pattern, html_content)
            right_file = right_match.group(1) if right_match else None

            cell = ws.cell(row=excel_row - 3, column=1 + offset)
            cell.value = f"模式: 仅显示差异 "
            cell = ws.cell(row=excel_row - 2, column=1 + offset)
            cell.value = f"左边文件:{left_file.replace('&nbsp', ' ')}"
            cell = ws.cell(row=excel_row - 1, column=1 + offset)
            cell.value = f"右边文件:{right_file.replace('&nbsp', ' ')}"

            cell = ws.cell(row=excel_row - 1, column=4 + offset * 2)
            cell.value = f"确认结果"
            # 设置字体加粗
            cell.font = Font(bold=True)
            # 设置单元格背景颜色为黄色
            cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

            cell = ws.cell(row=excel_row - 1, column=5 + offset * 2)
            cell.value = f"备注"
            # 设置字体加粗
            cell.font = Font(bold=True)
            # 设置单元格背景颜色为黄色
            cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

            for row in rows:
                cells = row.find_all('td')
                if len(cells) != 5:
                    continue

                # 解析5列数据
                file1_offset_cell = cells[0]
                file1_content_cell = cells[1]
                compare_cell = cells[2]
                file2_offset_cell = cells[3]
                file2_content_cell = cells[4]

                # 提取偏移地址
                file1_offset = file1_offset_cell.get_text(strip=True)
                file2_offset = file2_offset_cell.get_text(strip=True)
                compare_symbol = compare_cell.get_text(strip=True)

                # 跳过完全空的行
                if not file1_offset and not file2_offset:
                    continue

                # 提取内容段
                file1_segments = extract_content_with_diff_info(file1_content_cell)
                file2_segments = extract_content_with_diff_info(file2_content_cell)

                # A列: 文件1内容
                cell = ws.cell(row=excel_row, column=1 + offset)
                cell.value = create_rich_text_cell(file1_offset, file1_segments)
                cell.alignment = left_align
                cell.border = border

                # B列: 比较符号
                cell = ws.cell(row=excel_row, column=2 + offset, value=compare_symbol)
                cell.font = normal_font
                cell.alignment = center_align
                cell.border = border
                if offset > 0:
                    cell = ws.cell(row=excel_row, column=3 + offset, value=compare_symbol)
                    cell.border = border

                # C列: 文件2内容
                cell = ws.cell(row=excel_row, column=3 + offset * 2)
                cell.value = create_rich_text_cell(file2_offset, file2_segments)
                cell.alignment = left_align
                cell.border = border

                excel_row += 1
                processed_count += 1

            # # 设置列宽
            # ws.column_dimensions['A'].width = 80
            # ws.column_dimensions['B'].width = 8
            # ws.column_dimensions['C'].width = 80

            # 添加统计信息
            stats_row = excel_row + 2
            ws.cell(row=stats_row, column=1, value="统计信息:").font = Font(bold=True)
            ws.cell(row=stats_row + 1, column=1, value=f"总行数: {processed_count}")
            ws.cell(row=stats_row + 2, column=1, value="说明: 差异字节标红显示")

            logger.info(f"✅ Excel报告生成成功: ")
            logger.debug(f"📊 共处理 {processed_count} 行数据")
            logger.debug("🔴 差异字节已标红显示")
            return True

        except Exception as e:
            logger.error(f"❌ 解析HTML失败: {e}")
            traceback.print_exc()
            return False

    def parse_bc_html_to_excel_v2(self, html_report, excel_sheet, check_insert_row):
        self.parse_bc_html_with_byte_diff(html_report, excel_sheet, check_insert_row)
        return True

    def compare_bin_files_bc_to_excel(self, file1, file2, excel_sheet, check_insert_row):
        """使用Beyond Compare生成报告然后转换为Excel"""
        logger.info(f"开始对比文件:\n  文件1: {file1}\n  文件2: {file2}")

        try:
            # 先生成HTML报告
            html_report = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\bc_report.html"

            logger.info("步骤1: 使用Beyond Compare生成HTML报告...")
            bc_success = self.generate_bc_hex_report_v2(file1, file2, html_report)

            logger.info("步骤2: 解析HTML报告并转换为Excel...")
            excel_success = self.parse_bc_html_to_excel_v2(html_report, excel_sheet, check_insert_row)

            if excel_success:
                # 清理临时HTML文件
                logger.info(f"🎉 对比完成！报告保存在: {html_report}")
                return True
            else:
                logger.error("❌ 转换失败")
                return False

        except Exception as e:
            logger.error(f"❌ 生成报告时出错: {e}")
            traceback.print_exc()
            return False

    def compare_bin_files_to_excel(self, file1, file2, excel_sheet, check_insert_row):
        """完整的bin文件对比流程 - 优先使用BC方法"""
        logger.info(f"开始对比文件:\n  文件1: {file1}\n  文件2: {file2}")
        bc_path = self.beyond_compare_tool_path
        # 优先尝试BC方法
        if bc_path and os.path.exists(bc_path):
            logger.info("使用Beyond Compare方法...")
            return self.compare_bin_files_bc_to_excel(file1, file2, excel_sheet, check_insert_row)
        else:
            logger.error("使用Beyond Compare对比文件失败")
            return False

    def insert_bin_compare_pic(self, bin_cehck_list, excel_file_sheet, check_insert_row):
        for bin_file in bin_cehck_list:
            try:
                pre_path_prefix = os.path.normpath(self.pre_package_save_path)
                after_path_prefix = os.path.normpath(self.after_package_save_path)
                file_before = os.path.join(pre_path_prefix, os.path.normpath(bin_file))
                file_after = os.path.join(after_path_prefix, os.path.normpath(bin_file))
                # 检查文件是否存在
                if os.path.exists(file_before) and os.path.exists(file_after):
                    # 如果两个文件都存在，调用函数进行比较
                    self.compare_bin_files_to_excel(file_before, file_after, excel_file_sheet, check_insert_row)
                else:
                    # 如果文件不存在，记录日志或抛出异常
                    missing_files = []
                    if not os.path.exists(file_before):
                        missing_files.append(bin_file)
            except Exception as e:
                traceback.print_exc()
                raise e

        return


def compare_bin_packages_service(pack_compare_info: PackageFileCompareInfo):
    """
    API接口：对比FTP软件包文件
    """
    try:
        ssh_hostname = config_manager_dev_env_check.config['ssh_config']['hostname']
        ssh_username = config_manager_dev_env_check.config['ssh_config']['username']
        ssh_password = config_manager_dev_env_check.config['ssh_config']['password']
        ssh_port = config_manager_dev_env_check.config['ssh_config']['port']
        ssh_timeout = config_manager_dev_env_check.config['ssh_config']['timeout']

        ssh_config = SSHConfig(
            hostname=ssh_hostname,
            username=ssh_username,
            password=ssh_password,
            port=ssh_port,
            timeout=ssh_timeout,
        )

        ftp_host = config_manager_dev_env_check.config['ftp_config']['host']
        ftp_user = config_manager_dev_env_check.config['ftp_config']['user']
        ftp_password = config_manager_dev_env_check.config['ftp_config']['password']
        ftp_port = config_manager_dev_env_check.config['ftp_config']['port']

        # 创建FTP下载工具
        ftp_util = FTPDownloadUtil(
            ssh_config=ssh_config,
            ftp_host=ftp_host,
            ftp_user=ftp_user,
            ftp_password=ftp_password,
            ftp_port=ftp_port,
        )

        # 执行对比流程
        with PackageFilesCompareUtil(ssh_config, ftp_util) as packet_util:
            # 下载并解析所有包文件
            packet_util.download_and_parse_all_package_files(pack_compare_info.pre_pack_path,
                                                             pack_compare_info.after_pack_path)

            # 对比本地文件
            packet_util.compare_local_bin_file(
                packet_util.pre_package_md5_path,
                packet_util.after_package_md5_path
            )

            # 写入对比结果到Excel
            output_save_path = packet_util.get_EMMC_compare_book_path()
            packet_util.write_compare_result_to_file(output_save_path)
            logger.success(f"软件包对比结果已保存到Excel文件: {packet_util.out_compare_excel_file}")
            return packet_util.out_compare_excel_file

    except Exception as e:
        logger.error(f"软件包对比失败: {str(e)}")
        return {
            "success": False,
            "message": f"软件包对比失败: {str(e)}",
            "data": None
        }


if __name__ == "__main__":
    # 使用示例
    ssh_config = SSHConfig(
        hostname="************",
        username="dnkt",
        password="dnkt$88"
    )

    ftp_util = FTPDownloadUtil(
        ssh_config=ssh_config,
        ftp_host="************",
        ftp_user="release",
        ftp_password="release",
    )

    pre_pack_info = PackageFileInfo(
        name="pre_package.zip",
        path="/Orca3/263D/release-build/20230214164344-v3.0.1_3/263D-v301_3-20230214.zip",
        size=1024000,
        modified_time="2024-01-15 10:30:00"
    )

    after_pack_info = PackageFileInfo(
        name="after_package.zip",
        path="/Orca3/263D/release-build/20230106210042-V2.1.0/263D_V2.1.0_release_2_20230106210520.zip",
        size=1048576,
        modified_time="2024-01-20 14:45:00"
    )

    pre_package_local_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\pre_package_save_path\result_md5"
    after_package_local_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\after_package_save_path\result_md5"
    compare_excel_file = r"C:\Users\<USER>\Desktop\work\xhcode\DEV_Agent\src\sdw_agent\service\dev_env_check\book_data\Bin_Compare_EMMC_template.xlsx"

    # 示例1：下载软件包文件
    with PackageFilesCompareUtil(ssh_config, ftp_util) as packet_util:
        #     # 下载所有文件
        # packet_util.download_and_parse_all_package_files(pre_pack_info, after_pack_info)
        packet_util.compare_local_bin_file(pre_package_local_path, after_package_local_path)
        packet_util.write_compare_result_to_file(compare_excel_file)
        # a = packet_util.select_shell_script("/Panda/525D/nightly-build/")
        # print(a)
