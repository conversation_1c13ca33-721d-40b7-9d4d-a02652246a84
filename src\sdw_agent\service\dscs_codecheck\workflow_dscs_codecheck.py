"""
設計基準CS(コードチェック)工作流

V字对应：
3.3 設計基準CS(コードチェック)

该模块提供新变表更新功能，检查代码中新变表的更新内容。
1. 从本地Git仓库获取变更信息
2. 解析变更信息，将提交输出给大模型
3. 输出設計基準CS(コードチェック)
"""
# 修正路径计算 - 需要向上追溯4层才能到达项目根目录
#import sys
#from pathlib import Path
#project_root = Path(__file__).parent.parent.parent.parent
#sys.path.insert(0, str(project_root))  # 使用insert(0)确保优先搜索

from pathlib import Path
from typing import Dict, List, Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.config.env import ENV

from sdw_agent.service.dscs_codecheck.models import DscsCodeCheckInput, DscsCodeCheckResult, GitCommitChange
from sdw_agent.service.dscs_codecheck.util.git_util import get_commit_changes_between
from sdw_agent.service.dscs_codecheck.util.code_analysis_util import run_static_analysis,generate_report


@register_workflow("dscs_codecheck")
class DscsCodeCheckWorkflow(BaseWorkflow):
    """設計基準CS(コードチェック)工作流"""
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """注册配置模型"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="dscs_codecheck")

    def validate_input(self, input_data: DscsCodeCheckInput) -> bool:
        """验证输入参数"""
        try:
            # 验证代码仓库路径
            repo_path = Path(input_data.repo_path)
            if not repo_path.exists():
                self.logger.error(f"代码仓库不存在: {input_data.repo_path}")
                return False
                
            # 验证commit id格式
            if not input_data.commit_id or len(input_data.commit_id) != 40:
                self.logger.error(f"无效的commit id: {input_data.commit_id}")
                return False
            
            # 验证commit id_base格式
            if not input_data.commit_id_base or len(input_data.commit_id_base) != 40:
                self.logger.error(f"无效的commit id_base: {input_data.commit_id_base}")
                return False
                
            return True
        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, input_data: DscsCodeCheckInput) -> WorkflowResult:
        """执行工作流"""
        self.logger.info("开始执行DSCS代码检查工作流")
        code_changes: List[GitCommitChange] = []
        try:
            # 1. 获取代码变更
            code_changes = get_commit_changes_between(
                repo_path=input_data.repo_path,
                start_commit=input_data.commit_id_base,
                end_commit=input_data.commit_id
            )
            
            # 2. 执行静态分析
            analysis_results = run_static_analysis(
                code_changes=code_changes,
                config=self.config
            )
            
            # 3. 生成报告
            report_path = generate_report(
                all_results=analysis_results,
            )
            
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="DSCS代码检查工作流执行成功",
                data={"report_paths": report_path}  # 将列表包装成字典
            )
        except Exception as e:
            self.logger.exception("DSCS代码检查工作流执行失败")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"DSCS代码检查工作流执行失败: {str(e)}",
                error=str(e)
            )
        
        
def do_dscs_codecheck(repo_path: str, commit_id: str, commit_id_base: str) -> str:
    """执行DSCS代码检查"""
    workflow = DscsCodeCheckWorkflow()
    result = workflow.run(DscsCodeCheckInput(
        repo_path=repo_path,
        commit_id=commit_id,
        commit_id_base=commit_id_base
    ))
    
    if result.status == WorkflowStatus.SUCCESS:
        return result.data["report_paths"]  # 从字典中取出报告路径
    raise Exception(result.message)


if __name__ == "__main__":
    FILE_PATH = 'D:/00.19PF-V3/04.Code/cr7_app_global'
    COMMIT_ID = '711ba8c1c5cf0aaa22a9b6d896216014391e8740'
    COMMIT_ID_BASE = '711ba8c1c5cf0aaa22a9b6d896216014391e8740'

    results = do_dscs_codecheck(FILE_PATH, COMMIT_ID, COMMIT_ID_BASE)
