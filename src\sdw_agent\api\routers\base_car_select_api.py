"""
@File    : base_car_select_api.py
@Time    : 2025/7/28 17:27
<AUTHOR> qiliang.zhou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 base 车辆选定
@Desc    : API 接口定义
"""


from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from loguru import logger

from sdw_agent.model.request_model import SourceInfo
from sdw_agent.service import WorkflowStatus
from sdw_agent.service.base_car_select.model import InputDataModel
from sdw_agent.service.base_car_select.utils.car_info_resolver import CarInfoResolver
from sdw_agent.service.base_car_select.workflow import BaseCarSelectWorkflow

router = APIRouter(prefix="/api/sdw/base_select", tags=["RAM设计书作成"])


class BaseCarSelectRequest(BaseModel):
    base_db: SourceInfo
    new_db: SourceInfo
    new_car_name: str = Field("", description="版本信息$车型编号")
    base_car_name: Optional[str] = Field("", description="版本信息$车型编号")

class BaseCarSelectResponse(BaseModel):
    code: int = Field(0, description="状态码，0表示成功，其他表示失败")
    msg: str = Field("", description="响应消息")
    data: Dict[str, str|None] = Field(default_factory=dict, description="响应数据")

class ResolveCarRequest(BaseModel):
    file_db: SourceInfo

class CarInfo(BaseModel):
    code: int = Field(0, description="状态码，0表示成功，其他表示失败")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any|None] = Field(default_factory=dict, description="响应数据")


@router.post(
    path="/resolve_car",
    summary="从上传的标准式样DB中解析出所有车辆信息",
    response_model=CarInfo
)
async def resolve_base_car(request: ResolveCarRequest):
    try:
        file_path = request.file_db.uri
        car_info_resolver = CarInfoResolver(db_path=file_path)
        res = car_info_resolver.resolve_car_info()

        return {
            "code": 0,
            "msg": "车型信息",
            "data": res
        }
    except Exception as e:
        logger.error(f"车型信息解析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    path="/standard_db",
    summary="根据目次差分结果选定base 车辆",
    response_model=BaseCarSelectResponse
)
async def select_base_car(request: BaseCarSelectRequest):
    try:
        base_db = request.base_db.uri
        new_db = request.new_db.uri
        new_car_name = request.new_car_name
        if request.base_car_name:
            base_car_name = request.base_car_name
            input_data = InputDataModel(
                base_db=base_db,
                new_db=new_db,
                new_car_name=new_car_name,
                base_car_name=base_car_name
            )
        else:
            input_data = InputDataModel(
                base_db=base_db,
                new_db=new_db,
                new_car_name=new_car_name
            )
        # 执行工作流
        logger.info("开始执行base车辆选定工作流")
        workflow = BaseCarSelectWorkflow()
        result = workflow.run(input_data)

        # 处理结果
        if result.status == WorkflowStatus.SUCCESS:
            output_data = result.data['output_path']
            logger.success("Base车辆选定工作流执行成功")
            return {
                "code": 0,
                "msg": "Base车辆选定工作流执行成功",
                "data": {
                    "keyList": output_data
                }
            }
        else:
            logger.error(f"Base车辆选定工作流执行失败: {result.error}")
            return {
                "code": 500,
                "msg": "Base车辆选定工作流执行失败",
                "data": {
                    "keyList": ""
                }
            }
    except Exception as e:
        logger.error(f"Base车辆选定工作流执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
