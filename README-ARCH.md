# 汽车软件开发AI辅助工作流自动化系统

基于V字开发流程的汽车软件开发AI辅助工作流自动化系统，能够通过自然语言理解用户需求，自动规划和执行开发步骤。

## 系统架构

系统架构包括五层：

1. **用户接口层**：处理用户输入和结果展示
2. **任务规划层**：解析用户需求，规划执行步骤
3. **工作流引擎层**：管理和调度工作流执行
4. **步骤执行层**：执行具体的开发和测试步骤
5. **数据存储层**：存储配置、中间结果和输出

核心组件：
- 基础抽象类(BaseStep、AgentStep、ToolStep)
- 工作流引擎(WorkflowEngine、WorkflowScheduler)
- 自然语言处理器(NLPQueryParser、TaskPlanner)
- LLM交互模块(LLMManager、LLMClient)
- 提示词管理模块(PromptManager、AgentPromptBuilder)

## 主要功能

系统支持25个预定义步骤，分为DEV阶段(19个)和TEST阶段(6个)，每个步骤可以是AI实现(Agent)或工具调用(Tool)。

### DEV阶段步骤

1. 要求规格读取 (Agent)
2. 法规确认 (Agent)
3. 软件设计标准CS（需求分析）(Agent)
4. 功能一览与变更内容制作 (Tool)
5. 软件设计标准CS（基本设计）(Agent)
6. 通信故障安全CS (Agent)
7. 软件设计书制作 (Tool)
8. I/F整合性确认 (Agent)
9. CAN入出力一览确认 (Agent)
10. 函数式样书制作 (Tool)
11. RAM设计书制作 (Tool)
12. 软件设计标准CS（详细设计）(Agent)
13. 设计同行评审 (Agent)
14. 功能一览与变更内容更新 (Tool)
15. 编码标准确认&结果验证 (Agent)
16. 文件比较实施 (Agent)
17. 自我检查与代码审查视角CS (Tool)
18. 代码审查 (Agent)
19. 软件设计标准CS（代码检查）(Agent)

### TEST阶段步骤

1. 检查表IV确认和结果验证 (Agent)
2. 结合检查规格书审查 (Agent)
3. 检查表V确认和结果验证 (Agent)
4. 软件设计标准CS（耦合检查）(Agent)
5. 通信检查与结果验证 (Agent)
6. 真实设备评估与结果验证 (Tool)

## LLM和提示词功能

系统集成了大语言模型(LLM)功能，用于支持Agent步骤的智能分析和生成能力：

### LLM模块

- **LLMConfig**: LLM配置类，支持多种参数设置
- **LLMClient**: LLM客户端，处理API调用
- **LLMManager**: LLM管理器，支持多客户端管理
- **LLMMessage**: 消息类，用于构建提示

支持的LLM提供商：
- OpenAI (GPT-4, GPT-3.5)
- Azure OpenAI Service
- 可扩展支持其他提供商

### 提示词模块

- **PromptTemplate**: 提示词模板，支持变量渲染
- **PromptManager**: 提示词管理器，管理多个模板
- **AgentPromptBuilder**: Agent提示词构建器，针对不同任务构建提示

提示词模板支持：
- 基于Jinja2的模板渲染
- 从文件或目录加载模板
- 针对不同Agent类型和任务的专用模板

## 安装与使用

### 依赖安装

```bash
pip install -r requirements.txt
```

### 配置

1. 复制`.env.example`为`.env`并填写必要的API密钥
2. 根据需要修改`config/system.yaml`和`config/workflows.yaml`

### 基本使用

```python
from src.core import WorkflowEngine
from src.core.nlp_planner import TaskPlanner

# 创建工作流引擎
engine = WorkflowEngine(config, workflows)

# 方法1：直接执行工作流
result = engine.execute_workflow("dev_workflow", input_data)

# 方法2：通过自然语言查询
planner = TaskPlanner(config, workflows)
plan = planner.plan_from_query("分析新的刹车控制需求并生成设计文档")
result = engine.execute_plan(plan)
```

### LLM和提示词使用

```python
from src.core.llm import LLMManager, LLMMessage
from src.core.prompt import PromptManager

# 创建LLM管理器
llm_manager = LLMManager.from_config(config["llm"])

# 创建提示词管理器
prompt_manager = PromptManager("./prompts")

# 使用提示词模板
prompt = prompt_manager.render_template(
    "requirement_analysis",
    requirement_text="实现一个基于摄像头的自动泊车系统"
)

# 调用LLM
messages = [
    LLMMessage(role="system", content="你是一个汽车软件专家"),
    LLMMessage(role="user", content=prompt)
]
response = llm_manager.call(messages)
```

## 系统特点

- **模块化设计**：各组件高度解耦，易于扩展
- **AI与工具集成**：结合AI分析能力和自动化工具
- **自然语言交互**：通过自然语言指令控制系统
- **并行执行能力**：支持步骤的并行执行
- **可扩展性**：易于添加新的步骤和工作流

## 许可证

MIT

## 项目结构

```
.
├── src/                    # 源代码目录
│   ├── core/              # 核心组件
│   │   ├── base.py        # 基础类定义
│   │   ├── workflow.py    # 工作流引擎
│   │   └── nlp_planner.py # NLP和任务规划
│   └── steps/             # 步骤实现
│       ├── dev_steps.py   # DEV阶段步骤
│       └── test_steps.py  # TEST阶段步骤
├── config/                # 配置文件
│   ├── system.yaml       # 系统配置
│   └── workflows.yaml    # 工作流定义
├── tests/                 # 单元测试
│   ├── test_workflow.py  # 工作流引擎测试
│   └── test_nlp_planner.py # NLP规划器测试
├── examples/              # 使用示例
│   └── example_usage.py  # 示例脚本
├── main.py               # 主程序入口
├── requirements.txt      # Python依赖
├── ARCHITECTURE.md       # 架构文档
└── README.md            # 本文档
```

## 常见问题

### Q: 如何配置AI模型？

A: 在 `config/system.yaml` 的 `agents` 部分配置API端点和密钥。

### Q: 如何调整并行执行的性能？

A: 修改 `config/system.yaml` 中的 `max_parallel_workers` 参数。

### Q: 如何添加新的自然语言模式？

A: 在 `src/core/nlp_planner.py` 的 `intent_patterns` 中添加新模式。

## 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

## 联系方式

- 项目维护者：[Your Name]
- Email: <EMAIL>
- 项目主页：https://github.com/your-org/auto-workflow-system

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础工作流引擎
- 支持25个预定义步骤
- 集成自然语言处理能力 