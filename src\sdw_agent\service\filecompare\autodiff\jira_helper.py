import jira

class JiraHelper:
    def __init__(self) -> None:
        self.jira = jira.JIRA(server="https://aip01.dndev.net/jira", 
        basic_auth=("dnkt_yanglin_xiao", "dnkt@11"),  # a username/password tuple [Not recommended]
        # basic_auth=("email", "API token"),  # Jira Cloud: a username/token tuple
        # token_auth="API token",  # Self-Hosted Jira (e.g. Server): the PAT token
        # auth=("admin", "admin"),  # a username/password tuple for cookie auth [Not recommended]
        )
    
    def get_epic(self, id) -> jira.Issue:
        issue = self.jira.issue(id)
        epic = issue.get_field("customfield_10006")
        if (epic) :
            epic = self.jira.issue(epic)            
        else:
            epic = None
            
        return epic
     
    def get_ar(self, epic:jira.Issue) -> list[jira.Issue]:
        ar_list = []        
        for link in epic.fields.issuelinks:
            issue = link.outwardIssue
            # AR 13200
            if issue.fields.issuetype.name == 'AR':
                ar_list.append(issue)
        
        return ar_list    