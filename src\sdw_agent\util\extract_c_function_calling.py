import re
from pathlib import Path
from loguru import logger
from typing import List, Set, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum


class ReferenceType(Enum):
    """引用类型枚举"""
    DIRECT_CALL = "direct_call"
    POINTER_ASSIGNMENT = "pointer_assignment"
    ARRAY_INITIALIZATION = "array_initialization"
    MACRO_DEFINITION = "macro_definition"


@dataclass
class FunctionReference:
    """函数引用信息"""
    caller_name: str
    reference_type: ReferenceType
    file_path: str
    line_number: int = 0
    is_global: bool = False

    def __str__(self) -> str:
        scope = "全局" if self.is_global else "函数内"
        return f"{self.caller_name} ({self.reference_type.value}, {scope})"


@dataclass
class FunctionInfo:
    """函数信息"""
    name: str
    start_pos: int
    end_pos: int
    body_start: int


class CFunctionCallAnalyzer:
    """
    C语言函数调用分析器
    
    提供C代码中函数调用关系的分析功能，包括：
    1. 直接函数调用分析
    2. 间接引用分析（函数指针、数组、宏定义等）
    3. 多种编码格式文件读取支持
    4. 详细的调试信息输出
    5. 支持多函数批量分析
    """

    # 默认C语言关键字
    DEFAULT_C_KEYWORDS = {'if', 'while', 'for', 'switch', 'return', 'sizeof', 'typedef', 'struct', 'union', 'enum'}

    # 默认文件编码尝试顺序
    DEFAULT_ENCODINGS = ['utf-8', 'latin-1', 'Windows-1252', 'gbk', 'gb2312']

    # 默认文件扩展名
    DEFAULT_FILE_EXTENSIONS = (".c", ".h", ".prm")

    # 默认头文件保护宏后缀
    DEFAULT_HEADER_GUARD_SUFFIXES = ['_H', '_PRM', '_VERSION', '_MAJOR', '_MINOR', '_PATCH']

    def __init__(self,
                 root_path: str,
                 target_function: Union[str, List[str]] = None,
                 debug: bool = False,
                 file_extensions: Tuple[str, ...] = None,
                 encodings: List[str] = None,
                 c_keywords: Set[str] = None,
                 header_guard_suffixes: List[str] = None):
        """
        初始化分析器
        
        Args:
            root_path: 搜索路径
            target_function: 目标函数名（单个字符串或字符串列表）
            debug: 是否开启调试模式
            file_extensions: 要搜索的文件扩展名
            encodings: 文件编码尝试顺序
            c_keywords: C语言关键字集合
            header_guard_suffixes: 头文件保护宏后缀列表
        """
        self._references = None
        self.target_function = target_function
        self.root_path = Path(root_path)

        # 处理单个函数或多个函数
        if isinstance(target_function, str):
            self.target_functions = [target_function]
        elif isinstance(target_function, list):
            self.target_functions = target_function
        else:
            self.target_functions = []

        self.debug = debug
        self.file_extensions = file_extensions or self.DEFAULT_FILE_EXTENSIONS
        self.encodings = encodings or self.DEFAULT_ENCODINGS
        self.c_keywords = c_keywords or self.DEFAULT_C_KEYWORDS
        self.header_guard_suffixes = header_guard_suffixes or self.DEFAULT_HEADER_GUARD_SUFFIXES

        self._processed_files = 0
        self._multi_references = {}  # 多函数分析结果缓存 {function_name: [references]}
        self._compile_patterns()

    def _compile_patterns(self) -> None:
        """预编译常用的正则表达式模式"""
        # 函数定义模式
        self._func_def_pattern = re.compile(
            r'(?:^|\n)\s*'
            r'(?:static\s+|extern\s+|inline\s+)*'
            r'(?:const\s+|volatile\s+)*'
            r'(?:unsigned\s+|signed\s+)?'
            r'(?:struct\s+|union\s+|enum\s+)?\w+'
            r'(?:\s*\*)*'
            r'(?:\s+const)?'
            r'\s+'
            r'(\w+)'
            r'\s*\('
            r'[^)]*'
            r'\)\s*'
            r'(?:\{|$)',
            re.MULTILINE
        )

        # 单行注释模式
        self._single_comment_pattern = re.compile(r'//.*?$', re.MULTILINE)

        # 多行注释模式
        self._multi_comment_pattern = re.compile(r'/\*.*?\*/', re.DOTALL)

    def analyze(self) -> List[FunctionReference]:
        """
        执行函数调用分析
        
        Returns:
            函数引用信息列表
        """
        if self._references is not None:
            return self._references

        references = []
        self._processed_files = 0

        if self.debug:
            logger.info(f"开始分析函数 {self.target_function} 的调用关系")
            logger.info(f"搜索路径: {self.root_path}")
            logger.info(f"搜索文件扩展名: {self.file_extensions}")

        for file_path in self._get_source_files():
            try:
                content = self._read_file_safely(file_path)
                if not content or self.target_function not in content:
                    continue

                if self.debug:
                    logger.info(f"在文件 {file_path} 中发现目标函数 {self.target_function}")

                file_references = self._analyze_file(content, self.target_function, str(file_path))
                references.extend(file_references)

            except Exception as e:
                if self.debug:
                    logger.warning(f"处理文件 {file_path} 时出错: {e}")

        self._references = references

        if self.debug:
            self._log_analysis_summary()

        return references

    def get_caller_names(self) -> List[str]:
        """
        获取所有调用者函数名
        
        Returns:
            调用者函数名列表
        """
        if self._references is None:
            self.analyze()

        return [ref.caller_name for ref in self._references]

    def get_references(self) -> List[FunctionReference]:
        """
        获取详细的引用信息
        
        Returns:
            函数引用信息列表
        """
        if self._references is None:
            self.analyze()

        return self._references

    def get_references_by_type(self, reference_type: ReferenceType) -> List[FunctionReference]:
        """
        按引用类型获取引用信息
        
        Args:
            reference_type: 引用类型
            
        Returns:
            指定类型的函数引用信息列表
        """
        if self._references is None:
            self.analyze()

        return [ref for ref in self._references if ref.reference_type == reference_type]

    def get_global_references(self) -> List[FunctionReference]:
        """
        获取全局作用域的引用
        
        Returns:
            全局作用域的函数引用信息列表
        """
        if self._references is None:
            self.analyze()

        return [ref for ref in self._references if ref.is_global]

    def get_function_references(self) -> List[FunctionReference]:
        """
        获取函数作用域的引用
        
        Returns:
            函数作用域的函数引用信息列表
        """
        if self._references is None:
            self.analyze()

        return [ref for ref in self._references if not ref.is_global]

    def _get_source_files(self) -> List[Path]:
        """获取源代码文件列表"""
        files = []
        for file_path in self.root_path.rglob("*"):
            if file_path.suffix.lower() in [ext.lower() for ext in self.file_extensions]:
                files.append(file_path)
                self._processed_files += 1
        return files

    def _analyze_file(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """分析单个文件中的函数引用"""
        references = []

        # 分析直接调用
        direct_calls = self._find_direct_calls(content, target_function, file_path)
        references.extend(direct_calls)

        # 分析间接引用
        indirect_refs = self._find_indirect_references(content, target_function, file_path)
        references.extend(indirect_refs)

        return references

    def _find_direct_calls(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """查找直接函数调用"""
        references = []
        cleaned_content = self._remove_comments(content)

        # 获取所有函数定义
        functions = self._extract_functions(cleaned_content)

        # 函数调用模式
        call_pattern = re.compile(rf'\b{re.escape(target_function)}\s*\(')

        for func_info in functions:
            if func_info.name == target_function or func_info.name in self.c_keywords:
                continue

            func_body = cleaned_content[func_info.body_start:func_info.end_pos + 1]

            if call_pattern.search(func_body) and not self._is_in_string_literal(func_body, call_pattern):
                references.append(FunctionReference(
                    caller_name=func_info.name,
                    reference_type=ReferenceType.DIRECT_CALL,
                    file_path=file_path
                ))

                if self.debug:
                    logger.info(f"发现直接调用: {func_info.name} -> {target_function} in {file_path}")

        return references

    def _find_indirect_references(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """查找间接引用"""
        references = []

        # 函数指针赋值
        references.extend(self._find_pointer_assignments(content, target_function, file_path))

        # 指针数组
        references.extend(self._find_pointer_array(content, target_function, file_path))

        # 数组初始化
        references.extend(self._find_array_initializations(content, target_function, file_path))

        # 宏定义
        references.extend(self._find_macro_definitions(content, target_function, file_path))

        return references

    def _find_pointer_assignments(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """查找函数指针赋值"""
        references = []
        pattern = re.compile(rf'(\w+)\s*=\s*&?\s*{re.escape(target_function)}\b(?!\w)')

        for match in pattern.finditer(content):
            var_name = match.group(1)
            containing_function = self._find_containing_function(content, match.start())

            if containing_function:
                caller_name = containing_function
                is_global = False
            else:
                caller_name = var_name
                is_global = True

            references.append(FunctionReference(
                caller_name=caller_name,
                reference_type=ReferenceType.POINTER_ASSIGNMENT,
                file_path=file_path,
                is_global=is_global
            ))

            if self.debug:
                scope = "全局" if is_global else f"函数{containing_function}"
                logger.info(f"发现指针赋值: {scope}中 {var_name} = {target_function}")

        return references

    def _find_pointer_array(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """查找指针数组中的函数引用"""
        references = []

        # 函数指针数组模式
        patterns = [
            # 复杂函数指针数组
            re.compile(
                r'(?:static\s+|extern\s+)?(?:const\s+)?\w+\s*'
                r'\(\s*\*\s*(?:const\s+)?\*?\s*(?:const\s+)?(\w+)'
                r'\s*\[\s*[^]]*\s*]\s*\)\s*\([^)]*\)'
                r'\s*=\s*\{[^}]*?'
                rf'&?\s*{re.escape(target_function)}\b'
                r'[^}]*?}',
                re.DOTALL
            )
        ]

        for pattern in patterns:
            for match in pattern.finditer(content):
                array_name = match.group(1)
                containing_function = self._find_containing_function(content, match.start())

                if containing_function:
                    caller_name = containing_function
                    is_global = False
                else:
                    caller_name = array_name
                    is_global = True

                references.append(FunctionReference(
                    caller_name=caller_name,
                    reference_type=ReferenceType.ARRAY_INITIALIZATION,
                    file_path=file_path,
                    is_global=is_global
                ))

                if self.debug:
                    scope = "全局" if is_global else f"函数{containing_function}"
                    logger.info(f"发现指针数组: {scope}中数组 {array_name} 包含 {target_function}")

        return references

    def _find_array_initializations(self, content: str, target_function, file_path: str) -> List[FunctionReference]:
        """查找数组初始化中的函数引用"""
        references = []
        # 预处理：移除注释
        code = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        code = re.sub(r'//.*?$', '', code, flags=re.MULTILINE)

        # 递归匹配嵌套花括号
        brace_pattern = r'(?:[^{}]|{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})*'

        # 完整数组匹配模式
        array_pattern = re.compile(
            r'(?:static\s+const\s+)?\w+(?:\s+\w+)*\s+'  # 类型声明
            r'(\w+)\s*\[\s*\w*\s*]\s*=\s*'  # 数组名捕获
            r'\{(' + brace_pattern + r')}',  # 嵌套内容
            re.DOTALL
        )
        for match in array_pattern.finditer(code):
            array_name = match.group(1)
            array_content = match.group(2)

            if re.search(r'&\s*' + re.escape(target_function) + r'\b', array_content):
                logger.info(array_name)
                containing_function = self._find_containing_function(content, match.start())

                if containing_function:
                    caller_name = containing_function
                    is_global = False
                else:
                    caller_name = array_name
                    is_global = True

                references.append(FunctionReference(
                    caller_name=caller_name,
                    reference_type=ReferenceType.ARRAY_INITIALIZATION,
                    file_path=file_path,
                    is_global=is_global
                ))

                if self.debug:
                    scope = "全局" if is_global else f"函数{containing_function}"
                    logger.info(f"发现数组初始化: {scope}中数组 {array_name} 包含 {target_function}")
        return references

    def _find_macro_definitions(self, content: str, target_function: str, file_path: str) -> List[FunctionReference]:
        """查找宏定义中的函数引用"""
        references = []
        pattern = re.compile(
            r'^\s*#define\s+(\w+)(?:\([^)]*\))?\s*.*?'
            rf'\b{re.escape(target_function)}\b.*?$',
            re.MULTILINE
        )

        for match in pattern.finditer(content):
            macro_name = match.group(1)

            # 过滤掉常见的头文件保护宏
            if self._is_header_guard_macro(macro_name):
                continue

            containing_function = self._find_containing_function(content, match.start())

            if containing_function:
                caller_name = containing_function
                is_global = False
            else:
                caller_name = macro_name
                is_global = True

            references.append(FunctionReference(
                caller_name=caller_name,
                reference_type=ReferenceType.MACRO_DEFINITION,
                file_path=file_path,
                is_global=is_global
            ))

            if self.debug:
                scope = "全局" if is_global else f"函数{containing_function}"
                logger.info(f"发现宏定义: {scope}中宏 {macro_name} 引用 {target_function}")

        return references

    def _extract_functions(self, content: str) -> List[FunctionInfo]:
        """提取所有函数定义信息"""
        functions = []

        for match in self._func_def_pattern.finditer(content):
            func_name = match.group(1)
            func_start = match.start()

            # 查找函数体开始位置
            brace_pos = content.find('{', match.end() - 10)
            if brace_pos == -1:
                continue

            # 查找函数体结束位置
            func_end = self._find_matching_brace(content, brace_pos)
            if func_end == -1:
                continue

            functions.append(FunctionInfo(
                name=func_name,
                start_pos=func_start,
                end_pos=func_end,
                body_start=brace_pos
            ))

        return functions

    def _remove_comments(self, content: str) -> str:
        """移除C代码中的注释"""
        # 移除单行注释
        content = self._single_comment_pattern.sub('', content)

        # 移除多行注释，但保留字符串中的内容
        result = []
        i = 0
        in_string = False
        in_char = False

        while i < len(content):
            if not in_string and not in_char:
                if i < len(content) - 1 and content[i:i + 2] == '/*':
                    end = content.find('*/', i + 2)
                    if end != -1:
                        i = end + 2
                        continue
                    else:
                        break
                elif content[i] == '"':
                    in_string = True
                elif content[i] == "'":
                    in_char = True
            else:
                if in_string and content[i] == '"' and (i == 0 or content[i - 1] != '\\'):
                    in_string = False
                elif in_char and content[i] == "'" and (i == 0 or content[i - 1] != '\\'):
                    in_char = False

            result.append(content[i])
            i += 1

        return ''.join(result)

    @staticmethod
    def _find_matching_brace(content: str, start_pos: int) -> int:
        """查找匹配的大括号"""
        if start_pos >= len(content) or content[start_pos] != '{':
            return -1

        brace_count = 1
        pos = start_pos + 1
        in_string = False
        in_char = False

        while pos < len(content) and brace_count > 0:
            char = content[pos]

            # 跳过转义字符
            if pos > 0 and content[pos - 1] == '\\':
                pos += 1
                continue

            if char == '"' and not in_char:
                in_string = not in_string
            elif char == "'" and not in_string:
                in_char = not in_char
            elif not in_string and not in_char:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1

            pos += 1

        return pos - 1 if brace_count == 0 else -1

    @staticmethod
    def _is_in_string_literal(func_body: str, call_pattern) -> bool:
        """检查函数调用是否在字符串字面量中"""
        for match in call_pattern.finditer(func_body):
            start = match.start()
            before = func_body[:start]
            quote_count = before.count('"') - before.count('\\"')

            if quote_count % 2 == 1:
                return True

        return False

    def _read_file_safely(self, file_path: Path) -> Optional[str]:
        """安全读取文件，尝试多种编码"""
        for encoding in self.encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue

        # 最后尝试二进制读取
        try:
            with open(file_path, 'rb') as f:
                return f.read().decode('utf-8', errors='replace')
        except Exception as e:
            if self.debug:
                logger.warning(f"无法读取文件 {file_path}: {e}")
            return None

    def _find_containing_function(self, content: str, position: int) -> Optional[str]:
        """查找指定位置所在的函数名"""
        functions = self._extract_functions(content)

        for func_info in functions:
            if func_info.start_pos <= position <= func_info.end_pos:
                return func_info.name

        return None

    def _is_header_guard_macro(self, macro_name: str) -> bool:
        """判断是否为头文件保护宏"""
        return any(macro_name.endswith(suffix) for suffix in self.header_guard_suffixes)

    def _log_analysis_summary(self) -> None:
        """输出分析结果摘要"""
        logger.info("=== 函数调用分析结果 ===")
        logger.info(f"目标函数: {self.target_function}")
        logger.info(f"搜索路径: {self.root_path}")
        logger.info(f"处理文件数: {self._processed_files}")
        logger.info(f"发现引用数: {len(self._references)}")

        # 按类型分组统计
        type_counts = {}
        for ref in self._references:
            ref_type = ref.reference_type.value
            type_counts[ref_type] = type_counts.get(ref_type, 0) + 1

        for ref_type, count in type_counts.items():
            logger.info(f"  {ref_type}: {count} 个")

        # 详细列表
        if self._references:
            logger.info("详细引用列表:")
            for ref in self._references:
                logger.info(f"  {ref}")

    def analyze_multiple(self, target_functions: List[str] = None) -> Dict[str, List[FunctionReference]]:
        """
        分析多个函数的调用关系

        Args:
            target_functions: 目标函数名列表，如果为None则使用初始化时的函数列表

        Returns:
            字典，格式为 {function_name: [FunctionReference, ...]}
        """
        if target_functions is None:
            target_functions = self.target_functions

        if not target_functions:
            logger.warning("没有指定要分析的目标函数")
            return {}

        # 检查缓存
        cache_key = tuple(sorted(target_functions))
        if cache_key in self._multi_references:
            return self._multi_references[cache_key]

        result = {}
        self._processed_files = 0

        if self.debug:
            logger.info(f"开始批量分析 {len(target_functions)} 个函数的调用关系")
            logger.info(f"目标函数: {target_functions}")
            logger.info(f"搜索路径: {self.root_path}")

        # 获取所有源文件
        source_files = self._get_source_files()

        for file_path in source_files:
            try:
                content = self._read_file_safely(file_path)
                if not content:
                    continue

                # 检查文件是否包含任何目标函数
                contains_target = any(func in content for func in target_functions)
                if not contains_target:
                    continue

                if self.debug:
                    logger.info(f"在文件 {file_path} 中发现目标函数")

                # 分析文件中的所有目标函数
                for target_func in target_functions:
                    if target_func in content:
                        file_references = self._analyze_file(content, target_func, str(file_path))

                        if target_func not in result:
                            result[target_func] = []
                        result[target_func].extend(file_references)

            except Exception as e:
                if self.debug:
                    logger.warning(f"处理文件 {file_path} 时出错: {e}")

        # 缓存结果
        self._multi_references[cache_key] = result

        if self.debug:
            self._log_multi_analysis_summary(result)

        return result

    def get_all_caller_names(self, target_functions: List[str] = None) -> Dict[str, List[str]]:
        """
        获取多个函数的所有调用者函数名

        Args:
            target_functions: 目标函数名列表

        Returns:
            字典，格式为 {function_name: [caller_names]}
        """
        multi_refs = self.analyze_multiple(target_functions)
        return {func: [ref.caller_name for ref in refs] for func, refs in multi_refs.items()}

    def get_combined_caller_names(self, target_functions: List[str] = None) -> List[str]:
        """
        获取多个函数的合并调用者函数名（去重）

        Args:
            target_functions: 目标函数名列表

        Returns:
            去重后的调用者函数名列表
        """
        all_callers = self.get_all_caller_names(target_functions)
        combined = set()
        for callers in all_callers.values():
            combined.update(callers)
        return list(combined)

    def get_references_by_function(self, function_name: str) -> List[FunctionReference]:
        """
        获取指定函数的引用信息

        Args:
            function_name: 函数名

        Returns:
            该函数的引用信息列表
        """
        multi_refs = self.analyze_multiple([function_name])
        return multi_refs.get(function_name, [])

    def _log_multi_analysis_summary(self, results: Dict[str, List[FunctionReference]]) -> None:
        """输出多函数分析结果摘要"""
        logger.info("=== 多函数调用分析结果 ===")
        logger.info(f"搜索路径: {self.root_path}")
        logger.info(f"处理文件数: {self._processed_files}")
        logger.info(f"分析函数数: {len(results)}")

        total_refs = sum(len(refs) for refs in results.values())
        logger.info(f"总引用数: {total_refs}")

        for func_name, refs in results.items():
            logger.info(f"\n函数 {func_name}:")
            logger.info(f"  引用数: {len(refs)}")

            # 按类型分组统计
            type_counts = {}
            for ref in refs:
                ref_type = ref.reference_type.value
                type_counts[ref_type] = type_counts.get(ref_type, 0) + 1

            for ref_type, count in type_counts.items():
                logger.info(f"    {ref_type}: {count} 个")

            # 调用者列表
            callers = [ref.caller_name for ref in refs]
            logger.info(f"  调用者: {callers}")
