# 新规变化表作成工作流配置文件
name: "新规变化表作成工作流"
description: "根据要件一览表和模板创建新规变化表"
version: "1.0.0"
author: "SDW Agent"

# 工作流特定配置
new_reg_table:
  # 目标工作表名称
  target_sheet: "機能一覧と新規・変更内容"
  
  # 表头配置
  header_start_row: 5
  header_end_row: 7
  start_col: "B"
  end_col: "J"
  
  # 数据映射规则
  mapping_rules:
    req_id: "要件No."
    step_name: "イベント名"
    req_type: "要件種別"
    req_clean_file_name: "要件の根拠 | 仕様 | 対象仕様書名"
    base_file_name: "ベース仕様書名"
    ar_no: "その他 | チケットID (不具合/仕様QA/課題/要件管理用)"
    req_change_content: "要件の内容"
  
  # 默认值配置
  defaults:
    req_type: "仕様変更"
    
  # 处理配置
  processing:
    # 是否清理临时文件
    cleanup_temp_files: true
    # 临时文件前缀
    temp_file_prefix: "temp_new_reg_table_"
