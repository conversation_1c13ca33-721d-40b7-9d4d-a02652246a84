# 新规变化表作成工作流

## 概述

新规变化表作成工作流用于根据要件一览表和模板创建新规变化表。该工作流自动化了数据提取、过滤、ID生成和映射等过程。

## V字对应

- **V字阶段**: 2.1 基本设计
- **V字项目**: 新规变化表作成

## 主要功能

1. **要件一览表数据提取**: 从要件一览表中提取相关数据
2. **数据过滤**: 处理"同上"情况并筛选需要的项目
3. **要件ID生成**: 根据P票编号生成唯一的要件ID
4. **数据预处理**: 清理文件名、获取基础式样书信息等
5. **数据映射**: 将处理后的数据映射到新规变化表模板

## 使用方法

### 1. 通过工作流注册表使用

```python
from sdw_agent.service import WorkflowRegistry
from sdw_agent.service.new_reg_table_workflow import NewRegTableInputModel
from sdw_agent.model.request_model import SourceInfo

# 创建工作流实例
workflow = WorkflowRegistry.create("new_reg_table")

# 准备输入数据
input_data = NewRegTableInputModel(
    requirement_source=SourceInfo(
        type="local",
        uri="/path/to/requirement_list.xlsx"
    ),
    template_source=SourceInfo(
        type="local", 
        uri="/path/to/new_reg_table_template.xlsx"
    )
)

# 执行工作流
result = workflow.run(input_data)
```

### 2. 直接使用工作流类

```python
from sdw_agent.service.new_reg_table_workflow import NewRegTableWorkflow, NewRegTableInputModel

# 创建工作流实例
workflow = NewRegTableWorkflow()

# 执行工作流
result = workflow.run(input_data)
```

### 3. 通过API接口使用

```bash
POST /api/sdw/new_reg_table/workflow
Content-Type: application/json

{
    "requirement_source": {
        "type": "local",
        "uri": "/path/to/requirement_list.xlsx"
    },
    "template_source": {
        "type": "local",
        "uri": "/path/to/new_reg_table_template.xlsx"
    },
    "target_sheet": "機能一覧と新規・変更内容"
}
```

## 输入参数

### NewRegTableInputModel

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| requirement_source | SourceInfo | 是 | 要件一览表的SourceInfo |
| template_source | SourceInfo | 是 | 新规变化表模板的SourceInfo |
| target_sheet | str | 否 | 目标工作表名称，默认使用配置值 |
| custom_mapping_rules | Dict[str, str] | 否 | 自定义映射规则 |

## 输出结果

### NewRegTableOutputModel

| 字段 | 类型 | 描述 |
|------|------|------|
| success | bool | 处理是否成功 |
| message | str | 处理结果消息 |
| processed_items | int | 处理的项目数量 |
| filtered_items | int | 过滤后的项目数量 |
| mapped_items | int | 映射的项目数量 |
| template_file | str | 模板文件路径 |
| requirement_file | str | 要件一览表文件路径 |
| processing_details | Dict[str, Any] | 处理详情信息 |
| error_details | str | 错误详情（如果有） |

## 配置说明

工作流配置文件位于 `config.yaml`，包含以下配置项：

### 基本配置

- `target_sheet`: 目标工作表名称
- `header_start_row`: 表头开始行号
- `header_end_row`: 表头结束行号
- `start_col`: 开始列
- `end_col`: 结束列

### 映射规则

默认的数据映射规则：

```yaml
mapping_rules:
  req_id: "要件No."
  step_name: "イベント名"
  req_type: "要件種別"
  req_clean_file_name: "要件の根拠 | 仕様 | 対象仕様書名"
  base_file_name: "ベース仕様書名"
  ar_no: "その他 | チケットID (不具合/仕様QA/課題/要件管理用)"
  req_change_content: "要件の内容"
```

### 默认值配置

- `req_type`: 默认要件种别（"仕様変更"）

### 处理配置

- `cleanup_temp_files`: 是否清理临时文件
- `temp_file_prefix`: 临时文件前缀

## 错误处理

工作流包含完善的错误处理机制：

1. **输入验证**: 验证文件存在性和参数有效性
2. **文件验证**: 验证模板文件和工作表存在性
3. **数据验证**: 验证提取的数据完整性
4. **异常捕获**: 捕获并记录所有异常信息

## 日志记录

工作流使用结构化日志记录，包含以下级别：

- `INFO`: 正常执行步骤
- `WARNING`: 警告信息（如获取AR信息失败）
- `ERROR`: 错误信息
- `DEBUG`: 详细调试信息

## 依赖项

- `pandas`: 数据处理
- `openpyxl`: Excel文件操作
- `loguru`: 日志记录
- `pydantic`: 数据验证
- `sdw_agent.util.excel_data_mapper`: Excel数据映射
- `sdw_agent.util.import_doc_util`: 要件导入工具
- `sdw_agent.util.jira_util`: Jira信息获取

## 注意事项

1. 确保要件一览表包含必要的字段（如if_check、p_no等）
2. 模板文件必须包含指定的目标工作表
3. 映射规则中的目标列名必须与模板文件中的列名匹配
4. 处理大量数据时注意内存使用情况
5. 确保有足够的磁盘空间用于临时文件
