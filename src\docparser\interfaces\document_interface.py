from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from docparser.models.document import DocumentObject


class DocumentInterface(ABC):
    """
    Interface for document parsing operations.
    Defines the contract for all document parsers.
    """
    
    @abstractmethod
    def parse_document(self, file_path: str) -> List[DocumentObject]:
        """
        Parse a document from the given file path.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing parsed document data
        """
        pass
    
    @abstractmethod
    def get_text_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all text objects from the parsed document.
        
        Returns:
            List of text objects with their properties
        """
        pass
    
    @abstractmethod
    def get_table_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all table objects from the parsed document.
        
        Returns:
            List of table objects with their properties
        """
        pass
    
    @abstractmethod
    def get_picture_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all picture objects from the parsed document.
        
        Returns:
            List of picture objects with their properties
        """
        pass
    
    @abstractmethod
    def get_graphic_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all graphic objects from the parsed document.
        
        Returns:
            List of graphic objects with their properties
        """
        pass
    
    @abstractmethod
    def export_to_json(self, output_path: str) -> None:
        """
        Export parsed document data to JSON format.
        
        Args:
            output_path: Path to save the JSON output
        """
        pass
