#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：TestcaseRAG 
@File    ：testview.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2025/4/28 16:55 
@Desc    : 说明
"""
import asyncio
import json
import os
from typing import List

import yaml
from loguru import logger

from sdw_agent.model.testview_merge import TestViewMerge
from sdw_agent.service.test_view_generate.utils.gene_testview.filter_testcase import get_filtered_testcase
from sdw_agent.service.test_view_generate.utils.gene_testview.find_function import ChangeMessageExtractor
from sdw_agent.service.test_view_generate.utils.gene_testview.gen_func_tree import ingest_testcases
from sdw_agent.service.test_view_generate.utils.gene_testview.judge_testcase_class import gene_test_view_main
from sdw_agent.util.excel_util import ExcelTemplateWriter
from sdw_agent.service.test_view_generate.utils.gene_testview.match_testview import get_matched_testview
from sdw_agent.service.test_view_generate.config import ROOT_DIR


class TestView:
    @staticmethod
    async def generate(api_change_points):
        logger.info("1.根据变更点生成功能点")
        try:
            function_point = await TestView.save_function_point(api_change_points)
            logger.info(f"功能点：{function_point}")
        except Exception as e:
            logger.error(f"根据变更点生成功能点失败：{e}")
            return False, "根据变更点生成功能点失败"
        logger.info("2.根据testcase解析树状结构")
        try:
            test_tree_path = TestView.get_fun_tree()
        except Exception as e:
            logger.error(f"根据testcase解析树状结构失败：{e}")
            return False, "根据testcase解析树状结构失败"
        logger.info("3根据功能点和树状结构匹配测试用例")
        filtered_testcase = await TestView.filte_testcase(test_tree_path, function_point)
        logger.info("4根据匹配测试用例、功能点、checklist、rule、md生成最终结果")
        res_data = await TestView.generate_result(filtered_testcase, function_point)
        return True, res_data

    @staticmethod
    def fomat_result(class_res, path_dict):
        result_list = []
        for i, item in enumerate(class_res):
            tmp = {
                "id": i + 1,
                "category": item["testview_class"] if "testview_class" in item else "",
                "checkpoint": item["testview_item"] if "testview_item" in item else "",
                "mse_category": "",
                "mse_content": "",
                "procedure": item["testview_content"] if "testview_content" in item else "",
                "source": path_dict.get(item['source']),
                "test_case": "\n".join(
                    [f"{testcase['sheet_name']}-{testcase['示例']}" for testcase in
                     item['test_case']]) if "test_case" in item else "",
                "is_use": False
            }
            result_list.append(tmp)
        return {"list": result_list}

    @staticmethod
    async def generate_result(filtered_testcase, function_point):
        check_path = os.path.join(ROOT_DIR, "data/checklists.json")
        rule_path = os.path.join(ROOT_DIR, "data/rules.json")
        # md_path = os.path.join(ROOT_DIR, "tests/example.md")
        md_path = ''
        class_res = await gene_test_view_main(filtered_testcase, function_point, check_path, rule_path, md_path)
        with open(os.path.join(ROOT_DIR, "data/result0.json"), "w", encoding="utf-8") as f:
            json.dump(class_res, f, ensure_ascii=False, indent=2)
        class_res = await get_matched_testview(os.path.join(ROOT_DIR, "data/result0.json"))
        save_path = os.path.join(ROOT_DIR, "data/result.json")
        with open(save_path, "w", encoding="utf-8") as f:
            json.dump(class_res, f, ensure_ascii=False, indent=2)
        result = TestView.fomat_result(class_res, {"rule": rule_path, "md": md_path})
        return result

    @staticmethod
    async def filte_testcase(test_tree_path, function_point):
        result = await get_filtered_testcase(pkl=test_tree_path, function=function_point.get('function_point'))
        save_path = os.path.join(ROOT_DIR, "data/filtered_testcase.json")
        with open(save_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        return result

    @staticmethod
    def get_fun_tree():
        """
        获取到树结构
        """
        config = yaml.load(open(os.path.join(ROOT_DIR, "config/config.yaml"), "r", encoding="utf-8"),
                           Loader=yaml.FullLoader)
        test_case_path = os.path.join(ROOT_DIR, config["test_agent_input_table"]["testcases"]["name"])
        save_path = os.path.join(ROOT_DIR, config["test_agent_input_table"]["test_tree"]["name"])
        ingest_testcases(test_case_path, save_path)
        return save_path

    @staticmethod
    async def save_function_point(api_change_points):
        """
        根据更改点生成功能点并保存
        """
        current_changepoint = os.path.join(ROOT_DIR,"data/current_changepoint.json")
        with open(current_changepoint, "w", encoding="utf-8") as f:
            json.dump(api_change_points, f, ensure_ascii=False, indent=4)
        del_key_list = ["id", "requirementMd", "summary", "changeNo"]
        for key in del_key_list:
            if key in api_change_points:
                del api_change_points[key]
        function_point = await TestView.find_function_point(api_change_points)
        save_path = os.path.join(ROOT_DIR, "data/function_point.json")
        with open(save_path, "w", encoding="utf-8") as f:
            json.dump(function_point, f, ensure_ascii=False, indent=4)
        return function_point

    @staticmethod
    async def find_function_point(change_points):
        """
        根据更改点生成功能点
        """
        change_class = ChangeMessageExtractor(change_points)
        deal_res = await change_class.function_extract()
        return deal_res


def write_excel(testView: List[TestViewMerge], template_path, output_path: str):
    """将测试用例写入测试用例模板"""
    writer = ExcelTemplateWriter("TestViewTemplate.xlsx")
    writer.write_data(sheet_name="非自动化_DR观点", data=testView, start_row=3)
    writer.save(output_path)


if __name__ == '__main__':
    input_data = {
        "change_points": {
                "ARチケットNO": "MET19PFV3-21517",
                "ARチケットNOリンク": "MET19PFV3-21517",
                "ARチケットタイトル": "【顧客要求_変更】MET-G_CSTMLST-CSTD-A0-04-A-C0",
                "A核": "〇",
                "R核": "〇",
                "SCL填写要": "×",
                "changeNo": "MET19PFV3-21517",
                "id": 2,
                "requirementMd": "MET_CSTM_警報音量の階層修正機能.md",
                "summary": "警報音量の階層修正",
                "エピック名": "MET-G_CSTMLST-CSTD_SoC R_警報音量の階層修正",
                "变更内容所在的章节信息": "5.1 画面階層情報",
                "変更内容": "第二階層の警報音量の詳細画面は第二階層から第一階層の警報音量項目の下に移動する",
                "対応イベント": "R2大",
                "差分種別": "选项位置变更",
                "概要": "警報音量の階層修正",
                "要件チケットNO": "-",
                "要件チケットNOリンク": "-",
                "要件需求文档名称": "MET_CSTM_警報音量の階層修正機能.md"
            }
    }
    _, res = asyncio.run(TestView.generate(input_data['change_points']))
    print(res)

