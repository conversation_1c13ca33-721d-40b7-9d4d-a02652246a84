# Auto Action 模块

## 概述

Auto Action 模块是V字流58号样例的实现，提供基于用户输入的智能文档查询和建议生成功能。该模块能够根据用户的查询内容，自动匹配企业内部知识库，结合大模型理解能力，生成结构化的MarkDown格式操作指引，并返回相关文件路径供人工核对。

## 功能特点

- **智能知识库匹配**: 根据用户输入自动识别相关数据库
- **Excel内容提取**: 支持从Excel文件中提取文字内容
- **大模型集成**: 集成Azure OpenAI进行智能分析和建议生成
- **Markdown输出**: 生成结构化的操作指引文档
- **文件路径返回**: 提供原始参考资料路径便于追溯

## 文件结构

```
auto_action/
├── readme.md              # 本说明文档
├── workflow.py            # 主要工作流实现
├── models.py              # 数据模型定义
├── config.yaml            # 配置文件
├── extract_info.py        # 信息提取核心逻辑
├── output.md              # 生成的输出示例
├── __init__.py            # 模块初始化文件
└── 性能测试手顺/          # 知识库数据目录
    ├── 1-ROM&RAM测试/
    ├── 2-Stack统计/
    ├── 3-负荷测试相关资料/
    └── 4-启动时间测试/
```

## 核心文件说明

### workflow.py
- **功能**: 主要工作流实现，包含完整的业务逻辑
- **主要类**: `AutoActionWorkflow`
- **核心方法**: 
  - `execute()`: 执行工作流主逻辑
  - `query_model()`: 调用大模型
  - `extract_text_from_excel()`: Excel内容提取

### models.py
- **功能**: 定义数据模型和枚举类型
- **主要类**:
  - `QueryStatus`: 查询状态枚举
  - `QueryConfigModel`: 配置模型
  - `QueryResult`: 查询结果模型
  - `AutoActionResult`: 工作流执行结果

### config.yaml
- **功能**: 模块配置文件
- **配置项**:
  - `io`: 输入输出配置
  - `llm`: 大模型配置
  - `prompts`: 提示词模板

### extract_info.py
- **功能**: 信息提取核心逻辑
- **主要函数**: `get_suggestion_from_files()`
- **作用**: 根据用户输入匹配知识库并生成建议

## 数据库分类

模块支持以下4个主要数据库：

1. **1-ROM&RAM测试**: ROM和RAM相关测试手顺
2. **2-Stack统计**: Stack统计相关文档
3. **3-负荷测试相关资料**: 负荷测试和CANoe地图发送手顺
4. **4-启动时间测试**: 启动时间测试相关文档

## 使用示例

```python
from sdw_agent.service.auto_action.workflow import AutoActionWorkflow

# 创建工作流实例
workflow = AutoActionWorkflow()

# 执行查询
result = await workflow.execute("如何进行RAM测试?")

# 处理结果
if result.status == QueryStatus.SUCCESS:
    print(f"输出文件: {result.data.md_path}")
    print(f"相关文件数: {len(result.data.file_paths)}")
```

## API接口

该模块通过 `support_query.py` 路由提供RESTful API接口：

- **POST** `/api/sdw/dev58/run_llm_with_files`
  - 功能: 根据用户询问生成大模型输出和相关文件路径
  - 输入: `user_input` (用户查询内容)
  - 输出: `md_path` (生成的markdown文件路径) 和 `file_paths` (相关文件路径列表)

## 技术栈

- **Python**: 主要开发语言
- **FastAPI**: Web框架
- **Azure OpenAI**: 大语言模型服务
- **openpyxl**: Excel文件处理
- **PyYAML**: 配置文件处理
- **Pydantic**: 数据验证

## 注意事项

1. 使用前需要配置有效的Azure OpenAI API密钥
2. 确保知识库目录结构正确
3. 生成的markdown文件会保存在模块目录下
4. 支持的文件格式: `.xlsx`, `.xls`

---

**V字流58号样例**: 本模块是V字流开发流程中第58号样例的实现，展示了如何构建智能化的文档查询和建议生成系统。
