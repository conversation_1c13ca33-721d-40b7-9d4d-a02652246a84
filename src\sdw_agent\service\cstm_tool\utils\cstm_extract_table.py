import os
import json
import pathlib
import traceback

import pandas as pd
from loguru import logger
from typing import List, Dict, Tu<PERSON>
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import AIMessage

from sdw_agent.service.cstm_tool.utils.config_manager import config_manager
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.cstm_tool.model import ScreenHierarchyItem, HasSimilarTextFormat
from sdw_agent.service.cstm_tool.utils.control_spec_parser import parse_control_spec_by_prefix, parse_detailed_spec, \
    parse_control_spec
from sdw_agent.service.cstm_tool.utils.cstm_utils import CstmUtils


logger.bind(name="CSTM_TOOL")


def analyze_requirement_changes( idx, pair_tree, req_name, before_cstm_json_path, after_cstm_json_path, cus_opt_cntt_info_list, target_folder=None):
    """
    分析变更前后的需求文档，并生成差异化分析结果

    参数:
        idx: pair_tree 组索引信息
        pair_tree: 变更前后的目录树
        req_name: 变更名称
        output_dir: 输出文件夹名称

    返回:
        包含差异分析结果的字典列表
    """

    # 分析变更
    objects_pairs, changes = analyze_changes(pair_tree, before_cstm_json_path, after_cstm_json_path, cus_opt_cntt_info_list, handle_most_deep=True)

    # 从右侧开始分割,第二个参数1表示只分割一次
    output_dir = os.path.join(pathlib.Path(config_manager.get('workflow_config.output.path')), target_folder)
    output_file = f"{req_name}_changes_{idx}.json"

    json_file_path = os.path.join(output_dir, output_file)
    export_changes_to_json(changes, json_file_path)
    return changes, json_file_path, objects_pairs

def build_after_path_mapping(after_paths):
    """
    构建 after_path_mapping，键为路径最后一部分（处理重复），值为完整路径
    :param after_paths: 变更后的路径列表
    :return: after_path_mapping 字典
    """
    after_path_mapping = {}
    for path in after_paths:
        parts = path.split("👍")
        if len(parts) > 1:
            # 统计每个元素出现的次数
            part_counts = {}
            new_parts = []
            for part in parts:
                if part in part_counts:
                    part_counts[part] += 1
                    # 只从第二次出现开始添加尾缀
                    new_parts.append(f"{part}_{part_counts[part]}")
                else:
                    part_counts[part] = 1
                    new_parts.append(part)
            key = new_parts[-1]  # 获取路径的最后一部分（已处理重复）
            after_path_mapping[key] = path
    return after_path_mapping

def analyze_changes(pair_tree, before_cstm_json_path, after_cstm_json_path, cus_opt_cntt_info_list, handle_most_deep=False):
    """
    分析变更前后的配置项路径信息，并生成差异化分析结果

    参数:
        pair_tree: 变更前后的目录树
        before_cstm_json_path: 变更前原始式样书json路径
        after_cstm_json_path: 变更后原始式样书json路径
        handle_most_deep: 变更后的Markdown文件路径

    返回:
        包含差异分析结果的字典列表
    """
    # 提取变更前的路径信息
    path_pairs, objects_pairs = deserialization_json(pair_tree)
    logger.info(f"变更前路径数量: {len(path_pairs[0])}")
    before_paths, before_objects = path_pairs[0], objects_pairs[0]

    # # 提取变更后的路径信息
    logger.info(f"变更后路径数量: {len(path_pairs[1])}")
    after_paths, after_objects = path_pairs[1], objects_pairs[1]


    # 用于存储最终结果
    results = []
    # 是否是选项新增，变更，删除
    option_change = False

    # 记录已经处理过的路径
    processed_after_paths = set()

    # 创建一个映射，存储路径的末尾部分到完整路径的映射
    # 这有助于识别位置变更的情况
    after_path_mapping = build_after_path_mapping(after_paths)

    # 1. 分析变更前的每个路径
    for before_path in before_paths:
        before_path_end = ""
        before_path_mapping = build_after_path_mapping([before_path])
        if before_path_mapping:
            before_path_end = list(before_path_mapping.keys())[0]

        # before_path_parts = before_path.split("👍")
        # before_path_end = before_path_parts[-1] if len(before_path_parts) > 1 else ""

        # 检查变更前选项是否在变更后的路径中
        if before_path_end in after_path_mapping:
            #得先判断after_path 的父路径是否存在
            # if not exist
                # 检查是否是 (画面名称update)
            # else
                # (选项位置变更)

            after_path = after_path_mapping[before_path_end]
            # 得到before_path和after_path的最大公共路径
            common_path = find_common_path(before_path, after_path)
            if after_path.replace(common_path, "").lstrip("👍").split("👍"):
                # # 定位到画面变更前后的 画面名称
                af_check_parent = after_path.replace(common_path, "").lstrip("👍").split("👍")[0]
                # 加载before_cstm_json_path路径对应的json文件
                before_cstm_json = None
                if os.path.exists(before_cstm_json_path):
                    with open(before_cstm_json_path, 'r', encoding='utf-8') as f:
                        before_cstm_json = json.load(f)  # 直接解析为 Python 字典/列表
                else:
                    logger.error(
                        # f"{gettext('原始式样书json解析文件不存在:')}{before_cstm_json_path}"
                        f"原始式样书json解析文件不存在: {before_cstm_json_path}"
                    )
                # 判断after_path对应的父路径在before_cstm_json_path中是否存在
                parent_exist = False
                if before_cstm_json:
                    for item in before_cstm_json:
                        if CstmUtils.clean_title(item.get("cntt_name")) == af_check_parent:
                            parent_exist = True
                            break
                # 如果after_path对应的父级画面之前就存在了，那证明是选项位置变更
                if parent_exist:
                    result = {
                        "type": "move",
                        "is_cntt": False,
                        "opt_before_path": before_path,
                        "opt_after_path": after_path,
                        "opt_property": {}
                    }
                    results.append(result)
                    processed_after_paths.add(after_path)
                    option_change = True

        if before_path not in after_paths and before_path_end not in after_path_mapping:
            # 如果变更前的路径在变更后的路径中不存在（选项更新）, 且变更前的路径末尾部分不在变更后的路径中（选项位置变更/画面名称变更）
            # 检查是否存在变更后的路径是变更前路径的父路径,或者 before_path_end 不存在了（选项删除）
            is_deleted = False
            for after_path in after_paths:
                if is_parent_path(after_path, before_path) and before_path_end not in after_path_mapping:
                    # 变更后的路径是变更前路径的父路径 - 选项删除
                    result = {
                        "type": "delete",
                        "is_cntt": False,
                        "opt_before_path": before_path,
                        "opt_after_path": after_path,  # 记录父路径
                        "opt_property": {}
                    }
                    results.append(result)
                    processed_after_paths.add(after_path)
                    is_deleted = True
                    option_change = True

            if not is_deleted:
                # 检查是否存在变更前的路径是变更后路径的父路径（选项追加）
                for after_path in after_paths:
                    if is_parent_path(before_path, after_path):
                        # 变更前的路径是变更后路径的父路径 - 选项追加
                        option_change = True
                        # 这里需要区分增加子项和增加画面的情况
                        added_path = after_path.replace(before_path+"👍", "").split("👍")
                        # 判断是否是将 选项变更为画面
                        opt_to_cntt = is_opt_to_cntt(before_path, after_path, objects_pairs, cus_opt_cntt_info_list)
                        if len(added_path) > 1 or opt_to_cntt:
                            # 增加画面
                            before_path_list = before_path.split("👍")
                            if opt_to_cntt:
                                before_path_list = before_path.split("👍")[:-1]
                            after_path_list = after_path.split("👍")
                            result = append_layer_view(len(before_path_list), before_path_list, after_path_list)
                            results.append(result)
                            processed_after_paths.add(after_path)
                        else:
                            # 增加子项
                            result = {
                                "type": "append",
                                "is_cntt": False,
                                "opt_before_path": before_path,  # 记录父路径
                                "opt_after_path": after_path,
                                "opt_property": {}
                            }
                            results.append(result)
                            processed_after_paths.add(after_path)

    # 比较变更前后的对象列表,确认父目录是否需要更新
    max_level = 0
    if after_objects and after_objects[-1].hierarchy_level.isdigit():
        max_level = int(after_objects[-1].hierarchy_level)
    for idx_bf, before_object in enumerate(before_objects):
        for idx_af, after_object in enumerate(after_objects):

            if before_object.hierarchy_level != after_object.hierarchy_level or before_object.title != after_object.title:
                # 如果阶层不一样则不比较
                continue
            else:
                if before_object.item_name == after_object.item_name or before_object.item_name == after_object.bf_opt_name:
                    # 如果 before_object.item_name == after_object.item_name  路径完全相同 - 选项属性更新、画面属性更新
                    # 如果 before_object.item_name==after_object.bf_opt_name 选项名称更新、画面名称更新
                    # 比较内容是否变化
                    has_changes, changes = compare_objects(before_object, after_object)
                    if has_changes:
                        change_types = []
                        # if  option_change:
                        #     change_types.append("1.选项变更/选项新增/削除")
                        # 不是选项变更, 才去判断其他的变更类型
                        if "item_name" in changes:
                            change_types.append("CSTM意匠变更")
                        if "switch_type" in changes:
                            change_types.append("切替类型变更")
                        if "operable_while_driving" in changes:
                            change_types.append("走形判断变更")
                        if "grayout_condition" in changes:
                            change_types.append("置灰条件变更")
                        if "display_init" in changes:
                            change_types.append("表示设定初始化关联机能表示")
                        if "default_value" in changes:
                            change_types.append("初始值变更")
                        if "meter_eeprom" in changes:
                            change_types.append("EEPROM存储要否变更")

                        result = None
                        # 这里得判断是画面属性发生了变化，还是选项属性发生了变化
                        if after_object.hierarchy_level.isdigit() and int(after_object.hierarchy_level) == max_level:
                            # 选项名称变更
                            result = {
                                "type": "update",
                                "is_cntt": False,
                                "opt_before_path": before_object.path,
                                "opt_after_path": after_object.path,
                                "opt_property": changes,
                                "change_types": change_types
                            }

                        elif after_object.hierarchy_level.isdigit() and int(after_object.hierarchy_level) < max_level:
                            # 画面名称变更
                            result = {
                                "type": "update",
                                "is_cntt": True,
                                "opt_before_path": before_object.path,
                                "opt_after_path": after_object.path,
                                "opt_property": changes,
                                "change_types": change_types
                            }
                        if result:
                            results.append(result)
                            processed_after_paths.add(result.get('opt_after_path'))

    # 2. 查找变更后新增的路径（完全新增，没有父路径关系的）
    for after_path in after_paths:
        if after_path not in processed_after_paths:
            # 检查是否已经作为子路径被处理过
            parent_found = False
            has_exist = False
            for before_path in before_paths:
                if before_path == after_path:
                    has_exist = True
                    break
                if is_parent_path(before_path, after_path):
                    # 变更前路径是变更后路径的父路径 应该是 选项新增，画面新增情况中的一种，在前面处理过了
                    parent_found = True
                    break

            if not parent_found and not has_exist:
                # 如果变更后路径在之前没有被处理过即parent_found为False
                # 完全新增的路径
                result = {
                    "type": "append",
                    "is_cntt": False,
                    "opt_before_path": "",
                    "opt_after_path": after_path,
                    "opt_property": {}
                }
                results.append(result)
                option_change = True


    # 从提取的对象中获取额外属性信息
    for result in results:
        # 为移动和更新类型的变更添加显示顺序属性
        if result["type"] in ["move", "update"] and result["opt_after_path"]:
            # 提取对象的显示顺序
            display_order = extract_display_order(after_objects, result["opt_after_path"])
            if display_order:
                result["opt_property"]["order"] = display_order

        # 为新增类型的变更添加显示顺序属性
        elif result["type"] == "append" and result["opt_after_path"]:
            # 这里区分增加画面和增加子项
            if 'is_parent' in result:
                # 增加画面
                add_display_order(result, after_objects)
            else:
                # 增加子项
                # 提取对象的显示顺序
                display_order = extract_display_order(after_objects, result["opt_after_path"])
                if display_order:
                    result["opt_property"]={
                            "order":display_order
                        }

        # 为删除类型的变更添加显示顺序属性
        elif result["type"] == "delete" and result["opt_before_path"]:
            # 提取对象的显示顺序
            display_order = extract_display_order(before_objects, result["opt_before_path"])
            if display_order:
                result["opt_property"]["order"] = display_order


    if handle_most_deep:
        path_deep_list = []
        for item in results:
            max_deep = max(item['opt_before_path'].count('👍'), item['opt_after_path'].count('👍'))
            path_deep_list.append(max_deep)
        
        # 找出最大深度
        max_depth = max(path_deep_list) if path_deep_list else 0
        
        new_results = []
        for i, x in enumerate(path_deep_list):
            if x == max_depth:
                new_results.append(results[i])
        # results = new_results

    return objects_pairs, results

def is_opt_to_cntt(before_path, after_path, objects_pairs, cus_opt_cntt_info_list):
    '''
    判断选项是否变更成一个画面
    :param before_path:
    :param after_path:
    :param objects_pairs:
    :param cus_opt_cntt_info_list:
    :return:
    '''
    # 判断before 路径中最后一个元素 是否是一个画面
    is_cntt = cntt_is_exist_using_definition(before_path, cus_opt_cntt_info_list)
    before_paths = before_path.split("👍")
    after_paths = after_path.split("👍")
    if len(after_paths)-len(before_paths) == 1 and not is_cntt:
        return True
    else:
        return False

def cntt_is_exist_using_definition(before_path, cus_opt_cntt_info_list):
    '''
    判断路径中最后一个元素是否是一个画面
    :param before_path:
    :param cus_opt_cntt_info_list:
    :return:
    '''
    before_paths = before_path.split("👍")
    before_path_end = before_paths[-1]
    container_map = {container.Label: container for container in cus_opt_cntt_info_list}
    container_def_map = {container.Definit: container for container in cus_opt_cntt_info_list}
    current_container = container_map.get(before_paths[0])

    # 获取配置中的映射字典
    item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

    cnnt_exist = True
    for idx, item in enumerate(before_paths[1:]):
        current_container_map = {container.OptLabel: container for container in current_container.cus_opt_info_list}
        # 根据容器标签从父级的cus_opt_info_list中匹配到对应的选项
        if item in title_map:
            # 处理原始式样书中命名与cstm配置工具中不一致的情况
            item = title_map[item]
        current_opt = current_container_map.get(item)
        if not current_opt:
            # raise ValueError(f"{gettext('选项名称在变更前CSTM配置工具中不存在：')}{item}")
            raise ValueError(f"选项名称在变更前CSTM配置工具中不存在：{item}")
        current_definition = current_opt.OptDef
        current_container = container_def_map.get(current_definition)
        if item == before_path_end and current_container is None:
            cnnt_exist = False

    return cnnt_exist

def export_changes_to_json(changes, json_file_path=None):
    """
    将变更分析结果导出为JSON文件

    参数:
        changes: 变更分析结果
        output_file: 输出JSON文件名
    """

    # 确保输出目录存在
    output_dir = os.path.dirname(json_file_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 写入JSON文件
    with open(json_file_path, 'w', encoding='utf-8') as file:
        json.dump(changes, file, ensure_ascii=False, indent=4)
    logger.success(f"变更分析结果已成功导出到 {json_file_path}")

def deserialization_json(pair_tree, verbose=False):
    """
    opt 对象反序列化得到object
    参数:
        pair_tree: 变更前后选项信息
        verbose: 是否打印详细信息，默认为False
    返回:
        层次路径信息列表，格式为：👍Menu👍path1👍path2👍...👍target_item
    """

    # 创建对象
    objects_pair = create_objects_from_data(pair_tree)

    # 如果需要打印详细信息
    if verbose:
        # 打印对象详细信息
        logger.info("变更前层级对象详细信息：")
        print_objects(objects_pair[0])
        logger.info("变更后层级对象详细信息：")
        print_objects(objects_pair[1])

    # 提取层次路径
    paths_bf = extract_hierarchy_paths(objects_pair[0])
    paths_af = extract_hierarchy_paths(objects_pair[1])

    return list([paths_bf,paths_af]), objects_pair

def create_objects_from_data(pair_tree) -> List[List[ScreenHierarchyItem]]:
    """
    从格式化json数据创建对象列表
    参数:
        data: 表格数据
        headers: 表头列表
    返回:
        包含ScreenHierarchyItem对象的列表
    """
    if pair_tree is None:
        return None

    # 将变更前的选项目录树转换成对象
    bf_objects = []
    logger.info("变更前层级目录：")
    for opt in pair_tree[0]:
        # 创建对象，传入之前创建的对象列表以计算路径
        # logger.info(json.dumps(opt, indent=2))
        item = ScreenHierarchyItem.from_row(opt, bf_objects)
        bf_objects.append(item)
    # 将变更后的选项目录树转换成对象
    af_objects = []
    logger.info("变更后层级目录：")
    for opt in pair_tree[1]:
        # 创建对象，传入之前创建的对象列表以计算路径
        # logger.info(json.dumps(opt, indent=2))
        item = ScreenHierarchyItem.from_row(opt, af_objects)
        af_objects.append(item)

    return list([bf_objects, af_objects])

def print_objects(objects: List[ScreenHierarchyItem]):
    """
    打印对象列表
    """
    logger.info("\n对象列表（共 {} 项）：".format(len(objects)))
    for i, obj in enumerate(objects, 1):
        logger.info(f"\n对象 {i}: {obj.function_name}")
        # 获取对象的所有属性
        for field_name, value in obj.__dict__.items():
            # 跳过icon字段
            if field_name == 'icon':
                continue

            # 为了更好的可读性，对包含换行符的值进行特殊处理
            if isinstance(value, str) and '\n' in value:
                logger.info(f"  {field_name}:")
                for line in value.split('\n'):
                    logger.info(f"    {line}")
            else:
                logger.info(f"  {field_name}: {value}")

def extract_hierarchy_paths(objects: List[ScreenHierarchyItem]) -> List[str]:
    """
    提取配置项的层次路径信息
    参数:
        objects: 对象列表
    返回:
        层次路径信息列表，格式为：👍Menu👍path1👍path2👍...👍target_item
    """
    if not objects:
        return []

    # 打印调试信息
    ("\n调试信息 - 对象层级：")
    for obj in objects:
        logger.info(f"层级: {obj.hierarchy_level}, 功能: {obj.function_name}, 标题: {obj.title}, 项目: {obj.item_name}")

    # 获取配置中的映射字典
    item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

    # 将对象按层级排序
    # 按层级数字排序
    sorted_objs = sorted(objects, key=lambda x: x.hierarchy_level)

    # 按层级分组对象
    level_groups = {}
    for obj in sorted_objs:
        level_num = int(obj.hierarchy_level)
        if level_num not in level_groups:
            level_groups[level_num] = []
        level_groups[level_num].append(obj)

    # 获取最大层级
    max_level = max(level_groups.keys()) if level_groups else 0

    # 基础路径部分 - 共同的部分
    base_path_parts = ["Menu"]  # 第0层固定为Menu

    # 添加0-倒数第二层的路径部分
    for level in range(0, max_level):
        if level in level_groups:
            # 对于每个层级，只需要第一个对象的标题
            obj = level_groups[level][0]
            if level == 0:
                # 第0层已经固定为Menu，跳过
                continue
            else:
                # 中间层级使用title
                base_path_parts.append(obj.title)

    # 为最后一层的每个对象生成完整路径
    paths = []
    if max_level in level_groups:
        last_level_objs = level_groups[max_level]
        for obj in last_level_objs:
            # 复制基础路径
            path_parts = base_path_parts.copy()
            # 添加最后一层标题和项目名称
            if obj.title.upper() != 'MENU':
                path_parts.append(obj.title)
            path_parts.append(obj.item_name)

            # 移除可能的重复项
            # unique_path_parts = []
            for idx,part in enumerate(path_parts):
                if part in item_name_map:
                    path_parts[idx] = item_name_map[part]

            # 用👍连接所有路径部分
            path_str = "👍".join(path_parts)
            paths.append(path_str)
    else:
        # 如果没有最后一层，使用基础路径
        path_str = "👍".join(base_path_parts)
        paths.append(path_str)

    # 打印生成的路径
    for path in paths:
        logger.info(f"\n生成的路径: {path.replace('👍', '/')}")

    return paths

def find_common_path(before_path: str, after_path: str) -> str:
    """
    寻找两个路径的最大公共路径

    参数:
        before_path: 变更前路径
        after_path: 变更后路径

    返回:
        str: 最大公共路径，如果没有公共路径则返回空字符串
    """
    if not before_path or not after_path:
        return ""

    # 分割路径
    before_path_parts = before_path.split("👍")
    after_path_parts = after_path.split("👍")

    # 寻找公共部分
    common_parts = []
    min_length = min(len(before_path_parts), len(after_path_parts))

    for i in range(min_length):
        if before_path_parts[i] == after_path_parts[i]:
            common_parts.append(before_path_parts[i])
        else:
            break

    # 返回公共路径
    return "👍".join(common_parts) if common_parts else ""


def is_parent_path(parent: str, child: str) -> bool:
    """
    判断一个路径是否是另一个路径的父路径

    参数:
        parent: 可能的父路径
        child: 可能的子路径

    返回:
        bool: 如果parent是child的父路径则返回True
    """
    parent_parts = parent.split("👍")
    child_parts = child.split("👍")

    # 父路径的部分必须比子路径少
    if len(parent_parts) >= len(child_parts):
        return False

    # 检查父路径的所有部分是否与子路径的对应部分相同
    return all(parent_parts[i] == child_parts[i] for i in range(len(parent_parts)))

def append_layer_view(level_num, before_list, after_list):
    """
    添加阶层画面场景生成对应的json
    """
    obj = {}
    obj["type"] = "append"
    if level_num == len(before_list):
        obj["opt_before_path"] = "👍".join(after_list[:level_num])
    obj["opt_after_path"] = "👍".join(after_list[:level_num+1])
    if len(after_list)>(level_num+1):
        obj["is_parent"] = True
        obj["is_cntt"] = True
        # obj["name"] = after_list[level_num] #用于确定order
        obj["children"] = append_layer_view(level_num+1, before_list, after_list)
        return obj
    else:
        # obj["is_parent"] = False
        # obj["name"] = after_list[level_num]
        obj["children"] = None
        return obj

def extract_display_order(objects, path):
    """
    从对象列表中提取特定路径对应对象的显示顺序
    参数:
        objects: 对象列表
        path: 配置项路径
    返回:显示顺序字符串
    """

    # 获取路径的最后一部分（项目名称）
    path_parts = path.split("👍")
    if len(path_parts) < 2:
        return None

    target_item = path_parts[-1]
    target_deep = len(path_parts)-2 # 因为路径第一位为Menu，所以减2

    # 获取配置中的映射字典
    item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

    # 查找匹配的对象
    for idx, obj in enumerate(objects):
        # 处理cstm 配置工具中选项名称与原始式样书不一致的情况
        if obj.item_name in item_name_map:
            if (item_name_map.get(obj.item_name) == target_item or item_name_map.get(obj.function_name) == target_item) \
                    and obj.hierarchy_level==str(target_deep):
                return obj.display_order
        else:
            if (obj.item_name == target_item or obj.function_name == target_item) \
                    and obj.hierarchy_level==str(target_deep):
                return obj.display_order

    return None

def add_display_order(json_data, after_objects):
    """
    添加阶层画面时，为新增的每一层及添加显示顺序
    """
    display_order = extract_display_order(after_objects, json_data["opt_after_path"])
    if display_order:
        # json_data["opt_property"]["order"] = display_order
        json_data["opt_property"] = {
            "order": display_order
        }
    # 移除name属性
    if "name" in json_data:
        json_data.pop("name")

    # 移除is_parent属性
    if "is_parent" in json_data:
        json_data.pop("is_parent")

    # 递归情况：如果当前层有 "children"，并且它是一个字典
    if "children" in json_data and json_data["children"]:
        json_data["children"] = add_display_order(json_data["children"], after_objects)

    return json_data

def compare_objects(before_object: ScreenHierarchyItem, after_object: ScreenHierarchyItem) -> Tuple[bool, Dict[str, Tuple[str, str]]]:
    """
    比较两个 ScreenHierarchyItem 对象的内容是否发生改变

    参数:
        before_object: 变更前的对象
        after_object: 变更后的对象

    返回:
        Tuple[bool, Dict[str, Tuple[str, str]]]:
        - bool: True 表示有变化，False 表示无变化
        - Dict: 变化的字段及其变更前后的值，格式为 {字段名: (变更前值, 变更后值)}
    """
    # 如果两个对象的阶层信息不一样 就没必要比较
    if before_object.hierarchy_level!=after_object.hierarchy_level:
        return False, {}

    # 需要比较的字段列表（排除不需要比较的字段）
    fields_to_compare = [
        'function_name',
        'title',
        'item_name',
        'enter_sw_operation',
        'item_description',
        'on_off_display',
        'switch_type',
        'operable_while_driving',
    ]

    changes = {}
    has_changes = False

    # 比较每个字段的值
    for field in fields_to_compare:
        before_value = getattr(before_object, field, '')
        after_value = getattr(after_object, field, '')

        # 如果字段值不同，记录变化
        if before_value != after_value:
            has_changes = True
            changes[field] = (before_value, after_value)

    return has_changes, changes

def get_linked_spec_file(prefix: List[str], cur_dir:str):
    '''
    根据关联式样书前缀定位到关联式样书的文件路径
    '''
    try:
        directory = pathlib.Path(config_manager.get('workflow_config.input.linked_spec_path'))
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取关联式样书配置信息失败，请检查配置文件: {e}")
        # raise RuntimeError(gettext('未找到关联式样文件存储路径，请检查配置文件相关路径')) from e
        raise RuntimeError('未找到关联式样文件存储路径，请检查配置文件相关路径') from e

    if not directory.exists():
        if pathlib.Path(cur_dir).exists():
            directory = cur_dir
        else:
            # raise RuntimeError(gettext('未找到关联式样文件存储路径，请检查配置文件相关路径'))
            raise RuntimeError('未找到关联式样文件存储路径，请检查配置文件相关路径')

    if not prefix or prefix==['None']:
        return None

    linked_spec_file = parse_control_spec_by_prefix(prefix[0], directory)

    return linked_spec_file

@logger.catch(reraise=True)
def extract_get_set_cnt_table(prefix: List[str], change_name: str, cur_dir:str):
    """
    提取设置计数表和获取计数表数据。

    根据给定的前缀、变更名称和目录解析控制规范字典，然后从该字典中提取
    设置计数表（set table）和获取计数表（get table）的数据。

    参数:
    - prefix: 用于解析控制规范的前缀字符串。
    - change_name: 用于解析控制规范的变更名称字符串。
    - directory: 包含控制规范文件的目录路径。

    返回:
    一个元组，包含两个列表：(set_table_data, get_table_data)。
    - set_table_data: 设置计数表的数据列表。
    - get_table_data: 获取计数表的数据列表。

    抛出:
    - RuntimeError: 如果解析控制规范字典失败。
    - ValueError: 如果解析的结果不是字典类型。
    """
    linked_spec_file = get_linked_spec_file(prefix, cur_dir)
    logger.info(f"选项{change_name}对应的关联式样书前缀为{prefix}, 对应路径：{linked_spec_file}")

    if linked_spec_file is None or not os.path.exists(linked_spec_file):
        logger.warning(f"选项{change_name}无参照关联式样")
        return None, None, None

    # 使用 openpyxl 引擎，并只读取需要的数据区域，避免自动类型推断和公式计算带来的性能波动
    # 先读取所有sheet
    all_sheets = pd.read_excel(
        linked_spec_file,
        sheet_name=None,
        engine="openpyxl",
        dtype=str
    )
    try:
        control_spec_dict = parse_control_spec(linked_spec_file, change_name, all_sheets)
    except Exception as e:
        # 解析失败时，抛出 RuntimeError 并包含原始异常
        # raise RuntimeError(gettext("关联式样书解析失败")) from e
        raise RuntimeError('关联式样书解析失败') from e

    # 确保解析结果是字典类型
    if control_spec_dict is None:
        logger.warning("关联式样书不符合标准解析格式")
        return None, None, all_sheets
        # raise ValueError("关联式样书解析失败")

    # 初始化设置和获取计数表的数据列表
    set_table_data, get_table_data = [], []

    # 遍历控制规范字典的每个键值对
    for key in control_spec_dict:
        # 处理设置计数表的数据
        if key.startswith("5.2.1"):
            # 确保 control_spec_dict[key] 是列表且每个元素是字典
            if isinstance(control_spec_dict[key], list):
                set_table_data.extend(
                    "".join(item.values()) for item in control_spec_dict[key] if isinstance(item, dict)
                )
        # 处理获取计数表的数据
        elif key.startswith("5.2.2"):
            if isinstance(control_spec_dict[key], list):
                get_table_data.extend(
                    "".join(item.values()) for item in control_spec_dict[key] if isinstance(item, dict)
                )

    # 返回设置和获取计数表的数据
    return set_table_data, get_table_data, all_sheets

def if_need_special_handle(spec_name: str, linked_spec_excel):
    """
    判断变更点是否需要特殊处理
    参数:
        related_spec_doc_path: 关联规格书文档路径
        spec_name: 规格名称

    返回：是否需要特殊处理
    """
    try:
        # 如果没有关联式样，默认返回值为True
        if linked_spec_excel is None:
            return True

        # 解析关联规格书文档中的详细规格信息
        special_condition_tbl_data = parse_detailed_spec(linked_spec_excel, spec_name)

        if special_condition_tbl_data is None:
            # 如果为None,表示关联式样为非标准格式
            return True

        # 过滤出所有需要特殊处理的项
        special_handlings = [item.strip() for item in special_condition_tbl_data if
                             (item.strip() and item.strip() != '-')]

        if len(special_handlings) > 0:
            # 将特殊处理项格式化为字符串，以便后续处理
            special_handling_text = "\n".join(special_handlings)
            # 从配置文件中获取特殊处理的关键字
            specific_words = config_manager.get('workflow_config.special_handling.keywords', [])
            # 创建聊天提示模板，用于识别特殊处理中是否包含指定词汇及含义相近的词汇
            template = ChatPromptTemplate(
                [
                    ("user", config_manager.get('prompt.generate_cstm_tool_prompts.identify_special_handling'))
                ],
                template_format="mustache"
            )

            # 计算使用语言模型前的token数量
            token_count_dict = CstmUtils.count_tokens_for_langchain(template, {
                "special_handling_text": special_handling_text,
                "specific_words": "\n".join(specific_words)
            })
            logger.info("获取变更信息tokens:", json.dumps(token_count_dict, indent=4, ensure_ascii=False))

            # 使用语言模型进行特殊处理的判断
            invoke_data = {
                "special_handling_text": special_handling_text,
                "specific_words": "\n".join(specific_words)
            }

            resp: AIMessage = get_ai_message_with_structured_output(
                template,
                invoke_data,
                HasSimilarTextFormat,
                llm_model=None
            )

            response = resp.result
            # 根据模型返回的结果决定是否需要特殊处理
            if response.lower() == 'true':
                return True
            else:
                return False
        else:
            # 如果没有特殊处理项，则不需要特殊处理
            return False
    except (IOError, ValueError, KeyError) as e:
        # 处理预期的异常
        logger.error(f"判断变更点是否需要特殊处理时发生预期异常: {e}", exc_info=True)
        return False
    except Exception as e:
        traceback.print_exc()
        # 处理未知异常
        logger.error(f"未知异常发生在获取变更点特殊处理逻辑中: {e}", exc_info=True)
        return False
