
demonstrations = [
{"变更点": "MET-G_CSTMLST-CSTD_SoC R_第0階層⇒第一階層へ移動 1500Wコンセント,2400Wコンセント,7200Wコンセント", "related_checkitems": [{"编号": "0", "是否相关": "是", "原因": "由于功能项从第0层级移动到第一层级，可能会影响这些功能在UI中的可用状态(灰显与否)，需要验证这些功能在新层级中的可用性。"}, {"编号": "1", "是否相关": "是", "原因": "变更点涉及模块层级的移动，这会直接影响图标在UI中的位置、颜色适配性和内容显示。因此，必须对图标的颜色、位置和内容进行检查，以确保用户界面的正确性、一致性和易用性。"}, {"编号": "2", "是否相关": "是", "原因": "这种层级变更虽然主要是结构性的，但会直接影响UI界面的文字布局，因此需要作为重要的视觉检查项进行验证。"}, {"编号": "3", "是否相关": "否", "原因": "与行数限制无关"}, {"编号": "4", "是否相关": "是", "原因": "这是针对非第一层级元素的检查要求，意味着所有不在第一层级的项目都需要进行标题核对。"}, {"编号": "5", "是否相关": "是", "原因": "这种变更通常需要额外检查亮度参数，因为菜单层级调整可能意外影响显示驱动的电压参数或背光配置，特别是在采用不同显示技术的区域混合使用时。"}, {"编号": "6", "是否相关": "是", "原因": "这种变更通常需要同时检查技术实现和文档一致性，确保软件变更不会影响用户界面(UI)的规范性和用户体验。"}, {"编号": "7", "是否相关": "是", "原因": "由于功能项从第0层级移动到第一层级，可能会影响这些功能在UI中的可用状态(灰显与否)，需要验证这些功能在新层级中的可用性。"}, {"编号": "8", "是否相关": "是", "原因": "该功能在行驶中的可用性从“明确允许”变为“条件性允许”，需根据业务逻辑（如安全规则）动态决定是否禁用（置灰），确保高功率充电等功能在行驶中仅在安全条件下可用。"}, {"编号": "9", "是否相关": "否", "原因": "与按键时长无关"}, {"编号": "10", "是否相关": "是", "原因": "该检查项是为了验证在SoC R移动到第一层级后，与功率选择相关的切换功能（包括UI效果和底层信号）仍然能正常工作，确保用户体验的一致性。"}, {"编号": "11", "是否相关": "是", "原因": "层级变更可能导致菜单导航路径变化，影响动画触发时机，状态管理逻辑需要重新评估，可能影响ON/OFF设置的持久性。"}, {"编号": "12", "是否相关": "否", "原因": "与特殊逻辑无关"}, {"编号": "13", "是否相关": "否", "原因": "与联动显示无关"}, {"编号": "14", "是否相关": "否", "原因": "与存储无关"}, {"编号": "15", "是否相关": "否", "原因": "与控制选项无关"}, {"编号": "16", "是否相关": "否", "原因": "与界面卡顿无关"}, {"编号": "17", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "18", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "19", "是否相关": "否", "原因": "与按键显示无关"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无关"}, {"编号": "21", "是否相关": "否", "原因": "与按键异常无关"}, {"编号": "22", "是否相关": "是", "原因": "这种层级移动可能影响了BACK按键的导航逻辑，需要验证在变更后的菜单结构中，BACK按键仍能正确返回到上一级菜单。"}, {"编号": "23", "是否相关": "是", "原因": "如果变更的插座控制模块与系统电源管理相关，可能影响MCU的运算资源分配，进而影响按键响应延迟；功能模块的层级调整可能使UI状态机更复杂，需要验证状态切换时的响应时间。"}, {"编号": "24", "是否相关": "否", "原因": "与按键响应无关"}, {"编号": "25", "是否相关": "否", "原因": "与按键响应无关"}, {"编号": "26", "是否相关": "否", "原因": "与灰显项目无关"}, {"编号": "27", "是否相关": "否", "原因": "与信号无关"}, {"编号": "28", "是否相关": "否", "原因": "与诊断清除无关"}, {"编号": "29", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "30", "是否相关": "否", "原因": "与信号显示无关"}, {"编号": "31", "是否相关": "否", "原因": "与信号切换无关"}, {"编号": "32", "是否相关": "否", "原因": "与信号切换无关"}, {"编号": "33", "是否相关": "否", "原因": "与信号显示无关"}, {"编号": "34", "是否相关": "否", "原因": "与信号跳变无关"}, {"编号": "35", "是否相关": "否", "原因": "与信号关联无关"}, {"编号": "36", "是否相关": "是", "原因": "当进行这种层级移动时，信号路径可能被重新配置或中断，系统需要确保在架构变更后仍能正确检测和显示信号中断状态。"}, {"编号": "37", "是否相关": "是", "原因": "架构层级变更可能导致信号路径长度变化，电源管理单元(PMU)与负载的新连接关系，故障检测电路的触发阈值需要重新验证。"}, {"编号": "38", "是否相关": "否", "原因": "与信号跳变无关"}, {"编号": "39", "是否相关": "否", "原因": "与出力时机无关"}, {"编号": "40", "是否相关": "否", "原因": "与项目数无关"}, {"编号": "41", "是否相关": "否", "原因": "与迁移动作无关"}, {"编号": "42", "是否相关": "否", "原因": "与计时打断无关"}, {"编号": "43", "是否相关": "否", "原因": "与迟滞曲线无关"}, {"编号": "44", "是否相关": "否", "原因": "与灰显无关"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无关"}, {"编号": "46", "是否相关": "否", "原因": "与默认选项无关"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无关"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无关"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无关"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无关"}, {"编号": "51", "是否相关": "否", "原因": "与返回现象无关"}, {"编号": "52", "是否相关": "否", "原因": "与迟滞曲线无关"}, {"编号": "53", "是否相关": "否", "原因": "与灰显出力无关"}, {"编号": "54", "是否相关": "否", "原因": "与单双眼切换无关"}, {"编号": "55", "是否相关": "否", "原因": "与走形置灰无关"}, {"编号": "56", "是否相关": "否", "原因": "与语言切换无关"}, {"编号": "57", "是否相关": "是", "原因": "电源模块所在层级的变更可能影响其状态保持机制，需要验证状态切换时（如IGN/BAT切换）是否能正确处理来自不同层级的请求。"}, {"编号": "58", "是否相关": "否", "原因": "与ST点火无关"}, {"编号": "59", "是否相关": "是", "原因": "\"BAT/IGN重启后显示是否正确\"是验证电源管理系统在架构变更后的关键功能"}, {"编号": "60", "是否相关": "否", "原因": "与高负载无关"}, {"编号": "61", "是否相关": "否", "原因": "与存储等待无关"}]},
{"变更点": "MET-G_CSTMLST-CSTD_SoC R_警報音量の階層修正", "related_checkitems": [{"编号": "0", "是否相关": "是", "原因": "该变更点可能通过层级逻辑调整间接影响UI控件的可用性状态，因此需通过“文言灰显一致性”检查确保交互合规性与用户体验统一性。"}, {"编号": "1", "是否相关": "是", "原因": "该变更点可能通过调整报警音量的触发逻辑或显示优先级，间接要求对关联图标的 颜色、位置和内容进行同步优化，以确保人机交互的一致性和安全性。"}, {"编号": "2", "是否相关": "是", "原因": "在进行警报音量层级修正后，必须检查文字排列以确保用户界面的可用性和美观性。"}, {"编号": "3", "是否相关": "否", "原因": "与行数限制无关"}, {"编号": "4", "是否相关": "否", "原因": "不涉及阶层移动"}, {"编号": "5", "是否相关": "是", "原因": "报警音量的调整可能伴随HMI界面更新（例如音量条显示、警告弹窗）。若音量层级变更导致告警触发逻辑变化（如不同级别告警对应不同显示效果），则需验证亮度一致性。"}, {"编号": "6", "是否相关": "是", "原因": "为了确保\"报警音量层级修正\"变更后，所有相关的文字描述、显示顺序或配置参数完全遵循规格说明书的要求，从而保证功能的正确性和人机交互的一致性。"}, {"编号": "7", "是否相关": "是", "原因": "该变更点可能通过层级逻辑调整间接影响UI控件的可用性状态，因此需通过“文言灰显一致性”检查确保交互合规性与用户体验统一性。"}, {"编号": "8", "是否相关": "否", "原因": "与走行状态无关"}, {"编号": "9", "是否相关": "否", "原因": "与按键时长无关"}, {"编号": "10", "是否相关": "是", "原因": "需检查新的音量层级调整是否影响切换动画的帧序列，验证各层级切换时的音频反馈与视觉动画的同步延迟。"}, {"编号": "11", "是否相关": "是", "原因": "警报音量的层级修正可能涉及设置菜单中音量控制逻辑的调整（例如音量级别的划分或默认值的修改）。"}, {"编号": "12", "是否相关": "否", "原因": "与特殊逻辑无关"}, {"编号": "13", "是否相关": "否", "原因": "与联动显示无关"}, {"编号": "14", "是否相关": "否", "原因": "与存储无关"}, {"编号": "15", "是否相关": "否", "原因": "与控制选项无关"}, {"编号": "16", "是否相关": "否", "原因": "与界面卡顿无关"}, {"编号": "17", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "18", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "19", "是否相关": "否", "原因": "与按键显示无关"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无关"}, {"编号": "21", "是否相关": "否", "原因": "与按键异常无关"}, {"编号": "22", "是否相关": "是", "原因": "这个检查项属于对系统基本功能的回归测试，确保变更没有破坏基本的导航功能，尽管变更点本身不直接涉及BACK键功能。"}, {"编号": "23", "是否相关": "是", "原因": "该变更可能涉及HMI层与功能层的接口修改，BACK键作为通用导航控制，需要验证其在不同层级设置界面（包括修改后的报警音量设置）中的一致性"}, {"编号": "24", "是否相关": "否", "原因": "与按键响应无关"}, {"编号": "25", "是否相关": "否", "原因": "与按键响应无关"}, {"编号": "26", "是否相关": "否", "原因": "与灰显项目无关"}, {"编号": "27", "是否相关": "否", "原因": "与信号无关"}, {"编号": "28", "是否相关": "否", "原因": "与诊断清除无关"}, {"编号": "29", "是否相关": "否", "原因": "与按键出力无关"}, {"编号": "30", "是否相关": "否", "原因": "与信号显示无关"}, {"编号": "31", "是否相关": "否", "原因": "与信号切换无关"}, {"编号": "32", "是否相关": "否", "原因": "与信号切换无关"}, {"编号": "33", "是否相关": "否", "原因": "与信号显示无关"}, {"编号": "34", "是否相关": "否", "原因": "与信号跳变无关"}, {"编号": "35", "是否相关": "否", "原因": "与信号关联无关"}, {"编号": "36", "是否相关": "是", "原因": "当SOC报警层级变更后，信号中断时是否仍能保持符合功能安全要求的显示状态（如强制显示最高级别警告）。"}, {"编号": "37", "是否相关": "是", "原因": "确保信号路径的变更未引入通信链路的脆弱性以及报警系统的故障恢复机制符合功能安全标准。"}, {"编号": "38", "是否相关": "否", "原因": "与信号跳变无关"}, {"编号": "39", "是否相关": "否", "原因": "与出力时机无关"}, {"编号": "40", "是否相关": "否", "原因": "与项目数无关"}, {"编号": "41", "是否相关": "否", "原因": "与迁移动作无关"}, {"编号": "42", "是否相关": "否", "原因": "与计时打断无关"}, {"编号": "43", "是否相关": "否", "原因": "与迟滞曲线无关"}, {"编号": "44", "是否相关": "否", "原因": "与灰显无关"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无关"}, {"编号": "46", "是否相关": "否", "原因": "与默认选项无关"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无关"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无关"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无关"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无关"}, {"编号": "51", "是否相关": "否", "原因": "与返回现象无关"}, {"编号": "52", "是否相关": "否", "原因": "与迟滞曲线无关"}, {"编号": "53", "是否相关": "否", "原因": "与灰显出力无关"}, {"编号": "54", "是否相关": "否", "原因": "与单双眼切换无关"}, {"编号": "55", "是否相关": "否", "原因": "与走形置灰无关"}, {"编号": "56", "是否相关": "否", "原因": "与语言切换无关"}, {"编号": "57", "是否相关": "是", "原因": "修改警报音量的层级后，需要确保新的音量设置能够在电源状态切换时正确保持。"}, {"编号": "58", "是否相关": "否", "原因": "与ST点火无关"}, {"编号": "59", "是否相关": "是", "原因": "当修改报警音量的层级参数后，需要特别验证新参数是否被正确写入ECU的NVM(非易失性存储器)以及系统重新初始化时能否正确读取修改后的配置。"}, {"编号": "60", "是否相关": "否", "原因": "与高负载无关"}, {"编号": "61", "是否相关": "否", "原因": "与存储等待无关"}]},
{"变更点": "MET-G_CSTMLST-CSTD_SoC R_「表示シーン拡張」を追加", "related_checkitems": [{"编号": "0", "是否相关": "是", "原因": "新增的\"表示シーン拡張\"功能可能涉及在用户界面中添加新的显示场景或状态"}, {"编号": "1", "是否相关": "是", "原因": "变更点“添加显示场景扩展”通常涉及新增或修改显示元素（如图标），因此需要对这些图标的颜色、位置和内容进行检查，以确保显示的正确性、一致性和用户体验。"}, {"编号": "2", "是否相关": "是", "原因": "\"显示场景扩展\"功能的添加意味着系统中会增加新的显示场景或界面，新的显示场景必然涉及文本内容的布局和排列"}, {"编号": "3", "是否相关": "是", "原因": "变更点（新增显示场景）直接触发了对文本行数限制的检查，属于功能实现与设计约束的合规性验证。"}, {"编号": "4", "是否相关": "是", "原因": "变更点中提到的 CSTD_SoC R 可能是次级模块（非第一阶层），因此需触发检查项中的“标题核对”流程。"}, {"编号": "5", "是否相关": "是", "原因": "新增UI元素与既有显示内容的亮度协调性"}, {"编号": "6", "是否相关": "是", "原因": "新增场景中涉及的文本元素（如弹窗提示、状态栏信息等）必须按照式样书规定的顺序排列"}, {"编号": "7", "是否相关": "是", "原因": "新增的\"表示シーン拡張\"功能可能涉及在用户界面中添加新的显示场景或状态"}, {"编号": "8", "是否相关": "是", "原因": "这些新增的显示或控制功能可能与自动驾驶相关，因此在手动驾驶时不需要使用，但无需刻意置灰（可能默认不显示或保持原样）。"}, {"编号": "9", "是否相关": "是", "原因": "新显示场景可能改变系统响应时序，需要确保物理操作与视觉反馈的同步性"}, {"编号": "10", "是否相关": "是", "原因": "新增的“表示场景扩展”功能需要支持通过选项ON/OFF的切换，因此检查项是为了验证这一切换功能的正确性和显示效果。"}, {"编号": "11", "是否相关": "是", "原因": "显示场景的扩展可能引入了新的UI状态切换逻辑"}, {"编号": "12", "是否相关": "否", "原因": "无任何关联"}, {"编号": "13", "是否相关": "是", "原因": "新添加的场景扩展功能可能影响SOC电量在多个显示位置的呈现方式，需要验证新增功能是否与现有的调光系统（包括HUD调光）正确联动。"}, {"编号": "14", "是否相关": "是", "原因": "新增显示场景可能涉及存储在ERROM中的某些参数"}, {"编号": "15", "是否相关": "否", "原因": "与SOC无直接关系"}, {"编号": "16", "是否相关": "否", "原因": "与界面切换无直接关系"}, {"编号": "17", "是否相关": "是", "原因": "变更若涉及按键事件处理逻辑（如新增中断响应或信号路由），需确保硬件层（出力信号）与软件层（场景响应）的兼容性。"}, {"编号": "18", "是否相关": "是", "原因": "显示场景扩展可能引入了新的状态机分支"}, {"编号": "19", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无直接关系"}, {"编号": "21", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "22", "是否相关": "是", "原因": "新增的显示场景可能改变了界面层级结构，需确保BACK按键在长按操作时能正确返回上级菜单"}, {"编号": "23", "是否相关": "是", "原因": "新添加的\"显示场景扩展\"功能可能引入了新的界面或显示层级，这会影响BACK按键的行为逻辑。"}, {"编号": "24", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "25", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "26", "是否相关": "是", "原因": "为了确保新增的显示场景扩展功能没有引入意外的交互行为，特别是对界面中禁用状态项目的处理逻辑。"}, {"编号": "27", "是否相关": "是", "原因": "这项检查项是针对新添加显示场景功能的基础验证，确保功能能根据系统信号状态正确显示或隐藏"}, {"编号": "28", "是否相关": "是", "原因": "变更可能引入了新的功能状态或显示逻辑，需确认诊断清除操作不会破坏这些状态的同步性或一致性。"}, {"编号": "29", "是否相关": "是", "原因": "新增的显示场景可能需要特定的按键操作来触发或切换，需要确保在扩展场景下按键功能仍然正常工作。"}, {"编号": "30", "是否相关": "是", "原因": "该检查项专门用于验证变更点中新增的显示场景功能是否能正确接收信号输入以及按规格要求实现可视化输出。"}, {"编号": "31", "是否相关": "是", "原因": "新功能的加入可能影响原有选项框的切换逻辑"}, {"编号": "32", "是否相关": "是", "原因": "需要验证新增显示场景扩展功能后，界面在边界条件下的操作正确性和交互连续性，确保新增功能不会影响原有的界面切换和数据通信机制。"}, {"编号": "33", "是否相关": "是", "原因": "需要通过测试验证新增的显示功能是否能正确响应输入信号"}, {"编号": "34", "是否相关": "是", "原因": "新增了一个显示场景扩展功能，可能会引入新的信号或修改信号发送逻辑，需要验证新增或修改的信号是否会无意中影响其他功能的可用性或状态（灰显或跳变）。"}, {"编号": "35", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "36", "是否相关": "是", "原因": "显示扩展可能新增了信号状态的可视化场景，需要确保新增的显示逻辑能正确处理信号丢失等异常情况。"}, {"编号": "37", "是否相关": "是", "原因": "新增显示场景后需验证复归逻辑是否与新增场景的状态机正确同步"}, {"编号": "38", "是否相关": "是", "原因": "当新增显示场景扩展功能时，需要验证选项标志状态机是否与原有逻辑兼容"}, {"编号": "39", "是否相关": "是", "原因": "新增功能（显示场景扩展）必须通过输出时机的验证来保证其正确性和可靠性。"}, {"编号": "40", "是否相关": "否", "原因": "与页面项目数无直接关系"}, {"编号": "41", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "42", "是否相关": "否", "原因": "与计时无直接关系"}, {"编号": "43", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "44", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无直接关系"}, {"编号": "46", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无直接关系"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无直接关系"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无直接关系"}, {"编号": "51", "是否相关": "否", "原因": "与返回无直接关系"}, {"编号": "52", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "53", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "54", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "55", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "56", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "57", "是否相关": "是", "原因": "新增的显示场景可能需要支持在IGN（点火）或BAT（电池）等不同电源状态下的显示。"}, {"编号": "58", "是否相关": "否", "原因": "与异常测试无直接关系"}, {"编号": "59", "是否相关": "是", "原因": "新增了对显示场景的扩展支持，可能引入了新的显示模式或动态内容，因此需要验证车辆电源重启（BAT/IGN重启）后能否正确加载和显示。"}, {"编号": "60", "是否相关": "否", "原因": "与负载无直接关系"}, {"编号": "61", "是否相关": "是", "原因": "新功能可能需要在车辆电源状态变化时（如IG OFF/+B OFF）正确处理显示场景的保存和恢复"}]},
{"变更点": "MET-G_CSTMLST-CSTD_SoC A_Speed Limiter　運転状態により自動起動　が削除", "related_checkitems": [{"编号": "0", "是否相关": "否", "原因": "与显示内容无直接关系"}, {"编号": "1", "是否相关": "否", "原因": "与图标显示无直接关系"}, {"编号": "2", "是否相关": "否", "原因": "与文字排列无直接关系"}, {"编号": "3", "是否相关": "否", "原因": "与文字行数无直接关系"}, {"编号": "4", "是否相关": "否", "原因": "与阶层移动无关"}, {"编号": "5", "是否相关": "否", "原因": "与亮度无直接关系"}, {"编号": "6", "是否相关": "否", "原因": "与文字排列无直接关系"}, {"编号": "7", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "8", "是否相关": "否", "原因": "与走行状态无直接关系"}, {"编号": "9", "是否相关": "否", "原因": "与按键时长无直接关系"}, {"编号": "10", "是否相关": "是", "原因": "需要确保以前与自动启动相关的界面效果（如自动启用时的动画）不再出现，避免用户混淆。"}, {"编号": "11", "是否相关": "是", "原因": "当删除一个与驾驶状态联动的自动启动功能时，需要特别验证设置界面可能存在的残留控制逻辑和相关开关状态（ON/OFF设置可能发生非预期变化）"}, {"编号": "12", "是否相关": "否", "原因": "与特殊逻辑无直接关系"}, {"编号": "13", "是否相关": "否", "原因": "与联动显示无直接关系"}, {"编号": "14", "是否相关": "否", "原因": "与存储无直接关系"}, {"编号": "15", "是否相关": "否", "原因": "与SOC无直接关系"}, {"编号": "16", "是否相关": "否", "原因": "与界面切换无直接关系"}, {"编号": "17", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "18", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "19", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无直接关系"}, {"编号": "21", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "22", "是否相关": "是", "原因": "删除功能可能导致界面或状态管理逻辑变化，从而影响“BACK”按键的行为，因此需要验证。"}, {"编号": "23", "是否相关": "是", "原因": "被删除的速度限制器自动启动功能可能与BACK按键的响应逻辑存在交互。删除该功能后，需要确认BACK按键的响应不受影响。"}, {"编号": "24", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "25", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "26", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "27", "是否相关": "是", "原因": "由于删除了自动启动功能，需要确认相关信号是否被正确处理"}, {"编号": "28", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "29", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "30", "是否相关": "是", "原因": "当\"Speed Limiter自动起動\"功能被删除后，需验证信号发送后HMI显示是否符合更新后的式样（不应再显示已删除的功能状态），以及相关DTC（诊断故障码）是否同步更新。"}, {"编号": "31", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "32", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "33", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "34", "是否相关": "是", "原因": "删除“根据驾驶状态自动启动”功能后，需要验证原本与驾驶状态相关的信号是否仍然会影响其他选项的状态。"}, {"编号": "35", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "36", "是否相关": "是", "原因": "当删除自动启动功能后，需要确认原功能相关的信号传输路径是否被正确禁用。"}, {"编号": "37", "是否相关": "是", "原因": "当删除自动启动功能后，需要特别检查原功能相关的信号路径是否被完全禁用。"}, {"编号": "38", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "39", "是否相关": "否", "原因": "与出力时机无直接关系"}, {"编号": "40", "是否相关": "否", "原因": "与页面项目数无直接关系"}, {"编号": "41", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "42", "是否相关": "否", "原因": "与计时无直接关系"}, {"编号": "43", "是否相关": "是", "原因": "当速度限制器的自动启动逻辑被删除后，需要验证灰显状态是否符合新的设计规范以及曲线显示逻辑是否与删除的功能完全解耦。"}, {"编号": "44", "是否相关": "是", "原因": "删除“自动启动”功能后，需要验证与之相关的界面显示（选项、灰显状态）和辅助文本是否正确调整，避免出现无效或误导性信息。"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无直接关系"}, {"编号": "46", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无直接关系"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无直接关系"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无直接关系"}, {"编号": "51", "是否相关": "否", "原因": "与返回无直接关系"}, {"编号": "52", "是否相关": "是", "原因": "删除自动启动功能后，速度限制器的控制逻辑完全变为手动操作，需要检查这种变更是否会影响界面选项的可操作性"}, {"编号": "53", "是否相关": "是", "原因": "功能删除后，需确保其相关输出信号在灰显状态下也被完全禁用。"}, {"编号": "54", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "55", "是否相关": "是", "原因": "由于速度限制器的自动启动功能被删除，在设定界面中对应的选项应该被置灰或移除"}, {"编号": "56", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "57", "是否相关": "否", "原因": "与电源状态无直接关系"}, {"编号": "58", "是否相关": "否", "原因": "与异常测试无直接关系"}, {"编号": "59", "是否相关": "否", "原因": "与重启无直接关系"}, {"编号": "60", "是否相关": "否", "原因": "与负载无直接关系"}, {"编号": "61", "是否相关": "是", "原因": "被删除的自动启动逻辑可能原先与ECU的非易失性存储(NVM)相关，需要确认功能配置位的删除是否在EEPROM/Flash中正确生效。"}]},
{"变更点": "MET-G_CSTMLST-CSTD_SoC R_1500Wコンセント的メータEEPROM記憶由\"变为“する”", "related_checkitems": [{"编号": "0", "是否相关": "否", "原因": "与显示内容无直接关系"}, {"编号": "1", "是否相关": "否", "原因": "与图标显示无直接关系"}, {"编号": "2", "是否相关": "否", "原因": "与文字排列无直接关系"}, {"编号": "3", "是否相关": "否", "原因": "与文字行数无直接关系"}, {"编号": "4", "是否相关": "否", "原因": "与阶层移动无关"}, {"编号": "5", "是否相关": "否", "原因": "与亮度无直接关系"}, {"编号": "6", "是否相关": "否", "原因": "与文字排列无直接关系"}, {"编号": "7", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "8", "是否相关": "否", "原因": "与走行状态无直接关系"}, {"编号": "9", "是否相关": "否", "原因": "与按键时长无直接关系"}, {"编号": "10", "是否相关": "否", "原因": "与切替动画无直接关系"}, {"编号": "11", "是否相关": "否", "原因": "与动画无直接关系"}, {"编号": "12", "是否相关": "否", "原因": "与特殊逻辑无直接关系"}, {"编号": "13", "是否相关": "否", "原因": "与联动显示无直接关系"}, {"编号": "14", "是否相关": "是", "原因": "确保从\"不存储\"到\"存储\"的变更确实生效"}, {"编号": "15", "是否相关": "否", "原因": "与SOC无直接关系"}, {"编号": "16", "是否相关": "否", "原因": "与界面切换无直接关系"}, {"编号": "17", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "18", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "19", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无直接关系"}, {"编号": "21", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "22", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "23", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "24", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "25", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "26", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "27", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "28", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "29", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "30", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "31", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "32", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "33", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "34", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "35", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "36", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "37", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "38", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "39", "是否相关": "否", "原因": "与出力时机无直接关系"}, {"编号": "40", "是否相关": "否", "原因": "与页面项目数无直接关系"}, {"编号": "41", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "42", "是否相关": "否", "原因": "与计时无直接关系"}, {"编号": "43", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "44", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无直接关系"}, {"编号": "46", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无直接关系"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无直接关系"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无直接关系"}, {"编号": "51", "是否相关": "否", "原因": "与返回无直接关系"}, {"编号": "52", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "53", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "54", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "55", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "56", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "57", "是否相关": "否", "原因": "与电源状态无直接关系"}, {"编号": "58", "是否相关": "否", "原因": "与异常测试无直接关系"}, {"编号": "59", "是否相关": "否", "原因": "与重启无直接关系"}, {"编号": "60", "是否相关": "否", "原因": "与负载无直接关系"}, {"编号": "61", "是否相关": "是", "原因": "系统需要在检测到IG/+B OFF信号后维持电源足够长时间以完成写入，这个等待时间需要经过严格验证以确保在各种条件下都能可靠工作。"}]},
{"变更点": "MET-G_CSTMLST-CSTD_SoC A_文言No.修正\n\n充電スケジュール【#1856】　→　【#5945】\n外部給電【#1975】　→　【#5813】\n±0【#2629】　→　0【#6110】\n先行車発進告知【#680】　→　【#6123】\n◎：決定【#420】　→　◎：設定を変更できます【#496】", "related_checkitems": [{"编号": "0", "是否相关": "是", "原因": "变更点需通过\"文言灰显一致性\"检查，以保证界面文本更新后，其显示状态（尤其是灰显逻辑）符合功能设计要求。"}, {"编号": "1", "是否相关": "否", "原因": "与图标显示无直接关系"}, {"编号": "2", "是否相关": "是", "原因": "此变更点需优先执行文字排列检查，以确保界面可读性与用户体验一致性，避免因文本长度、ID格式或术语修改导致的显示异常。"}, {"编号": "3", "是否相关": "是", "原因": "这个变更需要执行\"文言的行数最大值\"检查，以确保所有修改后的文本在不同显示条件下都能符合式样书规定的行数限制要求。"}, {"编号": "4", "是否相关": "是", "原因": "涉及阶层移动的标题显示"}, {"编号": "5", "是否相关": "是", "原因": "文言编号的变更可能伴随文本内容、格式或显示规则的调整，需要确保新文言在亮度显示上与原系统保持一致。"}, {"编号": "6", "是否相关": "是", "原因": "变更点中的文言编号修正必须与检查项的“排列顺序”要求联动验证，确保文本内容更新后，其显示顺序仍严格符合规格书定义。"}, {"编号": "7", "是否相关": "是", "原因": "变更点需通过\"文言灰显一致性\"检查，以保证界面文本更新后，其显示状态（尤其是灰显逻辑）符合功能设计要求。"}, {"编号": "8", "是否相关": "否", "原因": "与走行状态无直接关系"}, {"编号": "9", "是否相关": "否", "原因": "与按键时长无直接关系"}, {"编号": "10", "是否相关": "否", "原因": "与切替动画无直接关系"}, {"编号": "11", "是否相关": "否", "原因": "与动画无直接关系"}, {"编号": "12", "是否相关": "否", "原因": "与特殊逻辑无直接关系"}, {"编号": "13", "是否相关": "否", "原因": "与联动显示无直接关系"}, {"编号": "14", "是否相关": "否", "原因": "与存储无直接关系"}, {"编号": "15", "是否相关": "否", "原因": "与SOC无直接关系"}, {"编号": "16", "是否相关": "否", "原因": "与界面切换无直接关系"}, {"编号": "17", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "18", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "19", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "20", "是否相关": "否", "原因": "与循环显示无直接关系"}, {"编号": "21", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "22", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "23", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "24", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "25", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "26", "是否相关": "否", "原因": "与按键无直接关系"}, {"编号": "27", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "28", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "29", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "30", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "31", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "32", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "33", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "34", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "35", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "36", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "37", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "38", "是否相关": "否", "原因": "与信号无直接关系"}, {"编号": "39", "是否相关": "否", "原因": "与出力时机无直接关系"}, {"编号": "40", "是否相关": "否", "原因": "与页面项目数无直接关系"}, {"编号": "41", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "42", "是否相关": "否", "原因": "与计时无直接关系"}, {"编号": "43", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "44", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "45", "是否相关": "否", "原因": "与语言单位无直接关系"}, {"编号": "46", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "47", "是否相关": "否", "原因": "与互斥性无直接关系"}, {"编号": "48", "是否相关": "否", "原因": "与数值上限无直接关系"}, {"编号": "49", "是否相关": "否", "原因": "与初始化无直接关系"}, {"编号": "50", "是否相关": "否", "原因": "与界面打断无直接关系"}, {"编号": "51", "是否相关": "否", "原因": "与返回无直接关系"}, {"编号": "52", "是否相关": "否", "原因": "与迟滞曲线无直接关系"}, {"编号": "53", "是否相关": "否", "原因": "与灰显无直接关系"}, {"编号": "54", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "55", "是否相关": "否", "原因": "与切替无直接关系"}, {"编号": "56", "是否相关": "否", "原因": "与切换无直接关系"}, {"编号": "57", "是否相关": "否", "原因": "与电源状态无直接关系"}, {"编号": "58", "是否相关": "否", "原因": "与异常测试无直接关系"}, {"编号": "59", "是否相关": "是", "原因": "这些SOC相关的显示变更需要在电源重启(BAT/IGN重启)后保持正确"}, {"编号": "60", "是否相关": "否", "原因": "与负载无直接关系"}, {"编号": "61", "是否相关": "否", "原因": "与存储无直接关系"}]}
]