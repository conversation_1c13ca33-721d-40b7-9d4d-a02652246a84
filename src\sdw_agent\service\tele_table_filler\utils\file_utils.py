"""
文件操作工具模块

V字对应：
4.4 对应的V字阶段
64. 对应的V字项目


模块简介：提供文件路径处理、调试报告保存等功能。

主要功能：
1. 文本标准化
2. 调试信息添加和报告保存
"""


import os
from datetime import datetime
from typing import List
from loguru import logger


def normalize_text(text: str) -> str:
    """
    标准化文本：去除多余空格、换行，转换为半角字符、英文括号并转为小写，最终去掉所有空格

    Args:
        text: 输入文本

    Returns:
        str: 标准化后的文本
    """
    if not text:
        return ""
    str_text = str(text).strip()
    str_text = str_text.replace('\n', ' ').replace('\r', ' ')
    str_text = str_text.replace('　', ' ')
    str_text = str_text.replace('（', '(').replace('）', ')')
    str_text = ' '.join(str_text.split())
    str_text = str_text.lower()
    str_text = str_text.replace(' ', '')
    return str_text


def add_debug_info(debug_report: List[str], message: str, is_error: bool = False, workflow_logger: logger = None):
    """
    添加调试信息到报告

    Args:
        debug_report: 调试报告列表
        message: 消息内容
        is_error: 是否为错误
        workflow_logger: 日志器
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    entry = f"[{timestamp}] {'错误' if is_error else '信息'}: {message}"
    debug_report.append(entry)
    if workflow_logger:
        if is_error:
            workflow_logger.error(message)
        else:
            workflow_logger.info(message)


def save_debug_report(debug_report: List[str], file_path: str) -> str:
    """
    保存调试报告到文件

    Args:
        debug_report: 调试报告列表
        file_path: 关联文件路径

    Returns:
        str: 报告路径
    """
    name = os.path.splitext(os.path.basename(file_path))[0]
    report_filename = f"{name}_debug_report.txt"
    report_path = os.path.join(os.path.dirname(file_path), 'debug', report_filename)
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("表格填充工具调试报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理文件: {file_path}\n\n")
        # 添加更多统计如原代码，但简化
        f.write("\n".join(debug_report))
    return report_path