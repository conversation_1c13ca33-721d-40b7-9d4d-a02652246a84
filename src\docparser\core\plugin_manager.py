import os
import importlib.util
import logging
from typing import Dict, List, Any, Optional

from docparser.interfaces.plugin_interface import PluginInterface
from docparser.models.document import DocumentObject

# Configure logging
logger = logging.getLogger('docparser')

class PluginManager:
    """
    Manager for document parser plugins.
    Handles plugin discovery, loading, and management.
    """
    
    def __init__(self):
        self.plugins: Dict[str, PluginInterface] = {}
    
    def register_plugin(self, plugin: PluginInterface) -> None:
        """
        Register a plugin.
        
        Args:
            plugin: Plugin instance implementing PluginInterface
        """
        plugin_name = plugin.get_plugin_name()
        if plugin_name in self.plugins:
            logger.warning(f"Plugin {plugin_name} already registered. Overwriting.")
        
        self.plugins[plugin_name] = plugin
        logger.info(f"Registered plugin: {plugin_name} v{plugin.get_plugin_version()}")
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """
        Get a registered plugin by name.
        
        Args:
            plugin_name: Name of the plugin
            
        Returns:
            Plugin instance or None if not found
        """
        return self.plugins.get(plugin_name)
    
    def get_plugins(self) -> List[PluginInterface]:
        """
        Get all registered plugins.
        
        Returns:
            List of plugin instances
        """
        return list(self.plugins.values())
    
    def discover_plugins(self, plugins_dir: str) -> None:
        """
        Discover and load plugins from a directory.
        
        Args:
            plugins_dir: Directory containing plugin modules
        """
        if not os.path.exists(plugins_dir):
            logger.warning(f"Plugins directory not found: {plugins_dir}")
            return
        
        logger.info(f"Discovering plugins in: {plugins_dir}")
        
        for filename in os.listdir(plugins_dir):
            if not filename.endswith('.py') or filename.startswith('_'):
                continue
            
            plugin_path = os.path.join(plugins_dir, filename)
            try:
                self._load_plugin_from_file(plugin_path)
            except Exception as e:
                logger.error(f"Error loading plugin from {plugin_path}: {e}")
    
    def _load_plugin_from_file(self, plugin_path: str) -> None:
        """
        Load a plugin from a Python file.
        
        Args:
            plugin_path: Path to the plugin Python file
        """
        try:
            module_name = os.path.basename(plugin_path).replace('.py', '')
            spec = importlib.util.spec_from_file_location(module_name, plugin_path)
            if spec is None or spec.loader is None:
                logger.error(f"Could not load spec for plugin: {plugin_path}")
                return
                
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find plugin classes in the module
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, PluginInterface) and 
                    attr is not PluginInterface):
                    
                    # Create plugin instance
                    plugin = attr()
                    self.register_plugin(plugin)
                    
        except Exception as e:
            logger.error(f"Error loading plugin from {plugin_path}: {e}")
            raise
    
    def apply_plugins_to_document(self, document_data: DocumentObject):
        """
        Apply all registered plugins to a document.
        
        Args:
            document_data: Dictionary containing document data
            
        Returns:
            Processed document data
        """
        if not self.plugins:
            return document_data
        
        document_type = document_data.file_type

        for plugin_name, plugin in self.plugins.items():
            logger.info(f"Applying plugin: {plugin.get_plugin_name()}")

            # Check if plugin supports this document type
            if document_type not in plugin.get_supported_document_types():
                logger.warning(
                    f"Plugin {plugin.get_plugin_name()} does not support {document_type} documents")
                continue
            # Process document with plugin
            plugin.process_document(document_data)

            logger.info(f"Plugin applied successfully: {plugin.get_plugin_name()}")
