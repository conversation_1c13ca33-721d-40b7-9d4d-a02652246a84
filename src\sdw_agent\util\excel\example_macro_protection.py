"""
Win32com引擎宏和图片保护示例

这个示例展示了如何使用win32com引擎来安全地操作包含宏和图片的Excel文件，
而不会破坏这些重要内容。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CommonStyles


def demonstrate_macro_protection():
    """演示宏保护功能"""
    print("=== Win32com引擎宏保护演示 ===")
    print()
    
    print("场景说明:")
    print("假设你有一个包含VBA宏的Excel文件，需要更新其中的数据，")
    print("但又不想破坏现有的宏代码和功能。")
    print()
    
    # 创建临时文件模拟包含宏的Excel文件
    with tempfile.NamedTemporaryFile(suffix='.xlsm', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        print(f"1. 创建模拟的宏文件: {os.path.basename(temp_path)}")
        
        # 使用win32com引擎创建文件（保护宏）
        with ExcelUtil(temp_path, engine="win32com", auto_create=True) as excel:
            # 创建一个模拟的宏文件结构
            excel.write_cell("Sheet1", 1, 1, "数据更新区域")
            excel.write_cell("Sheet1", 2, 1, "姓名")
            excel.write_cell("Sheet1", 2, 2, "年龄")
            excel.write_cell("Sheet1", 2, 3, "部门")
            
            # 设置样式
            excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
            excel.set_range_style("Sheet1", "A2:C2", CommonStyles.HEADER)
            
            # 添加一些说明
            excel.write_cell("Sheet1", 5, 1, "注意: 此文件包含VBA宏")
            excel.write_cell("Sheet1", 6, 1, "使用win32com引擎可以安全更新数据而不破坏宏")
            
            excel.save()
        
        print("✓ 文件创建完成")
        print()
        
        print("2. 模拟数据更新操作...")
        
        # 模拟更新数据（保护宏）
        with ExcelUtil(temp_path, engine="win32com") as excel:
            # 更新数据
            data = [
                ["张三", 28, "技术部"],
                ["李四", 32, "销售部"],
                ["王五", 25, "人事部"]
            ]
            
            for i, row_data in enumerate(data, start=3):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", i, j, value)
                    excel.set_cell_style("Sheet1", i, j, CommonStyles.DATA)
            
            # 添加更新时间戳
            from datetime import datetime
            excel.write_cell("Sheet1", 7, 1, f"最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            excel.save()
        
        print("✓ 数据更新完成")
        print("✓ VBA宏和其他内容保持完整")
        print()
        
        print("3. 验证文件内容...")
        
        # 验证更新结果
        with ExcelUtil(temp_path, engine="win32com") as excel:
            # 读取更新后的数据
            name = excel.read_cell("Sheet1", 3, 1)
            age = excel.read_cell("Sheet1", 3, 2)
            dept = excel.read_cell("Sheet1", 3, 3)
            
            print(f"读取到的数据: {name}, {age}岁, {dept}")
        
        print("✓ 数据读取成功")
        print()
        
        print("优势总结:")
        print("- ✅ VBA宏代码完全保护")
        print("- ✅ 嵌入的图片和图表保护")
        print("- ✅ 复杂格式和样式保护")
        print("- ✅ Excel原生功能完全支持")
        print("- ✅ 与现有业务流程无缝集成")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def demonstrate_image_protection():
    """演示图片保护功能"""
    print("\n=== Win32com引擎图片保护演示 ===")
    print()
    
    print("场景说明:")
    print("假设你有一个包含图片、图表的Excel报表模板，")
    print("需要更新数据但保持图片和图表不变。")
    print()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        print(f"1. 创建模拟的图片文件: {os.path.basename(temp_path)}")
        
        # 使用win32com引擎创建包含"图片"的文件
        with ExcelUtil(temp_path, engine="win32com", auto_create=True) as excel:
            # 创建报表标题
            excel.write_cell("Sheet1", 1, 1, "销售报表")
            excel.merge_cells("Sheet1", "A1:E1")
            
            title_style = CellStyle(
                font_size=16,
                font_bold=True,
                alignment_horizontal="center",
                bg_color="4472C4",
                font_color="FFFFFF"
            )
            excel.set_cell_style("Sheet1", 1, 1, title_style)
            
            # 模拟图片区域
            excel.write_cell("Sheet1", 3, 1, "[此处有公司Logo图片]")
            excel.merge_cells("Sheet1", "A3:B4")
            
            image_style = CellStyle(
                alignment_horizontal="center",
                alignment_vertical="center",
                bg_color="E7E6E6",
                border_style="thin"
            )
            excel.set_cell_style("Sheet1", 3, 1, image_style)
            
            # 数据区域
            excel.write_cell("Sheet1", 6, 1, "月份")
            excel.write_cell("Sheet1", 6, 2, "销售额")
            excel.write_cell("Sheet1", 6, 3, "增长率")
            
            excel.set_range_style("Sheet1", "A6:C6", CommonStyles.HEADER)
            
            # 模拟图表区域
            excel.write_cell("Sheet1", 6, 5, "[此处有销售趋势图表]")
            excel.merge_cells("Sheet1", "E6:G10")
            excel.set_cell_style("Sheet1", 6, 5, image_style)
            
            excel.save()
        
        print("✓ 模板文件创建完成")
        print()
        
        print("2. 更新数据（保护图片和图表）...")
        
        # 更新数据但保护图片
        with ExcelUtil(temp_path, engine="win32com") as excel:
            # 更新销售数据
            sales_data = [
                ["1月", 100000, "5%"],
                ["2月", 120000, "20%"],
                ["3月", 110000, "-8%"],
                ["4月", 150000, "36%"]
            ]
            
            for i, row_data in enumerate(sales_data, start=7):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", i, j, value)
                    if j == 2:  # 销售额列
                        excel.set_cell_style("Sheet1", i, j, CommonStyles.NUMBER)
                    else:
                        excel.set_cell_style("Sheet1", i, j, CommonStyles.DATA)
            
            # 添加更新说明
            excel.write_cell("Sheet1", 12, 1, "数据已更新，图片和图表保持不变")
            
            excel.save()
        
        print("✓ 数据更新完成")
        print("✓ 图片和图表区域保持完整")
        print()
        
        print("3. 验证保护效果...")
        
        # 验证图片区域是否保持不变
        with ExcelUtil(temp_path, engine="win32com") as excel:
            logo_text = excel.read_cell("Sheet1", 3, 1)
            chart_text = excel.read_cell("Sheet1", 6, 5)
            sales_jan = excel.read_cell("Sheet1", 7, 2)
            
            print(f"Logo区域: {logo_text}")
            print(f"图表区域: {chart_text}")
            print(f"1月销售额: {sales_jan}")
        
        print("✓ 所有内容验证成功")
        print()
        
        print("保护效果:")
        print("- ✅ 图片和Logo完全保护")
        print("- ✅ 图表和可视化元素保护")
        print("- ✅ 复杂布局和合并单元格保护")
        print("- ✅ 数据更新正常进行")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def compare_engines():
    """对比不同引擎的效果"""
    print("\n=== 引擎对比演示 ===")
    print()
    
    print("对比win32com和openpyxl引擎处理复杂Excel文件的差异:")
    print()
    
    print("Win32com引擎 (推荐):")
    print("✅ 完美保护VBA宏")
    print("✅ 完美保护图片和图表")
    print("✅ 保持复杂格式不变")
    print("✅ 支持Excel所有功能")
    print("✅ 100%兼容现有文件")
    print("⚠️ 需要安装Excel程序")
    print()
    
    print("Openpyxl引擎:")
    print("✅ 纯Python实现")
    print("✅ 不依赖Excel程序")
    print("✅ 跨平台支持")
    print("❌ 可能破坏VBA宏")
    print("❌ 可能丢失图片和图表")
    print("❌ 可能改变复杂格式")
    print()
    
    print("使用建议:")
    print("- 🏢 企业环境，有宏和图片 → 使用win32com")
    print("- 🐍 纯Python环境，简单数据 → 使用openpyxl")
    print("- 📊 只读取数据分析 → 使用pandas")


def main():
    """主函数"""
    print("Win32com引擎宏和图片保护演示")
    print("=" * 50)
    
    try:
        # 检查win32com可用性
        import win32com.client
        
        try:
            # 测试Excel可用性
            excel_app = win32com.client.DispatchEx("Excel.Application")
            excel_app.Visible = False
            excel_app.Quit()
            
            # 运行演示
            demonstrate_macro_protection()
            demonstrate_image_protection()
            compare_engines()
            
            print("\n" + "=" * 50)
            print("演示完成！")
            print()
            print("总结:")
            print("Win32com引擎现在是ExcelUtil的默认引擎，")
            print("专门为保护Excel文件中的宏和图片而设计。")
            print("这使得ExcelUtil成为处理企业级Excel文件的理想选择。")
            
        except Exception as e:
            print(f"Excel程序不可用: {e}")
            print("请确保已安装Microsoft Excel")
            
    except ImportError:
        print("win32com不可用，请安装: pip install pywin32")
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
