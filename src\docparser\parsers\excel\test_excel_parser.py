# -*- coding: utf-8 -*-
"""
@File    : test_excel_parser.py
<AUTHOR> zhenp
@Date    : 2025-05-21 15:22
@Desc    : Description of the file
"""
import json
import os

from docparser.parsers.excel.excel import ExcelParser

# AI generation start

def test_excel_1220():
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/1220演示数据变更前.xlsx")
    file_path = os.path.normpath(file_path)

    parser = ExcelParser()
    res = parser.parse_document(file_path)

    expect_dir = os.path.join(os.path.dirname(file_path), "excel_results")
    for doc in res._document:
        file_name = doc.file_name.split(".")[0]
        json_file_name = f"{file_name} {doc._name}.json"
        full_path = os.path.join(expect_dir, json_file_name)
        if not os.path.exists(full_path):
            print(f"File '{json_file_name}' not found in directory '{expect_dir}'.")
            return None
        with open(full_path, 'r', encoding='utf-8') as f:
            dict_res = json.load(f)
            del dict_res["_elements"]
            # 原输出结果 layout 信息为空，新修改的有layout信息,比较是跳过这个字段
            # assert doc.to_dict() == remove_underscores(dict_res)
            res = deep_compare_dicts(remove_underscores(dict_res), doc.to_dict())
            assert res == True


def deep_compare_dicts(dict1: dict, dict2: dict) -> bool:
    """
    Recursively compare two dictionaries to check if their values are equal.

    Args:
        dict1 (dict): First dictionary.
        dict2 (dict): Second dictionary.

    Returns:
        bool: True if both dictionaries are deeply equal, False otherwise.
    """
    if type(dict1) != type(dict2):  # Compare types
        return False

    if isinstance(dict1, dict):  # If both are dictionaries
        for key in dict1:  # Compare values for each key recursively
            if key == "layout":
                return True
            if not deep_compare_dicts(dict1[key], dict2[key]):
                return False
    elif isinstance(dict1, list):  # If both are lists
        if len(dict1) != len(dict2):  # Compare list lengths
            return False
        for item1, item2 in zip(dict1, dict2):  # Compare list items recursively
            if not deep_compare_dicts(item1, item2):
                return False
    else:  # Compare primitive values
        return dict1 == dict2

    return True

def remove_underscores(data):
    """
    Recursively remove underscores from all keys in a nested dictionary.

    Args:
        data (dict | list): The input dictionary or list to process.

    Returns:
        dict | list: The processed dictionary or list with underscores removed from keys.
    """
    ignore_keys = ["px_height", "px_width", "shape_type", "cell_list", "in_cell", "position"]
    if isinstance(data, dict):
        return {
            key.lstrip('_'): remove_underscores(value)
            for key, value in data.items()
            if key.lstrip('_') not in ignore_keys  # Exclude 'cell_list' key
        }
    elif isinstance(data, list):
        return [remove_underscores(item) for item in data]
    else:
        return data  # Return value unchanged if it's neither a dict nor a list
# AI generation end

def test_get_cell_context():
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/test_case.xlsx")
    file_path = os.path.normpath(file_path)

    parser = ExcelParser()
    res = parser.parse_document(file_path)

    sheet = res.document[0]

    test_cell = sheet.tables[0].rows[2].cells[0]

    context = test_cell.get_context()

    assert len(context.current_row) != 0
    assert len(context.pre_context) != 0
    assert len(context.next_context) != 0
    assert len(context.cell_header) != 0
    assert len(context.header_list) != 0
    assert context.header_list.index(context.cell_header) != -1

    text = sheet.texts[1]
    text_context = text.get_context()
    assert len(text_context.pre_context) != 0
    assert len(text_context.next_context) != 0
