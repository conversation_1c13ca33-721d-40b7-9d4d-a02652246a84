"""
Rule interface for document parsing.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Callable

class Rule(ABC):
    """
    Base interface for all rules.
    Rules are used to transform document objects based on conditions.
    """
    
    @abstractmethod
    def get_rule_id(self) -> str:
        """
        Get rule ID.
        
        Returns:
            Rule ID
        """
        pass
    
    @abstractmethod
    def get_rule_name(self) -> str:
        """
        Get rule name.
        
        Returns:
            Rule name
        """
        pass
    
    @abstractmethod
    def get_rule_description(self) -> str:
        """
        Get rule description.
        
        Returns:
            Rule description
        """
        pass
    
    @abstractmethod
    def get_rule_priority(self) -> int:
        """
        Get rule priority.
        Higher priority rules are applied first.
        
        Returns:
            Rule priority
        """
        pass
    
    @abstractmethod
    def get_rule_target(self) -> str:
        """
        Get rule target.
        Target can be 'text', 'table', 'picture', 'graphic', or 'document'.
        
        Returns:
            Rule target
        """
        pass
    
    @abstractmethod
    def get_rule_condition(self) -> Callable[[Dict[str, Any]], bool]:
        """
        Get rule condition function.
        
        Returns:
            Function that takes an object and returns True if the rule should be applied
        """
        pass
    
    @abstractmethod
    def apply_rule(self, target_object: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply rule to target object.
        
        Args:
            target_object: Object to apply rule to
            
        Returns:
            Modified object
        """
        pass
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """
        Check if rule is enabled.
        
        Returns:
            True if rule is enabled, False otherwise
        """
        pass
    
    @abstractmethod
    def enable(self) -> None:
        """Enable rule."""
        pass
    
    @abstractmethod
    def disable(self) -> None:
        """Disable rule."""
        pass

class TextRule(Rule):
    """Rule for text objects."""
    
    def get_rule_target(self) -> str:
        """
        Get rule target.
        
        Returns:
            'text'
        """
        return 'text'

class TableRule(Rule):
    """Rule for table objects."""
    
    def get_rule_target(self) -> str:
        """
        Get rule target.
        
        Returns:
            'table'
        """
        return 'table'

class PictureRule(Rule):
    """Rule for picture objects."""
    
    def get_rule_target(self) -> str:
        """
        Get rule target.
        
        Returns:
            'picture'
        """
        return 'picture'

class GraphicRule(Rule):
    """Rule for graphic objects."""
    
    def get_rule_target(self) -> str:
        """
        Get rule target.
        
        Returns:
            'graphic'
        """
        return 'graphic'

class DocumentRule(Rule):
    """Rule for document objects."""
    
    def get_rule_target(self) -> str:
        """
        Get rule target.
        
        Returns:
            'document'
        """
        return 'document'
