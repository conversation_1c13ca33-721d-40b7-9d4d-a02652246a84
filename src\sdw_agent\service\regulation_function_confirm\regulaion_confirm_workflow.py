"""
regulation_function_workflow.py
法规确认工作流

V字对应：
1.3 要件分析
法规确认模块

实现法规确认的核心流程，集成LLM能力进行自动化评估

主要功能：
1. 法规知识库预处理
2. 法规功能变更分类
3. 变更结果生成
4. Excel数据写入
"""

# 标准库
from pathlib import Path
from typing import  Optional

# 第三方库
from loguru import logger

# 本地应用
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.service.regulation_function_confirm.utils import (
    classify_change,
    extract_governing_overview,
    embedding_func,
    read_excel_columns,
    calculate_similarity,
    write_classification_to_excel
)
from sdw_agent.service.regulation_function_confirm.models import (
    RegulationInputData,
    ClassificationResult,
    ServiceResult,
    RegulationCategory,
    RegulationImpact,
    RegulationConfigModel
)

@register_workflow("regulation_function")
class RegulationFunctionWorkflow(BaseWorkflow):
    """
    负责处理法规变更影响分析的核心流程，包含以下能力：
    - 法规知识库预处理
    - 法规功能变更分类

    Attributes:
        origin_file_path: 变更需求文件路径
        kb_file_path: 法规知识库文件路径
        regulation_texts: 提取的法规文本
        regulation_embeddings: 法规文本向量表示
    """

    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        # 启用日志队列处理多线程
        logger.add("regulation_workflow.log",
                   enqueue=True,  # 重要：启用队列
                   format="{time} {level} {message}",
                   level="DEBUG")
        self.config_model = self._load_config_model()
        self._init_global_vars()

    def _init_global_vars(self):
        """
        初始化全局变量

        设置以下全局变量：
        - origin_file_path: 原始文件路径
        - kb_file_path: 知识库文件路径
        - regulation_texts: 法规文本列表
        - regulation_embeddings: 法规文本向量
        """
        self.origin_file_path = None
        self.kb_file_path = None
        self.regulation_texts = []
        self.regulation_embeddings = None

    def _load_config_model(self):
        """
        加载配置模型

        Returns:
            加载成功的配置模型对象
            加载失败返回None
        """
        try:
            from .models import RegulationConfigModel

            # 显式转换嵌套模型
            config_data = {
                "azure": self.config.get("azure", {}),
                "io": self.config.get("io", {}),
                "llm": self.config.get("llm", {}),
                "processing": self.config.get("processing", {})
            }
            return self.config
        except Exception as e:
            self.logger.error(f"配置模型加载失败: {str(e)}")
            return None

    def validate_input(self, origin_file_path: str,
                      kb_file_path: Optional[str] = None) -> bool:
        """
                验证输入参数有效性

                Args:
                    origin_file_path: 变更需求文件路径
                    kb_file_path: 法规知识库文件路径（可选）

                Returns:
                    bool: 验证是否通过

                Raises:
                    FileNotFoundError: 当指定文件不存在时
                    ValueError: 当文件扩展名不符合要求时
                """
        try:
            # 文件路径验证
            if not Path(origin_file_path).exists():
                return False

            # 验证文件扩展名
            io_config = self.config_model.get("io", {})
            allowed_exts = io_config.get("excel_extensions", [".xlsx", ".xls", ".xlsm"])
            if Path(origin_file_path).suffix.lower() not in allowed_exts:
                return False

            # 保存参数
            self.origin_file_path = origin_file_path
            self.kb_file_path = kb_file_path

            return True

        except Exception as e:
            self.logger.error(f"输入参数验证异常: {str(e)}")
            return False

    def pre_execute(self, origin_file_path: str,
                   kb_file_path: Optional[str] = None) -> bool:
        """
        预处理阶段：加载法规知识库

        Args:
            origin_file_path: 变更需求文件路径
            kb_file_path: 法规知识库文件路径（可选）

        Returns:
            bool: 预处理是否成功

        Raises:
            ValueError: 当知识库处理失败时
        """
        try:
            if not self.validate_input(origin_file_path, kb_file_path):
                raise ValueError("输入参数验证失败")

            kb_path = kb_file_path or self.config_model.io.kb_file_path
            self.logger.info(f"开始预处理法规知识库: {kb_path}")

            # 提取法规文本
            self.regulation_texts = extract_governing_overview(kb_path)
            if not self.regulation_texts:
                raise ValueError("未提取到法规概要内容")

            # 生成嵌入向量
            texts_for_embedding = self.regulation_texts[:10]
            self.regulation_embeddings = embedding_func(
                texts_for_embedding,
                self.config_model
            )
            if self.regulation_embeddings is None or len(self.regulation_embeddings) == 0:
                raise ValueError("法规嵌入向量生成失败，请检查API配置")

            self.config_model["regulation_embeddings"] = self.regulation_embeddings
            self.config_model["regulation_texts"] = texts_for_embedding

            self.logger.info(f"法规知识库预处理完成，提取文本数量: {len(self.regulation_texts)}")
            return True
        except Exception as e:
            self.logger.error(f"法规知识库预处理失败: {str(e)}")
            return False

    def execute(self, origin_file_path: str,
               kb_file_path: Optional[str] = None) -> WorkflowResult:
        """
        执行核心工作流逻辑

        Args:
            origin_file_path: 变更需求文件路径
            kb_file_path: 法规知识库文件路径（可选）

        Returns:
            WorkflowResult: 包含执行结果的对象

        Raises:
            ValueError: 当预处理或数据处理失败时
        """
        try:
            self.logger.info("开始执行法规确认工作流")

            # 1. 预处理法规库
            if not self.pre_execute(origin_file_path, kb_file_path):
                raise ValueError("预处理法规知识库失败")

            # 2. 读取变更需求
            data = read_excel_columns(self.origin_file_path)
            if not data:
                raise ValueError("读取Excel列数据失败")

            # 3. 执行分类评估
            results = [classify_change(item, self.config_model) for item in data]

            # 4. 写入结果
            write_classification_to_excel(self.origin_file_path, results)


            # 构建结果
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="工作流执行成功",
                data={
                    "results": [r.dict() for r in results]
                }
            )
        except ValueError as e:
            self.logger.error(f"工作流执行失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"执行失败: {str(e)}",
                error=str(e)
            )