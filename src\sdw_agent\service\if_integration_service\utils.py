"""
IF整合性确认工作流辅助工具

提供文件解析、Excel处理等辅助功能
"""
import os
from pathlib import Path
import pandas as pd
from typing import Dict, Any, Optional, List
from loguru import logger
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.if_integration_service.models import ConfirmResult
from sdw_agent.util.excel.core import ExcelUtil


def merge_column_name(col) -> str:
    """
    合并列名，忽略 None、空白字符串以及以 'Unnamed' 开头的列名。

    Args:
        col: 包含列名的可迭代对象

    Returns:
        str: 合并后的列名字符串
    """

    # 定义有效列名的条件
    def is_valid(name):
        # 检查列名是否非空且不以 'Unnamed' 开头
        return name.strip() and not name.startswith('Unnamed')

    # 过滤并合并有效列名
    parts = []
    for c in col:
        # 将列名转换为字符串并去除首尾空格
        c_name = str(c).strip()
        # 如果列名有效，则添加到合并列表中
        if is_valid(c_name):
            parts.append(c_name)

    # 使用下划线连接所有有效列名并返回
    return '_'.join(parts)


def if_stylebook_to_json(file_path: str, sheet_name: str, if_name_keyword: str) -> Dict[str, Any]:
    """
    读取Excel并进行数据清洗，最终转换为JSON格式
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        if_name_keyword: 用于定位起始行的关键字

    Returns:
        Dict[str, Any]: 清洗后的数据
    """
    try:
        # 安全检查路径
        real_path = os.path.realpath(file_path)
        if not os.path.isfile(real_path):
            raise FileNotFoundError(f"指定的文件路径不存在: {real_path}")

        # 第一次读取用于定位起始行
        df_all = pd.read_excel(io=real_path, sheet_name=sheet_name, header=None)

        rows = df_all[
            df_all.apply(lambda row: any(if_name_keyword in str(cell) for cell in row), axis=1)
        ]
        min_idx = rows.index.min() if not rows.empty else None

        if min_idx is None:
            raise ValueError(f"未找到包含 {if_name_keyword} 的行，请检查表头内容")

        # 第二次读取，使用正确的 header 行
        df = pd.read_excel(
            io=real_path,
            sheet_name=sheet_name,
            header=[min_idx, min_idx + 1]
        )

        # 移除所有层级均为 Unnamed 的列
        valid_cols = [col for col in df.columns
                      if not all('Unnamed' in str(level) for level in col)]
        df = df[valid_cols]

        # 合并多级表头，去除前缀或后缀多余的下划线
        df.columns = [merge_column_name(col).strip('_') for col in df.columns.values]

        # 清除全为空的行和列
        df = df.fillna('').dropna(how='all').dropna(axis=1, how='all')

        # 去除重复的行和列（保留首次出现）
        df = df.drop_duplicates()
        df = df.loc[:, ~df.T.duplicated()]

        # 检查列是否存在
        if if_name_keyword not in df.columns:
            raise KeyError(f"列 {if_name_keyword} 不存在，请确认表头是否正确")

        # 筛选符合命名规范的行（先转字符串）
        df[if_name_keyword] = df[if_name_keyword].astype(str)
        df = df[df[if_name_keyword].str.fullmatch(r'[a-zA-Z0-9_]+')]

        # 返回 JSON 数据
        return {item[if_name_keyword]: item for item in df.to_dict(orient='records')}

    except Exception as e:
        logger.error(f"读取IF设计书失败: {str(e)}")
        raise


def llm_integration(match_line: Dict[str, Any], match_file: str, if_stylebook: Dict[str, Any]) -> str:
    """
    使用LLM判断接口集成是否符合要求
    
    Args:
        match_line: 匹配行信息
        match_file: 匹配文件内容
        if_stylebook: IF设计书信息
        
    Returns:
        str: 判断结果
    """
    try:
        # 导入环境变量中的提示模板
        from sdw_agent.config.env import ENV

        # 创建一个聊天提示模板
        template = ChatPromptTemplate(
            [("user", ENV.prompt.if_integration_prompt)],
            template_format="mustache",
        )

        # 准备输入数据
        invoke_data = {
            "match_line": match_line,
            "match_file": match_file,
            "if_stylebook": if_stylebook
        }

        # 调用LLM
        resp: AIMessage = get_ai_message_with_structured_output(
            template,
            invoke_data,
            ConfirmResult,
            llm_model=None
        )

        # 返回结果
        return resp.confirm_result

    except Exception as e:
        # 记录错误日志
        logger.error(f"LLM调用失败: {str(e)}")
        # 默认返回Pending
        return "Pending"


class IFIntegrationExcelUtil(ExcelUtil):
    """
    IF集成服务专用Excel工具类，继承自ExcelUtil
    
    提供IF集成数据的Excel操作功能，包括：
    1. 基于模板创建工作表
    2. 填充集成数据
    3. 应用特定样式
    4. 处理特殊列格式
    """

    def __init__(self, output_file: str, template_file: Optional[str] = None,
                 excel_style: Optional[Dict[str, Any]] = None, engine: str = "win32com"):
        """
        初始化IF集成Excel工具

        Args:
            output_file: Excel文件保存路径
            template_file: 模板文件路径
            excel_style: Excel样式配置
            engine: 操作引擎类型
        """
        # 调用父类初始化方法，设置模板文件路径和操作引擎类型
        super().__init__(template_file, engine=engine, auto_create=False)

        # 设置模板文件路径，如果未提供则为None

        self.template_file = Path(template_file) if template_file else None

        # 设置Excel文件保存路径，如果未提供则为空字符串
        self.output_file = Path(output_file) if output_file else ""

        # 设置Excel样式配置，如果未提供则使用默认配置
        self.excel_style = excel_style or {}

        # 从样式配置中获取默认工作表名称，如果未配置则使用默认值
        self.default_sheet = self.excel_style.get("default_sheet", "DEFAULT_IF")

        # 设置新的工作表名称，如果未配置则使用默认值
        self.new_sheet = self.excel_style.get("new_sheet", "IF")

        # 从样式配置中获取特殊列配置，如果未配置则使用空列表
        self.special_cols = self.excel_style.get("special_cols", [])

        # 从样式配置中获取数据起始行配置，如果未配置则使用默认值
        self.data_start_row = self.excel_style.get("data_start_row", 14)

        # 从样式配置中获取数据起始列配置，如果未配置则使用默认值
        self.data_start_col = self.excel_style.get("data_start_col", 2)

        # 记录特殊列配置日志
        logger.info(self.special_cols)

    def save_integration_data(self, integration_data: List[List[Any]]) -> None:
        """
        保存集成数据到Excel文件

        Args:
            integration_data: 包含多个工作表数据的字典，键是工作表名称，值是该工作表的数据
        """
        try:
            # 检查模板文件是否存在
            if self.template_file and self.template_file.exists():
                logger.info(f"使用模板文件: {self.template_file}")
                # 使用模板文件保存集成数据
                self._save_with_template(integration_data)
            else:
                # 如果模板文件不存在，抛出异常
                raise FileNotFoundError(f"模板文件 {self.template_file} 不存在")

            logger.info(f"集成数据已保存到 {self.file_path}")

        except Exception as e:
            # 日志记录保存过程中发生的异常
            logger.error(f"保存Excel文件失败: {str(e)}")
            raise

    def _save_with_template(self, integration_data: List[List[Any]]) -> None:
        """
        使用模板文件保存数据
        
        Args:
            integration_data: 集成数据
        """
        try:
            # 处理所有工作表
            self._process_all_sheets(integration_data)

            # 清理并保存
            self._cleanup_and_save()

        except PermissionError as e:
            self._handle_permission_error(e)
        except Exception as e:
            logger.error(f"保存模板文件失败: {e}")
            raise

    def _process_all_sheets(self, integration_data:  List[List[Any]]) -> None:
        """处理所有工作表

        该函数遍历包含工作表数据的字典，对每个工作表进行处理
        它首先准备（处理）工作表名称，然后创建并填充工作表数据

        参数:
        integration_data: 一个字典，键是工作表名称，值是包含该工作表数据的列表

        返回:
        无
        """
        # 验证默认工作表存在
        if self.default_sheet not in self.get_sheet_names():
            raise ValueError(f"默认工作表 {self.default_sheet} 不存在")

        # 复制默认工作表并重命名
        success = self.copy_sheet(self.default_sheet, self.new_sheet)
        if not success:
            raise RuntimeError(f"复制工作表失败: {self.default_sheet} -> {self.new_sheet}")

        logger.info(f"成功复制工作表: {self.default_sheet} -> {self.new_sheet}")

        self._fill_template_sheet_data(self.new_sheet, integration_data)

    def _cleanup_and_save(self) -> None:
        """
        清理默认工作表并保存文件

        该方法首先检查默认工作表是否存在，如果存在则将其删除，
        然后将当前工作簿保存到指定的输出文件路径
        """
        # 删除默认工作表
        if self.default_sheet in self.get_sheet_names():
            self.delete_sheet(self.default_sheet)
            logger.info(f"删除默认工作表: {self.default_sheet}")

        # 保存文件
        self.save(str(self.output_file))
        logger.info(f"文件保存成功: {self.output_file}")

    @staticmethod
    def _handle_permission_error(e: PermissionError) -> None:
        """
        处理权限错误

        当尝试执行一个文件操作但遇到权限错误时调用此方法它记录了一个错误消息，
        提示用户检查可能导致权限错误的几个常见问题，然后重新抛出原始异常

        参数:
        e (PermissionError): 引发的权限错误异常

        返回:
        无返回值
        """
        # 记录权限错误的详细信息
        logger.error(f"文件权限错误: {e}")
        # 提示用户检查可能的原因
        logger.error("请检查:")
        logger.error("1. 目标文件是否被Excel或其他程序打开")
        logger.error("2. 是否有写入目标目录的权限")
        logger.error("3. 磁盘空间是否充足")
        logger.error("4. 文件是否被其他进程占用")
        # 重新抛出异常，以便可以在调用此方法的地方处理
        raise

    def _fill_template_sheet_data(self, sheet_name: str, data: List[List[Any]]) -> None:
        """
        填充模板工作表数据
        
        Args:
            sheet_name: 工作表名称
            data: 要填充的数据
        """
        # 打开指定名称的工作表
        with self.worksheet(sheet_name) as ws:
            # 遍历数据集，每行数据用一个列表表示
            for index, row in enumerate(data):
                # 计算每行数据在工作表中的起始和结束行号
                start_row = index * 4 + self.data_start_row
                end_row = start_row + 3

                # 遍历一行中的每个数据项，列索引从2开始
                for col_idx, value in enumerate(row, start=self.data_start_col):
                    # 判断当前列是否为特殊列
                    if col_idx in self.special_cols:
                        # 特殊列：首行写入内容，其余行写入'-'
                        self._fill_special_column(ws, col_idx, start_row, end_row, value)
                    else:
                        # 普通列：合并单元格并写入内容
                        self._fill_regular_column(sheet_name, col_idx, start_row, end_row, value)

    @staticmethod
    def _fill_special_column(ws, col_idx: int, start_row: int, end_row: int, value: Any) -> None:
        """
        填充特殊列：首行写入内容，其余行写入'-'
        
        Args:
            ws: 工作表上下文
            col_idx: 列索引
            start_row: 起始行号
            end_row: 结束行号
            value: 填充值
        """
        # 遍历指定的行范围
        for inner_row in range(start_row, end_row + 1):
            # 如果是首行，则使用指定的值；否则使用'-'
            cell_value = value if inner_row == start_row else "-"
            # 在当前行和列索引处写入计算得到的单元格值
            ws.write_cell(inner_row, col_idx, cell_value)

    def _fill_regular_column(self, sheet_name: str, col_idx: int, start_row: int, end_row: int, value: Any) -> None:
        """
        填充普通列：合并单元格并写入内容
        
        Args:
            sheet_name: 工作表名称
            col_idx: 列索引
            start_row: 起始行号
            end_row: 结束行号
            value: 要写入的值
        """
        from openpyxl.utils import get_column_letter
        col_letter = get_column_letter(col_idx)
        merge_range = f"{col_letter}{start_row}:{col_letter}{end_row}"
        self.merge_cells(sheet_name, merge_range)

        # 检查是否为超链接格式
        if isinstance(value, dict) and 'url' in value:
            # 创建超链接
            url = value['url']
            display_text = value.get('text', url)
            self.write_hyperlink(sheet_name, start_row, col_idx, url, display_text)
        else:
            # 普通内容
            self.write_cell(sheet_name, start_row, col_idx, value, value_must_be_safe=True)

    @staticmethod
    def _is_valid_sheet_name(sheet_name: str) -> bool:
        """
        验证工作表名称是否有效
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            是否有效
        """
        # 检查工作表名称是否为空或长度超过31个字符
        if not sheet_name or len(sheet_name) > 31:
            return False

        # Excel工作表名称不能包含的字符
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        # 检查工作表名称中是否包含任何无效字符
        return not any(char in sheet_name for char in invalid_chars)
