"""判断测试用例所属测试观点，并生成测试观点对应的检查项和检查内容"""
import collections
import asyncio
import time

from loguru import logger

from sdw_agent.llm.openai_qwen import OpenAIQwen
from sdw_agent.util.local_file_read_save import read_json
from sdw_agent.util.markdown_util import TestMdParser


llm = OpenAIQwen(base_url="http://172.30.19.113:11400/v1",
                      api_key="sk-xxx")


class JudgeTestcaseClass:
    def __init__(self, check_data):
        self.check_list = check_data

    async def testcase_class(self, test_case):
        """
        获取单条测试用例后，生成判别prompt后获取测试观点相关信息
        Args:
            test_case: 单条测试用例字典
        Return:
            dict: 该条测试用例所属测试观点及相关信息
        """
        prompt = """
            <instruction>
                你是一名汽车项目研发人员，熟悉的了解汽车的每个功能模块，现在需要你根据<input>提供的测试用例，按照下面步骤，一步一步进行思考，生成测试观点；
                step1: 结合input测试用例中的信息，判断该测试用例所属测试观点类别、并生成对应的检查项及检查内容；
                step2: 验证生成的测试观点及检查项、检查内容是否符合<check_list>中的要求，如果不符合，则重新生成测试观点，直到符合要求为止；
            </instruction>
            
            <requirement>
                1. 必须严格按照json格式输出，不要输出其他内容；
                2. 输出格式按照<output_structure>中的格式输出；
            </requirement>
            
            <knowledge>
                1. 测试观点分为7个类别：内部计算类、表示类、画面迁移类、CAN出力类、异常系（CAN/电源电压）类、影响机能、其他类；
                    *内部计算类：指数据在程序内部处理的逻辑，例如：仪表车速=实际车速/车辆补正率+指示偏差，输入是实际车速，经过程序内部处理后，输出仪表车速；
                    *表示类：指仪表的画面显示，包括是否显示，显示的时机，显示的位置，颜色，效果等；
                    *画面迁移类：指显示从一个画面切换到另一个画面的过程，可以是整个仪表画面的切换，例如大中断，也可以是仪表局部画面切换；
                    *CAN出力类：指仪表在工作过程中，需要对外发送当前仪表的状态CAN信号，例如：车速值，ODO值，按键是否按下，需要考虑出力值，出力时机，出力周期是否正确；
                    *异常系（CAN/电源电压）类：指CAN或者电压发生异常时，仪表处理逻辑，例如CAN信号途绝后，仪表的动作；BAT，IGN重启后，仪表的动作；
                    *影响机能：指该变更点是否影响到其他机能，例如CSTM机能，Unit中新增100KM/L选项，除了考虑CSTM中该选项的位置，还需要考虑选中后，平均油耗、瞬时油耗显示的单位是否正确切换；
                    *其他类：指上述以外的测试观点；
                2. 每个测试用例一定且只匹配一个最相关的测试观点。
            </knowledge>
            
            <input>
                {{input}}
            </input>
            
            <check_list>
                {{check_list}}
            </check_list>
            
            <output_structure>
                {
                    "内部计算类": {"检查项": "", "检查内容": ""}
                }
            </output_structure>
            
            <example>
                {
                    "内部计算类": {
                        "检查项": "AVNMS79（0x385）信号值确认",
                        "检查内容": "①有效值范围（本次追加值，既存值遍历确认）:
                                    NAV_TXT_COUNT4=0x01-0xFF确认(取值0，1，128，255)
                                    NAV_MODE11=0，1，2，3
                                    GPDIST_CHG_A=0，1，8，15
                                    "
                    }
                }
            </example>
            
            <language>
                请使用中文输出。
            </language>
            
            <question>
                请输出你生成的测试观点？
            </question>
            """
        try:
            logger.info(f'开始为测试用例{str(test_case)}生成测试观点')
            message_input = prompt.replace("{{input}}", str(test_case))
            message_input = message_input.replace("{{check_list}}", str(self.check_list))
            response_res = await llm.generate(message_input)
            split_json = {k: v for k, v in response_res.items() if v.get("检查项")}
            logger.info(f'测试用例{str(test_case)}生成的测试观点为：{str(split_json)}')
            return split_json
        except Exception as e:
            logger.error(f'为测试用例生成测试观点失败：{str(e)}')
            return {}


class TestViewGenerate:
    """使用变更点筛选测试观点并更新测试观点"""
    def __init__(self, md_message, rule_message, check_list):
        self.md_message = md_message
        self.rule_message = rule_message
        self.check_list = check_list

    async def split_update_test_view(self, func_msg, test_view):
        """使用变更信息过滤测试观点，并且如果测试观点符合变更信息，则根据相关信息更新测试观点"""
        prompt = """
            <instruction>
                你是一名汽车项目研发人员，熟悉的了解汽车的每个功能模块，现在需要你根据<input>提供的测试用例，按照下面步骤，一步一步进行思考，生成、过滤、更新测试观点；
                step1: 结合input测试用例中的信息，判断该测试用例所属测试观点类别、并生成对应的检查项及检查内容；
                step2: 根据step1中生成的测试观点信息，结合<function_message>中的变更信息，判断该测试观点是否符合变更点描述；
                step3: 如果step1中的测试观点不符合变更信息，则输出空字典；如果符合，则根据<context>的上下文信息，更新step1生成的测试观点
                step4: 如果step3中更新了测试观点，则验证更新后的测试观点是否符合<check_list>中的要求，如果不符合，则重新更新测试观点，直到符合要求为止；
            </instruction>
            
            <requirement>
                1. 必须严格按照json格式输出，不要输出其他内容；
                2. 输出格式按照<output_structure>中的格式输出；
            </requirement>
            
            <knowledge>
                1. 测试观点分为7个类别：内部计算类、表示类、画面迁移类、CAN出力类、异常系（CAN/电源电压）类、影响机能、其他类；
                    *内部计算类：指数据在程序内部处理的逻辑，例如：仪表车速=实际车速/车辆补正率+指示偏差，输入是实际车速，经过程序内部处理后，输出仪表车速；
                    *表示类：指仪表的画面显示，包括是否显示，显示的时机，显示的位置，颜色，效果等；
                    *画面迁移类：指显示从一个画面切换到另一个画面的过程，可以是整个仪表画面的切换，例如大中断，也可以是仪表局部画面切换；
                    *CAN出力类：指仪表在工作过程中，需要对外发送当前仪表的状态CAN信号，例如：车速值，ODO值，按键是否按下，需要考虑出力值，出力时机，出力周期是否正确；
                    *异常系（CAN/电源电压）类：指CAN或者电压发生异常时，仪表处理逻辑，例如CAN信号途绝后，仪表的动作；BAT，IGN重启后，仪表的动作；
                    *影响机能：指该变更点是否影响到其他机能，例如CSTM机能，Unit中新增100KM/L选项，除了考虑CSTM中该选项的位置，还需要考虑选中后，平均油耗、瞬时油耗显示的单位是否正确切换；
                    *其他类：指上述以外的测试观点；
                2. 不是所有测试观点都符合变更信息，若有不符合变更信息的测试观点，输出空字典；
            </knowledge>
            
            <input>
                {{input}}
            </input>
            
            <context>
                <feature_content>
                    {{feature_content}}
                </feature_content>
            
                <rule_data>
                    {{rule_data}}
                </rule_data>
            </context>
            
            <check_list>
                {{check_list}}
            </check_list>
            
            <function_message>
                {{function_message}}
            </function_message>
            
            <output_structure>
                {
                    "内部计算类": {"检查项": "", "检查内容": ""}
                }
            </output_structure>
            
            <example>
                {
                    "内部计算类": {
                        "检查项": "AVNMS79（0x385）信号值确认",
                        "检查内容": "①有效值范围（本次追加值，既存值遍历确认）:
                                    NAV_TXT_COUNT4=0x01-0xFF确认(取值0，1，128，255)
                                    NAV_MODE11=0，1，2，3
                                    GPDIST_CHG_A=0，1，8，15
                                    ②无效值范围（本次削除值、边界值确认）:
                                    NAV_MODE11=4，8，15
                                    ③有效值和无效值组合的切换：
                                    有效值<->有效值
                                    有效值<->无效值
                                    "
                    }
                }
            </example>
            
            <language>
                请使用中文输出。
            </language>
            
            <question>
                请输出你更新后的测试观点？
            </question>
        """
        if not test_view:
            return {}
        try:
            logger.info(f"开始为测试观点{str(test_view)}进行筛选并更新")
            message_input = prompt.replace("{{input}}", str(test_view))
            message_input = message_input.replace("{{function_message}}", str(func_msg))
            message_input = message_input.replace("{{check_list}}", str(self.check_list))
            message_input = message_input.replace("{{feature_content}}", str(self.md_message))
            message_input = message_input.replace("{{rule_data}}", str(self.rule_message))
            response_res = await llm.generate(message_input)
            if not response_res:
                logger.info(f"经LLM判断，测试观点{str(test_view)}并不符合{str(func_msg)}，故筛选并不做更新")
                return {}
            update_json = {k: v for k, v in response_res.items() if v.get("检查项")}
            logger.info(f"筛选并更新测试观点{str(test_view)}完成，更新后的测试观点为：{update_json}")
            return update_json
        except Exception as e:
            logger.error(f"筛选并更新测试观点{str(test_view)}更新失败：{str(e)}")
            return {}


class JudgeAndCheckTest:
    def __init__(self, md_message, rule_message, check_list):
        self.md_message = md_message
        self.rule_message = rule_message
        self.check_list = check_list

    async def gen_split_update(self, func_msg, test_case):
        """生成测试用例对应的测试观点，使用变更信息过滤测试观点，并且如果测试观点符合变更信息，则根据相关信息更新测试观点"""
        prompt = """
            <instruction>
                你是一名汽车项目研发人员，熟悉的了解汽车的每个功能模块，现在需要你根据<input>提供的测试用例，按照下面步骤，一步一步进行思考，生成、过滤、更新测试观点；
                step1: 结合input测试用例中的信息，判断该测试用例所属测试观点类别、并生成对应的检查项及检查内容，检查内容仅生成一条；
                step2: 根据step1中生成的测试观点信息，结合<function_message>中的变更信息，判断该测试观点是否符合变更点描述；
                step3: 如果step1中的测试观点不符合变更信息，则输出空字典；如果符合，则根据<context>的上下文信息，更新step1生成的测试观点
                step4: 如果step3中更新了测试观点，则验证更新后的测试观点是否符合<check_list>中的要求，如果不符合，则重新更新测试观点，直到符合要求为止；
            </instruction>

            <requirement>
                1. 必须严格按照json格式输出，不要输出其他内容；
                2. 输出格式按照<output_structure>中的格式输出；
            </requirement>

            <knowledge>
                1. 测试观点分为7个类别：内部计算类、表示类、画面迁移类、CAN出力类、异常系（CAN/电源电压）类、影响机能、其他类；
                    *内部计算类：指数据在程序内部处理的逻辑，例如：仪表车速=实际车速/车辆补正率+指示偏差，输入是实际车速，经过程序内部处理后，输出仪表车速；
                    *表示类：指仪表的画面显示，包括是否显示，显示的时机，显示的位置，颜色，效果等；
                    *画面迁移类：指显示从一个画面切换到另一个画面的过程，可以是整个仪表画面的切换，例如大中断，也可以是仪表局部画面切换；
                    *CAN出力类：指仪表在工作过程中，需要对外发送当前仪表的状态CAN信号，例如：车速值，ODO值，按键是否按下，需要考虑出力值，出力时机，出力周期是否正确；
                    *异常系（CAN/电源电压）类：指CAN或者电压发生异常时，仪表处理逻辑，例如CAN信号途绝后，仪表的动作；BAT，IGN重启后，仪表的动作；
                    *影响机能：指该变更点是否影响到其他机能，例如CSTM机能，Unit中新增100KM/L选项，除了考虑CSTM中该选项的位置，还需要考虑选中后，平均油耗、瞬时油耗显示的单位是否正确切换；
                    *其他类：指上述以外的测试观点；
                2. 每个测试用例一定且只匹配一个最相关的测试观点；
                3. 不是所有测试观点都符合变更信息，若有不符合变更信息的测试观点，输出空字典；
                4. 每个测试观点的检查内容的不同条目间信息必须独立不重合。
            </knowledge>

            <input>
                {{input}}
            </input>

            <context>
                <feature_content>
                    {{feature_content}}
                </feature_content>

                <rule_data>
                    {{rule_data}}
                </rule_data>
            </context>

            <check_list>
                {{check_list}}
            </check_list>

            <function_message>
                {{function_message}}
            </function_message>

            <output_structure>
                {
                    "内部计算类": {"检查项": "", "检查内容": ""}
                }
            </output_structure>

            <example>
                {
                    "内部计算类": {
                        "检查项": "AVNMS79（0x385）信号值确认",
                        "检查内容": "①有效值范围（本次追加值，既存值遍历确认）:
                                    NAV_TXT_COUNT4=0x01-0xFF确认(取值0，1，128，255)
                                    NAV_MODE11=0，1，2，3
                                    GPDIST_CHG_A=0，1，8，15
                                    "
                    }
                }
            </example>

            <language>
                请使用中文输出。
            </language>

            <question>
                请输出你更新后的测试观点？
            </question>
        """
        if not test_case:
            return {}
        try:
            logger.info(f"开始为测试用例{str(test_case)}进行生成、筛选并更新")
            message_input = prompt.replace("{{input}}", str(test_case))
            message_input = message_input.replace("{{function_message}}", str(func_msg))
            message_input = message_input.replace("{{check_list}}", str(self.check_list))
            message_input = message_input.replace("{{feature_content}}", str(self.md_message))
            message_input = message_input.replace("{{rule_data}}", str(self.rule_message))
            response_res = await llm.generate(message_input)
            if not response_res:
                logger.info(f"经LLM判断，测试观点{str(test_case)}并不符合{str(func_msg)}，故筛选并不做生成及更新")
                return {}
            update_json = {k: v for k, v in response_res.items() if v.get("检查项")}
            logger.info(f"为{str(test_case)}生成、筛选并更新测试观点完成，更新后的测试观点为：{update_json}")
            return update_json
        except Exception as e:
            logger.error(f"为{str(test_case)}生成、筛选并更新测试观点失败：{str(e)}")
            return {}


async def gene_test_view_main(choose_test_case, func_message, checklist_path, rule_path, md_path):
    """

    Args:
        choose_test_case: 上游根据功能点信息筛选的测试用例
        func_message: 从变更信息中抽取到的功能点相关信息
        checklist_path: checklist路径
        rule_path: rule路径
        md_path: md路径
    Return:
        test_view: 生成的测试用例结果
    """
    # 读取基础的依赖文件
    logger.info("读取基础的依赖文件")
    checklist_data = read_json(checklist_path)
    rule_data = read_json(rule_path)
    if md_path:
        md_data = TestMdParser().data_load(md_path)
    else:
        md_data = ''
    logger.info("读取基础的依赖文件完成")

    # 初始化需要用到的类
    logger.info("初始化变更点生成测试观点的相关类")
    # judge_test_case = JudgeTestcaseClass(checklist_data)
    # update_test_case = TestViewGenerate(md_message=md_data, rule_message=rule_data, check_list=checklist_data)
    judge_and_update = JudgeAndCheckTest(md_message=md_data, rule_message=rule_data, check_list=checklist_data)
    logger.info("初始化变更点生成测试观点的相关类完成")

    # 逐个对测试用例进行测试观点生成及筛选、更新
    start_time = time.time()
    all_test_view = collections.defaultdict(list)
    all_test_case = collections.defaultdict(list)
    test_case_list = []
    for _, cases in choose_test_case.items():
        for one_case in cases:
            test_case_list.append(one_case)
    # 开始遍历测试用例生成测试观点
    logger.info(f'总共有{str(len(test_case_list))}条测试用例需要进行测试观点生成、筛选及更新')

    # 将生成测试用例、筛选及更新测试用例合并
    # test_case_list = test_case_list[:3]
    map_update_task = [judge_and_update.gen_split_update(func_message, one_case) for one_case in test_case_list]
    map_update = await asyncio.gather(*map_update_task)

    # 存在测试用例与变更点无关、无对应测试观点输出的情况
    logger.info('获取测试观点后进行临时性保存')
    for one_update, one_case in zip(map_update, test_case_list):
        if one_update:
            for k, v in one_update.items():
                all_test_view[k].append(v)
                all_test_case[k].append(one_case)
    # 对所有测试用例的测试观点进行合并处理
    logger.info('对测试观点进行合并处理')
    merge_res = await merge_message(all_test_view, all_test_case)
    logger.info(f'合并测试观点完成')
    end_time = time.time()

    # 无测试观点的内容补充为空
    view_class = ["内部计算类", "表示类", "画面迁移类", "CAN出力类", "异常系（CAN/电源电压）类", "影响机能", "其他类"]
    has_class = []
    for one_res in merge_res:
        class_name = one_res.get('testview_class')
        has_class.append(class_name)
    for one_view in view_class:
        if one_view not in has_class:
            merge_res.append({"testview_class": one_view, "testview_item": "无",
                              "testview_content": "无", "test_case": [], "source": ""})
    return merge_res


async def merge_message(input_data, case_dict):
    """
    对每个测试观点的信息进行合并
    Args:
        input_data: 需要进行信息合并的测试观点
        case_dict: 测试观点对应的测试用例条目
    Return:
        merge_data: 合并后结果
    """
    all_message = list()
    origin_list = list()
    merge_data = []
    for k, v in input_data.items():
        k_v = {"testview_class": k,
               "testview_item": [i.get('检查项') for i in v if i.get('检查项')],
               "testview_content": [i.get('检查内容') for i in v if i.get('检查内容')]}
        if not (k_v['testview_item'] or k_v['testview_content']):
            continue
        prompt = """
                    <instruction>
                        你是一名汽车项目研发人员，熟悉的了解汽车的每个功能模块，现在需要你根据<input>中的测试观点信息，对其中的testview_item和testview_content进行信息整合；
                    </instruction>

                    <requirement>
                        1. 必须严格按照json格式输出，不要输出其他内容；
                        2. 输出格式按照<output_structure>中的格式输出；
                        3. testview_item和testview_content原始数据类型为list，整合后数据类型为str，且一定不为空；
                        4. testview_class的内容保持不变；
                        5. 整合后的testview_content的不同条目间、信息必须独立不重合。
                    </requirement>

                    <input>
                        {{input}}
                    </input>

                    <output_structure>
                        {
                            "testview_class": "内部计算类",
                            "testview_item": "AVNMS79（0x385）信号值确认",
                            "testview_content": "①有效值范围（本次追加值，既存值遍历确认）:
                                            NAV_TXT_COUNT4=0x01-0xFF确认(取值0，1，128，255)
                                            NAV_MODE11=0，1，2，3
                                            GPDIST_CHG_A=0，1，8，15
                                            ②无效值范围（本次削除值、边界值确认）:
                                            NAV_MODE11=4，8，15
                                            ③有效值和无效值组合的切换：
                                            有效值<->有效值
                                            有效值<->无效值"
                        }
                    </output_structure>
                """
        message_input = prompt.replace("{{input}}", str(k_v))
        all_message.append(message_input)
        origin_list.append(k_v)

    try:
        logger.info(f'开始进行的测试观点合并')
        message_output_task = [llm.generate(i) for i in all_message]
        message_output = await asyncio.gather(*message_output_task)
        # filter_messages_output = await analyze_duplicates(message_output)
        # 增加test_case信息
        for idx, one_output in enumerate(message_output):
            k_dict = origin_list[idx]
            view_name = k_dict.get('testview_class')
            one_output["test_case"] = case_dict.get(view_name)
            # 该模块为基于变更点生成测试观点，所有的观点来源均来源于rule
            one_output["source"] = 'rule'
            logger.info(f'{str(k_dict)}的测试观点合并完成，结果为：{str(one_output)}')
            merge_data.append(one_output)
    except Exception as e:
        logger.error(f'测试观点合并失败：{str(e)}')
    return merge_data

async def analyze_duplicates(test_view):
    """对不同测试观点间相似的内容进行去重操作"""
    prompt = """
        <instruction>
            你是一位专业的汽车软件测试分析师，擅长识别测试观点中的重复内容，现在需要你根据<input>中的信息，按照下面步骤，一步一步进行思考，识别并处理重复内容；
                step1: 分析提供的测试观点列表中、两两测试观点间的testview_content细节部分；
                step2: 识别testview_content中重复的测试步骤；
                step3: 确定哪个测试观点、即哪个testview_class更适合保留这些重复步骤；
                step4: 仅在对应testview_class的testview_content中保留重复步骤，其余testview_class不保留这些重复信息；
                step5: 整理测试观点的testview_content信息，并输出结果。
        </instruction>

        <requirement>
            1. 必须严格按照json格式输出，不要输出其他内容；
            2. 输出格式按照<output_structure>中的格式输出；
            3. 处理之后的测试观点列表长度与处理前的一定一致；
            4. 测试观点的testview_content序号从①开始递增。
        </requirement>

        <knowledge>
            1. 测试观点分为6个类别：内部计算类、表示类、画面迁移类、CAN出力类、异常系（CAN/电源电压）类、其他类；
                *内部计算类：指数据在程序内部处理的逻辑，例如：仪表车速=实际车速/车辆补正率+指示偏差，输入是实际车速，经过程序内部处理后，输出仪表车速；
                *表示类：指仪表的画面显示，包括是否显示，显示的时机，显示的位置，颜色，效果等；
                *画面迁移类：指显示从一个画面切换到另一个画面的过程，可以是整个仪表画面的切换，例如大中断，也可以是仪表局部画面切换；
                *CAN出力类：指仪表在工作过程中，需要对外发送当前仪表的状态CAN信号，例如：车速值，ODO值，按键是否按下，需要考虑出力值，出力时机，出力周期是否正确；
                *异常系（CAN/电源电压）类：指CAN或者电压发生异常时，仪表处理逻辑，例如CAN信号途绝后，仪表的动作；BAT，IGN重启后，仪表的动作；
                *其他类：指上述以外的测试观点；
        </knowledge>

        <input>
            {{input}}
        </input>

        <output_structure>
            {"testview_list":
                [
                    {"testview_class": "内部计算类", "testview_item": "", "testview_content": ""},
                    {"testview_class": "画面迁移类", "testview_item": "", "testview_content": ""}
                ]
            }
        </output_structure>

        <language>
            请使用中文输出。
        </language>

        <question>
            请输出你识别并处理重复内容后的测试观点？
        </question>
    """
    if not test_view:
        return {}
    logger.info(f"开始为测试观点{str(test_view)}进行重复内容的识别及处理")
    message_input = prompt.replace("{{input}}", str({"testview_list": test_view}))
    response_res = await llm.generate(message_input)
    output_res = response_res.get("testview_list")
    if not output_res:
        logger.warning(f"识别并处理测试观点失败，LLM处理结果为：{str(response_res)}，故而输出处理前的测试观点")
        output_res = test_view
    elif len(output_res) != len(test_view):
        logger.warning(f"LLM处理后的测试观点存在缺少类别的情况，故而认为识别及处理测试观点失败，输出处理前的测试观点")
        output_res = test_view
    logger.info(f"识别并处理重复内容完成，处理后的测试观点为：{output_res}")
    return output_res


if __name__ == '__main__':
    test_case_dict = read_json("../data/filtered_testcase.json")
    func_msg = {'change_content': '1500Wコンセントと2400Wコンセントと7200Wコンセントの3つの項目は第0階層から第一階層の車両設定画面に移動する。', 'change_type': 'update', 'function_point': '1500Wコンセント', 'function_path': {'before': '第0階層-1500Wコンセント', 'after': '第一階層-1500Wコンセント'}}

    check_path = '../data/checklists.json'
    rule_path = '../data/rules.json'
    md_path = r'D:\临时测试数据\test.md'
    class_res = asyncio.run(gene_test_view_main(test_case_dict, func_msg, check_path, rule_path, md_path))
    print(class_res)
