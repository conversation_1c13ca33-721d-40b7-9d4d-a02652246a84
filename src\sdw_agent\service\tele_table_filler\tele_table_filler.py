"""
表格填充工作流实现模块

V字对应：
4.4 对应的V字阶段
64. 对应的V字项目

模块简介：实现Excel表格填充工作流，使用RAG从规格书提取数据填充Input/Output表特定列。

主要功能：
1. 加载Excel文件并验证输入
2. 使用RAG查询填充列（如IGR/IGP, Event, Special, Timeout等）
3. 保存填充后的文件和调试报告
"""

import os
import asyncio
import pathlib
from datetime import datetime
from typing import Dict, Any

import nest_asyncio
from loguru import logger
from openpyxl import load_workbook, Workbook
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.service.workflow_config import WorkflowConfigManager
from sdw_agent.service.tele_table_filler.models import ConfigModel, InputModel, OutputModel
from sdw_agent.service.tele_table_filler.utils.file_utils import normalize_text, save_debug_report, add_debug_info
from sdw_agent.service.tele_table_filler.utils.rag_utils import get_rag_instance, query_rag_with_retry
from sdw_agent.service.tele_table_filler.utils.sheet_utils import (
    find_merged_header, find_header_column_contains, find_all_header_columns_contains,
    get_sub_headers, get_data_start_row, get_previous_value, find_expected_results_range,
    fill_igr_igp, fill_event, fill_special, fill_initial_value, fill_can_id_dlc_cycle,
    fill_timeout, fill_recovery, fill_invalid_values, fill_data
)

USER_HOME = pathlib.Path.home()
BASE_OUTPUT_DIR = os.path.join(USER_HOME, '.sdw', 'data')


@register_workflow("tele_table_filler")
class TeleTableFillerWorkflow(BaseWorkflow):
    """
    表格填充工作流类

    负责处理Excel文件，使用RAG从规格书提取数据填充特定列。
    主要用于电信表格填充场景。
    """

    def __init__(self, config_path=None):
        super().__init__(config_path)
        nest_asyncio.apply()
        self.debug_report = []
        self.rag_instances = {}
        self.query_cache = {
            "timeout": {}, "recovery": {}, "invalid": {}, "special": {}, "data": {}
        }
        self.wb = None
        self.input_sheet = None
        self.output_sheet = None
        self.add_debug_info("工作流初始化完成")

    def register_config_model(self):
        """
        注册配置模型
        """
        config_manager = WorkflowConfigManager(workflow_name="tele_table_filler")
        config_manager.register_schema("tele_table_filler", ConfigModel)

    def pre_execute(self, input_data: InputModel):
        """
        执行前准备工作

        Args:
            input_data: 输入数据模型

        Raises:
            ValueError: 如果输入验证失败
        """
        self.add_debug_info("开始预执行准备")
        # 加载Excel
        try:
            self.wb = load_workbook(input_data.file_path, data_only=True)
            self.input_sheet = self.wb.get_sheet_by_name('Input') if 'Input' in self.wb.sheetnames else None
            self.output_sheet = self.wb.get_sheet_by_name('Output') if 'Output' in self.wb.sheetnames else None
            self.add_debug_info(f"成功加载工作簿，包含工作表: {self.wb.sheetnames}")
        except Exception as e:
            self.logger.error(f"加载工作簿失败: {str(e)}")
            raise ValueError(f"加载工作簿失败: {str(e)}")

    def post_execute(self, filled_path: str):
        """
        执行后清理工作

        Args:
            filled_path: 填充后的文件路径
        """
        self.add_debug_info("开始后执行清理")
        report_path = save_debug_report(self.debug_report, filled_path)
        self.add_debug_info(f"调试报告已保存至: {report_path}")
        return report_path

    def validate_input(self, input_data: InputModel) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据模型

        Returns:
            bool: 验证是否通过
        """
        self.add_debug_info("开始输入验证")
        if not os.path.exists(input_data.file_path):
            self.logger.warning(f"文件不存在: {input_data.file_path}")
            return False
        if not os.path.exists(input_data.source_dir):
            self.logger.warning(f"RAG源目录不存在: {input_data.source_dir}")
            return False
        self.add_debug_info("输入验证通过")
        return True

    def execute(self, input_data: Dict[str, Any]) -> WorkflowResult:
        """
        执行工作流核心逻辑

        Args:
            input_data: 输入字典，包含file_path和source_dir

        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            # 解析输入
            input_model = InputModel(**input_data)
            if not self.validate_input(input_model):
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="输入验证失败",
                    data={"error": "无效输入"}
                )

            self.pre_execute(input_model)
            self.source_dir = input_model.source_dir

            total_filled = 0
            if self.input_sheet:
                total_filled += fill_igr_igp(self.input_sheet, self) or 0
                total_filled += fill_event(self.input_sheet, self) or 0
                total_filled += fill_special(self.input_sheet, self) or 0
                total_filled += fill_timeout(self.input_sheet, self) or 0
                total_filled += fill_recovery(self.input_sheet, self) or 0
                total_filled += fill_invalid_values(self.input_sheet, self) or 0

            if self.output_sheet:
                total_filled += fill_data(self.output_sheet, self) or 0
                total_filled += fill_igr_igp(self.output_sheet, self) or 0
                total_filled += fill_can_id_dlc_cycle(self.output_sheet, self) or 0
                total_filled += fill_event(self.output_sheet, self) or 0
                total_filled += fill_special(self.output_sheet, self) or 0
                total_filled += fill_initial_value(self.output_sheet, self) or 0

            # 保存文件
            name, ext = os.path.splitext(os.path.basename(input_model.file_path))
            filled_name = f"{name}_filled{ext}"
            filled_path = os.path.join(self.config.get("output_dir",BASE_OUTPUT_DIR), filled_name)
            self.wb.save(filled_path)
            self.add_debug_info(f"文件保存至: {filled_path}")

            report_path = self.post_execute(filled_path)

            output_data = OutputModel(
                filled_path=filled_path,
                report_path=report_path,
                filled_rows=total_filled,
                status="成功"
            ).dict()

            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="处理完成",
                data=output_data
            )

        except Exception as e:
            self.logger.exception(f"执行失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message="执行失败",
                error=str(e)
            )

    def add_debug_info(self, message: str, is_error: bool = False):
        """
        添加调试信息

        Args:
            message: 消息内容
            is_error: 是否为错误
        """
        add_debug_info(self.debug_report, message, is_error, self.logger)