"""
Bug提交工作流

V字对应：
4.2 検査実施
65. Bug提交確認

该模块提供Bug提交功能，支持分析测试用例中的NG结果，并生成标准化的Bug提交内容。
"""

import os
import re
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict

from openpyxl import Workbook, load_workbook
from sdw_agent.util.excel_util import ReadExcel
from sdw_agent.config.config import ROOT_DIR
from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow

from sdw_agent.service.bug_commit.models import (
    BugCommitConfigModel, BugSeverity, BugInfluence, BugStatus,
    BugCaseInfo, BugAnalysisResult, BugSubmitInfo, BugCommitResult
)
from sdw_agent.service.bug_commit.utils import (
    get_ng_chunk, write_theme_llm, match_module_llm, write_severity_llm,
    write_bug_influence_llm, get_procedure_llm, get_occur_rate, get_finish_date,
    process_excel_file, get_redmine_keyword, write_redmine_keywords_to_excel, preprocess_excel
)
from sdw_agent.config.env import ENV

@register_workflow("bug_commit")
class BugCommitWorkflow(BaseWorkflow):
    """
    Bug提交工作流类
    
    提供测试用例NG分析和标准化Bug提交功能。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化Bug提交工作流
        
        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        
        # 注册配置模型
        self.register_config_model()
        
        # 初始化状态变量
        self.origin_case_path = None
        self.output_dir = None
        self.output_file_redmine = None
        self.output_file_testcase = None
        
    def register_config_model(self):
        """注册配置模型用于验证"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        
        # 创建临时配置管理器用于验证
        config_manager = WorkflowConfigManager(workflow_name="bug_commit")
        config_manager.register_schema("bug_commit", BugCommitConfigModel)
        
    def validate_input(self, origin_case_path: str, output_dir: Optional[str] = None) -> bool:
        """
        验证输入参数
        
        Args:
            origin_case_path: 测试用例文件路径
            output_dir: 输出目录，如不提供则使用默认值
            
        Returns:
            bool: 验证是否通过
        """
        # 验证测试用例文件路径
        case_file_path = Path(origin_case_path)
        if not case_file_path.exists():
            self.logger.error(f"测试用例文件不存在: {origin_case_path}")
            return False
            
        # 验证文件类型
        allowed_exts = self.config.get("io", {}).get("input", {}).get("excel_extensions", [".xlsx", ".xls"])
        if case_file_path.suffix.lower() not in allowed_exts:
            self.logger.error(f"测试用例文件格式不支持: {case_file_path.suffix}，支持的格式: {allowed_exts}")
            return False
            
        # 设置输出目录
        if output_dir is None:
            output_dir = self.config.get("io", {}).get("output", {}).get("default_output_dir", "./output")
            output_root_path = ENV.config.output_data_path
            output_dir = os.path.join(output_root_path, output_dir)
        # 创建输出目录（如果不存在）
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {str(e)}")
            return False
            
        # 保存参数
        self.origin_case_path = str(case_file_path)
        self.output_dir = output_dir
        
        # 设置输出文件路径
        output_filename_redmine = self.config.get("io", {}).get("output", {}).get(
            "output_filename_redmine", "bug_commit_results.xlsx"
        )
        output_filename_testcase = self.config.get("io", {}).get("output", {}).get(
            "output_filename_testcase", "bug_id_to_testcase.xlsx"
        )
        self.output_file_redmine = os.path.join(output_dir, output_filename_redmine)
        self.output_file_testcase = os.path.join(output_dir, output_filename_testcase)
        
        return True
        
    def execute(self, origin_case_path: str, output_dir: Optional[str] = None) -> WorkflowResult:
        """
        执行Bug提交工作流
        
        Args:
            origin_case_path: 测试用例文件路径
            output_dir: 输出目录，如不提供则使用默认值
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        self.logger.info(f"开始执行Bug提交工作流")
        self.logger.info(f"测试用例文件: {origin_case_path}")
        self.logger.info(f"输出目录: {output_dir or '使用默认值'}")
        
        try:
            # 1. 预处理Excel文件，获取当前Bug ID字典
            self.logger.info("步骤1: 预处理Excel文件")
            bug_id_dict = self._preprocess_excel()
            
            # 2. 处理Excel文件
            self.logger.info("步骤2: 处理Excel文件")
            result = self._process_excel_file(bug_id_dict)
            
            # 处理完成
            self.logger.info(f"Bug提交工作流执行完成，输出文件: {result.output_file}")
            
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="Bug提交工作流执行成功",
                data={
                    "output_file": result.output_file,
                    "test_case_overwrite": result.test_case_overwrite,
                    "bug_count": len(result.bug_cases),
                    # "case_count": len(result.bug_cases)
                }
            )
            
        except Exception as e:
            self.logger.exception(f"Bug提交工作流执行异常")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"Bug提交工作流执行失败: {str(e)}",
                error=str(e)
            )
    
    def _preprocess_excel(self) -> Dict[str, int]:
        """
        预处理Excel文件，获取当前Bug ID字典
        
        Returns:
            Dict[str, int]: Bug ID字典
        """
        bug_id_dict = {}
        
        try:
            preprocess_excel(self.origin_case_path)
            self.logger.info(f"预处理Excel表格 {self.origin_case_path} 成功")
            # 检查输出文件是否存在
            # if os.path.exists(self.output_file_testcase):
            #     # 读取已有的Bug ID
            #     wb = load_workbook(self.output_file_testcase)
            #     ws = wb.active
            #
            #     # 从第2行开始读取（跳过表头）
            #     for row in range(2, ws.max_row + 1):
            #         case_id = ws.cell(row=row, column=1).value
            #         if case_id:
            #             bug_id_dict[case_id] = 1
            #
            #     self.logger.info(f"从输出文件中读取了 {len(bug_id_dict)} 个Bug ID")
        except Exception as e:
            self.logger.warning(f"读取输入文件失败: {str(e)}")
        
        return bug_id_dict
    
    def _process_excel_file(self, bug_id_dict: Dict[str, int]) -> BugCommitResult:
        """
        处理Excel文件
        
        Args:
            bug_id_dict: Bug ID字典
            
        Returns:
            BugCommitResult: Bug提交结果
        """
        # 初始化结果
        bug_cases = []
        origin_case_path = self.origin_case_path
        try:
            commit_res = list()
            self.logger.info(f"开始解析传入的excel文件{self.origin_case_path}")
            _, case_json = ReadExcel.parse_excel("testcases", origin_case_path)
            self.logger.info(f"解析传入的excel文件{self.origin_case_path}结束")
            ng_num = 0
            # 保存NG结果的Bug_ID与测试用例的映射关系
            ng_2_bug_id = defaultdict(list)
            self.logger.info("开始逐条对NG样例进行处理")
            for one_case in case_json:
                # 对case字典中的换行符进行操作
                one_case = {k: v.replace('\n', '') for k, v in one_case.items()}
                # 基本功能测试用例中是Manual Check result列名下写测试结果
                # 其余测试用例是手动测试结果列名下写测试结果
                ng_res = one_case.get("手动确认结果")
                manual_res = one_case.get('Manual Check result')
                bug_res = ng_res if ng_res else manual_res
                # OK项跳过
                if (bug_res and "NG" not in bug_res) or not bug_res:
                    continue
                bug_cases.append(one_case)
                # 对bug结果进行markdown结构整理
                bug_chunk = get_ng_chunk(one_case)
                # 对该bug提交信息进行整合
                one_commit = get_redmine_keyword(origin_case_path, one_case, bug_chunk)
                # 此处暂由人为增加bug id号，待bug excel提交redmine功能完善后，此处bug id号应由redmine返回
                ng_num += 1
                bug_id = one_commit.get("跟踪") + " #" + str(ng_num)
                ng_id = one_case.get('示例')
                one_commit["Bug_ID"] = bug_id
                commit_res.append(one_commit)
                ng_2_bug_id[one_case.get("sheet_name")].append([ng_id, bug_id])
            self.logger.info("对NG样例的处理结束")

            self.logger.info("开始保存对应的分析结果")
            # 保存bug信息到excel
            save_path = write_redmine_keywords_to_excel(commit_res, self.output_file_redmine)
            # 回写bug id到测试用例文件
            update_case = process_excel_file(origin_case_path, self.output_file_testcase, ng_2_bug_id)
            # 对返回结果进行整理
            # res_dict = {
            #     "bug_commit_excel": save_path,
            #     "rewrite_testcase_excel": update_case
            # }

            # 返回结果
            return BugCommitResult(
                bug_cases=bug_cases,
                # analysis_results=analysis_results,
                # submit_infos=submit_infos,
                output_file=save_path,
                test_case_overwrite=update_case
            )
            
        except Exception as e:
            self.logger.error(f"处理Excel文件失败: {str(e)}")
            raise

    def _extract_ng_case(self, row: Dict[str, str]) -> Optional[BugCaseInfo]:
        case_id = row.get('示例', '')
        module = row.get('模块名称', '')
        test_case = row.get('确认点', '')
        expected_result = row.get('画面显示', '')
        actual_result = row.get('画面显示_actual', '')
        remarks = row.get('Remarks', '')
        date = row.get('Date', '')

        # 创建案例信息
        case_info = BugCaseInfo(
            case_id=case_id,
            module=module,
            test_case=test_case,
            actual_result=actual_result,
            expected_result=expected_result,
            remarks=remarks,
            date=date
        )

        return case_info

    def _extract_ng_case_delete(
        self, row: List[Any], no_col: int, module_col: int, test_case_col: int, 
        act_col: int, exp_col: int, remarks_col: int, date_col: int
    ) -> Optional[BugCaseInfo]:
        """
        提取NG案例信息
        
        Args:
            row: 行数据
            no_col: No.列索引
            module_col: 模块名称列索引
            test_case_col: 测试项目列索引
            act_col: 实际结果列索引
            exp_col: 期待结果列索引
            remarks_col: 备注列索引
            date_col: 日期列索引
            
        Returns:
            Optional[BugCaseInfo]: Bug案例信息
        """
        try:
            # 确保索引有效
            max_col = max(no_col, module_col, test_case_col, act_col, exp_col, remarks_col, date_col)
            if len(row) <= max_col:
                return None
                
            # 提取信息
            case_id = str(row[no_col]) if row[no_col] is not None else ""
            module = str(row[module_col]) if row[module_col] is not None else ""
            test_case = str(row[test_case_col]) if row[test_case_col] is not None else ""
            actual_result = str(row[act_col]) if row[act_col] is not None else ""
            expected_result = str(row[exp_col]) if row[exp_col] is not None else ""
            remarks = str(row[remarks_col]) if row[remarks_col] is not None else ""
            date = str(row[date_col]) if row[date_col] is not None else ""
            
            # 创建案例信息
            case_info = BugCaseInfo(
                case_id=case_id,
                module=module,
                test_case=test_case,
                actual_result=actual_result,
                expected_result=expected_result,
                remarks=remarks,
                date=date
            )
            
            return case_info
            
        except Exception as e:
            self.logger.warning(f"提取NG案例信息失败: {str(e)}")
            return None
    
    def _analyze_ng_cases(self, ng_cases: List[BugCaseInfo], sheet_name: str) -> List[BugAnalysisResult]:
        """
        分析NG案例
        
        Args:
            ng_cases: NG案例列表
            sheet_name: 工作表名称
            
        Returns:
            List[BugAnalysisResult]: Bug分析结果列表
        """
        analysis_results = []
        
        for case in ng_cases:
            try:
                # 转换为Markdown格式
                result_rows = {
                    "ID": case.case_id,
                    "模块": case.module,
                    "测试项目": case.test_case,
                    "实际结果": case.actual_result,
                    "期望结果": case.expected_result,
                    "备注": case.remarks or ""
                }
                
                chunk_markdown = get_ng_chunk(result_rows)
                
                # 获取Redmine关键词
                ng_case_dict = {
                    "No.": case.case_id,
                    "模块名称": case.module,
                    "测试项目": case.test_case,
                    "实际结果": case.actual_result,
                    "期待结果": case.expected_result,
                    "Remarks": case.remarks,
                    "Date": case.date
                }
                
                keywords = get_redmine_keyword(self.origin_case_path, ng_case_dict, chunk_markdown, self.config)
                
                # 创建分析结果
                result = BugAnalysisResult(
                    case_id=case.case_id,
                    theme=keywords.get("主题", ""),
                    severity=keywords.get("优先级", BugSeverity.C),
                    influence=keywords.get("Bug影响度", BugInfluence.C),
                    component=keywords.get("所属组件", "其他"),
                    procedure=keywords.get("步骤", ""),
                    occur_rate=keywords.get("发生率", "100%"),
                    related_screen=keywords.get("涉及画面", "")
                )
                
                analysis_results.append(result)
                
            except Exception as e:
                self.logger.warning(f"分析NG案例失败: {str(e)}")
                continue
        
        return analysis_results
    
    def _prepare_submit_info(self, result: BugAnalysisResult) -> BugSubmitInfo:
        """
        准备Bug提交信息
        
        Args:
            result: Bug分析结果
            
        Returns:
            BugSubmitInfo: Bug提交信息
        """
        # 创建Bug提交信息
        submit_info = BugSubmitInfo(
            bug_id=result.case_id,  # 使用案例ID作为Bug ID
            theme=result.theme,
            priority=result.severity,
            component=result.component,
            related_screen=result.related_screen,
            bug_influence=result.influence,
            remarks=f"步骤:\n{result.procedure}\n\n发生率: {result.occur_rate}"
        )
        
        return submit_info


def bug_commit_service(origin_case_path: str, output_dir: Optional[str] = None) -> tuple[bool, dict[str, Any] | None]:
    """
    Bug提交服务（兼容旧接口）
    
    Args:
        origin_case_path: 测试用例文件路径
        output_dir: 输出目录
        
    Returns:
        str: 输出文件路径
    """
    workflow = BugCommitWorkflow()
    result = workflow.run(origin_case_path, output_dir)
    
    if result.status == WorkflowStatus.SUCCESS:
        return True, result.data
    else:
        raise Exception(result.message)

if __name__ == '__main__':
    origin_case_path = r"C:\Users\<USER>\Downloads\19PFv3_TestCase_Auto_MET-G_CSTMLST-CSTD-A0-06-A-C0 (全集).xlsx"
    print(bug_commit_service(origin_case_path))