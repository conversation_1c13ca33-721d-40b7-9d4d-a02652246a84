# 汽车软件开发AI辅助系统 - 系统配置

# 系统基本配置
system:
  name: "汽车软件开发AI辅助工作流自动化系统"
  version: "1.0.0"
  description: 基于V字开发流程的AI辅助系统
  log_level: "INFO"
  data_dir: "./data"
  output_dir: "./output"
  temp_dir: "./temp"

# 执行引擎配置
engine:
  # 最大并行执行的步骤数
  max_parallel_workers: 5
  # 执行超时时间（秒）
  step_timeout: 300
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 5

# 日志配置
logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  # 日志文件配置
  file:
    enabled: true
    path: logs/system.log
    max_size_mb: 100
    backup_count: 5

# 数据存储配置
storage:
  # 工作目录
  work_dir: ./workspace
  # 输出目录
  output_dir: ./output
  # 临时文件目录
  temp_dir: ./temp
  # 数据持久化
  persistence:
    enabled: true
    type: json  # json/database
    path: ./data

# NLP配置
nlp:
  # 查询解析配置
  query_parser:
    # 最小置信度阈值
    min_confidence: 0.6
    # 是否启用模糊匹配
    fuzzy_matching: true
  # 实体识别配置
  entity_recognition:
    # 是否提取文件路径
    extract_file_paths: true
    # 是否提取commit ID
    extract_commit_ids: true

# AI Agent配置
agents:
  # 默认AI模型配置
  default_model: gpt-4
  # API配置
  api:
    # API端点
    endpoint: https://api.openai.com/v1
    # 超时设置（秒）
    timeout: 60
    # 重试次数
    max_retries: 3
  # 提示词模板路径
  prompt_templates_dir: ./prompts

# 工具配置
tools:
  # Git配置
  git:
    # 默认仓库路径
    default_repo_path: ./git_repo
    # 是否自动拉取最新代码
    auto_pull: true
  # 文档生成工具
  document_generator:
    # 模板目录
    templates_dir: ./templates
    # 输出格式
    output_formats: 
      - pdf
      - docx
      - markdown
  # 代码检查工具
  code_checker:
    # 规则文件路径
    rules_file: ./config/coding_rules.yaml
    # 忽略的文件模式
    ignore_patterns:
      - "*.test.js"
      - "*_test.py"

# 通信配置
communication:
  # CAN配置
  can:
    # 信号定义文件
    signal_definitions: ./config/can_signals.json
    # BitAssign文件
    bit_assign_file: ./config/bit_assign.json
  # 其他协议配置
  protocols:
    - name: LIN
      config_file: ./config/lin_config.json
    - name: Ethernet
      config_file: ./config/ethernet_config.json

# 测试配置
testing:
  # 测试用例模板
  test_case_templates: ./templates/test_cases
  # Bug跟踪系统
  bug_tracking:
    system: redmine
    url: https://redmine.example.com
    project_id: automotive_software
  # 设备测试配置
  device_testing:
    # 支持的设备类型
    supported_devices:
      - ECU
      - HIL
      - SIL

# 安全配置
security:
  # 数据加密
  encryption:
    enabled: true
    algorithm: AES-256
  # 访问控制
  access_control:
    enabled: true
    # 角色定义
    roles:
      - name: developer
        permissions:
          - execute_dev_steps
          - view_results
      - name: tester
        permissions:
          - execute_test_steps
          - submit_bugs
          - view_results
      - name: admin
        permissions:
          - all

# 监控配置
monitoring:
  # 性能监控
  performance:
    enabled: true
    # 记录执行时间阈值（秒）
    slow_step_threshold: 60
  # 资源监控
  resources:
    enabled: true
    # CPU使用率阈值
    cpu_threshold: 80
    # 内存使用率阈值
    memory_threshold: 80

# 报告配置
reporting:
  # 报告生成配置
  generation:
    # 自动生成报告
    auto_generate: true
    # 报告格式
    formats:
      - html
      - pdf
      - json
  # 报告模板
  templates:
    summary: ./templates/reports/summary.html
    detailed: ./templates/reports/detailed.html
  # 通知配置
  notifications:
    enabled: true
    # 邮件通知
    email:
      enabled: true
      smtp_server: smtp.example.com
      smtp_port: 587
      use_tls: true
    # Webhook通知
    webhook:
      enabled: false
      url: https://hooks.example.com/workflow

# LLM配置
llm:
  clients:
    openai:
      model_name: "gpt-4"
      api_endpoint: "https://api.openai.com/v1"
      api_key: "${OPENAI_API_KEY}"
      temperature: 0.2
      max_tokens: 2048
      additional_params:
        top_p: 0.95
    azure_openai:
      model_name: "gpt-4-turbo"
      api_endpoint: "https://${AZURE_OPENAI_RESOURCE}.openai.azure.com"
      api_key: "${AZURE_OPENAI_KEY}"
      temperature: 0.1
      max_tokens: 4096
      additional_params:
        deployment_id: "${AZURE_OPENAI_DEPLOYMENT_ID}"
        api_version: "2023-05-15"
  default_client: "openai"

# 提示词配置
prompt:
  templates_dir: "./prompts"
  default_templates:
    system_requirement: |
      你是一个专业的汽车软件需求分析专家，擅长分析和理解软件需求。
      你需要从用户提供的需求描述中提取以下信息：
      1. 功能性需求列表
      2. 非功能性需求列表
      3. 约束条件
      4. 优先级
      
      请以JSON格式返回分析结果。
    
    system_design: |
      你是一个专业的汽车软件设计专家，擅长软件架构设计和详细设计。
      根据提供的需求，你需要生成包含以下部分的设计方案：
      
      # 架构设计
      [在此处提供整体架构设计]
      
      # 组件设计
      [在此处提供主要组件的设计]
      
      # 接口设计
      [在此处提供关键接口的设计]
      
    system_code_review: |
      你是一个专业的代码审查专家，擅长发现代码中的问题和改进点。
      请审查提供的代码，并提供以下反馈：
      
      # 总体评价
      [总体代码质量评价]
      
      # 问题列表
      - **问题**: [问题描述]
        **位置**: [问题位置]
        **严重性**: [高/中/低]
        **建议**: [修复建议]
      
      # 改进建议
      [整体改进建议] 