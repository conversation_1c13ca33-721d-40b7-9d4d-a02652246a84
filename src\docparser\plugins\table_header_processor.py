import re
import logging
from typing import Any, Dict, List, Optional, Union
import numpy as np
from docparser.interfaces.plugin_interface import TableProcessorPlugin
from docparser.models.document import DocumentBlockObject, DocumentObject
from docparser.models.table import TableObject
import json
from kotei_formal_rule.match_rule import translate_parse

# Configure logging
logger = logging.getLogger('docparser')


class TableHeaderProcessorPlugin(TableProcessorPlugin):
    """
    Plugin for analyzing table objects in documents.
    Adds metadata about table content such as word count, character count, etc.
    """

    def get_plugin_name(self) -> str:
        """Get plugin name"""
        return "TableHeaderProcessor"

    def get_plugin_version(self) -> str:
        """Get plugin version"""
        return "1.0.0"

    def get_supported_document_types(self) -> List[str]:
        """Get supported document types"""
        return ["docx", "doc"]  # Support all document types

    def process_document(self, document: DocumentObject):
        """
        Process document data.

        Args:
            document: Dictionary containing parsed document

        Returns:
            Processed document data
        """
        logger.info("Processing document with TableHeaderProcessor plugin")
        document_data = document.document[0]
        # Process table objects
        if  document_data.tables:
            self.process_table_objects(document_data.tables, document.file_name, document_data.name)

        # Add document-level statistics
        # document_data.metadata['table_analysis'] = self._analyze_document_table(document_data)
        #
        # return document_data

    def process_table_objects(self, table_objects: List[TableObject], file_name: str, block_name: str):
        """
        Process table objects.

        Args:
            table_objects: List of table objects

            file_name: file_name of document

            block_name: sheet_name or ''

        Returns:
            Processed table objects
        """
        for table_obj in table_objects:

            rule_db = translate_parse(table_obj, file_name, block_name, table_obj.layout.parent_content, '')
            rule = rule_db.get("module_name", "")
            if rule == 'table_parse_multi_line_header' :  # rule 注册的插件名
                self.table_parse_multi_line_header_handle(table_obj, rule_db["rule_data"])
            if not table_obj.head_list:
                self.table_header_common_parser_handle(table_obj)

            # if 'table' in table_obj:
            #     # Add table analysis metadata
            #     table_obj['metadata']['analysis'] = self._analyze_table(table_obj['table'])

    def table_header_common_parser_handle(self, data: TableObject):
        # 定义cell矩阵
        matrix = np.array([row.cells for row in data.rows])

        # 提取每个cell对象的 "background_color" 属性值
        background_color_matrix = np.array([[cell.style.background_color for cell in row] for row in matrix])

        find_vertical_heads_by_color = self.try_find_heads_by_color(background_color_matrix, data, 'vertical')
        if not find_vertical_heads_by_color:
            find_horizontal_heads_by_color = self.try_find_heads_by_color(background_color_matrix.T, data,
                                                                     'horizontal')
            if not find_horizontal_heads_by_color:
                logging.info(f"TableHeaderCommonParser could not find headers by background_color")

    def get_heads_by_color(self, background_color_matrix):
        heads = []
        for i in range(1, background_color_matrix.shape[1]):
            if background_color_matrix[0, i] != background_color_matrix[0, i - 1]:
                # 颜色区分
                heads = [x for x in range(i)]
                break
        return heads

    def try_find_heads_by_color(self, background_color_matrix, data: TableObject, head_type):
        all_consistent = True
        for i in range(background_color_matrix.shape[1]):
            column = background_color_matrix[:, i]
            is_consistent = np.all(column == column[0])
            if not is_consistent:
                all_consistent = False
                break
        find_heads_by_color = False
        if all_consistent:
            heads = self.get_heads_by_color(background_color_matrix)
            if heads:
                # 2.1 有列颜色一致性且有区分颜色
                find_heads_by_color = True
                data.head_type = head_type
                data.head_list = heads
                logging.info(
                    f"try_find_heads_by_color head_list: {heads} head_type: {head_type}, data: {data.rows[0]}")
        return find_heads_by_color

    def table_parse_multi_line_header_handle(self, data: TableObject, rule_data=None) -> TableObject:
        """
       通过表头中包含的特定文字, 识别出一行或多行表头
       :param data: 表格对象
       :rule_data: {"head_type":"","head_info": [{"row_index": 0, "head": ["A" , "B"]}]}
       :return: 处理完成表格对象 TableObject
       """
        rule_data = self.format_rule_data(rule_data)
        if not rule_data:
            return data

        head_list = []
        for rows in rule_data:
            for index, head_cols in rows:
                if len(data.rows) <= index:
                    continue
                row = data.rows[index]
                list_ = head_cols
                if not list_:
                    continue
                if self.check_elements_in_list([list_], [cell.text for cell in row.cells]):
                    head_list.append(index)
                else:
                    head_list = []
                    break
            if head_list:
                break
        data.head_list = head_list
        logging.info(f"TableParseMultiLineHeaderMark HEAD_MARK_LIST: {rule_data} head_list: {head_list}")
        return data

    def format_rule_data(self, rule_data):
        """
        rule_data: {"head_type":"表头列组合","head_info":
        [{"row_index": 1, "head_cols": ["C"]}, {"row_index": 0, "head_cols": ["A" , "B"]}] }
        return [["A" , "B"],["C"]]
        """
        results = []
        datas = json.loads(rule_data)
        for data in datas:
            head_info = data.get("head_info", [])
            head_list = sorted(head_info, key=lambda x: x['row_index'])
            rows = []
            for head in head_list:
                rows.append((head["row_index"], head["head_cols"]))
            if rows:
                results.append(rows)
        return results

    def check_elements_in_list(self, head_mark_list: List, list_to_check: List):
        """
        检查 list_to_check 中的元素是否包含 head_mark_list 中的所有元素
        :param head_mark_list: 需要检查的列表
        :param list_to_check: 被检查的列表
        :return: 如果 head_mark_list 中的所有元素都在 list_to_check 中找到，返回 True，否则返回 False
        """
        for config_list in head_mark_list:
            # 检查 config_list 中的每个元素是否都在 list_to_check 中
            con_exists = True
            for con in config_list:
                if con not in list_to_check:
                    con_exists = False
                    break
            if con_exists:
                return True
        return False

    def validate_document(self, document_data: Dict[str, Any]) -> bool:
        """
        Validate document data.

        Args:
            document_data: Dictionary containing parsed document data

        Returns:
            True if document data is valid, False otherwise
        """
        # Check if document has table objects
        if 'table_objects' not in document_data or not document_data['table_objects']:
            logger.warning("Document has no table objects")
            return False

        return True

    def _analyze_table(self, table: str) -> Dict[str, Any]:
        """
        Analyze table content.

        Args:
            table: Table content

        Returns:
            Dictionary containing table analysis results
        """
        # Count words
        words = re.findall(r'\b\w+\b', table)
        word_count = len(words)

        # Count characters
        char_count = len(table)
        char_count_no_spaces = len(table.replace(' ', ''))

        # Count sentences
        sentences = re.split(r'[.!?]+', table)
        sentence_count = len([s for s in sentences if s.strip()])

        # Calculate average word length
        avg_word_length = sum(len(word) for word in words) / word_count if word_count > 0 else 0

        # Calculate average sentence length
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0

        # Find most common words
        word_freq = {}
        for word in words:
            word_lower = word.lower()
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1

        most_common_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            'word_count': word_count,
            'char_count': char_count,
            'char_count_no_spaces': char_count_no_spaces,
            'sentence_count': sentence_count,
            'avg_word_length': avg_word_length,
            'avg_sentence_length': avg_sentence_length,
            'most_common_words': most_common_words
        }

    def _analyze_document_table(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze all table in the document.

        Args:
            document_data: Dictionary containing parsed document data

        Returns:
            Dictionary containing document table analysis results
        """
        # Combine all table
        all_table = ""

        # Add table from table objects
        for table_obj in document_data.get('table_objects', []):
            all_table += table_obj.get('table', '') + " "

        # Add table from table cells
        for table_obj in document_data.get('table_objects', []):
            for cell in table_obj.get('cells', []):
                all_table += cell.get('table', '') + " "

        # Analyze combined table
        return self._analyze_table(all_table)
