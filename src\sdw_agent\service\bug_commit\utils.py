"""
Bug提交工作流辅助工具

提供Bug分析、LLM调用等辅助功能
"""

import os
import re
import time, shutil, yaml
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
from sdw_agent.config.config import ROOT_DIR

from loguru import logger
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import AIMessage
from openpyxl import Workbook, load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import PatternFill

from sdw_agent.llm.llm_util import get_ai_message
from .models import BugSeverity, BugInfluence, BugCaseInfo, BugAnalysisResult


def extract_number(content: str) -> Optional[str]:
    """
    从内容中提取开头的连续数字
    
    Args:
        content: 需要提取数字的内容
        
    Returns:
        Optional[str]: 提取到的数字字符串，如未提取到则为None
    """
    if not content or not isinstance(content, str):
        return None
        
    match = re.match(r'^(\d+)', content.strip())
    return match.group(1) if match else None


def common_llm(prompt):
    """提供prompt以及填充到prompt中的信息，组织LLM调用方法入参、获取结果并返回"""
    # 组合成聊天提示模板
    chat_prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题"),
    ("user", "{question}"),
])
    invoke_dict = {
        "question": prompt
    }
    llm_response: AIMessage = get_ai_message(chat_prompt, invoke_dict)
    result = llm_response.content
    return result

def write_theme_llm(new_precondition,right_screen_display):

    prompt = f"""
    我需要你帮我将两个分别描述操作过程和结果的文本用一句话总结，可以补充一些词句，不改变原文本含义，待匹配文本可能是中文、日文、英文。
    <示例>
        操作过程：已进入第0阶层光标选中“時計設定”，时间为12:55短按“ENTER”
        结果：时间显示为13:00
        输出：进入第0阶层光标选中“時計設定”，时间为12:55时短按“ENTER”，时间显示为13:00
    </示例>
    
    <示例>
        操作过程：1.发送车速7.9km/h，光标选中“時計設定”;\n2.发送信号PTSYS = 5，短按“ENTER”
        结果：按下立即响应（分を00に合わせます）
        输出：当发送车速7.9km/h，发送信号PTSYS = 5时，光标选中“時計設定”后短按“ENTER”，立即响应（分を00に合わせます）
    </示例>
    现在，请你参考上述示例，根据下面的操作过程和结果，补全输出的内容。
    <待拼接文本>
        操作过程：{new_precondition}
        结果：{right_screen_display}
        输出：
    </待拼接文本>
    """
    try:
        logger.info(f"开始对bug进行总结性描述，prompt为：{prompt}")
        result = common_llm(prompt)
        logger.info(f"完成对bug的总结性描述，生成描述为：{str(result).strip()}")
        return str(result).strip()
    except Exception as e:
        logger.error(f"对bug总结性描述生成失败，失败原因：{str(e)}")
        return ""


def match_module_llm(module_rough):
    prompt = f"""
    我需要你帮我做一个词义匹配，要求是从<词语全集>里面找出一个和<待匹配词>最接近的一个选项，待匹配词可能是中文、日文、英文。
    <待匹配词>
        {module_rough}
    </待匹配词>
    <词语全集>
        Speedo
        Tacho
        Fuel
        Dimmer
        Temp
        ODO/Trip
        Shift
        Indicator
        Buzzer
        Warning
        SOC
        Hybsys
        Adas
        HUD
        共通
        MM连携
        按键
        画面迁移
    </词语全集>
    <要求>
        你必须在<词语全集>里选一个，只输出<词语全集>里和<待匹配词>词义最接近的词，无需任何其他解释
    </要求>
    """
    try:
        logger.info(f"开始进行module匹配，prompt为：{prompt}")
        result = common_llm(prompt)
        logger.info((f"module匹配结束，结果为：{str(result).strip()}"))
        return str(result).strip()
    except Exception as e:
        logger.error(f"module匹配失败，失败原因为：{str(e)}")
        return ""


def write_severity_llm(chunk_markdown):
    chunk_markdown_str = '\n'.join(chunk_markdown)

    prompt = f"""
    下面的信息取自一个表格，代表了一个汽车软件相关功能的测试用例，该条测试用例被标记为'NG'不通过，即相关软件功能有bug。
    请你仔细分析这条测试用例，按照<Severity>里的内容，为这条bug评估出一个严重程度。
    <测试用例>
        {chunk_markdown_str}
    </测试用例>
    <Severity>
        S： 非常严重，可能导致用户有生命危险
        A： 仪表花屏、黑屏、死机、卡滞、不能休眠（24h内解决）
        B： 功能不动作：TT不能点亮、ADAS功能不能触发、Customize中某功能未实现、警告不显示
        C： 显示问题：显示不完整、文字内容超框、交互内容显示先后顺序错误
        D： 建议修改类问题、字符不统一、大小不一致、排列不美观等不影响使用，但影响用户体验
    </Severity>
    <要求>
        只输出对应Severity代表的字母，如 A，不要任何多余的解释
    </要求>
    """
    try:
        logger.info(f"开始进行bug严重程度评估，prompt为：{prompt}")
        result = common_llm(prompt)
        logger.info(f"对bug严重程度评估结束，结果为：{str(result).strip()}")
        return str(result).strip()
    except Exception as e:
        logger.error(f"对bug严重程度评估失败，失败原因为：{str(e)}")
        return ""


def write_bug_influence_llm(chunk_markdown):
    chunk_markdown_str = '\n'.join(chunk_markdown)

    prompt = f"""
        下面的信息取自一个表格，代表了一个汽车软件相关功能的测试用例，该条测试用例被标记为'NG'不通过，即相关软件功能有bug。
        请你仔细分析这条测试用例，按照<bug影响度>里的内容，为这条bug评估出一个影响程度。
        <测试用例>
            {chunk_markdown_str}
        </测试用例>
        <bug影响度>
            A0：重大问题（reset、黑画
            A1：对车完有重要影响的S级问题
            A2：对车完有影响的A级问题
            B0：功能完全不动作
            B1：功能部分动作
            C：显示问题，功能本身已实装
        </bug影响度>
        <要求>
            只输出对应bug影响度的代号，如 A0，不要任何多余的解释
        </要求>
        """
    try:
        logger.info(f"开始进行bug影响程度评估，prompt为：{prompt}")
        result = common_llm(prompt)
        logger.info(f"对bug影响程度评估结束，结果为：{str(result).strip()}")
        return str(result).strip()
    except Exception as e:
        logger.error(f"对bug影响程度评估失败，失败原因为：{str(e)}")
        return ""


def get_procedure_llm(chunk_markdown,new_precondition,expected_results,actual_results,keywords_Remarks):
    chunk_markdown_str = '\n'.join(chunk_markdown)

    prompt = f"""
    下面的信息取自一个表格，代表了一个汽车软件相关功能的测试用例，该条测试用例被标记为'NG'不通过，即相关软件功能有bug。
    请你仔细分析这条用例的信息，分析它的步骤和现象，按照下面的<示例>里的格式输出这条测试用例的Procedure.输出内容可能是中文、日文、英文。
    <测试用例>
        {chunk_markdown_str}
    </测试用例>
    <操作手顺>
        {new_precondition}
        {expected_results}
        {actual_results}
        {keywords_Remarks}
    </操作手顺>
    <示例>
        【前提条件】
        1. +B-ON、IG-ON
        2. 电压13.5V
        【操作步骤】
        1.发送车速信号SP1=9000
        2.进入设定画面，任意进入第一阶层查看辅助文言显示
        【预期结果】
        辅助文言置灰
        【实际结果】
        辅助文言没置灰
        【备注】
        面企画也有同样的问题
    </示例>
    <要求>
        请只输出'示例'里的字符串内容，不要任何多余的解释。【】与【】中间无需空行。
        
        【前提条件】里的内容参照用例表格“+B”、“IG”、“电压”列里的说明，不要任何多余的解释
        例如：“ “+B”、“IG”、“电压”内容说明为 ON、13.5V”，此处测试步骤中【前提条件】填：1. +B-ON、IG-ON \n 2. 电压13.5V
        
        【操作步骤】里的内容，参照<操作手顺>中 {new_precondition}的语句，可以追加一些词句，让语句更加通顺，但不能改变 {new_precondition}字符串原本含义
        例如：【操作步骤】内容填：1.已进入第0阶层光标选中“時計設定” \n 2.时间为12:05,短按“ENTER”
        
        【预期结果】里的内容，参照<操作手顺>中{expected_results}的语句，可以追加一些词句，让语句更加通顺，但不能改变 {expected_results}字符串原本含义
        例如：{expected_results}的语句为：“ウイジェット”，此处【预期结果】的内容填： “シンプル下级阶层显示项目表示：ウイジェット”
        
        【实际结果】里的内容，参照<操作手顺>中{actual_results}的语句，可以追加一些词句，让语句更加通顺，但不能改变 {actual_results}字符串原本含义
        例如：{actual_results}的语句为：“シーン拡張”，此处【实际结果】的内容填： “シンプル下级阶层显示项目表示：シーン拡張”
        
        【备注】里的内容，参照<操作手顺>中{keywords_Remarks}的语句，如果内容为空，则显示“-”，去掉概率相关的描述(数字内容去掉)，如果有内容，可以追加一些词句，让语句更加通顺。
        例如：{keywords_Remarks}内容为 “CONV动力类型下不复现，HV动力类型下复现，复现概率为50%”，此处测试步骤中【备注】填：CONV动力类型下不复现，HV动力类型下复现							
    </要求>
    """
    try:
        logger.info(f"开始生成bug的Procedure，prompt为：{prompt}")
        result = common_llm(prompt)
        logger.info(f"生成bug的Procedure结束，结果为：{str(result).strip()}")
        return str(result).strip()
    except Exception as e:
        logger.error(f"生成bug的Procedure失败，失败原因为：{str(e)}")
        return ""


def get_occur_rate(Remarks):
    try:
        logger.info(f"开始获取occur_rate，输入信息为：{Remarks}")
        if not Remarks:
            return '必现(100%)'
        pct = re.search(r'(?:\d+/\d+)|(?:\d+(?:\.\d+)?%?)', Remarks)
        if pct:
            pct = pct.group()
            if '%' in pct or (str(pct).isdigit() and int(pct) < 100):
                pct = str(int(pct.split('%')[0])) + '%'
            elif '/' in pct:
                pct = str(int(eval(pct) * 100)) + '%'
            else:
                pct = '必现(100%)'
        else:
            pct = '必现(100%)'

        if pct == '30%':
            ret = "小概率(30%)"
        elif pct == '70%':
            ret = "大概率(70%)"
        elif pct == '100%':
            ret = '必现(100%)'
        else:
            ret = pct
        logger.info(f"获取occur_rate结束，结果为：{ret}")
        return ret
    except Exception as e:
        logger.error(f"获取occur_rate失败，失败原因为：{str(e)}")
        return ""


def get_finish_date(ng_dict, severity):
    date_time = ng_dict.get('Date')
    try:
        logger.info(f"开始对bug完成时间进行标准化处理，待标准化时间为：{date_time}")
        cost_time = {'A': 1, 'B': 2, 'C': 3, 'D': 7}
        if not date_time:
            return ""
        date_str = date_time.split()[0]
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        # 加上 7 天
        new_date = date_obj + timedelta(days=cost_time.get(severity, 1))
        logger.info(f"对bug完成时间进行标准化处理结束，标准化时间为：{new_date}")
        return new_date.strftime("%Y/%m/%d")
    except Exception as e:
        logger.error(f"对bug完成时间标准化处理失败，失败原因为：{str(e)}")
        return date_time


def get_book_orca(file_name, ng_dict):
    try:
        logger.info(f"开始获取book_orca信息")
        if '基本功能' not in file_name:
            book_orca = file_name.split('Auto_')[1].split('.')[0]
        else:
            book_orca = ng_dict.get('对应式样书')
        logger.info(f"book_orca信息获取结束，获取结果为：{book_orca}")
        return book_orca
    except Exception as e:
        logger.error(f"获取book_orca失败，失败原因为：{str(e)}")
        return ""


def extract_module_content(file_path: str) -> str:
    """
    从文档路径中提取 `-CSTD` 前的关键词，支持多种分隔符
    示例:
    "19PFv3_..._調光処理(ステップ)-CSTD-..." → "調光処理(ステップ)"
    "19PFv3_..._MET-ST_LAUNCH-CSTD-..." → "LAUNCH"

    Args:
        file_path: 文档的完整路径

    Returns:
        提取的关键词，如果未找到则返回空字符串
    """
    # 获取文件名（不含路径）
    file_name = os.path.basename(file_path)

    # 正则表达式模式：
    # 1. 匹配 `-CSTD` 前的最后一个分隔符后的内容
    # 2. 分隔符支持 `-`、`_` 或 `.`
    pattern = r'(?:[-_.]|^)([^-_.]+)-CSTD'

    # 搜索匹配项
    match = re.search(pattern, file_name)

    if not match:
        return ""

    # 获取匹配的关键词
    keyword = match.group(1)

    # 关键词映射：将特定关键词替换为目标值
    keyword_mapping = {
        'CSTMLST': 'Setting', '調光処理(ステップ)': 'Dimmer',  'BRPADW': 'Warning',
    }
    # 应用映射（如果存在）
    return keyword_mapping.get(keyword, keyword)

"""
#车辆种类规则暂时未定义，项目组内协商暂不使用以下逻辑
def extract_vehicles_type(input_str: str) -> str:

    从输入字符串中提取 PTSYS 相关信息（不区分大小写）
    支持映射规则：
    - PTSYS = 1或2 → CONV
    - PTSYS = 3 → HEV
    - PTSYS = 4 → PHEV
    - PTSYS = 7 → FCEV
    - PTSYS = 5或6 → BEV
    其他值返回 "车辆类型异常"
    未找到匹配信息返回 "全车型共通"

    # 定义常量和映射关系
    VALID_PTSYS_VALUES = {'1', '2', '3', '4', '5', '6', '7'}
    PTSYS_MAPPING = {
        '1': 'CONV-燃油车',
        '2': 'CONV-燃油车',
        '3': 'HEV-油电混动车',
        '4': 'PHEV-插电式混合动力汽车',
        '5': 'BEV-纯电动车',
        '6': 'BEV-纯电动车',
        '7': 'FCEV-燃料电池车',
    }
    DEFAULT_RESULT = "全车型共通"
    ERROR_RESULT = "车辆类型异常"

    # 检查输入是否为字符串
    if not isinstance(input_str, str):
        return DEFAULT_RESULT

    # 检查字符串是否为空
    if not input_str.strip():
        return DEFAULT_RESULT

    try:
        # 使用正则表达式查找 PTSYS = 值（忽略大小写）
        match = re.search(r'(?i)PTSYS\s*=\s*(\d+)', input_str)

        # 未找到匹配时返回默认值
        if not match:
            return DEFAULT_RESULT

        # 提取 PTSYS 值
        ptsys_value = match.group(1)

        # 检查值是否在有效范围内
        if ptsys_value not in VALID_PTSYS_VALUES:
            return ERROR_RESULT

        # 返回映射结果
        return PTSYS_MAPPING[ptsys_value]

    except Exception as e:
        # 记录异常信息（实际应用中可替换为日志记录）
        print(f"处理 PTSYS 时发生异常: {str(e)}")
        return DEFAULT_RESULT
"""

def get_ng_chunk(result_rows):
    """
    对ng的结果字典进行格式整合
    """
    logger.info(f"开始对NG结果进行MD格式的拼接")
    chunk_markdown = []
    chunk_markdown.append("| " + " | ".join(result_rows.keys()) + " |")
    chunk_markdown.append("|----" * len(result_rows.keys()) + " |")
    chunk_markdown.append("| " + " | ".join(result_rows.values()) + " |")
    logger.info(f"对NG结果进行MD格式的拼接完成")
    return chunk_markdown


def get_redmine_keyword(origin_path, ng_case_dict, chunk_markdown):
    # 基础的bug提交输出模板
    redmine_keywords_dict = {
        "sheet名": "", "测试用例No.": "", "跟踪": "ST_Defect", "主题": "", "状态": "Assigned", "优先级": "", "指派给": "",
        "目标版本": "XXX_NO_PLAN", "Submitter Group": "DNKT", "Module": "", "Found Version": "",
        "Test Environment": "ASSY",
        "测试类别": "", "Occur Rate": "", "Severity": "", "Bug影响度": "", "计划完成日期": "", "Submitted Date": "",
        "Submitter": "", "Assignee": "", "车型(orca)": "", "D1L1版本": "", "DTF版本": "",
        "機能名": "", "Found Defect Methods": "人工检查",  "Procedure": "","车辆种类":""
    }
    logger.info(f"开始为NG样例分析bug提交模板，NG样例为：{ng_case_dict}")
    # 获取测试类别
    redmine_keywords_dict["测试类别"] = "基本功能测试" if "基本功能测试" in origin_path else "其他"
    # 对sheet页名称进行整理
    sheet_name = ng_case_dict.get('sheet_name')
    redmine_keywords_dict["sheet名"] = sheet_name.replace('項目一覧', '').strip()
    # 获取NO号
    redmine_keywords_dict["测试用例No."] = ng_case_dict.get('示例')

    # 获取Module和指派对象信息
    keywords_module = extract_module_content(origin_path)
    redmine_keywords_dict["Module"] = keywords_module

    # 对车型(orca)和主题信息进行拼接
    car_orca = "19PFV3共通"
    redmine_keywords_dict["车型(orca)"] = car_orca

    #获取前提条件内容
    keywords_precondition = ng_case_dict.get("Precondition")
    new_precondition = keywords_precondition.replace("-", ",")  # 空字符串替换安全
    # 获取画面显示并分割（安全处理）
    screen_display = ng_case_dict.get("画面显示", "")  # 默认空字符串
    # 使用partition安全分割，无论是否存在分隔符
    expected_results, separator, actual_results = screen_display.partition("-")
    if not separator:  # 未找到"-"
        actual_results = "未找到分隔符'-'"  # 或其他默认值

    # 获取主题
    redmine_keywords_dict["主题"] = (f"【{car_orca}】【{redmine_keywords_dict['Module']}】"
                                    + str(write_theme_llm(new_precondition,actual_results)))

    redmine_keywords_dict["指派给"] = f"【{keywords_module}】担当"

    # 获取 Found Version
    ng_version = ng_case_dict.get("Version")
    # redmine_keywords_dict["Found Version"] = "E3" if "E3" in ng_case_dict.get('Version') else "E1"
    if ng_version:
        try:
            redmine_keywords_dict["Found Version"] =ng_version.split()[0].split('E3：')[1]
        except Exception as e:
            logger.error(f"从{ng_version}中获取Found Version失败，失败原因为：{str(e)}")
    # 获取Occur Rate，待确认逻辑
    # redmine_keywords_dict["Occur Rate"] = ng_case_dict.get('Remarks') if ng_case_dict.get('Remarks') else '必现100%'
    redmine_keywords_dict["Occur Rate"] = get_occur_rate(ng_case_dict.get('Remarks'))

    #获取Remarks内容
    keywords_Remarks = ng_case_dict.get('Remarks')

    # 获取Severity和优先级
    priority_dict = {
        'S': '紧急', 'A': '高', 'B': '普通', 'C': '低', 'D': '低'
    }
    severity = write_severity_llm(chunk_markdown)
    redmine_keywords_dict['Severity'] = severity
    redmine_keywords_dict["优先级"] = priority_dict.get(severity)
    # 获取bug影响度
    redmine_keywords_dict["Bug影响度"] = write_bug_influence_llm(chunk_markdown)
    # 获取计划完成日期
    redmine_keywords_dict["计划完成日期"] = get_finish_date(ng_case_dict, severity)
    # 获取Submitted Date
    submit_time = ng_case_dict.get('Date')
    if submit_time:
        try:
            redmine_keywords_dict["Submitted Date"] = submit_time.split()[0].replace('-', '/')
        except Exception as e:
            logger.error(f"从{submit_time}中获取Submitted Date失败，失败原因为：{str(e)}")
    # 获取Submitter和Assignee
    redmine_keywords_dict["Submitter"] = ng_case_dict.get('Tester')
    redmine_keywords_dict["Assignee"] = ng_case_dict.get('Tester')
    # 获取D1L1版本
    if ng_version:
        try:
            redmine_keywords_dict["D1L1版本"] = ng_version.split()[1].split('CODE：')[1]
        except Exception as e:
            logger.error(f"从{ng_version}中获取D1L1版本失败，失败原因为：{str(e)}")
        try:
            # 获取DTF版本
            redmine_keywords_dict["DTF版本"] = ng_version.split()[-1].split('DTF：')[1]
        except Exception as e:
            logger.error(f"从{ng_version}中获取DTF版本失败，失败原因为：{str(e)}")

    #获取车辆种类
    redmine_keywords_dict["车辆种类"] = "人工确认"
    # 获取式样书名(Orca)
    redmine_keywords_dict["機能名"] = get_book_orca(origin_path, ng_case_dict)
    # 获取Procedure
    redmine_keywords_dict["Procedure"] = str(get_procedure_llm(chunk_markdown,new_precondition,expected_results,actual_results,keywords_Remarks))

    return redmine_keywords_dict


def preprocess_excel(origin_case_path):
    wb = load_workbook(origin_case_path, data_only=True)

    # 遍历工作簿中的所有工作表
    for sheet in wb.worksheets:
        old_title = sheet.title
        new_title = old_title.strip()
        if new_title != old_title and new_title not in wb.sheetnames:
            sheet.title = new_title
    # 保存修改后的Excel文件
    wb.save(origin_case_path)


def write_redmine_keywords_to_excel(data_list, curr_path):
    save_path = curr_path
    try:
        logger.info("开始保存bug提交结果")
        AI_keyword_list = ['主题', 'Module', 'Severity', 'Bug影响度', 'Procedure']
        green_fill = PatternFill(start_color='00FF00', fill_type='solid')
        blue_fill = PatternFill(start_color='0000FF', fill_type='solid')
        wb = Workbook()
        ws = wb.active
        default_sheet = ws
        wb.remove(default_sheet)
        for i, item in enumerate(data_list):
            sheet_name = f"redmine关键词{i + 1}"
            ws = wb.create_sheet(title=sheet_name)
            # 写入字典的每一项
            for j, (key, value) in enumerate(item.items()):
                ws.append([key, value])
                if key not in AI_keyword_list:
                    ws.cell(row=j+1, column=1).fill = green_fill
                else:
                    ws.cell(row=j+1, column=1).fill = blue_fill
        wb.save(save_path)
        logger.info(f"保存bug提交结果结束，保存路径为：{save_path}")
    except Exception as e:
        logger.error(f"保存bug提交结果失败，失败原因为：{str(e)}")
    return save_path


def process_excel_file(ori_path, curr_path, bug_id_dict):
    """
    处理 Excel 文件，在指定 sheet 页的最后一列添加新表头，并在匹配条件的行中填入新值。

    Args:
        ori_path: 原始有bug信息的测试用例文件路径
        curr_path: 需要补充bug id的测试用例保存路径
        bug_id_dict: 需要保存bug id的sheet页、bug id与测试用例编号的映射关系
    """
    try:
        logger.info(f"开始回写bug id，待回写文件为：{ori_path}")
        # 将原始测试用例文件复制一份后，后续在复制文件上进行修改
        now_excel = curr_path
        shutil.copy2(ori_path, now_excel)
        # 获取测试用例的表头行号
        config = yaml.load(open(os.path.join(ROOT_DIR, "service/test_view_generate/config.yaml"), "r", encoding="utf-8"),
                           Loader=yaml.FullLoader)
        test_agent_input_table = config["test_agent_input_table"]["testcases"]
        header_index = test_agent_input_table["header_index"]
        # 加载工作簿
        workbook = load_workbook(now_excel, data_only=True)
        # 遍历每一个需要补充bug id信息的sheet页
        for one_sheet, map_list in bug_id_dict.items():
            # 不存在待补充的bug id信息时过滤
            if not map_list:
                continue
            # 获取指定 sheet
            sheet = workbook[one_sheet]
            # 获取表头行
            header_row = next(sheet.iter_rows(min_row=header_index, max_row=header_index, values_only=True))
            # 查找搜索列的索引
            search_col_idx = header_row.index("示例")
            # 获取最后一列的索引
            last_col_idx = sheet.max_column
            # 在最后一列之后添加新列，并设置表头
            new_col_idx = last_col_idx + 1
            new_col_letter = get_column_letter(new_col_idx)
            sheet[f"{new_col_letter}{header_index}"] = "Bug ID"
            # 查找匹配的行并更新新列的值
            for one_map in map_list:
                for row in sheet.iter_rows(min_row=header_index+1, values_only=False):
                    row_id_num = row[search_col_idx].value
                    if not row_id_num:
                        continue
                    if str(row_id_num) == one_map[0]:
                        row[new_col_idx - 1].value = one_map[-1]
        # 保存工作簿（覆盖原文件）
        workbook.save(now_excel)
        logger.info(f"回写bug id结束，回写文件为：{now_excel}")
        return now_excel
    except Exception as e:
        logger.error(f"回写bug id时失败，失败原因为: {str(e)}")
        return ""