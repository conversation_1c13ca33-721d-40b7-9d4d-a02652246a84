"""
base 车辆选定工作流

V字对应：
2.1 基本設計 base 车辆选定

模块简介和主要功能说明：

"""
import os
from typing import Optional, Union, Dict, Any

from sdw_agent.config.env import ENV
from sdw_agent.service import register_workflow, BaseWorkflow, WorkflowResult, WorkflowStatus
from sdw_agent.service.base_car_select.model import ConfigModel, InputDataModel
from sdw_agent.service.base_car_select.utils.base_analyzer import BaseAnalyzer
from sdw_agent.service.base_car_select.utils.write_excel_util import WriteExcelUtil
from sdw_agent.service.workflow_config import WorkflowConfigManager
from sdw_agent.util.file_base_util import get_output_dir_path, gen_output_path


@register_workflow("communication_fault_cs")
class BaseCarSelectWorkflow(BaseWorkflow):
    """
    通信故障安全CS工作流类

    提供CAN信号和控制信号匹配功能，分析代码变更中的信号使用情况。
    支持BitAssign和SPI JSON两种数据源。
    """
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化通信故障安全CS工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)

        # 注册配置模型
        self.config_manager = self.register_config_model()

        # 初始化工作流特定配置
        self._init_workflow_config()

    def register_config_model(self):
        """注册配置模型用于验证"""
        try:
            config_manager = WorkflowConfigManager(workflow_name="base_car_select")
            config_manager.register_schema("workflow_config", ConfigModel)
            self.logger.info("配置模型注册成功")

            return config_manager
        except Exception as e:
            self.logger.warning(f"配置模型注册失败: {e}")

    def _init_workflow_config(self):
        """初始化工作流特定配置"""
        workflow_config = self.config.get("workflow_config", {})

        self.logger.info("工作流配置初始化完成")

    def validate_input(self, input_data: Union[Dict[str, Any], InputDataModel]) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据，可以是字典或InputDataModel实例

        Returns:
            bool: 验证是否通过
        """
        try:
            # 如果是字典，转换为模型进行验证
            if isinstance(input_data, dict):
                validated_data = InputDataModel(**input_data)
            elif isinstance(input_data, InputDataModel):
                validated_data = input_data
            else:
                self.logger.error("输入数据类型不正确")
                return False

            # 验证Base 式样DB文件是否存在
            if not validated_data.base_db or not os.path.exists(validated_data.base_db):
                self.logger.error(f"Base 式样DB文件不存在: {validated_data.base_db}")
                return False

            # 验证New 式样DB文件是否存在
            if not validated_data.new_db or not os.path.exists(validated_data.new_db):
                self.logger.error(f"New 式样DB文件不存在: {validated_data.new_db}")
                return False

            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入参数验证失败: {e}")
            return False

    def execute(self, input_data: Union[Dict[str, Any], InputDataModel]):
        """

        Args:
            input_data:

        Returns:

        """
        try:
            base_car_analyzer = BaseAnalyzer(input_data, self.config_manager, self.logger)
            new_car_doc_info, recommend_base_info = base_car_analyzer.analyze_base_new_doc(input_data)
            # 生成文件名
            output_dir = get_output_dir_path(ENV.config.output_data_path, 'base_car_select')
            output_path = gen_output_path(output_dir, f"{input_data.new_car_name}")
            writer = WriteExcelUtil(output_path, self.logger)

            output_path = writer.save_recommend_info(new_car_doc_info, recommend_base_info)

            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="Base车辆选定工作流执行成功",
                data={
                    "output_path": output_path
                }
            )
        except Exception as e:
            self.logger.exception(f"工作流执行失败: {e}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"工作流执行失败: {str(e)}",
                error=str(e)
            )


