import json
import os
import time
import asyncio
from sdw_agent.llm.openai_qwen import OpenAIQwen

class MatchTestview:
    def __init__(self):
        self.llm = OpenAIQwen(
            base_url="http://172.30.19.113:11400/v1", api_key="sk-xxx"
        )

    async def testview_match(self, data):
        all_results = []
        for item in data:
            prompt = """
                <instruction>
                你是一名资深汽车项目研发工程师，熟悉整车各功能模块的架构与测试规范。请基于以下要求进行精准匹配：
                </instruction>
    
                <task>
                1. 分析{input}中"testview_content"表的内容，根据每一行的含义，去"test_case"中匹配与该行相关联的测试用例，并用<example>中的格式输出
                2. 如果一条测试用例已经匹配到，则不再匹配
                </task>
    
                <constraints>
                ❗️输出必须满足：
                1. 必须严格按照<example>的格式输出
                </constraints>
                
                <input>
                    {{input}}
                </input>
                
                <example>
                [
                            {
                            "testview_class": "AAAA类",
                            "testview_item": "testview_item1",
                            "source": "source1",
                            "testview_content_list": [
                                {
                                    "testview_content_item": "testview_content_item1",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                },
                                {
                                    "testview_content_item": "xxxx",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                },
                                {
                                    "testview_content_item": "xxxx",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                }
                                ]
                            },
                        
                            {
                            "testview_class": "BBB类",
                            "testview_item": "testview_item2",
                            "source": "source2",
                            "testview_content_list": [
                                {
                                    "testview_content_item": "testview_content_item2",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                },
                                {
                                    "testview_content_item": "xxxx",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                },
                                {
                                    "testview_content_item": "xxxx",
                                    "test_case_list": [
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    },
                                    {
                                        "示例": "xxx",
                                        "模块名称": "xxx",
                                        "确认点": "xxx",
                                        "+B": "xx",
                                        "IG": "xx",
                                        "电压": "xxx",
                                        "Precondition": "xxx",
                                        "画面显示": "xxx",
                                        "sheet_name": "xxx"
                                    }
                                    ]
                                }
                                ]
                            }
                        ]
                </example>
    
                """
            message_input = prompt.replace("{{input}}", json.dumps(item, ensure_ascii=False))

            for _ in range(5):
                print(f"第{_}次调用")
                response_res = await self.llm.generate(message_input)
                if len(response_res) != 0:
                    all_results.append(response_res)
                    break
                else:
                    print("大模型调用失败，等待10s")
                    time.sleep(10)
            # Combine all batch results
        return all_results

async def get_matched_testview(json_file):
    with open(json_file, 'r', encoding='utf-8') as load_f:
        data = json.load(load_f)

    matchtestview = MatchTestview()
    res = await matchtestview.testview_match(data)
    return res
    
if __name__ == "__main__":
    class_res = get_matched_testview(r"C:/test_agent/test_data/result 1.json")
    print(class_res)

    output_dir = r"C:/test_agent/test_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_file = os.path.join(
        output_dir, "matched_testview.json")
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(class_res, f, ensure_ascii=False, indent=2)
    print(f"\n结果已保存至: {output_file}")