# 设计基准Check Sheet工作流配置文件

# 基本配置
name: "设计基准Check Sheet工作流"
description: "分析Excel文件中的设计基准检查表数据，提供数据提取、隐藏行处理等功能"
version: "1.0.0"
author: "SDW Agent"

# 模块特定配置
module_specific:
  # 默认文件配置
  default_file_path: "C:/tdd_input/ソフトウェア 設計基準CS.xlsm"
  default_target_sheet: "設計基準CS-基本設計"
  
  # 数据处理配置
  default_header_row: 6
  default_start_row: 7
  max_rows: 10000
  timeout: 300
  
  # Excel处理配置
  excel_processing:
    data_only: true
    read_only: true
    keep_vba: false
  
  # 表头映射配置
  header_mapping:
    enable_auto_mapping: true
    fallback_prefix: "field_"
    
  # 隐藏行列处理配置
  hidden_processing:
    check_hidden_rows: true
    check_hidden_columns: true
    target_columns: [13, 14, 15]  # M, N, O列
    
  # 数据过滤配置
  filtering:
    enable_empty_row_filter: true
    enable_category_filter: true
    
  # 输出配置
  output:
    include_row_numbers: true
    include_hidden_status: true
    export_format: "json"
    
  # 日志配置
  logging:
    level: "INFO"
    include_progress: true
    log_hidden_rows: true
