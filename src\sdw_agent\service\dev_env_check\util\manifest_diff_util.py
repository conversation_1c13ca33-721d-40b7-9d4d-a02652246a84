"""
Manifest文件对比工具类

提供manifest文件下载、对比和Excel报告生成功能
"""
import difflib
import os
import re
import shutil
import subprocess
import tempfile
import traceback
from datetime import datetime

import requests
from typing import List, Dict, Optional, Tuple

from loguru import logger
from urllib.parse import urlparse

from sdw_agent.config.env import ENV
from sdw_agent.service.dev_env_check.config import manifest_compare_styles, manifest_compare_sheet_map, \
    manifest_sub_map, manifest_compare_file_map
from sdw_agent.service.dev_env_check.models import PackageFileCompareInfo
from sdw_agent.service.dev_env_check.util.config_manager import config_manager_dev_env_check
from sdw_agent.util.excel.core import ExcelUtil


class ManifestDiffUtil:
    """Manifest文件对比工具类"""

    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化工具类

        Args:
            temp_dir: 临时目录，用于存储下载的文件
        """
        self.temp_dir = temp_dir or tempfile.mkdtemp()
        os.makedirs(self.temp_dir, exist_ok=True)

        # 定义样式 - 使用CellStyle类
        self.styles = manifest_compare_styles

    def download_file(self, url: str, local_path: str) -> bool:
        """
        从URL下载文件到本地

        Args:
            url: 文件URL
            local_path: 本地保存路径

        Returns:
            bool: 下载是否成功
        """
        try:
            logger.info(f"正在下载文件: {url}")
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(response.text)

            logger.info(f"文件下载成功: {local_path}")
            return True

        except Exception as e:
            logger.error(f"下载文件失败 {url}: {str(e)}")
            return False

    def get_file_content(self, file_path_or_url: str) -> Tuple[List[str], str]:
        """
        获取文件内容，支持本地文件路径或URL

        Args:
            file_path_or_url: 文件路径或URL

        Returns:
            Tuple[List[str], str]: (文件行列表, 文件标识)
        """
        # 判断是否为URL
        if self._is_url(file_path_or_url):
            # 从URL下载
            filename = os.path.basename(urlparse(file_path_or_url).path) or "manifest.xml"
            local_path = os.path.join(self.temp_dir, filename)

            if self.download_file(file_path_or_url, local_path):
                with open(local_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                return lines, file_path_or_url
            else:
                raise RuntimeError(f"无法下载文件: {file_path_or_url}")
        else:
            # 本地文件
            if not os.path.exists(file_path_or_url):
                raise FileNotFoundError(f"文件不存在: {file_path_or_url}")

            with open(file_path_or_url, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            return lines, os.path.basename(file_path_or_url)

    def _is_url(self, path: str) -> bool:
        """判断是否为URL"""
        return path.startswith(('http://', 'https://'))

    def compare_manifests(self, file1_path_or_url: str, file2_path_or_url: str) -> Dict:
        """
        对比两个manifest文件

        Args:
            file1_path_or_url: 第一个文件路径或URL
            file2_path_or_url: 第二个文件路径或URL

        Returns:
            Dict: 对比结果
        """
        logger.info("开始对比manifest文件")

        # 获取文件内容
        lines1, name1 = self.get_file_content(file1_path_or_url)
        lines2, name2 = self.get_file_content(file2_path_or_url)

        # 使用difflib进行对比
        differ = difflib.unified_diff(
            lines1, lines2,
            fromfile=name1,
            tofile=name2,
            lineterm=''
        )

        # 获取详细差异
        detailed_diff = list(difflib.ndiff(lines1, lines2))

        # 分析tag变化

        result = {
            'file1_url': file1_path_or_url,
            'file2_url': file2_path_or_url,
            'file1_name': name1,
            'file2_name': name2,
            'file1_lines': lines1,
            'file2_lines': lines2,
            'unified_diff': list(differ),
            'detailed_diff': detailed_diff,
        }

        logger.info("manifest文件对比完成")
        return result

    def generate_excel_report(self, compare_result: Dict, output_path: str, target_sheet_name="manifest比較",
                              opened_excel=None) -> str:
        """
        生成Excel对比报告
        """
        logger.info(f"开始生成Excel报告: {output_path}")

        if opened_excel:
            excel = opened_excel
            self._create_side_by_side_sheet(excel, compare_result, target_sheet_name)
            return ""
        else:
            with ExcelUtil(output_path, auto_create=False) as excel:
                # 创建并排对比sheet
                self._create_side_by_side_sheet(excel, compare_result, target_sheet_name)
                current_time = datetime.now()
                timestamp = current_time.strftime("%Y%m%d_%H%M%S")
                output_data_path = os.path.normpath(ENV.config.output_data_path)
                if not os.path.exists(output_data_path):
                    os.makedirs(output_data_path)
                ret_book = os.path.join(output_data_path, f"Manifest_Check_{timestamp}.xlsx")
                if opened_excel is None:
                    excel.save(ret_book)

            logger.success(f"manifest对比报告生成完成: {ret_book}")
            return ret_book

    def get_manifest_compare_book_path(self) -> str:
        """获取样本书路径"""
        return os.path.join(os.path.dirname(__file__), os.path.normpath('../book_data/Manifest_Check_template.xlsx'))

    def get_manifest_from_git(self, repo_url, username, password, tag_name_list, target_file,
                              local_path="./repo") -> list[str]:
        """
        克隆 Git 仓库，按 tag 列表切换，找到目标文件并复制到临时目录。

        Args:
            repo_url (str): Git 仓库的 URL。
            username (str): Git 用户名。
            password (str): Git 密码。
            tag_name_list (List[str]): 要切换的 tag 名称列表。
            target_file (str): 要检查的文件路径（相对于仓库根目录）。
            local_path (str): 克隆到的本地路径（默认 "./repo"）。

        Returns:
            str: 临时目录中目标文件的路径。如果未找到，返回错误信息。
        """
        # 构建带认证信息的 URL
        auth_url = f"http://{username}:{password}@{repo_url.lstrip('http://')}"

        # 路径记忆字典，用于避免重复切换和搜索
        cached_paths = ""

        # 定义临时目录
        temp_dir = os.path.join(os.path.dirname(__file__), "temp_files")
        os.makedirs(temp_dir, exist_ok=True)
        temp_path_list = []
        try:
            # 如果本地路径已存在，删除旧的文件夹（防止干扰）
            if os.path.exists(local_path):
                shutil.rmtree(local_path)
                logger.info(f"删除已存在的目录: {local_path}")
            # 克隆仓库
            subprocess.run(["git", "clone", auth_url, local_path], check=True)
            logger.info("克隆成功！")

            # 切换到仓库目录
            os.chdir(local_path)

            # 遍历 tag 列表
            for tag_name in tag_name_list:

                # 切换到指定 tag
                subprocess.run(["git", "checkout", f"tags/{tag_name}"], check=True)
                logger.info(f"切换到 tag: {tag_name}")

                # 检查目标文件是否存在
                # 如果路径已缓存，直接返回
                if cached_paths:
                    logger.info(f"使用缓存路径: {cached_paths}")
                    file_path = os.path.join(local_path, target_file)
                else:
                    for root, dirs, files in os.walk(local_path):
                        if target_file in files:
                            file_path = os.path.join(root, target_file)
                            logger.info(f"找到文件: {file_path}")
                            # 缓存路径
                            cached_paths = target_file
                            break

                # 复制文件到临时目录
                temp_file_path = os.path.join(temp_dir, f"{tag_name}_{target_file}")
                shutil.copy(file_path, temp_file_path)
                print(f"文件已复制到临时目录: {temp_file_path}")
                temp_path_list.append(temp_file_path)

            # 如果所有 tag 都未找到目标文件
            return temp_path_list

        except subprocess.CalledProcessError as e:
            raise f"Git 操作失败: {e}"
        except Exception as e:
            raise f"发生错误: {e}"
        finally:
            # 切换回初始目录，防止影响后续操作
            os.chdir("..")

    def compare_sub_manifest_from_git(self, sub_type, tag_name_list, excel):
        git_path = config_manager_dev_env_check.config['manifest_git_cfg'].get(sub_type, "")
        username = ENV.config.gerrit.username
        password = ENV.config.gerrit.password
        target_file = manifest_compare_file_map.get(sub_type, "")
        target_sheet_name = manifest_compare_sheet_map.get(sub_type, "manifest比較")
        local_path = os.path.join(os.path.dirname(__file__), "repo")
        scripts = self.get_manifest_from_git(git_path, username, password, tag_name_list, target_file, local_path)
        self.compare_and_generate_report(file1_path_or_url=scripts[0], file2_path_or_url=scripts[1],
                                         target_sheet_name=target_sheet_name, excel=excel)

        return

    def _create_side_by_side_sheet(self, excel: ExcelUtil, result: Dict, target_sheet_name):
        """创建并排对比sheet"""
        sheet_name = target_sheet_name

        # 从ndiff结果中重建原始文件内容
        lines1 = []  # 源文件内容
        lines2 = []  # 目标文件内容

        detailed_diff = result['detailed_diff']
        for diff_line in detailed_diff:
            if not diff_line or len(diff_line) < 2:
                continue

            status = diff_line[0]
            content = diff_line[2:] if len(diff_line) > 2 else ""

            if status == ' ':  # 相同行，两个文件都有
                lines1.append(content.rstrip())
                lines2.append(content.rstrip())
            elif status == '-':  # 只在源文件中存在（被删除的行）
                lines1.append(content.rstrip())
            elif status == '+':  # 只在目标文件中存在（新增的行）
                lines2.append(content.rstrip())
            # 忽略 '?' 行（ndiff的提示行）

        # 使用SequenceMatcher进行精确对比
        matcher = difflib.SequenceMatcher(None, lines1, lines2)

        row = 12
        excel.write_cell(sheet_name, row - 1, 1, f"{result['file1_url']}", write_fuzzy = False)
        excel.write_cell(sheet_name, row - 1, 3, f"{result['file2_url']}", write_fuzzy = False)

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # 相同行
                for k in range(i2 - i1):
                    line_content = lines1[i1 + k]

                    excel.write_cell(sheet_name, row, 1, line_content)
                    excel.write_cell(sheet_name, row, 2, "")
                    excel.write_cell(sheet_name, row, 3, line_content)
                    excel.write_cell(sheet_name, row, 4, "-")
                    excel.write_cell(sheet_name, row, 5, "-")

                    # 设置样式
                    excel.set_cell_style(sheet_name, row, 1, self.styles['same'])
                    excel.set_cell_style(sheet_name, row, 2, self.styles['same'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['same'])
                    row += 1

            elif tag == 'delete':
                # 删除的行（只在原文件中存在）
                for k in range(i2 - i1):
                    line_content = lines1[i1 + k]

                    excel.write_cell(sheet_name, row, 1, f"- {line_content}")
                    excel.write_cell(sheet_name, row, 2, "删除")
                    excel.write_cell(sheet_name, row, 3, "")
                    excel.write_cell(sheet_name, row, 4, "")
                    excel.write_cell(sheet_name, row, 5, "")

                    # 设置删除样式
                    excel.set_cell_style(sheet_name, row, 1, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 2, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 4, self.styles['deleted'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['deleted'])
                    row += 1

            elif tag == 'insert':
                # 新增的行（只在新文件中存在）
                for k in range(j2 - j1):
                    line_content = lines2[j1 + k]

                    excel.write_cell(sheet_name, row, 1, "")
                    excel.write_cell(sheet_name, row, 2, "新增")
                    excel.write_cell(sheet_name, row, 3, f"+ {line_content}")
                    excel.write_cell(sheet_name, row, 4, "")
                    excel.write_cell(sheet_name, row, 5, "")

                    # 设置新增样式
                    excel.set_cell_style(sheet_name, row, 1, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 2, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 3, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 4, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['added'])
                    row += 1

            elif tag == 'replace':
                # 替换的行（修改）- 确保左右对齐
                max_lines = max(i2 - i1, j2 - j1)

                for k in range(max_lines):
                    # 左侧（删除的行）
                    if k < (i2 - i1):
                        left_content = lines1[i1 + k]
                        excel.write_cell(sheet_name, row, 1, f"- {left_content}")
                        excel.set_cell_style(sheet_name, row, 1, self.styles['deleted'])
                    else:
                        excel.write_cell(sheet_name, row, 1, "")
                        excel.set_cell_style(sheet_name, row, 1, self.styles['modified'])

                    # 中间状态列
                    excel.write_cell(sheet_name, row, 2, "修改")
                    excel.set_cell_style(sheet_name, row, 2, self.styles['modified'])

                    # 右侧（新增的行）
                    if k < (j2 - j1):
                        right_content = lines2[j1 + k]
                        excel.write_cell(sheet_name, row, 3, f"+ {right_content}")
                        excel.set_cell_style(sheet_name, row, 3, self.styles['added'])
                    else:
                        excel.write_cell(sheet_name, row, 3, "")
                        excel.set_cell_style(sheet_name, row, 3, self.styles['modified'])
                    excel.write_cell(sheet_name, row, 4, "")
                    excel.write_cell(sheet_name, row, 5, "")
                    excel.set_cell_style(sheet_name, row, 4, self.styles['added'])
                    excel.set_cell_style(sheet_name, row, 5, self.styles['added'])
                    if target_sheet_name == "manifest比較":
                        # 检查是不是子Manifest的行
                        for project_pattern, sub_type, in manifest_sub_map.items():
                            if project_pattern in left_content:
                                # 提取revision标签
                                left_match = re.search(r'revision="([^"]+)"', left_content)
                                right_match = re.search(r'revision="([^"]+)"', right_content)

                                if left_match and right_match:
                                    left_tag = left_match.group(1).split("/")[-1]
                                    right_tag = right_match.group(1).split("/")[-1]
                                    tag_list = [left_tag, right_tag]

                                    # 调用子manifest对比
                                    self.compare_sub_manifest_from_git(sub_type, tag_list, excel)
                                    logger.info(f"处理子manifest: {sub_type}, tags: {tag_list}")

                        row += 1

        excel.auto_fit_columns(sheet_name)

    def compare_and_generate_report(self, file1_path_or_url: str, file2_path_or_url: str,
                                    output_path: str = "", target_sheet_name="manifest比較", excel=None) -> str:
        """
        一键对比并生成报告
        """
        logger.info("开始manifest文件对比和报告生成")

        try:
            # 对比文件
            compare_result = self.compare_manifests(file1_path_or_url, file2_path_or_url)

            # 生成Excel报告
            report_path = self.generate_excel_report(compare_result, output_path, target_sheet_name, excel)

            logger.info("manifest文件对比和报告生成完成")
            return report_path

        except Exception as e:
            logger.error(f"对比和报告生成失败: {str(e)}")
            traceback.print_exc()
            raise e

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时目录失败: {str(e)}")


def compare_manifest_files(config: PackageFileCompareInfo):
    with ManifestDiffUtil() as util:
        result = util.compare_and_generate_report(
            file1_path_or_url=config.pre_manifest_path,
            file2_path_or_url=config.after_manifest_path,
            output_path=util.get_manifest_compare_book_path()
        )
        return result


# 使用示例
if __name__ == "__main__":
    # 示例1: 对比本地文件
    with ManifestDiffUtil() as util:
        result = util.compare_and_generate_report(
            file1_path_or_url=r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release4.2.0.xml",
            file2_path_or_url=r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release6.1.0.xml",
            output_path=util.get_manifest_compare_book_path()
        )
        print(f"报告生成完成: {result}")

    # 示例2: 对比在线文件
    # with ManifestDiffUtil() as util:
    #     result = util.compare_and_generate_report(
    #         file1_path_or_url="https://example.com/manifest1.xml",
    #         file2_path_or_url="https://example.com/manifest2.xml",
    #         output_path="online_manifest_diff.xlsx"
    #     )
    #     print(f"在线文件对比报告: {result}")
