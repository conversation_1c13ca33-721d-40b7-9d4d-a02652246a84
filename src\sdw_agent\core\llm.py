"""LLM交互模块 - 处理与大语言模型的交互"""
from typing import Dict, Any, List, Optional, Union
import os
import json
import logging
import time
import requests
from dataclasses import dataclass, field


@dataclass
class LLMConfig:
    """LLM配置"""
    model_name: str  # 模型名称
    api_endpoint: str  # API端点
    api_key: Optional[str] = None  # API密钥
    timeout: int = 60  # 超时时间（秒）
    max_retries: int = 3  # 最大重试次数
    retry_delay: int = 2  # 重试延迟（秒）
    temperature: float = 0.2  # 温度参数
    max_tokens: int = 2048  # 最大生成token数
    additional_params: Dict[str, Any] = field(default_factory=dict)  # 其他参数
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'LLMConfig':
        """从字典创建配置"""
        return cls(
            model_name=config_dict.get("model_name", "gpt-4"),
            api_endpoint=config_dict.get("api_endpoint", "https://api.openai.com/v1"),
            api_key=config_dict.get("api_key") or os.environ.get("OPENAI_API_KEY"),
            timeout=config_dict.get("timeout", 60),
            max_retries=config_dict.get("max_retries", 3),
            retry_delay=config_dict.get("retry_delay", 2),
            temperature=config_dict.get("temperature", 0.2),
            max_tokens=config_dict.get("max_tokens", 2048),
            additional_params=config_dict.get("additional_params", {})
        )


@dataclass
class LLMMessage:
    """LLM消息"""
    role: str  # 角色：system, user, assistant
    content: str  # 内容
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            "role": self.role,
            "content": self.content
        }


@dataclass
class LLMResponse:
    """LLM响应"""
    content: str  # 响应内容
    model: str  # 使用的模型
    usage: Dict[str, int]  # 使用情况
    finish_reason: str  # 完成原因
    raw_response: Optional[Dict[str, Any]] = None  # 原始响应


class LLMClient:
    """LLM客户端"""
    
    def __init__(self, config: Union[LLMConfig, Dict[str, Any]]):
        """初始化LLM客户端"""
        self.config = config if isinstance(config, LLMConfig) else LLMConfig.from_dict(config)
        self.logger = logging.getLogger(__name__)
        
    def call(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """调用LLM"""
        # 合并配置和参数
        params = {
            "model": self.config.model_name,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
            **self.config.additional_params,
            **kwargs
        }
        
        # 准备消息
        params["messages"] = [msg.to_dict() for msg in messages]
        
        # 调用API
        for attempt in range(self.config.max_retries):
            try:
                self.logger.debug(f"调用LLM API，尝试 {attempt+1}/{self.config.max_retries}")
                response = self._make_api_call(params)
                return self._parse_response(response)
            except Exception as e:
                self.logger.warning(f"LLM API调用失败: {str(e)}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay)
                else:
                    raise RuntimeError(f"LLM API调用失败，已重试 {self.config.max_retries} 次: {str(e)}")
    
    def _make_api_call(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.api_key}"
        }
        
        endpoint = f"{self.config.api_endpoint}/chat/completions"
        
        response = requests.post(
            endpoint,
            headers=headers,
            json=params,
            timeout=self.config.timeout
        )
        
        if response.status_code != 200:
            raise RuntimeError(f"API调用失败: {response.status_code} - {response.text}")
            
        return response.json()
    
    def _parse_response(self, response: Dict[str, Any]) -> LLMResponse:
        """解析API响应"""
        try:
            choices = response.get("choices", [])
            if not choices:
                raise ValueError("API响应中没有choices")
                
            first_choice = choices[0]
            message = first_choice.get("message", {})
            content = message.get("content", "")
            
            return LLMResponse(
                content=content,
                model=response.get("model", self.config.model_name),
                usage=response.get("usage", {}),
                finish_reason=first_choice.get("finish_reason", ""),
                raw_response=response
            )
        except Exception as e:
            raise ValueError(f"解析API响应失败: {str(e)}")


class LLMManager:
    """LLM管理器 - 管理多个LLM客户端"""
    
    def __init__(self):
        """初始化LLM管理器"""
        self.clients: Dict[str, LLMClient] = {}
        self.default_client_name: Optional[str] = None
        self.logger = logging.getLogger(__name__)
        
    def register_client(self, name: str, config: Union[LLMConfig, Dict[str, Any]], is_default: bool = False):
        """注册LLM客户端"""
        self.clients[name] = LLMClient(config)
        self.logger.info(f"注册LLM客户端: {name}")
        
        if is_default or self.default_client_name is None:
            self.default_client_name = name
            self.logger.info(f"设置默认LLM客户端: {name}")
    
    def get_client(self, name: Optional[str] = None) -> LLMClient:
        """获取LLM客户端"""
        client_name = name or self.default_client_name
        
        if client_name is None:
            raise ValueError("未指定客户端名称，且未设置默认客户端")
            
        if client_name not in self.clients:
            raise ValueError(f"未找到名为 {client_name} 的LLM客户端")
            
        return self.clients[client_name]
    
    def call(self, messages: List[LLMMessage], client_name: Optional[str] = None, **kwargs) -> LLMResponse:
        """调用LLM"""
        client = self.get_client(client_name)
        return client.call(messages, **kwargs)
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'LLMManager':
        """从配置创建LLM管理器"""
        manager = cls()
        
        clients_config = config.get("clients", {})
        default_client = config.get("default_client")
        
        for name, client_config in clients_config.items():
            is_default = name == default_client
            manager.register_client(name, client_config, is_default)
            
        return manager 