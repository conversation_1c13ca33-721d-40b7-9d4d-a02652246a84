"""
业务专用Excel工具类示例

这个文件展示了如何继承ExcelUtil来创建业务专用的Excel工具类。
"""

import os
import tempfile
from datetime import datetime
from typing import List, Dict, Any
import pandas as pd

from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange, CommonStyles


class SalesReportExcel(ExcelUtil):
    """销售报表专用Excel工具类"""
    
    def __init__(self, file_path: str, auto_create: bool = True):
        """
        初始化销售报表Excel工具
        
        Args:
            file_path: Excel文件路径
            auto_create: 如果文件不存在是否自动创建
        """
        super().__init__(file_path, engine="openpyxl", auto_create=auto_create)
        
        # 定义销售报表专用样式
        self.title_style = CellStyle(
            font_name="Arial",
            font_size=16,
            font_bold=True,
            font_color="FFFFFF",
            bg_color="4472C4",
            alignment_horizontal="center",
            border_style="thick"
        )
        
        self.summary_style = CellStyle(
            font_name="Calibri",
            font_size=12,
            font_bold=True,
            bg_color="E7E6E6",
            alignment_horizontal="right",
            border_style="thin"
        )
    
    def create_sales_report(self, sheet_name: str = "销售报表"):
        """
        创建销售报表模板
        
        Args:
            sheet_name: 工作表名称
        """
        # 创建工作表（如果不存在）
        if sheet_name not in self.get_sheet_names():
            self.create_sheet(sheet_name)
        
        # 写入标题
        self.write_cell(sheet_name, 1, 1, "销售报表")
        self.merge_cells(sheet_name, "A1:F1")
        self.set_cell_style(sheet_name, 1, 1, self.title_style)
        
        # 写入报表信息
        self.write_cell(sheet_name, 2, 1, f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 写入表头
        headers = ["日期", "产品名称", "销售数量", "单价", "销售额", "备注"]
        for i, header in enumerate(headers, start=1):
            self.write_cell(sheet_name, 4, i, header)
            self.set_cell_style(sheet_name, 4, i, CommonStyles.HEADER)
        
        # 自动调整列宽
        self.auto_fit_columns(sheet_name)
    
    def add_sales_data(self, sheet_name: str, sales_data: List[Dict[str, Any]], start_row: int = 5):
        """
        添加销售数据
        
        Args:
            sheet_name: 工作表名称
            sales_data: 销售数据列表
            start_row: 数据起始行
        """
        for i, record in enumerate(sales_data):
            row = start_row + i
            
            # 写入数据
            self.write_cell(sheet_name, row, 1, record.get('date', ''))
            self.write_cell(sheet_name, row, 2, record.get('product_name', ''))
            self.write_cell(sheet_name, row, 3, record.get('quantity', 0))
            self.write_cell(sheet_name, row, 4, record.get('unit_price', 0))
            self.write_cell(sheet_name, row, 5, record.get('total_amount', 0))
            self.write_cell(sheet_name, row, 6, record.get('remarks', ''))
            
            # 设置样式
            for col in range(1, 7):
                if col in [3, 4, 5]:  # 数字列
                    self.set_cell_style(sheet_name, row, col, CommonStyles.NUMBER)
                else:  # 文本列
                    self.set_cell_style(sheet_name, row, col, CommonStyles.DATA)
    
    def add_summary_section(self, sheet_name: str, summary_data: Dict[str, Any], start_row: int):
        """
        添加汇总部分
        
        Args:
            sheet_name: 工作表名称
            summary_data: 汇总数据
            start_row: 汇总部分起始行
        """
        # 空行
        row = start_row + 1
        
        # 汇总标题
        self.write_cell(sheet_name, row, 4, "汇总信息")
        self.set_cell_style(sheet_name, row, 4, self.summary_style)
        row += 1
        
        # 汇总数据
        summary_items = [
            ("总销售数量:", summary_data.get('total_quantity', 0)),
            ("总销售额:", summary_data.get('total_amount', 0)),
            ("平均单价:", summary_data.get('avg_price', 0)),
            ("产品种类:", summary_data.get('product_count', 0))
        ]
        
        for label, value in summary_items:
            self.write_cell(sheet_name, row, 4, label)
            self.write_cell(sheet_name, row, 5, value)
            self.set_cell_style(sheet_name, row, 4, self.summary_style)
            self.set_cell_style(sheet_name, row, 5, CommonStyles.NUMBER)
            row += 1
    
    def generate_complete_report(self, sales_data: List[Dict[str, Any]], sheet_name: str = "销售报表"):
        """
        生成完整的销售报表
        
        Args:
            sales_data: 销售数据列表
            sheet_name: 工作表名称
        """
        # 创建报表模板
        self.create_sales_report(sheet_name)
        
        # 添加销售数据
        self.add_sales_data(sheet_name, sales_data)
        
        # 计算汇总数据
        if sales_data:
            total_quantity = sum(record.get('quantity', 0) for record in sales_data)
            total_amount = sum(record.get('total_amount', 0) for record in sales_data)
            avg_price = total_amount / total_quantity if total_quantity > 0 else 0
            product_count = len(set(record.get('product_name', '') for record in sales_data))
            
            summary_data = {
                'total_quantity': total_quantity,
                'total_amount': total_amount,
                'avg_price': round(avg_price, 2),
                'product_count': product_count
            }
            
            # 添加汇总部分
            start_row = 5 + len(sales_data)
            self.add_summary_section(sheet_name, summary_data, start_row)


class InventoryExcel(ExcelUtil):
    """库存管理专用Excel工具类"""
    
    def __init__(self, file_path: str, auto_create: bool = True):
        super().__init__(file_path, engine="openpyxl", auto_create=auto_create)
        
        # 定义库存专用样式
        self.low_stock_style = CellStyle(
            font_name="Calibri",
            font_size=11,
            font_bold=True,
            font_color="FF0000",
            bg_color="FFCCCC",
            border_style="thin"
        )
        
        self.high_stock_style = CellStyle(
            font_name="Calibri",
            font_size=11,
            font_bold=True,
            font_color="006600",
            bg_color="CCFFCC",
            border_style="thin"
        )
    
    def create_inventory_sheet(self, sheet_name: str = "库存清单"):
        """创建库存清单模板"""
        if sheet_name not in self.get_sheet_names():
            self.create_sheet(sheet_name)
        
        # 标题
        self.write_cell(sheet_name, 1, 1, "库存管理清单")
        self.merge_cells(sheet_name, "A1:G1")
        self.set_cell_style(sheet_name, 1, 1, CommonStyles.HEADER)
        
        # 表头
        headers = ["产品编码", "产品名称", "当前库存", "安全库存", "最大库存", "状态", "备注"]
        for i, header in enumerate(headers, start=1):
            self.write_cell(sheet_name, 3, i, header)
            self.set_cell_style(sheet_name, 3, i, CommonStyles.HEADER)
    
    def add_inventory_data(self, sheet_name: str, inventory_data: List[Dict[str, Any]]):
        """添加库存数据并自动标记状态"""
        start_row = 4
        
        for i, item in enumerate(inventory_data):
            row = start_row + i
            current_stock = item.get('current_stock', 0)
            safe_stock = item.get('safe_stock', 0)
            max_stock = item.get('max_stock', 0)
            
            # 判断库存状态
            if current_stock <= safe_stock:
                status = "库存不足"
                status_style = self.low_stock_style
            elif current_stock >= max_stock:
                status = "库存过多"
                status_style = CommonStyles.WARNING
            else:
                status = "正常"
                status_style = self.high_stock_style
            
            # 写入数据
            self.write_cell(sheet_name, row, 1, item.get('product_code', ''))
            self.write_cell(sheet_name, row, 2, item.get('product_name', ''))
            self.write_cell(sheet_name, row, 3, current_stock)
            self.write_cell(sheet_name, row, 4, safe_stock)
            self.write_cell(sheet_name, row, 5, max_stock)
            self.write_cell(sheet_name, row, 6, status)
            self.write_cell(sheet_name, row, 7, item.get('remarks', ''))
            
            # 设置样式
            for col in range(1, 8):
                if col == 6:  # 状态列
                    self.set_cell_style(sheet_name, row, col, status_style)
                elif col in [3, 4, 5]:  # 数字列
                    self.set_cell_style(sheet_name, row, col, CommonStyles.NUMBER)
                else:  # 文本列
                    self.set_cell_style(sheet_name, row, col, CommonStyles.DATA)


def demo_sales_report():
    """演示销售报表功能"""
    print("=== 销售报表演示 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 模拟销售数据
        sales_data = [
            {
                'date': '2024-01-01',
                'product_name': '产品A',
                'quantity': 10,
                'unit_price': 100,
                'total_amount': 1000,
                'remarks': '正常销售'
            },
            {
                'date': '2024-01-02',
                'product_name': '产品B',
                'quantity': 5,
                'unit_price': 200,
                'total_amount': 1000,
                'remarks': '促销活动'
            },
            {
                'date': '2024-01-03',
                'product_name': '产品A',
                'quantity': 8,
                'unit_price': 100,
                'total_amount': 800,
                'remarks': '正常销售'
            }
        ]
        
        # 生成销售报表
        with SalesReportExcel(temp_path) as excel:
            excel.generate_complete_report(sales_data)
            excel.save()
        
        print(f"销售报表已生成: {temp_path}")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def demo_inventory_management():
    """演示库存管理功能"""
    print("\n=== 库存管理演示 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 模拟库存数据
        inventory_data = [
            {
                'product_code': 'P001',
                'product_name': '产品A',
                'current_stock': 5,
                'safe_stock': 10,
                'max_stock': 100,
                'remarks': '需要补货'
            },
            {
                'product_code': 'P002',
                'product_name': '产品B',
                'current_stock': 50,
                'safe_stock': 20,
                'max_stock': 80,
                'remarks': '库存正常'
            },
            {
                'product_code': 'P003',
                'product_name': '产品C',
                'current_stock': 90,
                'safe_stock': 15,
                'max_stock': 80,
                'remarks': '库存过多'
            }
        ]
        
        # 生成库存清单
        with InventoryExcel(temp_path) as excel:
            excel.create_inventory_sheet()
            excel.add_inventory_data("库存清单", inventory_data)
            excel.auto_fit_columns("库存清单")
            excel.save()
        
        print(f"库存清单已生成: {temp_path}")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


if __name__ == "__main__":
    print("业务Excel工具类演示...")
    
    try:
        demo_sales_report()
        demo_inventory_management()
        
        print("\n演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
