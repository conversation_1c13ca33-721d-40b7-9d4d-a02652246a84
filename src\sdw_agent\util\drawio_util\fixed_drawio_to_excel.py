#!/usr/bin/env python3
"""
修复版本：正确处理 win32com 的图像插入
"""

import os
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def export_drawio_to_excel_fixed(drawio_file: Optional[str] = None,
                                excel_file: str = "output/fixed_architecture_diagram.xlsx",
                                sheet_name: str = "架构图",
                                title: str = "系统架构图",
                                max_width: int = 1400,
                                max_height: int = 1000) -> Dict[str, Any]:
    """
    修复版本：正确将 Draw.io 文件导出并插入 Excel
    """
    
    # 1. 检查 Playwright
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        return {
            "success": False,
            "message": "Playwright 未安装，请运行: poetry add playwright && poetry run playwright install chromium",
            "method": "playwright"
        }
    
    # 2. 确定 Draw.io 文件路径
    if drawio_file is None:
        drawio_file = template_manager.get_template_path("block_diagram_file")
        if not drawio_file:
            return {
                "success": False,
                "message": "未找到 Draw.io 模板文件",
                "method": "playwright"
            }
    
    if not Path(drawio_file).exists():
        return {
            "success": False,
            "message": f"Draw.io 文件不存在: {drawio_file}",
            "method": "playwright"
        }
    
    logger.info(f"使用 Draw.io 文件: {drawio_file}")
    
    # 3. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        logger.info(f"文件大小: {len(xml_content)} 字符")
        
    except Exception as e:
        return {
            "success": False,
            "message": f"读取 Draw.io 文件失败: {e}",
            "method": "playwright"
        }
    
    # 4. 创建输出目录和永久 PNG 文件
    output_dir = Path(excel_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 使用永久文件名，不删除
    png_file = output_dir / f"drawio_export_{Path(drawio_file).stem}.png"
    
    # 5. 使用 Playwright 导出图像
    try:
        # URL 编码
        encoded_content = urllib.parse.quote(xml_content)
        drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=diagram#R{encoded_content}"
        
        logger.info("正在启动 Playwright 导出...")
        
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 设置视口大小
            page.set_viewport_size({"width": 1920, "height": 1080})
            
            # 访问页面
            page.goto(drawio_url, timeout=60000)
            
            # 等待页面加载
            page.wait_for_timeout(8000)
            
            # 尝试等待图表元素
            try:
                page.wait_for_selector(".geDiagramContainer", timeout=15000)
                logger.info("检测到图表容器")
            except:
                logger.warning("未检测到图表容器，继续截图")
            
            # 截图
            page.screenshot(path=str(png_file), full_page=True, type="png")
            browser.close()
        
        if not png_file.exists() or png_file.stat().st_size == 0:
            return {
                "success": False,
                "message": "Playwright 截图失败",
                "method": "playwright"
            }
        
        logger.success(f"Playwright 导出成功: {png_file}")
        logger.info(f"PNG 文件大小: {png_file.stat().st_size} 字节")
        
    except Exception as e:
        return {
            "success": False,
            "message": f"Playwright 导出失败: {e}",
            "method": "playwright"
        }
    
    # 6. 插入图像到 Excel - 使用 win32com 方式
    try:
        with ExcelUtil(excel_file, auto_create=True) as excel:
            # 确保工作表存在
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            current_row = 1
            
            # 添加标题
            excel.write_cell(sheet_name, current_row, 1, title)
            title_style = CellStyle(
                font_size=16,
                font_bold=True,
                alignment_horizontal="center"
            )
            excel.set_cell_style(sheet_name, current_row, 1, title_style)
            current_row += 2
            
            # 使用 win32com 插入图像
            success = insert_image_win32com(excel, sheet_name, str(png_file), current_row, 1)
            
            if success:
                excel.save()
                logger.success(f"图像已成功插入到 Excel: {excel_file}")
                
                return {
                    "success": True,
                    "message": "Draw.io 原始图像导出成功",
                    "method": "playwright + win32com",
                    "excel_file": excel_file,
                    "sheet_name": sheet_name,
                    "png_file": str(png_file),
                    "image_position": f"行{current_row}, 列1"
                }
            else:
                return {
                    "success": False,
                    "message": "图像插入 Excel 失败",
                    "method": "playwright + win32com",
                    "png_file": str(png_file)  # 至少 PNG 文件是成功的
                }
                
    except Exception as e:
        return {
            "success": False,
            "message": f"Excel 操作失败: {e}",
            "method": "playwright + win32com",
            "png_file": str(png_file)  # 至少 PNG 文件是成功的
        }


def insert_image_win32com(excel: ExcelUtil, sheet_name: str, image_path: str, row: int, col: int) -> bool:
    """
    使用 win32com 方式插入图像
    """
    try:
        # 方法1：使用现有的工具函数
        from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import insert_image_to_excel
        
        # 获取当前工作表
        if hasattr(excel, 'worksheet') and excel.worksheet:
            insert_image_to_excel(excel.worksheet, image_path, row, col)
            logger.success("使用现有工具函数插入图像成功")
            return True
        else:
            logger.warning("无法获取当前工作表，尝试切换工作表")
            
            # 尝试切换到指定工作表
            if hasattr(excel, 'workbook') and excel.workbook:
                try:
                    # 切换工作表
                    excel.workbook.Worksheets(sheet_name).Activate()
                    current_ws = excel.workbook.ActiveSheet
                    
                    insert_image_to_excel(current_ws, image_path, row, col)
                    logger.success("切换工作表后插入图像成功")
                    return True
                    
                except Exception as e:
                    logger.error(f"切换工作表失败: {e}")
        
        # 方法2：直接使用 win32com
        try:
            import win32com.client as win32
            
            if hasattr(excel, 'app') and excel.app:
                # 获取工作表
                ws = excel.app.Worksheets(sheet_name)
                
                # 计算插入位置
                cell = ws.Cells(row, col)
                left = cell.Left
                top = cell.Top
                
                # 插入图片
                shape = ws.Shapes.AddPicture(
                    Filename=image_path,
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=left,
                    Top=top,
                    Width=-1,  # 保持原始宽度
                    Height=-1  # 保持原始高度
                )
                
                # 调整大小
                if shape.Width > 1200:
                    scale_factor = 1200 / shape.Width
                    shape.Width = 1200
                    shape.Height = shape.Height * scale_factor
                
                logger.success("直接使用 win32com 插入图像成功")
                return True
                
        except Exception as e:
            logger.error(f"直接 win32com 方式失败: {e}")
        
        return False
        
    except Exception as e:
        logger.error(f"插入图像失败: {str(e)}")
        return False


def demo_fixed_export():
    """演示修复版本的导出功能"""
    print("🎯 修复版本：Draw.io 原始图像导出到 Excel")
    print("=" * 60)
    
    # 测试导出
    result = export_drawio_to_excel_fixed(
        excel_file="output/fixed_architecture_diagram.xlsx",
        title="系统架构图（修复版本）",
        max_width=1400,
        max_height=1000
    )
    
    print(f"📊 导出结果:")
    print(f"   方法: {result.get('method', 'unknown')}")
    print(f"   成功: {'✅' if result['success'] else '❌'}")
    print(f"   消息: {result['message']}")
    
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   PNG文件: {result['png_file']}")
        print(f"   图像位置: {result['image_position']}")
        print(f"\n🎉 成功！请打开 Excel 文件查看原始 Draw.io 图表！")
    else:
        print(f"\n❌ 导出失败: {result['message']}")
        if 'png_file' in result:
            print(f"💡 但是 PNG 文件已生成: {result['png_file']}")
            print(f"   你可以手动将此 PNG 插入到 Excel 中")


if __name__ == "__main__":
    demo_fixed_export()
