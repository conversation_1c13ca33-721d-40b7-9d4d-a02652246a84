"""步骤模块"""

# 导入DEV阶段步骤
from .dev_steps import (
    RequirementSpecReadingAgent,
    LegalComplianceAgent,
    SoftwareDesignStandardCSAgent,
    FunctionListTool,
    BasicDesignCSAgent,
    CommunicationFailSafeAgent,
    SoftwareDesignDocTool,
    InterfaceIntegrityAgent,
    CANIOListAgent,
    FunctionSpecTool,
    RAMDesignTool,
    DetailedDesignCSAgent,
    DesignPeerReviewAgent,
    FunctionListUpdateTool,
    CodingStandardAgent,
    FileComparisonAgent,
    SelfCheckTool,
    CodeReviewAgent,
    CodeCheckCSAgent
)

# 导入TEST阶段步骤
from .test_steps import (
    ChecklistIVAgent,
    TestCaseReviewAgent,
    ChecklistVAgent,
    CouplingCheckAgent,
    CommunicationTestAgent,
    RealDeviceEvaluationTool
)

# 所有步骤列表
ALL_STEPS = [
    # DEV阶段
    RequirementSpecReadingAgent,
    LegalComplianceAgent,
    SoftwareDesignStandardCSAgent,
    FunctionListTool,
    BasicDesignCSAgent,
    CommunicationFailSafeAgent,
    SoftwareDesignDocTool,
    InterfaceIntegrityAgent,
    CANIOListAgent,
    FunctionSpecTool,
    RAMDesignTool,
    DetailedDesignCSAgent,
    DesignPeerReviewAgent,
    FunctionListUpdateTool,
    CodingStandardAgent,
    FileComparisonAgent,
    SelfCheckTool,
    CodeReviewAgent,
    CodeCheckCSAgent,
    # TEST阶段
    ChecklistIVAgent,
    TestCaseReviewAgent,
    ChecklistVAgent,
    CouplingCheckAgent,
    CommunicationTestAgent,
    RealDeviceEvaluationTool
]

# 创建步骤实例的便捷函数
def create_all_steps():
    """创建所有步骤的实例"""
    return [step_class() for step_class in ALL_STEPS]

# 导出所有内容
__all__ = [
    # DEV步骤
    'RequirementSpecReadingAgent',
    'LegalComplianceAgent',
    'SoftwareDesignStandardCSAgent',
    'FunctionListTool',
    'BasicDesignCSAgent',
    'CommunicationFailSafeAgent',
    'SoftwareDesignDocTool',
    'InterfaceIntegrityAgent',
    'CANIOListAgent',
    'FunctionSpecTool',
    'RAMDesignTool',
    'DetailedDesignCSAgent',
    'DesignPeerReviewAgent',
    'FunctionListUpdateTool',
    'CodingStandardAgent',
    'FileComparisonAgent',
    'SelfCheckTool',
    'CodeReviewAgent',
    'CodeCheckCSAgent',
    # TEST步骤
    'ChecklistIVAgent',
    'TestCaseReviewAgent',
    'ChecklistVAgent',
    'CouplingCheckAgent',
    'CommunicationTestAgent',
    'RealDeviceEvaluationTool',
    # 工具函数
    'ALL_STEPS',
    'create_all_steps'
] 