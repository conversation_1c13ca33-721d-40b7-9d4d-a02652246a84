"""
Example rules for document parsing.
"""

from typing import Any, Dict

from docparser.interfaces.rule_interface import TextRule, TableRule, PictureRule, GraphicRule, DocumentRule

class HeaderTextRule(TextRule):
    """Rule to identify and mark header text."""
    
    def get_rule_id(self) -> str:
        return "header_text_rule"
    
    def get_rule_name(self) -> str:
        return "Header Text Rule"
    
    def get_rule_description(self) -> str:
        return "Identifies and marks header text based on font size and style."
    
    def get_rule_priority(self) -> int:
        return 100
    
    def get_rule_condition(self):
        def condition(text_obj: Dict[str, Any]) -> bool:
            # Check if font size is large or text is bold
            return (
                ('font_size' in text_obj and text_obj['font_size'] >= 14) or
                ('bold' in text_obj and text_obj['bold'] is True)
            )
        
        return condition
    
    def apply_rule(self, text_obj: Dict[str, Any]) -> Dict[str, Any]:
        result = text_obj.copy()
        result['is_header'] = True
        result['importance'] = 'high'
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass

class TableCaptionRule(TableRule):
    """Rule to add captions to tables."""
    
    def get_rule_id(self) -> str:
        return "table_caption_rule"
    
    def get_rule_name(self) -> str:
        return "Table Caption Rule"
    
    def get_rule_description(self) -> str:
        return "Adds captions to tables based on their position and content."
    
    def get_rule_priority(self) -> int:
        return 80
    
    def get_rule_condition(self):
        def condition(table_obj: Dict[str, Any]) -> bool:
            # Apply to all tables
            return True
        
        return condition
    
    def apply_rule(self, table_obj: Dict[str, Any]) -> Dict[str, Any]:
        result = table_obj.copy()
        
        # Generate a caption if not present
        if 'caption' not in result or not result['caption']:
            row_count = len(result.get('rows', []))
            col_count = len(result.get('columns', []))
            result['caption'] = f"Table with {row_count} rows and {col_count} columns"
        
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass

class ImageMetadataRule(PictureRule):
    """Rule to enhance picture metadata."""
    
    def get_rule_id(self) -> str:
        return "image_metadata_rule"
    
    def get_rule_name(self) -> str:
        return "Image Metadata Rule"
    
    def get_rule_description(self) -> str:
        return "Enhances picture metadata with additional information."
    
    def get_rule_priority(self) -> int:
        return 70
    
    def get_rule_condition(self):
        def condition(picture_obj: Dict[str, Any]) -> bool:
            # Apply to all pictures
            return True
        
        return condition
    
    def apply_rule(self, picture_obj: Dict[str, Any]) -> Dict[str, Any]:
        result = picture_obj.copy()
        
        # Add metadata if not present
        if 'metadata' not in result:
            result['metadata'] = {}
        
        # Add content type based on file extension
        if 'file_path' in result:
            file_path = result['file_path']
            if file_path.lower().endswith('.jpg') or file_path.lower().endswith('.jpeg'):
                result['metadata']['content_type'] = 'image/jpeg'
            elif file_path.lower().endswith('.png'):
                result['metadata']['content_type'] = 'image/png'
            elif file_path.lower().endswith('.gif'):
                result['metadata']['content_type'] = 'image/gif'
            elif file_path.lower().endswith('.bmp'):
                result['metadata']['content_type'] = 'image/bmp'
            else:
                result['metadata']['content_type'] = 'image/unknown'
        
        # Add image category based on size
        if 'width' in result and 'height' in result:
            width = result['width']
            height = result['height']
            area = width * height
            
            if area > 1000000:  # > 1 million pixels
                result['metadata']['size_category'] = 'large'
            elif area > 250000:  # > 250,000 pixels
                result['metadata']['size_category'] = 'medium'
            else:
                result['metadata']['size_category'] = 'small'
        
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass

class GraphicClassifierRule(GraphicRule):
    """Rule to classify graphics by type."""
    
    def get_rule_id(self) -> str:
        return "graphic_classifier_rule"
    
    def get_rule_name(self) -> str:
        return "Graphic Classifier Rule"
    
    def get_rule_description(self) -> str:
        return "Classifies graphics by type based on their properties."
    
    def get_rule_priority(self) -> int:
        return 60
    
    def get_rule_condition(self):
        def condition(graphic_obj: Dict[str, Any]) -> bool:
            # Apply to all graphics
            return True
        
        return condition
    
    def apply_rule(self, graphic_obj: Dict[str, Any]) -> Dict[str, Any]:
        result = graphic_obj.copy()
        
        # Determine graphic type based on properties
        if 'shape_type' in result:
            shape_type = result['shape_type'].lower() if isinstance(result['shape_type'], str) else ''
            
            if 'chart' in shape_type or 'graph' in shape_type:
                result['graphic_type'] = 'chart'
            elif 'arrow' in shape_type or 'line' in shape_type:
                result['graphic_type'] = 'connector'
            elif 'rectangle' in shape_type or 'square' in shape_type:
                result['graphic_type'] = 'box'
            elif 'circle' in shape_type or 'oval' in shape_type:
                result['graphic_type'] = 'circle'
            else:
                result['graphic_type'] = 'other'
        
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass

class DocumentMetadataRule(DocumentRule):
    """Rule to enhance document metadata."""
    
    def get_rule_id(self) -> str:
        return "document_metadata_rule"
    
    def get_rule_name(self) -> str:
        return "Document Metadata Rule"
    
    def get_rule_description(self) -> str:
        return "Enhances document metadata with additional information."
    
    def get_rule_priority(self) -> int:
        return 90
    
    def get_rule_condition(self):
        def condition(document_obj: Dict[str, Any]) -> bool:
            # Apply to all documents
            return True
        
        return condition
    
    def apply_rule(self, document_obj: Dict[str, Any]) -> Dict[str, Any]:
        result = document_obj.copy()
        
        # Ensure metadata exists
        if 'metadata' not in result:
            result['metadata'] = {}
        
        # Add document complexity metrics
        text_count = len(result.get('text_objects', []))
        table_count = len(result.get('table_objects', []))
        picture_count = len(result.get('picture_objects', []))
        graphic_count = len(result.get('graphic_objects', []))
        
        result['metadata']['complexity_score'] = (
            text_count * 1 +
            table_count * 3 +
            picture_count * 2 +
            graphic_count * 2
        )
        
        # Classify document based on content
        if table_count > (text_count + picture_count + graphic_count):
            result['metadata']['document_type'] = 'data-heavy'
        elif picture_count > (text_count + table_count + graphic_count):
            result['metadata']['document_type'] = 'visual'
        elif graphic_count > (text_count + table_count + picture_count):
            result['metadata']['document_type'] = 'presentation'
        else:
            result['metadata']['document_type'] = 'text-based'
        
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass
