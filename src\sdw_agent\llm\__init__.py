from langchain_openai import AzureOpenAIEmbeddings
from langchain_openai import AzureChatOpenAI
from langchain_openai import ChatOpenAI

from .model import *
from loguru import logger

embeddings = AzureOpenAIEmbeddings(
    azure_endpoint="https://sdw-openai.openai.azure.com",
    azure_deployment='text-embedding-3-large',
    api_key='8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1',
    api_version='2025-02-01-preview',
    model='text-embedding-3-large',
)

azure = AzureChatOpenAI(
    azure_endpoint="https://sdw-openai.openai.azure.com",
    azure_deployment='gpt-4o',
    api_key='8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1',
    api_version='2025-02-01-preview',
    model='gpt-4o',
    temperature=0
)

gpt4o = azure.with_config(tags=["inner"])
gpt4o_api = azure

dnkt_model = ChatOpenAI(
    api_key="aaa",
    # base_url="http://172.30.19.111:11434/v1/",
    base_url="http://172.30.19.113:11400/v1/",
    # model="qwen2.5:14b",
    model="qwen3",
    # model="QwQ-32B",
    # model="gemma3:27b-it-fp16",
    # model="deepseek-r1:32b-qwen-distill-fp16",
    # model="deepseek-r1:70b",
    temperature=0,
    extra_body={
        "chat_template_kwargs": {"enable_thinking": False}
    }
)

llama_model = ChatOpenAI(
    api_key="aaa",
    base_url="http://172.30.19.111:11434/v1/",
    # model="qwen2.5:14b",
    model="llama3.3",
    # model="QwQ-32B",
    # model="gemma3:27b-it-fp16",
    # model="deepseek-r1:32b-qwen-distill-fp16",
    # model="deepseek-r1:70b",
    temperature=0
)

content_model = dnkt_model.with_config(tags=["inner"])

deepseek = ChatOpenAI(
    api_key="***********************************",
    base_url="https://api.deepseek.com/beta",
    model="deepseek-chat",
    temperature=0,
    # model='deepseek-coder',
)
deepseek_inner = deepseek.with_config(tags=["inner"])

logger.info(f'model_endpoint:{dnkt_model.openai_api_base}')
# deepseekr1 = ChatOpenAI(
#     api_key="sk-pLktsd1i4Mbol5JFZNHhhZ7zFOj9rQjnmoB6GlzVCUotwK9Q",
#     base_url="http://127.0.0.1:3000/v1",
#     model="DeepSeekr1",
#     temperature=0,
# )
#
# azure = deepseekr1
# gpt4o = deepseekr1.with_config(tags=["inner"])
# gpt4o_api = deepseekr1
