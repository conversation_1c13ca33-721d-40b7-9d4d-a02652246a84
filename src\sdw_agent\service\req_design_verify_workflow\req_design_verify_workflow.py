"""
要件设计书照合实施工作流

基于BaseWorkflow实现的要件设计书照合实施工作流
"""

import os
import yaml
from typing import Dict, Any

from loguru import logger

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus
from sdw_agent.service import register_workflow
from sdw_agent.util.excel.req_design_verify_excel import ReqDesignVerifyExcelUtil
from .models import (
    ReqDesignVerifyInputModel,
    ReqDesignVerifyOutputModel, 
    ReqDesignVerifyConfigModel
)


@register_workflow("req_design_verify")
class ReqDesignVerifyWorkflow(BaseWorkflow):
    """要件设计书照合实施工作流"""
    
    def __init__(self):
        """初始化工作流"""
        super().__init__()
        self.logger = logger
        self.excel_util = None
        self.config = None
        
    def validate_input(self, input_data: ReqDesignVerifyInputModel) -> bool:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            self.logger.info("开始验证输入数据")
            
            # 验证Excel源文件
            if not input_data.excel_source:
                raise ValueError("Excel源文件不能为空")
            
            if not input_data.excel_source.uri or not input_data.excel_source.uri.strip():
                raise ValueError("Excel文件URI不能为空")
            
            # 对于local类型，检查文件是否存在
            if input_data.excel_source.type == "local":
                if not os.path.exists(input_data.excel_source.uri):
                    raise FileNotFoundError(f"Excel文件不存在: {input_data.excel_source.uri}")
                
                if not os.path.isfile(input_data.excel_source.uri):
                    raise ValueError(f"Excel路径不是有效文件: {input_data.excel_source.uri}")
                
                # 检查文件扩展名
                file_ext = os.path.splitext(input_data.excel_source.uri)[1].lower()
                if file_ext not in ['.xlsx', '.xls']:
                    raise ValueError(f"不支持的文件格式: {file_ext}，仅支持.xlsx和.xls文件")
            
            self.logger.info("输入数据验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"输入数据验证失败: {e}")
            raise
    
    def pre_execute(self, input_data: ReqDesignVerifyInputModel) -> None:
        """
        执行前准备
        
        Args:
            input_data: 输入数据
        """
        try:
            self.logger.info("开始执行前准备")
            
            # 加载配置
            self.config = self._load_config(input_data.custom_config)
            
            # 初始化Excel工具类
            self.excel_util = ReqDesignVerifyExcelUtil(input_data.excel_source.uri)
            
            self.logger.info("执行前准备完成")
            
        except Exception as e:
            self.logger.error(f"执行前准备失败: {e}")
            raise
    
    def execute(self, input_data: ReqDesignVerifyInputModel) -> WorkflowResult:
        """
        执行核心业务逻辑
        
        Args:
            input_data: 输入数据

        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.logger.info("开始执行要件设计书照合实施")

            # 直接调用原有service的逻辑，保持一致性
            from sdw_agent.service.req_design_verify_workflow.req_design_verify_service import process_requirement_design_verification

            # 调用原有service函数
            service_result = process_requirement_design_verification(input_data.excel_source)

            # 构建工作流结果
            output_model = ReqDesignVerifyOutputModel(
                status=service_result["status"],
                message=service_result["message"],
                output_file=service_result["output_file"]
            )

            workflow_result = WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message=service_result["message"],
                data=output_model.model_dump()  # 转换为字典
            )

            self.logger.info("要件设计书照合实施执行完成")
            return workflow_result

        except Exception as e:
            self.logger.error(f"要件设计书照合实施执行失败: {e}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"要件设计书照合实施执行失败: {str(e)}",
                error=str(e)
            )
    
    def post_execute(self, result: WorkflowResult) -> None:
        """
        执行后清理

        Args:
            result: 工作流执行结果
        """
        try:
            self.logger.info("开始执行后清理")

            # 清理临时文件（如果配置了需要清理）
            if self.config and self.config.get('cleanup_temp_files', True):
                # 这里可以添加临时文件清理逻辑
                pass

            # 重置工具类
            if self.excel_util:
                try:
                    # 轻量级实现的close方法不会有任何操作
                    self.excel_util.close()
                except:
                    pass
                self.excel_util = None

            self.config = None

            self.logger.info("执行后清理完成")

        except Exception as e:
            self.logger.warning(f"执行后清理失败: {e}")
    
    def register_config_model(self) -> type:
        """
        注册配置模型
        
        Returns:
            type: 配置模型类
        """
        return ReqDesignVerifyConfigModel
    
    def _load_config(self, custom_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        加载配置
        
        Args:
            custom_config: 自定义配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        try:
            # 加载默认配置文件
            config_path = os.path.join(os.path.dirname(__file__), "config.yaml")
            default_config = {}
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                    default_config = yaml_config.get('req_design_verify', {})
            
            # 合并自定义配置
            if custom_config:
                default_config.update(custom_config)
            
            self.logger.info("配置加载完成")
            return default_config
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            # 返回默认配置
            return {
                'target_sheet': '機能一覧と新規・変更内容',
                'header_start_row': 5,
                'header_end_row': 7,
                'data_start_row': 8,
                'start_col': 1,
                'end_col': 100,
                'exclude_hidden_rows': True,
                'req_content_column': '要件の内容',
                'design_book_column': 'パラメータ設計書',
                'consecutive_empty_limit': 5,
                'min_design_book_length': 3,
                'output_subdir': 'check_results'
            }
