
"""
通信故障安全CS工作流

V字对应：
2.1 基本設計 通信故障安全 CS

模块简介和主要功能说明：
通信故障安全CS工作流，用于分析代码变更中的CAN信号和控制信号匹配。
支持从BitAssign文件和SPI JSON文件中解析信号定义，并在代码变更中搜索匹配的信号。

主要功能：
1. 解析BitAssign文件中的CAN信号定义
2. 解析SPI JSON文件中的CAN信号和控制信号定义
3. 从Gerrit或本地仓库获取代码变更信息
4. 在变更代码中搜索匹配的信号
5. 生成匹配结果报告
"""

import os
import re
import time
import threading
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

import pandas as pd

from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.service.workflow_config import WorkflowConfigManager
from sdw_agent.util.file_base_util import create_text_file_link, get_output_dir_path, gen_output_path
from sdw_agent.service.communication_fault_cs.utils import CommunicationFaultExcelUtil
from sdw_agent.util.gerrit_util import get_diff_code_from_gerrit
from sdw_agent.util.git_util import get_git_structured_diff
from sdw_agent.util.excel_util import check_file_exists
from sdw_agent.config.env import ENV
from sdw_agent.service.communication_fault_cs.utils import (
    get_column_index, extract_added_lines_from_gerrit,
    extract_added_lines_from_local
)

from sdw_agent.service.communication_fault_cs.models import (
    InputDataModel, OutputDataModel, ConfigModel, MatchedSignalModel,
    SourceType
)


@register_workflow("communication_fault_cs")
class CommunicationFaultCSWorkflow(BaseWorkflow):
    """
    通信故障安全CS工作流类

    提供CAN信号和控制信号匹配功能，分析代码变更中的信号使用情况。
    支持BitAssign和SPI JSON两种数据源。
    """

    def __init__(self, source_type:str, config_path: Optional[str] = None):
        """
        初始化通信故障安全CS工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)

        # 注册配置模型
        self.register_config_model()

        # 初始化工作流特定配置
        self._init_workflow_config()

        # 输入要件的类型
        self.source_type = source_type

    def register_config_model(self):
        """注册配置模型用于验证"""
        try:
            config_manager = WorkflowConfigManager(workflow_name="communication_fault_cs")
            config_manager.register_schema("workflow_config", ConfigModel)
            self.logger.info("配置模型注册成功")
        except Exception as e:
            self.logger.warning(f"配置模型注册失败: {e}")

    def _init_workflow_config(self):
        """初始化工作流特定配置"""
        workflow_config = self.config.get("workflow_config", {})

        # 处理配置
        self.max_workers = workflow_config.get("max_workers", 10)
        self.default_case_sensitive = workflow_config.get("default_case_sensitive", True)

        # 输出配置
        self.output_format = workflow_config.get("output_format", "xlsx")
        self.include_summary = workflow_config.get("include_summary", True)

        # 文件处理配置
        self.supported_extensions = workflow_config.get("supported_file_extensions", [".c", ".cpp", ".h", ".hpp"])

        # 信号匹配配置
        signal_matching = workflow_config.get("signal_matching", {})
        self.timeout_seconds = signal_matching.get("timeout_seconds", 300)
        self.min_line_length = signal_matching.get("min_line_length", 3)
        self.exclude_comments = signal_matching.get("exclude_comments", True)

        # 加载搜索模式配置
        self.pattern_configs = signal_matching.get("patterns", {})
        self.logger.info(f"加载了 {len(self.pattern_configs)} 个搜索模式配置")

        # 点击成果物中代码链接查看代码的工具
        self.code_render = workflow_config.get("code_render", "vscode")

        self.logger.info("工作流配置初始化完成")

    def validate_input(self, input_data: Union[Dict[str, Any], InputDataModel]) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据，可以是字典或InputDataModel实例

        Returns:
            bool: 验证是否通过
        """
        try:
            # 如果是字典，转换为模型进行验证
            if isinstance(input_data, dict):
                validated_data = InputDataModel(**input_data)
            elif isinstance(input_data, InputDataModel):
                validated_data = input_data
            else:
                self.logger.error("输入数据类型不正确")
                return False

            # 验证文件是否存在
            if validated_data.source_type == SourceType.BIT_ASSIGN:
                if not validated_data.bit_assign_path or not os.path.exists(validated_data.bit_assign_path):
                    self.logger.error(f"BitAssign文件不存在: {validated_data.bit_assign_path}")
                    return False
            elif validated_data.source_type == SourceType.SPI_JSON:
                if not validated_data.can_json_path or not os.path.exists(validated_data.can_json_path):
                    self.logger.error(f"CAN JSON文件不存在: {validated_data.can_json_path}")
                    return False
                if not validated_data.ctrl_json_path or not os.path.exists(validated_data.ctrl_json_path):
                    self.logger.error(f"控制信号JSON文件不存在: {validated_data.ctrl_json_path}")
                    return False

            # 验证commit_id格式
            if not validated_data.commit_id or len(validated_data.commit_id) < 7:
                self.logger.error("commit_id格式不正确")
                return False

            # 验证本地仓库路径（如果提供）
            if validated_data.repository_path and not os.path.exists(validated_data.repository_path):
                self.logger.error(f"本地仓库路径不存在: {validated_data.repository_path}")
                return False

            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入参数验证失败: {e}")
            return False

    def execute(self, input_data: Union[Dict[str, Any], InputDataModel]) -> WorkflowResult:
        """
        执行工作流核心逻辑

        Args:
            input_data: 输入数据

        Returns:
            WorkflowResult: 工作流执行结果
        """
        start_time = time.time()

        try:
            # 转换输入数据
            if isinstance(input_data, dict):
                validated_input = InputDataModel(**input_data)
            else:
                validated_input = input_data

            self.logger.info(f"开始执行通信故障安全CS工作流，数据源类型: {validated_input.source_type}")

            # 解析信号定义
            can_dict, ctrl_dict = self._parse_signal_definitions(validated_input)

            # 获取代码变更信息
            added_lines_info = self._get_code_changes(validated_input)

            if not added_lines_info:
                raise ValueError("从该commit id提交代码中没有找到任何代码变更")

            # 搜索匹配的信号
            output_data = self._search_signals(validated_input, can_dict, ctrl_dict, added_lines_info)

            # 计算处理时间
            processing_time = time.time() - start_time

            self.logger.info(f"工作流执行完成，耗时: {processing_time:.2f}秒")

            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="通信故障安全CS工作流执行成功",
                data=output_data.model_dump()
            )

        except Exception as e:
            self.logger.exception(f"工作流执行失败: {e}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"工作流执行失败: {str(e)}",
                error=str(e)
            )

    def _parse_signal_definitions(self, input_data: InputDataModel) -> tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        解析信号定义

        Args:
            input_data: 输入数据

        Returns:
            tuple: (CAN信号字典, 控制信号字典)
        """
        self.logger.info("开始解析信号定义")

        if input_data.source_type == SourceType.BIT_ASSIGN:
            # 解析BitAssign文件
            can_dict = self._parse_bit_assign(input_data.bit_assign_path)
            ctrl_dict = None
        elif input_data.source_type == SourceType.SPI_JSON:
            # 解析SPI JSON文件
            can_dict, ctrl_dict = self._parse_spi_json(input_data.can_json_path, input_data.ctrl_json_path)

        else:
            raise ValueError(f"不支持的数据源类型: {input_data.source_type}")

        return can_dict, ctrl_dict

    def _get_code_changes(self, input_data: InputDataModel) -> List[Dict[str, Any]]:
        """
        获取代码变更信息

        Args:
            input_data: 输入数据

        Returns:
            List[Dict[str, Any]]: 代码变更信息列表
        """
        self.logger.info("开始获取代码变更信息")

        if input_data.repository_path:
            # 从本地仓库获取
            diffs = get_git_structured_diff(
                input_data.repository_path,
                input_data.commit_id,
                input_data.compared_commit_id
            )
            added_lines_info = extract_added_lines_from_local(input_data.repository_path, diffs)
        else:
            # 从Gerrit获取
            gerrit_url = ENV.config.gerrit.host
            username = ENV.config.gerrit.username
            password = ENV.config.gerrit.password

            diffs = get_diff_code_from_gerrit(gerrit_url, input_data.commit_id, username, password)
            added_lines_info = extract_added_lines_from_gerrit(diffs)

        self.logger.info(f"获取到 {len(added_lines_info)} 个文件的变更信息")
        return added_lines_info

    def _search_signals(self, input_data: InputDataModel, can_dict: Dict[str, Any],
                       ctrl_dict: Optional[Dict[str, Any]], added_lines_info: List[Dict[str, Any]]) -> OutputDataModel:
        """
        搜索匹配的信号

        Args:
            input_data: workflow输入数据
            can_dict: CAN信号字典
            ctrl_dict: 控制信号字典
            added_lines_info: 代码变更信息

        Returns:
            OutputDataModel: 输出数据
        """
        self.logger.info("开始搜索匹配的信号")

        # 搜索CAN信号
        can_matches = self._search_can_signals(added_lines_info, can_dict, input_data)
        self.logger.info(f"搜索完成，从变更代码中匹配到 {len(can_matches)} 个CAN信号")

        # 搜索控制信号（如果有）
        ctrl_matches = None
        if ctrl_dict:
            ctrl_matches = self._search_can_signals(added_lines_info, ctrl_dict, input_data)
            self.logger.info(f"搜索完成，从变更代码中匹配到 {len(ctrl_matches)} 个CTRL信号")

        # 生成输出文件
        output_file_path = self._generate_output_file(input_data, can_matches, ctrl_matches)

        # 构建输出数据
        output_data = OutputDataModel(
            output_file_path=output_file_path,
            total_files_processed=len(added_lines_info),
            total_matches_found=len(can_matches) + (len(ctrl_matches) if ctrl_matches else 0),
            can_matches=can_matches,
            ctrl_matches=ctrl_matches
        )

        return output_data

    def _search_can_signals(self, added_lines_info: List[Dict[str, Any]],
                           signal_dict: Dict[str, Any], input_data: InputDataModel) -> List[MatchedSignalModel]:
        """
        搜索CAN信号匹配

        Args:
            added_lines_info: 代码变更信息
            signal_dict: 信号字典
            input_data: 输入数据

        Returns:
            List[MatchedSignalModel]: 匹配结果列表
        """
        result_lock = threading.Lock()
        matched_results = []
        max_workers = min(len(added_lines_info), self.max_workers)

        self.logger.info(f"开始多线程搜索，使用 {max_workers} 个线程处理 {len(added_lines_info)} 个文件")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(
                    self._search_signals_in_file,
                    file_info,
                    signal_dict,
                    input_data.source_type,
                    input_data.case_sensitive
                ): file_info
                for file_info in added_lines_info
            }

            completed_files = 0
            for future in as_completed(future_to_file):
                try:
                    file_results = future.result()
                    with result_lock:
                        matched_results.extend(file_results)
                    completed_files += 1
                    self.logger.debug(f"进度: {completed_files}/{len(added_lines_info)} 文件处理完成")
                except Exception as e:
                    traceback.print_exc()
                    file_info = future_to_file[future]
                    self.logger.error(f"处理文件 {file_info.get('file_name', 'unknown')} 时发生错误: {e}")

        # 排序结果
        matched_results.sort(key=lambda x: (x.file_name, x.line_number))
        return matched_results

    def _search_signals_in_file(self, file_info: Dict[str, Any], signal_dict: Dict[str, Any],
                               source_type: SourceType, case_sensitive: bool) -> List[MatchedSignalModel]:
        """
        在单个文件中搜索信号

        Args:
            file_info: 文件信息
            signal_dict: 信号字典
            source_type: 数据源类型
            case_sensitive: 是否区分大小写

        Returns:
            List[MatchedSignalModel]: 匹配结果列表
        """
        file_name = file_info.get("file_name", "")
        file_path = file_info.get("file_path", "")
        commit_id = file_info.get("commit_id", "")
        added_lines = file_info.get("added_lines", [])
        added_line_nums = file_info.get("added_line_num", [])

        flags = 0 if case_sensitive else re.IGNORECASE

        # 预处理信号模式
        signal_patterns = self._prepare_signal_patterns(signal_dict, flags)

        file_results = []
        for i, line_content in enumerate(added_lines):
            line_num = added_line_nums[i] if i < len(added_line_nums) else 0
            line_content_clean = line_content.strip()

            # 跳过过短的行或注释行
            if len(line_content_clean) < self.min_line_length:
                continue
            if self.exclude_comments and self._is_comment_line(line_content_clean):
                continue

            # 搜索匹配
            matches = self._find_matches_in_line(
                signal_patterns, file_path, file_name, line_num,
                line_content_clean, commit_id, source_type
            )
            file_results.extend(matches)

        return file_results

    def _prepare_signal_patterns(self, signal_dict: Dict[str, Any], flags: int) -> Dict[str, Dict[str, Any]]:
        """
        预处理信号的正则表达式模式

        Args:
            signal_dict: 信号字典
            flags: 正则表达式标志

        Returns:
            Dict[str, Dict[str, Any]]: 处理后的信号模式字典
        """
        for signal_name, signal_info in signal_dict.items():
            patterns = self._create_search_patterns(signal_name)
            signal_info['patterns'] = [re.compile(pattern, flags) for pattern in patterns]

        return signal_dict

    def _create_search_patterns(self, signal_name: str) -> List[str]:
        """
        为信号创建多种搜索模式，基于配置文件中的模式定义

        Args:
            signal_name: 信号名称

        Returns:
            List[str]: 搜索模式列表
        """
        signal_name_clean = signal_name.strip()
        if not signal_name_clean:
            return []

        signal_name_escaped = re.escape(signal_name_clean)
        patterns = []

        # 从配置文件中读取模式配置
        for pattern_name, pattern_config in self.pattern_configs.items():
            # 检查模式是否启用
            if not pattern_config.get("enabled", False):
                self.logger.debug(f"跳过未启用的模式: {pattern_name}")
                continue

            # 获取模式模板
            pattern_template = pattern_config.get("pattern", "")
            if not pattern_template:
                self.logger.warning(f"模式 {pattern_name} 没有定义pattern字段")
                continue

            # 替换信号名称占位符
            try:
                pattern = pattern_template.format(signal_name=signal_name_escaped)
                patterns.append(pattern)
                self.logger.debug(f"添加模式 {pattern_name}: {pattern}")
            except Exception as e:
                self.logger.error(f"处理模式 {pattern_name} 时出错: {e}")
                continue

        # 如果配置文件中没有启用的模式，使用默认模式
        if not patterns:
            self.logger.warning("没有启用的搜索模式，使用默认模式")
            patterns = [
                rf'\b{signal_name_escaped}\b',  # 精确匹配（单词边界）
                rf'{signal_name_escaped}\s*[=:]\s*',  # 匹配赋值语句
                rf'->{signal_name_escaped}\b',  # 匹配指针访问（C/C++）
                rf'\(U[0-9]\)\s*{signal_name_escaped}\b',  # 匹配带(U数字)前缀的信号
            ]

        self.logger.debug(f"为信号 {signal_name} 生成了 {len(patterns)} 个搜索模式")
        return patterns

    def _is_comment_line(self, line: str) -> bool:
        """
        判断是否为注释行

        Args:
            line: 代码行

        Returns:
            bool: 是否为注释行
        """
        line_stripped = line.strip()
        return (line_stripped.startswith('//') or
                line_stripped.startswith('/*') or
                line_stripped.startswith('*'))

    def _find_matches_in_line(self, signal_patterns: Dict[str, Dict[str, Any]],
                             file_path: str, file_name: str, line_num: int,
                             line_content: str, commit_id: str, source_type: SourceType) -> List[MatchedSignalModel]:
        """
        在单行代码中查找所有信号匹配

        Args:
            signal_patterns: 信号模式字典
            file_path: 文件路径
            file_name: 文件名
            line_num: 行号
            line_content: 行内容
            commit_id: 提交ID
            source_type: 数据源类型

        Returns:
            List[MatchedSignalModel]: 匹配结果列表
        """
        matches = []

        for signal_name, signal_info in signal_patterns.items():
            for pattern in signal_info['patterns']:
                if pattern.search(line_content):
                    # 根据数据源类型创建不同的匹配结果
                    if source_type == SourceType.SPI_JSON:
                        matches.extend(self._create_spi_matches(
                            signal_info, file_path, file_name, line_num, line_content, commit_id
                        ))
                    else:  # BitAssign
                        matches.extend(self._create_bit_assign_matches(
                            signal_info, file_path, file_name, line_num, line_content, commit_id
                        ))
                    break  # 一个信号只记录一次

        return matches

    def _create_spi_matches(self, signal_info: Dict[str, Any], file_path: str,
                           file_name: str, line_num: int, line_content: str, commit_id: str) -> List[MatchedSignalModel]:
        """
        创建SPI信号匹配结果

        Args:
            signal_info: 信号信息
            file_path: 文件路径
            file_name: 文件名
            line_num: 行号
            line_content: 行内容
            commit_id: 提交ID

        Returns:
            List[MatchedSignalModel]: 匹配结果列表
        """
        matches = []

        # 由于frame_id不相同但存在信号名称重复的情况
        for idx in range(len(signal_info['frame_id'])):
            match = MatchedSignalModel(
                commit_id=commit_id,
                file_path=file_path,
                file_name=file_name,
                line_number=line_num,
                line_content=line_content,
                frame_id=signal_info['frame_id'][idx],
                matched_signal=signal_info['label'],
                signal_name=signal_info['name'][idx],
                init_value=signal_info['init_value'][idx],
                max_value=signal_info['max'][idx],
                min_value=signal_info['min'][idx],
                notes=signal_info['notes'][idx],
                main_to_sub_com_id=signal_info['main_to_sub_com_id'][idx],
                sub_to_main_com_id=signal_info['sub_to_main_com_id'][idx],
                fld_len=signal_info['fld_len'][idx],
                fld_strt=signal_info['fld_strt'][idx],
                text_file_link=create_text_file_link(file_path, line_num, self.code_render)
            )
            matches.append(match)

        return matches

    def _create_bit_assign_matches(self, signal_info: Dict[str, Any], file_path: str,
                                  file_name: str, line_num: int, line_content: str, commit_id: str) -> List[MatchedSignalModel]:
        """
        创建BitAssign信号匹配结果

        Args:
            signal_info: 信号信息
            file_path: 文件路径
            file_name: 文件名
            line_num: 行号
            line_content: 行内容
            commit_id: 提交ID

        Returns:
            List[MatchedSignalModel]: 匹配结果列表
        """
        matches = []

        # 由于frame_id不相同但存在信号名称重复的情况
        for idx in range(len(signal_info['frame_id'])):
            match = MatchedSignalModel(
                commit_id=commit_id,
                file_path=file_path,
                file_name=file_name,
                line_number=line_num,
                line_content=line_content,
                frame_id=signal_info['frame_id'][idx],
                matched_signal=signal_info['label'],
                signal_name_en=signal_info['name_en'][idx],
                signal_name_ja=signal_info['name_ja'][idx],
                init_value=signal_info['init_value'][idx],
                text_file_link = create_text_file_link(file_path, line_num, self.code_render)
            )
            matches.append(match)

        return matches

    def _generate_output_file(self, input_data: InputDataModel,
                             can_matches: List[MatchedSignalModel],
                             ctrl_matches: Optional[List[MatchedSignalModel]]) -> Optional[str]:
        """
        生成输出文件

        Args:
            input_data: 输入数据
            can_matches: CAN信号匹配结果
            ctrl_matches: 控制信号匹配结果

        Returns:
            Optional[str]: 输出文件路径
        """
        if not can_matches and not ctrl_matches:
            self.logger.warning("没有找到匹配的信号，不生成输出文件")
            raise Exception("未从变更代码中找到匹配的信号")

        # 生成文件名
        output_dir = get_output_dir_path(ENV.config.output_data_path, 'communication_fault_cs')
        output_path = gen_output_path(output_dir, f"matched_signals_{input_data.commit_id}")

        try:
            # 准备要保存的数据
            dataframes = {}

            # 准备CAN信号匹配结果
            if can_matches:
                can_df = pd.DataFrame([match.model_dump() for match in can_matches])
                dataframes["Matched_CAN"] = can_df
                self.logger.info(f"CAN信号匹配结果: {len(can_matches)} 条记录")

            # 准备控制信号匹配结果
            if ctrl_matches:
                ctrl_df = pd.DataFrame([match.model_dump() for match in ctrl_matches])
                dataframes["Matched_CTRL"] = ctrl_df
                self.logger.info(f"CTRL信号匹配结果: {len(ctrl_matches)} 条记录")

            # 使用Excel工具类保存数据
            with CommunicationFaultExcelUtil(self.config.get("workflow_config", {}), output_path, auto_create=True) as excel_util:
                excel_util.save_multiple_dataframes(dataframes, self.source_type)
                excel_util.save()

            self.logger.info(f"输出文件生成成功: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"生成输出文件失败: {e}")
            return None

    def _generate_summary(self, output_data: OutputDataModel) -> str:
        """
        生成处理摘要

        Args:
            output_data: 输出数据

        Returns:
            str: 处理摘要
        """
        summary_parts = [
            f"处理文件数: {output_data.total_files_processed}",
            f"找到匹配总数: {output_data.total_matches_found}",
            f"CAN信号匹配: {len(output_data.can_matches)}",
        ]

        if output_data.ctrl_matches:
            summary_parts.append(f"控制信号匹配: {len(output_data.ctrl_matches)}")

        summary_parts.append(f"处理时间: {output_data.processing_time:.2f}秒")

        if output_data.output_file_path:
            summary_parts.append(f"输出文件: {output_data.output_file_path}")

        return "; ".join(summary_parts)

    # 解析方法
    def _parse_bit_assign(self, file_path: str) -> Dict[str, Any]:
        """
        解析BitAssign文件

        Args:
            file_path: BitAssign文件路径

        Returns:
            Dict[str, Any]: CAN信号字典
        """
        import xlrd

        self.logger.info(f"开始解析BitAssign文件: {file_path}")

        # 检查文件是否存在
        check_file_exists(file_path)

        # 打开Excel文件
        workbook = xlrd.open_workbook(file_path)
        sheet = workbook.sheet_by_name('Transmit')  # 默认使用Transmit sheet

        # 提取列名（表头行）
        headers = sheet.row_values(11)  # 第12行为表头

        # 获取列索引
        can_col_idx = get_column_index(headers, 'Data Label') + 1
        can_name_en_col_idx = get_column_index(headers, 'Data Name(E)') + 1
        can_name_ja_col_idx = get_column_index(headers, 'Data Name(J)') + 1
        can_init_value_col_idx = get_column_index(headers, 'Trans.\nInitial\nValue') + 1
        can_meter_col_idx = get_column_index(headers, 'Meter') + 1

        # 提取数据行
        data = [sheet.row_values(row) for row in range(13, sheet.nrows)]  # 从第14行开始
        df = pd.DataFrame(data, columns=headers)

        can_dict = {}
        cur_status_id = None

        # 解析数据
        for row in df.itertuples():
            if row[1].strip() != '':
                cur_status_id = row[1]
                continue

            if row[can_meter_col_idx].strip() not in ['R', 'R/', 'T']:
                continue

            can_label = row[can_col_idx]
            if row[can_meter_col_idx].strip() in ['R', 'R/']:
                union_can_label = 'DOWN_' + can_label
            else:
                union_can_label = 'UP_' + can_label


            if union_can_label not in can_dict:
                can_dict[union_can_label] = {
                    'frame_id': [cur_status_id],
                    'label': can_label,
                    'name_en': [row[can_name_en_col_idx]],
                    'name_ja': [row[can_name_ja_col_idx]],
                    'init_value': [row[can_init_value_col_idx]]
                }
            else:
                can_dict[union_can_label]['frame_id'].append(cur_status_id)
                can_dict[union_can_label]['name_en'].append(row[can_name_en_col_idx])
                can_dict[union_can_label]['name_ja'].append(row[can_name_ja_col_idx])
                can_dict[union_can_label]['init_value'].append(row[can_init_value_col_idx])

        self.logger.info(f"BitAssign文件解析完成，共解析到 {len(can_dict)} 个CAN信号")
        return can_dict

    def _parse_spi_json(self, can_path: str, ctrl_path: str) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """
        解析SPI JSON文件

        Args:
            can_path: CAN信号JSON文件路径
            ctrl_path: 控制信号JSON文件路径

        Returns:
            tuple: (CAN信号字典, 控制信号字典)
        """
        import json

        self.logger.info(f"开始解析SPI JSON文件: CAN={can_path}, CTRL={ctrl_path}")

        # 检查文件是否存在
        check_file_exists(can_path)
        check_file_exists(ctrl_path)

        # 读取CAN JSON文件
        with open(can_path, 'r', encoding='utf-8') as f:
            can_json = json.load(f)
        can_dict = self._process_spi_json(can_json, True)

        # 读取控制信号JSON文件
        with open(ctrl_path, 'r', encoding='utf-8') as f:
            ctrl_json = json.load(f)
        ctrl_dict = self._process_spi_json(ctrl_json, False)

        self.logger.info(f"SPI JSON文件解析完成，CAN信号: {len(can_dict)}, 控制信号: {len(ctrl_dict)}")
        return can_dict, ctrl_dict

    def _process_spi_json(self, data: Dict[str, Any], is_can: bool = False) -> Dict[str, Any]:
        """
        处理SPI JSON数据

        Args:
            data: JSON数据
            is_can: 是否是CAN信号

        Returns:
            Dict[str, Any]: 处理后的信号字典
        """
        signal_dict = {}

        for key, signals in data['Message'].items():
            frame_id = key
            for idx, signal in enumerate(signals):
                if is_can and idx == 0:
                    # 如果是CAN信号，第一个元素是frame_id，跳过
                    continue

                can_label = signal['DatLbl']

                # 处理MainToSub方向
                if signal['MainToSub']:
                    union_can_label = 'DOWN_' + can_label
                    self._add_signal_to_dict(signal_dict, union_can_label, frame_id, signal)

                # 处理SubToMain方向
                if signal['SubToMain']:
                    union_can_label = 'UP_' + can_label
                    self._add_signal_to_dict(signal_dict, union_can_label, frame_id, signal)

        return signal_dict

    def _add_signal_to_dict(self, signal_dict: Dict[str, Any], union_label: str, frame_id: str, signal: Dict[str, Any]):
        """
        将信号添加到信号字典中

        Args:
            signal_dict: 信号字典
            union_label: 联合标签
            frame_id: 帧ID
            signal: 信号数据
        """
        if union_label not in signal_dict:
            signal_dict[union_label] = {
                'frame_id': [frame_id],
                'label': signal['DatLbl'],
                'name': [signal['DatName']],
                'init_value': [signal['Detail']['Init']],
                'max': [signal['Detail']['Max']],
                'min': [signal['Detail']['Min']],
                'notes': [signal['Detail']['Notes']],
                'main_to_sub_com_id': [signal['MainToSub']['ComID'] if signal['MainToSub'] else ""],
                'sub_to_main_com_id': [signal['SubToMain']['ComID'] if signal['SubToMain'] else ""],
                'fld_len': [signal['FldLen']],
                'fld_strt': [signal['FldStrt']]
            }
        else:
            signal_dict[union_label]['frame_id'].append(frame_id)
            signal_dict[union_label]['name'].append(signal['DatName'])
            signal_dict[union_label]['init_value'].append(signal['Detail']['Init'])
            signal_dict[union_label]['max'].append(signal['Detail']['Max'])
            signal_dict[union_label]['min'].append(signal['Detail']['Min'])
            signal_dict[union_label]['notes'].append(signal['Detail']['Notes'])
            signal_dict[union_label]['main_to_sub_com_id'].append(
                signal['MainToSub']['ComID'] if signal['MainToSub'] else ""
            )
            signal_dict[union_label]['sub_to_main_com_id'].append(
                signal['SubToMain']['ComID'] if signal['SubToMain'] else ""
            )
            signal_dict[union_label]['fld_len'].append(signal['FldLen'])
            signal_dict[union_label]['fld_strt'].append(signal['FldStrt'])



