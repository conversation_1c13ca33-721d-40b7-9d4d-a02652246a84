import openpyxl
import os
import sys

sys.path.append('src')
from sdw_agent.util.update_excel_cells_util import (
    update_excel_cells
)


def test_data_passing():
    """测试数据传递 - 测试Excel文件能否打开并打印sheet名"""
    file_path = 'C:\\tdd_input\\ソフトウェア 設計基準CS.xlsm'

    # 首先检查文件是否存在
    assert os.path.exists(file_path), f"文件不存在: {file_path}"
    print(f"文件存在: {file_path}")

    try:
        # 使用openpyxl打开Excel文件
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        print(f"成功打开Excel文件: {file_path}")

        # 获取所有sheet名称
        sheet_names = workbook.sheetnames
        print(f"Sheet总数: {len(sheet_names)}")
        print("所有Sheet名称:")
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"  {i}. {sheet_name}")

        # 关闭工作簿
        workbook.close()
        print("文件已成功关闭")

    except Exception as e:
        print(f"打开Excel文件时出错: {e}")
        raise


def test_excel_update_single():
    """测试单个单元格更新"""
    file_path = 'C:\\tdd_input\\ソフトウェア 設計基準CS.xlsm'
    sheet_name = "設計基準CS"  # 假设这是一个存在的sheet名

    # 测试数据：更新一个单元格
    test_updates = {
        10: {"S": "测试单个更新"}  # 第10行S列
    }

    try:
        print("开始测试单个单元格更新...")
        update_excel_cells(file_path, sheet_name, test_updates)
        print("单个单元格更新测试完成")
    except Exception as e:
        print(f"单个单元格更新测试失败: {e}")
        raise


def test_vba_friendly_update():
    """测试VBA友好的更新功能"""
    file_path = 'C:\\tdd_input\\ソフトウェア 設計基準CS.xlsm'
    sheet_name = "設計基準CS-要件分析"

    # 测试数据：只更新可见行
    test_updates = {
        "20": {
            "s": "需求变更内容与电源管理状态迁移无直接关联，无需调整相关序列设计。"
        },
        "21": {
            "s": "需求变更未涉及外部IC在睡眠状态下的行为，因此无需检查相关路径。"
        },
        "23": {
            "s": "需求变更未涉及异常处理逻辑，因此无需调整监控条件和方法。"
        },
        "25": {
            "s": "需求变更未涉及硬件时钟或温度特性，因此无需重新评估公差设计。"
        },
        "26": {
            "s": "需求变更未涉及微控制器或时钟频率调整，因此无需重新评估处理负载。"
        },
        "28": {
            "s": "需求变更仅涉及电源插座层级调整，未涉及ROM/RAM容量变化或MCU变更，因此无需实施ROM/RAM容量检查。"
        },
        "29": {
            "s": "变更内容不涉及信号定义或功能有无变化，因此无需实施系统整合性确认。"
        },
        "30": {
            "s": "变更内容不涉及与其他ECU的通信或等待处理逻辑，因此无需实施等待处理的FS设计检查。"
        },
        "31": {
            "s": "变更内容不涉及硬件等待处理逻辑，因此无需与回路课进行审議。"
        },
        "32": {
            "s": "变更内容不涉及MCU变更或Flash使用方式，因此无需实施ROM异常监控检查。"
        },
        "33": {
            "s": "需求变更仅涉及层级移动和新增插座功能，未涉及控制逻辑或重试机制的修改，因此无需实施。"
        },
        "34": {
            "s": "需求变更未涉及处理流程的开始或结束逻辑，因此无需实施。"
        },
        "35": {
            "s": "需求变更未涉及软件复用或版本更新，因此无需实施。"
        },
        "38": {
            "s": ""
        }
    }

    try:
        print("开始测试VBA友好的更新...")
        update_excel_cells(file_path, sheet_name, test_updates)
        print("VBA友好更新测试完成")
    except Exception as e:
        print(f"VBA友好更新测试失败: {e}")
        raise
