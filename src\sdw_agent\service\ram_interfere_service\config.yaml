# RAM干涉工作流配置

# 基本配置
name: "RAM干涉检查"
description: "分析代码中的全局变量变更和RAM干涉情况"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入配置
  input:
    repo_extensions: [".c", ".h", ".prm"]  # 支持的文件扩展名
    
  # 输出文件配置
  output:
    default_output_dir: "C:\\sdw_output\\ram_interfere" # 默认输出目录
    report_base_name: "RAM干涉"      # 报告文件名基础
    template_file: "templates/RAM干涉_模板.xlsx"  # 报告模板文件

# 处理参数
processing:
  # 干涉检查配置
  interference_check:
    scheduler_task_name: "st_gp_SCHDLR_RGLR_TASK"
    
  # Excel输出配置
  excel_output:
    header_rows: 2  # 表头行数
    default_sheet: "干渉CS（実施日DD.MM.YY）"  # 报告模板文件默认工作表名
    data_start_row: 21  # 数据起始行
    data_start_col: 2  # 数据起始列
    column_widths:  # 列宽配置
      1: 20  # 全局变量名列宽
      2: 15  # 中断列宽
      3: 15  # 中断地址列宽
      4: 25  # 文件路径列宽
      5: 25  # 变更函数列宽
      6: 10  # 行号列宽
      7: 40  # 代码内容列宽
      8: 20  # 备注列宽
      9: 20  # 结果列宽

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {message}" 