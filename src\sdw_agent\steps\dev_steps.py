"""DEV阶段步骤实现"""
from typing import Dict, Any, List, Optional
from ..core.base import AgentStep, ToolStep, StepInput
from ..core.llm import LLMManager, LLMMessage
from ..core.prompt import PromptManager

# 一个功能写一个py
# commit的时候是否有冲突
# 抽取通用的方法，集成来基础类
# Rag让dnkt的业务专家测试，做个简单的前端界面。
class RequirementSpecReadingAgent(AgentStep):
    """1. 要求规格读取 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_1",
            name="要求规格读取",
            description="基于已有的SCL和要件一览表，分析该变更对其他方面的影响"
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        """验证输入数据"""
        required = ["scl_file", "requirements_list"]
        return all(key in input_data.data.get("initial_data", {}) for key in required)
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """AI分析逻辑"""
        # 模拟AI分析
        return {
            "requirement_spec": {
                "change_impacts": ["模块A", "模块B"],
                "related_specs": ["规格1", "规格2"],
                "risk_level": "medium"
            }
        }


class LegalComplianceAgent(AgentStep):
    """2. 法规确认 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_2",
            name="法规确认",
            description="判定输出法规变更的关系，有无和标号",
            dependencies=["dev_1"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "compliance_check": {
                "is_compliant": True,
                "regulations": ["ISO26262", "GB/T34590"],
                "notes": "符合相关法规要求"
            }
        }


class SoftwareDesignStandardCSAgent(AgentStep):
    """3. 软件设计标准CS（需求分析）(Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_3",
            name="软件设计标准CS（需求分析）",
            description="基于变更的需求，匹配设计库或CS分析表中的内容",
            dependencies=["dev_1", "dev_2"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "design_standard_cs": {
                "matched_patterns": ["模式1", "模式2"],
                "design_principles": ["原则A", "原则B"],
                "checklist_items": ["检查项1", "检查项2"]
            }
        }


class FunctionListTool(ToolStep):
    """4. 功能一览与变更内容制作 (Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_4",
            name="功能一览与变更内容制作",
            description="根据多源数据填充到固定格式的表格中",
            dependencies=["dev_1", "dev_2", "dev_3"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具生成功能一览表"""
        return {
            "function_list": {
                "functions": [
                    {"id": "F001", "name": "功能1", "change_type": "新增"},
                    {"id": "F002", "name": "功能2", "change_type": "修改"}
                ],
                "change_summary": "新增1项，修改1项"
            }
        }


class BasicDesignCSAgent(AgentStep):
    """5. 软件设计标准CS（基本设计）(Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_5",
            name="软件设计标准CS（基本设计）",
            description="根据需求变更对应的代码，生成设计方针的实施",
            dependencies=["dev_3", "dev_4"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "basic_design": {
                "design_policy": "采用模块化设计",
                "architecture": "分层架构",
                "modules": ["模块A", "模块B", "模块C"]
            }
        }


class CommunicationFailSafeAgent(AgentStep):
    """6. 通信故障安全CS (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_6",
            name="通信故障安全CS",
            description="从变更代码中提取包含Can信号的代码行与片段信息",
            dependencies=["dev_5"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "can_signals": {
                "extracted_signals": ["Signal1", "Signal2"],
                "fail_safe_measures": ["措施1", "措施2"],
                "risk_assessment": "低风险"
            }
        }


class SoftwareDesignDocTool(ToolStep):
    """7. 软件设计书制作（含I/F）(Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_7",
            name="软件设计书制作",
            description="制作软件设计书",
            dependencies=["dev_5", "dev_6"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "design_document": {
                "document_path": "/output/design_doc.pdf",
                "warning_code": "生成完成",
                "checklist_result": "通过"
            }
        }


class InterfaceIntegrityAgent(AgentStep):
    """8. I/F整合性确认 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_8",
            name="I/F整合性确认",
            description="根据I/F中参数的类型和取值范围判断调用的源码是否正确",
            dependencies=["dev_7"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "interface_check": {
                "total_interfaces": 10,
                "ok_count": 8,
                "ng_count": 1,
                "pending_count": 1,
                "details": []
            }
        }


class CANIOListAgent(AgentStep):
    """9. CAN入出力一览确认 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_9",
            name="CAN入出力一览确认",
            description="确认代码中符合CAN输入输出",
            dependencies=["dev_6"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "can_io_check": {
                "input_signals": ["IN1", "IN2"],
                "output_signals": ["OUT1", "OUT2"],
                "validation_result": "通过"
            }
        }


class FunctionSpecTool(ToolStep):
    """10. 函数式样书制作 (Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_10",
            name="函数式样书制作",
            description="完成函数流程的式样书",
            dependencies=["dev_5"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "function_spec": {
                "document_path": "/output/function_spec.pdf",
                "functions_documented": 15,
                "status": "完成"
            }
        }


class RAMDesignTool(ToolStep):
    """11. RAM设计书制作 (Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_11",
            name="RAM设计书制作",
            description="根据基本设计，填写变量的信息",
            dependencies=["dev_5"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "ram_design": {
                "document_path": "/output/ram_design.pdf",
                "variables_documented": 50,
                "memory_usage": "2KB"
            }
        }


class DetailedDesignCSAgent(AgentStep):
    """12. 软件设计标准CS（详细设计）(Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_12",
            name="软件设计标准CS（详细设计）",
            description="根据需求变更对应的代码，生成设计方针的实施",
            dependencies=["dev_5", "dev_10", "dev_11"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "detailed_design": {
                "algorithms": ["算法1", "算法2"],
                "data_structures": ["数据结构1", "数据结构2"],
                "implementation_notes": "详细实现说明"
            }
        }


class DesignPeerReviewAgent(AgentStep):
    """13. 设计同行评审 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_13",
            name="设计同行评审",
            description="检查成果物的表格中有没有空缺项",
            dependencies=["dev_12"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "review_result": {
                "status": "OK",
                "missing_items": [],
                "review_comments": ["建议1", "建议2"]
            }
        }


class FunctionListUpdateTool(ToolStep):
    """14. 功能一览与变更内容更新 (Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_14",
            name="功能一览与变更内容更新",
            description="基于规则更新功能一览表",
            dependencies=["dev_13"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "updated_function_list": {
                "update_count": 3,
                "new_version": "v2.0",
                "status": "更新完成"
            }
        }


class CodingStandardAgent(AgentStep):
    """15. 编码标准确认&结果验证 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_15",
            name="编码标准确认&结果验证",
            description="对代码进行规则检查",
            dependencies=["dev_12"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "coding_standard_check": {
                "total_rules": 100,
                "passed": 95,
                "failed": 5,
                "violations": []
            }
        }


class FileComparisonAgent(AgentStep):
    """16. 文件比较实施 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_16",
            name="文件比较实施",
            description="读取代码差分结果，对每一行进行确认",
            dependencies=["dev_15"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "file_comparison": {
                "files_compared": 10,
                "differences_found": 25,
                "critical_changes": 2
            }
        }


class SelfCheckTool(ToolStep):
    """17. 自我检查与代码审查视角CS (Tool)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_17",
            name="自我检查与代码审查视角CS",
            description="Gerrit工具自动检查",
            dependencies=["dev_16"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "gerrit_check": {
                "score": "+2",
                "comments": [],
                "status": "通过"
            }
        }


class CodeReviewAgent(AgentStep):
    """18. 代码审查 (Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_18",
            name="代码审查",
            description="AI对检查结果进行判定是否可以review",
            dependencies=["dev_15", "dev_17"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "code_review": {
                "review_decision": "可以评审",
                "quality_score": 85,
                "suggestions": []
            }
        }


class CodeCheckCSAgent(AgentStep):
    """19. 软件设计标准CS（代码检查）(Agent)"""
    
    def __init__(self):
        super().__init__(
            step_id="dev_19",
            name="软件设计标准CS（代码检查）",
            description="根据代码检查规则，每个文件每一条规则跑一条AI结果",
            dependencies=["dev_18"]
        )
        
    def validate_input(self, input_data: StepInput) -> bool:
        return True
        
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "code_check_cs": {
                "files_checked": 20,
                "total_issues": 5,
                "critical_issues": 0,
                "final_status": "通过"
            }
        }


# 新增基于LLM的Agent步骤实现
class RequirementAnalysisLLMAgent(AgentStep):
    """需求分析步骤（基于LLM）"""
    
    def __init__(self, 
                 step_id: str = "dev_req_analysis",
                 name: str = "需求分析",
                 description: str = "分析用户需求，提取关键功能点和约束条件",
                 dependencies: List[str] = None,
                 llm_manager: Optional[LLMManager] = None,
                 prompt_manager: Optional[PromptManager] = None,
                 llm_client_name: Optional[str] = None):
        """初始化需求分析步骤"""
        super().__init__(
            step_id=step_id,
            name=name,
            description=description,
            dependencies=dependencies,
            agent_type="requirement",
            llm_manager=llm_manager,
            prompt_manager=prompt_manager,
            llm_client_name=llm_client_name
        )
    
    def validate_input(self, input_data: StepInput) -> bool:
        """验证输入数据"""
        # 检查是否包含需求描述
        if "requirement_text" not in input_data.data:
            self.logger.error("缺少需求描述文本")
            return False
        return True
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行需求分析"""
        # 构建消息
        messages = self.build_messages(data, "requirement_analysis")
        
        # 调用LLM
        response = self.call_llm(messages)
        
        # 解析结果
        result = self.parse_llm_response(response)
        
        # 添加原始需求文本到结果中
        result["original_requirement"] = data.get("requirement_text", "")
        
        return result
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        # 这里可以实现更复杂的解析逻辑，例如提取特定格式的内容
        # 简单示例：假设LLM返回的是JSON格式的字符串
        try:
            import json
            # 尝试解析为JSON
            parsed = json.loads(response)
            return parsed
        except json.JSONDecodeError:
            # 如果不是JSON格式，返回原始响应
            return {
                "functional_requirements": [],
                "non_functional_requirements": [],
                "constraints": [],
                "analysis_result": response
            }


class DesignGenerationLLMAgent(AgentStep):
    """设计生成步骤（基于LLM）"""
    
    def __init__(self, 
                 step_id: str = "dev_design_gen",
                 name: str = "设计生成",
                 description: str = "根据需求生成软件设计方案",
                 dependencies: List[str] = None,
                 llm_manager: Optional[LLMManager] = None,
                 prompt_manager: Optional[PromptManager] = None,
                 llm_client_name: Optional[str] = None):
        """初始化设计生成步骤"""
        super().__init__(
            step_id=step_id,
            name=name,
            description=description,
            dependencies=dependencies,
            agent_type="design",
            llm_manager=llm_manager,
            prompt_manager=prompt_manager,
            llm_client_name=llm_client_name
        )
    
    def validate_input(self, input_data: StepInput) -> bool:
        """验证输入数据"""
        # 检查是否包含需求分析结果
        if "functional_requirements" not in input_data.data:
            self.logger.error("缺少功能需求列表")
            return False
        return True
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行设计生成"""
        # 构建消息
        messages = self.build_messages(data, "design_generation")
        
        # 调用LLM
        response = self.call_llm(messages)
        
        # 解析结果
        result = self.parse_llm_response(response)
        
        return result
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        # 设计文档可能包含多个部分，这里进行简单解析
        sections = {}
        
        # 尝试提取架构设计部分
        import re
        
        # 提取架构设计
        arch_match = re.search(r'# 架构设计(.*?)(?=# |$)', response, re.DOTALL)
        if arch_match:
            sections["architecture"] = arch_match.group(1).strip()
            
        # 提取组件设计
        comp_match = re.search(r'# 组件设计(.*?)(?=# |$)', response, re.DOTALL)
        if comp_match:
            sections["components"] = comp_match.group(1).strip()
            
        # 提取接口设计
        intf_match = re.search(r'# 接口设计(.*?)(?=# |$)', response, re.DOTALL)
        if intf_match:
            sections["interfaces"] = intf_match.group(1).strip()
            
        # 如果没有提取到任何部分，返回完整响应
        if not sections:
            sections["complete_design"] = response
            
        return sections


class CodeReviewLLMAgent(AgentStep):
    """代码审查步骤（基于LLM）"""
    
    def __init__(self, 
                 step_id: str = "dev_code_review",
                 name: str = "代码审查",
                 description: str = "审查代码质量和符合性",
                 dependencies: List[str] = None,
                 llm_manager: Optional[LLMManager] = None,
                 prompt_manager: Optional[PromptManager] = None,
                 llm_client_name: Optional[str] = None):
        """初始化代码审查步骤"""
        super().__init__(
            step_id=step_id,
            name=name,
            description=description,
            dependencies=dependencies,
            agent_type="code_review",
            llm_manager=llm_manager,
            prompt_manager=prompt_manager,
            llm_client_name=llm_client_name
        )
    
    def validate_input(self, input_data: StepInput) -> bool:
        """验证输入数据"""
        # 检查是否包含代码内容
        if "code_content" not in input_data.data:
            self.logger.error("缺少代码内容")
            return False
        return True
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行代码审查"""
        # 构建消息
        messages = self.build_messages(data, "code_review")
        
        # 调用LLM
        response = self.call_llm(messages)
        
        # 解析结果
        result = self.parse_llm_response(response)
        
        return result
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        # 解析代码审查结果
        import re
        
        # 提取问题列表
        issues = []
        issues_pattern = r'- \*\*问题\*\*: (.*?)(?=- \*\*问题\*\*:|$)'
        for match in re.finditer(issues_pattern, response, re.DOTALL):
            issue_text = match.group(1).strip()
            if issue_text:
                issues.append(issue_text)
                
        # 提取总体评价
        overall_match = re.search(r'# 总体评价(.*?)(?=# |$)', response, re.DOTALL)
        overall = overall_match.group(1).strip() if overall_match else "未提供总体评价"
        
        # 提取建议
        suggestions_match = re.search(r'# 改进建议(.*?)(?=# |$)', response, re.DOTALL)
        suggestions = suggestions_match.group(1).strip() if suggestions_match else "未提供改进建议"
        
        return {
            "issues": issues,
            "overall_assessment": overall,
            "improvement_suggestions": suggestions,
            "raw_review": response
        } 