"""
@File    : search_global_var.py
@Time    : 2025/7/15 19:13
<AUTHOR> qiliang.zhou
@Contact : <EMAIL>
@Desc    : 1. 从变更代码中解析出变更的全局变量
           2. 解析出全局变量的类型定义
           3. 解析出全局变量的注释
           4. 解析出常量的初始值
           5. 将解析出的全局变量信息按模板写入成果物
"""
import os
import pathlib
from datetime import datetime

from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.service.ram_design_global_var.enhance_global_var_info import enhance_global_var_info
from sdw_agent.service.ram_design_global_var.save_to_temp import save_to_excel
from sdw_agent.util.file_base_util import get_output_dir_path, gen_output_path
from sdw_agent.util.git_util import  get_git_structured_diff
from sdw_agent.service.ram_design_global_var.search_resolve_global_var import search_global_var_from_diff, \
    find_const_def, replace_const_with_value, search_global_var_from_full_code


class RAMGlobalVar:
    domain: str = "" # domain
    component: str = "" # component
    data_type: str = "" # type
    name: str = "" # name
    struct_or_union: str = "-" # struct_or_union
    member_data_type: list = [] # attr_type
    member_bit_len: list = [] # attr_bit_length
    member_name: list = [] # attr_name
    var_desc: str = "" # comment
    b_backup: str = "非保持"
    min: list = [] # min
    max: list = [] # max
    lsb: str = "-"
    unit: str = "-"

@logger.catch(reraise=True)
def search_global_var(repo_path, cmp_list_file_path, commit_id, compared_commit_id):
    # 根据commit id 获取代码变更内容以及所在文件全部的代码
    code_diff = get_diff_code(repo_path, commit_id, compared_commit_id)
    # 从变更代码中检索匹配出全局变量
    global_vars_info = {}
    for code_info in code_diff:
        global_vars = search_global_var_from_full_code(code_info.get('full_code',[]))
        global_vars_info[code_info.get('file_path')] = global_vars

    # 得到全局变量定义时使用的宏定义信息
    const_defs = find_const_def(repo_path, global_vars_info)
    # 将全局变量中使用的宏定义信息替换成宏值
    replace_const_with_value(global_vars_info, const_defs)
    # 从变更代码中匹配出变更的全局变量
    changed_vars = search_global_var_from_diff(global_vars_info, code_diff)
    result = enhance_global_var_info(repo_path, cmp_list_file_path, changed_vars, code_diff)
    # 将全局变量结果写入模板文件
    output_path = save_as_output(result)

    return output_path

def save_as_output(result):
    ram_vars = transform_res_to_obj(result)

    # 导出结果到Excel文件.xlsx
    output_dir = get_output_dir_path(ENV.config.output_data_path, 'ram_global_var_analysis')
    output_path = gen_output_path(output_dir, "ram_global_var", file_ext='.xls')
    # 将全局变量结果写入模板文件
    save_to_excel(ram_vars, output_path)
    logger.info(f"RAM 全局变量分析结果已保存到 {output_path}")

    return output_path

def transform_res_to_obj(result):
    # Mapping from RAMGlobalVar attribute to dict key
    attr_map = {
        'domain': 'domain',
        'component': 'component',
        'data_type': 'type',
        'name': 'name',
        'struct_or_union': 'struct_or_union',
        'member_data_type': 'attr_type',
        'member_bit_len': 'attr_bit_length',
        'member_name': 'attr_name',
        'var_desc': 'comment',
        'min': 'min',
        'max': 'max',
        'lsb': 'lsb',
        'unit': 'unit',
        # b_backup is always "非保持"
    }

    ram_vars = []
    for file_vars in result.values():
        for var_dict in file_vars:
            obj = RAMGlobalVar()
            for attr, key in attr_map.items():
                if key in var_dict:
                    target_type = RAMGlobalVar.__annotations__.get(attr, None)
                    value = var_dict[key]
                    # 如果目标类型是list，但值是字符串，则尝试分割
                    if target_type == list:
                        if isinstance(value, str):
                            value = [v.strip() for v in value.split(',') if v.strip()]
                        elif not isinstance(value, list):
                            value = [value]
                    setattr(obj, attr, value)
            # b_backup is always "非保持"
            obj.b_backup = "非保持"
            ram_vars.append(obj)
    return ram_vars

def get_diff_code(repo_path, commit_id, compared_commit_id):
    '''
    根据commit 获取变更代码及所在文件的全量代码
    '''
    diffs = get_git_structured_diff(repo_path, commit_id, compared_commit_id)
    code_diffs = []
    # 获取新增的代码以及当前文件的全量代码
    for file_path, diff_info in diffs.items():
        # 抽取新增的代码内容
        added_lines = [] # 新增的代码内容
        full_code = [] # 当前文件的全量代码
        for item in diff_info.get("diff", []):
            if item['type'] == 'ab':
                full_code.extend(item['content'])
            if item['type'] == 'b':
                added_lines.extend(item['content'])
                full_code.extend(item['content'])

        code_diffs.append({
            "file_path": diff_info["file_path"],
            "added_lines": added_lines,
            "full_code": full_code
        })

    return code_diffs


if __name__ == '__main__':
    repo_path = r"D:\dnkt_19v3\gfx_agl_met"
    commit_id = "7d5e5e8a9910c95fc97ec51da47ff5b8fd6db7a6"
    # commit_id = "50b1aba19c902b40d667b26a80c615e74abf1def"
    compared_commit_id = "c1224e880b25f6a96fb05d402526f25628d7a1bc"
    # get_diff_code(repo_path, commit_id, compared_commit_id)
    file_path = r"D:\tdd_input\684D_Component_List.xlsx"
    search_global_var(repo_path, file_path, commit_id, compared_commit_id)
    logger.info(f"project_name: {commit_id}")
