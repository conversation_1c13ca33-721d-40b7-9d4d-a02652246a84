"""
模板文件管理服务

V字对应：
x.x 对应的V字阶段
x. 对应的V字项目

模板文件管理服务，用于管理各个工作流的模板文件配置，支持自定义模板文件的设置和获取。

主要功能：
1. 加载和保存模板文件配置
2. 设置自定义模板文件路径
3. 获取模板文件路径（优先使用自定义，否则使用内置）
4. 复制模板文件到指定目录
"""

import os
import shutil
import pathlib
from typing import Optional, Dict, Any
from dynaconf import Dynaconf
from loguru import logger

from sdw_agent.config.env import ENV


class TemplateManager:
    """模板文件管理器"""
    
    def __init__(self):
        """初始化模板管理器"""
        # 直接使用内置配置文件
        base_path = pathlib.Path(os.path.abspath(os.path.dirname(__file__)))
        self.config_file = base_path.parent / "config" / "template_file.yaml"
        self.config = self._load_config()

        # 用户自定义模板配置文件路径
        home_dir = pathlib.Path.home()
        config_dir = home_dir / ".sdw" / "config"
        self.custom_templates_file = config_dir / "custom_templates.yaml"

        # 加载用户自定义模板配置
        self.custom_templates = self._load_custom_templates()
    
    def _load_config(self) -> Dynaconf:
        """加载模板配置"""
        if self.config_file.exists():
            logger.info(f"加载模板配置文件: {self.config_file}")
            return Dynaconf(settings_files=[str(self.config_file)])
        else:
            logger.warning(f"模板配置文件不存在: {self.config_file}")
            return Dynaconf()

    def _load_custom_templates(self) -> Dict[str, str]:
        """加载用户自定义模板配置"""
        if not self.custom_templates_file.exists():
            return {}

        try:
            import yaml
            with open(self.custom_templates_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f) or {}
            custom_templates = data.get('custom_templates', {})
            logger.info(f"加载自定义模板配置: {len(custom_templates)} 个自定义模板")
            return custom_templates
        except Exception as e:
            logger.error(f"加载自定义模板配置失败: {e}")
            return {}

    def _save_custom_templates(self) -> bool:
        """保存用户自定义模板配置"""
        try:
            import yaml

            # 确保目录存在
            self.custom_templates_file.parent.mkdir(parents=True, exist_ok=True)

            data = {
                'version': '1.0.0',
                'custom_templates': self.custom_templates
            }

            with open(self.custom_templates_file, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"自定义模板配置已保存到: {self.custom_templates_file}")
            return True
        except Exception as e:
            logger.error(f"保存自定义模板配置失败: {e}")
            return False
    
    def get_template_path(self, template_file_name: str) -> Optional[str]:
        """
        获取模板文件路径

        Args:
            template_file_name: 模板文件名称标识

        Returns:
            模板文件路径，优先返回自定义模板，否则返回内置模板
        """
        try:
            # 优先使用内存中的自定义模板
            if template_file_name in self.custom_templates:
                custom_template = self.custom_templates[template_file_name]
                if custom_template and os.path.exists(custom_template):
                    logger.info(f"使用自定义模板: {custom_template}")
                    return custom_template

            # 使用内置模板
            template_config = self.config.templates.get(template_file_name)
            if not template_config:
                logger.warning(f"未找到模板文件 {template_file_name} 的配置")
                return None

            builtin_template = template_config.get('builtin_template')
            if builtin_template:
                # 转换为绝对路径
                if not os.path.isabs(builtin_template):
                    # 获取项目根目录 (src/sdw_agent/service -> src/sdw_agent -> src -> 项目根)
                    base_path = pathlib.Path(os.path.abspath(os.path.dirname(__file__))).parent.parent.parent
                    builtin_template = str(base_path / builtin_template)

                if os.path.exists(builtin_template):
                    logger.info(f"使用内置模板: {builtin_template}")
                    return builtin_template
                else:
                    logger.error(f"内置模板文件不存在: {builtin_template}")

            return None

        except Exception as e:
            logger.error(f"获取模板路径失败: {e}")
            return None
    
    def set_custom_template(self, template_file_name: str, template_path: Optional[str]) -> bool:
        """
        设置模板文件的自定义路径

        Args:
            template_file_name: 模板文件名称标识
            template_path: 自定义模板文件路径，None表示重置为使用内置模板

        Returns:
            设置是否成功
        """
        try:
            # 检查模板文件配置是否存在
            if template_file_name not in self.config.templates:
                logger.error(f"未找到模板文件 {template_file_name} 的配置")
                return False

            # 如果template_path不为None，检查文件是否存在
            if template_path is not None and not os.path.exists(template_path):
                logger.error(f"模板文件不存在: {template_path}")
                return False

            # 更新内存中的自定义模板配置
            if template_path is None:
                # 重置为使用内置模板
                if template_file_name in self.custom_templates:
                    del self.custom_templates[template_file_name]
            else:
                # 设置自定义模板
                self.custom_templates[template_file_name] = template_path

            # 保存到配置文件
            if not self._save_custom_templates():
                logger.error("保存自定义模板配置失败")
                return False

            logger.info(f"成功设置模板文件 {template_file_name} 的自定义路径: {template_path}")
            return True

        except Exception as e:
            logger.error(f"设置自定义模板失败: {e}")
            return False
    
    def copy_template_to_input_dir(self, template_file_name: str, filename: str) -> Optional[str]:
        """
        复制模板文件到输入数据目录

        Args:
            template_file_name: 模板文件名称标识
            filename: 目标文件名

        Returns:
            复制后的文件路径，失败返回None
        """
        try:
            # 获取模板文件路径
            template_path = self.get_template_path(template_file_name)
            if not template_path:
                logger.error(f"无法获取模板文件 {template_file_name} 的路径")
                return None

            # 确保输入数据目录存在
            input_dir = pathlib.Path(ENV.config.input_data_path)
            input_dir.mkdir(parents=True, exist_ok=True)

            # 构建目标文件路径
            target_path = input_dir / filename

            # 复制文件
            shutil.copy2(template_path, target_path)
            logger.info(f"模板文件已复制到: {target_path}")

            return str(target_path)

        except Exception as e:
            logger.error(f"复制模板文件失败: {e}")
            return None

    def copy_template_to_input_dir_from_source(self, source_path: str, filename: str) -> Optional[str]:
        """
        从指定源文件复制到输入数据目录

        Args:
            source_path: 源文件路径
            filename: 目标文件名

        Returns:
            复制后的文件路径，失败返回None
        """
        try:
            # 检查源文件是否存在
            if not os.path.exists(source_path):
                logger.error(f"源文件不存在: {source_path}")
                return None

            # 确保输入数据目录存在
            input_dir = pathlib.Path(ENV.config.input_data_path)
            input_dir.mkdir(parents=True, exist_ok=True)

            # 构建目标文件路径
            target_path = input_dir / filename

            # 复制文件
            shutil.copy2(source_path, target_path)
            logger.info(f"模板文件已从 {source_path} 复制到: {target_path}")

            return str(target_path)

        except Exception as e:
            logger.error(f"复制模板文件失败: {e}")
            return None
    
    def get_template_list(self) -> Dict[str, Any]:
        """
        获取所有模板配置列表

        Returns:
            模板配置字典
        """
        try:
            templates = {}
            for template_file_name, template_config in self.config.templates.items():
                # 检查是否有自定义模板
                custom_template_path = self.custom_templates.get(template_file_name)

                templates[template_file_name] = {
                    'name': template_config.get('name', ''),
                    'description': template_config.get('description', ''),
                    'file_type': template_config.get('file_type', ''),
                    'has_custom_template': bool(custom_template_path),
                    'custom_template_path': custom_template_path,
                    'builtin_template_exists': self._check_builtin_template_exists(template_config.get('builtin_template'))
                }
            return templates
        except Exception as e:
            logger.error(f"获取模板列表失败: {e}")
            return {}
    
    def _check_builtin_template_exists(self, builtin_template: str) -> bool:
        """检查内置模板文件是否存在"""
        if not builtin_template:
            return False

        if not os.path.isabs(builtin_template):
            # 获取项目根目录 (src/sdw_agent/service -> src/sdw_agent -> src -> 项目根)
            base_path = pathlib.Path(os.path.abspath(os.path.dirname(__file__))).parent.parent.parent
            builtin_template = str(base_path / builtin_template)

        exists = os.path.exists(builtin_template)
        if not exists:
            logger.debug(f"内置模板文件不存在: {builtin_template}")

        return exists
    



# 全局模板管理器实例
template_manager = TemplateManager()
