import os
from copy import deepcopy

# 中间成果物的目录名称
SUB_OUTPUT = "test_view_data"
RESULT_OUTPUT = "test_view_output"

# 保存变更点匹配的类型
TEST_VIEW_TYPE = "test_view_map_type.json"
TYPE_ALPHA = 0.5

# 最终测试观点保存地址
RESULT_JSON = "result.json"
# 最终测试观点结果的输出结构定义
OUTPUT_STRUCTURE = {"testview_class": '', 'testview_item': '',
                            'source': '', 'testview_content_list': []}

# 被选中的变更点保存地址
CHOOSE_CHANGE_POINT = "current_changepoint.json"

# 抽取的功能点结果保存地址
FUNCTION_POINT = "function_point.json"

# 回写测试用例的地址
TEST_CASE = "testcase.xlsx"
# 测试用例匹配测试观点的阈值
VIEW_MAP_CASE_ALPHA = 0.6

# 回写测试观点结果的地址
TEST_VIEW = "testview.xlsx"

# 获取当前文件所在目录的相对路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.join(CURRENT_DIR)

# 规则中非标准名到标准名的映射
STANDARD_DR_NAME = {
    "内部数据算出": "内部データ算出",
    "表示": "表示",
    "画面迁移": "画面遷移",
    "CAN出力": "CAN出力(MCUへ出力)",
    "异常系": "異常系(CAN/電源電圧)",
    "影响机能": "影響機能",
    "其他": "其他"
}
# 无结果或异常结果时，给定输出模板
STANDARD_DR_MODULE = list()
for one_dr in STANDARD_DR_NAME.values():
    copy_module = deepcopy(OUTPUT_STRUCTURE)
    copy_module["testview_class"] = one_dr
    STANDARD_DR_MODULE.append(copy_module)

# 匹配测试大观点及DR观点所用的prompt
DR_MAP_SYSTEM = "你是一个日本汽车领域的专家，擅长对需求的变更信息进行分类"
DR_MAP_USER = """
    <instruction>
        请判断变更信息<input>属于给定类别<label>的得分以及得分理由
    </instruction>

    <requirement>
        1. 必须严格按照json格式输出，不要输出其他内容；
        2. 输出格式按照<output_structure>中的格式输出；
        3. <label>中关键词只需要在<input>信息中存在或相似即可，不需要要求关键词完全覆盖<input>；
        4. 得分的取值范围为0-1；如果判定不相似则给0。
    </requirement>

    <input>
        {{input}}
    </input>

    <label>
        {{label}}
    </label>

    <output_structure>
        {
            "score": 0.5,
            "reason": "给定0.5分值的理由描述"
        }
    </output_structure>

    <question>
        请输出输入的变更信息与给定类别的相似度得分及理由？
    </question>
"""

# 抽取功能点的prompt
FUNCTION_FIND_SYSTEM = "你是一名日本汽车项目研发人员，熟悉的了解汽车的每个功能模块"
FUNCTION_FIND_USER = """
    <instruction>
        现在需要你根据<input>中的功能变更信息和功能列表<function>，提取出这次变更所涉及到的功能项、变更内容、变更类型；
    </instruction>

    <requirement>
        1. 必须严格按照json格式输出，不要输出其他内容；
        2. 输出格式按照<output_structure>中的格式输出；
        3. 变更类型change_type仅包含三种类型：add、update、delete
        4. <input>中大概率出现与<function>中类似的关键字，如果没有类似的关键字，则功能项输出空即可
    </requirement>

    <input>
        {{input}}
    </input>
    
    <function>
        {{function}}
    </function>

    <output_structure>
        {
            "function_point": ["功能点1", "功能点2", "功能点3"],
            "change_content": "变更内容",
            "change_type": "变更类型"
        }
    </output_structure>

    <question>
        请提取输入的变更信息的功能项、变更内容、变更类型？
    </question>
"""

if __name__ == '__main__':
    print(ROOT_DIR)