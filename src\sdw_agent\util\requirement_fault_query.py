#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
需求变更与故障模式查询工具

此工具用于根据需求变更类型查询对应的需求变更描述、故障模式列表以及相关信息。
"""
import json
import os
import pathlib
import re
import sys
import threading
import traceback
from collections import defaultdict
from gettext import gettext
from typing import List

import numpy as np
import pandas as pd
from fastapi import HTTPException
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from pandas import json_normalize
from pydantic import BaseModel, Field
from tenacity import retry, stop_after_attempt, retry_if_result, wait_exponential

from sdw_agent.config.env import ENV
from sdw_agent.llm import kt_azure_azure_gpt4o as azure
from sdw_agent.util.file_util import process_merged_cells


class ChangeTypeDescResult(BaseModel):
    """
    变更类型结果
    """
    id: str = Field(description="变更类型序号，不要变")
    name: str = Field(description="变更类型名", examples=["Option追加", "机能适用性变化"])
    description: str = Field(description="对汽车电子领域变更类型的详细描述，50~100字",
                             examples=["配置选项或功能开关新增或者删除,移动"])
    keywords: str = Field(description="变更类型的关键词", examples=["新規追加、オプション、追加設定、新規移动、删除"])


class ChangeTypeDescResultList(BaseModel):
    """
    变更需求结果列表
    """
    results: List[ChangeTypeDescResult]


class DesignMethodResult(BaseModel):
    """
    变更类型结果
    """
    design_desc_list: List[str] = Field(description="每一条设计评价方针改写后的语句列表，长度与原长度相同")
    change_score: str = Field(description="改写程度评分, 整数[0-10]", examples=["8", "9"])
    # change_reason: str = Field(description="改写依据, 泛用性的规则字段XXX与需求XX信息结合, 50字内关键证据", examples=["因为需求涉及燃費，添加燃費到第一句话主语"])


def clean_string(text):
    """
    清理字符串，移除控制字符和特殊字符
    """
    if pd.isna(text):
        return ""

    # 将text转为字符串
    text = str(text)

    # 替换特殊换行符和控制字符为普通换行符
    text = text.replace('\r\n', '\n').replace('\r', '\n')

    # 移除JSON不支持的控制字符
    text = re.sub(r'[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]', '', text)

    return text


def get_requirement_fault_info(requirement_type):
    """
    根据需求变更类型查询对应的需求变更描述以及故障模式相关信息

    Args:
        requirement_type (str): 需求变更类型，例如"选项追加"

    Returns:
        dict: 包含以下信息的字典：
            - requirement_type: 需求变更类型
            - requirement_description: 需求变更描述
            - fault_modes: 故障模式列表，每个故障模式包含：
                - fault_mode: 故障模式名称
                - fault_description: 故障模式描述
                - concern_description: 担心点内容描述
                - design: 设计
                - evaluation: 评价
    """
    # 确保Excel文件存在

    excel_path = os.path.join(pathlib.Path(ENV.config.input_data_path), "变更点类型&设计评价方针.xlsx")
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel文件 '{excel_path}' 不存在")

    # 读取Excel文件
    df = pd.read_excel(excel_path)

    # 过滤出指定需求变更类型的数据
    filtered_df = df[df['需求变更类型'] == requirement_type]

    # 如果没有找到对应数据，返回空结果
    if filtered_df.empty:
        raise HTTPException(status_code=500, detail=str(f"未找到需求变更类型为 '{requirement_type}' 的数据"))
    # 获取需求变更描述（由于需求变更类型和描述是一一对应的，取第一行的描述即可）
    # requirement_description = clean_string(filtered_df['需求变更描述'].iloc[0])

    # 构建故障模式列表
    fault_modes = []
    for _, row in filtered_df.iterrows():
        # 处理NaN值和清理字符串
        fault_mode_info = {
            "requirement_description": clean_string(row['需求变更描述(组件)']),
            "unit_requirement_description": clean_string(row['需求变更描述(单元)']),
            "fault_mode": clean_string(row['故障模式']),
            "fault_description": clean_string(row['故障模式描述']),
            "concern_description": clean_string(row['担心点内容描述']),
            "design": clean_string(row['设计']),
            "evaluation": clean_string(row['评价'])
        }
        fault_modes.append(fault_mode_info)

    # 构建结果数据
    result = {
        "status": "success",
        "data": {
            "requirement_type": requirement_type,
            # "requirement_description": requirement_description,
            "fault_modes": fault_modes
        }
    }

    return result


def format_json_output(data):
    """
    将数据转换为格式化的JSON字符串
    """
    try:
        # 使用json.dumps将对象转换为字符串
        json_string = json.dumps(
            data,
            ensure_ascii=False,  # 保留中文和其他Unicode字符
            indent=2  # 缩进格式
        )
        return json_string
    except (TypeError, OverflowError) as e:
        # 如果有不可序列化的对象，使用更安全的方法
        logger.warning(f"警告: JSON序列化过程中出现错误: {e}")

        # 递归地将所有对象转换为可序列化的形式
        def convert_to_serializable(obj):
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(i) for i in obj]
            elif pd.isna(obj):
                return ""
            else:
                try:
                    # 尝试json化，看是否能序列化
                    json.dumps(obj)
                    return obj
                except (TypeError, OverflowError):
                    # 如果无法序列化，转换为字符串
                    return str(obj)

        serializable_data = convert_to_serializable(data)
        return json.dumps(serializable_data, ensure_ascii=False, indent=2)


def save_to_file(data, filename):
    """
    将数据保存到文件
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json_str = format_json_output(data)
            f.write(json_str)
        return True
    except Exception as e:
        logger.error(f"保存文件时出错: {e}")
        return False


def print_usage():
    """
    打印使用方法
    """
    print("用法: python requirement_fault_query.py <需求变更类型> [选项]")
    print("选项:")
    print("  --save          将结果保存到文件中")
    print("  --output=FILE   将结果保存到指定的文件")
    print("示例:")
    print("  python requirement_fault_query.py 选项追加")
    print("  python requirement_fault_query.py 选项追加 --save")
    print("  python requirement_fault_query.py 选项追加 --output=result.json")


# 定义提取数字的函数
def extract_change_id(text):
    """
    从输入文本中提取第一个连续数字序列作为变更ID
    Args:
        text (str/int/float/pd.NaT): 待处理文本，支持字符串、数值类型或pandas空值
    Returns:
        str: 提取到的第一个连续数字序列
        None: 输入为空值或未匹配到数字
    """
    if pd.isna(text):
        return None
    # 使用正则表达式匹配所有数字并拼接
    change_id = re.search(r'\d+', str(text))
    return change_id.group()


def clean_guideline_file(guideline_uri: str, sheet_name: str):
    """
    清洗和处理规范文件Excel表格，提取变更类型和故障类型数据
    Args:
        guideline_uri (str): Excel文件路径或URI
        sheet_name (str): 要处理的工作表名称
    Returns:
        tuple: 包含以下元素的元组
            - df (pd.DataFrame): 清洗后的规范数据表格
            - change_type_map (dict): 变更类型映射字典
                - 键: 变更类型ID (str)
                - 值: 变更类型描述 (str)
            - failure_index_map (dict): 故障类型索引映射
                - 键: 故障类型描述 (str)
                - 值: 预设的故障类型索引 (int)
    """
    try:
        df = pd.read_excel(guideline_uri, sheet_name=sheet_name)
        # 找出同时包含"評価方法"、"設計方法"和"设计方针"的行
        rows = df[
            df.apply(lambda row:
                     ('输入' in str(row.values)) and
                     ('设计方针' in str(row.values)) and
                     ('設計·实装步骤' in str(row.values)),
                     axis=1)
        ]
        # 获取最小的规则起始行号
        min_idx = rows.index.min() if not rows.empty else None
        logger.info(f"clean_guideline_file find start_idx{min_idx}")
        df = df.loc[min_idx:]
        # 将第一行设置为列名
        df.columns = df.iloc[0]
        # 删除无意义的第一行
        df = df[1:]

        # 找出规则表格的结束行
        valid_rows = df[~df.iloc[:, 1].isna()]
        if not valid_rows.empty:
            # 输出最小的行号
            min_row_index = valid_rows.index.min()
            logger.info(f"clean_guideline_file find end_idx{min_row_index}")
        else:
            logger.error("clean_guideline_file cant find end_idx")
            return None

        df = process_merged_cells(guideline_uri, sheet_name, min_idx, min_row_index - 1)

        # 提取第二行数据（索引为 1，因为索引从 0 开始）
        new_index = df.iloc[2].tolist()
        # 重置索引（可选，确保索引连续）
        df = df.reset_index(drop=True)
        # 置新索引
        df.columns = new_index
        df = df[3:]

        # 识别重复列名（保留第一个出现的）
        is_duplicate = df.columns.duplicated()
        # 为重复列名添加 "-sub" 后缀
        df.columns = [f"{col}-sub" if is_duplicate[i] else col for i, col in enumerate(df.columns)]

        # 重置索引
        df = df.reset_index(drop=True)

        # 查找列名为"输出的用途 "的列索引
        target_col_idx = df.columns.get_loc("输出的用途")
        # 保留从开头到目标列的所有列（包括目标列）
        df = df.iloc[:, :target_col_idx + 1]

        # 清洗空值 空行 空列  重复行 重复列
        df = df.replace(['-', ''], np.nan).dropna(how='all').dropna(axis=1, how='all')
        df = df.drop_duplicates()
        df = df.T.drop_duplicates().T

        # 处理df的列名
        df = df.rename(columns={'故障モード': '故障类型'})
        df = df.rename(columns={'输入': '变更类型'})
        # 删除故障列或需求列为 NaN/None 的行
        # df = df.dropna(subset=['故障类型', '变更类型'])
        df = df.dropna(subset=['变更类型'])
        failure_types = [
            "実行されない",
            "実行順序ミス",
            "処理時間オーバー",
            "処理間違い",
            "データのタイミングミス",
            "メモリの誤書き込み",
            "入力データミス"
        ]

        # 生成故障类型到索引的映射
        failure_index_map = {ft: idx for idx, ft in enumerate(failure_types)}
        # 添加索引ID列
        df["故障类型ID"] = df["故障类型"].map(failure_index_map).fillna(-1).astype(int)
        # 调整列顺序
        columns = df.columns.tolist()
        fault_type_pos = columns.index("故障类型")
        columns.insert(fault_type_pos + 1, "故障类型ID")
        df = df[columns]

        # 将nan替换为""避免异常
        df = df.replace(np.nan, "")

        df['变更类型ID'] = df['变更类型'].apply(extract_change_id)
        df['变更类型'] = df['变更类型'].str.replace(r'^(\d+[.)]?)\s*', '', regex=True)
        # 1. 去除重复行
        unique_df = df[['变更类型ID', '变更类型']].drop_duplicates()
        # 2. 构建映射字典（ID作为键，类型作为值）
        change_type_map = dict(zip(unique_df['变更类型ID'], unique_df['变更类型']))

        logger.info(f"change_type_map parse result : {str(change_type_map)}")
        return df, change_type_map, failure_index_map

    except Exception as e:
        logger.error(f"clean guideline file error : {str(e)}")
        traceback.print_exc()
        raise e


def guideline_2_json(df):
    """
    将处理后的规范数据表格转换为特定 JSON 格式，按变更类型与故障类型聚合数据
    Args:
        df (pd.DataFrame): 清洗后包含变更类型、故障类型等信息的 DataFrame，需有 '变更类型'、'变更类型ID'、'故障类型'、'故障类型ID' 等列
    Returns:
        list: 转换后的 JSON 结构列表，每个元素为字典，包含：
            - 变更类型: 变更类型描述
            - 变更类型ID: 变更类型对应的 ID
            - 故障类型: 故障类型描述
            - 故障类型ID: 故障类型对应的 ID
            - 内容: 同变更类型、故障类型组合下的原始数据项列表（每个元素为原始行数据字典）
    """
    data = df.to_dict('records')
    # 用于存储汇聚结果的字典
    aggregated_data = {}

    # 遍历原始数据
    for item in data:
        change_type = item['变更类型']
        change_type_ID = item['变更类型ID']
        fault_type = item['故障类型']
        fault_type_ID = item['故障类型ID']

        key = (change_type, change_type_ID, fault_type, fault_type_ID)

        # 如果键不存在，则初始化一个新的列表
        if key not in aggregated_data:
            aggregated_data[key] = []

        # 将当前项添加到对应的列表中
        aggregated_data[key].append(item)

    # 转换为所需的格式
    result = []
    for (change_type, change_type_ID, fault_type, fault_type_ID), content in aggregated_data.items():
        result.append({
            "变更类型": change_type,
            "变更类型ID": change_type_ID,
            "故障类型": fault_type,
            "故障类型ID": fault_type_ID,
            "内容": content
        })
    return result


def parse_changepoint_excel(guideline_uri: str, sheet_name: str = "③差分点分析&读合"):
    """
    这个函数分析变更点分析指南
    """
    try:
        df = pd.read_excel(guideline_uri, sheet_name=sheet_name)
        # 找出guideline规则表的首行
        begin_rows = df[
            df.apply(lambda row:
                     ('变更点内容' in str(row.values)) and
                     ('影响分析' in str(row.values)) and ('仕样书名' in str(row.values))
                     ,
                     axis=1)
        ].index[0]

        # 从 start_idx 开始遍历 DataFrame
        for idx in range(begin_rows, df.shape[0]):
            # 检查当前行是否全为空
            if df.iloc[idx].isna().all():
                break
        # df = df[begin_rows: idx + 1]
        df = process_merged_cells(guideline_uri, sheet_name, begin_rows, idx + 1)

        # 清洗空值 空行 空列 重复行 重复列
        df = df.replace([''], np.nan).dropna(how='all').dropna(axis=1, how='all').replace(np.nan, '')
        df = df.drop_duplicates()
        df = df.T.drop_duplicates().T

        # 将第二行(index=1)设置为表头
        new_header = df.iloc[1]
        df = df[2:]
        df.columns = new_header
        df = df.reset_index(drop=True)

        df = df.rename(columns={'仕样书中标记位置\n（变更点分类）': '变更类型'})
        return df
    except Exception as e:
        traceback.print_exc()
        raise e


def influence_2_json(df):
    """
    这个函数将变更影响分析转换为json格式
    """
    data = df.to_dict('records')
    # 用于存储汇聚结果的字典
    result_data = {}
    # 遍历原始数据
    for item in data:
        req_book_name = item['仕样书名']
        change_type = item['变更类型']
        del item['仕样书名']
        del item['变更类型']
        if req_book_name not in result_data:
            result_data[req_book_name] = {change_type: [item]}
        else:
            if change_type not in result_data[req_book_name]:
                result_data[req_book_name][change_type] = [item]
            else:
                result_data[req_book_name][change_type].append(item)
    return result_data


def parse_change_type_row(row, change_info_map, change_type_map):
    '''
    总体作用：处理和解析输入行数据中的变更类型信息，并通过调用大语言模型提取详细的描述信息，将解析结果填充到一个映射表中。
    输入参数：  1. `row`：字典对象，包含当前行数据的信息。 2. `change_info_map`：字典对象，用于存储变更类型详细信息，以变更类型的ID为键。
    输出： 返回与给定变更类型ID对应的详细信息字典。
    '''
    change_type = str(row['变更类型'])
    change_type_id = re.search(r'(^\d+)\.', change_type, re.MULTILINE)
    if change_type_id is None:
        logger.error(f"change_type_id not found in change_type:{change_type}, can't parse")
        return None
    else:
        change_type_id = change_type_id.group(1)

    change_type = change_type_map[change_type_id]
    if change_type_id not in change_info_map:
        logger.info(f"get_change_types_detail LLM parse, begin: {str(change_type)}")
        template = ChatPromptTemplate(
            [
                ("user", ENV.prompt.design_policy_prompts.change_type_detail_prompt),
            ],
            template_format="mustache"
        )
        chain = template | azure.with_structured_output(ChangeTypeDescResult)
        resp: ChangeTypeDescResultList = chain.invoke(
            {"change_type_name": change_type})
        if resp is None:
            logger.error(f"调用模型：{str(azure)}异常，请检查")
            raise Exception("大模型处理异常，无数据返回, 请检查")
        logger.info(f"get_change_types_detail LLM parse, result {str(resp)}")
        # 不用返回值里面的id和name，不稳定
        change_detail_info = resp.__dict__
        change_detail_info.update({"id": change_type_id, "name": change_type})
        change_info_map[change_type_id] = change_detail_info
    return change_info_map[change_type_id]


def parse_change_type_tree(df, change_type_map):
    """解析变更类型树结构，构建变更类型的DataFrame转为json，按'仕样书名'分组，解析每组的变更类型数据为结构化字典
    Args:
        df (DataFrame): 源数据，需包含'仕样书名'和'变更类型'列
    Returns:
        list: 结构化变更类型树，格式如:
              [{"categories": "组名", "change_type_list": [变更项1{}, 变更项2{}, ...]}, ...]
    """
    change_info_map = {}
    result = []

    # 只取需要的两列并去重
    df = df[['仕样书名', '变更类型']].drop_duplicates()
    for group_name, group_data in df.groupby('仕样书名'):
        items = []
        threads = []
        for _, row in group_data.iterrows():
            thread = threading.Thread(
                target=lambda x: items.append(parse_change_type_row(x, change_info_map, change_type_map)),
                args=(row,)
            )
            thread.start()
            threads.append(thread)
        for thread in threads:
            thread.join()  # 等待所有线程结束
        change_items = [x for x in items if x is not None]
        change_items = sorted(change_items, key=lambda item: int(item['id']))
        group_change_type = {
            "categories": group_name.replace("\n", "").strip(),
            "change_type_list": change_items,
        }
        result.append(group_change_type)
    return result


def get_change_types_detail(change_type_dict):
    '''
    根据传入的变更类型字典，调用大模型生成对应的变更类型描述信息，并返回结构化的描述结果列表
    '''
    llm_imput_change_dict = str(change_type_dict)

    logger.info(f"get_change_types_detail LLM parse, begin: {str(change_type_dict)}")

    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt_630_config.design_policy_prompts.change_type_desc_prompt),
        ],
        template_format="mustache"
    )
    chain = template | azure.with_structured_output(ChangeTypeDescResultList)
    resp: ChangeTypeDescResultList = chain.invoke(
        {"change_type_dict": llm_imput_change_dict, "change_type_dict_len": len(change_type_dict)})

    if resp is None or len(resp.results) == 0:
        raise Exception("大模型处理异常，无数据返回")

    logger.info(f"get_change_types_detail LLM parse, result {str(resp)}")
    # 转json
    # 将对象列表转换为字典列表
    dict_list = [result.model_dump() for result in resp.results]
    return dict_list


def get_tdd_rule_dir():
    """获取.tdd rule配置目录路径"""
    home_dir = os.path.expanduser("~")
    tdd_dir = os.path.join(home_dir, ".tdd")
    rule_dir = os.path.join(tdd_dir, "rule")
    if not os.path.exists(tdd_dir):
        os.makedirs(tdd_dir)
    if not os.path.exists(rule_dir):
        os.makedirs(rule_dir)
    return rule_dir


def parse_guideline_v2(guideline_uri: str, sheet_name: str, guideline_domain: str):
    '''
        用于解析guideline，并将其转换为结构化的JSON格式，便于后续处理和使用。
        主要功能包括：读取Excel文件、清洗数据、提取设计评价方针，变更点影响分析、构建变更类型树，并将结果保存为JSON文件
        '''
    # 清洗输入的guideline文件
    df, change_type_map, failure_index_map = clean_guideline_file(guideline_uri, sheet_name)
    if df is None:
        logger.error(f"guideline file error, read {guideline_uri} error")
        return
        # 处理成json并存入中间文件
    guideline = guideline_2_json(df)

    logger.success(f"提取guideline")

    df_influence = parse_changepoint_excel(guideline_uri)
    influence = influence_2_json(df_influence)
    logger.success(f"提取influence")

    change_type_list = parse_change_type_tree(df_influence, change_type_map)
    logger.success(f"提取chaneg_type_list")

    return {
        'guideline': guideline,
        'influence': influence,
        'change_type_list': change_type_list,
    }


def parse_guideline(guideline_uri: str, sheet_name: str, guideline_domain: str):
    '''
    用于解析guideline，并将其转换为结构化的JSON格式，便于后续处理和使用。
    主要功能包括：读取Excel文件、清洗数据、提取设计评价方针，变更点影响分析、构建变更类型树，并将结果保存为JSON文件
    '''
    # 清洗输入的guideline文件
    try:
        df, change_type_map, failure_index_map = clean_guideline_file(guideline_uri, sheet_name)
        if df is None:
            logger.error(f"guideline file error, read {guideline_uri} error")
            return
        # 处理成json并存入中间文件
        result = guideline_2_json(df)

        # 构建输出路径，使用 os.path.join 确保跨平台兼容性, 保存到.tdd/rule路径下
        rule_output_path = get_tdd_rule_dir()
        json_name = os.path.join(rule_output_path, f"{guideline_domain}_guideline.json")

        with open(json_name, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        logger.success(f"guideline已将数据保存为JSON文件: {json_name}")
    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('解析guideline 设计方针文件到json错误')} {str(e)}"
        raise Exception(exc_msg)

    try:
        df_influence = parse_changepoint_excel(guideline_uri)
        result = influence_2_json(df_influence)
        json_name = os.path.join(rule_output_path, f"{guideline_domain}_influence.json")
        with open(json_name, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.success(f"influence已将数据保存为JSON文件: {json_name}")

    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('解析guideline 变更影响分析文件到json错误')} {str(e)}"
        raise Exception(exc_msg)

    try:
        change_type_list = parse_change_type_tree(df_influence, change_type_map)
        json_name = os.path.join(rule_output_path, f"{guideline_domain}_change_type_list.json")

        with open(json_name, 'w', encoding='utf-8') as f:
            json.dump(change_type_list, f, ensure_ascii=False, indent=2)

        logger.success(f"chaneg_type_list已将数据保存为JSON文件: {json_name}")

    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('解析guideline 变更类型文件到json错误')} {str(e)}"
        raise Exception(exc_msg)

    return


def load_guideline_rule_file(guideline_base_path: str, guideline_domain: str):
    rule_data = None
    tdd_rule_path = get_tdd_rule_dir()
    guideline_json_name = os.path.join(tdd_rule_path, f"{guideline_domain}_guideline.json")
    try:
        with open(guideline_json_name, 'r', encoding='utf-8') as f:
            rule_data = json.load(f)
            return rule_data
    except Exception as e:
        traceback.print_exc()
        raise e


def query_rules_by_input(data, requirement_type_id: int = None, fault_point_type_id: int = None):
    """
    根据输入条件查询数据

    参数:
        data: 数据源列表
        requirement_type: 变更类型（可选）
        fault_point_type: 故障类型（可选）

    返回:
        符合条件的第一条记录，未找到则返回 None
    """
    results = []
    for item in data:
        if int(item['变更类型ID']) < 0 or int(item['故障类型ID']) < 0:
            continue
        # 检查输入条件
        if requirement_type_id is not None and int(item['变更类型ID']) != requirement_type_id:
            continue
        # 检查故障名条件
        if fault_point_type_id is not None and int(item['故障类型ID']) != fault_point_type_id:
            continue
        results.append(item)

    # 输出查询结果数量
    logger.info(f"找到 {len(results)} 条匹配的记录")

    # 返回结果
    if len(results) == 0:
        logger.warning(f"未找到符合条件的记录: 变更类型={requirement_type_id}, 故障类型ID={fault_point_type_id}")
        return None
    return results  # 返回第一条匹配的记录


def get_guideline_rules(guideline_base_path: str, guideline_domain: str, requirement_type_id: int = None,
                        fault_point_type_id: int = None):
    try:
        # 加载规则文件
        logger.info(f"Loading guideline rules from {guideline_base_path} for domain {guideline_domain}")
        data = load_guideline_rule_file(guideline_base_path, guideline_domain)

        if not data:
            logger.warning(f"No data loaded from guideline file for domain {guideline_domain}")
            return None

        # 查询规则
        logger.info(f"Querying rules with requirement_type_id={requirement_type_id}, "
                    f"fault_point_type_id={fault_point_type_id}")
        rule_json = query_rules_by_input(data, requirement_type_id, fault_point_type_id)
        if not rule_json:
            logger.info("No matching rules found")
        return rule_json

    except Exception as e:
        traceback.print_exc()
        return None


def get_change_types_list_all(domain: str):
    # change_type_path = f"{ENV.tdd_config.output_data_path}/rule/{domain}_change_type_list.json"
    change_type_path = os.path.join(get_tdd_rule_dir(), f"{domain}_change_type_list.json")
    try:
        with open(change_type_path, 'r', encoding='utf-8') as file:
            # 直接从文件对象加载JSON数据
            change_types_list = json.load(file)
            df = json_normalize(
                change_types_list,
                record_path=["change_type_list"],  # 指定展开数组的路径
                meta=["categories"]  # 保留外层字段
            )
            df = df[['id', 'name']].drop_duplicates(subset=['id'])
            return df.set_index('id').to_dict('index')
    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('获取变更类型列表异常, 异常为')} {str(e)}"
        raise Exception(exc_msg)


def get_change_incatagory(domain: str):
    # change_type_path = f"{ENV.tdd_config.output_data_path}/rule/{domain}_change_type_list.json"
    change_type_path = os.path.join(get_tdd_rule_dir(), f"{domain}_change_type_list.json")
    try:
        with open(change_type_path, 'r', encoding='utf-8') as file:
            # 直接从文件对象加载JSON数据
            change_types_list = json.load(file)
            return [item["catagory"] for item in change_types_list]
    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('获取变更类型列表异常, 异常为')} {str(e)}"
        raise Exception(exc_msg)


def get_change_types_list_incatagory(domain: str, catagory: str):
    # change_type_path = f"{ENV.tdd_config.output_data_path}/rule/{domain}_change_type_list.json"
    change_type_path = os.path.join(get_tdd_rule_dir(), f"{domain}_change_type_list.json")
    try:
        with open(change_type_path, 'r', encoding='utf-8') as file:
            # 直接从文件对象加载JSON数据
            change_types_list = json.load(file)
            df = json_normalize(
                change_types_list,
                record_path=["change_type_list"],  # 指定展开数组的路径
                meta=["categories"]  # 保留外层字段
            )
            df = df[df['categories'] == catagory].drop(columns=['categories'])

            return df.to_dict('records')
    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('获取变更类型列表异常, 异常为')} {str(e)}"
        raise Exception(exc_msg)


def aggr_design_policy(changeinfo, fault_item_rule, aggr_rule_name, lang):
    # 用于按设计方法聚合步骤，key 是设计方法，value 是步骤列表
    method_steps = defaultdict(list)
    for item in fault_item_rule["内容"]:
        step = item["設計·实装步骤"]
        method = item[aggr_rule_name]
        import numpy as np
        if isinstance(method, float) and np.isnan(method):
            method = ''
        method_steps[method].append(step)

    # 生成合并后的结果（去重+排序+编号合并）
    result = []
    combined_index_list = []
    index = 1  # 全局统一编号
    for method, steps in method_steps.items():
        # 去重并按步骤原始顺序排序（保持数据中的出现顺序，如需自定义排序可修改）
        unique_steps = list(dict.fromkeys(steps))  # 保留首次出现顺序
        # 生成合并的步骤编号（如 "1&3" 表示第1和第3个步骤使用相同设计方法）
        step_indices = [str(i) for i in range(index, index + len(unique_steps))]
        combined_index = "&".join(step_indices)
        method_clean = re.sub(r'[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳]', '', method).replace("\n", " / ")
        result.append(f"{combined_index}. {method_clean}")
        combined_index_list.append(f"{combined_index}.")
        index += len(unique_steps)  # 按去重后的步骤数递增索引

    result_len = len(result)

    formatted_design = "\n".join(["[" + item + "]" for item in result])

    logger.info(f"requirement_type{changeinfo.change_name}")
    logger.info(f"design_info{formatted_design}")

    template = ChatPromptTemplate(
        [
            ('system', ENV.config.language_prompts),
            ("user", ENV.prompt_630_config.design_policy_prompts.design_methods_prompt),
        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}
    change_type_name_list = get_change_types_list_all(changeinfo.change_domain)
    change_type_name = change_type_name_list[str(changeinfo.change_type_id)]["name"]

    chain = template | azure.with_structured_output(DesignMethodResult)
    lang = lang_map.get(lang, "日本語")
    logger.info(f"LLM begin to invoke task : aggr design policy lang {lang}")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: DesignMethodResult = chain.invoke(
            {"design_info": formatted_design, "requirement_title": changeinfo.change_name,
             "requirement_type": change_type_name, "result_len": result_len,
             "lang": lang})
        if result is None:
            logger.warning("大模型调用异常, 现在重试")
        return resp

    try:
        resp = _invoke()
        logger.info(f"LLM parse aggr_design_policy over, result_len： {result_len} , resp: {str(resp)}")
    except Exception as e:
        logger.error(f"大模型调用返回异常，请重试{str(e)}")
        exc_msg = f"{gettext('大模型调用返回异常，请重试')} {str(e)}"
        raise Exception(exc_msg)

    # pattern = r'^\d+\.\s*'  # 匹配开头的数字、点和可能的空格
    pattern = r'^(?:\d+(?:&\d+)?\.)?(.+)$'
    result_temp = []
    for item in resp.design_desc_list:
        # 替换匹配到的部分为空字符串
        # cleaned = re.sub(pattern, '', item)
        # cleaned = re.sub(r'[\d.&]', '', item)
        match = re.match(pattern, item)
        if match:
            cleaned = match.group(1)
        result_temp.append(cleaned)

    result = [f"{combined_index_list[i]} {item}" for i, item in enumerate(result_temp)]
    return "\n".join(result)


def gen_design_policy(changeinfo, lang):
    CSTM_worries = [
        "実行されない",
        "実行順序が間違っている",
        "処理時間がオーバーしている",
        "処理が間違っている",
        "データのTimingが間違っている",
        "Memoryに誤って書き込まれている",
        "入力データが間違っている"
    ]

    design_policy_list = []
    try:
        rule_list = get_guideline_rules(ENV.tdd_config.output_data_path, changeinfo.change_domain,
                                        requirement_type_id=changeinfo.change_type_id)
        if rule_list is None:
            raise LookupError(
                f"没有找到{changeinfo.change_domain}类目下的{changeinfo.change_type_id}这个变更类型规则，请检查")
        logger.info(f"rule_list: {str(rule_list)}")

        for fault_item_rule in rule_list:
            # 提取所有心配な点并去重
            unique_worries = set()
            unique_design = set()
            unique_test = set()

            for item in fault_item_rule["内容"]:
                if "心配な点" in item and item["心配な点"] is not None:
                    unique_worries.add(item["心配な点"])
                if "設計方法" in item and item["設計方法"] is not None:
                    unique_design.add(item["設計方法"])
                if "評価方法" in item and item["評価方法"] is not None:
                    unique_test.add(item["評価方法"])
            # 将去重后的担忧点按序号格式化输出

            # 创建字典用于聚合，键是"設計·实装步骤"，值是该步骤对应的"心配な点"集合
            step_worry_dict = {}
            for item in fault_item_rule["内容"]:
                step = item["設計·实装步骤"]
                worry = item["心配な点"]
                if step not in step_worry_dict:
                    step_worry_dict[step] = set()  # 使用集合来存储去重的担忧点
                step_worry_dict[step].add(worry)  # 添加到集合中自动去重

            # 按照格式输出
            result = []
            for index, (step, worries) in enumerate(step_worry_dict.items(), start=1):
                step_clean = re.sub(r'[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳]', '', step).replace("\n", " / ")
                result.append(f"{index}. {step_clean}")
                # 将集合转换为列表以保持顺序
                worry_list = list(worries)
                for sub_index, worry in enumerate(worry_list, start=1):
                    if worry is not None and len(worry) > 0:
                        result.append(f"  {index}.{sub_index} {worry}")

            # 打印结果
            formatted_worries = "\n".join(result)

            formatted_design = aggr_design_policy(changeinfo, fault_item_rule, "設計方法", lang)
            formatted_test = aggr_design_policy(changeinfo, fault_item_rule, "評価方法", lang)

            each_design = {
                "設計方針": fault_item_rule["内容"][0]["设计方针"],
                "心配な点（該当する故障モードに○を記入）":
                    [
                        [item, "○" if index == fault_item_rule["故障类型ID"] else "-", ""] for index, item in
                        enumerate(CSTM_worries)
                    ]
                ,
                "担心点具体内容": formatted_worries,
                "設計": formatted_design,
                "評価": formatted_test,
            }
            design_policy_list.append(each_design)
        return design_policy_list
    except Exception as e:
        traceback.print_exc()
        exc_msg = f"{gettext('生成设计评价方针失败, 异常为')} {str(e)}"
        raise Exception(exc_msg)


if __name__ == "__main__":
    # 设置标准输出的编码为UTF-8
    if sys.stdout.encoding != 'utf-8':
        # 在Windows上处理中文输出
        try:
            import io

            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        except:
            # 如果上述方法失败，使用另一种方法
            try:
                import codecs

                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
            except:
                # 如果都失败了，告诉用户可能会有编码问题
                logger.warning("警告: 无法设置终端为UTF-8编码，中文显示可能会有问题")

    # 检查命令行参数
    if len(sys.argv) < 2:
        print_usage()
        sys.exit(1)

    # 获取需求变更类型（第一个参数）
    requirement_type = sys.argv[1]

    # 处理其他选项
    save_option = False
    output_file = None

    if len(sys.argv) > 2:
        for arg in sys.argv[2:]:
            if arg == '--save':
                save_option = True
                output_file = f"{requirement_type}_result.json"
            elif arg.startswith('--output='):
                save_option = True
                output_file = arg.split('=')[1]

    try:
        result = get_requirement_fault_info(requirement_type)

        # 根据选项决定输出方式
        if save_option and output_file:
            if save_to_file(result, output_file):
                logger.success(f"结果已保存到 {output_file}")
            else:
                logger.error("保存文件失败")
        else:
            # 直接输出到终端
            logger.info(format_json_output(result))
    except Exception as e:
        error_result = {
            "status": "error",
            "message": str(e)
        }
        logger.error(json.dumps(error_result, ensure_ascii=False, indent=2))
