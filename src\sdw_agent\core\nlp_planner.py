"""自然语言处理和任务规划模块"""
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import re
import logging
from .base import BaseStep
from .workflow import WorkflowDefinition, WorkflowEngine


@dataclass
class QueryIntent:
    """查询意图"""
    intent_type: str  # 意图类型
    entities: Dict[str, Any]  # 提取的实体
    required_steps: List[str]  # 需要执行的步骤
    confidence: float  # 置信度
    

@dataclass
class TaskPlan:
    """任务计划"""
    query: str
    intent: QueryIntent
    workflow_id: str
    selected_steps: List[str]
    step_sequence: List[List[str]]  # 执行序列(考虑并行)
    initial_data: Dict[str, Any] = field(default_factory=dict)


class NLPQueryParser:
    """自然语言查询解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.intent_patterns = self._init_intent_patterns()
        self.step_keywords = self._init_step_keywords()
        
    def _init_intent_patterns(self) -> Dict[str, List[Tuple[str, List[str]]]]:
        """初始化意图识别模式"""
        return {
            "design_standard": [
                (r"设计标准|设计基准|CS", ["dev_1", "dev_3", "dev_5", "dev_12"]),
                (r"法规确认|法规", ["dev_2"]),
            ],
            "test_case": [
                (r"测试用例|检查|通信检查", ["test_5"]),
                (r"检查表|checklist", ["test_1", "test_3"]),
            ],
            "document": [
                (r"设计书|I/F|接口", ["dev_7", "dev_8"]),
                (r"函数式样书|RAM设计", ["dev_10", "dev_11"]),
            ],
            "code_review": [
                (r"代码审查|代码检查|编码标准", ["dev_15", "dev_18", "dev_19"]),
                (r"文件比较|差分", ["dev_16"]),
            ],
            "full_process": [
                (r"完整流程|全流程|端到端", ["all"]),
            ]
        }
        
    def _init_step_keywords(self) -> Dict[str, List[str]]:
        """初始化步骤关键词映射"""
        return {
            "dev_1": ["要求规格", "SCL", "要件一览表"],
            "dev_2": ["法规", "法律", "合规"],
            "dev_3": ["需求分析", "设计标准CS"],
            "dev_4": ["功能一览", "变更内容"],
            "dev_5": ["基本设计", "设计基准"],
            "dev_6": ["通信故障", "Can信号"],
            "dev_7": ["软件设计书", "warning"],
            "dev_8": ["I/F整合", "接口确认"],
            "dev_9": ["CAN输入输出", "CAN入出力"],
            "dev_10": ["函数式样书", "函数流程"],
            "dev_11": ["RAM设计书", "变量信息"],
            "dev_12": ["详细设计"],
            "dev_13": ["设计评审", "同行评审"],
            "dev_14": ["功能更新"],
            "dev_15": ["编码标准", "规则检查"],
            "dev_16": ["文件比较", "差分分析"],
            "dev_17": ["代码审查视角", "Gerrit"],
            "dev_18": ["代码审查"],
            "dev_19": ["代码检查"],
            "test_1": ["检查表IV", "Checklist4"],
            "test_2": ["检查规格书", "测试用例评审"],
            "test_3": ["检查表V", "Checklist5"],
            "test_4": ["耦合检查", "关联性"],
            "test_5": ["通信检查", "通信测试"],
            "test_6": ["真实设备", "bug分析"],
        }
        
    def parse_query(self, query: str) -> QueryIntent:
        """解析用户查询"""
        query_lower = query.lower()
        
        # 提取实体
        entities = self._extract_entities(query)
        
        # 识别意图和步骤
        intent_type, required_steps, confidence = self._identify_intent_and_steps(query_lower)
        
        return QueryIntent(
            intent_type=intent_type,
            entities=entities,
            required_steps=required_steps,
            confidence=confidence
        )
        
    def _extract_entities(self, query: str) -> Dict[str, Any]:
        """提取查询中的实体"""
        entities = {}
        
        # 提取变更需求标识
        change_pattern = r"变更(?:需求|点)?[：:]?\s*([^\s,，。]+)"
        change_match = re.search(change_pattern, query)
        if change_match:
            entities["change_request"] = change_match.group(1)
            
        # 提取文件路径
        path_pattern = r"[a-zA-Z0-9_/\\.-]+\.[a-zA-Z0-9]+"
        paths = re.findall(path_pattern, query)
        if paths:
            entities["file_paths"] = paths
            
        # 提取commit ID
        commit_pattern = r"commit\s*(?:id)?[：:]?\s*([a-f0-9]{7,40})"
        commit_match = re.search(commit_pattern, query, re.IGNORECASE)
        if commit_match:
            entities["commit_id"] = commit_match.group(1)
            
        return entities
        
    def _identify_intent_and_steps(self, query_lower: str) -> Tuple[str, List[str], float]:
        """识别意图和需要的步骤"""
        best_intent = "unknown"
        best_steps = []
        best_confidence = 0.0
        
        # 检查意图模式
        for intent_type, patterns in self.intent_patterns.items():
            for pattern, default_steps in patterns:
                if re.search(pattern, query_lower):
                    # 基于模式匹配的基础置信度
                    confidence = 0.7
                    
                    # 检查是否有特定步骤关键词
                    specific_steps = []
                    for step_id, keywords in self.step_keywords.items():
                        for keyword in keywords:
                            if keyword.lower() in query_lower:
                                specific_steps.append(step_id)
                                confidence += 0.1
                                
                    # 使用特定步骤或默认步骤
                    steps = specific_steps if specific_steps else default_steps
                    
                    # 更新最佳匹配
                    if confidence > best_confidence:
                        best_intent = intent_type
                        best_steps = steps
                        best_confidence = min(confidence, 0.95)
                        
        return best_intent, best_steps, best_confidence


class TaskPlanner:
    """任务规划器"""
    
    def __init__(self, engine: WorkflowEngine):
        self.engine = engine
        self.query_parser = NLPQueryParser()
        self.logger = logging.getLogger(__name__)
        
    def plan_from_query(self, query: str) -> TaskPlan:
        """从查询生成任务计划"""
        # 解析查询意图
        intent = self.query_parser.parse_query(query)
        self.logger.info(f"解析查询意图: {intent}")
        
        # 确定工作流
        workflow_id = self._determine_workflow(intent)
        
        # 扩展步骤列表（考虑依赖）
        expanded_steps = self._expand_steps_with_dependencies(intent.required_steps)
        
        # 计算执行序列
        step_sequence = self._calculate_step_sequence(expanded_steps)
        
        # 准备初始数据
        initial_data = self._prepare_initial_data(intent.entities)
        
        return TaskPlan(
            query=query,
            intent=intent,
            workflow_id=workflow_id,
            selected_steps=expanded_steps,
            step_sequence=step_sequence,
            initial_data=initial_data
        )
        
    def _determine_workflow(self, intent: QueryIntent) -> str:
        """根据意图确定工作流"""
        # 简单映射，实际可以更复杂
        workflow_mapping = {
            "design_standard": "dev_workflow",
            "test_case": "test_workflow",
            "document": "dev_workflow",
            "code_review": "dev_workflow",
            "full_process": "full_workflow"
        }
        
        return workflow_mapping.get(intent.intent_type, "custom_workflow")
        
    def _expand_steps_with_dependencies(self, step_ids: List[str]) -> List[str]:
        """扩展步骤列表，包含所有依赖"""
        if "all" in step_ids:
            # 返回所有步骤
            return list(self.engine.steps_registry.keys())
            
        expanded = set(step_ids)
        
        # 递归添加依赖
        def add_dependencies(step_id: str):
            if step_id not in self.engine.steps_registry:
                return
                
            step = self.engine.steps_registry[step_id]
            for dep in step.dependencies:
                if dep not in expanded:
                    expanded.add(dep)
                    add_dependencies(dep)
                    
        for step_id in step_ids:
            add_dependencies(step_id)
            
        return list(expanded)
        
    def _calculate_step_sequence(self, step_ids: List[str]) -> List[List[str]]:
        """计算步骤执行序列"""
        if not step_ids:
            return []
            
        # 创建临时工作流来利用引擎的并行计算能力
        temp_workflow = WorkflowDefinition(
            workflow_id="temp",
            name="Temporary Workflow",
            description="",
            steps=step_ids
        )
        
        # 临时注册
        self.engine.register_workflow(temp_workflow)
        
        try:
            # 创建执行计划
            plan = self.engine.create_execution_plan("temp", step_ids)
            return plan.parallel_groups
        finally:
            # 清理临时工作流
            del self.engine.workflows_registry["temp"]
            
    def _prepare_initial_data(self, entities: Dict[str, Any]) -> Dict[str, Any]:
        """准备初始数据"""
        initial_data = {"initial_data": entities.copy()}
        
        # 可以在这里添加更多的数据准备逻辑
        
        return initial_data
        
    def execute_plan(self, plan: TaskPlan) -> Dict[str, Any]:
        """执行任务计划"""
        self.logger.info(f"执行任务计划: {plan.workflow_id}")
        self.logger.info(f"选定步骤: {plan.selected_steps}")
        self.logger.info(f"执行序列: {plan.step_sequence}")
        
        # 创建执行计划
        execution_plan = self.engine.create_execution_plan(
            plan.workflow_id,
            plan.selected_steps
        )
        
        # 执行工作流
        result = self.engine.execute_workflow(execution_plan, plan.initial_data)
        
        # 整理结果
        return self._format_execution_result(result)
        
    def _format_execution_result(self, result) -> Dict[str, Any]:
        """格式化执行结果"""
        formatted = {
            "status": result.status.value,
            "duration": result.duration,
            "steps": {}
        }
        
        for step_id, step_result in result.step_results.items():
            formatted["steps"][step_id] = {
                "name": step_result.step_name,
                "status": step_result.status.value,
                "duration": step_result.duration,
                "output": step_result.output.data if step_result.output else None,
                "error": step_result.error
            }
            
        return formatted 