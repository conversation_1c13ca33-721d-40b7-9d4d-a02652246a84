from pygerrit2 import GerritRestAPI, HTTPBasicAuth
from urllib.parse import quote

__all = [
    "get_diff_code_from_gerrit"
]

def get_diff_code_from_gerrit(gerrit_url, commit_id, username='', password=''):
    """
    获取指定commit下所有变更文件的diff及其变更行号信息
    Args:
        gerrit_url (str): Gerrit服务器地址（含端口号）
        commit_id (str): 提交的commit hash值
        username (str, optional): Gerrit账号用户名，默认空字符串
        password (str, optional): Gerrit账号密码，默认空字符串
    
    返回格式:
    {
        "file_path": {
            "project": project_name,
            "change_id": change_id,
            "commit_id": commit_id,
            "file_path": file_path,
            "diff": diff原始数据,
            "changed_lines": {
                "added": [新增代码在新文件中的行号, ...],
                "deleted": [删除代码在旧文件中的行号, ...],
            }
        },
        ...
    }
    """
    try:
        auth = HTTPBasicAuth(username, password)
        rest = GerritRestAPI(url=gerrit_url, auth=auth)

        # 查询change
        query = {'q': f'commit:{commit_id}', 'o': ['CURRENT_REVISION', 'ALL_COMMITS', 'DETAILED_ACCOUNTS']}
        changes = rest.get("/changes/", params=query)

        files = {}
        # 遍历所有变更
        for change in changes:
            change_id = change['change_id']
            project_name = change['project']
            revisions = change.get('revisions', {})

            # 遍历变更的所有修订版本
            for revision_id, revision_info in revisions.items():
                if revision_id == commit_id:
                    # 找到匹配的修订版本
                    files = rest.get(f"/changes/{change_id}/revisions/{revision_id}/files")
                    return _process_files(rest, change_id, commit_id, project_name, files)
                else:
                    continue

        if not files:
            raise ValueError(f"未找到与 commit_id 匹配的变更文件: {commit_id}")

        return None
    except Exception as e:
        raise ValueError(f"Gerrit 查询错误，请传入本地仓库所在路径: {e}")


def _validate_changes(changes, commit_id):
    """验证changes查询结果有效性"""
    if not changes:
        raise ValueError("No matching changes found")
    if changes is None:
        raise ValueError(f"{_('该commit_id未匹配到代码变更内容:')}{commit_id}") # noqa



    return {
        'change_id': changes[0]['change_id'],
        'project': changes[0]['project'],
        'branch': changes[0]['branch']
    }


def _process_files(rest, change_id, commit_id, project_name, files):
    """处理文件diff信息"""
    file_diffs = {}
    for file_path in files:
        if file_path == "/COMMIT_MSG":
            continue  # 跳过提交信息
        
        # 处理单个文件diff
        encoded_file_path = quote(file_path, safe='')
        diff = rest.get(f"/changes/{change_id}/revisions/{commit_id}/files/{encoded_file_path}/diff")
        changed_lines = _parse_diff_content(diff.get("content", []))
        
        # 构建返回结果
        file_diffs[file_path] = {
            "project": project_name,
            "change_id": change_id,
            "commit_id": commit_id,
            "file_path": file_path,
            "diff": diff,
            "changed_lines": changed_lines
        }
    
    return file_diffs


def _parse_diff_content(content):
    """解析diff内容获取变更行号"""
    changed_lines = {"added": [], "deleted": []}
    old_line = 0
    new_line = 0
    
    for chunk in content:
        if "ab" in chunk:  # 处理未变更内容
            lines = chunk["ab"]
            old_line += len(lines)
            new_line += len(lines)
        else:
            if "a" in chunk:  # 处理删除内容
                lines = chunk["a"]
                for i in range(len(lines)):
                    changed_lines["deleted"].append(old_line + 1)
                    old_line += 1
            
            if "b" in chunk:  # 处理新增内容
                lines = chunk["b"]
                for i in range(len(lines)):
                    changed_lines["added"].append(new_line + 1)
                    new_line += 1
    
    return changed_lines
