"""
开发环境检查工作流路由 - 软件包对比检查
"""
from fastapi import APIRouter
from pydantic import BaseModel
from typing import Dict, Any

from sdw_agent.service.dev_env_check.workflow_dev_env_check import DeVEnvCheckWorkflow
from sdw_agent.service.dev_env_check.models import PackageFileCompareInfo

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/dev_env_check", tags=["开发环境检查工作流"])


class DevEnvCheckRequest(BaseModel):
    """
    开发环境检查请求模型。
    功能说明:
    - 用于定义开发环境检查接口的请求数据结构。
    - 包含预检查和后检查的相关文件路径。
    """
    pre_pack_path: str = ""  # 旧版本二进制检查的软件包路径，默认为空字符串。
    after_pack_path: str = ""  # 新版本二进制检查的软件包路径，默认为空字符串。
    pre_manifest_path: str = ""  # 旧版本Manifest清单文件路径，默认为空字符串。
    after_manifest_path: str = ""  # 新版本Manifest清单文件路径，默认为空字符串。
    pre_jenkins_script_path: str = ""  # 旧版本Jenkins 脚本路径，默认为空字符串。
    after_jenkins_script_path: str = ""  # 新版本Jenkins 脚本路径，默认为空字符串。


class DevEnvCheckResponse(BaseModel):
    """开发环境检查响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


# API端点
@router.post("/getCheckResultBook",
             summary="软件包对比检查接口",
             description="对比两个软件包版本的差异，生成对比报告",
             response_model=DevEnvCheckResponse)
async def compare_packages_process(request: DevEnvCheckRequest):
    """
    软件包对比检查处理函数
    
    Args:
        request: 请求参数
        db: 数据库会话
        
    Returns:
        处理结果
    """
    try:
        # 创建工作流实例
        workflow = DeVEnvCheckWorkflow()

        # 转换请求数据为内部模型
        config = PackageFileCompareInfo(
            pre_pack_path=request.pre_pack_path,
            after_pack_path=request.after_pack_path,
            pre_manifest_path=request.pre_manifest_path,
            after_manifest_path=request.after_manifest_path,
            pre_jenkins_script_path=request.pre_jenkins_script_path,
            after_jenkins_script_path=request.after_jenkins_script_path,
        )

        # 执行工作流
        result = workflow.execute(config)

        if result.genetate_status:
            return DevEnvCheckResponse(
                code=0,
                msg="开发环境检查完成",
                data={
                    "bin_compare_result": result.bin_compare_excel_path,
                    "manifest_compare_result": result.manifest_compare_excel_path,
                    "jenkins_script_compare_result": result.jenkins_script_compare_excel_path,
                }
            )
        else:
            return DevEnvCheckResponse(
                code=500,
                msg=result.get("message", "开发环境检查失败"),
                data={}
            )

    except Exception as e:
        return DevEnvCheckResponse(
            code=1,
            msg=f"处理失败: {str(e)}",
            data={}
        )
