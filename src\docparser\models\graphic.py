
from .base_object import BaseObject
from .layout import LayoutObject
from .position import Position
from .style import StyleObject
from .coordinate import CoordinateObject

class GraphicObject(BaseObject):
    """图形对象"""

    def __init__(self):
        self._type = "graphic"
        self._id = ''  # ID
        self._name = ''  # 名称
        self._width = ''  # 宽
        self._height = ''  # 高
        self._digest = ''  # 图片数据的hash值
        self._data = ''  # 图片二进制数据的base64编码

        self._text = ''  # 图形文本框内容
        self._graphic_type = ''  # 类型，如矩形rect、线条line、嵌入对象等

        self._layout = LayoutObject()  # 文本布局对象
        self._style = StyleObject()  # 样式
        self._coordinate = CoordinateObject()  # 坐标位置
        self._data_id = None  # 唯一标识
        self._position = Position()

    def to_dict(self):
        """
        将 GraphicObject 对象转换为字典
        """
        return {
            "type": self._type,
            "id": self._id,
            "name": self._name,
            "width": self._width,
            "height": self._height,
            "digest": self._digest,
            "data": self._data,
            "text": self._text,
            "graphic_type": self._graphic_type,
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "style": self._style.to_dict(),  # 调用 StyleObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
            "data_id": self._data_id,
            "position": self._position.to_dict()
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 GraphicObject 实例
        """
        obj = cls()
        obj._type = data.get("type", "graphic")
        obj._id = data.get("id", '')
        obj._name = data.get("name", '')
        obj._width = data.get("width", '')
        obj._height = data.get("height", '')
        obj._digest = data.get("digest", '')
        obj._data = data.get("data", '')
        obj._text = data.get("text", '')
        obj._graphic_type = data.get("graphic_type", '')
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 恢复 StyleObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        obj._data_id = data.get("data_id", None)
        obj._position = Position.from_dict(data.get("position", {}))

        return obj

    def __repr__(self):
        return f'{self.__class__.__name__}()[{str(self) }]'

    def __str__(self):
        coordinate_str = f'\nCoordinate: {self._coordinate.desc}' if self._coordinate.desc else ''
        return f'Image [{self._name}]: Width:{self._width}, Height:{self._height}. \nType:{self._type}. \nOffset: Top:{self._coordinate.top or 0},Left:{self._coordinate.left or 0}.{coordinate_str}'

    def get_data(self):
        """精简化输出支持"""
        data = {
            "data_id": self.data_id,
            'type': 'graphic',
            "parent_content": self.layout.parent_content,
            'content': {
                'id': self.id,
                'name': self.name,
                'width': self.width,
                'height': self.height,
                'data': self.data,
                'digest': self.digest,
                'text': self.text,
            },
            "index": self.index if hasattr(self, "index") else 0
        }
        if self.coordinate.desc:
            data["coord"] = self.coordinate.desc
        else:
            data["page_num"] = self.layout.page_id

        if hasattr(self, 'text_obj'):
            runs_style_obj = []
            from docparser.models.table import get_text_obj_runs_style
            get_text_obj_runs_style(self.text_obj, runs_style_obj)
            if runs_style_obj:
                data['runs_style'] = runs_style_obj

        return data

    @property
    def data_id(self):
        return self._data_id

    @data_id.setter
    def data_id(self, new_value):
        assert type(new_value) == int
        self._data_id = new_value

    @property
    def id(self):
        return self._id

    @id.setter
    def id(self, new_value):
        assert type(new_value) == str
        self._id = new_value

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, new_value):
        assert type(new_value) == str
        self._name = new_value

    @property
    def width(self):
        return self._width

    @width.setter
    def width(self, new_value):
        assert type(new_value) == str
        self._width = new_value

    @property
    def height(self):
        return self._height

    @height.setter
    def height(self, new_value):
        assert type(new_value) == str
        self._height = new_value

    @property
    def digest(self):
        return self._digest

    @digest.setter
    def digest(self, new_value):
        assert type(new_value) == str
        self._digest = new_value

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, new_value):
        assert type(new_value) == str
        self._data = new_value

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, new_value):
        assert type(new_value) == str
        self._text = new_value

    @property
    def graphic_type(self):
        return self._graphic_type

    @graphic_type.setter
    def graphic_type(self, new_value):
        assert type(new_value) == str
        self._graphic_type = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value

    @property
    def position(self):
        return self._position

    @position.setter
    def position(self, new_value):
        assert isinstance(new_value, Position)
        self._position = new_value
