"""
@File    : car_info_resolver.py
@Time    : 2025/7/28 18:01
<AUTHOR> qiliang.zhou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 base 车辆选定
@Desc    : $ 从式样DB中解析出包含的车型信息
"""
import re
from loguru import logger

from sdw_agent.service.workflow_config import WorkflowConfigManager
from sdw_agent.util.excel.core import ExcelUtil


class CarInfoResolver:
    def __init__(self, db_path):

        self.db_path = db_path
        self.excel = ExcelUtil(db_path, auto_create=False)
        self.logger = logger.bind(name="base_car_select")

        # 加载配置信息
        self._load_config()

    def _load_config(self):
        config_manager = WorkflowConfigManager(workflow_name="base_car_select")
        self.config = config_manager.get_config()
        self.config_manager = config_manager


    def resolve_car_info(self):
        """
        从式样DB中解析出车型信息
        Returns:

        """
        db_type = self._identify_excel_type()
        car_info = None

        if db_type == 'v3_db':
            car_info = self._resolve_v3_db()
        elif db_type == 'const_db':
            car_info = self._resolve_const_db()
        elif db_type == 'standard_db':
            car_info = self._resolve_standard_db()

        return car_info

    def _identify_excel_type(self):
        """
        识别出式样数の类别： 标准式样DB(面企画)，定数式样DB(面企画)，制御式样書目次（19v3）
        Returns:
        """
        # 获取所有sheet 名称
        sheet_names = self.excel.get_sheet_names()
        if not sheet_names:
            raise ValueError("式样书解析失败")

        excel_type = None
        standard_db_identifier = self.config_manager.get('workflow_config.standard_db_identifier', '標準仕様書DB')
        const_db_identifier = self.config_manager.get('workflow_config.const_db_identifier', '標準定数仕様書DB')
        v3_db_identifier = self.config_manager.get('workflow_config.v3_db_identifier', '制御仕様書目次')

        for sheet_name in sheet_names:
            if standard_db_identifier in sheet_name:
                excel_type = 'standard_db'
                break
            elif const_db_identifier in sheet_name:
                excel_type = 'const_db'
                break
            elif v3_db_identifier in sheet_name:
                excel_type = 'v3_db'
                break

        return excel_type

    @staticmethod
    def trans_col_name(col_name):
        """
        将字母表示的列名转换成列索引
        :param col_name:
        :return:
        """
        col_idx = 0
        for char in col_name:
            col_idx = col_idx * 26 + (ord(char.upper()) - ord('A') + 1)

        return col_idx

    def _resolve_standard_db(self):
        """
        从标准式样DB中解析出车辆信息
        Returns:
        """
        # todo
        # 从面企画项目的标准式样DB中解析出所有车型信息
        raise ValueError("暂不支持从面企画标准式样DB中解析出所有车型信息")

        pass

    def _resolve_const_db(self):
        """
        从定数式样DB中解析出车辆信息
        Returns:
        """
        # todo
        # 从面企画项目的定数式样DB中解析出所有车型信息
        raise ValueError("暂不支持从面企画定数式样DB中解析出所有车型信息")

    def _resolve_v3_db(self):
        """
        从制御式样DB中解析出车辆信息
        Returns:
        """
        multi_header = self.excel.extract_multi_level_headers(
            sheet_name='制御仕様書目次',
            start_row=self.config_manager.get('workflow_config.v3_db.header_range.start_row', 2),
            end_row=self.config_manager.get('workflow_config.v3_db.header_range.end_row', 3),
            start_col=self.trans_col_name(self.config_manager.get('workflow_config.v3_db.header_range.start_col', 'A')),
            end_col=self.trans_col_name(self.config_manager.get('workflow_config.v3_db.header_range.end_col', 'BK'))
        )
        logger.info(multi_header)
        flat_header = multi_header.flat_columns
        # '19ePFv3　ソフトリリース  （#1/#4用） | R5-0'
        car_name_dict = {}
        prefix = self.config.get('workflow_config',{}).get('v3_db',{}).get('car_resolver_col_prefix','19ePFv3ソフトリリース')
        for header in flat_header:
            # 将header中的全角空格和半角空格都替换成""
            clean_header = header.replace('　', '').replace(' ', '')
            if clean_header.startswith(prefix):
                # 识别出clean_header中该车型适用的版本信息
                matches = re.findall(
                    self.config.get('workflow_config',{}).get('v3_db',{}).get('version_finder_pattern',"#\s*\d+(?:\.\d+)?"),
                    clean_header
                )
                version_str = ','.join(matches)
                if not version_str:
                    car_name_dict['未指定版本信息'].append(clean_header.split('|')[1])
                    continue

                if version_str not in car_name_dict:
                    car_name_dict[version_str] = [clean_header.split('|')[1]]
                else:
                    car_name_dict[version_str].append(clean_header.split('|')[1])

        return car_name_dict

if __name__ == '__main__':
    # car_info_resolver = CarInfoResolver(db_path=r"D:\tdd_input\面開発管理用_標準仕様DB_231110_v0093.xlsm")
    car_info_resolver = CarInfoResolver(db_path=r"D:\tdd_input\19ePFv3メータ制御仕様書目次 Ver.R6-10.xlsx")
    car_info_resolver.resolve_car_info()
