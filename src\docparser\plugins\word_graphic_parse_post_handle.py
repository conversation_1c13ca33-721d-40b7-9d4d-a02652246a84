import logging
import tempfile
import win32com.client
import shutil
import pythoncom
import math
import os
import io
# import fitz  # PyMuPDF
from typing import Any, Dict, List, Optional, Union
from docparser.interfaces.plugin_interface import PluginInterface
from docparser.models.picture import PictureObject
from docparser.models.graphic import GraphicObject
from contextlib import contextmanager
from PIL import Image
from base64 import b64encode
from docparser.common.base import get_img_hash
from docparser.models.position import Position
from docparser.models.document import DocumentObject

# Configure logging
logger = logging.getLogger('docparser.plugins.combine_graphic_plugin')

class GraphicPostParsePlugin(PluginInterface):
    """
        It includes two main functions:
            1) Combine graphs and pictures which looks like an entirety,
                while it constitutes by several separate graph(s) and/or picture(s)
            2) Screenshot for the graph(s) and/or picture(s) which do not have 'data' values,
                because some uncertain bug(s) happened when parse word file(s).
                (This is a backup method)
    """

    def __init__(self):
        super().__init__()
        self.DPI = 96

    def get_plugin_name(self) -> str:
        """Get plugin name"""
        return "CombineGraphicPlugin"

    def get_plugin_version(self) -> str:
        """Get plugin version"""
        return "1.0.0"

    def get_supported_document_types(self) -> List[str]:
        """Get supported document types"""
        return ["docx", "doc"]  # Support all document types

    def _get_graphic_pictures_positions(self, objs: list,page_width, page_height) :
        """
            给定一个list([GraphicObject...] or [PictureObject...] or [GraphicObject,PictureObject...])
            计算每个图形的位置,返回一个二维列表
        """
        positions = []

        for obj in objs:
            position =[obj.position.x, obj.position.y]

            if hasattr(obj, 'px_height') and float(obj.px_height) != 0:
                w = (float(obj.px_width) / page_width) * 100
                h = (float(obj.px_height) / page_height) * 100
            else:
                w = (float(obj.width) / page_width) * 100
                h = (float(obj.height) / page_height) * 100
            position.append(w)
            position.append(h)
            positions.append(position)

        return positions

    @staticmethod
    def _calculate_max_range(rectangles):
        """
        给定多个矩形的范围计算最大外包矩形

        Args:
            rectangles (list): 2D列表，每个子列表包含4个值 [x, y, width, height]

        Returns:
            list: 包括最大范围的[x, y, width, height]
        """
        # 初始化边界值
        min_x = float('inf')  # 初始值设为最大正数
        min_y = float('inf')
        max_r = float('-inf')  # 初始值设为最小负数
        max_b = float('-inf')

        # 遍历每个矩形，计算边界范围
        for rect in rectangles:
            x, y, width, height = rect
            r = x + width
            b = y + height
            min_x = min(min_x, x)
            min_y = min(min_y, y)
            max_r = max(max_r, r)
            max_b = max(max_b, b)

        # 计算外包矩形的宽和高
        total_width = max_r - min_x
        total_height = max_b - min_y

        # 返回结果：新的外包矩形
        return [min_x, min_y, total_width, total_height]

    def _find_overlapping_groups(self, picture_objects, graphic_objects, page_width, page_height):
        """
            使用广度优先搜索，确保多图组合(e.g. 一张大图嵌套多张小图)都可以被正确覆盖
            找到重合的对象并分组，同时从原列表中移除这些对象
        """
        all_objects = picture_objects + graphic_objects  # 合并两个列表
        overlapping_groups = []  # 用于存储结果二维列表
        visited = set()  # 用于记录已处理的对象
        grouped_objects = set()  # 记录被分组的对象

        # 创建位置索引字典 {对象: [x, y, w, h]}
        position_dict = {}
        for obj in all_objects:
            pos = [
                obj.position.x,
                obj.position.y,
                (float(obj.px_width) / page_width * 100) if hasattr(obj, 'px_height') and float(obj.px_height) != 0
                    else (float(obj.width) / page_width * 100),
                (float(obj.px_height) / page_height * 100) if hasattr(obj, 'px_height') and float(obj.px_height) != 0
                    else (float(obj.height) / page_height * 100)
            ]
            position_dict[obj] = pos

        for key,value in position_dict.items():
            print(key.name)
            print(key.layout.page_id)
            print(key.position.to_dict())
            print(value)
            print('##'*50)

        # 使用BFS算法进行连通域分组
        for obj in all_objects:
            if obj in visited:
                continue

            queue = [obj]
            group = []

            while queue:
                current = queue.pop(0)
                if current in visited:
                    continue

                visited.add(current)
                group.append(current)

                # 查找所有与当前对象重合的未访问对象
                for other in all_objects:
                    if other == current or other in visited:
                        continue

                    # 检查是否在同一页
                    if current.layout.page_id != other.layout.page_id:
                        continue

                    # 检查是否重合
                    if self._calculate_overlap(
                        page_width, page_height,
                        position_dict.get(current),
                        position_dict.get(other)
                    ):
                        queue.append(other)

            # 只保留包含多个对象的组
            if len(group) > 1:
                overlapping_groups.append(group)
                grouped_objects.update(group)  # 将组内对象添加到 grouped_objects

        # 从原列表中移除重合的对象
        picture_objects[:] = [obj for obj in picture_objects if obj not in grouped_objects]
        graphic_objects[:] = [obj for obj in graphic_objects if obj not in grouped_objects]
        return picture_objects, graphic_objects, overlapping_groups

    def _calculate_overlap(self, page_width, page_height,pos1=None, pos2=None, threshold=0.9):
        """计算两个矩形图像的重合度（支持预计算位置）"""
        # 如果提供了预计算位置则直接使用
        # if pos1 is None:
        #     if hasattr(obj1, 'px_height') and int(obj1.px_height) != 0:
        #         w1 = (float(obj1.px_width) / page_width) * 100
        #         h1 = (float(obj1.px_height) / page_height) * 100
        #     else:
        #         w1 = (float(obj1.width) / page_width) * 100
        #         h1 = (float(obj1.height) / page_height) * 100
        #     x1, y1 = obj1.position.x, obj1.position.y
        # else:
        #
        #
        # if pos2 is None:
        #     if hasattr(obj2, 'px_height') and int(obj2.px_height) != 0:
        #         w2 = (float(obj2.px_width) / page_width) * 100
        #         h2 = (float(obj2.px_height) / page_height) * 100
        #     else:
        #         w2 = (float(obj2.width) / page_width) * 100
        #         h2 = (float(obj2.height) / page_height) * 100
        #     x2, y2 = obj2.position.x, obj2.position.y
        # else:

        x1, y1, w1, h1 = pos1
        x2, y2, w2, h2 = pos2

        # 计算交集区域
        inter_x1 = max(x1, x2)
        inter_y1 = max(y1, y2)
        inter_x2 = min(x1 + w1, x2 + w2)
        inter_y2 = min(y1 + h1, y2 + h2)

        # 检查是否有交集
        if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
            return False

        intersection_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1) # 计算交集面积
        min_area = min(w1 * h1, w2 * h2) # 计算较小对象的面积
        overlap_ratio = intersection_area / min_area # 计算重合度

        return overlap_ratio > threshold

    def _find_null_data(self,objects):
        """
            找出在解析过程中因数据读取异常(在差分端同时调用的时候）而没有数值的数据；使用pdf截图补充
        """
        null_objects = []
        for obj in objects:
            tmp_data = obj.data
            if tmp_data.strip() == '' or tmp_data.strip() == None :
                null_objects.append(obj)

        objects[:] = [obj for obj in objects if obj not in null_objects]

        return objects, null_objects

    def convert_percentage2pt_via_px(self,position, page_width, page_height,scale_x, scale_y):
        """
            将position（储存在word中的，以px为基数的，x,y,w,h，
            转换成以point为单位的，在pdf中的坐标系 x0，y0，x1，y1的值，以便截图使用
        """
        x0 = (position[0]/100) * page_width
        y0 = (position[1]/100) * page_height
        x1 = x0 + (position[2]/100) * page_width
        y1 = y0 + (position[3]/100) * page_height

        # To be fixed
        # when convert word to pdf, pdf file will continue the comment part on the right side,
        # which will change the total width
        # As a result of it, the x0,y0,x1,y1 will be changed

        # x0 = ((72*x0)/self.DPI)*scale_x
        # y0 = ((72*y0)/self.DPI)*scale_y
        # x1 = ((72*x1)/self.DPI)*scale_x
        # y1 = ((72*y1)/self.DPI)*scale_y

        x0 = ((72*x0)/self.DPI)
        y0 = ((72*y0)/self.DPI)
        x1 = ((72*x1)/self.DPI)
        y1 = ((72*y1)/self.DPI)

        return [x0,y0,x1,y1] # 以point为单位的左上角和右下角坐标

    def process_document(self, document: DocumentObject):
        """
        Process document data.

        Args:
            document: Dictionary containing parsed document data

        Returns:
            Processed document data
        """

        def save_word_as_pdf(app, source_path, pdf_path):
            """
                Word转PDF方法
                复制自 bibrain 模块，word_convert_service.py文件
            """
            doc = None
            try:
                doc = app.Documents.Open(source_path)
                doc.ExportAsFixedFormat(
                    OutputFileName=pdf_path,
                    ExportFormat=17,  # PDF
                    OpenAfterExport=False
                )
            except Exception as e:
                raise RuntimeError(f"Word保存PDF失败: {str(e)}") from e
            finally:
                if doc:
                    doc.Close(SaveChanges=False)

        # 前置处理传入的值是DocumentObject，其中包含一个key为‘document’的list
        # 该list中的每个元素都是DocumentBlockObject （按照目前设计，word的解析文档中一个DocumentObject只包含一个DocumentBlockObject）
        # DocumentBlockObject 中包含key为‘graphics’，‘pictures’，‘tables’的list
        document_files = document.document
        for document_file in document_files:
            # 数据准备
            pdf_path, temp_dir = None, None
            word_path = document.file_path
            doc, pages = None, None
            try:
                # 转换成pdf
                pdf_path, temp_dir = self._convert_to_pdf(
                    word_path,
                    "Word.Application",
                    save_word_as_pdf
                )
                doc, pages = self._get_PDF_file(pdf_path)

                # 获取word文档的宽高
                self.page_width = document_file.page_width  # word文档的宽度 (point)
                self.page_height = document_file.page_height  # word文档的高度 (point)
                page_width = self.page_width * (self.DPI / 72)  # word文档的宽度 (pixel)
                page_height = self.page_height * (self.DPI / 72)  # word文档的高度 (pixel)

                # 获取pdf的宽和高，
                pdf_rect = pages[0].rect
                pdf_width = pdf_rect.width  #point
                pdf_height = pdf_rect.height #point

                # pdf和word的宽高进行等比换算，分别计算x,y缩放因子以应对非等比缩放
                scale_x = pdf_width / self.page_width
                scale_y = pdf_height / self.page_height

                # 遍历每一个table 关系如下：TableObject -> RowOject -> CellObject -> content -> PictureObject/GraphicObject
                # 如果一个cell中存在两张及以上的PictureObject 和/或 GraphicObject，则将整个cell截图成为一个整体
                tables = document_file.tables
                for table in tables:
                    for row in table.rows:
                        for cell in row.cells:
                            count = 0
                            page_idx = -1
                            tmp_obj_data_dict = None
                            for c in cell.content:
                                if count > 1 and page_idx != -1 and tmp_obj_data_dict is not None:
                                    cell_position = [float(cell.position.x),float(cell.position.y),float(cell.position.width),float(cell.position.height)]
                                    cell_position_inPDF = self.convert_percentage2pt_via_px(cell_position, page_width, page_height,scale_x, scale_y)
                                    digest, _, new_image_binary = self.screenshotPDF(pages[page_idx-1], cell_position_inPDF)
                                    tmp_obj_data_dict['type'] = "graphic"
                                    new_graphic_obj = GraphicObject.from_dict(tmp_obj_data_dict)
                                    new_graphic_obj.width = str((cell_position[3]/100)*page_width)  # px
                                    new_graphic_obj.height = str((cell_position[4]/100)*page_height )  # px
                                    new_graphic_obj.data = new_image_binary
                                    new_graphic_obj.digest = digest
                                    new_graphic_obj.graphic_type = 'cell_graph'
                                    new_graphic_obj_position = Position()
                                    new_graphic_obj_position.x = cell_position[0]
                                    new_graphic_obj_position.y = cell_position[1]
                                    new_graphic_obj_position.width = cell_position[2]
                                    new_graphic_obj_position.height = cell_position[3]
                                    new_graphic_obj.position = new_graphic_obj_position  # %
                                    # To be determined in the future
                                    # 现行的逻辑是如果一个cell中存在两张及以上的PictureObject 和/或 GraphicObject，则将整个cell截图成为一个整体
                                    # 同时删除该cell content中所有的内容，替换成该cell截图
                                    # 潜在问题：会删掉 TextObject，是否需要保留，有待进一步确认
                                    cell.content = [new_graphic_obj]
                                    break
                                if isinstance(c, GraphicObject):
                                    count +=1
                                    tmp_obj_data_dict = c.to_dict()
                                    page_idx = int(c.layout.page_id)
                                elif isinstance(c, PictureObject):
                                    count +=1
                                    tmp_obj_data_dict = c.to_dict()
                                    page_idx = int(c.layout.page_id)

                # 分别获取
                graphics = document_file.graphics  # list [GraphicObject ...]
                pictures = document_file.pictures  # list [PictureObject ...]

                pictures, graphics, overlapping_groups = self._find_overlapping_groups(pictures, graphics, page_width,page_height)
                if len(overlapping_groups) > 0:
                    combine_graphics = []

                    for overlapping in overlapping_groups:
                        group_positions = self._get_graphic_pictures_positions(overlapping, page_width, page_height)
                        new_image_position = self._calculate_max_range(group_positions)  #储存的[x,y,width,height]的百分比
                        new_image_width = math.ceil((new_image_position[2]/100) * page_width)  # 宽的pixel值
                        new_image_height = math.ceil((new_image_position[3]/100) * page_height) # 高的pixel值
                        tmp_pdf_position = self.convert_percentage2pt_via_px(new_image_position, page_width, page_height,scale_x, scale_y)
                        digest, _,new_image_binary= self.screenshotPDF(pages[int(overlapping[0].layout.page_id)-1], tmp_pdf_position)

                        # 计算组合图形中面积最大的图片的索引
                        biggest_area_images_idx = 0
                        biggest_area = float('-inf')

                        for obj,position in zip(overlapping,group_positions):
                            tmp_area = position[2] * position[3]
                            if tmp_area > biggest_area:
                                biggest_area = tmp_area
                                biggest_area_images_idx = overlapping.index(obj)

                        # 新的合成图形的id，name， text, layout, style, coordinate, data_id均继承自面积最大的的图片
                        biggest_area_images_obj = overlapping[biggest_area_images_idx].to_dict()
                        new_graphic_obj =  GraphicObject.from_dict(biggest_area_images_obj)
                        new_graphic_obj.width = str(new_image_width)  # px
                        new_graphic_obj.height = str(new_image_height)  # px
                        new_graphic_obj.data = new_image_binary
                        new_graphic_obj.digest = digest
                        new_graphic_obj.graphic_type = 'combine_graph'
                        new_graphic_obj_position = Position()
                        new_graphic_obj_position.x = new_image_position[0]
                        new_graphic_obj_position.y = new_image_position[1]
                        new_graphic_obj_position.width = new_image_position[2]
                        new_graphic_obj_position.height = new_image_position[3]
                        new_graphic_obj.position = new_graphic_obj_position #%
                        combine_graphics.append(new_graphic_obj)
                    graphics += combine_graphics

                pictures, null_pictures = self._find_null_data(pictures)
                if len(null_pictures) > 0:
                    null_pictures_position =  self._get_graphic_pictures_positions(null_pictures,page_width, page_height)
                    for pict_obj, position in zip(null_pictures, null_pictures_position):
                        if position[2] ==0 or position[3] == 0:
                            continue
                        tmp_pdf_position = self.convert_percentage2pt_via_px(position, page_width,page_height, scale_x, scale_y)
                        digest, _, image_binary = self.screenshotPDF(pages[int(pict_obj.layout.page_id)-1],tmp_pdf_position)
                        pict_obj.digest = digest
                        pict_obj.data = image_binary
                    pictures += null_pictures

                graphics, null_graphics = self._find_null_data(graphics)
                if len(null_graphics) > 0:
                    null_graphics_position = self._get_graphic_pictures_positions(null_graphics, page_width, page_height)
                    for graphic_obj, position in zip(null_graphics, null_graphics_position):
                        if position[2] ==0 or position[3] == 0:
                            continue
                        tmp_pdf_position = self.convert_percentage2pt_via_px(position, page_width, page_height, scale_x, scale_y)
                        digest, _, image_binary = self.screenshotPDF(pages[int(graphic_obj.layout.page_id)-1], tmp_pdf_position)
                        graphic_obj.digest = digest
                        graphic_obj.data = image_binary
                    graphics += null_graphics

            finally:
                # 关闭pdf文件
                if doc:
                    doc.close()
                # 确保清理临时文件
                if temp_dir and os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                # 确保PDF文件已关闭
                if pdf_path and os.path.exists(pdf_path):
                    try:
                        os.remove(pdf_path)
                    except:
                        pass
        return

    def screenshotPDF(self,page, position, zoom=2):
        """
            负责在传入的PDF页面对制定位置进行截图操作

            page:已经打开的pdf页面
            position: [x0,y0,x1,y1] #pdf截图单位默认是point
            zoom: 设置缩放因子（提高截图清晰度）
        """
        import fitz
        mat = fitz.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=mat)  # 将页面渲染为图像
        img = Image.open(io.BytesIO(pix.tobytes()))  # 将图像转换为PIL图像对象

        x0, y0, x1, y1 = [coord * zoom for coord in position]
        cropped_img = img.crop((x0, y0, x1, y1))  # 截图操作

        digest, hash_array = get_img_hash(cropped_img)

        # 获取新图片的data （字符串）值
        b_io = io.BytesIO()  # 创建一个字节缓冲区
        cropped_img.save(b_io, format='PNG')  # 将图像保存到缓冲区中（以指定格式）
        image_binary = b_io.getvalue()  # 提取缓冲区中的二进制数据
        image_binary = b64encode(image_binary).decode("utf-8")  # 将二进制数据转换为 Base64 字符串 #GraphicObject.data

        return digest, hash_array,image_binary

    def _get_PDF_file(self,path):
        """
            path: (临时生成)PDF路径 (在所有程序执行完成之后会删除，不可读取)
            读取并保存pdf所有的页面
        """
        import fitz
        doc = fitz.open(path)
        pages = [doc.load_page(page_num) for page_num in range(len(doc))]

        return doc, pages
    @contextmanager
    def _office_app(self, app_name: str):
        """
            Office应用上下文管理器
            复制自 bibrain 模块，base_convert_service.py文件
        """
        pythoncom.CoInitialize()
        app = None
        try:
            app = win32com.client.DispatchEx(app_name)
            app.Visible = False
            app.DisplayAlerts = False
            yield app
        except Exception as e:
            raise RuntimeError(f"启动 {app_name} 失败: {str(e)}") from e
        finally:
            if app:
                try:
                    app.Quit()
                except:
                    pass
            pythoncom.CoUninitialize()
    def _convert_to_pdf(self,source_path: str, app_name: str, save_method: callable) :
        """
            通用文件转PDF方法
            return: (pdf_path, temp_dir) PDF路径和临时目录
            复制自 bibrain 模块，base_convert_service.py文件
        """
        source_path = os.path.abspath(source_path)
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文件不存在: {source_path}")
        output_dir = tempfile.mkdtemp(prefix="file_converter_")

        pdf_path = os.path.join(output_dir, "temp.pdf")
        try:
            with self._office_app(app_name) as app:
                save_method(app, source_path, pdf_path)

            if not os.path.exists(pdf_path):
                raise RuntimeError("PDF文件未生成")

            return pdf_path, output_dir
        except Exception as e:
            shutil.rmtree(output_dir, ignore_errors=True)
            raise RuntimeError(f"转换为PDF失败: {str(e)}") from e