
from .base_object import BaseObject

class LayoutObject(BaseObject):
    """布局对象"""
    def __init__(self):
        self._page_id = ''  # 页码
        self._parent_content = ''  # 父对象的内容
        self._parent_ref = ''  # 父对象的引用
        self._prev_ref = ''  # 上一个对象的引用
        self._next_ref = ''  # 下一个对象的引用

    def to_dict(self):
        """
        将 LayoutObject 对象转换为字典
        """
        dic = {
            "page_id": self._page_id,
            "parent_content": self._parent_content,
            "prev_ref": self._prev_ref.data_id if hasattr(self._prev_ref, 'data_id') else self._prev_ref,
            "next_ref": self._next_ref.data_id if hasattr(self._next_ref, 'data_id') else self._next_ref
        }
        from .document import DocumentBlockObject
        if hasattr(self._parent_ref, 'data_id'):
            dic["parent_ref"] = self._parent_ref.data_id
        elif isinstance(self._parent_ref, DocumentBlockObject):
            dic["parent_ref"] = ''
        else:
            dic["parent_ref"] = self._parent_ref
        return dic

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 LayoutObject 实例
        """
        obj = cls()
        obj._page_id = data.get("page_id", "")
        obj._parent_content = data.get("parent_content", "")
        obj._parent_ref = data.get("parent_ref", "")
        obj._prev_ref = data.get("prev_ref", "")
        obj._next_ref = data.get("next_ref", "")
        return obj

    @property
    def page_id(self):
        return self._page_id

    @page_id.setter
    def page_id(self, new_value):
        assert type(new_value) == str
        self._page_id = new_value

    @property
    def parent_ref(self):
        return self._parent_ref

    @parent_ref.setter
    def parent_ref(self, new_value):
        self._parent_ref = new_value

    @property
    def parent_content(self):
        return self._parent_content

    @parent_content.setter
    def parent_content(self, new_value):
        assert type(new_value) == str
        self._parent_content = new_value

    @property
    def prev_ref(self):
        return self._prev_ref

    @prev_ref.setter
    def prev_ref(self, new_value):
        self._prev_ref = new_value

    @property
    def next_ref(self):
        return self._next_ref

    @next_ref.setter
    def next_ref(self, new_value):
        self._next_ref = new_value

    @classmethod
    def new_with_parent_ref(cls, parent_ref) -> 'LayoutObject':
        obj = cls()
        obj._parent_ref = parent_ref
        return obj
