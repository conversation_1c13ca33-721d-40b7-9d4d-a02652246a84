#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : rule_test_view.py
@Time    : 2025/7/28 16:15
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 根据大量业务规则、匹配、生成对应的测试大观点、小观点及测试用例
"""
import json
import yaml
import os
from collections import defaultdict

from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.service.test_view_generate.config import *
from sdw_agent.util.local_file_read_save import read_json, save_json
from sdw_agent.service.test_view_generate.model import MapDRModel, FindFunctionModel
from sdw_agent.service.test_view_generate.utils.llm_request import common_llm
from sdw_agent.service.test_view_generate.utils.sim_tool import (_tokenize,
                                                                 filter_map_case,
                                                                 calculate_similarity_scores)


input_path = ENV.config.input_data_path


def read_base_data():
    # 读取规则文件及全集测试用例文件
    config = yaml.load(open(os.path.join(ROOT_DIR, "config.yaml"), "r", encoding="utf-8"),
                       Loader=yaml.FullLoader)
    case_data_name = config["test_agent_input_table"]["testcases"]["name"]
    rule_data_name = config["test_agent_input_table"]["rules"]["name"]
    middle_path = os.path.join(input_path, SUB_OUTPUT)
    case_path = os.path.join(middle_path, case_data_name)
    rule_path = os.path.join(middle_path, rule_data_name)
    case_data = read_json(case_path)
    rule_data = read_json(rule_path)
    return middle_path, case_path, rule_path, case_data, rule_data


class NewTestView:
    @staticmethod
    def generate(api_change_point):
        """生成测试观点及匹配测试用例"""
        try:
            logger.info(f"开始进行测试观点匹配及测试用例匹配")
            # 读取基础的依赖文件，包括中间结果文件的存储名及文件夹路径
            logger.info("加载规则文件及全集测试用例文件")
            middle_path, case_path, rule_path, case_data, rule_data = read_base_data()
            logger.info("加载规则文件及全集测试用例文件结束")

            # 保存选中的变更点
            save_json(os.path.join(middle_path, CHOOSE_CHANGE_POINT), api_change_point)
            # 对规则文件进行格式处理
            map_keywords, map_dr = NewTestView.get_view_type(rule_data)

            # 匹配测试大观点及DR观点
            map_dr_res = NewTestView.map_dr_view(api_change_point, map_keywords, map_dr)
            # 对匹配到的大观点进行整合
            merge_res = NewTestView.merge_dr_view(map_dr_res, OUTPUT_STRUCTURE)
            # 如果没有匹配到大观点，则输出预设结果（只有基础输出模板内容）
            if merge_res:
                # 输出要求：一共七类DR观点，merge_res结果中没有出现的DR名，也需要补充到merge_res中
                add_merge_res = NewTestView.add_no_dr(merge_res)
                # 获取变更点中的功能名
                function_res = NewTestView.save_function_point(api_change_point, case_data)
                # 关键词匹配功能名对应的测试用例
                test_case = NewTestView.map_case(function_res.get("function_point", []), case_data)
                # 匹配测试用例与测试观点
                vew_2_case_res = NewTestView.view_to_case(add_merge_res, test_case)
            else:
                vew_2_case_res = STANDARD_DR_MODULE

            # 保存生成的中间结果
            save_json(os.path.join(middle_path, RESULT_JSON), vew_2_case_res)
            # 按照输出结构定义整合输出结果
            map_res = NewTestView.fomat_result(vew_2_case_res, {"rule": rule_path})
            return True, map_res
        except Exception as e:
            logger.error(f"测试观点匹配及测试用例匹配失败，失败原因为：{str(e)}")
            return False, {}

    @staticmethod
    def add_no_dr(now_dr_res):
        """将七类DR观点中、未出现在现有dr结果的类型进行补充"""
        try:
            logger.info(f"对现有的测试大观点结果进行补充，现有结果为：{now_dr_res}")
            add_dr = list()
            # 标准的七类DR名
            stand_name = STANDARD_DR_NAME.values()
            # 实际的DR名
            now_name = list()
            for dr_ in now_dr_res:
                key_ = dr_.get("testview_class")
                stand_key = STANDARD_DR_NAME.get(key_)
                if not stand_key:
                    continue
                dr_["testview_class"] = stand_key
                now_name.append(stand_key)
            add_list = list()
            for one_stand in stand_name:
                if one_stand not in now_name:
                    copy_model = deepcopy(OUTPUT_STRUCTURE)
                    copy_model["testview_class"] = one_stand
                    add_list.append(copy_model)
            now_dr_res.extend(add_list)
            logger.info(f"对现有的测试大观点结果补充结束，结果为：{now_dr_res}")
            return now_dr_res
        except Exception as e:
            logger.error(f"对现有的测试大观点结果补充失败，失败原因为：{str(e)}")
            return now_dr_res

    @staticmethod
    def get_view_type(input_data):
        type_keywords = defaultdict(list)
        type_dr = defaultdict(list)
        for one_res in input_data:
            type_ = one_res.get('类型')
            keys_ = one_res.get('关键字')
            if keys_:
                type_keywords[type_].append(keys_)
            type_dr[type_].append(one_res)
        return type_keywords, type_dr

    @staticmethod
    def save_function_point(api_change_points, case_list):
        """
        根据更改点生成功能点并保存
        """
        try:
            logger.info(f"开始从变更点中抽取功能点")
            # 整理候选功能名
            function_list = list()
            for one_case in case_list:
                fun_name = one_case.get('模块名称', "").replace('\n', '')
                function_list.append(fun_name)
            function_list = list(set(function_list))
            # 拼接user的输入
            copy_prompt = deepcopy(FUNCTION_FIND_USER)
            message_input = copy_prompt.replace("{{input}}", str(api_change_points))
            message_input = message_input.replace("{{function}}", str(function_list))
            score_result = common_llm(FUNCTION_FIND_SYSTEM, message_input, FindFunctionModel)
            # 将大模型输出结果转字典
            function_point = score_result.model_dump()
            save_path = os.path.join(input_path, SUB_OUTPUT, FUNCTION_POINT)
            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(function_point, f, ensure_ascii=False, indent=4)
            logger.info(f"从变更点中抽取功能点成功，抽取结果为：{function_point}")
            return function_point
        except Exception as e:
            logger.error(f"从变更点中抽取功能点失败，失败原因为：{str(e)}")
            return {}

    @staticmethod
    def view_to_case(test_view_list, choose_case_list):
        """将测试观点与测试用例进行匹配"""
        try:
            logger.info(f"开始为测试观点匹配测试用例")
            for one_view in test_view_list:
                content_list = one_view.get('testview_content_list')
                # 遍历每个小观点
                for one_content in content_list:
                    cont_key = one_content.get('testview_content_key')
                    # 将测试大观点关键字与测试用例进行匹配计算
                    if not cont_key:
                        continue
                    cont_map = calculate_similarity_scores([cont_key],
                                                           choose_case_list, VIEW_MAP_CASE_ALPHA)
                    # 专门处理易混淆的信息，待丰富规则及封装专门的处理模块
                    if "长按" in cont_key or "长压" in cont_key:
                        cont_map = filter_map_case(cont_map, "长")
                    if "短按" in cont_key or "短压" in cont_key:
                        cont_map = filter_map_case(cont_map, "短")
                    one_content["test_case_list"] = cont_map
            logger.info(f"测试观点匹配测试用例成功")
        except Exception as e:
            logger.error(f"测试观点匹配测试用例失败，失败原因为：{str(e)}")
        return test_view_list

    @staticmethod
    def map_case(function_list, case_list):
        """使用功能点名称从全量测试用例中匹配符合的用例"""
        try:
            logger.info(f"开始使用变更点中抽取的功能点匹配测试用例")
            # 读取全量测试数据
            case_dict = defaultdict(list)
            for one_case in case_list:
                sheet_name = one_case.get('sheet_name')
                fun_name = one_case.get('模块名称', "").replace('\n', '')
                case_dict['-'.join([sheet_name, fun_name])].append(one_case)

            # 遍历功能点列表，获取case
            map_case_list = list()
            for one_func in function_list:
                target_name = ""
                for module_name in case_dict.keys():
                    one_func = one_func.split('【')[0]
                    module_name_split = module_name.split('【')[0]
                    map_ = _tokenize(one_func) & _tokenize(module_name_split)
                    if map_:
                        target_name = module_name
                if target_name:
                    logger.info(f"从全量测试用例中匹配到的功能名为：{target_name}")
                    map_case_list.extend(case_dict.get(target_name))
            logger.info(f"使用变更点中抽取的功能点匹配测试用例成功")
            return map_case_list
        except Exception as e:
            logger.error(f"使用变更点中抽取的功能点匹配测试用例失败，失败原因为：{str(e)}")
            return []

    @staticmethod
    def map_dr_view(one_change, view_keywords, view_dr):
        """依据已有信息，进行测试大观点匹配"""
        map_res = list()
        try:
            logger.info(f"开始进行变更点类型的匹配")
            # 整理变更点相关信息
            epic_name = one_change.get('エピック名')
            content_str = one_change.get('概要')
            epic_name = epic_name.replace('\n', '')
            content_str = content_str.replace('\n', '')
            input_str = f"变更点的エピック名为：{epic_name}，概要为：{content_str}"
            type_score = dict()
            type_reason = dict()
            # 整理规则类型相关信息，包括类型名与关键字，并依次与变更点信息做相似计算
            for one_key, key_words in view_keywords.items():
                type_str = f"变更类型为：{one_key}，属于该变更类型的关键字列表为：{key_words}"
                copy_prompt = deepcopy(DR_MAP_USER)
                message_input = copy_prompt.replace("{{input}}", str(input_str))
                message_input = message_input.replace("{{label}}", str(type_str))
                # 请求LLM，获取相似计算得分及原因
                score_result = common_llm(DR_MAP_SYSTEM, message_input, MapDRModel)
                score_res = score_result.score
                reason_res = score_result.reason
                type_score[one_key] = score_res
                type_reason[one_key] = reason_res
            # 对相似计算得分排序，并取top 1且top 1得分高于0.6作为匹配类型的结果
            sort_list = sorted(type_score.keys(), key=lambda k: type_score[k], reverse=True)
            top_type = sort_list[0]
            if type_score[top_type] >= TYPE_ALPHA:
                map_res = view_dr.get(top_type)
            # 对匹配到的类型进行保存
            one_change["匹配到的类型"] = top_type
            one_change["匹配的原因"] = type_reason[top_type]
            save_json(os.path.join(input_path, SUB_OUTPUT, TEST_VIEW_TYPE), one_change)
            logger.info(f"变更点类型的匹配结束，匹配类型为：{top_type}，匹配原因为：{type_reason[top_type]}")
        except Exception as e:
            logger.error(f"变更点类型的匹配失败，失败原因为：{str(e)}")
        return map_res

    @staticmethod
    def merge_dr_view(map_dr, structure):
        """对匹配到的测试大观点及DR信息进行整合"""
        try:
            logger.info("开始按照指定格式整理匹配到的测试大观点结果")
            merge_res = defaultdict(list)
            for one_map in map_dr:
                dr_ = one_map.get('DR', '').split('/')
                view_ = one_map.get('测试大观点')
                view_key = one_map.get('大观点关键字', '')
                # 没有匹配到DR观点及测试大观点
                if not dr_ or not view_:
                    continue
                for one_dr in dr_:
                    merge_res[one_dr].append([view_, view_key])
            default_list = list()
            for k_, v_ in merge_res.items():
                copy_st = deepcopy(structure)
                copy_st["testview_class"] = k_ if k_ else "其他"
                copy_st["testview_item"] = '\n'.join([str(inx_+1)+'. '+_[0].replace('\n', '') for inx_, _ in enumerate(v_)])
                copy_st["source"] = "rule"
                for inx_, inx_v in enumerate(v_):
                    one_content = {"testview_content_item": str(inx_+1)+'. '+inx_v[0].replace('\n', ''),
                                   "test_case_list": [],
                                   "testview_content_key": inx_v[-1]}
                    copy_st["testview_content_list"].append(one_content)
                default_list.append(copy_st)
            logger.info(f"按照指定格式整理匹配到的测试大观点结果结束，结果为：{default_list}")
            return default_list
        except Exception as e:
            logger.error(f"按照指定格式整理匹配到的测试大观点结果失败，失败原因为：{str(e)}")
            return []

    @staticmethod
    def fomat_result(class_res, path_dict):
        result_list = []
        for i, item in enumerate(class_res):
            tmp = {
                "id": i + 1,
                "category": item.get("testview_class", ""),
                "checkpoint": item.get("testview_item", ""),
                "mse_category": "",
                "mse_content": "",
                "procedure": '\n'.join([i.get("testview_content_item", "")
                                        for i in item.get("testview_content_list", [])]),
                "source": path_dict.get(item['source']),
                "test_case": "",
                "is_use": False
            }
            result_list.append(tmp)
        return {"list": result_list}


if __name__ == '__main__':
    input_data = {
        "change_points": {
  "対応イベント": "R2大",
  "ARチケットNO": "MET19PFV3-21517",
  "ARチケットタイトル": "【顧客要求_変更】MET-G_CSTMLST-CSTD-A0-04-A-C0",
  "エピック名": "MET-G_CSTMLST-CSTD_SoC R_警報音量の階層修正",
  "概要": "警報音量の階層修正"
}
    }
    _, res = NewTestView.generate(input_data['change_points'])
    print('ok')
