"""
对象选择工具

本模块提供了两种对象匹配方法：

1. 基于大模型的匹配（语义理解）:
   - select_one: 从候选对象列表中选择与标准对象最匹配的一个
   - select_many: 从候选对象列表中选择与标准对象最匹配的多个，并按相关性排序

2. 基于Embedding的匹配（向量相似度）:
   - select_one_by_embedding: 使用embedding向量相似度选择最匹配的一个对象
   - select_many_by_embedding: 使用embedding向量相似度选择多个匹配对象

这些函数适用于复杂对象的语义匹配场景，可以根据需要选择不同的匹配方法。
"""

import json
from typing import Any, List, Optional, Union, TypeVar, Tuple

import numpy as np
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

from sdw_agent.llm import embeddings
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output

T = TypeVar('T')


def _object_to_string(obj: Any) -> str:
    """
    将对象转换为字符串表示

    Args:
        obj: 要转换的对象

    Returns:
        字符串表示
    """
    if isinstance(obj, str):
        # 如果是字符串，直接返回
        return obj
    else:
        # 如果不是字符串，使用JSON序列化
        try:
            return json.dumps(obj, ensure_ascii=False, default=str, sort_keys=True)
        except Exception as e:
            # 如果JSON序列化失败，使用str()转换
            return str(obj)


class MatchResult(BaseModel):
    """匹配结果模型"""
    index: int = Field(..., description="候选对象在列表中的索引")
    score: float = Field(..., description="匹配分数，范围0-100，越高表示匹配度越高")
    reason: str = Field(..., description="匹配理由，解释为什么这个候选对象与标准对象匹配")


class MatchResultList(BaseModel):
    """匹配结果列表模型"""
    results: List[MatchResult] = Field(..., description="匹配结果列表")


def _find_exact_matches(standard: Any, candidates: List[Any]) -> List[int]:
    """
    查找与标准对象完全一致的候选对象索引

    Args:
        standard: 标准对象
        candidates: 候选对象列表

    Returns:
        完全匹配的候选对象索引列表
    """
    exact_matches = []

    # 将标准对象转换为字符串
    try:
        standard_str = _object_to_string(standard)
    except Exception:
        return exact_matches

    # 检查每个候选对象是否与标准对象完全一致
    for i, candidate in enumerate(candidates):
        try:
            candidate_str = _object_to_string(candidate)
            if standard_str == candidate_str:
                exact_matches.append(i)
        except Exception:
            continue

    return exact_matches


def select_one(standard: Any, candidates: List[Any], **kwargs) -> Optional[Any]:
    """
    从候选对象列表中选择与标准对象最匹配的一个

    Args:
        standard: 标准对象，用于比较的基准（支持字符串或任何可JSON序列化的对象）
        candidates: 候选对象列表，从中选择最匹配的对象（支持字符串或任何可JSON序列化的对象）
        **kwargs: 额外参数，可包含:
            - model: 自定义LangChain模型对象
            - detailed: 是否返回详细结果，包括匹配分数和理由 (默认: False)
            - threshold: 匹配阈值，低于此分数的匹配将被忽略 (默认: 50)

    Returns:
        如果detailed=False (默认)，返回最匹配的候选对象；
        如果detailed=True，返回元组 (最匹配的候选对象, 分数, 理由)；
        如果没有找到匹配项或匹配分数低于阈值，返回None

    Note:
        - 字符串对象会直接使用，无需JSON转换
        - 其他对象会自动转换为JSON字符串进行比较
        - 如果存在完全一致的对象，将直接返回而不调用大模型
    """
    # 提取额外参数
    model = kwargs.get('model', None)
    detailed = kwargs.get('detailed', False)
    threshold = kwargs.get('threshold', 50)

    # 验证输入
    if not candidates:
        return None

    if len(candidates) == 1:
        # 如果只有一个候选对象，直接返回
        if detailed:
            return (candidates[0], 100, "唯一候选对象")
        return candidates[0]

    # 首先检查是否有完全匹配的对象
    exact_matches = _find_exact_matches(standard, candidates)
    if exact_matches:
        # 如果有完全匹配的对象，直接返回第一个
        best_index = exact_matches[0]
        if detailed:
            return (candidates[best_index], 100, "完全匹配")
        return candidates[best_index]

    # 将对象转换为字符串以便大模型处理
    try:
        standard_str = _object_to_string(standard)
        candidates_str = [_object_to_string(c) for c in candidates]
    except Exception as e:
        raise ValueError(f"无法将对象转换为字符串: {e}")

    # 构建提示模板
    template = ChatPromptTemplate(
        [
            ("system", """
你是一个精确的对象匹配专家。你的任务是从候选对象列表中找出与标准对象最匹配的一个。
请仔细分析标准对象和每个候选对象的属性和结构，考虑它们之间的相似度和关联性。
对每个候选对象进行评分（0-100分），并提供详细的匹配理由。

评分标准：
- 100分：完全匹配，所有重要属性都一致
- 80-99分：高度匹配，核心属性一致，次要属性可能有差异
- 60-79分：中度匹配，部分重要属性一致
- 40-59分：低度匹配，少数属性一致
- 0-39分：几乎不匹配

请返回分数最高的候选对象的索引、分数和匹配理由。
            """),
            ("user", """
标准对象:
{{standard}}

候选对象列表:
{{candidates}}

请分析每个候选对象与标准对象的匹配程度，并选出最匹配的一个。
            """)
        ],
        template_format="mustache"
    )

    # 准备输入数据
    invoke_data = {
        "standard": standard_str,
        "candidates": "\n\n".join([f"候选对象 {i}:\n{c}" for i, c in enumerate(candidates_str)])
    }

    # 调用大模型进行匹配
    try:
        resp: MatchResultList = get_ai_message_with_structured_output(
            template,
            invoke_data,
            MatchResultList,
            llm_model=model
        )

        # 找出分数最高的结果
        if not resp.results:
            return None

        best_match = max(resp.results, key=lambda x: x.score)

        # 检查是否达到阈值
        if best_match.score < threshold:
            return None

        # 返回结果
        if detailed:
            return (candidates[best_match.index], best_match.score, best_match.reason)
        return candidates[best_match.index]

    except Exception as e:
        raise RuntimeError(f"对象匹配失败: {e}")


def select_many(standard: Any, candidates: List[Any], count: Optional[int] = None, **kwargs) -> List[
    Union[Any, Tuple[Any, float, str]]]:
    """
    从候选对象列表中选择与标准对象最匹配的多个，并按相关性排序

    Args:
        standard: 标准对象，用于比较的基准（支持字符串或任何可JSON序列化的对象）
        candidates: 候选对象列表，从中选择匹配的对象（支持字符串或任何可JSON序列化的对象）
        count: 要返回的匹配对象数量，如果为None则返回所有匹配对象
        **kwargs: 额外参数，可包含:
            - model: 自定义LangChain模型对象
            - detailed: 是否返回详细结果，包括匹配分数和理由 (默认: False)
            - threshold: 匹配阈值，低于此分数的匹配将被忽略 (默认: 50)

    Returns:
        如果detailed=False (默认)，返回匹配的候选对象列表；
        如果detailed=True，返回元组列表 [(候选对象, 分数, 理由), ...]；
        列表按匹配分数从高到低排序

    Note:
        - 字符串对象会直接使用，无需JSON转换
        - 其他对象会自动转换为JSON字符串进行比较
        - 如果存在完全一致的对象且满足数量要求，将直接返回而不调用大模型
    """
    # 提取额外参数
    model = kwargs.get('model', None)
    detailed = kwargs.get('detailed', False)
    threshold = kwargs.get('threshold', 50)

    # 验证输入
    if not candidates:
        return []

    if len(candidates) == 1:
        # 如果只有一个候选对象，直接返回
        if detailed:
            return [(candidates[0], 100, "唯一候选对象")]
        return [candidates[0]]

    # 首先检查是否有完全匹配的对象
    exact_matches = _find_exact_matches(standard, candidates)
    if exact_matches:
        # 如果有完全匹配的对象，检查是否满足数量要求
        exact_count = len(exact_matches)
        if count is None or exact_count >= count:
            # 如果完全匹配的数量满足要求，直接返回
            result_indices = exact_matches[:count] if count is not None else exact_matches
            if detailed:
                return [(candidates[i], 100, "完全匹配") for i in result_indices]
            return [candidates[i] for i in result_indices]

        # 如果完全匹配的数量不够，先收集完全匹配的结果，然后继续用大模型匹配剩余的
        exact_results = []
        if detailed:
            exact_results = [(candidates[i], 100, "完全匹配") for i in exact_matches]
        else:
            exact_results = [candidates[i] for i in exact_matches]

        # 从候选列表中移除已经完全匹配的对象，继续匹配剩余的
        remaining_candidates = [candidates[i] for i in range(len(candidates)) if i not in exact_matches]
        remaining_count = count - exact_count if count is not None else None

        if remaining_candidates and remaining_count != 0:
            # 递归调用处理剩余的候选对象
            remaining_results = select_many(standard, remaining_candidates, remaining_count, **kwargs)
            # 合并结果
            if detailed:
                return exact_results + remaining_results
            else:
                return exact_results + remaining_results
        else:
            return exact_results

    # 将对象转换为字符串以便大模型处理
    try:
        standard_str = _object_to_string(standard)
        candidates_str = [_object_to_string(c) for c in candidates]
    except Exception as e:
        raise ValueError(f"无法将对象转换为字符串: {e}")

    # 构建提示模板
    template = ChatPromptTemplate(
        [
            ("system", """
你是一个精确的对象匹配专家。你的任务是从候选对象列表中找出与标准对象匹配的所有对象，并按匹配程度排序。
请仔细分析标准对象和每个候选对象的属性和结构，考虑它们之间的相似度和关联性。
对每个候选对象进行评分（0-100分），并提供详细的匹配理由。

评分标准：
- 100分：完全匹配，所有重要属性都一致
- 80-99分：高度匹配，核心属性一致，次要属性可能有差异
- 60-79分：中度匹配，部分重要属性一致
- 40-59分：低度匹配，少数属性一致
- 0-39分：几乎不匹配

请返回所有候选对象的索引、分数和匹配理由，按分数从高到低排序。
            """),
            ("user", """
标准对象:
{{standard}}

候选对象列表:
{{candidates}}

请分析每个候选对象与标准对象的匹配程度，并按匹配度从高到低排序。
            """)
        ],
        template_format="mustache"
    )

    # 准备输入数据
    invoke_data = {
        "standard": standard_str,
        "candidates": "\n\n".join([f"候选对象 {i}:\n{c}" for i, c in enumerate(candidates_str)])
    }

    # 调用大模型进行匹配
    try:
        resp: MatchResultList = get_ai_message_with_structured_output(
            template,
            invoke_data,
            MatchResultList,
            llm_model=model
        )

        # 过滤并排序结果
        if not resp.results:
            return []

        # 按分数从高到低排序
        sorted_results = sorted(resp.results, key=lambda x: x.score, reverse=True)

        # 过滤低于阈值的结果
        filtered_results = [r for r in sorted_results if r.score >= threshold]

        # 限制返回数量
        if count is not None and count > 0:
            filtered_results = filtered_results[:count]

        # 准备返回结果
        if detailed:
            return [(candidates[r.index], r.score, r.reason) for r in filtered_results]
        return [candidates[r.index] for r in filtered_results]

    except Exception as e:
        raise RuntimeError(f"对象匹配失败: {e}")


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    计算两个向量的余弦相似度

    Args:
        vec1: 第一个向量
        vec2: 第二个向量

    Returns:
        余弦相似度值，范围[-1, 1]，值越大表示越相似
    """
    # 转换为numpy数组
    a = np.array(vec1)
    b = np.array(vec2)

    # 计算余弦相似度
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)

    if norm_a == 0 or norm_b == 0:
        return 0.0

    return dot_product / (norm_a * norm_b)


def select_one_by_embedding(standard: Any, candidates: List[Any], **kwargs) -> Optional[Any]:
    """
    使用embedding向量相似度从候选对象列表中选择与标准对象最匹配的一个

    Args:
        standard: 标准对象，用于比较的基准（支持字符串或任何可JSON序列化的对象）
        candidates: 候选对象列表，从中选择最匹配的对象（支持字符串或任何可JSON序列化的对象）
        **kwargs: 额外参数，可包含:
            - detailed: 是否返回详细结果，包括相似度分数 (默认: False)
            - threshold: 相似度阈值，低于此分数的匹配将被忽略 (默认: 0.5)
            - embedding_model: 自定义embedding模型 (默认: 使用全局embeddings)

    Returns:
        如果detailed=False (默认)，返回最匹配的候选对象；
        如果detailed=True，返回元组 (最匹配的候选对象, 相似度分数)；
        如果没有找到匹配项或相似度低于阈值，返回None

    Note:
        - 字符串对象会直接使用，无需JSON转换
        - 其他对象会自动转换为JSON字符串进行向量化
        - 如果存在完全一致的对象，将直接返回而不计算embedding
    """
    # 提取额外参数
    detailed = kwargs.get('detailed', False)
    threshold = kwargs.get('threshold', 0.5)
    embedding_model = kwargs.get('embedding_model', embeddings)

    # 验证输入
    if not candidates:
        return None

    if len(candidates) == 1:
        # 如果只有一个候选对象，直接返回
        if detailed:
            return (candidates[0], 1.0)
        return candidates[0]

    # 首先检查是否有完全匹配的对象
    exact_matches = _find_exact_matches(standard, candidates)
    if exact_matches:
        # 如果有完全匹配的对象，直接返回第一个
        best_index = exact_matches[0]
        if detailed:
            return (candidates[best_index], 1.0)
        return candidates[best_index]

    # 将对象转换为字符串
    try:
        standard_str = _object_to_string(standard)
        candidates_str = [_object_to_string(c) for c in candidates]
    except Exception as e:
        raise ValueError(f"无法将对象转换为字符串: {e}")

    try:
        # 获取标准对象的embedding
        standard_embedding = embedding_model.embed_query(standard_str)

        # 获取所有候选对象的embedding
        candidates_embeddings = embedding_model.embed_documents(candidates_str)

        # 计算相似度
        similarities = []
        for i, candidate_embedding in enumerate(candidates_embeddings):
            similarity = cosine_similarity(standard_embedding, candidate_embedding)
            similarities.append((i, similarity))

        # 找出相似度最高的候选对象
        best_match = max(similarities, key=lambda x: x[1])
        best_index, best_similarity = best_match

        # 检查是否达到阈值
        if best_similarity < threshold:
            return None

        # 返回结果
        if detailed:
            return (candidates[best_index], best_similarity)
        return candidates[best_index]

    except Exception as e:
        raise RuntimeError(f"Embedding匹配失败: {e}")


def select_many_by_embedding(standard: Any, candidates: List[Any], count: Optional[int] = None, **kwargs) -> List[
    Union[Any, Tuple[Any, float]]]:
    """
    使用embedding向量相似度从候选对象列表中选择与标准对象最匹配的多个，并按相似度排序

    Args:
        standard: 标准对象，用于比较的基准（支持字符串或任何可JSON序列化的对象）
        candidates: 候选对象列表，从中选择匹配的对象（支持字符串或任何可JSON序列化的对象）
        count: 要返回的匹配对象数量，如果为None则返回所有匹配对象
        **kwargs: 额外参数，可包含:
            - detailed: 是否返回详细结果，包括相似度分数 (默认: False)
            - threshold: 相似度阈值，低于此分数的匹配将被忽略 (默认: 0.5)
            - embedding_model: 自定义embedding模型 (默认: 使用全局embeddings)

    Returns:
        如果detailed=False (默认)，返回匹配的候选对象列表；
        如果detailed=True，返回元组列表 [(候选对象, 相似度分数), ...]；
        列表按相似度从高到低排序

    Note:
        - 字符串对象会直接使用，无需JSON转换
        - 其他对象会自动转换为JSON字符串进行向量化
        - 如果存在完全一致的对象且满足数量要求，将直接返回而不计算embedding
    """
    # 提取额外参数
    detailed = kwargs.get('detailed', False)
    threshold = kwargs.get('threshold', 0.5)
    embedding_model = kwargs.get('embedding_model', embeddings)

    # 验证输入
    if not candidates:
        return []

    if len(candidates) == 1:
        # 如果只有一个候选对象，直接返回
        if detailed:
            return [(candidates[0], 1.0)]
        return [candidates[0]]

    # 首先检查是否有完全匹配的对象
    exact_matches = _find_exact_matches(standard, candidates)
    if exact_matches:
        # 如果有完全匹配的对象，检查是否满足数量要求
        exact_count = len(exact_matches)
        if count is None or exact_count >= count:
            # 如果完全匹配的数量满足要求，直接返回
            result_indices = exact_matches[:count] if count is not None else exact_matches
            if detailed:
                return [(candidates[i], 1.0) for i in result_indices]
            return [candidates[i] for i in result_indices]

        # 如果完全匹配的数量不够，先收集完全匹配的结果，然后继续用embedding匹配剩余的
        exact_results = []
        if detailed:
            exact_results = [(candidates[i], 1.0) for i in exact_matches]
        else:
            exact_results = [candidates[i] for i in exact_matches]

        # 从候选列表中移除已经完全匹配的对象，继续匹配剩余的
        remaining_candidates = [candidates[i] for i in range(len(candidates)) if i not in exact_matches]
        remaining_count = count - exact_count if count is not None else None

        if remaining_candidates and remaining_count != 0:
            # 递归调用处理剩余的候选对象
            remaining_results = select_many_by_embedding(standard, remaining_candidates, remaining_count, **kwargs)
            # 合并结果
            if detailed:
                return exact_results + remaining_results
            else:
                return exact_results + remaining_results
        else:
            return exact_results

    # 将对象转换为字符串
    try:
        standard_str = _object_to_string(standard)
        candidates_str = [_object_to_string(c) for c in candidates]
    except Exception as e:
        raise ValueError(f"无法将对象转换为字符串: {e}")

    try:
        # 获取标准对象的embedding
        standard_embedding = embedding_model.embed_query(standard_str)

        # 获取所有候选对象的embedding
        candidates_embeddings = embedding_model.embed_documents(candidates_str)

        # 计算相似度
        similarities = []
        for i, candidate_embedding in enumerate(candidates_embeddings):
            similarity = cosine_similarity(standard_embedding, candidate_embedding)
            similarities.append((i, similarity))

        # 按相似度从高到低排序
        sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)

        # 过滤低于阈值的结果
        filtered_similarities = [(i, sim) for i, sim in sorted_similarities if sim >= threshold]

        # 限制返回数量
        if count is not None and count > 0:
            filtered_similarities = filtered_similarities[:count]

        # 准备返回结果
        if detailed:
            return [(candidates[i], similarity) for i, similarity in filtered_similarities]
        return [candidates[i] for i, _ in filtered_similarities]

    except Exception as e:
        raise RuntimeError(f"Embedding匹配失败: {e}")


# 示例用法
if __name__ == "__main__":
    # 示例标准对象
    standard_obj = {
        "name": "智能手机",
        "features": ["高清屏幕", "长续航", "快速充电", "高性能处理器"],
        "price_range": "中高端"
    }

    # 示例候选对象列表
    candidates_list = [
        {
            "name": "手机A",
            "features": ["普通屏幕", "一般续航", "标准充电", "中等处理器"],
            "price_range": "中端"
        },
        {
            "name": "手机B",
            "features": ["高清屏幕", "超长续航", "快速充电", "高性能处理器"],
            "price_range": "高端"
        },
        {
            "name": "手机C",
            "features": ["超高清屏幕", "中等续航", "快速充电", "中等处理器"],
            "price_range": "中高端"
        }
    ]

    # 测试select_one
    best_match = select_one(standard_obj, candidates_list, detailed=True)
    if best_match:
        obj, score, reason = best_match
        print(f"最佳匹配: {obj['name']}, 分数: {score}, 理由: {reason}")

    # 测试select_many
    matches = select_many(standard_obj, candidates_list, count=2, detailed=True)
    print("\n按匹配度排序的结果:")
    for obj, score, reason in matches:
        print(f"匹配: {obj['name']}, 分数: {score}, 理由: {reason}")
