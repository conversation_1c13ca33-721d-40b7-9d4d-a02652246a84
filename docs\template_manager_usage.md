# 模板文件管理器使用说明

## 概述

模板文件管理器是一个用于管理各个工作流模板文件的系统，支持自定义模板文件的配置和使用。系统会优先使用用户配置的自定义模板文件，如果没有配置则使用内置的默认模板文件。

## 主要功能

1. **模板文件配置管理** - 支持为不同工作流配置自定义模板文件或重置为内置模板
2. **自动模板选择** - 优先使用自定义模板，否则使用内置模板
3. **自动文件复制** - 设置自定义模板后自动复制到输入目录
4. **配置持久化** - 模板配置保存在用户配置目录中

## API接口

### 1. 获取模板文件列表

```http
GET /api/sdw/template/list
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "获取模板列表成功",
  "data": {
    "templates": {
      "design_cs_workflow": {
        "name": "设计基准Check Sheet模板",
        "description": "设计基准工作流使用的Excel模板文件",
        "file_type": "xlsm",
        "has_custom_template": false,
        "custom_template_path": null,
        "builtin_template_exists": true
      }
    },
    "total_count": 1
  }
}
```

### 2. 设置或重置模板文件

```http
POST /api/sdw/template/set_custom
```

**设置自定义模板请求参数：**

```json
{
  "template_file_name": "design_cs_file",
  "template_path": "C:/templates/my_custom_template.xlsm"
}
```

**设置自定义模板响应示例：**

```json
{
  "code": 0,
  "msg": "设置自定义模板成功，并已复制到输入目录",
  "data": {
    "template_file_name": "design_cs_file",
    "template_path": "C:/templates/my_custom_template.xlsm",
    "copied_to_input_dir": "C:/sdw_input/my_custom_template_20250728_163000.xlsm"
  }
}
```

**重置为内置模板请求参数：**

```json
{
  "template_file_name": "design_cs_file",
  "template_path": null
}
```

**重置为内置模板响应示例：**

```json
{
  "code": 0,
  "msg": "重置为内置模板成功",
  "data": {
    "template_file_name": "design_cs_file",
    "template_path": null,
    "current_template": "src/sdw_agent/service/design_cs_workflow/ソフトウェア 設計基準CS.xlsm"
  }
}
```

### 3. 获取指定模板文件的信息

```http
GET /api/sdw/template/info/{template_file_name}
```

**响应示例：**

```json
{
  "code": 0,
  "msg": "获取模板信息成功",
  "data": {
    "template_file_name": "design_cs_file",
    "current_template_path": "C:/templates/my_custom_template.xlsm",
    "template_info": {
      "name": "设计基准Check Sheet模板",
      "description": "设计基准工作流使用的Excel模板文件",
      "file_type": "xlsm",
      "has_custom_template": true,
      "custom_template_path": "C:/templates/my_custom_template.xlsm",
      "builtin_template_exists": true
    }
  }
}
```

## 支持的模板文件

当前系统支持以下模板文件的管理：

1. **design_cs_file** - 设计基准Check Sheet模板
2. **new_reg_table_file** - 新规变化表模板
3. **communication_cs_file** - 通信故障安全CS模板
4. **requirement_cs_file** - 要件CS模板
5. **function_book_file** - 函数分析书模板

## 配置文件

### 内置模板配置

内置模板配置保存在项目中的 `src/sdw_agent/config/template_file.yaml`，包含所有模板的基础信息：

```yaml
version: 1.0.0
templates:
  design_cs_file:
    name: "设计基准Check Sheet模板"
    description: "设计基准工作流使用的Excel模板文件"
    builtin_template: "src/sdw_agent/service/design_cs_workflow/ソフトウェア 設計基準CS.xlsm"
    custom_template: null
    file_type: "xlsm"
```

### 用户自定义模板配置

用户设置的自定义模板路径保存在 `~/.sdw/config/custom_templates.yaml` 中：

```yaml
version: 1.0.0
custom_templates:
  design_cs_file: "C:\\sdw_input\\my_custom_template_20250728_194000.xlsm"
  new_reg_table_file: "C:\\sdw_input\\機能一覧と新規・変更内容_20250728_194000.xlsx"
```

**说明：**

- 内置配置文件不需要用户修改，随项目打包分发
- 自定义配置文件只保存用户通过API设置的自定义模板路径
- 重启后自定义模板设置会自动加载，无需重新配置

## 使用流程

1. **查看可用模板** - 调用 `/api/sdw/template/list` 获取所有支持的模板文件
2. **设置自定义模板** - 调用 `/api/sdw/template/set_custom` 为特定模板文件设置自定义路径
   - 系统会自动将文件复制到输入目录
   - 自定义模板路径会保存到用户配置文件中，重启后不会丢失
3. **重置为内置模板** - 调用 `/api/sdw/template/set_custom` 并传入 `template_path: null` 重置为使用内置模板
4. **使用工作流** - 工作流会自动使用配置的自定义模板，如果没有配置则使用内置模板

**注意事项：**

- 自定义模板设置会持久化保存，重启服务后仍然有效
- 内置模板配置不会被修改，确保系统稳定性
- 所有自定义模板文件都会被复制到统一的输入目录进行管理

## 注意事项

1. 自定义模板文件必须存在且可访问
2. 模板文件格式应与工作流要求的格式一致
3. 配置更改会立即生效，无需重启服务
4. 建议在设置自定义模板前备份原始模板文件
