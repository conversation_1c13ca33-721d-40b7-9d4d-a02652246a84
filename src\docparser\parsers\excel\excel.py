"""
@File    : excel.py
<AUTHOR> 董根虎
@Date    : 2024-10-29
@Desc    : 文档对象模型的excel格式的实现
"""
import logging
import os
import time
import re
import shutil
import psutil

from win32com.client.gencache import GetGeneratePath

from docparser.common.base import convert_xls_to_xlsx
from docparser.core.base_parser import BaseParser
from docparser.models.document import DocumentBlockObject
from docparser.parsers.excel.excel_standard import ExcelStandardParser
from docparser.parsers.excel.excel_timing import ExcelTimingParser
from docparser.parsers.excel.utils import fix_openpyxl_descriptors_base_bug
from docparser.utils.theme_color import ThemeColor

fix_openpyxl_descriptors_base_bug()



class ExcelParser(BaseParser):

    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.xlsx', '.xls', '.xlsm']
    """excel文档解析器"""
    def _get_bus_type(self, file_path):
        """ 根据文件名获取excel的解析器对象 """
        file_name = os.path.basename(file_path)
        # 文件名中是否有TIMING
        if "TIMING" in file_name.upper():
            return 13, ExcelTimingParser()
        return 0, ExcelStandardParser()

    def _parse_document_content(self, file_path) -> None:
        """
        解析excel文档入口
        :param file_path: excel文件路径
        :return: 返回解析后的文档数据。文档数据包含了文档信息、布局信息、样式信息和内容信息
        """
        # 清理残余进程
        self.clean_tgt_process()
        # 清理缓存的接口代码
        self.clean_gen_cache()
        # 加载excel解析器对象
        bus_type, parser = self._get_bus_type(file_path)

        # Convert .xls to .xlsx if needed
        if file_path.lower().endswith('.xls'):
            file_path = convert_xls_to_xlsx(file_path)

        parser.read_workbook(file_path)
        # 解析文档
        blocks = parser.parse_document()
        logging.info('parse document end')
        self.document._document = blocks

    @staticmethod
    def clean_gen_cache():
        """ 清除缓存的接口代码 """
        try:
            gen_cache_path = GetGeneratePath()
            shutil.rmtree(gen_cache_path)
        except (OSError, PermissionError):
            logging.error("clean gen cache failed")

    @staticmethod
    def clean_tgt_process():
        """ 清除旧的office进程 """
        tgt_process = [
            "RuntimeBroker.exe",
            "wps.exe",
            "wpscloudsvr.exe"
        ]
        # 遍历所有的进程
        cur_ts = time.time()
        for proc in psutil.process_iter():
            if re.findall(r"(?:RuntimeBroker|wps|wpscloudsvr|EXCEL.EXE)", proc.name()) and \
                    proc.create_time() < cur_ts - 5:
                try:
                    proc.terminate()
                    logging.info("old office processes has been killed.")
                except psutil.NoSuchProcess:
                    continue


if __name__ == '__main__':
    # r"D:\桌面文件\fengtian\line13\before\MET-G_TIMING-CSTD-A0-00-B-C0变更前.xls",
    # r"D:\桌面文件\fengtian\line13\after\MET-G_TIMING-CSTD-A0-00-B-C0变更后.xlsx"
    paths = [
        r"D:\Users\kotei\Desktop\详细json数据\详细json数据\excel\1220演示数据变更前.xlsx"
    ]
    for file_path in paths:
        file_name, ext = os.path.splitext(file_path)
        # 每个worksheet对应一个block对象， 返回block对象列表
        data = ExcelParser().parse_document(file_path)
        # 保存数据
        # for block in data:
        #     with open(file_name + " " + block.name + ".json", "w", encoding="utf-8") as f:
        #         f.write(block.to_json())

        for sheet in data._document:
            print(f"sheet_name {sheet._name} texts {len(sheet.texts)} tables {len(sheet.tables)} "
                  f"timing_texts {len(sheet.timing_texts)} timing_waves {len(sheet.timing_waves)} "
                  f"pictures {len(sheet.pictures)} graphics {len(sheet.graphics)}")
            for x in sheet.graphics:
                if x.name == "Text Box 385":
                    # if x.name == "Text Box 493":
                    # if x.name == "AutoShape 2026":
                    # if "直線コネクタ" in x.name:
                    print(f"id:{x.id} coordinate:{x.coordinate.desc} name:{x.name} width:{x.width} height:{x.height} ")


