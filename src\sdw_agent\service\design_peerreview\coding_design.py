import glob
import os

import pandas as pd
import xlwings as xw
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries, column_index_from_string, get_column_letter
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.styles import PatternFill, Border, Side, Alignment
from pydantic import BaseModel, Field

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
import difflib
from pathlib import Path
import sdw_agent.service.design_peerreview.common_cs as common_cs
from sdw_agent.service.design_peerreview.models import CheckRangeIninfo

def check_drqamd(file_path):
    try:
        stamp_results = {}
        # 检查空白和NG内容
        ng_results = {}
        check_ng_sheets_info1 = {
            "機能一覧と新規・変更内容": {"range_address": ["C2:C3","H3:H3","R2:R3"],
                           "check_direction": 'down',
                           "check_direction_enable": False,
                           "range_not_included": []},
            "ファイルリスト": {"range_address": ["C4:M3","H3:H3"],
                           "check_direction": 'down',
                           "check_direction_enable": True,
                           "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info1.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        check_ng_sheets_info3 = {
            "機能一覧と新規・変更内容": {"range_address": ["C8:G8","J8:N8","P8:Z8","AE8:AN8","AP8:AT8","AV8:BD8"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": []}#"H:I","O:O","AA:AD","AO:AO","AU:AU"
        }
        for sheet_name, check_info_value in check_ng_sheets_info3.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        file_names_1 = common_cs.get_excel_columns_data(file_path, "機能一覧と新規・変更内容", 7, data_by_col_idx=1)
        get_valid_idx = [idx  for idx,value in enumerate(file_names_1)  if str(value)=="end"]
        file_names_42 = common_cs.get_excel_columns_data(file_path,"機能一覧と新規・変更内容",7,data_by_col_idx=42)#43 44 45 46
        file_names_42 = file_names_42[0:get_valid_idx[0]-1]
        file_names_42 = list(set(file_names_42))
        file_names_43 = common_cs.get_excel_columns_data(file_path, "機能一覧と新規・変更内容", 7, data_by_col_idx=43)
        file_names_43 = file_names_43[0:get_valid_idx[0] - 1]
        file_names_43 = list(set(file_names_43))
        file_names_44 = common_cs.get_excel_columns_data(file_path, "機能一覧と新規・変更内容", 7, data_by_col_idx=44)
        file_names_44 = file_names_44[0:get_valid_idx[0] - 1]
        file_names_44 = list(set(file_names_44))
        file_names_45 = common_cs.get_excel_columns_data(file_path, "機能一覧と新規・変更内容", 7, data_by_col_idx=45)
        file_names_45 = file_names_45[0:get_valid_idx[0] - 1]
        file_names_45 = list(set(file_names_45))

        file_names_42 = list(set(file_names_42+file_names_43+file_names_44+file_names_45))
        check_file_names = common_cs.get_excel_columns_data(file_path, "ファイルリスト", 3, data_by_col_idx=2)
        file_check_result = []
        for file_name in file_names_42:
            if str(file_name).strip() == "" or str(file_name) == "-" or str(file_name).upper() == "NAN":
                continue
            if str(file_name) not in check_file_names:
                file_check_result.append(file_name)
        file_check_results = {
            "ファイルリスト":{
                "确认内容填写是否异常":"OK" if len(file_check_result)==0 else "NG",
                "content_message":str(file_check_result)+"在sheet【ファイルリスト】找不到，可能填写遗漏" if len(file_check_result)>0 else ""
            }
        }
        ng_results = common_cs.mergeDict(ng_results,file_check_results)
        return ng_results
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)





    # errlist = []
    # file = pd.ExcelFile(file_path)
    # sheet_name = "機能一覧と新規・変更内容"
    # df = pd.read_excel(file, sheet_name=sheet_name)
    #
    # # 1. 从上往下找到第2列值为"end"的行索引
    # first_col = df.iloc[:, 1]  # 获取第2列
    # end_row_index = None
    # for idx, value in enumerate(first_col):
    #     if pd.notna(value) and str(value).strip().lower() == "end":
    #         end_row_index = idx
    #         logger.info(f"找到第一列值为'end'的行索引: {end_row_index}")
    #         break
    #
    # if end_row_index is None:
    #     logger.error("未找到第一列值为'end'的行")
    #     return {
    #         'success': False,
    #         'message': "未找到第一列值为'end'的行"
    #     }
    #
    # # 2. 从第3列6-row行的范围内从上往下找出最后一个不为空或不是'-'的单元格索引
    # second_col = df.iloc[6:end_row_index, 2]  # 获取第3列从第6行到end_row_index的范围
    # last_valid_index = None
    #
    # for idx, value in enumerate(second_col):
    #     if pd.notna(value) and str(value).strip() != "" and str(value).strip() != "-":
    #         last_valid_index = idx
    #         logger.info(f"在第二列6-{end_row_index}行范围内，找到有效值: {value}，索引: {last_valid_index}")
    #
    # data_df = df.iloc[6:last_valid_index + 7, 2:55]
    #
    # # 检查data_df中是否存在空单元格
    # empty_cells = []
    # for row_idx, row in data_df.iterrows():
    #     for col_idx, value in enumerate(row):
    #         if col_idx == 44:
    #             continue
    #         if pd.isna(value) or str(value).strip() == "":
    #             # 计算实际的Excel行号和列名
    #             excel_row_num = row_idx + 2
    #             excel_col_name = common_cs.index_to_excel_column(2 + col_idx)  # 从第2列开始
    #             empty_cells.append(f"{excel_col_name}{excel_row_num}")
    #
    # if empty_cells:
    #     error_msg = f"{sheet_name}: 存在空单元格: {empty_cells}"
    #     logger.error(error_msg)
    #     errlist.append(error_msg)
    # else:
    #     logger.success(f"{sheet_name}: 所有单元格都有值")
    #
    # non_dash_count = 0
    # for row_idx, row in data_df.iloc[:, 40:42].iterrows():
    #     for col_idx, value in enumerate(row):
    #         if pd.notna(value) and str(value).strip() != "" and str(value).strip() != "-":
    #             non_dash_count += 1
    #
    # print(f"temp中值不为'-'的单元格个数: {non_dash_count}")
    #
    # df = pd.read_excel(file, sheet_name="ファイルリスト")
    # first_col = df.iloc[:, 1]  # 获取第2列
    # end_row_index = None
    # for idx, value in enumerate(first_col):
    #     if pd.notna(value) and str(value).strip().lower() == "end":
    #         end_row_index = idx
    #         logger.info(f"找到第一列值为'end'的行索引: {end_row_index}")
    #         break
    #
    # if end_row_index is None:
    #     logger.error("未找到第一列值为'end'的行")
    #     return {
    #         'success': False,
    #         'message': "未找到第一列值为'end'的行"
    #     }
    #
    # # 2. 从第3列6-row行的范围内从上往下找出最后一个不为空或不是'-'的单元格索引
    # second_col = df.iloc[2:end_row_index, 2]  # 获取第3列从第6行到end_row_index的范围
    # count = 0
    #
    # for idx, value in enumerate(second_col):
    #     if pd.notna(value) and str(value).strip() != "" and str(value).strip() != "-":
    #         count += 1
    #         logger.info(f"在第二列6-{end_row_index}行范围内，找到有效值: {value}，索引: {last_valid_index}")
    #
    # if count != non_dash_count:
    #     logger.error(f"ファイルリスト中文件名的个数: {count}，機能一覧と新規・変更内容中文件名的个数: {non_dash_count}")
    #     errlist.append(
    #         f"ファイルリスト中文件名的个数: {count}，機能一覧と新規・変更内容中文件名的个数: {non_dash_count}")
    # else:
    #     logger.success(
    #         f"ファイルリスト中文件名的个数: {count}，機能一覧と新規・変更内容中文件名的个数: {non_dash_count}")
    #
    # if errlist:
    #     return {
    #         'success': False,
    #         'message': errlist
    #     }
    # else:
    #     return {
    #         'success': True,
    #         'message': "所有检查通过"
    #     }

def check_qac(file_path):
    try:
        stamp_results = {}
        # 检查空白和NG内容
        ng_results = {}
        #检车盖章
        stamp_sheets_name = {"表紙":["G12:L16","V12:X16"]}
        for sheet_name,check_addrs in stamp_sheets_name.items():
            for check_addr in  check_addrs:
                stamp_result = common_cs.check_stamp(file_path, sheet_name,check_addr)
                stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        #检查空白
        check_ng_sheets_info = {
            "warn": {"range_address": ["G4:V4"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": ["G:G"]}
        }



        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info.items():
            header_names = common_cs.get_excel_columns_data(file_path, sheet_name, 2, data_by_row_idx=0)
            target_names = []
            company_names = []
            if "除外判定" in header_names:
                target_idx = header_names.index("除外判定")
                target_names = common_cs.get_excel_columns_data(file_path, sheet_name, 0, data_by_col_idx=target_idx)
            if "会社名" in header_names:
                company_idx = header_names.index("会社名")
                company_names = common_cs.get_excel_columns_data(file_path, sheet_name, 0, data_by_col_idx=company_idx)


            not_includes = []
            # 添加排除项
            if len(target_names):  #特殊处理DNKT_CPPResult_MET.xlsm
                not_includes = [f"{str(idx+1)}:{str(idx+1)}" for idx in range(0, min(len(target_names), len(company_names))) if (str(target_names[idx]).strip() != "確認対象") or (str(company_names[idx]).strip() != "DNKT")]
            else:
                not_includes = [f"{str(idx + 1)}:{str(idx + 1)}" for idx in
                                range(0, len(company_names)) if (
                                            str(company_names[idx]).strip() != "DNKT")]
                not_includes.append("V:V")

            check_info_value["range_not_included"].extend(not_includes)
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info,full_word_matching=True)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        # 合并处理结果
        results = common_cs.mergeDict(ng_results, stamp_results)
        return results
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)

def check_codesonar(file_path):
    try:
        stamp_results = {}
        # 检查空白和NG内容
        ng_results = {}
        # 检车盖章
        stamp_sheets_name = {"表紙": ["G12:L16", "V12:X16"]}
        for sheet_name, check_addrs in stamp_sheets_name.items():
            for check_addr in check_addrs:
                stamp_result = common_cs.check_stamp(file_path, sheet_name, check_addr)
                stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        # 检查空白
        check_ng_sheets_info = {
            "警告一覧": {"range_address": ["N2:AB2"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": ["N:N"]}
        }

        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info.items():
            header_names = common_cs.get_excel_columns_data(file_path, sheet_name, 0, data_by_row_idx=0)
            target_names = []
            company_names = []
            if "対象判定" in header_names:
                target_idx = header_names.index("対象判定")
                target_names = common_cs.get_excel_columns_data(file_path, sheet_name, 0, data_by_col_idx=target_idx)
            if "会社名" in header_names:
                company_idx = header_names.index("会社名")
                company_names = common_cs.get_excel_columns_data(file_path, sheet_name, 0, data_by_col_idx=company_idx)

            # 添加排除项
            not_includes = [f"{str(idx + 1)}:{str(idx + 1)}" for idx in
                            range(0, min(len(target_names), len(company_names))) if
                            (str(target_names[idx]).strip() != "確認対象") or (
                                        str(company_names[idx]).strip() != "DNKT")]
            check_info_value["range_not_included"].extend(not_includes)
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info,full_word_matching=True)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        # 合并处理结果
        results = common_cs.mergeDict(ng_results, stamp_results)
        return results
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)
def check_file_compare(file_path):
    try:
        stamp_results = {}
        # 检查空白和NG内容
        ng_results = {}
        stamp_sheets_name = ["Cover"]
        for sheet_name in stamp_sheets_name:
            stamp_result = common_cs.check_stamp(file_path, sheet_name)
            stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        check_ng_sheets_info1 = {
            "Cover": {"range_address": ["B16:B16","B21:B21"],
                           "check_direction": 'down',
                           "check_direction_enable": False,
                           "range_not_included": []},
            "FileList": {"range_address": ["C3:C3","C6:C6"],
                           "check_direction": 'down',
                           "check_direction_enable": False,
                           "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info1.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        check_ng_sheets_info2 = {
            "FileList": {"range_address": ["C11:G11"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": []}#"H:I","O:O","AA:AD","AO:AO","AU:AU"
        }
        file_names = common_cs.get_excel_columns_data(file_path, "FileList", 10, data_by_col_idx=6)
        for file_name in file_names:
            if str(file_name).strip()!="" and file_name  is  not  None:
                check_ng_sheets_info2[file_name] = {"range_address": ["A7:I7"],
                         "check_direction": 'down',
                         "check_direction_enable": True,
                         "range_not_included": ["A:E"]}

        for sheet_name, check_info_value in check_ng_sheets_info2.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na_xls(file_path, sheet_name, check_info,full_word_matching=True)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        return common_cs.mergeDict(ng_results, stamp_results)
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)


def check_file_compare_functionsheet(file, sheet_names=[]):
    """

    """
    issues = []
    try:
        for sheet_name in sheet_names:
            df = pd.read_excel(file, sheet_name=sheet_name)
            # 获取F、G、H、I列的索引（对应Excel的F、G、H、I列）
            f_col = 0  # df_from_row5中的第0列对应原DataFrame的第5列(F列)
            g_col = 1  # df_from_row5中的第1列对应原DataFrame的第6列(G列)
            h_col = 2  # df_from_row5中的第2列对应原DataFrame的第7列(H列)
            i_col = 3  # df_from_row5中的第3列对应原DataFrame的第8列(I列)

            # 找到第一列最后一个数字所在的行
            first_col = df.iloc[:, 0]  # 获取第一列
            last_row = None
            for idx, value in enumerate(first_col):
                if pd.notna(value) and str(value).strip() != "":
                    last_row = idx

            # 从第5行开始分析，选择第5-8列（F、G、H、I列）
            df_from_row5 = df.iloc[5:last_row, 5:9]
            # print(df_from_row5)

            # 1. 检查df_from_row5是否全不为空
            if df_from_row5.empty:
                logger.warning(f"{sheet_name}: df_from_row5为空DataFrame")
                return issues

            # 检查是否有空值
            empty_cells = []
            for idx, row in df_from_row5.iterrows():
                for col_idx, value in enumerate(row):
                    if pd.isna(value) or str(value).strip() == "":
                        excel_row_num = idx + 6  # Excel实际行号（从第5行开始，所以是idx + 6）
                        excel_col_name = common_cs.index_to_excel_column(5 + col_idx)  # 5是起始列索引
                        empty_cells.append(f"{excel_col_name}{excel_row_num}")

            if empty_cells:
                logger.error(f"{sheet_name}: 结果列存在空单元格: {empty_cells}")
                issues.append(f"{sheet_name}: 结果列存在空单元格: {empty_cells}")
            else:
                logger.success(f"{sheet_name}: 结果列全不为空")

            # 2. 业务逻辑检查：F列不为"-"时的检查
            for idx, row in df_from_row5.iterrows():
                excel_row_num = idx + 6  # Excel实际行号（从第5行开始，所以是idx + 6）
                f_value = str(row.iloc[f_col]).strip()
                g_value = str(row.iloc[g_col]).strip()
                h_value = str(row.iloc[h_col]).strip()
                i_value = str(row.iloc[i_col]).strip()

                # 如果F列不为"-"
                if f_value != "-":
                    # 检查G列是否也为"-"
                    if g_value == "-":
                        msg = f"{sheet_names} 第{excel_row_num}行: 番号列不为'-'但修正理由列为'-'，番号列值: {f_value}"
                        logger.error(msg)
                        issues.append(msg)

                    # 检查H列和I列是否为"OK"
                    if h_value != "OK":
                        msg = f"{sheet_names} 第{excel_row_num}行: 番号列值: {f_value}，セルフ列值: {h_value}"
                        logger.error(msg)
                        issues.append(msg)

                    if i_value != "OK":
                        msg = f"{sheet_names} 第{excel_row_num}行: 番号列值: {f_value}，コードレビュー結果列值: {i_value}"
                        logger.error(msg)
                        issues.append(msg)

        if issues:
            return {
                'success': False,
                'message': issues
            }
        else:
            return {
                'success': True,
                'message': "所有检查通过",
            }
    except Exception as e:
        error_msg = f"{sheet_names}: 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }

def check_viewcs(file_path):
    # 检测盖章和签字
    results = {}
    stamp_results = {}
    ng_results = {}
    stamp_sheets_name = ["コードレビュー観点CS"]
    for sheet_name in stamp_sheets_name:
        pictures_count = 3
        stamp_count = common_cs.check_pictures(file_path, sheet_name,pictures_count = pictures_count)
        stamp_result = {sheet_name: {
            "stamp_message": "" if stamp_count >= pictures_count else f"{sheet_name}的sheet发现缺少{pictures_count - stamp_count}个盖章",
            f"确认签字盖章是否OK": bool(True if stamp_count >= pictures_count else False)
        }
        }
        stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        titles = common_cs.get_excel_columns_data(file_path, sheet_name, 5, data_by_row_idx=0)
        title_result = []
        for title_idx,title in enumerate(titles):
            if title_idx in [5,6,7,8] and str(title).strip().replace("実施者","") =="":
                title_result.append(f"${get_column_letter(title_idx+1)}$6")
        stamp_result = {sheet_name: {
            "stamp_message": "" if len(title_result) == 0 else f"{sheet_name}的sheet发现{title_result}缺少签名",
            f"确认签字盖章是否OK": bool(True if len(title_result) == 0 else False)
        }
        }
        stamp_results = common_cs.mergeDict(stamp_results, stamp_result)


    # 检查空白和NG内容
    check_ng_sheets_info = {
        "コードレビュー観点CS": {"range_address": ["D5:D6","F6:I6","F8:I26"], "check_direction": 'down', "check_direction_enable": False,"range_not_included":[]},
        }

    check_info = CheckRangeIninfo()
    for sheet_name, check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        check_info.range_not_included = check_info_value["range_not_included"]
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
            ng_results = common_cs.mergeDict(ng_results, ng_result)

    # 合并处理结果
    results = common_cs.mergeDict(ng_results, stamp_results)

    return results


def check_ram_interference(file_path):
    try:
        stamp_results = {}
        ng_results = {}
        stamp_sheets_name = {"干渉CS":["J13:J16","N13:O16","U13:U16","Y13:AA16"]}
        for sheet_name,stamp_ranges in stamp_sheets_name.items():
            for stamp_range in stamp_ranges:
                stamp_result = common_cs.check_stamp(file_path, sheet_name,stamp_range)
                stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        # 检查空白和NG内容
        check_ng_sheets_info1 = {
            "干渉CS": {"range_address": ["C6:C10"],
                           "check_direction": 'down',
                           "check_direction_enable": False,
                           "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info1.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info,check_ng_flag = False)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        check_ng_sheets_info3 = {
            "干渉CS": {"range_address": ["H21:J21"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info3.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info, check_ng_flag=False)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        check_ng_sheets_info2 = {
            "干渉CS": {"range_address": ["B21:AA21"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": ["H:P","V:V"]}
        }


        #檢測單元格填寫是否有空白或者NG
        check_info = CheckRangeIninfo()

        row_idx = 0
        for sheet_name, check_info_value in check_ng_sheets_info2.items():
            title_name = common_cs.getfilesheetname(file_path,sheet_name)
            ok_titles = common_cs.get_excel_columns_data(file_path, title_name, None, data_by_col_idx=9)
            not_includes = check_info_value["range_not_included"]
            for title_idx,title in enumerate(ok_titles):
                if title == "OK":
                    not_included_str = "Q"+str(title_idx+1)+":U"+str(title_idx+1)
                    not_includes.append(not_included_str)
                    not_included_str = "W"+str(title_idx+1)+":Y"+str(title_idx+1)
                    not_includes.append(not_included_str)
            range_addresses = check_info_value["range_address"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                check_info.check_direction = check_info_value["check_direction"]
                check_info.check_direction_enable = check_info_value["check_direction_enable"]
                check_info.range_not_included = not_includes
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)



        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)


def check_design_book_code_match(file_path):
    try:
        stamp_results = {}
        ng_results = {}
        stamp_sheets_name = {"表紙": ""}
        for sheet_name, stamp_ranges in stamp_sheets_name.items():
            stamp_result = common_cs.check_stamp(file_path, sheet_name)
            stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        check_ng_sheets_info = {
            "表紙": {"range_address": ["X19:AA21"],
                                                    "check_direction": 'down',
                                                    "check_direction_enable": False,
                                                    "range_not_included": []},
            "設計文書　⇔　コード　照合実施＆結果検証": {"range_address": ["D6:I6"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info, check_ng_flag=False)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
            error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
            logger.error(error_msg)

def check_design_cs_basic(file_path):
    try:
        return common_cs.check_cs_design_conditions(file_path)
    except Exception as e:
            error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
            logger.error(error_msg)


