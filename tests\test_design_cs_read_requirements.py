"""
测试设计基准CS - 读取要件一览表
"""
import os
import shutil
from datetime import datetime
from loguru import logger
from sdw_agent.util.import_doc_util import get_task_content
from sdw_agent.model.response_model import SourceInfo
from sdw_agent.service.design_cs_workflow import DesignCSWorkflow, DesignCSInputModel


def test_read_requirement_list():
    """读取要件一览表并返回列表"""

    # 要件一览表文件路径 - 使用相对路径
    current_dir = os.path.dirname(__file__)
    file_path = os.path.join(current_dir, "epic v001_要件分析1.xlsx")

    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return []

    logger.info(f"开始读取要件一览表: {file_path}")

    # 创建SourceInfo对象
    source_info = SourceInfo(
        type="local",
        uri=file_path
    )

    try:
        # 调用get_task_content获取内容
        content_list = get_task_content(source_info)

        logger.info(f"总共读取到 {len(content_list)} 条记录")

        # 返回前30条记录
        max_rows = min(30, len(content_list))
        result_list = content_list[:max_rows]

        logger.info(f"返回前 {len(result_list)} 条记录")
        return result_list

    except Exception as e:
        logger.error(f"读取要件一览表时发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return []


def test_design_cs_workflow():
    """测试DesignCSWorkflow工作流，3种类型各测试10次，共30次"""

    # 获取要件列表
    requirement_list = test_read_requirement_list()

    if not requirement_list:
        logger.error("没有获取到要件数据，无法进行测试")
        return

    # 内置模板文件路径
    template_file_path = r"c:\code\0730-TDD\DEV_Agent/src\sdw_agent\service\design_cs_workflow\ソフトウェア 設計基準CS.xlsm"

    # 检查模板文件是否存在
    if not os.path.exists(template_file_path):
        logger.error(f"模板文件不存在: {template_file_path}")
        return

    # 输出目录
    output_base_dir = "C:/tdd_output/design_cs"
    os.makedirs(output_base_dir, exist_ok=True)

    # 定义3种工作表类型
    sheet_types = [
        {"sheet": "設計基準CS-要件分析", "suffix": "要件分析"},
        {"sheet": "設計基準CS-基本設計", "suffix": "基本設計"},
        {"sheet": "設計基準CS-詳細設計", "suffix": "詳細設計"}
    ]

    logger.info(f"开始测试DesignCSWorkflow，3种类型各10次，共30次测试")

    # 创建工作流实例
    workflow = DesignCSWorkflow()

    # 统计结果
    total_success = 0
    total_failed = 0
    type_results = {}

    # 针对3种类型分别测试
    test_counter = 0

    for sheet_type in sheet_types:
        sheet_name = sheet_type["sheet"]
        suffix = sheet_type["suffix"]

        logger.info(f"\n=== 开始测试 {sheet_name} ===")

        # 初始化该类型的统计
        type_success = 0
        type_failed = 0
        type_results[suffix] = {"success": 0, "failed": 0, "files": []}

        # 每种类型测试10次
        for i in range(10):
            test_counter += 1
            requirement_index = i % len(requirement_list)  # 循环使用要件数据
            requirement = requirement_list[requirement_index]

            logger.info(f"\n--- {suffix} 第 {i+1} 次测试 (总第 {test_counter} 次) ---")

            try:
                # 从要件数据中提取信息
                if isinstance(requirement, dict):
                    ar_no = requirement.get('ar_no', f'AR_{test_counter:03d}')
                    ar_summary = requirement.get('req_change_content', f'测试要件_{test_counter}')
                    p_no = requirement.get('p_no', f'P_{test_counter:03d}')
                else:
                    ar_no = getattr(requirement, 'ar_no', f'AR_{test_counter:03d}')
                    ar_summary = getattr(requirement, 'req_change_content', f'测试要件_{test_counter}')
                    p_no = getattr(requirement, 'p_no', f'P_{test_counter:03d}')

                # 生成带时间戳的输出文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 包含毫秒
                template_name = os.path.splitext(os.path.basename(template_file_path))[0]
                output_filename = f"{template_name}_{suffix}_{test_counter:02d}_{timestamp}.xlsm"
                output_file_path = os.path.join(output_base_dir, output_filename).replace("\\", "/")

                # 复制模板文件到输出目录
                shutil.copy2(template_file_path, output_file_path)
                logger.info(f"模板文件已复制到: {output_file_path}")

                logger.info(f"工作表: {sheet_name}")
                logger.info(f"AR编号: {ar_no}")
                logger.info(f"要件内容: {ar_summary}")
                logger.info(f"P编号: {p_no}")

                # 创建输入数据
                input_data = DesignCSInputModel(
                    file_path=output_file_path,  # 使用复制后的文件
                    target_sheet=sheet_name,
                    include_hidden_rows=False,
                    header_row=None,
                    start_row=None,
                    filter_major_category=None,
                    filter_middle_category=None,
                    # AI分析相关参数
                    enable_ai_analysis=True,
                    ar_no=ar_no,
                    ar_summary=ar_summary,
                    p_no=p_no
                )

                # 执行工作流
                logger.info(f"执行工作流...")
                result = workflow.run(input_data)

                # 检查结果
                if result.status.name == 'SUCCESS':
                    type_success += 1
                    total_success += 1
                    type_results[suffix]["success"] += 1
                    type_results[suffix]["files"].append(output_file_path)
                    logger.info(f"✓ {suffix} 第 {i+1} 次测试成功")
                    logger.info(f"结果消息: {result.message}")
                else:
                    type_failed += 1
                    total_failed += 1
                    type_results[suffix]["failed"] += 1
                    logger.error(f"✗ {suffix} 第 {i+1} 次测试失败")
                    logger.error(f"失败原因: {result.message}")

            except Exception as e:
                type_failed += 1
                total_failed += 1
                type_results[suffix]["failed"] += 1
                logger.error(f"✗ {suffix} 第 {i+1} 次测试异常: {str(e)}")
                logger.exception("详细错误信息:")

        # 输出该类型的测试总结
        logger.info(f"\n--- {suffix} 测试总结 ---")
        logger.info(f"成功: {type_success}/10")
        logger.info(f"失败: {type_failed}/10")
        logger.info(f"成功率: {type_success/10*100:.1f}%")

    # 输出总体测试总结
    logger.info(f"\n=== 总体测试总结 ===")
    logger.info(f"总测试数量: 30 (3种类型 × 10次)")
    logger.info(f"总成功数量: {total_success}")
    logger.info(f"总失败数量: {total_failed}")
    logger.info(f"总成功率: {total_success/30*100:.1f}%")

    # 输出各类型详细结果
    logger.info(f"\n=== 各类型详细结果 ===")
    for suffix, results in type_results.items():
        logger.info(f"{suffix}: 成功 {results['success']}, 失败 {results['failed']}")
        logger.info(f"  生成文件数: {len(results['files'])}")

    logger.info(f"\n所有文件保存在: {output_base_dir}")


if __name__ == "__main__":
    test_read_requirement_list()
    # test_design_cs_workflow()
