import re
import logging
from typing import Any, Dict, List, Optional, Union

from openpyxl.styles.colors import B<PERSON>C<PERSON>

from docparser import TableProcessorPlugin
from docparser.models.document import DocumentBlockObject, DocumentObject
from docparser.models.table import TableObject

# Configure logging
logger = logging.getLogger('docparser.plugins.excel_table_plugin')

class ExcelTableHeaderPlugin(TableProcessorPlugin):
    """
    Plugin for analyzing text objects in documents.
    Adds metadata about text content such as word count, character count, etc.
    """

    def get_plugin_name(self) -> str:
        """Get plugin name"""
        return "ExcelTableHeader"

    def get_plugin_version(self) -> str:
        """Get plugin version"""
        return "1.0.0"

    def get_supported_document_types(self) -> List[str]:
        """Get supported document types"""
        return ["xlsx", "xls"]  # Support excel

    def process_document(self, document: DocumentObject) -> Dict[str, Any]:
        """
        Process document data.

        Args:
            document_data: Dictionary containing parsed document data

        Returns:
            Processed document data
        """
        logger.info("Processing document with TextAnalyzer plugin")
        document_dict = document.to_dict()
        for doc in document_dict["document"]:
            doc['tables'] = self.process_table_objects(doc['tables'])

        return document_dict

    def process_table_objects(self, table_objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process text objects.

        Args:
            text_objects: List of text objects

        Returns:
            Processed text objects
        """
        res = []
        for table in table_objects:
            table_obj = TableObject.from_dict(table)
            self._parse_table_head(table_obj)
            res.append(table_obj.to_dict())
        return res

    def validate_document(self, document_data: Dict[str, Any]) -> bool:
        """
        Validate document data.

        Args:
            document_data: Dictionary containing parsed document data

        Returns:
            True if document data is valid, False otherwise
        """
        # Check if document has text objects
        if 'tables' not in document_data or not document_data['tables']:
            logger.warning("Document has no table objects")
            return False

        return True

    def _parse_table_head(self, table: TableObject, rule=1):
        """
        自动识别表格的表头
        通过表格背景色,单元格边框信息自动识别表头
        :param table: TableObject对象
        :param rule: 0: AI识别表头;  1: 背景颜色; 2: 下边框的样式
        """
        if not (table and table.rows):
            return

        _BLACK = "#" + BLACK[2:]
        _HEAD_BORDER_STYLE = ['medium', 'double']  # 粗线,双横线

        def check_horizontal_head(condition, only_condition=True):
            """
            水平表格的表头提取
            :param condition:
            :param only_condition: True, 只计算满足条件的数据作为表头; False, 自动识别到最大值之前所有的都算做表头
            :return:
            """
            for i in range(len(table.rows)):
                cells_ = table.rows[i].cells
                if all(condition(cells_[j]) for j in range(len(cells_))):
                    table.head_list.append(i)
            if table.head_list and not only_condition:
                # 自动识别到最大值之前所有的数据都算做表头
                last_index = table.head_list[-1]
                table.head_list = [x for x in range(last_index + 1)]
            # 修正处理: 如果所有行都被标记为表头, 那么相当于没能正常识别出表头
            if len(table.head_list) == len(table.rows):
                logging.info("check_table_head All rows are marked as headers, is invalid")
                table.head_list = []
            if table.head_list and table.head_list[-1] == len(table.rows) - 1:
                table.head_list.pop()
            if table.head_list and table.head_list[0] != 0:
                table.head_list = []
            if table.head_list:
                for i in range(len(table.head_list)):
                    if i > 0 and (table.head_list[i - 1] + 1) != table.head_list[i]:
                        table.head_list = table.head_list[:i]
                        break
                table.head_type = "horizontal"
                return True
            return False

        def check_vertical_head(condition, only_condition=True):
            """
            垂直表格的表头提取
            :param condition:
            :param only_condition: True, 只计算满足条件的数据作为表头; False, 自动识别到最大值之前所有的都算做表头
            :return:
            """
            col_len = len(table.rows[0].cells)
            for i in range(col_len):
                # 当前列中所有行都有背景色，那么该列识别为表头
                if all(condition(row.cells[i]) for row in table.rows):
                    table.head_list.append(i)
            if table.head_list and not only_condition:
                # 自动识别到最大值之前所有的数据都算做表头
                last_index = table.head_list[-1]
                table.head_list = [x for x in range(last_index + 1)]
            # 修正处理: 如果所有列都被标记为表头, 那么相当于没能正常识别出表头
            if len(table.head_list) == col_len:
                logging.info("check_table_head, All columns are marked as headers, is invalid")
                table.head_list = []
            if table.head_list and table.head_list[-1] == col_len - 1:
                table.head_list.pop()
            if table.head_list and table.head_list[0] != 0:
                table.head_list = []
            if table.head_list:
                for i in range(len(table.head_list)):
                    if i > 0 and (table.head_list[i - 1] + 1) != table.head_list[i]:
                        table.head_list = table.head_list[:i]
                        break
                table.head_type = "vertical"
                return True
            return False

        # 0. 使用AI识别表头
        # if rule == 0:
        #     index_list = get_table_head_by_ai(format_table(table))
        #     table.head_type = "horizontal"
        #     table.head_list = index_list
        #     return
        if type == 1 and check_horizontal_head(lambda c: c.style.background_color not in [_BLACK, '']):
            return
        elif type == 2 and check_horizontal_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE,
                                                 True):
            return

        # # 1. 通过背景颜色识别水平表的表头
        # if check_horizontal_head(lambda c: c.style.background_color not in [_BLACK, '']):
        #     return
        # # 2. 通过表格的下边框加粗或者双横线识别水平表格的表头
        # if check_horizontal_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE, True):
        #     return
        # # 3. 通过背景颜色识别垂直表的表头
        # if check_vertical_head(lambda c: c.style.background_color not in [_BLACK, '']):
        #     return
        # # 4. 通过表格的右边框加粗或者双横线识别垂直表格的表头
        # if check_vertical_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE, True):
        #     return


