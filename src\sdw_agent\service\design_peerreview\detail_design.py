import glob
import os

import pandas as pd
import xlwings as xw
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries, column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.utils import get_column_letter
from openpyxl.styles import PatternFill, Border, Side, Alignment
from pydantic import BaseModel, Field

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
import difflib
from pathlib import Path
import sdw_agent.service.design_peerreview.common_cs as common_cs
from sdw_agent.service.design_peerreview.models import CheckRangeIninfo


def check_if_cs(file_path):
    """
         2.5 詳細設計	I/F整合性確認。
        :param file_path: Excel 文件路径
        :return:
    """

    #检测盖章和签字
    results={}
    stamp_results = {}
    ng_results = {}
    stamp_sheets_name = ["表紙","変更履歴"]
    for sheet_name in stamp_sheets_name:
        stamp_result = common_cs.check_stamp(file_path,sheet_name)
        stamp_results = common_cs.mergeDict(stamp_results,stamp_result)

    #检查空白和NG内容
    check_ng_sheets_info = {"概要":{"range_address":["F8:F16"],"check_direction":'right',"check_direction_enable":True},
                            "_IF":{"range_address":["B14:L14"],"check_direction":'down',"check_direction_enable":True}
                            }

    check_info = CheckRangeIninfo()
    for sheet_name,check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = common_cs.check_blank_ng_na(file_path,sheet_name,check_info)
            ng_results = common_cs.mergeDict(ng_results,ng_result)


    #合并处理结果
    results = common_cs.mergeDict(ng_results,stamp_results)

    return results
'''
    # file = pd.ExcelFile(file_path)
    # sheet_names = file.sheet_names
    # sheet_names_exclude = [x for x in sheet_names if IF_sheet not in x]
    # empty_sheets = common_cs.check_empty_sheet(file, sheet_names)
    # function_name_sheet = [sheet for sheet in sheet_names if
    #                        sheet.isascii() and sheet.replace(' ', '').replace('_', '').replace('-', '').isalpha()]
    # 
    # 
    # 
    # errlist = []
    # 
    # # 检查空表
    # if not empty_sheets['success']:
    #     logger.error(empty_sheets['message'])
    #     return empty_sheets
    # else:
    #     logger.success("no empty sheets")
    # # 检查変更履歴
    # res = common_cs.check_empty_sheet(file, check_sheet="変更履歴", check_range="C5:F6", row_full_not_empty=True)
    # if not res['success']:
    #     logger.error(res['message'])
    #     errlist.append(res['message'])
    # 
    # # 检查概要表
    # sheet3 = pd.read_excel(file, sheet_name="概要")
    # res = common_cs.check_value_between_positions(sheet3, search_name1="■本評価の目的", search_name2="■評価対象",
    #                                     exact_match=True)
    # if not res['success']:
    #     logger.error(res['message'])
    #     errlist.append(res['message'])
    # 
    # res = common_cs.search_around_value(sheet3, "■評価対象", ["vehcle", "Soft version", "module", "source path", "document"],
    #                           cols=(1, 4), rows=(1, 7), direction='bottom-right', exact_match=True)
    # if not res['success']:
    #     logger.error(res['message'])
    #     errlist.append(res['message'])
    # 
    # # 检查函数名称表
    # for i in function_name_sheet:
    #     df = pd.read_excel(file, sheet_name=i)
    #     # C列第2行（pandas索引第0行第2列）
    #     c2_value = df.iloc[0, 2] if df.shape[0] > 0 and df.shape[1] > 2 else None
    #     if c2_value == "OK":
    #         logger.success(f"{i}中Judge结果为'OK'")
    #     else:
    #         logger.error(f"{i}中Judge结果不是'OK'，实际值为: {c2_value}")
    #         errlist.append(f"{i}中Judge结果不是'OK'，实际值为: {c2_value}")
    # 
    # if not errlist:
    #     return {
    #         'success': True,
    #         'message': "所有检查通过"
    #     }
    # else:
    #     return {
    #         'success': False,
    #         'message': errlist
    #     }
'''

def check_bf_bh_ok(df, sheet_name=""):
    """
    检查从第14行开始，BF列为"対象"时，BH列是否为"OK"。
    返回不满足条件的行号和内容列表。
    """
    issues = []
    try:
        # 获取BF和BH列的索引
        confirm_flag_col = 56
        bf_col = 57
        bg_col = 58
        bh_col = 59

        # 从第14行开始分析
        df_from_row14 = df.iloc[13:]
        for idx, row in df_from_row14.iterrows():
            excel_row_num = idx + 2  # Excel实际行号
            if f"MM連携" == str(row.iloc[confirm_flag_col]).strip():
                if str(row.iloc[bf_col]).strip() == "対象":
                    if str(row.iloc[bh_col]).strip().upper() != "OK":
                        celladress = f"${get_column_letter(bh_col+1)}${excel_row_num}"
                        msg = f"{celladress}不是'OK'，实际为: {row.iloc[bh_col]}"
                        logger.info(msg)
                        issues.append(msg)
                    if (r"-" != str(row.iloc[bg_col]).strip()):
                        celladress = f"${get_column_letter(bg_col+1)}${excel_row_num}"
                        msg = f"{celladress}不是'-'，实际为: {row.iloc[bg_col]}"
                        logger.info(msg)
                        issues.append(msg)
                elif str(row.iloc[bf_col]).strip() == "対象外":
                    if str(row.iloc[bg_col]).strip() == "-" or str(row.iloc[bg_col]).strip() == "":
                        celladress = f"${get_column_letter(bg_col+1)}${excel_row_num}"
                        msg = f"{celladress}没有写对象外理由"
                        logger.info(msg)
                        issues.append(msg)
                    if str(row.iloc[bh_col]).strip() != "対象外":
                        celladress = f"${get_column_letter(bh_col+1)}${excel_row_num}"
                        msg = f"{celladress}前面是对象外，此处应该-或者写对象外，实际写了其他值"
                        logger.info(msg)
                        issues.append(msg)
                else:
                    celladress = f"${get_column_letter(confirm_flag_col+1)}${excel_row_num}"
                    msg = f"{celladress}没有填写'对象'/'对象外'"
                    logger.info(msg)
                    issues.append(msg)
            else:
                continue


        if not issues:
            logger.success(f"{sheet_name}: 所有检查通过")
        return {
        sheet_name: {
            "确认内容填写是否异常": True if (len(issues) == 0) else False,
            "content_message": str(list(set(issues))) if (len(issues)) else ""
        }}

    except Exception as e:
        error_msg = f"{sheet_name}: 检查过程中出现错误: {e}"
        logger.error(error_msg)



def check_can_output(file_path):

    try:
        file = pd.ExcelFile(file_path)
        # 检测盖章和签字
        results = {}
        stamp_results = {}
        stamp_sheets_name = ["表紙"]
        for sheet_name in stamp_sheets_name:
            stamp_result = common_cs.check_stamp(file_path, sheet_name)
            stamp_results = common_cs.mergeDict(stamp_results, stamp_result)



        #检车空白，NG，填写内容
        sheet3 = pd.read_excel(file, sheet_name="Transmit")
        check_sheet3 = check_bf_bh_ok(sheet3, "Transmit")
        # 合并处理结果
        results = common_cs.mergeDict(stamp_results,check_sheet3)

        return results
    except Exception as e:
        error_msg = f"{file_path}: 检查过程中出现错误: {e}"
        logger.error(error_msg)

def check_cell_right_3rd_has_value(file, sheet_name, cell_coordinate):
    """
    检查指定单元格右边第3个单元格是否有值

    Args:
        file: Excel文件对象
        sheet_name: sheet名称
        cell_coordinate: 要检查的单元格坐标（如 "D2"）

    Returns:
        dict: 包含检查结果的字典
    """
    try:
        wb = load_workbook(file)
        ws = wb[sheet_name]

        # 解析单元格坐标

        col_letter, row_num = coordinate_from_string(cell_coordinate)
        col_num = column_index_from_string(col_letter)

        # 计算右边第3个单元格的坐标
        right_3rd_col = col_num + 3
        right_3rd_cell_coord = f"{ws.cell(row=row_num, column=right_3rd_col).coordinate}"
        right_3rd_cell = ws.cell(row=row_num, column=right_3rd_col)

        # 检查是否有值
        has_value = right_3rd_cell.value is not None and str(right_3rd_cell.value).strip() != ""

        wb.close()

        result = {
            'success': has_value,
            'message': f"Sheet '{sheet_name}' 单元格 {cell_coordinate} 右边第3个单元格 {right_3rd_cell_coord} {'有值' if has_value else '为空'}: {right_3rd_cell.value}",
        }

        if has_value:
            logger.success(result['message'])
            return {'success': True}
        else:
            logger.error(result['message'])
            return result

    except Exception as e:
        error_msg = f"Sheet '{sheet_name}' 单元格 {cell_coordinate} 检查右边第3个单元格时出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }


def get_cells_with_values_in_column(file, sheet_name, column_index=1):
    """
    遍历指定sheet的指定列，找出有值的单元格并记录上下两个有数据单元格之间的距离。

    Args:
        file: Excel文件对象
        sheet_name: sheet名称
        column_index: 要检查的列索引（默认为1，即第一列）

    Returns:
        list: 包含有值单元格信息的列表，每个元素包含坐标、值、距离等信息
    """
    cells_with_values = []
    try:
        # 使用openpyxl读取工作簿
        wb = load_workbook(file)
        ws = wb[sheet_name]

        # 遍历指定列的所有单元格，找出有值的单元格
        for row in range(1, ws.max_row + 1):
            cell = ws.cell(row=row, column=column_index)

            # 检查单元格是否有值
            if cell.value is not None and str(cell.value).strip() != "":
                cells_with_values.append({
                    'coordinate': cell.coordinate,
                    'row': row,
                    'value': cell.value
                })

        # 计算每个单元格与下一个有值单元格之间的距离
        for i in range(len(cells_with_values)):
            current_cell = cells_with_values[i]

            if i < len(cells_with_values) - 1:
                # 计算与下一个有值单元格的距离
                next_cell = cells_with_values[i + 1]
                distance = next_cell['row'] - current_cell['row']
                current_cell['distance_to_next'] = distance
                logger.info(
                    f"单元格 {current_cell['coordinate']} 与下一个有值单元格 {next_cell['coordinate']} 的距离为 {distance}")
            else:
                # 最后一个单元格，距离设为0或特殊标记
                current_cell['distance_to_next'] = 0
                logger.info(f"单元格 {current_cell['coordinate']} 是最后一个有值的单元格")

        wb.close()
        logger.success(f"在{sheet_name}的第{column_index}列中找到 {len(cells_with_values)} 个有值的单元格")
        return cells_with_values

    except Exception as e:
        error_msg = f"检查列中有值单元格时出现错误: {e}"
        logger.error(error_msg)
        return []


def check_right_cells_below_in_range(file, sheet_name, cell_coordinate, distance):
    """
    找出指定单元格右侧有值的单元格，检查右侧所有单元格下方在指定距离范围内是否有值

    Args:
        file: Excel文件对象
        sheet_name: sheet名称
        cell_coordinate: 要检查的单元格坐标（如 "D2"）
        distance: 距离范围

    Returns:
        dict: 包含检查结果的字典
    """
    issues = []
    try:
        wb = load_workbook(file)
        ws = wb[sheet_name]

        # 解析单元格坐标

        col_letter, row_num = coordinate_from_string(cell_coordinate)
        col_num = column_index_from_string(col_letter)

        # 找出右侧有值的单元格
        right_cells_with_values = []
        for col in range(col_num + 1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col)
            if cell.value is not None and str(cell.value).strip() != "":
                right_cells_with_values.append({
                    'parent': ws.cell(row=row_num, column=col_num).value,
                    'coordinate': cell.coordinate,
                    'col': col,
                    'value': cell.value
                })

        logger.info(
            f"Sheet '{sheet_name}' 单元格 {cell_coordinate} 右侧找到 {len(right_cells_with_values)} 个有值的单元格")

        # 检查每个右侧单元格下方在距离范围内的单元格
        for right_cell in right_cells_with_values:
            right_col = right_cell['col']
            right_coord = right_cell['coordinate']
            right_parent = right_cell['parent']

            # 检查下方距离范围内的单元格
            for row_offset in range(1, distance):
                if right_parent == '引数' or right_parent == '戻り値' or right_parent == 'ローカル変数':
                    check_row = row_num + 1
                else:
                    check_row = row_num + row_offset
                check_cell = ws.cell(row=check_row, column=right_col)
                check_coord = check_cell.coordinate

                # 检查是否有值
                if check_cell.value is None or str(check_cell.value).strip() == "":
                    issue_msg = f"Sheet '{sheet_name}' 右侧单元格 {right_coord} 下方的单元格 {check_coord} 在设定的范围内为空（有空单元格）"
                    issues.append(issue_msg)
                    logger.error(issue_msg)
                else:
                    logger.success(
                        f"Sheet '{sheet_name}' 右侧单元格 {right_coord} 下方的单元格 {check_coord} 有值: {check_cell.value}")

        wb.close()

        if len(issues) == 0:
            return {'success': True}
        else:
            result = {
                'success': False,
                'message': f"{issues}"
            }
            return result

    except Exception as e:
        error_msg = f"Sheet '{sheet_name}' 单元格 {cell_coordinate} 检查右侧单元格下方值时出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }



def check_all_cells_with_methods(file, sheet_name, column_index=1):
    """
    Args:
        file: Excel文件对象
        sheet_name: sheet名称
        column_index: 要检查的列索引（默认为1，即第一列）

    Returns:
        dict: 包含所有检查结果的字典
    """
    results = {
        'success': True,
        'message': ''
    }

    try:
        # 获取指定列中有值的单元格
        cells_with_values = get_cells_with_values_in_column(file, sheet_name, column_index)

        logger.info(f"Sheet '{sheet_name}' 开始检查 {len(cells_with_values)} 个有值单元格")
        check_right_3rd = ["D4", "D6", "D8"]
        # 对每个有值的单元格应用两个检查方法
        for cell_info in cells_with_values:
            cell_coord = cell_info['coordinate']
            distance = cell_info['distance_to_next']
            if cell_info['value'] == "■関数定義" or cell_info['value'] == "■処理概要":
                continue
            logger.info(f"Sheet '{sheet_name}' 检查单元格: {cell_coord}")
            if cell_coord in check_right_3rd or cell_info['value'] == '制約事項':
                # 方法1：检查右边第3个单元格
                method1_result = check_cell_right_3rd_has_value(file, sheet_name, cell_coord)
                if not method1_result['success']:
                    results['message'] += method1_result['message'] + "; "
            else:
                # 方法2：检查右侧单元格下方在距离范围内
                if distance > 0:  # 只对非最后一个单元格进行检查
                    method2_result = check_right_cells_below_in_range(file, sheet_name, cell_coord, distance)
                    if not method2_result['success']:
                        results['message'] += method2_result['message'] + "; "
                else:
                    # 最后一个单元格，跳过方法2检查
                    pass

        # 计算总体结果
        if results['message']:
            results['success'] = False
            results['message'] = results['message'].rstrip("; ")
            logger.error(results['message'])
            return results
        else:
            logger.success(f"Sheet '{sheet_name}' 检查完成，共检查 {len(cells_with_values)} 个单元格，无问题")
            return {'success': True}

    except Exception as e:
        error_msg = f"Sheet '{sheet_name}' 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }



def check_func_book(file_path):
    try:
        stamp_results = {}
        ng_results = {}
        stamp_sheets_name = {"表紙":["V42:AJ42"]}
        for sheet_name,stamp_ranges in stamp_sheets_name.items():
            for stamp_range in stamp_ranges:
                stamp_result = common_cs.check_stamp(file_path, sheet_name,stamp_range)
                stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        # 检查空白和NG内容
        ng_results = {}
        check_ng_sheets_info1 = {
            "関数一覧": {"range_address": ["B4:F4"],
                           "check_direction": 'down',
                           "check_direction_enable": True,
                           "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info1.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)
        check_ng_sheets_info3 = {
            "temp": {"range_address": ["G4:G5", "G7:G7", "G8:G8"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": []}
        }
        check_ng_sheets_info2 = {
            "引数": {"range_address": ["G:AR"],
                          "check_direction": 'down',
                          "check_direction_enable": False,
                          "range_not_included": ["H:H", "J:J", "L:P", "R:W", "Y:Y", "AA:AA", "AC:AD", "AF:AQ"],
                        "next_title":"戻り値"
                     },
            "戻り値": {"range_address": ["G:AR"],
                                         "check_direction": 'down',
                                         "check_direction_enable": False,
                                         "range_not_included": ["H:J", "L:P", "R:W", "Y:Y", "AA:AA", "AC:AD", "AF:AQ"],
                        "next_title":f"■処理概要"
                     },
            "ローカル変数": {"range_address": ["G:AR"],
                                         "check_direction": 'down',
                                         "check_direction_enable": False,
                                         "range_not_included": ["H:J", "L:P", "R:W", "Y:Y", "AA:AA", "AC:AD", "AF:AQ"],
                        "next_title":"制約事項"
                     },
            "使用IF": {"range_address": ["G:AB"],
                            "check_direction": 'down',
                            "check_direction_enable": False,
                            "range_not_included": ["H:J", "L:O", "Q:AA"],
                        "next_title":"使用RAM"
                     },
            "使用RAM": {"range_address": ["G:AR"],
                             "check_direction": 'down',
                             "check_direction_enable": False,
                             "range_not_included": ["H:I", "K:L", "N:AA", "AC:AQ"],
                        "next_title":"ローカル変数"
                     },
        }

        func_sheet_names = common_cs.get_excel_columns_data(file_path,"関数一覧",4,data_by_col_idx=4)

        #檢測單元格填寫是否有空白或者NG
        check_info = CheckRangeIninfo()
        for func_sheet_name in func_sheet_names:
            if str(func_sheet_name).strip().upper() == "NAN" or str(func_sheet_name).strip() =="":
                continue

            titles = common_cs.get_excel_columns_data(file_path, func_sheet_name, None, data_by_col_idx=3)
            range_addresses = check_ng_sheets_info3["temp"]["range_address"]
            check_info.check_direction = check_ng_sheets_info3["temp"]["check_direction"]
            check_info.check_direction_enable = check_ng_sheets_info3["temp"]["check_direction_enable"]
            check_info.range_not_included = check_ng_sheets_info3["temp"]["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, func_sheet_name, check_info,full_word_matching=True)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

            row_idx = 0
            for temp_name, check_info_value in check_ng_sheets_info2.items():
                row_idx = titles.index(temp_name)+2
                max_row_idx = titles.index(check_info_value["next_title"])
                range_addresses = check_info_value["range_address"]
                for range_addr in range_addresses:
                    if row_idx == max_row_idx:
                        check_info.check_direction_enable = check_info_value["check_direction_enable"]
                    else:
                        max_row_idx = row_idx
                        check_info.check_direction_enable = True
                    range_add = range_addr.split(":")[0] + str(row_idx) + ":" + range_addr.split(":")[1] + str(
                        max_row_idx)
                    check_info.range_address = range_add
                    check_info.check_direction = check_info_value["check_direction"]
                    check_info.range_not_included = check_info_value["range_not_included"]
                    ng_result = common_cs.check_blank_ng_na(file_path, func_sheet_name, check_info,full_word_matching=True)
                    ng_results = common_cs.mergeDict(ng_results, ng_result)

            #檢測函數流程圖
            row_idx = titles.index("関数設計") + 14
            pictures_count = 1
            stamp_count = common_cs.check_pictures(file_path, func_sheet_name, pictures_count=pictures_count,cell_address = "K"+str(row_idx)+":"+"K"+str(row_idx))
            picture_result = {func_sheet_name: {
                "content_message": "" if stamp_count >= pictures_count else f"{func_sheet_name}的sheet发现缺少{pictures_count - stamp_count}个流程图",
                f"确认内容填写是否异常": bool(True if stamp_count >= pictures_count else False)
            }
            }
            ng_results = common_cs.mergeDict(ng_results, picture_result)

        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)

    # file = pd.ExcelFile(file_path)
    # sheet_names = file.sheet_names
    # empty_sheets = common_cs.check_empty_sheet(file, sheet_names)
    # err_list = []
    # # 检查空表
    # if not empty_sheets['success']:
    #     logger.error(empty_sheets['message'])
    #     return empty_sheets
    # else:
    #     logger.success("no empty sheets")
    #
    # # 检查変更符号sheet
    # if "変更符号" in sheet_names:
    #     res = common_cs.check_empty_sheet(file, check_sheet="変更符号", check_range="Q18:Q20", col_full_not_empty=True)
    #     if not res['success']:
    #         logger.error(res['message'])
    #     err_list.append(res['message'])
    #
    # # 检查関数一覧sheet
    # df = pd.read_excel(file, sheet_name="関数一覧")
    # func_names = [i for i in df.iloc[3:, 4] if i != ""]
    # res = check_empty_from_row(df, sheet_name="関数一覧", start_row_index=4, start_col_index=1, end_col_index=6)
    # if res:
    #     logger.error(f"sheet 関数一覧存在存在非完整行")
    #     err_list.append(f"sheet 関数一覧存在存在非完整行")
    #
    # # 检查函数表数量是否对应
    # func_names_from_sheet = [sheet_name for sheet_name in sheet_names if
    #                          sheet_name not in ["表紙(承認)", "変更符号", "関数一覧", "【参考】関数一覧",
    #                                             "【参考】u1_g_OdoTripKm", "関数仕様書雛形"]]
    # if len(func_names) != len(func_names_from_sheet):
    #     logger.error(f"函数表数量不对应，应有{len(func_names)}个，实际有{len(func_names_from_sheet)}个")
    #     err_list.append(f"函数表数量不对应，应有{len(func_names)}个，实际有{len(func_names_from_sheet)}个")
    #
    # # 使用组合方法检查第4列中的所有有值单元格
    # logger.info("=== 开始检查第4列中的所有有值单元格 ===")
    # for i in func_names:
    #     results = check_all_cells_with_methods(file, i, column_index=4)
    #     # 返回检查结果
    #     if results['success']:
    #         logger.success("所有检查通过")
    #     else:
    #         logger.error("检查发现问题")
    #         err_list.append(results['message'])
    # if not err_list:
    #     return {
    #         'success': True,
    #         'message': "所有检查通过"
    #     }
    # else:
    #     return {
    #         'success': False,
    #         'message': err_list
    #     }


def check_ram_book(file_path):
    # 检查空白和NG内容
    ng_results = {}
    check_ng_sheets_info = {
        "データ構造": {"range_address": ["B5:AU5"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included":["E10:F18","R:R","T:T","V:V","X:X","Z:Z","AB:AB","AD:AD","AF:AF","AG:AH","AJ:AJ","AL:AL","AN:AN"]}
        }

    stamp_results = {}
    ng_results = {}
    stamp_sheets_name = ["表紙","変更履歴"]
    for sheet_name in stamp_sheets_name:
        stamp_result = common_cs.check_stamp(file_path, sheet_name)
        stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

    check_info = CheckRangeIninfo()
    for sheet_name, check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        check_info.range_not_included = check_info_value["range_not_included"]
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = common_cs.check_blank_ng_na_xls(file_path, sheet_name, check_info,full_word_matching=True)
            ng_results = common_cs.mergeDict(ng_results, ng_result)

    return common_cs.mergeDict(ng_results, stamp_results)


def check_empty_from_row(df, sheet_name="", start_row_index=None, start_col_index=None, end_col_index=None,
                         except_cols_index=None, ignore_cols_index=None):
    """
    从第start_row_index行开始检查，除except_cols列外其它列有NaN就报错，except_cols列只要有值即可。
    ignore_cols_index: 忽略检查的列索引列表，这些列不会被检查是否为空。
    返回错误信息列表，全部非空返回空列表。
    """
    issues = []
    try:
        # 获取except_cols列的列名
        except_cols_names = []
        if except_cols_index is not None:
            except_cols_names = [df.columns[i] for i in except_cols_index]

        # 获取ignore_cols列的列名
        ignore_cols_names = []
        if ignore_cols_index is not None:
            ignore_cols_names = [df.columns[i] for i in ignore_cols_index]

        # 从第start_row_index行开始，从start_col_index列开始，到end_col_index列结束
        df_from_row = df.iloc[start_row_index - 1:, start_col_index:end_col_index]
        for idx, row in df_from_row.iterrows():
            excel_row_num = idx + 1  # Excel实际行号

            # 如果指定了except_cols_index，检查except_cols列是否有值（排除ignore_cols列）
            if except_cols_index is not None:
                # 只在最后一行检查整个列是否都为空
                if idx == len(df_from_row) - 1:  # 最后一行
                    # 过滤掉ignore_cols列，只检查不在ignore_cols中的except_cols列
                    filtered_except_cols = [col for col in except_cols_names if col not in ignore_cols_names]
                    if filtered_except_cols:  # 只有当过滤后还有列需要检查时才进行检查
                        all_rows_empty = all(
                            all(pd.isna(df_from_row.iloc[i][col]) for col in filtered_except_cols) for i in
                            range(len(df_from_row)))
                        if all_rows_empty:
                            issues.append(f"{sheet_name}: {filtered_except_cols}列所有行都为NaN")
                # continue

            # 检查所有列（除了ignore_cols列）
            for col_idx, col in enumerate(df_from_row.columns):
                # 跳过ignore_cols列
                if ignore_cols_index is not None and col in ignore_cols_names:
                    continue

                if pd.isna(row[col]):
                    # 处理start_col_index可能为None的情况
                    actual_col_index = (start_col_index or 0) + col_idx
                    excel_col_name = common_cs.index_to_excel_column(actual_col_index)
                    issues.append(f"{sheet_name} 第{excel_row_num + 1}行: {excel_col_name}列为NaN")

        if not issues:
            logger.success(
                f"{sheet_name}: 从第{start_row_index + 1}行开始所有行（除except_cols列和ignore_cols列）均无NaN")
        else:
            for msg in issues:
                logger.error(msg)
            return issues
    except Exception as e:
        error_msg = f"{sheet_name}: 检查过程中出现错误: {e}"
        logger.error(error_msg)
        return [error_msg]

def check_software_design_book_function_specification_book_2_5(file_path):
    # 检查空白和NG内容
    ng_results = {}
    check_ng_sheets_info = {
        "設計ID一覧": {"range_address": ["D4:F4"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": []},
        "ソフトウェア設計書⇔関数仕様書照合": {"range_address": ["D6:J6"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": []}
    }
    check_info = CheckRangeIninfo()
    for sheet_name, check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        check_info.range_not_included = check_info_value["range_not_included"]
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = common_cs.check_blank_ng_na_xls(file_path, sheet_name, check_info, full_word_matching=True)
            ng_results = common_cs.mergeDict(ng_results, ng_result)

    return ng_results


def check_design_cs_basic(file_path):
    return common_cs.check_cs_design_conditions(file_path)



def check_recent_bug_points(file_path):
    try:
        # 检查空白和NG内容
        ng_results = {}
        sheets_info = {
            "要件分析": {"range_address": "F4:I6",
                         "ignore_col":"G"},
            "設計": {"range_address": "F4:I11",
                         "ignore_col":"G"},

            "結合検査": {"range_address": "F4:I13",
                         "ignore_col":"G"},

            "システム": {"range_address": "F4:I11",
                         "ignore_col":"G"},
        }
        check_ng_sheets_info = {
            "要件分析": {"range_address": ["F4:I4"],
                         "check_direction": "down",
                         "check_direction_enable":True,
                         "range_not_included":["H:I"]},
            "設計": {"range_address": ["F4:I4"],
                         "check_direction": "down",
                         "check_direction_enable":True,
                         "range_not_included":["H:I"]},

            "結合検査": {"range_address": ["F4:I4"],
                         "check_direction": "down",
                         "check_direction_enable":True,
                         "range_not_included":["H:I"]},

            "システム": {"range_address": ["F4:I4"],
                         "check_direction": "down",
                         "check_direction_enable":True,
                         "range_not_included":["H:I"]},
        }

        stamp_results = {}
        ng_results = {}
        stamp_sheets_name = ["使用時変更履歴"]
        for sheet_name,check_info in sheets_info.items():
            stamp_result = common_cs.check_stamp(file_path, sheet_name,cell_address = check_info["range_address"],ignore_col = check_info["ignore_col"])
            stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        return common_cs.mergeDict(ng_results, stamp_results)

    except Exception as e:
        error_msg = f"{file_path}: 检查过程中出现错误: {e}"
        logger.error(error_msg)

