"""
SSH远程连接工具类

提供远程登录Linux服务器并执行命令的功能
"""

import paramiko
import socket
import time
from typing import Op<PERSON>, Tu<PERSON>, List
from dataclasses import dataclass
from loguru import logger


@dataclass
class SSHConfig:
    """SSH连接配置"""
    hostname: str
    port: int = 22
    username: str = ""
    password: str = ""
    private_key_path: str = ""
    timeout: int = 30
    connect_timeout: int = 10


@dataclass
class CommandResult:
    """命令执行结果"""
    success: bool
    exit_code: int
    stdout: str
    stderr: str
    command: str
    execution_time: float


class SSHUtil:
    """SSH工具类"""
    
    def __init__(self, config: SSHConfig):
        """
        初始化SSH工具
        
        Args:
            config: SSH连接配置
        """
        self.config = config
        self.client = None
        self.is_connected = False
    
    def connect(self) -> bool:
        """
        连接到远程服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 准备连接参数
            connect_kwargs = {
                'hostname': self.config.hostname,
                'port': self.config.port,
                'username': self.config.username,
                'timeout': self.config.connect_timeout
            }
            
            # 使用密码或私钥认证
            if self.config.private_key_path:
                private_key = paramiko.RSAKey.from_private_key_file(self.config.private_key_path)
                connect_kwargs['pkey'] = private_key
            else:
                connect_kwargs['password'] = self.config.password
            
            # 建立连接
            self.client.connect(**connect_kwargs)
            self.is_connected = True
            
            logger.info(f"成功连接到服务器: {self.config.hostname}:{self.config.port}")
            return True
            
        except Exception as e:
            logger.error(f"连接服务器失败: {str(e)}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.client:
                self.client.close()
                self.is_connected = False
                logger.info("SSH连接已断开")
        except Exception as e:
            logger.error(f"断开连接时出错: {str(e)}")
    
    def execute_command(self, command: str, timeout: Optional[int] = None) -> CommandResult:
        """
        执行单个命令
        
        Args:
            command: 要执行的命令
            timeout: 命令执行超时时间（秒）
            
        Returns:
            CommandResult: 命令执行结果
        """
        if not self.is_connected:
            if not self.connect():
                return CommandResult(
                    success=False,
                    exit_code=-1,
                    stdout="",
                    stderr="SSH连接失败",
                    command=command,
                    execution_time=0.0
                )
        
        try:
            start_time = time.time()
            timeout = timeout or self.config.timeout
            
            logger.info(f"执行命令: {command}")
            
            # 执行命令
            stdin, stdout, stderr = self.client.exec_command(command, timeout=timeout)
            
            # 获取执行结果
            exit_code = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8', errors='ignore')
            stderr_data = stderr.read().decode('utf-8', errors='ignore')
            
            execution_time = time.time() - start_time
            
            result = CommandResult(
                success=(exit_code == 0),
                exit_code=exit_code,
                stdout=stdout_data,
                stderr=stderr_data,
                command=command,
                execution_time=execution_time
            )
            
            logger.info(f"命令执行完成，耗时: {execution_time:.2f}秒，退出码: {exit_code} {stderr}")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time if 'start_time' in locals() else 0.0
            logger.error(f"执行命令失败: {str(e)}")
            
            return CommandResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                command=command,
                execution_time=execution_time
            )
    
    def execute_commands(self, commands: List[str], timeout: Optional[int] = None) -> List[CommandResult]:
        """
        批量执行命令
        
        Args:
            commands: 命令列表
            timeout: 每个命令的超时时间
            
        Returns:
            List[CommandResult]: 命令执行结果列表
        """
        results = []
        
        for command in commands:
            result = self.execute_command(command, timeout)
            results.append(result)
            
            # 如果命令失败，记录日志但继续执行下一个命令
            if not result.success:
                logger.warning(f"命令执行失败: {command}, 错误: {result.stderr}")
        
        return results
    
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """
        上传文件到远程服务器
        
        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径
            
        Returns:
            bool: 上传是否成功
        """
        if not self.is_connected:
            if not self.connect():
                return False
        
        try:
            sftp = self.client.open_sftp()
            sftp.put(local_path, remote_path)
            sftp.close()
            
            logger.info(f"文件上传成功: {local_path} -> {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return False
    
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """
        从远程服务器下载文件
        
        Args:
            remote_path: 远程文件路径
            local_path: 本地文件路径
            
        Returns:
            bool: 下载是否成功
        """
        if not self.is_connected:
            if not self.connect():
                return False
        
        try:
            sftp = self.client.open_sftp()
            sftp.get(remote_path, local_path)
            sftp.close()
            
            logger.info(f"文件下载成功: {remote_path} -> {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件下载失败: {str(e)}")
            return False
    
    def download_from_ftp(self, ftp_host: str, ftp_user: str, ftp_password: str, 
                         ftp_remote_path: str, local_path: str, ftp_port: int = 21) -> CommandResult:
        """
        从远程FTP服务器下载文件到Linux服务器
        
        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_path: FTP服务器上的文件路径
            local_path: Linux服务器上的本地路径
            ftp_port: FTP端口（默认21）
            
        Returns:
            CommandResult: 下载命令执行结果
        """
        # 构建wget命令下载FTP文件
        wget_command = (
            f"wget --ftp-user='{ftp_user}' --ftp-password='{ftp_password}' "
            f"ftp://{ftp_host}:{ftp_port}{ftp_remote_path} -O {local_path}"
        )
        
        logger.info(f"从FTP服务器下载文件: {ftp_host}{ftp_remote_path} -> {local_path}")
        
        # 执行下载命令
        result = self.execute_command(wget_command)
        
        if result.success:
            logger.info(f"FTP文件下载成功: {ftp_remote_path} -> {local_path}")
        else:
            logger.error(f"FTP文件下载失败: {result.stderr}")
            
        return result
    
    def download_from_ftp_with_curl(self, ftp_host: str, ftp_user: str, ftp_password: str,
                                   ftp_remote_path: str, local_path: str, ftp_port: int = 21) -> CommandResult:
        """
        使用curl从远程FTP服务器下载文件到Linux服务器
        
        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_path: FTP服务器上的文件路径
            local_path: Linux服务器上的本地路径
            ftp_port: FTP端口（默认21）
            
        Returns:
            CommandResult: 下载命令执行结果
        """
        # 构建curl命令下载FTP文件
        curl_command = (
            f"curl -u '{ftp_user}:{ftp_password}' "
            f"ftp://{ftp_host}:{ftp_port}{ftp_remote_path} -o {local_path}"
        )
        
        logger.info(f"使用curl从FTP服务器下载文件: {ftp_host}{ftp_remote_path} -> {local_path}")
        
        # 执行下载命令
        result = self.execute_command(curl_command)
        
        if result.success:
            logger.info(f"FTP文件下载成功: {ftp_remote_path} -> {local_path}")
        else:
            logger.error(f"FTP文件下载失败: {result.stderr}")
            
        return result
    
    def download_from_ftp_with_lftp(self, ftp_host: str, ftp_user: str, ftp_password: str,
                                   ftp_remote_path: str, local_path: str, ftp_port: int = 21) -> CommandResult:
        """
        使用lftp从远程FTP服务器下载文件到Linux服务器（支持断点续传）
        
        Args:
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_path: FTP服务器上的文件路径
            local_path: Linux服务器上的本地路径
            ftp_port: FTP端口（默认21）
            
        Returns:
            CommandResult: 下载命令执行结果
        """
        # 构建lftp命令下载FTP文件
        lftp_command = (
            f"lftp -u '{ftp_user},{ftp_password}' -p {ftp_port} {ftp_host} "
            f"-e 'get {ftp_remote_path} -o {local_path}; quit'"
        )
        
        logger.info(f"使用lftp从FTP服务器下载文件: {ftp_host}{ftp_remote_path} -> {local_path}")
        
        # 执行下载命令
        result = self.execute_command(lftp_command)
        
        if result.success:
            logger.info(f"FTP文件下载成功: {ftp_remote_path} -> {local_path}")
        else:
            logger.error(f"FTP文件下载失败: {result.stderr}")
            
        return result
    
    def download_directory(self, remote_dir: str, local_dir: str, 
                          exclude_patterns: Optional[List[str]] = None) -> bool:
        """
        从远程服务器下载整个文件夹到本地
        
        Args:
            remote_dir: 远程文件夹路径
            local_dir: 本地文件夹路径
            exclude_patterns: 排除的文件模式列表（如 ['*.log', '*.tmp']）
            
        Returns:
            bool: 下载是否成功
        """
        if not self.is_connected:
            if not self.connect():
                return False
        
        try:
            import os
            from pathlib import Path
            
            # 创建本地目录
            local_path = Path(local_dir)
            local_path.mkdir(parents=True, exist_ok=True)
            
            sftp = self.client.open_sftp()
            
            def _download_recursive(remote_path: str, local_path: str):
                """递归下载文件夹"""
                try:
                    # 获取远程目录内容
                    items = sftp.listdir_attr(remote_path)
                    
                    for item in items:
                        remote_item_path = f"{remote_path}/{item.filename}"
                        local_item_path = os.path.join(local_path, item.filename)
                        
                        # 检查是否需要排除
                        if exclude_patterns:
                            import fnmatch
                            should_exclude = any(
                                fnmatch.fnmatch(item.filename, pattern) 
                                for pattern in exclude_patterns
                            )
                            if should_exclude:
                                logger.info(f"跳过文件: {remote_item_path}")
                                continue
                        
                        # 判断是文件还是目录
                        if item.st_mode & 0o040000:  # 是目录
                            logger.info(f"创建目录: {local_item_path}")
                            os.makedirs(local_item_path, exist_ok=True)
                            _download_recursive(remote_item_path, local_item_path)
                        else:  # 是文件
                            logger.info(f"下载文件: {remote_item_path} -> {local_item_path}")
                            sftp.get(remote_item_path, local_item_path)
                            
                except Exception as e:
                    logger.error(f"下载目录 {remote_path} 时出错: {str(e)}")
                    raise
            
            # 开始递归下载
            _download_recursive(remote_dir, str(local_path))
            sftp.close()
            
            logger.info(f"文件夹下载成功: {remote_dir} -> {local_dir}")
            return True
            
        except Exception as e:
            logger.error(f"文件夹下载失败: {str(e)}")
            return False
    
    def download_directory_with_rsync(self, remote_dir: str, local_dir: str,
                                     exclude_patterns: Optional[List[str]] = None,
                                     compress: bool = True) -> CommandResult:
        """
        使用rsync下载文件夹（需要本地安装rsync）
        
        Args:
            remote_dir: 远程文件夹路径
            local_dir: 本地文件夹路径
            exclude_patterns: 排除的文件模式列表
            compress: 是否启用压缩传输
            
        Returns:
            CommandResult: rsync命令执行结果
        """
        import subprocess
        import os
        
        try:
            # 创建本地目录
            os.makedirs(local_dir, exist_ok=True)
            
            # 构建rsync命令
            rsync_cmd = [
                "rsync", "-avz" if compress else "-av",
                "--progress",
                f"{self.config.username}@{self.config.hostname}:{remote_dir}/",
                local_dir
            ]
            
            # 添加排除模式
            if exclude_patterns:
                for pattern in exclude_patterns:
                    rsync_cmd.extend(["--exclude", pattern])
            
            # 添加SSH选项
            ssh_opts = f"-p {self.config.port}"
            if self.config.private_key_path:
                ssh_opts += f" -i {self.config.private_key_path}"
            rsync_cmd.extend(["-e", f"ssh {ssh_opts}"])
            
            logger.info(f"执行rsync命令: {' '.join(rsync_cmd)}")
            
            # 执行rsync命令
            start_time = time.time()
            result = subprocess.run(
                rsync_cmd,
                capture_output=True,
                text=True,
                timeout=self.config.timeout * 60  # rsync可能需要更长时间
            )
            execution_time = time.time() - start_time
            
            command_result = CommandResult(
                success=(result.returncode == 0),
                exit_code=result.returncode,
                stdout=result.stdout,
                stderr=result.stderr,
                command=' '.join(rsync_cmd),
                execution_time=execution_time
            )
            
            if command_result.success:
                logger.info(f"rsync下载成功: {remote_dir} -> {local_dir}")
            else:
                logger.error(f"rsync下载失败: {command_result.stderr}")
            
            return command_result
            
        except Exception as e:
            logger.error(f"rsync下载失败: {str(e)}")
            return CommandResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                command="rsync",
                execution_time=0.0
            )
    
    def download_directory_as_archive(self, remote_dir: str, local_path: str,
                                     archive_format: str = "tar.gz") -> bool:
        """
        将远程文件夹打包后下载到本地
        
        Args:
            remote_dir: 远程文件夹路径
            local_path: 本地文件路径（包含文件名）
            archive_format: 压缩格式 ("tar.gz", "tar.bz2", "zip")
            
        Returns:
            bool: 下载是否成功
        """
        try:
            import tempfile
            import os
            
            # 在远程服务器创建临时压缩文件
            remote_temp_file = f"/tmp/download_archive_{int(time.time())}"
            
            # 根据格式选择压缩命令
            if archive_format == "tar.gz":
                compress_cmd = f"tar -czf {remote_temp_file}.tar.gz -C {os.path.dirname(remote_dir)} {os.path.basename(remote_dir)}"
                remote_archive = f"{remote_temp_file}.tar.gz"
            elif archive_format == "tar.bz2":
                compress_cmd = f"tar -cjf {remote_temp_file}.tar.bz2 -C {os.path.dirname(remote_dir)} {os.path.basename(remote_dir)}"
                remote_archive = f"{remote_temp_file}.tar.bz2"
            elif archive_format == "zip":
                compress_cmd = f"cd {os.path.dirname(remote_dir)} && zip -r {remote_temp_file}.zip {os.path.basename(remote_dir)}"
                remote_archive = f"{remote_temp_file}.zip"
            else:
                raise ValueError(f"不支持的压缩格式: {archive_format}")
            
            # 执行压缩命令
            logger.info(f"压缩远程文件夹: {remote_dir}")
            compress_result = self.execute_command(compress_cmd)
            if not compress_result.success:
                logger.error(f"压缩失败: {compress_result.stderr}")
                return False
            
            # 下载压缩文件
            logger.info(f"下载压缩文件: {remote_archive} -> {local_path}")
            download_success = self.download_file(remote_archive, local_path)
            
            # 清理远程临时文件
            cleanup_cmd = f"rm -f {remote_archive}"
            self.execute_command(cleanup_cmd)
            
            if download_success:
                logger.info(f"文件夹打包下载成功: {remote_dir} -> {local_path}")
            
            return download_success
            
        except Exception as e:
            logger.error(f"文件夹打包下载失败: {str(e)}")
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
    
    @staticmethod
    def execute_remote_command(hostname: str, username: str, password: str, command: str, 
                              port: int = 22, timeout: int = 30) -> CommandResult:
        """
        静态方法：执行单个远程命令
        
        Args:
            hostname: 服务器地址
            username: 用户名
            password: 密码
            command: 要执行的命令
            port: SSH端口
            timeout: 超时时间
            
        Returns:
            CommandResult: 命令执行结果
        """
        config = SSHConfig(
            hostname=hostname,
            port=port,
            username=username,
            password=password,
            timeout=timeout
        )
        
        with SSHUtil(config) as ssh:
            return ssh.execute_command(command)
    
    @staticmethod
    def execute_remote_commands(hostname: str, username: str, password: str, commands: List[str],
                               port: int = 22, timeout: int = 30) -> List[CommandResult]:
        """
        静态方法：批量执行远程命令
        
        Args:
            hostname: 服务器地址
            username: 用户名
            password: 密码
            commands: 命令列表
            port: SSH端口
            timeout: 超时时间
            
        Returns:
            List[CommandResult]: 命令执行结果列表
        """
        config = SSHConfig(
            hostname=hostname,
            port=port,
            username=username,
            password=password,
            timeout=timeout
        )
        
        with SSHUtil(config) as ssh:
            return ssh.execute_commands(commands)
    
    @staticmethod
    def download_ftp_file_to_remote(hostname: str, username: str, password: str,
                                   ftp_host: str, ftp_user: str, ftp_password: str,
                                   ftp_remote_path: str, local_path: str,
                                   ssh_port: int = 22, ftp_port: int = 21,
                                   method: str = "wget") -> CommandResult:
        """
        静态方法：从FTP服务器下载文件到远程Linux服务器
        
        Args:
            hostname: Linux服务器地址
            username: Linux服务器用户名
            password: Linux服务器密码
            ftp_host: FTP服务器地址
            ftp_user: FTP用户名
            ftp_password: FTP密码
            ftp_remote_path: FTP服务器上的文件路径
            local_path: Linux服务器上的本地路径
            ssh_port: SSH端口
            ftp_port: FTP端口
            method: 下载方法 ("wget", "curl", "lftp")
            
        Returns:
            CommandResult: 下载命令执行结果
        """
        config = SSHConfig(
            hostname=hostname,
            port=ssh_port,
            username=username,
            password=password
        )
        
        with SSHUtil(config) as ssh:
            if method == "curl":
                return ssh.download_from_ftp_with_curl(
                    ftp_host, ftp_user, ftp_password, ftp_remote_path, local_path, ftp_port
                )
            elif method == "lftp":
                return ssh.download_from_ftp_with_lftp(
                    ftp_host, ftp_user, ftp_password, ftp_remote_path, local_path, ftp_port
                )
            else:  # 默认使用wget
                return ssh.download_from_ftp(
                    ftp_host, ftp_user, ftp_password, ftp_remote_path, local_path, ftp_port
                )

    @staticmethod
    def download_remote_directory(hostname: str, username: str, password: str,
                                 remote_dir: str, local_dir: str,
                                 port: int = 22, method: str = "sftp",
                                 exclude_patterns: Optional[List[str]] = None) -> bool:
        """
        静态方法：从远程服务器下载文件夹到本地
        
        Args:
            hostname: 服务器地址
            username: 用户名
            password: 密码
            remote_dir: 远程文件夹路径
            local_dir: 本地文件夹路径
            port: SSH端口
            method: 下载方法 ("sftp", "rsync", "archive")
            exclude_patterns: 排除的文件模式列表
            
        Returns:
            bool: 下载是否成功
        """
        config = SSHConfig(
            hostname=hostname,
            port=port,
            username=username,
            password=password
        )
        
        with SSHUtil(config) as ssh:
            if method == "rsync":
                result = ssh.download_directory_with_rsync(remote_dir, local_dir, exclude_patterns)
                return result.success
            elif method == "archive":
                import os
                archive_path = os.path.join(local_dir, f"{os.path.basename(remote_dir)}.tar.gz")
                return ssh.download_directory_as_archive(remote_dir, archive_path)
            else:  # 默认使用sftp
                return ssh.download_directory(remote_dir, local_dir, exclude_patterns)


if __name__ == "__main__":
    # 使用示例
    config = SSHConfig(
        hostname="************",
        username="dnkt",
        password="dnkt$88",
        port=22,
        timeout=30
    )
    
    # 方式1：使用类
    ssh = SSHUtil(config)
    if ssh.connect():
        # result = ssh.execute_command("./xhtest/pre_pack_path_dir/emmc_checkOrca2.sh E3")
        result = ssh.execute_command("cd ./xhtest/pre_pack_path_dir && echo 'dnkt$88' | sudo -S ./emmc_checkOrca2.sh E3/")
        # result = ssh.execute_command("cd ./xhtest/pre_pack_path_dir &&  ./emmc_checkOrca2.sh E3/")
        print(f"命令输出: {result.stdout}")
        ssh.disconnect()
    
    # 方式2：使用上下文管理器
    with SSHUtil(config) as ssh:
        result = ssh.execute_command("pwd")
        print(f"当前目录: {result.stdout.strip()}")
    
    # 方式3：使用便捷函数
    # result = SSHUtil.execute_remote_command(
    #     hostname="************",
    #     username="dnkt",
    #     password="dnkt$88",
    #     command="bash ./xhtest/emmc_checkOrca2.sh E3/"
    # )
    # print(f"系统信息: {result.stdout}")


    def test_ftp_download():
        """简单的FTP下载测试"""

        # 使用wget方法下载
        print("=== 使用wget下载FTP文件 ===")
        result = SSHUtil.download_ftp_file_to_remote(
            hostname="************",
            username="dnkt",
            password="dnkt$88",
            ftp_host="************",
            ftp_user="release",
            ftp_password="release",
            ftp_remote_path="/Orca3/263D/release-build/20230106210042-V2.1.0/263D_V2.1.0_release_2_20230106210520.zip",
            local_path="/home/<USER>/xhtest/263D_V2.1.0_release_2_20230106210520.zip",
            method="wget"
        )

        print(f"wget下载结果: {'成功' if result.success else '失败'}")
        print(f"退出码: {result.exit_code}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        print("-" * 50)

    # test_ftp_download()


    def quick_download():
        """快速下载示例"""
        print("\n=== 快速下载示例 ===")

        # 最简单的调用方式
        folders_to_download = [
            ("/home/<USER>/xhtest/", "./"),
        ]

        for remote_dir, local_dir in folders_to_download:
            print(f"下载: {remote_dir} -> {local_dir}")

            success = SSHUtil.download_remote_directory(
                hostname="************",
                username="dnkt",
                password="dnkt$88",
                remote_dir=remote_dir,
                local_dir=local_dir,
                exclude_patterns=["*.log", "*.tmp"]  # 排除日志和临时文件
            )

            status = "✓" if success else "✗"
            print(f"  {status} {'成功' if success else '失败'}")

    # quick_download()

