"""
RAG相关工具模块

V字对应：
4.4 对应的V字阶段
64. 对应的V字项目

模块简介：提供RAG实例创建和查询功能。

主要功能：
1. 获取RAG实例（支持缓存和模糊匹配）
2. 带重试的RAG查询
"""

import os
import re
import asyncio
from typing import Dict, Any
from sdw_agent.service.tele_table_filler.rag_document_processor import create_rag_from_document, query_rag
from sdw_agent.service.tele_table_filler.utils.file_utils import normalize_text  # 本地导入



def get_rag_instance(spec_file_name: str, source_dir: str, config: Dict[str, Any],
                     rag_instances: Dict[str, Any]) -> Any:
    """
    获取指定规格书的RAG实例，支持模糊匹配

    Args:
        spec_file_name: 规格书名称
        source_dir: 源目录
        config: 配置字典
        rag_instances: RAG实例缓存

    Returns:
        Any: RAG实例或错误标志
    """
    if source_dir is None:
        return "FILE_DIR_NOT_FOUND"

    cache_key = f"{spec_file_name}_{config['processing']['rag']['fuzzy_match']}_{config['processing']['rag']['truncate_dash_count']}_{config['processing']['rag']['match_strategy']}"
    if cache_key in rag_instances:
        return rag_instances[cache_key]

    # 查找文件逻辑
    file_extensions = ['.xlsx', '.xlsm', '.json']
    all_files = [f for f in os.listdir(source_dir) if os.path.splitext(f)[1].lower() in file_extensions]
    matching_files = [os.path.join(source_dir, f) for f in all_files if os.path.splitext(f)[0] == spec_file_name]
    if not matching_files and config['processing']['rag']['fuzzy_match']:
        prefix = spec_file_name
        decrement_count = 0
        while '-' in prefix and decrement_count < config['processing']['rag']['truncate_dash_count']:
            prefix = prefix.rsplit('-', 1)[0]
            matching_files = [os.path.join(source_dir, f) for f in all_files if
                              os.path.splitext(f)[0].startswith(prefix)]
            if matching_files:
                break
            decrement_count += 1

    if not matching_files:
        rag_instances[cache_key] = "FILE_NOT_FOUND"
        return "FILE_NOT_FOUND"

    spec_path = matching_files[0]
    try:
        rag = create_rag_from_document(file_path=spec_path, chunk_line=config['processing']['rag']["chunk_line"], verbose=True, cache_base_dir=config['processing']['rag']['cache_base_dir'])
        rag_instances[cache_key] = rag
        return rag
    except Exception:
        rag_instances[cache_key] = "FILE_ERROR"
        return "FILE_ERROR"


async def query_rag_with_retry(rag: Any, signal_name: str, query_type: str, spec_file_name: str, config: Dict[str, Any],
                               query_cache: Dict[str, Any]) -> str:
    """
    带重试机制的RAG查询

    Args:
        rag: RAG实例
        signal_name: 信号名
        query_type: 查询类型 (timeout/recovery等)
        spec_file_name: 规格书名
        config: 配置
        query_cache: 查询缓存

    Returns:
        str: 查询结果
    """
    queries = {
        "timeout": f"""请查找信号'{signal_name}'的途絶判定時間，单位为msec。
        只返回数字，不要包含单位或其他文字说明。如果找不到确切值，详细检查上下表格，用通信周期的值x5，和3600msec比大小，取大的就是途绝时间,"NOT_FOUND"。""",
        "recovery": f"""请查找信号'{signal_name}',调取该信号出现的制御式样中excel格式通信式样描述处关于该信号的途绝判定后设置的值。
        以格式'(IGR-ON:XXX IGR-OFF:XXX)'返回。如果找不到，返回'(IGR-ON:未提及 IGR-OFF:未提及)'。""",
        "invalid": f"""请查找信号'{signal_name}'的所有有效。
        只返回值本身，用逗号分隔，不要包含说明文字。例如: 11b,01b。如果找不到相关信息，返回"NOT_FOUND"。""",
        "special": f"""请查找信号'{signal_name}'的所有Special相关描述，包括Normal、+B、+BA等条件。
        以格式'Special = 1 （Normal: +B = 0， +BA=...）'返回所有相关内容作为一个长字符串。如果找不到，返回"NOT_FOUND"。""",
        "data": f"""请查找信号'{signal_name}'的送信说明。 
        返回所有相关描述作为字符串。例如:
        パワーバックドア ブザー音量の切替表示-音量中でSW押下-PBDBZMVL=11b
        パワーバックドア ブザー音量の切替表示-音量中でSW押下-PBDBZMVL=10b
        如果找不到相关信息，返回"NOT_FOUND"。"""
    }

    hl_keywords = {
        "timeout": [signal_name, "途絶判定時間"],
        "recovery": [signal_name, "IGR-ON中", "IGR-OFF中", "通信途絶判定"],
        "invalid": [signal_name, "有效值"],
        "special": [signal_name, "Special", "Normal", "+B", "+BA"],
        "data": [signal_name, "送信说明", "发送说明", f"{signal_name}="]
    }

    ll_keywords = {
        "timeout": ["msec", "最大通信"],
        "recovery": ["IG2-ON", "IG2_OFF"],
        "invalid": [f"{signal_name}="],
        "special": ["Normal", "+B", "+BA"],
        "data": ["送信", "说明", "条件", "描述"]
    }

    if query_type not in queries:
        return None

    if config['processing']['rag']["cache_enabled"] and spec_file_name in query_cache[query_type] and signal_name in query_cache[query_type][
        spec_file_name]:
        return query_cache[query_type][spec_file_name][signal_name]

    for attempt in range(config['processing']['rag']["max_retries"] + 1):
        try:
            response = await query_rag(rag=rag, query_text=queries[query_type],
                                       hl_keywords=hl_keywords[query_type], ll_keywords=ll_keywords[query_type])
            result = response.strip() if response != "NOT_FOUND" else None
            if config['processing']['rag']["cache_enabled"]:
                if spec_file_name not in query_cache[query_type]:
                    query_cache[query_type][spec_file_name] = {}
                query_cache[query_type][spec_file_name][signal_name] = result
            return result
        except Exception:
            if attempt == config['processing']['rag']["max_retries"]:
                return None
