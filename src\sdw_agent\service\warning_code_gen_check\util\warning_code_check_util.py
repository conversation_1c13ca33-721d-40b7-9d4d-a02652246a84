"""
Warning Code Check Utilities
警告代码检查工具类
"""
import os
import traceback
from typing import Dict, <PERSON><PERSON>, Any
from loguru import logger
import xlwings as xw

from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo


class WarningCodeCheckUtils:
    """警告代码检查工具类"""

    @staticmethod
    def check_gen_warning_code_result(warning_info_dict: WarningChangeInfo) -> Tuple[bool, str]:
        """
        检查生成警告代码结果
        
        Args:
            warning_info_dict: 警告变更信息
            
        Returns:
            (是否有错误, 错误描述)
        """
        try:
            if warning_info_dict.pre_code_tools_url and warning_info_dict.after_code_tools_url:
                return False, "工具生成代码未报错"
            else:
                return True, f"工具生成代码异常,请检查, 正常:生成{warning_info_dict.pre_code_tools_url}, 其他文件生成异常"

        except Exception as e:
            logger.error(f"检查生成警告代码结果失败: {str(e)}")
            return True, f"检查失败: {str(e)}"

    @staticmethod
    def write_warning_check_to_file(check_result_info, excel_file_path):
        try:
            # 打开 Excel 文件
            app = xw.App(visible=False)
            app.display_alerts = False
            wb = app.books.open(excel_file_path)
            # 获取指定的 sheet（假设是第一个 sheet）
            sheet = wb.sheets["总览"]  # 或者使用 sheet 名称，

            # 将 result["a"][0] 的值写入 F2
            sheet.range("F2").value = "NG" if check_result_info["source_sheet_data_type_err"][0] else "OK"
            sheet.range("H2").value = check_result_info["source_sheet_data_type_err"][1]

            sheet.range("F3").value = "NG" if check_result_info["sort_err"][0] else "OK"
            sheet.range("H3").value = check_result_info["sort_err"][1]

            sheet.range("F4").value = "NG" if check_result_info["warning_property_err"][0] else "OK"
            sheet.range("H4").value = check_result_info["warning_property_err"][1]

            sheet.range("F5").value = "NG" if check_result_info["interafce_popup_sheet_data_type_err"][0] else "OK"
            sheet.range("H5").value = check_result_info["interafce_popup_sheet_data_type_err"][1]

            sheet.range("F6").value = "NG" if check_result_info["toyota_sheet_data_type_err"][0] else "OK"
            sheet.range("H6").value = check_result_info["toyota_sheet_data_type_err"][1]

            sheet.range("F7").value = "NG" if check_result_info["gen_warning_code_result_err"][0] else "OK"
            sheet.range("H7").value = check_result_info["gen_warning_code_result_err"][1]

            sheet.range("F8").value = "OK"
            sheet.range("H8").value = "参考本表各个配置表筛选内容"

            sheet.range("F9").value = "OK"
            sheet.range("H9").value = "参考本表各个配置表筛选内容"

            sheet.range("F10").value = "NG" if check_result_info["dspmnscrl_msg_max_length_err"][0] else "OK"
            sheet.range("H10").value = check_result_info["dspmnscrl_msg_max_length_err"][1].replace("False", "长度未超长")

            sheet.range("F11").value = "NG" if check_result_info["warning_num_code_exceeds_err"][0] else "OK"
            sheet.range("H11").value = check_result_info["warning_num_code_exceeds_err"][1]

            sheet.range("F13").value = "NG" if check_result_info["wrndtcfggetreq_code_err"][0] else "OK"
            sheet.range("H13").value = check_result_info["wrndtcfggetreq_code_err"][1]

            sheet.range("F14").value = "NG" if check_result_info["u4_dwtt_exceed_max_size_err"][0] else "OK"
            sheet.range("H14").value = check_result_info["u4_dwtt_exceed_max_size_err"][1]

            sheet.range("F15").value = "NG" if check_result_info["msg2_code_size_err"][0] else "OK"
            sheet.range("H15").value = check_result_info["msg2_code_size_err"][1]

            for sheet_name, (status, content) in check_result_info["code_sheet_err"].items():
                # 如果页签存在
                if sheet_name in [sheet.name for sheet in wb.sheets] and content is not None:
                    sheet = wb.sheets[sheet_name]

                    # 写入新增内容，从第 100 行开始
                    start_row = 101
                    sheet.range(f"A{100}").value = "新增"  # 写入到第一列（A列）
                    for i, item in enumerate(content["新增"]):
                        sheet.range(f"A{start_row + i}").value = item  # 写入到第一列（A列）

                    # 写入删除内容，从新增内容之后开始
                    delete_start_row = start_row + len(content["新增"]) + 1
                    sheet.range(f"A{delete_start_row}").value = "删除"
                    for j, item in enumerate(content["删除"]):
                        sheet.range(f"A{delete_start_row + j + 1}").value = item  # 写入到第一列（A列）

                    # 在最后一行写入 True 或 False，表示是否有问题
                    final_row = delete_start_row + len(content["删除"]) + 2
                    sheet.range(f"A{final_row}").value = "NG, 代码变更有冗余,请确认" if status else "代码检查OK,符合预期结果"

            # 保存修改后的文件

            # 保存文件
            directory, filename = os.path.split(excel_file_path)
            # 修改文件名
            new_filename = "检查结果" + "_" + filename
            # 合并路径
            new_path = os.path.join(directory, new_filename)
            wb.save(new_path)
            logger.success(f"代码检查完成, 结果已保存到 {new_path}")
            return new_path
        except Exception as e:
            traceback.print_exc()
            raise e
        finally:
            # 确保文件被关闭
            wb.close()
            app.quit()
            print("Excel 文件已关闭。")

    @staticmethod
    def _format_check_results(check_result_info: Dict[str, Any]) -> Dict[str, Dict]:
        """格式化检查结果"""
        formatted_results = {}

        for check_name, result in check_result_info.items():
            if isinstance(result, tuple) and len(result) >= 2:
                formatted_results[check_name] = {
                    "is_error": result[0],
                    "description": result[1],
                    "details": result[2] if len(result) > 2 else None
                }
            elif isinstance(result, dict):
                formatted_results[check_name] = result
            else:
                formatted_results[check_name] = {
                    "is_error": False,
                    "description": str(result),
                    "details": None
                }

        return formatted_results

    @staticmethod
    def replace_nan_in_dict(d):
        if isinstance(d, dict):  # 如果是字典，递归处理每个键值对
            return {key: WarningCodeCheckUtils.replace_nan_in_dict(value) for key, value in d.items()}
        elif isinstance(d, list):  # 如果是列表，递归处理列表中的每个元素
            return [WarningCodeCheckUtils.replace_nan_in_dict(item) for item in d]
        elif isinstance(d, str) and d == "nan":  # 如果是字符串类型的 "nan"，替换为空字符串
            return ""
        else:  # 其他类型保持不变
            return d
