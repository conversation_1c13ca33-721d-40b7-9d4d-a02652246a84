"""
设计工作流相关的API路由
"""
import os
import shutil
from datetime import datetime

from fastapi import APIRouter
from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.model.response_model import (
    GetKeyListRequest, GetTaskListResponse,
    StartCheckSheetTaskRequest, CheckSheetTaskResponse
)
from sdw_agent.util.import_doc_util import get_task_content
from sdw_agent.service.design_cs_workflow import DesignCSWorkflow, DesignCSInputModel
from sdw_agent.service import WorkflowStatus
from sdw_agent.service.template_manager import template_manager

router = APIRouter(prefix="/api/sdw/design", tags=["设计基准工作流"])


# 我们将使用与generate_check_sheet_sync相同的请求和响应模型
# StartCheckSheetTaskRequest和CheckSheetTaskResponse


@router.post("/analyzeKeyList",
             summary="1.获取要件一览表",
             description="",
             response_description="",
             response_model=GetTaskListResponse)
async def get_task_list(request: GetKeyListRequest):
    """获取要件一览表"""
    key_source = request.keySource
    content = get_task_content(key_source)
    return {
        "code": 0,
        "msg": "",
        "data": {
            "keyList": content
        }
    }


@router.post("/check_sheet_generate_sync",
             summary="设计基准Check Sheet分析",
             description="使用重构后的工作流分析设计基准Check Sheet Excel文件",
             response_description="返回简单的分析结果",
             response_model=CheckSheetTaskResponse)
async def analyze_design_cs(request: StartCheckSheetTaskRequest):
    """
    设计基准Check Sheet分析接口

    使用重构后的设计基准工作流分析Excel文件
    """
    try:
        # 处理模板文件路径
        if request.checkSheetFilePath:
            # 使用用户提供的文件路径
            file_path = request.checkSheetFilePath
            logger.info(f"使用用户提供的Check Sheet文件: {file_path}")
        else:
            # 使用模板管理器获取模板文件
            template_path = template_manager.get_template_path("design_cs_file")

            if not template_path:
                raise FileNotFoundError("无法获取设计基准工作流的模板文件")

            # 创建输出目录
            output_base_dir = os.path.join(ENV.config.output_data_path, "design_cs").replace("\\", "/")
            os.makedirs(output_base_dir, exist_ok=True)

            # 生成带时间戳的输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_name = os.path.splitext(os.path.basename(template_path))[0]
            output_filename = f"{template_name}_{timestamp}.xlsm"
            file_path = os.path.join(output_base_dir, output_filename).replace("\\", "/")

            # 复制模板文件到输出目录
            shutil.copy2(template_path, file_path)
            logger.info(f"模板文件已复制到: {file_path}")

        logger.info(f"开始分析设计基准Check Sheet: {file_path}")

        # 创建工作流实例
        workflow = DesignCSWorkflow()

        # 准备输入数据，让模型从配置中获取默认值
        input_data = DesignCSInputModel(
            file_path=file_path,
            target_sheet=request.targetSheetName,
            include_hidden_rows=False,  # 默认不包含隐藏行
            header_row=None,  # 使用配置中的默认值
            start_row=None,  # 使用配置中的默认值
            filter_major_category=None,
            filter_middle_category=None,
            # AI分析相关参数
            enable_ai_analysis=True,  # 启用AI分析
            ar_no=request.taskInfo.ar_no if request.taskInfo else None,
            ar_summary=request.taskInfo.req_change_content if request.taskInfo else None,
            p_no=request.taskInfo.p_no if request.taskInfo else None
        )

        # 执行工作流
        result = workflow.run(input_data)

        if result.status == WorkflowStatus.SUCCESS:
            logger.info(f"设计基准Check Sheet分析成功: {file_path}")

            return {
                "code": 0,
                "msg": "设计基准Check Sheet分析成功",
                "data": {
                    "success": True,
                    "output_file_path": file_path
                }
            }
        else:
            logger.error(f"设计基准Check Sheet分析失败: {file_path}, {result.message}")
            # 如果工作流失败且使用了内置模板，删除已复制的模板文件
            try:
                if not request.checkSheetFilePath and os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"已清理失败的输出文件: {file_path}")
            except Exception as cleanup_error:
                logger.warning(f"清理失败文件时出错: {cleanup_error}")

            return {
                "code": 1,
                "msg": f"设计基准Check Sheet分析失败: {result.message}",
                "data": {
                    "success": False,
                    "error_details": result.error if hasattr(result, 'error') else result.message
                }
            }

    except Exception as e:
        logger.exception(f"设计基准Check Sheet分析异常: {file_path if 'file_path' in locals() else 'unknown'}, {str(e)}")

        # 如果异常发生且使用了内置模板，尝试清理可能已创建的输出文件
        try:
            if not request.checkSheetFilePath and 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已清理异常时的输出文件: {file_path}")
        except Exception as cleanup_error:
            logger.warning(f"清理异常文件时出错: {cleanup_error}")

        return {
            "code": 1,
            "msg": f"设计基准Check Sheet分析异常: {str(e)}",
            "data": {
                "success": False,
                "error_details": str(e)
            }
        }