# -*- coding: utf-8 -*-
"""
@File    : test_parser.py
<AUTHOR> zhenp
@Date    : 2025-06-04 09:55
@Desc    : Description of the file
"""
import os
import time

from docparser.common.base import get_sheet_free_cells
from docparser.parsers.excel.excel_standard import ExcelStandardParser
from docparser.parsers.excel_new.base import get_sheet_free_cells as get_sheet_free_cells_new, \
    get_no_border_cells_and_table_cells, get_table_range_from_boundary_set
from docparser.parsers.excel_new.new_parser import NewParser
import pythoncom  # 导入 pythoncom 库
import win32com.client

# 重构后，和原版本解析出来的范围应该是一致的
def test_get_table_ranges():
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/test_case.xlsx")
    file_path = os.path.normpath(file_path)

    parser = ExcelStandardParser()

    parser.read_workbook(file_path)

    sheet = parser._workbook["Sheet1"]
    table_range_list, out_table_cells = parser._get_start_end_of_table(sheet)
    new_parser = NewParser(file_path)
    new_parser._cells = new_parser._parse_cells(sheet)
    new_table_ranges, new_free_cells = new_parser._get_start_end_of_table(sheet.max_row, sheet.max_column)

    assert out_table_cells == new_free_cells
    assert set(table_range_list) == set(new_table_ranges)

def test_parse_tables():
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/test_case.xlsx")
    file_path = os.path.normpath(file_path)

    parser = ExcelStandardParser()
    parser.read_workbook(file_path)
    old_res = parser.parse_document()

    new_parser = NewParser(file_path)
    new_res = new_parser.parse()

    assert old_res[0].tables[0].to_dict() == new_res.document[0].tables[0].to_dict()
    #assert old_res[0].to_dict().get("tables") == new_res.document[0].to_dict().get("tables")
