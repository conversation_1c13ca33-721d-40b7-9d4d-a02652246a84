from openpyxl import load_workbook
from typing import List, Dict, Any
from pathlib import Path

def load_excel_sheet(file_path: str, sheet_name: str, start_row: int = 8):
    """加载Excel工作表"""
    wb = load_workbook(file_path)
    return wb[sheet_name].iter_rows(min_row=start_row)

def save_excel_results(file_path: str, sheet_name: str, result_list: List[Any], field_mapping: Dict) -> str:
    """保存结果到Excel"""
    # 确保文件存在
    if not Path(file_path).exists():
        raise FileNotFoundError(f"Excel文件不存在: {file_path}")
        
    wb = load_workbook(file_path)
    
    # 确保工作表存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"工作表 '{sheet_name}' 不存在")
        
    sheet = wb[sheet_name]
    
    for result_item in result_list:
        row_obj = result_item.row if hasattr(result_item, 'row') else result_item['row']
        row_num = row_obj[0].row if isinstance(row_obj, (list, tuple)) else row_obj.row
        
        for field, col in field_mapping.items():
            value = getattr(result_item, field, None) if hasattr(result_item, field) else result_item.get(field, "")
            if value is not None:
                sheet.cell(row=row_num, column=col, value=value)

    
    new_file_path = str(Path(file_path).with_name(f"{Path(file_path).stem}_result.xlsx"))
    wb.save(new_file_path)
    return new_file_path
        
