#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : rewrite_msg_2_file.py
@Time    : 2025/7/17 19:12
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 将文本框及框内信息回填到对应文件
"""

## 以下内容待测试
# import os
# import argparse
# from openpyxl import load_workbook
# from openpyxl.drawing.image import Image
# from openpyxl.drawing.text import RichText
# from openpyxl.drawing.xdr import XDRPoint2D, XDRPositiveSize2D
# from openpyxl.utils.units import pixels_to_EMU, cm_to_EMU
# from docx import Document
# from docx.shared import Cm, Pt
# from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
# from docx.enum.shape import WD_SHAPE
# from docx.shared import RGBColor
# from PIL import Image as PILImage
# from PIL import ImageDraw, ImageFont
#
#
# def add_textbox_to_excel(file_path, sheet_name, x, y, width, height, text, fill_color, font_color='FFFFFF'):
#     """
#     在Excel的指定位置添加带颜色的文本框
#
#     Args:
#         file_path: Excel文件路径
#         sheet_name: Sheet名称
#         x, y: 文本框左上角位置（像素）
#         width, height: 文本框尺寸（像素）
#         text: 文本内容
#         fill_color: 填充颜色（RGB十六进制，如'FF0000'）
#         font_color: 文本颜色（RGB十六进制，如'FFFFFF'）
#     """
#     # 加载工作簿
#     wb = load_workbook(file_path)
#     ws = wb[sheet_name]
#
#     # 创建临时图像用于绘制文本框
#     img = PILImage.new('RGBA', (width, height), (0, 0, 0, 0))
#     draw = ImageDraw.Draw(img)
#
#     # 解析颜色
#     fill_rgb = tuple(int(fill_color[i:i + 2], 16) for i in (0, 2, 4))
#
#     # 绘制矩形
#     draw.rectangle([(0, 0), (width - 1, height - 1)], fill=fill_rgb, outline=(0, 0, 0))
#
#     # 尝试加载字体，若失败则使用默认
#     try:
#         font = ImageFont.truetype("simhei.ttf", 12)
#     except IOError:
#         try:
#             # 尝试其他常见字体
#             font = ImageFont.truetype("arial.ttf", 12)
#         except IOError:
#             # 使用默认字体
#             font = ImageFont.load_default()
#
#     # 计算文本位置以居中显示
#     text_width, text_height = draw.textsize(text, font=font)
#     text_x = (width - text_width) // 2
#     text_y = (height - text_height) // 2
#
#     # 解析文本颜色
#     font_rgb = tuple(int(font_color[i:i + 2], 16) for i in (0, 2, 4))
#
#     # 绘制文本
#     draw.text((text_x, text_y), text, font=font, fill=font_rgb)
#
#     # 保存临时图像
#     temp_img_path = "temp_textbox.png"
#     img.save(temp_img_path, "PNG")
#
#     # 将图像添加到Excel
#     img = Image(temp_img_path)
#
#     # 设置图像位置和大小
#     img.anchor = (x, y)
#     img.width = width
#     img.height = height
#
#     # 添加到指定sheet
#     ws.add_image(img)
#
#     # 保存工作簿
#     wb.save(file_path)
#
#     # 删除临时图像
#     os.remove(temp_img_path)
#
#     print(f"已在Excel的{sheet_name}中添加文本框: {text}")
#
#
# def add_textbox_to_word(file_path, x, y, width, height, text, fill_color, font_color='FFFFFF'):
#     """
#     在Word的指定位置添加带颜色的文本框
#
#     Args:
#         file_path: Word文件路径
#         x, y: 文本框左上角位置（厘米）
#         width, height: 文本框尺寸（厘米）
#         text: 文本内容
#         fill_color: 填充颜色（RGB十六进制，如'FF0000'）
#         font_color: 文本颜色（RGB十六进制，如'FFFFFF'）
#     """
#     # 加载文档
#     doc = Document(file_path)
#
#     # 解析颜色
#     fill_r = int(fill_color[0:2], 16)
#     fill_g = int(fill_color[2:4], 16)
#     fill_b = int(fill_color[4:6], 16)
#
#     font_r = int(font_color[0:2], 16)
#     font_g = int(font_color[2:4], 16)
#     font_b = int(font_color[4:6], 16)
#
#     # 创建一个新的形状
#     section = doc.sections[0]
#     header = section.header
#     header.is_linked_to_previous = True
#
#     # 添加文本框
#     txBox = header.add_shape(WD_SHAPE.RECTANGLE, Cm(x), Cm(y), Cm(width), Cm(height))
#     tf = txBox.text_frame
#
#     # 添加文本
#     p = tf.paragraphs[0]
#     p.text = text
#
#     # 设置文本格式
#     p.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
#     run = p.runs[0]
#     run.font.size = Pt(12)
#     run.font.color.rgb = RGBColor(font_r, font_g, font_b)
#
#     # 设置填充颜色
#     txBox.fill.solid()
#     txBox.fill.fore_color.rgb = RGBColor(fill_r, fill_g, fill_b)
#
#     # 设置边框
#     txBox.line.color.rgb = RGBColor(0, 0, 0)
#     txBox.line.width = Pt(1)
#
#     # 保存文档
#     doc.save(file_path)
#
#     print(f"已在Word中添加文本框: {text}")
#
#
# def main():
#     # 创建命令行参数解析器
#     parser = argparse.ArgumentParser(description="在Excel或Word的指定位置添加带颜色的文本框")
#     parser.add_argument("file_path", help="Excel或Word文件路径")
#     parser.add_argument("--sheet", help="Excel的Sheet名称（仅Excel需要）")
#     parser.add_argument("--x", type=float, required=True, help="文本框左上角x坐标（Excel:像素，Word:厘米）")
#     parser.add_argument("--y", type=float, required=True, help="文本框左上角y坐标（Excel:像素，Word:厘米）")
#     parser.add_argument("--width", type=float, required=True, help="文本框宽度（Excel:像素，Word:厘米）")
#     parser.add_argument("--height", type=float, required=True, help="文本框高度（Excel:像素，Word:厘米）")
#     parser.add_argument("--text", required=True, help="文本内容")
#     parser.add_argument("--fill_color", default="FFFF00", help="填充颜色（RGB十六进制，如'FF0000'，默认为黄色）")
#     parser.add_argument("--font_color", default="000000", help="文本颜色（RGB十六进制，如'FFFFFF'，默认为黑色）")
#
#     # 解析命令行参数
#     args = parser.parse_args()
#
#     # 检查文件类型
#     file_ext = os.path.splitext(args.file_path)[1].lower()
#
#     if file_ext == '.xlsx':
#         # 检查是否提供了sheet名称
#         if not args.sheet:
#             print("错误：处理Excel文件时需要指定--sheet参数")
#             return
#         add_textbox_to_excel(
#             args.file_path, args.sheet, args.x, args.y,
#             args.width, args.height, args.text,
#             args.fill_color, args.font_color
#         )
#     elif file_ext == '.docx':
#         add_textbox_to_word(
#             args.file_path, args.x, args.y,
#             args.width, args.height, args.text,
#             args.fill_color, args.font_color
#         )
#     else:
#         print("错误：不支持的文件类型。请提供.xlsx或.docx文件")
#
#
# if __name__ == "__main__":
#     main()

