# -*- coding: utf-8 -*-
"""
@File    : base.py.py
<AUTHOR> zhenp
@Date    : 2025-06-05 14:51
@Desc    : Description of the file
"""
from typing import Tuple, Dict, Set, List

from build.lib.docparser.models import CellObject

def get_sheet_free_cells(cells: Dict[Tuple[int,int], CellObject], max_row: int, max_column: int):
    """ get sheet free cells """
    free_cells = set()
    left_top_cell = cells.get((1,1))

    if left_top_cell.border.border_left.border_style is None or left_top_cell.border.border_top.border_style is None:
        free_cells = free_cells | set(get_no_border_cells(left_top_cell, cells, search_depth=-1))
    right_bottom_cell = cells.get((max_row,max_column))
    if (max_row, max_column) not in free_cells and (
            right_bottom_cell.border.border_right.border_style is None or right_bottom_cell.border.border_bottom.border_style is None):
        free_cells = free_cells | set(get_no_border_cells(right_bottom_cell, cells, search_depth=-1))
    return free_cells

def get_no_border_cells(cell: CellObject, cells: Dict[Tuple[int,int], CellObject], search_directions=None, search_depth=5):
    if search_directions is None:
        search_directions = ['top', 'left', 'right', 'bottom']

    if search_depth == -1:
        search_count = float('inf')
    else:
        search_count = len(search_directions) * search_depth

    visited = []
    queue = [(cell.row_index, cell.col_index)]

    while queue and search_count > 0:
        row, col = queue.pop(0)
        if (row, col) in visited:
            continue
        visited.append((row, col))
        # 查找没有边框的单元格
        # 检查上边的单元格
        if 'top' in search_directions:
            top_cell = cells.get((row-1, col))
            if top_cell is not None and \
                    (top_cell.border.border_top.border_style is None and \
                    top_cell.border.border_bottom.border_style is None):

                queue.append((row - 1, col))

        # 检查左边的单元格
        if 'left' in search_directions:
            left_cell = cells.get((row, col - 1))
            if left_cell is not None and \
                    (left_cell.border.border_left.border_style is None and \
                    left_cell.border.border_right.border_style is None):
                queue.append((row, col - 1))

        # 检查右边的单元格
        if 'right' in search_directions:
            right_cell = cells.get((row, col + 1))
            if right_cell is not None and \
                    (right_cell.border.border_left.border_style is None and \
                    right_cell.border.border_right.border_style is None):
                queue.append((row, col + 1))

        # 检查下边的单元格
        if 'bottom' in search_directions:
            bottom_cell = cells.get((row+1, col))
            if bottom_cell is not None and \
                    (bottom_cell.border.border_top.border_style is None and \
                     bottom_cell.border.border_bottom.border_style is None):
                queue.append((row + 1, col))

        search_count -= 1

    return visited

class CellSets:
    free: Set[Tuple[int, int]]
    table: Set[Tuple[int, int]]
    def __init__(self):
        free = set()
        table = set()
# 获取所有无边框的cell 和 table 的边框部分
def get_no_border_cells_and_table_cells(cells: Dict[Tuple[int,int], CellObject],max_row: int, max_column: int) -> CellSets:
    # 加一层边框 max_row + 1 和 max_column + 1，避免被有边框的cell分割，导致遍历不到的情况
    border_set:Set[Tuple[int, int]] = set()
    for col in range(1, max_column + 1):
        border_set.add((max_row + 1, col))
    for row in range(1, max_row + 1):
        border_set.add((row, max_column + 1))
    border_set.add((max_row + 1, max_column + 1))
    free_set: Set[Tuple[int, int]] = set()
    table_set: Set[Tuple[int, int]] = set()
    # 从最右下角开始遍历 (max_row + 1, max_column + 1) 肯定是没有边框
    queue = [(max_row + 1, max_column + 1)]
    while queue:
        row, col = queue.pop(0)
        if (row, col) in free_set:
            continue
        free_set.add((row, col))
        # 查找没有边框的单元格
        # 检查上边的单元格
        top_index = (row - 1, col)
        if top_index in border_set and top_index not in free_set:
            queue.append(top_index)
        else:
            top_cell = cells.get(top_index)
            if top_cell is not None and top_index not in free_set:
                if (top_cell.border.border_top.border_style is None and
                     top_cell.border.border_bottom.border_style is None):
                    queue.append(top_index)
                else:
                    table_set.add(top_index)

        # 检查左边的单元格
        left_index = (row, col - 1)
        if left_index in border_set and left_index not in free_set:
            queue.append(left_index)
        else:
            left_cell = cells.get(left_index)
            if left_cell is not None and left_index not in free_set:
                if (left_cell.border.border_left.border_style is None and
                left_cell.border.border_right.border_style is None):
                    queue.append(left_index)
                else:
                    table_set.add(left_index)

        # 检查右边的单元格
        right_index = (row, col + 1)
        if right_index in border_set and right_index not in free_set:
            queue.append(right_index)
        else:
            right_cell = cells.get(right_index)
            if right_cell is not None and right_index not in free_set:
                if (right_cell.border.border_left.border_style is None and
                     right_cell.border.border_right.border_style is None):
                    queue.append(right_index)
                else:
                    table_set.add(right_index)

        # 检查下边的单元格
        bottom_index = (row + 1, col)
        if bottom_index in border_set and bottom_index not in free_set:
            queue.append(bottom_index)
        else:
            bottom_cell = cells.get(bottom_index)
            if bottom_cell is not None and bottom_index not in free_set:
                if (bottom_cell.border.border_top.border_style is None and
                     bottom_cell.border.border_bottom.border_style is None):
                    queue.append(bottom_index)
                else:
                    table_set.add(bottom_index)
    res = CellSets()
    res.free = free_set - border_set
    res.table = table_set
    return res

def get_table_range_from_boundary_set(boundary_set: Set[Tuple[int, int]]) -> List[Tuple[Tuple[int, int], Tuple[int, int]]]:
    table_range = []
    while bool(boundary_set):
        item = next(iter(boundary_set))
        visited_set = set()
        visited_set.add(item)
        queue = [item]
        min_index = (10000000, 10000000)
        max_index = (0, 0)
        while len(queue) > 0:
            row, col = queue.pop()
            visited_set.add((row, col))
            for dr, dc in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                new_index = (row + dr, col + dc)
                if new_index in boundary_set and new_index not in visited_set:
                    if new_index[0] <= min_index[0] and new_index[1] <= min_index[1]:
                        min_index = new_index
                    if new_index[0] >= max_index[0] and new_index[1] >= max_index[1]:
                        max_index = new_index
                    queue.append(new_index)
        table_range.append((min_index, max_index))
        boundary_set = boundary_set - visited_set
    table_range = sorted(table_range, key=lambda x: x[0])
    return table_range