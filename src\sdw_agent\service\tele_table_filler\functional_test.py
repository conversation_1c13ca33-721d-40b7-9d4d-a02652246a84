import os
import asyncio
from sdw_agent.service.tele_table_filler.tele_table_filler import TeleTableFillerWorkflow
from sdw_agent.service.tele_table_filler.models import InputModel


def main():
    """
    主函数：测试tele_table_filler工作流

    这个函数创建一个TeleTableFillerWorkflow实例，准备输入数据，
    执行工作流，并打印结果。
    """
    # 初始化工作流
    workflow = TeleTableFillerWorkflow(config_path="D:\\input\\config.yaml")

    # 准备输入数据（根据您的原始代码调整路径）
    input_data = {
        "file_path": "D:\\input\\input_excel.xlsx",
        "source_dir": "D:\\input\\data\\source",
        "config": None  # 可选自定义配置
    }



    # 验证输入（可选，手动调用）
    input_model = InputModel(**input_data)
    if not workflow.validate_input(input_model):
        print("输入验证失败")
        return

    # 执行工作流
    result = workflow.execute(input_data)

    # 打印结果
    print("工作流执行结果:")
    print(f"状态: {result.status}")
    print(f"消息: {result.message}")
    if result.data:
        print("数据:")
        for key, value in result.data.items():
            print(f"  {key}: {value}")
    if result.error:
        print(f"错误: {result.error}")


if __name__ == "__main__":
    main()