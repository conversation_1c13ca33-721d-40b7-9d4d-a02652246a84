"""
RAM确认工作流数据模型

定义工作流使用的数据模型和验证模式
"""

from typing import List, Dict, Any, Optional
from pathlib import Path

from pydantic import BaseModel, Field, field_validator


class RAMConfirmConfigModel(BaseModel):
    """RAM确认配置模型"""
    
    # 基本配置
    name: str = Field(default="RAM确认")
    description: str = Field(default="根据Gerrit Diff文档，获取变更的全局变量并确认初始化时机")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")
    
    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)
    
    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)
    
    # 日志配置
    logging: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        extra = "allow"


class RepositoryInfo(BaseModel):
    """代码仓库信息"""
    repo_path: str = Field(..., description="仓库路径")
    commit_id: str = Field(..., description="提交ID")
    compared_commit_id: Optional[str] = Field(None, description="对比提交ID")
    
    @field_validator('repo_path')
    @classmethod
    def validate_repo_path(cls, v):
        path = Path(v)
        if not path.exists():
            raise ValueError(f"仓库路径不存在: {v}")
        return str(path.resolve())
    
    @field_validator('commit_id')
    @classmethod
    def validate_commit_id(cls, v):
        if not v or len(v) < 7:
            raise ValueError("提交ID格式无效")
        return v


class RAMConfirmRequest(BaseModel):
    """RAM确认请求模型"""
    repoInfo: RepositoryInfo


class RAMConfirmResult(BaseModel):
    """RAM确认结果"""
    name: str = Field(..., description="全局变量名")
    init_timing: str = Field(default="", description="初始化时机")
    init_location: str = Field(default="", description="初始化位置")
    file_path: str = Field(..., description="文件路径")
    change_func: Optional[str] = Field(None, description="变更函数")
    line: int = Field(..., ge=1, description="行号")
    content: str = Field(..., description="代码内容")
    remark: str = Field(default="", description="备注")
    result: str = Field(default="", description="判定结果")


class ProcessingResult(BaseModel):
    """处理结果"""
    ram_confirm_data: List[List[str]] = Field(..., description="RAM确认数据")
    output_file: str = Field(..., description="输出文件路径")
    statistics: Dict[str, int] = Field(default_factory=dict, description="统计信息")
    global_vars_info: List[Dict[str, Any]] = Field(default_factory=list, description="全局变量详细信息")


class GlobalVarChangeInfo(BaseModel):
    """全局变量变更信息"""
    name: str = Field(..., description="全局变量名称")
    file_path: str = Field(..., description="文件路径")
    change_func: Optional[str] = Field(None, description="变更函数名")
    line: int = Field(..., ge=1, description="行号")
    content: str = Field(..., description="代码内容")
    var_type: str = Field(..., description="变量类型")
