import pytest
from .dynamic_rule import DynamicRule

@pytest.mark.parametrize("condition,obj,expected", [
    ({"type": "always"}, {"key": "value"}, True),                        # 始终返回 True
    ({"type": "never"}, {"key": "value"}, False),                        # 始终返回 False
    ({"type": "field_exists", "field": "name"}, {"name": "Alice"}, True),  # 字段存在，返回 True
    ({"type": "field_exists", "field": "name"}, {"age": 25}, False),    # 字段不存在，返回 False
    ({"type": "field_equals", "field": "status", "value": "active"}, {"status": "active"}, True),  # 值匹配
    ({"type": "field_equals", "field": "status", "value": "active"}, {"status": "inactive"}, False),  # 值不匹配
    ({"type": "field_equals", "field": "status", "value": "active"}, {"name": "<PERSON>"}, False),      # 字段不存在
    ({"type": "field_contains", "field": "tags", "value": "python"}, {"tags": "python, unittest"}, True),  # 包含值
    ({"type": "field_contains", "field": "tags", "value": "python"}, {"tags": "java, unittest"}, False),   # 不包含值
    ({"type": "field_contains", "field": "tags", "value": "python"}, {"tags": 123}, False),               # 非字符串
    ({"type": "field_matches", "field": "email", "pattern": r"^[^@\s]+@[^@\s]+\.[^@\s]+$"}, {"email": "<EMAIL>"}, True),  # 正则匹配
    ({"type": "field_matches", "field": "email", "pattern": r"^[^@\s]+@[^@\s]+\.[^@\s]+$"}, {"email": "invalid-email"}, False),   # 不匹配正则
    ({"type": "field_matches", "field": "email", "pattern": r"^[^@\s]+@[^@\s]+\.[^@\s]+$"}, {"name": "alice"}, False),           # 字段不存在
])
def test_simple_conditions(condition, obj, expected):
    func = DynamicRule._create_condition_function(condition)
    assert func(obj) == expected

@pytest.mark.parametrize("condition,obj,expected", [
    ({"type": "and", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"name": "Bob", "status": "active"}, True),  # 两者均满足
    ({"type": "and", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"name": "Bob", "status": "inactive"}, False),  # 第二个条件不满足
    ({"type": "and", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"status": "active"}, False),  # 第一个条件不满足
])
def test_and_condition(condition, obj, expected):
    func = DynamicRule._create_condition_function(condition)
    assert func(obj) == expected

@pytest.mark.parametrize("condition,obj,expected", [
    ({"type": "or", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"name": "Alice"}, True),  # 第一个条件满足
    ({"type": "or", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"status": "active"}, True),  # 第二个条件满足
    ({"type": "or", "conditions": [
        {"type": "field_exists", "field": "name"},
        {"type": "field_equals", "field": "status", "value": "active"}
    ]}, {"age": 25}, False),  # 都不满足
])
def test_or_condition(condition, obj, expected):
    func = DynamicRule._create_condition_function(condition)
    assert func(obj) == expected


@pytest.mark.parametrize("condition,obj,expected", [
    ({"type": "not", "condition": {"type": "field_exists", "field": "admin"}}, {"name": "Alice"}, True),  # 不存在 admin
    ({"type": "not", "condition": {"type": "field_exists", "field": "admin"}}, {"admin": True}, False),   # 存在 admin
])
def test_not_condition(condition, obj, expected):
    func = DynamicRule._create_condition_function(condition)
    assert func(obj) == expected


@pytest.mark.parametrize("transform,obj,expected", [
    # identity: 不做任何转换
    ({"type": "identity"}, {"name": "Alice", "age": 30}, {"name": "Alice", "age": 30}),

    # set_field: 设置一个新的字段
    ({"type": "set_field", "field": "status", "value": "active"}, {"name": "Alice"},
     {"name": "Alice", "status": "active"}),

    # remove_field: 删除指定字段
    ({"type": "remove_field", "field": "age"}, {"name": "Alice", "age": 30}, {"name": "Alice"}),

    # rename_field: 重命名字段
    ({"type": "rename_field", "old_field": "name", "new_field": "fullname"}, {"name": "Alice", "age": 30},
     {"fullname": "Alice", "age": 30}),

    # replace_text: 正则替换
    ({"type": "replace_text", "field": "description", "pattern": r"\d+", "replacement": "XXX"},
     {"description": "User123"}, {"description": "UserXXX"}),

    # replace_text: 字段不存在或字段不是字符串
    ({"type": "replace_text", "field": "description", "pattern": r"\d+", "replacement": "XXX"},
     {"other_field": "data"}, {"other_field": "data"}),

    ({"type": "replace_text", "field": "age", "pattern": r"\d+", "replacement": "XXX"},
     {"age": 30}, {"age": 30})
])
def test_simple_transforms(transform, obj, expected):
    func = DynamicRule._create_transform_function(transform)
    assert func(obj) == expected


def test_chain_transform():
    transform = {
        "type": "chain",
        "transforms": [
            {"type": "set_field", "field": "status", "value": "active"},
            {"type": "remove_field", "field": "age"},
            {"type": "rename_field", "old_field": "name", "new_field": "fullname"}
        ]
    }
    func = DynamicRule._create_transform_function(transform)
    obj = {"name": "Alice", "age": 30}
    expected = {"fullname": "Alice", "status": "active"}
    assert func(obj) == expected


@pytest.mark.parametrize("transform,obj,expected", [
    # Chain with replace_text
    ({"type": "chain", "transforms": [
        {"type": "set_field", "field": "status", "value": "active"},
        {"type": "replace_text", "field": "description", "pattern": r"\d+", "replacement": "XXX"}
    ]},
     {"description": "User123", "name": "Alice"},
     {"description": "UserXXX", "name": "Alice", "status": "active"})
])
def test_chain_with_replace_text(transform, obj, expected):
    func = DynamicRule._create_transform_function(transform)
    assert func(obj) == expected