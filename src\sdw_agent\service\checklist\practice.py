
import json

result1 = {'思考': '变更点中提及由第0阶层变更到第1阶层，符合“Option/Content/选项”中更改类型的提示，因此变更点类别是“Option/Content/选项”，变更类型是“更改”。', '变更点类别': 'Option/Content/选项', '变更类型': '更改'}
result2 = {'思考': '变更点中提及“階層修正”，即对现有选项的阶层进行更改，符合“Option/Content/选项”中更改类型的提示，因此变更点类别是“Option/Content/选项”，变更类型是“更改”。', '变更点类别': 'Option/Content/选项', '变更类型': '更改'}
result3 = {'思考': '变更点中提及追加了「表示シーン拡張」，符合“CSTM意匠-文言”中新增类型的提示，因此变更点类别是“CSTM意匠-文言”，变更类型是“新增”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '新增'}
result4 = {'思考': '变更点中提到在6dial-View中追加ON/OFF的widget，属于新增的显示内容，符合“CSTM意匠-文言”中新增类型的提示，因此变更点类别是“CSTM意匠-文言”，变更类型是“新增”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '新增'}
result5 = {'思考': "变更点中提到'削除'，并且涉及的是一个名为'SoC A_Speed Limiter 運転状態により自動起動'的功能，属于CSTM意匠的一部分，因此变更点类别是'CSTM意匠-文言'，变更类型是'删除'。", '变更点类别': 'CSTM意匠-文言', '变更类型': '删除'}
result6 = {'思考': '变更点中提及“カスタマイズ項目削除”，即删除了某个定制项目，符合“CSTM意匠-文言”中删除类型的提示，因此变更点类别是“CSTM意匠-文言”，变更类型是“删除”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '删除'}
result7 = {'思考': "变更点中提到EEPROM記憶由'しない'变为'する'，即存储状态由'不存储'变更为'存储'，符合“EEPROM存储要否变更”类别，变更类型为'更改'。", '变更点类别': 'EEPROM存储要否变更', '变更类型': '更改'}
result8 = {'思考': '变更点中提及“削除”，并且涉及“CSTD_SoC A_パワトレ判定シート”，属于CSTM意匠-文言中的删除类型，因此变更点类别是“CSTM意匠-文言”，变更类型是“删除”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '删除'}
result9 = {'思考': "变更点中提到'非表示から表示に変更'，即由不显示变为显示，这符合'机能适用性变化'中'新增'类型的描述（由不适用变更为适用），因此变更点类别是'机能适用性变化'，变更类型是'新增'。", '变更点类别': '机能适用性变化', '变更类型': '新增'}
result10 = {'思考': "变更点中提到在'ECO'中追加'電費'，属于新增内容，符合'CSTM意匠-文言'类别中'新增'类型的描述。", '变更点类别': 'CSTM意匠-文言', '变更类型': '新增'}
result11 = {'思考': '变更点中提及“補足説明変更”，即对文言/标题进行了更改，符合“CSTM意匠-文言”中更改类型的提示，因此变更点类别是“CSTM意匠-文言”，变更类型是“更改”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '更改'}
result12 = {'思考': '变更点中提到“階層構造を変更”以及“項目名を「表示」へ変更”，分别涉及阶层结构的更改和项目名称的更改，符合“Option/Content/选项”类别中更改类型的描述，因此变更点类别是“Option/Content/选项”，变更类型是“更改”。', '变更点类别': 'Option/Content/选项', '变更类型': '更改'}
result13 = {'思考': '变更点中提及多个文言编号的修正，如【#1856】→【#5945】等，表明文言内容发生了更改，符合“CSTM意匠-文言”中更改类型的提示，因此变更点类别是“CSTM意匠-文言”，变更类型是“更改”。', '变更点类别': 'CSTM意匠-文言', '变更类型': '更改'}




def match_testcase(keywords, testcases, alpha):
    from sdw_agent.llm.model import openai_embeddings
    from scipy.spatial.distance import cosine
    testcase_texts = [f"{testcases[i].get('确认点', '')}，{testcases[i].get('前置条件', '')}" for i in range(len(testcases))]
    keyword_vectors = openai_embeddings.embed_documents(texts=keywords)
    testcase_vectors = openai_embeddings.embed_documents(texts=testcase_texts)
    cosine_similarity_matrix = [[1-cosine(keyword_vectors[i], testcase_vectors[j]) for j in range(len(testcase_vectors))] for i in range(len(keyword_vectors))]
    results = [[testcases[j] for j in range(len(testcase_vectors)) if cosine_similarity_matrix[i][j] >= alpha] for i in range(len(keywords))]
    return results












