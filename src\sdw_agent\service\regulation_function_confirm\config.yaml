# config/agent.yaml
name: "法规变更评估"
description: "分析软件变更内容与法规功能的关联性"
version: "1.0.0"
author: "SDW-Team"

# Azure OpenAI配置
azure:
  openai_api_key: "8bDH0G6IMJNnPeLphXW03aycWepLpuscIgwfkRzJPqQQV7CNGPXGJQQJ99BAACYeBjFXJ3w3AAABACOGZXC1"
  embedding_api_version: "2023-05-15"
  openai_endpoint: "https://sdw-openai.openai.azure.com"
  embedding_deployment: "text-embedding-3-large"
  openai_deployment: "gpt-4o"
  openai_api_version: "2025-02-01-preview"

# 输入输出配置
io:
  excel_extensions: [".xlsx", ".xls", ".xlsm"]

# 处理参数
processing:
  embedding:
    batch_size: 5
    max_batch_size: 100
  similarity: # ✅ 使用嵌套结构
    threshold: 0.7  # ✅ 对应 SimilarityConfig.threshol

# LLM配置
llm:
  system_prompt: "你是一个专业的汽车电子测试工程师，请以专业严谨的态度回答我的问题"
  temperature: 0.7
  max_tokens: 1024
