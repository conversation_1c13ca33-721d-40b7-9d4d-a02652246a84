"""
要件设计书照合实施工作流路由
"""
from typing import Dict, Any

from fastapi import APIRouter
from pydantic import BaseModel
from loguru import logger

from sdw_agent.model.request_model import SourceInfo
from sdw_agent.service import WorkflowStatus
from sdw_agent.service.req_design_verify_workflow import ReqDesignVerifyWorkflow, ReqDesignVerifyInputModel

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/requirement_design_verification", tags=["要件设计书照合实施工作流"])


# 定义请求和响应模型
class RequirementDesignVerificationRequest(BaseModel):
    """要件设计书照合实施请求模型"""
    NewRegTemplateSource: SourceInfo  # Excel文件路径和keySource


class RequirementDesignVerificationResponse(BaseModel):
    """要件设计书照合实施响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


@router.post("/process",
             summary="要件设计书照合实施工作流",
             description="使用工作流模式进行要件设计书照合实施",
             response_model=RequirementDesignVerificationResponse)
async def process_requirement_design_verification_workflow(request: RequirementDesignVerificationRequest):
    """
    要件设计书照合实施工作流处理函数

    Args:
        request: 请求参数，与原有接口保持一致

    Returns:
        处理结果，与原有接口保持一致
    """
    try:
        logger.info(f"开始执行要件设计书照合实施工作流")
        logger.info(f"Excel文件: {request.NewRegTemplateSource.uri}")

        # 创建工作流实例
        workflow = ReqDesignVerifyWorkflow()

        # 准备输入数据（将原有接口的字段名映射到工作流输入模型）
        input_data = ReqDesignVerifyInputModel(
            excel_source=request.NewRegTemplateSource
        )

        # 执行工作流
        result = workflow.run(input_data)

        if result.status == WorkflowStatus.SUCCESS:
            logger.info(f"要件设计书照合实施工作流执行成功: {request.NewRegTemplateSource.uri}")
            # 返回与原有接口完全一致的格式
            return {
                "code": 0,
                "msg": "要件设计书照合实施成功",
                "data": {
                    "success": True,
                    "result": {
                        "status": result.data["status"],
                        "message": result.data["message"],
                        "output_file": result.data["output_file"]
                    }
                }
            }
        else:
            logger.error(f"要件设计书照合实施工作流执行失败: {result.message}")
            return {
                "code": 1,
                "msg": f"要件设计书照合实施失败: {result.message}",
                "data": {
                    "success": False,
                    "error_details": result.error if hasattr(result, 'error') else result.message
                }
            }

    except Exception as e:
        logger.exception(f"要件设计书照合实施工作流执行异常: {str(e)}")
        return {
            "code": 1,
            "msg": f"要件设计书照合实施失败: {str(e)}",
            "data": {
                "success": False,
                "error_details": str(e)
            }
        }
