# Tele Table Filler Service

基于RAG的Excel表格自动化填充系统

## 目录结构
```bash
├── README.md        # 本文件
├── __init__.py      # 模块初始化
├── config.yaml      # 配置文件
├── functional_test.py # 功能测试用例
├── models.py        # 数据模型定义
├── rag_document_processor.py # RAG文档处理核心
├── tele_table_filler.py # 主业务逻辑
└── utils/           # 工具函数目录
```

## 功能特性
1. **智能数据提取**：通过RAG从规格书自动提取IGR/IGP、Event、Special、Timeout等字段数据
2. **表格验证**：支持输入Excel文件格式校验和完整性检查
3. **多列填充**：可同时处理多个需要填充的列
4. **报告生成**：输出填充结果文件和调试日志

## 配置说明
配置文件 `config.yaml` 包含以下核心参数：

### 输入输出配置
```yaml
io:
  excel_extensions: [".xlsx", ".xls"]  # 支持的输入格式
  source_dir: "./source"  # RAG源文件默认目录
  output:
    default_output_dir: "./output"  # 默认输出目录
    output_filename: "filled_results.xlsx" # 输出文件名
```

### 处理参数
```yaml
processing:
  rag:
    chunk_line: 10  # RAG分块行数
    max_retries: 2  # RAG查询重试次数
    batch_size: 50  # 批量处理大小
    cache_enabled: true  # 是否启用缓存
    fuzzy_match: true  # 是否启用模糊匹配
```

## 数据模型
定义了以下核心数据结构：
- `ConfigModel`：工作流配置模型，包含RAG分块行数、重试次数等参数

# 运行服务
python tele_table_filler.py --input your_file.xlsx

# 执行测试
python functional_test.py
```

## 示例代码
```python
from tele_table_filler import TableFiller

filler = TableFiller(template_path="template.xlsx")
filler.fill_data(data_source="data\\")
filler.save_output(output_path="output.xlsx")
```

