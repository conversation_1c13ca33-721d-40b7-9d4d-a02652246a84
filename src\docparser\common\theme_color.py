from colorsys import rgb_to_hls, hls_to_rgb

_RGBMAX = 0xff  # Corresponds to 255
_HLSMAX = 240  # MS excel's tint function expects that <PERSON>LS is base 240. see:


# https://social.msdn.microsoft.com/Forums/en-US/e9d8c136-6d62-4098-9b1b-dac786149f43/excel-color-tint-algorithm-incorrect?forum=os_binaryfile#d3c2ac95-52e0-476b-86f1-e2a697f24969

class ThemeColor:
    def rgb_to_ms_hls(self, red, green=None, blue=None):
        """Converts rgb values in range (0,1) or a hex string of the form '[#aa]rrggbb'
        to _HLSMAX based HLS, (alpha values are ignored)
        """
        if green is None:
            if isinstance(red, str):
                if len(red) > 6:
                    red = red[-6:]  # Ignore preceding '#' and alpha values
                blue = int(red[4:], 16) / _RGBMAX
                green = int(red[2:4], 16) / _RGBMAX
                red = int(red[0:2], 16) / _RGBMAX
            else:
                red, green, blue = red
        h, l, s = rgb_to_hls(red, green, blue)
        return int(round(h * _HLSMAX)), int(round(l * _HLSMAX)), int(round(s * _HLSMAX))

    def ms_hls_to_rgb(self, hue, lightness=None, saturation=None):
        """Converts _HLSMAX based HLS values to rgb values in the range (0,1)"""
        if lightness is None:
            hue, lightness, saturation = hue
        return hls_to_rgb(hue / _HLSMAX, lightness / _HLSMAX, saturation / _HLSMAX)

    def rgb_to_hex(self, red, green=None, blue=None):
        """Converts (0,1) based RGB values to a hex string 'rrggbb'"""
        if green is None:
            red, green, blue = red
        return ('%02x%02x%02x' % (
            int(round(red * _RGBMAX)), int(round(green * _RGBMAX)), int(round(blue * _RGBMAX)))).upper()

    def get_theme_colors(self, wb):
        """Gets theme colors from the workbook"""
        # see: https://groups.google.com/forum/#!topic/openpyxl-users/I0k3TfqNLrc
        from openpyxl.xml.functions import QName, fromstring
        xlmns = 'http://schemas.openxmlformats.org/drawingml/2006/main'
        root = fromstring(wb.loaded_theme)
        themeEl = root.find(QName(xlmns, 'themeElements').text)
        colorSchemes = themeEl.findall(QName(xlmns, 'clrScheme').text)
        firstColorScheme = colorSchemes[0]

        colors = []

        for c in ['lt1', 'dk1', 'lt2', 'dk2', 'accent1', 'accent2', 'accent3', 'accent4', 'accent5', 'accent6']:
            accent = firstColorScheme.find(QName(xlmns, c).text)

            if 'window' in accent.getchildren()[0].attrib['val']:
                colors.append(accent.getchildren()[0].attrib['lastClr'])
            else:
                colors.append(accent.getchildren()[0].attrib['val'])

        return colors

    def tint_luminance(self, tint, lum):
        """Tints a _HLSMAX based luminance"""
        # See: http://ciintelligence.blogspot.co.uk/2012/02/converting-excel-theme-color-and-tint.html
        if tint < 0:
            return int(round(lum * (1.0 + tint)))
        else:
            return int(round(lum * (1.0 - tint) + (_HLSMAX - _HLSMAX * (1.0 - tint))))

    def theme_and_tint_to_rgb(self, theme_colors, theme, tint):
        """Given a workbook, a theme number and a tint return a hex based rgb"""
        # rgb = get_theme_colors(wb)[theme]
        if theme >= len(theme_colors):
            return ""
        rgb = theme_colors[theme]
        h, l, s = self.rgb_to_ms_hls(rgb)
        return self.rgb_to_hex(self.ms_hls_to_rgb(h, self.tint_luminance(tint, l), s))
