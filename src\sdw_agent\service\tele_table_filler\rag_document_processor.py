import copy
import os
import asyncio

import logging
from typing import List, Dict, Any, Optional, Tuple

import numpy as np



from lightrag import LightRAG, QueryParam
from lightrag.utils import EmbeddingFunc
from lightrag.kg.shared_storage import initialize_pipeline_status

from sdw_agent.llm.llm_util import get_ai_message,ChatPromptTemplate
from sdw_agent.llm.model import kt_azure_embeddings, kt_azure_azure_gpt4o

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def escape_langchain_curly_braces(s: str) -> str:
    """Escape curly braces in string to prevent <PERSON><PERSON><PERSON><PERSON> from interpreting them as variables."""
    if not s:
        return s
    return s.replace('{', '{{').replace('}', '}}')


async def llm_model_func(
        prompt: str,
        system_prompt: Optional[str] = None,
        history_messages: List[Dict[str, str]] = [],
        **kwargs
) -> str:
    """调用规范的 LLM 模型（使用 model.py 和 llm_util.py）"""
    try:
        # Escape curly braces in system_prompt and history_messages to avoid unintended variable parsing
        escaped_system_prompt = escape_langchain_curly_braces(system_prompt) if system_prompt else ""
        escaped_history = [
            (msg["role"], escape_langchain_curly_braces(msg["content"]))
            for msg in history_messages
        ]

        # 使用 llm_util.py 中的 ChatPromptTemplate 构建提示模板
        template = ChatPromptTemplate.from_messages([
            ("system", escaped_system_prompt),
            *escaped_history,
            ("user", "{prompt}")
        ])

        # 使用 llm_util.py 中的 get_ai_message 调用 LLM
        # 注意：get_ai_message 是同步的，使用 asyncio.to_thread 使其异步兼容
        response = await asyncio.to_thread(
            get_ai_message,
            template=template,
            invoke_data={"prompt": prompt},
            llm_model=kt_azure_azure_gpt4o,  # 使用 model.py 中的规范 GPT-4o 模型
            auto_parse=True  # 自动解析为字符串
        )

        return str(response)  # 确保返回字符串
    except Exception as e:
        logger.error(f"LLM 调用失败: {str(e)}")
        return ""


async def embedding_func(texts: List[str]) -> np.ndarray:
    """生成文本嵌入（使用 model.py 中的规范 embedding 模型）"""
    try:
        # 使用 model.py 中的 kt_azure_embeddings 异步生成 embedding
        embeddings = await kt_azure_embeddings.aembed_documents(texts)
        return np.array(embeddings)
    except Exception as e:
        logger.error(f"Embedding 生成失败: {str(e)}")
        return np.array([])


async def initialize_rag(working_dir: str) -> LightRAG:
    """初始化RAG系统"""
    rag = LightRAG(
        working_dir=working_dir,
        llm_model_func=llm_model_func,
        embedding_func=EmbeddingFunc(
            embedding_dim=3072,  # 根据 model.py 中的 text-embedding-3-large，维度为 3072
            max_token_size=8192,
            func=embedding_func,
        ),
    )

    await rag.initialize_storages()
    await initialize_pipeline_status()
    return rag

def is_rag_cache_ready(cache_dir: str) -> bool:
    """
    判断RAG缓存目录下是否存在 index、knowledge 或 chunks 关键词的文件/文件夹
    """
    keywords = ['index', 'knowledge', 'chunks']
    if not os.path.exists(cache_dir):
        return False
    for name in os.listdir(cache_dir):
        lower = name.lower()
        if any(kw in lower for kw in keywords):
            return True
    return False

def fast_chunk_file_from_excel(file_path, chunk_line=10, choose_sheets: List[str] = []):
    """
    极速按行切chunk，无视表头、合并和样式，适合全兜底“强制模式”
    返回：chunk文本列表
    修改：添加choose_sheets参数，只处理指定工作表；如果为空，则处理所有表。
    """
    from openpyxl import load_workbook
    wb = load_workbook(file_path, data_only=True, read_only=True)
    chunks = []

    # 获取所有工作表名
    all_sheets = wb.sheetnames
    sheets_to_process = choose_sheets if choose_sheets else all_sheets  # 如果未指定，处理所有

    for sheet_name in sheets_to_process:
        if sheet_name not in all_sheets:
            logger.warning(f"工作表 '{sheet_name}' 不存在，已跳过")
            continue

        ws = wb[sheet_name]
        lines = []
        for row in ws.iter_rows(values_only=True):
            if not any([cell for cell in row]):
                continue  # 跳过全空行
            line = " | ".join([str(cell) if cell is not None else "" for cell in row])
            lines.append(line)

        if not lines:
            logger.info(f"工作表 '{sheet_name}' 无有效数据，跳过")
            continue

        for i in range(0, len(lines), chunk_line):
            chunk = "\n".join(lines[i:i + chunk_line])
            chunk = f"{sheet_name}:\n" + chunk  # 添加工作表名作为前缀
            chunks.append(chunk)

    wb.close()
    return chunks

def create_rag_from_document(
        file_path: str,
        choose_sheets: List[str] = [],  # 原参数保留，但我们会硬编码覆盖
        chunk_line: int = 20,
        single_table: bool = False,
        header_regions_override: Optional[Dict[str, Tuple[int, int, int, int]]] = None,
        max_rows: int = None,
        verbose: bool = False,
        cache_base_dir = os.path.join("D:\\", "kb", "rag_cache")
):
    """
    创建RAG对象，并自动跳过已缓存的文件
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"输入文件不存在: {file_path}")

    file_name = os.path.splitext(os.path.basename(file_path))[0]
    # cache_dir = os.path.join("./kb", "rag_cache", file_name)



    # cache_dir = os.path.join("D:\\", "kb", "rag_cache", file_name)
    cache_dir = os.path.join(cache_base_dir, file_name)

    os.makedirs(cache_dir, exist_ok=True)

    # 检查缓存，如果已存在则直接初始化RAG，无需重复处理
    if is_rag_cache_ready(cache_dir):
        if verbose:
            logger.info(f"检测到缓存({cache_dir})，跳过解析，直接加载RAG实例")
        rag = asyncio.run(initialize_rag(cache_dir))
        return rag

    # 否则，继续文件解析和RAG内容插入
    try:
        rag = asyncio.run(initialize_rag(cache_dir))
        if verbose:
            logger.info("RAG系统初始化成功")
    except Exception as e:
        raise RuntimeError(f"RAG初始化失败: {str(e)}") from e

    # 修改点：硬编码只处理特定工作表
    specific_sheets = ['TT', 'BZR', 'MID', 'Variable_MID', 'SW']
    # 如果用户传入了choose_sheets，可以合并；这里优先使用硬编码
    choose_sheets = specific_sheets  # 硬编码覆盖

    # 使用修改后的fast函数，只处理指定表
    chunks = fast_chunk_file_from_excel(file_path, chunk_line, choose_sheets=choose_sheets)

    for i, chunk in enumerate(chunks):
        if i % 10 == 0 or i == len(chunks) - 1:
            print(f"插入{i + 1}/{len(chunks)}")
        # 这里仍然可以加文件名等上下文
        rag.insert(f"下面的资料来自于文件：{file_path}。\n{chunk}\n")

    if verbose:
        logger.info("所有分块已插入RAG")
    return rag

async def query_rag(rag: LightRAG, query_text: str, hl_keywords, ll_keywords) -> str:
    """
    向RAG实例发送查询并返回结果

    参数:
        rag: LightRAG实例
        query_text: 查询文本

    返回:
        查询结果字符串
    """
    try:
        print(hl_keywords)
        response = rag.query(query_text,param=QueryParam(only_need_context=False, mode="naive", hl_keywords=hl_keywords, ll_keywords=ll_keywords,enable_rerank=False))
        # print(rag.query(query_text,param=QueryParam(only_need_context=False, mode="local", hl_keywords=hl_keywords, ll_keywords=ll_keywords)))
        # print(rag.query(query_text,param=QueryParam(only_need_context=False, mode="naive", hl_keywords=hl_keywords, ll_keywords=ll_keywords)))
        # print(rag.query(query_text,param=QueryParam(only_need_context=False, mode="mix", hl_keywords=hl_keywords, ll_keywords=ll_keywords)))

        print(response)
        return response.strip()
    except Exception as e:
        logger.error(f"RAG查询失败: {str(e)}")
        return ""

# def parse_excel_to_unified_format(file_path: str, choose_sheets: List[str] = [], max_rows: int = None) -> Dict[str, Any]:
#     """将Excel文件解析为统一的数据结构"""
#     try:
#         # 以只读模式加载工作簿
#         wb = load_workbook(file_path, data_only=True)
#     except Exception as e:
#         if "encrypted" in str(e).lower():
#             raise RuntimeError(f"无法加载Excel文件: 文件已加密或受密码保护") from e
#         raise RuntimeError(f"无法加载Excel文件: {str(e)}") from e
#
#     unified_data = {"document": []}
#
#     # 确定要处理的工作表
#     all_sheets = wb.sheetnames
#     sheets_to_process = choose_sheets if choose_sheets else all_sheets
#
#     for sheet_name in sheets_to_process:
#         if sheet_name not in all_sheets:
#             logger.warning(f"工作表 '{sheet_name}' 不存在，已跳过")
#             continue
#
#         ws = wb[sheet_name]
#         logger.info(f"正在处理工作表: {sheet_name}")
#
#         # 获取合并单元格信息
#         merged_ranges = list(ws.merged_cells.ranges) if ws.merged_cells else []
#
#         # 确定要读取的行数
#         try:
#             max_row = ws.max_row
#         except TypeError:
#             max_row = 0  # 空工作表
#
#         if max_rows and max_row > max_rows:
#             max_row = max_rows
#             logger.info(f"已限制最大行数为: {max_row}")
#
#         # 提取表格数据
#         table_rows = []
#         for row_idx in range(1, max_row + 1):
#             try:
#                 row = ws[row_idx]
#             except Exception as e:
#                 logger.warning(f"读取行 {row_idx} 时出错: {str(e)}，已跳过该行")
#                 continue
#
#             row_cells = []
#
#             for col_idx, cell in enumerate(row, 1):  # col_idx从1开始
#                 # 检查单元格是否为合并单元格的一部分
#                 merged_value = None
#                 for merged_range in merged_ranges:
#                     if cell.coordinate in merged_range:
#                         # 获取合并单元格的左上角值
#                         top_left_cell = ws.cell(row=merged_range.min_row, column=merged_range.min_col)
#                         merged_value = top_left_cell.value
#                         break
#
#                 # 确定单元格值
#                 cell_value = merged_value if merged_value is not None else cell.value
#                 cell_text = str(cell_value) if cell_value is not None else ""
#
#                 # 获取单元格样式
#                 fill_color = None
#                 if cell.fill and isinstance(cell.fill, PatternFill) and cell.fill.fgColor.rgb:
#                     fill_color = cell.fill.fgColor.rgb
#                     # 转换ARGB格式为RGB（移除第一个字符）
#                     if len(fill_color) == 8 and fill_color.startswith('FF'):
#                         fill_color = fill_color[2:]
#
#                 font_bold = cell.font.bold if cell.font else False
#                 font_size = cell.font.size if cell.font else 11  # 默认字体大小
#
#                 # 创建与JSON格式兼容的单元格数据
#                 row_cells.append({
#                     "text": cell_text,
#                     "content": [{
#                         "style": {
#                             "background_color": fill_color,
#                             "font_bold": font_bold,
#                             "font_size": font_size
#                         }
#                     }],
#                     "border": {
#                         "border_top": {"border_style": None}  # 简化边框处理
#                     }
#                 })
#
#             table_rows.append({"cells": row_cells})
#
#         # 获取最大列数
#         max_col = ws.max_column if ws.max_column else 0
#
#         # 创建工作表数据
#         sheet_data = {
#             "name": sheet_name,
#             "tables": [
#                 {
#                     "rows": table_rows,
#                     "coordinate": {
#                         "desc": f"A1:{get_column_letter(max_col)}{max_row}" if max_col > 0 and max_row > 0 else "A1:A1"},
#                     "last_coordinate": f"{get_column_letter(max_col)}{max_row}" if max_col > 0 and max_row > 0 else "A1"
#                 }
#             ],
#             "texts": []  # Excel解析中暂不提取额外文本
#         }
#
#         unified_data["document"].append(sheet_data)
#
#     wb.close()
#     return unified_data

# def excel_fill_up(json_dict: Dict[str, Any], choose_sheets: List[str] = [],header_regions_override: Optional[Dict[str, Tuple[int, int, int, int]]] = None) -> Dict[str, Any]:
#     """处理解析后的Excel/JSON数据，提取表格和相关文本"""
#     header_regions_override = header_regions_override or {}
#     file_sheets = {}
#
#     for sheet_data in json_dict['document']:
#         sheet_name = sheet_data['name']
#
#         # 如果指定了工作表且当前工作表不在列表中，则跳过
#         if choose_sheets and sheet_name not in choose_sheets:
#             continue
#
#         file_sheets[sheet_name] = {
#             'extra_text': extract_extra_text(sheet_data),
#             'header_regions': {}  # 存储每个表格的表头区域
#         }
#
#         # 处理每个表格
#         for table_idx, table in enumerate(sheet_data['tables']):
#             table_head_position = table['coordinate']['desc']
#             last_table_tail_position = sheet_data['tables'][table_idx - 1]['last_coordinate'] if table_idx > 0 else '0'
#
#             # 查找与当前表格相关的文本
#             table_related_text = find_table_related_text(
#                 file_sheets[sheet_name]['extra_text'],
#                 table_head_position,
#                 last_table_tail_position
#             )
#
#             table_key = f'table{table_idx + 1}'
#             file_sheets[sheet_name][table_key] = []
#
#             new_line = '\n'
#             # 添加表格相关文本
#             if table_related_text:
#                 file_sheets[sheet_name][table_key].append(
#                     f"{sheet_name} | {new_line.join(table_related_text)}"
#                 )
#
#             # 检测表头区域（优先使用用户指定的区域）
#             if table_key in header_regions_override:
#                 header_region = header_regions_override[table_key]
#                 logger.info(f"表格 {table_key} 使用用户指定的表头区域: {header_region}")
#             else:
#                 header_region = detect_header_region(table['rows'])
#                 logger.info(f"表格 {table_key} 自动检测到表头区域: {header_region}")
#
#             file_sheets[sheet_name]['header_regions'][table_key] = header_region
#
#             # 处理表格数据
#             header_data, data_data = process_table_data(table['rows'], header_region)
#
#             # 添加表头数据
#             for header_row in header_data:
#                 file_sheets[sheet_name][table_key].append(
#                     f"{sheet_name} | {' | '.join(header_row)}"
#                 )
#
#             # 添加数据行
#             for data_row in data_data:
#                 file_sheets[sheet_name][table_key].append(
#                     f"{sheet_name} | {' | '.join(data_row)}"
#                 )
#
#     return file_sheets

# def chunk_file(file_sheets: Dict[str, Any],chunk_line: int = 10,single_table: bool = False,force_no_header: bool = False) -> List[str]:
#     """
#     将工作表数据分块。force_no_header=True 时，无论表头识别与否，直接全表按行切chunk。
#     """
#     chunks = []
#     for sheet_name in file_sheets:
#         for table_key in file_sheets[sheet_name]:
#             # 跳过额外文本和表头区域记录
#             if table_key in ['extra_text', 'header_regions']:
#                 continue
#             table_data = file_sheets[sheet_name][table_key]
#             if not table_data:
#                 continue
#
#             # 关键改动：强制不区分header直接切分
#             if force_no_header:
#                 for i in range(0, len(table_data), chunk_line):
#                     chunk = table_data[i:i + chunk_line]
#                     chunk_str = '\n'.join(chunk)
#                     chunks.append(chunk_str + '\n')
#                 continue  # 下一个table
#
#             # ---- 下面是原有header区分分块 ----
#             header_regions_dict = file_sheets[sheet_name].get('header_regions', {})
#             header_region = header_regions_dict.get(table_key, (0, 0, 0, 0))
#             header_rows = header_region[1] - header_region[0] + 1
#             if header_rows >= len(table_data):
#                 logger.warning(f"表格 {table_key} 的表头行数({header_rows})超过数据总行数，使用默认值1")
#                 header_rows = 1
#             if single_table:
#                 chunk_size = len(table_data) - header_rows
#             else:
#                 chunk_size = chunk_line
#             headers = table_data[:header_rows]
#             data_rows = table_data[header_rows:]
#             if chunk_size <= 0:
#                 chunk = headers + data_rows
#                 chunks.append('\n'.join(chunk) + '\n')
#             else:
#                 for i in range(0, len(data_rows), chunk_size):
#                     chunk = headers + data_rows[i:i + chunk_size]
#                     chunk_str = '\n'.join(chunk)
#                     chunks.append(chunk_str + '\n')
#     return chunks

# def find_table_related_text(extra_text_list: List[tuple], table_head_position: str, last_table_tail_position: str) -> List[str]:
#     """查找与表格相关的文本"""
#     extra_text_list_copy = copy.deepcopy(extra_text_list)
#     table_head_position_row = re.search(r'\d+', table_head_position).group()
#     last_table_tail_position_row = re.search(r'\d+', last_table_tail_position).group()
#
#     table_related_text = []
#     for item in extra_text_list_copy:
#         text_row = re.search(r'\d+', item[0]).group()
#         if int(last_table_tail_position_row) < int(text_row) < int(table_head_position_row):
#             table_related_text.append(item[1])
#             extra_text_list.remove(item)
#
#     return table_related_text

# def extract_extra_text(sheet_info: Dict[str, Any]) -> List[tuple]:
#     """提取表格外的额外文本"""
#     return [(item["coordinate"]["desc"], item['text'])
#             for item in sheet_info['texts']
#             if int(item["style"]["font_size"]) > 1]

# def process_table_data(table_rows: List[Dict[str, Any]], header_region: Tuple[int, int, int, int]) -> Tuple[List[List[str]], List[List[str]]]:
#     """处理表格数据，提取表头和数据区域"""
#     start_row, end_row, start_col, end_col = header_region
#
#     # 提取表头数据
#     header_data = []
#     for row_idx in range(start_row, end_row + 1):
#         if row_idx >= len(table_rows):
#             break
#
#         row = table_rows[row_idx]
#         header_row = []
#
#         for col_idx in range(start_col, end_col + 1):
#             if col_idx < len(row['cells']):
#                 cell_text = row['cells'][col_idx]['text'].replace('\n', ' ')
#                 header_row.append(cell_text)
#             else:
#                 header_row.append("")
#
#         header_data.append(header_row)
#
#     # 提取数据区域
#     data_data = []
#     for row_idx in range(end_row + 1, len(table_rows)):
#         row = table_rows[row_idx]
#         data_row = []
#
#         for col_idx in range(start_col, end_col + 1):
#             if col_idx < len(row['cells']):
#                 cell_text = row['cells'][col_idx]['text'].replace('\n', ' ')
#                 # 处理简单的垂直合并单元格
#                 if not cell_text and data_data:
#                     prev_row = data_data[-1]
#                     if len(prev_row) > (col_idx - start_col):
#                         cell_text = prev_row[col_idx - start_col]
#                 data_row.append(cell_text)
#             else:
#                 data_row.append("")
#
#         data_data.append(data_row)
#
#     return header_data, data_data

# def detect_header_region(table_rows: List[Dict[str, Any]]) -> Tuple[int, int, int, int]:
#     """检测表头区域，返回(start_row, end_row, start_col, end_col)"""
#     if not table_rows or not table_rows[0].get('cells'):
#         return (0, 0, 0, 0)
#
#     # 提取所有单元格的特征
#     cell_features = []
#     max_cols = max(len(row['cells']) for row in table_rows) if table_rows else 0
#
#     for row_idx, row in enumerate(table_rows):
#         row_features = []
#         for col_idx, cell in enumerate(row['cells']):
#             row_features.append(extract_cell_features(cell))
#
#         # 填充空单元格
#         while len(row_features) < max_cols:
#             row_features.append({"background_color": None, "font_bold": False, "font_size": 0, "text": ""})
#
#         cell_features.append(row_features)
#
#     # 寻找左上角连续相同背景色的最大矩形区域作为表头
#     best_score = -1
#     best_region = (0, 0, 0, 0)  # 默认表头区域
#
#     # 尝试不同的起始位置，寻找最佳表头区域
#     for start_row in range(min(3, len(cell_features))):  # 最多从第3行开始找表头
#         for start_col in range(min(3, max_cols)):  # 最多从第3列开始找表头
#             if start_row >= len(cell_features) or start_col >= max_cols:
#                 continue
#
#             base_color = cell_features[start_row][start_col]["background_color"]
#             base_bold = cell_features[start_row][start_col]["font_bold"]
#
#             # 如果没有背景色且不是粗体，可能不是表头
#             if not base_color and not base_bold:
#                 continue
#
#             # 寻找结束行
#             end_row = start_row
#             while end_row + 1 < len(cell_features):
#                 if (cell_features[end_row + 1][start_col]["background_color"] == base_color and
#                         cell_features[end_row + 1][start_col]["font_bold"] == base_bold):
#                     end_row += 1
#                 else:
#                     break
#
#             # 寻找结束列
#             end_col = start_col
#             while end_col + 1 < max_cols:
#                 if (cell_features[start_row][end_col + 1]["background_color"] == base_color and
#                         cell_features[start_row][end_col + 1]["font_bold"] == base_bold):
#                     end_col += 1
#                 else:
#                     break
#
#             # 计算区域得分
#             area = (end_row - start_row + 1) * (end_col - start_col + 1)
#             position_score = 1.0 / ((start_row + 1) * (start_col + 1))  # 左上角区域得分更高
#             score = area * position_score
#
#             if score > best_score:
#                 best_score = score
#                 best_region = (start_row, end_row, start_col, end_col)
#
#     # 如果没有找到合适的表头区域，使用默认的1行1列表头
#     if best_score == -1:
#         return (0, 0, 0, 0)
#
#     return best_region

# def extract_cell_features(cell: Dict[str, Any]) -> Dict[str, Any]:
#     """提取单元格的样式特征"""
#     content = cell["content"][0] if cell["content"] else {}
#     style = content.get("style", {})
#
#     return {
#         "background_color": style.get("background_color"),
#         "font_bold": style.get("font_bold", False),
#         "font_size": int(style.get("font_size", 0)),
#         "text": cell.get("text", "").strip()
#     }



