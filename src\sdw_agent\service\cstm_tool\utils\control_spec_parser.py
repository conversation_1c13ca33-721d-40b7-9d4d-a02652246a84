import glob
import os
import re
import numpy as np
import pandas as pd

from loguru import logger


logger.bind(name="CSTM_TOOL")

def find_specific_text_rows(df, texts, start_idx=0):
    """
    在DataFrame中从指定索引开始向下查找特定文本行
    参数:
    df: pandas DataFrame
    start_idx: 开始查找的索引位置，默认为0
    返回:
    tuple: 三个文本行的索引，未找到则返回None
    """
    # 定义要查找的三个文本
    # 结果列表
    results = []

    # 从指定索引开始向下查找
    current_idx = start_idx

    for text in texts:
        # 查找从当前索引开始之后的所有包含该文本的行
        matching_rows = df.loc[current_idx:][
            df.loc[current_idx:].apply(
                lambda row: row.astype(str).str.contains(text).any(),
                axis=1
            )
        ].index

        # 如果找到匹配行，记录第一个并更新当前索引
        if len(matching_rows) > 0:
            first_match = matching_rows[0]
            results.append(first_match)
            current_idx = first_match  # 更新当前索引为找到的行
        else:
            results.append(None)  # 未找到匹配行

    return results


def parse_SW_sheet_to_json(df):
    # 1. 获取第一行作为新列名
    new_header = df.iloc[0]
    # 2. 从第二行开始加载数据
    df = df[2:]
    # 3. 设置新的列名
    df.columns = new_header
    # 4. 重置索引（可选）
    df = df.reset_index(drop=True)
    df = df.dropna(axis=0, how='all')
    df = df.dropna(axis=1, how='all')
    # 首先删除列名为空的列
    df = df.loc[:, df.columns.notna() & (df.columns != '')]

    # 找到"NO"列的位置
    try:
        no_col_index = df.columns.get_loc('No')
    except KeyError:
        logger.error("警告: 未找到'No'列，会返回原始DataFrame")

    # 删除"No"列之前的所有列
    if no_col_index > 0:
        df = df.iloc[:, no_col_index:]
    # 移除列名中的\n
    df.columns = [col.replace('\n', '') for col in df.columns]
    parse_dict = df.to_dict(orient='records')

    return parse_dict

def parse_control_spec(related_spec_doc_path: str, change_name: str, all_sheets, sheet_name: str = "SW"):
    """
    解析Excel表格中的SW页签,解析关联仕样书里面的
    参数:
        related_spec_doc_path: 仕样书 Excel文件路径
        change_name: str : 要找的需求路径
        sheet_name: 页签名称，默认为"SW"
    返回:
        包含解析结果的json，包含:
            返回一个列表，列表中的元素为字典，每个字典对应表格中的一行数据，包含以下关键信息（不同子模块字段有差异 ）：
    - 对于 メータ送信相关：
        - "No"：行编号，标识不同状态条目
        - "switch_status"：SW 状态描述（含日语和英文 ，如 "SW非押下状態 not pushed" ）
        - "system_status_display"：显示时系统状态（含日语和英文 ，如 "SW非押下状態 SW not pushed" ）
        - "execution_condition"：实施条件（如 "ACINV_SW = 0b" ）
        - "other_conditions"：其他特别条件（示例中为 "×" ，实际按业务定义 ）
    - 对于 システム側ECU送信相关：
        - "No"：行编号，标识不同显示条目
        - "display_type"：显示種類（如 "TEXT" ）
        - "switch_status_display"：SW 状態表示（含日语和英文 ，如 "日本語:ON 英語:ON" ）
        - "system_status"：システムの状態（含日语和英文 ，如 "ACインバータが機能ON状態表示 AC inverter status ON" ）
        - "execution_condition"：实施条件（如 "ACINVACT = 1b" ）
        - "other_conditions"：其他特别条件（示例中为 "×" ，实际按业务定义 ）
    """

    # 定义要查找的三个文本对应表格的文本
    texts = [
        ".ステアリングSW押下制御　（メータ送信）",
        ".SW状態応答要求",
        ".通信仕様"
    ]

    try:
        # 尝试读取Excel文件的指定页签
        # 读取指定sheet
        df = all_sheets[sheet_name]
        # 5. 找出change_name所在的行列
        mask = df.applymap(lambda x: isinstance(x, str) and change_name in x)
        rows, cols = np.where(mask)
        for r, c in zip(rows, cols):
            logger.info(f"行索引 {r}, 列索引 {c}: {df.iloc[r, c]} ")

        # 6. 判断找出的位置是正确的位置
        current_row = rows[0]

        # 7. 读取变更需求的有用信息
        control_spec_dict = {}
        texts_title = [
            ".ステアリングSW押下制御　（メータ送信）",
            ".SW状態応答要求(システム側ECU送信)",
        ]
        title_index_list = find_specific_text_rows(df, texts, start_idx=current_row)
        if None in title_index_list:
            logger.error(f"列表中包含None值:{str(title_index_list)}")
            return None
        for i in range(0, len(title_index_list) - 1):
            df_switch_meter = df[title_index_list[i] + 1:title_index_list[i + 1] - 1]
            control_spec_dict_json = parse_SW_sheet_to_json(df_switch_meter)
            control_spec_dict_key = f"5.2.{i+1} {texts_title[i].strip('.')}"
            control_spec_dict[control_spec_dict_key] = control_spec_dict_json
        logger.info(f"关联式样SW制御信息 : {control_spec_dict}")
        return control_spec_dict

    except Exception as e:
        logger.error(f"解析关联式样书 5.2章节 非标或异常 异常信息:'{str(e)}'，文件: {related_spec_doc_path}，表名: {sheet_name}")
        return None

def find_files_by_prefix(prefix: str, directory: str = "."):
    """
    根据给定前缀查找匹配的文件
    参数:
        prefix: 文件名前缀（如 "log_"）
        directory: 要搜索的目录路径，默认当前目录
    返回:
        匹配的文件路径列表（完整绝对路径）
    异常:
        FileNotFoundError: 如果指定目录不存在
        NotADirectoryError: 如果指定路径不是目录
    """
    # 验证目录是否存在且是目录
    if not os.path.exists(directory):
        raise FileNotFoundError(
            # f"{gettext('目录不存在:')}{directory}"
            f"目录不存在:{directory}"
        )
    if not os.path.isdir(directory):
        raise NotADirectoryError(
            # f"{gettext('路径不是目录:')}{directory}"
            f"路径不是目录:{directory}"
        )

    # 处理路径格式（确保跨平台兼容）
    normalized_dir = os.path.normpath(directory)
    # 构建搜索模式
    search_pattern = os.path.join(normalized_dir, "**", f"{prefix}*")
    # 查找匹配文件（使用glob.iglob提高性能）
    matched_files = []
    for file_path in glob.iglob(search_pattern, recursive=True):
        if os.path.isfile(file_path):
            matched_files.append(os.path.abspath(file_path))

    # 如果未找到任何文件，抛出FileNotFoundError
    if not matched_files:
        logger.error(f"目录:{normalized_dir} 没有找到匹配的文件：{prefix}")
    return matched_files


def parse_control_spec_by_prefix(prefix: str, directory: str = "."):
    '''
    根据前缀找到关联式样书路径
    '''
    clean_prefix = re.search(r'^(.*?)\*\*\*', prefix)
    clean_prefix = clean_prefix.group(1) if clean_prefix else prefix
    matched_files = find_files_by_prefix(clean_prefix, directory)
    if len(matched_files) > 1:
        logger.warning(f"control_spec 文件 {prefix}找到大于1个文件，默认使用最后一个{matched_files[-1]}")
    elif len(matched_files) == 1:
        return matched_files[-1]
    else:
        return None


def search_position(data, search_str):
    mask = data.map(lambda x: search_str in str(x))

    # 获取位置信息
    positions = []
    for col in data.columns:
        rows = data[mask[col]].index.tolist()
        if rows:
            positions.extend([(row, col) for row in rows])

    logger.info(f"包含 '{search_str}' 的位置: {positions}")
    return positions


def search_around_position(df, search_type, search_str, cols=(6, 8), rows=(2, 4), direction='top-left',
                           exact_match=False):
    """
    在指定位置周围搜索字符串
    
    参数:
    df: DataFrame对象
    search_type: 要定位的字符串
    search_str: 要搜索的字符串
    cols: 要搜索的列数范围，元组(min, max)
    rows: 要搜索的行数范围，元组(min, max)
    direction: 搜索方向，'top-left' 或 'bottom-right'
    """
    # 首先找到search_type的位置
    if exact_match:
        mask = df.map(lambda x: str(x) == search_type)
    else:
        mask = df.map(lambda x: search_type in str(x))
    positions = []
    for col in df.columns:
        rows_found = df[mask[col]].index.tolist()
        if rows_found:
            positions.extend([(row, col) for row in rows_found])

    if not positions:
        logger.error(f"未找到 '{search_type}'")
        return False

    # 对每个找到的位置进行搜索
    for row, col in positions:
        # 获取列索引
        col_idx = df.columns.get_loc(col)

        # 根据方向计算搜索范围
        if direction == 'top-left':
            start_col = max(0, col_idx - cols[1])
            end_col = max(0, col_idx - cols[0])
            start_row = max(0, row - rows[1])
            end_row = max(0, row - rows[0])
        else:  # bottom-right
            start_col = min(len(df.columns), col_idx + cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = min(len(df), row + rows[0])
            end_row = min(len(df), row + rows[1])

        # 在指定范围内搜索
        search_area = df.iloc[start_row:end_row + 1, start_col:end_col + 1]
        if exact_match:
            mask = search_area.map(lambda x: str(x) == search_str)
        else:
            mask = search_area.map(lambda x: search_str in str(x))

        if mask.any().any():
            found_positions = []
            for c in search_area.columns:
                rows_found = search_area[mask[c]].index.tolist()
                if rows_found:
                    found_positions.extend([(r, c) for r in rows_found])
            logger.info(f"在 '{search_type}' 的{direction}方向找到 '{search_str}' 的位置:")
            for r, c in found_positions:
                logger.info(f"行: {r}, 列: {c}")
            return True, r, c
        else:
            logger.error(f"在 '{search_type}' 的{direction}方向未找到 '{search_str}'")
            return False

def parse_detailed_spec(linked_spec_excel, spec_name: str):
    """
    通过式样书提取特殊处理
    参数:
    related_spec_doc_path: 式样书路径
    spec_name: 需要提取的需求
    """
    # temp = pd.read_excel(related_spec_doc_path, sheet_name=None)
    if "SW" not in linked_spec_excel.keys():
        logger.error("关联式样为非标准格式")
        return None
    # file = pd.read_excel(related_spec_doc_path, sheet_name="SW")
    file = linked_spec_excel['SW']
    if len(search_position(file, spec_name)) == 0:
        logger.error(f"No {spec_name} in doc")
        return None

    res = search_around_position(file, spec_name, "SW状態表示", cols=(5, 7), rows=(3, 5), direction='top-left')
    if res == True:
        logger.info("查找位置正确")
    else:
        logger.error("查找位置不正确")
        return None

    res = search_around_position(file, spec_name, "他:特別条件", cols=(1, 4), rows=(6, 8), direction='bottom-right')
    section = file[res[2]][res[1] + 1]
    sec_num = re.search(r'Section\s+(\d+-\d+)', section).group(1)

    sheet_info = file[res[2]][res[1] + 2]
    pattern = r'「(.*?)」'
    sheet_name = re.findall(pattern, sheet_info)
    if sheet_name is None:
        logger.error(f"未找到关联表名{str(sheet_info)}")
    logger.info(f"{sheet_name}")
    sheet_name = sheet_name[0]

    # file2 = pd.read_excel(related_spec_doc_path, sheet_name=sheet_name)
    file2 = linked_spec_excel[sheet_name]
    target = sec_num + '. SW制御'

    start_row = search_around_position(file2, target, "仕様", cols=(1, 2), rows=(1, 2), direction='bottom-right')[1]
    end_row = search_around_position(file2, target, "その他", cols=(1, 2), rows=(1, 15), direction='bottom-right',
                                     exact_match=True)[1]
    target_col = search_around_position(file2, target, "特別処理", cols=(1, 4), rows=(1, 3), direction='bottom-right',
                                        exact_match=True)[2]

    # 获取指定列和行范围的值
    values = []
    for row in range(start_row + 1, end_row + 1):
        value = file2[target_col][row]
        if pd.notna(value) and value != "-":  # 只添加非空值
            values.append(value)

    logger.success("特殊处理提取完成")
    return values


if __name__ == "__main__":
    # parse_control_spec(r"D:\DNKT资料\01REQ\0102Report\REQ Robot\测试数据\变更后\v4.12.d\MET-H_ACINV-CSTD-A0-03-A-C0.xlsm", "1500Wコンセント")
    # # a = parse_control_spec("MET-H_ACINV-CSTD-***", "1500Wコンセント",
    # #                                  r"C:\Users\<USER>\Desktop\SVN_NEW\Roc_Doc\01REQ\0102Report\REQ Robot\测试数据")
    # a = parse_detailed_spec(r"D:\DNKT资料\01REQ\0102Report\REQ Robot\测试数据\变更后\v4.12.d\MET-H_ACINV-CSTD-A0-03-A-C0.xlsm", "1500Wコンセント")
    # print(a)

    parse_control_spec(r'D:\\SVN\\Roc_Doc\\01REQ\\0102Report\\REQ Robot\\19PFV3需求整理\\【19PFV3】CSTM需求文档\\MET-G_T_SCREEN-CSTD-A0-08-A-C0.xlsx', "燃費")



