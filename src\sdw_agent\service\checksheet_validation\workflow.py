"""
测试代理24工作流

V字对应：
4.1 检查実施
24. CheckList記載

该模块提供测试代理功能，支持测试用例信息提取和自动填写CheckSheet。
"""

import os
import shutil
import gc
import pathlib
import asyncio
import pickle
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import Alignment
from loguru import logger

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.service.checksheet_validation.models import (
    TestAgent24ConfigModel, StatusEnum, TestCaseInfo, SheetMatchInfo, 
    MatchResult, StatusDetail, ResultOutput, ProcessingResult
)
from sdw_agent.service.checksheet_validation.utils import (
    embedding_func, get_valid_sheets, extract_sheet_rows_with_merged_cells, 
    embed_and_store_all_valid_sheets, get_top_k_semantic_matches, check_ok_ng,
    group_sheet_indices_by_status, format_sheet_status_output, 
    merge_check_from_status_indices, write_filenames_to_excel
)


@register_workflow("checksheet_validation")
class TestAgent24Workflow(BaseWorkflow):
    """
    测试代理24工作流类
    
    提供测试用例信息提取和自动填写CheckSheet功能。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化测试代理24工作流
        
        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        
        # 注册配置模型
        self.register_config_model()
        
        # 初始化状态变量
        self.checksheet_path = None
        self.target_filename = None
        self.embeddings_cache_dir = None
        self.output_path = None
        
    def register_config_model(self):
        """注册配置模型用于验证"""
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        
        # 创建临时配置管理器用于验证
        config_manager = WorkflowConfigManager(workflow_name="checksheet_validation")
        config_manager.register_schema("checksheet_validation", TestAgent24ConfigModel)
        
    def validate_input(self, 
                      checksheet_path: str, 
                      target_filename: str,
                      embeddings_cache_dir: Optional[str] = None,
                      output_path: Optional[str] = None) -> bool:
        """
        验证输入参数
        
        Args:
            checksheet_path: CheckSheet文件路径
            target_filename: 目标文件名（与CheckSheet同一目录）
            embeddings_cache_dir: 嵌入向量缓存目录，如不提供则使用配置中的默认值
            output_path: 输出文件路径，如不提供则自动生成
            
        Returns:
            bool: 验证是否通过
        """
        # 验证CheckSheet文件路径
        checksheet_path_obj = pathlib.Path(checksheet_path)
        if not checksheet_path_obj.exists():
            self.logger.error(f"CheckSheet文件不存在: {checksheet_path}")
            return False
            
        # 验证文件类型
        checksheet_ext = checksheet_path_obj.suffix.lower()
        allowed_exts = self.config.get("io", {}).get("input", {}).get("checksheet_extensions", [".xlsx", ".xls"])
        if checksheet_ext not in allowed_exts:
            self.logger.error(f"CheckSheet文件格式不支持: {checksheet_ext}，支持的格式: {allowed_exts}")
            return False
            
        # 验证目标文件是否存在
        file_dir = checksheet_path_obj.parent
        target_file_path = file_dir / target_filename
        if not target_file_path.exists():
            self.logger.error(f"目标文件不存在: {target_file_path}")
            return False
            
        # 验证目标文件类型
        target_ext = target_file_path.suffix.lower()
        allowed_target_exts = self.config.get("io", {}).get("input", {}).get("target_file_extensions", [".xlsx", ".xls"])
        if target_ext not in allowed_target_exts:
            self.logger.error(f"目标文件格式不支持: {target_ext}，支持的格式: {allowed_target_exts}")
            return False
            
        # 设置嵌入缓存目录
        if embeddings_cache_dir is None:
            embeddings_cache_dir = self.config.get("embedding", {}).get("cache_dir", "./embeddings_cache")
            
        # 创建嵌入缓存目录（如果不存在）
        embeddings_cache_dir_obj = pathlib.Path(embeddings_cache_dir)
        try:
            embeddings_cache_dir_obj.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.error(f"创建嵌入缓存目录失败: {str(e)}")
            return False
            
        # 设置输出文件路径
        if output_path is None:
            output_suffix = self.config.get("io", {}).get("output", {}).get("output_suffix", "_output")
            base = os.path.splitext(checksheet_path)[0]
            output_path = f"{base}{output_suffix}{checksheet_ext}"
            
        # 检查输出目录是否可写
        output_dir = pathlib.Path(output_path).parent
        if not os.access(output_dir, os.W_OK):
            self.logger.error(f"输出目录没有写权限: {output_dir}")
            return False
            
        # 保存参数
        self.checksheet_path = str(checksheet_path_obj)
        self.target_filename = target_filename
        self.embeddings_cache_dir = str(embeddings_cache_dir_obj)
        self.output_path = output_path
        
        return True
        
    async def execute(self, 
                    checksheet_path: str, 
                    target_filename: str,
                    embeddings_cache_dir: Optional[str] = None,
                    output_path: Optional[str] = None) -> WorkflowResult:
        """
        执行测试代理24工作流
        
        Args:
            checksheet_path: CheckSheet文件路径
            target_filename: 目标文件名（与CheckSheet同一目录）
            embeddings_cache_dir: 嵌入向量缓存目录，如不提供则使用配置中的默认值
            output_path: 输出文件路径，如不提供则自动生成
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        self.logger.info(f"开始执行测试代理24工作流")
        self.logger.info(f"CheckSheet文件: {checksheet_path}")
        self.logger.info(f"目标文件名: {target_filename}")
        self.logger.info(f"嵌入缓存目录: {embeddings_cache_dir or '使用默认值'}")
        self.logger.info(f"输出文件路径: {output_path or '自动生成'}")
        
        if not self.validate_input(checksheet_path, target_filename, embeddings_cache_dir, output_path):
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message="参数校验失败，请检查输入路径和文件名",
                error="参数校验失败"
            )

        try:
            # 1. 提取并存储目标文件的嵌入向量
            self.logger.info("步骤1: 提取并存储目标文件的嵌入向量")
            file_dir = os.path.dirname(self.checksheet_path)
            file_path = os.path.join(file_dir, self.target_filename)
            valid_sheet_names = await embed_and_store_all_valid_sheets(
                file_path, 
                self.embeddings_cache_dir,
                self.config
            )
            
            if not valid_sheet_names:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="未找到有效的工作表",
                    error="目标文件中没有符合条件的工作表（B6单元格为'No.'）"
                )
            
            # 2. 读取CheckSheet，提取内容
            self.logger.info("步骤2: 读取CheckSheet，提取内容")
            test_cases = await self._extract_checksheet_content()
            
            if not test_cases:
                return WorkflowResult(
                    status=WorkflowStatus.WARNING,
                    message="未从CheckSheet中提取到内容",
                    data={"output_path": None}
                )
                
            # 3. 执行向量检索
            self.logger.info("步骤3: 执行向量检索")
            match_results = await self._perform_vector_search(test_cases, valid_sheet_names)
                
            # 4. 处理检索结果
            self.logger.info("步骤4: 处理检索结果")
            final_results = self._process_match_results(match_results, file_path)
                
            # 5. 生成输出文件
            self.logger.info("步骤5: 生成输出文件")
            output_path = self._generate_output_file(test_cases, final_results)
                
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="测试代理24工作流执行成功",
                data={
                    "output_path": output_path,
                    "valid_sheets": list(valid_sheet_names),
                    "test_case_count": len(test_cases),
                    "result_count": len(final_results)
                }
            )
                
        except Exception as e:
            self.logger.exception(f"测试代理24工作流执行异常")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"测试代理24工作流执行失败: {str(e)}",
                error=str(e)
            )
            
    async def _extract_checksheet_content(self) -> List[TestCaseInfo]:
        """
        从CheckSheet中提取内容
        
        Returns:
            List[TestCaseInfo]: 测试用例信息列表
        """
        # 获取配置参数
        config = self.config.get("processing", {}).get("checksheet", {})
        extract_start_row = config.get("extract_start_row", 11)
        content_columns = config.get("content_columns", [3, 4])
        
        # 读取Excel
        wb = load_workbook(self.checksheet_path)
        ws = wb.active
        extract_end_row = ws.max_row
        
        # 提取内容
        test_cases = []
        for row_idx, row in enumerate(ws.iter_rows(
            min_row=extract_start_row, 
            max_row=extract_end_row, 
            min_col=min(content_columns), 
            max_col=max(content_columns), 
            values_only=True
        ), extract_start_row):
            # 提取并合并指定列的内容
            content_parts = []
            for i, col_value in enumerate(row):
                col_str = str(col_value).replace('\n', '').replace('\r', '') if col_value is not None else ''
                content_parts.append(col_str)
                
            content = ' '.join(content_parts)
            if content.strip():
                test_cases.append(TestCaseInfo(
                    row_index=row_idx,
                    content=content
                ))
                
        wb.close()
        self.logger.info(f"从CheckSheet中提取了{len(test_cases)}个测试用例")
        return test_cases
        
    async def _perform_vector_search(
        self, 
        test_cases: List[TestCaseInfo],
        valid_sheet_names: set
    ) -> List[MatchResult]:
        """
        执行向量检索
        
        Args:
            test_cases: 测试用例信息列表
            valid_sheet_names: 有效工作表名称集合
            
        Returns:
            List[MatchResult]: 匹配结果列表
        """
        # 获取配置参数
        vector_search_config = self.config.get("processing", {}).get("vector_search", {})
        similarity_threshold = vector_search_config.get("similarity_threshold", 0.5)
        top_k_global = vector_search_config.get("top_k_global", 7)
        each_file_top_k = vector_search_config.get("each_file_top_k", 10)
        
        # 获取缓存文件列表
        cache_files = [f for f in os.listdir(self.embeddings_cache_dir) if f.endswith('_embeddings.npy')]
        cache_files = [f for f in cache_files if f.replace('_embeddings.npy', '') in valid_sheet_names]
        
        # 执行向量检索
        match_results = []
        
        for test_case in test_cases:
            self.logger.info(f"检索测试用例: {test_case.content[:30]}...")
            
            # 计算查询向量
            query_emb = await embedding_func([test_case.content], self.config)
            query_emb = query_emb[0]
            
            # 在所有工作表中搜索
            all_matches = []
            
            for emb_file in cache_files:
                sheet_name = emb_file.replace('_embeddings.npy', '')
                emb_path = os.path.join(self.embeddings_cache_dir, emb_file)
                lines_path = os.path.join(self.embeddings_cache_dir, f"{sheet_name}_lines.pkl")
                
                if not os.path.exists(lines_path):
                    continue
                    
                try:
                    # 加载嵌入向量和文本行
                    embeddings = np.load(emb_path, mmap_mode='r').astype(np.float32)
                    with open(lines_path, "rb") as f:
                        lines = pickle.load(f)
                        
                    # 执行向量检索
                    matches = get_top_k_semantic_matches(
                        embeddings=embeddings,
                        query_emb=query_emb,
                        lines=lines,
                        top_k=each_file_top_k,
                        score_threshold=similarity_threshold
                    )
                    
                    # 转换结果格式
                    sheet_matches = []
                    for idx, line, score in matches:
                        sheet_matches.append(SheetMatchInfo(
                            sheet_name=sheet_name,
                            index=idx,
                            line=line,
                            similarity=score
                        ))
                        
                    all_matches.extend(sheet_matches)
                    
                    # 释放内存
                    del embeddings
                    gc.collect()
                    
                except Exception as e:
                    self.logger.error(f"处理工作表 {sheet_name} 时出错: {str(e)}")
                    continue
                    
            # 按相似度排序并取前K个
            all_matches.sort(key=lambda x: x.similarity, reverse=True)
            top_matches = all_matches[:top_k_global]
            
            # 保存匹配结果
            result = MatchResult(matches=top_matches)
            match_results.append(result)
            
        return match_results
        
    def _process_match_results(
        self, 
        match_results: List[MatchResult],
        target_file_path: str
    ) -> List[ResultOutput]:
        """
        处理匹配结果
        
        Args:
            match_results: 匹配结果列表
            target_file_path: 目标文件路径
            
        Returns:
            List[ResultOutput]: 结果输出列表
        """
        # 加载目标工作簿
        target_wb = load_workbook(target_file_path)
        
        outputs = []
        for result in match_results:
            if not result.matches:
                # 如果没有匹配结果，返回空状态
                outputs.append(ResultOutput(
                    status=StatusEnum.NONE,
                    details=[],
                    raw_matches=[]
                ))
                continue
                
            # 提取匹配信息
            raw_matches = [(match.sheet_name, int(match.index)) for match in result.matches]
            
            # 按状态分组
            sheet_to_status_indices = group_sheet_indices_by_status(raw_matches, target_wb)
            
            # 确定最终状态
            status = merge_check_from_status_indices(sheet_to_status_indices)
            
            # 准备详情信息
            details = []
            for sheet, status_dict in sheet_to_status_indices.items():
                for status_val, indices in status_dict.items():
                    if indices:
                        details.append(StatusDetail(
                            sheet_name=sheet,
                            status=status_val,
                            indices=indices
                        ))
                        
            # 创建输出结果
            output = ResultOutput(
                status=status,
                details=details,
                raw_matches=raw_matches
            )
            
            outputs.append(output)
            
        # 关闭工作簿
        target_wb.close()
        
        return outputs
        
    def _generate_output_file(
        self, 
        test_cases: List[TestCaseInfo],
        results: List[ResultOutput]
    ) -> str:
        """
        生成输出文件
        
        Args:
            test_cases: 测试用例信息列表
            results: 结果输出列表
            
        Returns:
            str: 输出文件路径
        """
        # 获取配置参数
        excel_config = self.config.get("processing", {}).get("excel_output", {})
        result_column = excel_config.get("result_column", 5)  # E列
        detail_column = excel_config.get("detail_column", 6)  # F列
        filename_row = excel_config.get("filename_row", 8)
        filename_column = excel_config.get("filename_column", 5)  # E列
        
        # 复制原文件
        shutil.copyfile(self.checksheet_path, self.output_path)
        
        # 打开输出文件
        wb = load_workbook(self.output_path)
        ws = wb.active


        # 写入文件名
        filenames = [os.path.basename(f) for f in [self.target_filename]]
        write_filenames_to_excel(ws, filenames, start_row=filename_row, start_col=filename_column)
        
        # 写入结果
        for i, (test_case, result) in enumerate(zip(test_cases, results)):
            row = test_case.row_index
            
            # 写入状态
            ws.cell(row=row, column=result_column, value=result.status.value)
            
            # 写入详情
            if result.details:
                details_cell = ws.cell(row=row, column=detail_column, value=result.details_text)
                details_cell.alignment = Alignment(wrap_text=True)
            else:
                ws.cell(row=row, column=detail_column, value='')
                
        # 保存文件
        wb.save(self.output_path)
        self.logger.info(f"已将结果写入 {self.output_path}")
        
        return self.output_path


async def main(checksheet_path: str, target_filename: str, embeddings_cache_dir: Optional[str] = None, output_path: Optional[str] = None):
    """
    主函数入口
    
    Args:
        checksheet_path: CheckSheet文件路径
        target_filename: 目标文件名（与CheckSheet同一目录）
        embeddings_cache_dir: 嵌入向量缓存目录，如不提供则使用配置中的默认值
        output_path: 输出文件路径，如不提供则自动生成
        
    Returns:
        WorkflowResult: 工作流执行结果
    """
    # 创建工作流实例
    workflow = TestAgent24Workflow()
    
    # 运行工作流
    result = await workflow.execute(checksheet_path, target_filename, embeddings_cache_dir, output_path)
    # 打印结果
    if result.status == WorkflowStatus.SUCCESS:
        print(f"测试代理24工作流执行成功")
        print(f"输出文件: {result.data['output_path']}")
        print(f"有效工作表数: {len(result.data['valid_sheets'])}")
        print(f"测试用例数: {result.data['test_case_count']}")
    else:
        print(f"测试代理24工作流执行失败: {result.message}")
        if result.error:
            print(f"错误详情: {result.error}")    
    return result 

if __name__ == "__main__":
    import asyncio
    checksheet_path = r'D:/test_file/ソフトウェアCSⅣ.xlsx'
    target_filename = "19PFv3_TestCase_Auto_MET-G_CSTMLST-CSTD-A0-06-A-C0 (全集).xlsx"  # 可替换为任意目标文件
    embeddings_cache_dir = r"D:/test_file/lightrag 3/lightrag/embeddings_cache"
    output_path = r'D:/test_file/ソフトウェアCSⅣ_output.xlsx'
    main(checksheet_path=checksheet_path,target_filename=target_filename,embeddings_cache_dir=embeddings_cache_dir,output_path=output_path)