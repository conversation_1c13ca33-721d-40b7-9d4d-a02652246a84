import json
import os
import re

# 配置文件路径（这里不包含 '@' 字符，因为这不是标准的）  
CONFIG_PATH =  os.path.join(os.path.dirname(__file__), 'config')

class Config:
    DIFF_MERGE = 0
    DIFF_NEW = 1
    DIFF_DELETE = 2
    
        
    @staticmethod
    def get_diff_path(commitid):
        return os.path.join('.diffs',commitid)
    
    @staticmethod
    def get_reslut_path(info):
        return os.path.join('.result',info)
    
    @staticmethod
    def get_repository():
        return os.path.basename(os.getcwd())
    
    def __init__(self, commitid) -> None:
        self.commitid = commitid
        self.info = {}
        self.basedir =  Config.get_diff_path(commitid)
        self.path = os.path.join(self.basedir,'info.json')
        self.logfile = os.path.join(self.basedir,'diff.log')
                       
        if os.path.exists(self.path):
            self.load()
        else:
            self.info['diffs'] = {}
            self.info['name'] = self.commitid
            self.set_author(None)
            self.set_commitmsg(None)
            self.set_previous(None)
            self.set_excel_path(None)        
            self.set_repository(Config.get_repository())
            self.set_commitdate(None)
            if not os.path.exists(self.basedir):
                os.makedirs(self.basedir)
                        
    def load(self):
        file = None
        try:
            file =  open(self.path ,encoding='utf-8') 
            self.info = json.load(file)
        except:
            pass
        finally:
            if file: 
                file.close()
        
        commitid = self.info.get('name')
        if commitid != self.commitid:
            self.info['name'] = self.commitid
        
        self.commitid_previous = self.info.get('previous')
        self.repository = self.info.get('repository')
        self.commitmsg = self.info.get('commitmsg')
        self.excel_path = self.info.get('excel_path')
        self.author = self.info.get('author')
        self.commitdate = self.info.get('commitdate')
        
        diffs = self.info.get('diffs')
        if not diffs:
            self.info['diffs'] = {}
    
    def set_repository(self, repository):
        self.repository = repository
        self.info['repository'] = repository
        
    def set_commitmsg(self, commitmsg):
        self.commitmsg = commitmsg
        self.info['commitmsg'] = commitmsg
        
    def set_commitdate(self, date):
        self.commitdate = date
        self.info['commitdate'] = date    
    
    def set_author(self, author):
        self.author = author
        self.info['author'] = author
                    
    def set_previous(self, previous):
        self.commitid_previous = previous
        self.info['previous'] = previous
                
    def set_excel_path(self, excel_path):
        self.excel_path = excel_path
        self.info['excel_path'] = excel_path

    def diffpath(self, filepath) :
        return os.path.join(self.basedir, f'{os.path.basename(filepath)}_diff.html')
    
    def add(self, filepath, diffpath,difftype):
        self.info['diffs'][filepath] = {
            'diff': diffpath,
            'type': difftype
        }
    
    def save(self):        
        with open(self.path, mode="w", encoding='utf-8') as f:
            json.dump(self.info, f, indent=4)
    
    
    def is_finished(self, previous):
        return previous == self.commitid_previous and  self.excel_path and os.path.exists(self.excel_path)

class BCompConfig:
    TEXT_CONFIG_FILE = os.path.join(CONFIG_PATH, 'bc4_text.config') 
    BIN_CONFIG_FILE = os.path.join(CONFIG_PATH, 'bc4_bin.config')
    FOLDER_CONFIG_FILE = os.path.join(CONFIG_PATH, 'bc4_folder.config') 
    EMPTY_FILE = os.path.join(CONFIG_PATH, 'empty.txt') 
        
    def __init__(self) -> None:
        with open(os.path.join(CONFIG_PATH, 'config.json'), 'r') as f:
            self.settings = json.load(f)
            
    def get_path(self):
        return self.settings['BcompPath']
    
    def is_ignored(self, filename:str):
        binlist = self.settings['IgnorePattern']
        for bin in binlist:
            if re.match(bin,filename):
                return True
        return False
    
    def is_binary(self, filename:str):
        binlist = self.settings['BinPattern']
        for bin in binlist:
            if re.match(bin,filename):
                return True
        return False
    
    def get_config(self, filename:str):
        if self.is_binary(filename):
            return self.BIN_CONFIG_FILE
        else:
            return self.TEXT_CONFIG_FILE
        