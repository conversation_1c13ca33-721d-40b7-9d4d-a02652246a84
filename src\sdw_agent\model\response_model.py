from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Union, Any


class SourceInfo(BaseModel):
    type: str = Field(..., examples=['local', 'svn', 'git'], description="文件路径")  # local, svn, git
    uri: str = Field(..., examples=['C:/xxx/xx.xlsx', 'http://xxx/svn_path', 'http://xxx/git_path'],
                     description="文件路径")
    uuid: Optional[str] = ''


class GetKeyListRequest(BaseModel):
    keySource: SourceInfo


class TaskInfo(BaseModel):
    row_idx: int
    uuid: Optional[str] = ''
    ar_no: str
    ar_title: str
    ar_link: str
    epic_name: str
    req_change_content: str
    p_no: str  # 要件票号

class TeleTableFillerResponse(BaseModel):
    code: int = 0
    message: str = ''
    data: str = ''


class GetTaskListResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Dict[str, List[TaskInfo]]


class StartCheckSheetTaskRequest(BaseModel):
    """
    1. 要件一览表
    2. 基本设计sheet
    3. 详细设计sheet
    """
    targetSheetName: str
    taskInfo: TaskInfo
    checkSheetFilePath: Optional[str] = None  # check sheet文件路径，可选，不提供时使用内置模板


class CheckSheetTaskResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


class TaskSubmitResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Dict[str, str]  # {"task_id": "xxx"}


class TaskStatusResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Dict[str, str]  # {"task_id": "xxx", "status": "xxx", "result": "xxx", "error_message": "xxx"}


class TaskStatusRequest(BaseModel):
    task_id: str


class FileCompareRequest(BaseModel):
    input_path: Union[str, List[str]]


class FileCompareResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: List[str]


class DesignPeerReviewResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str


class ChangePointImportResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: list


class TestViewFileParserResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str


class TestViewGenerateResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: dict


class TestViewSuppleResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: dict


class RamGlobalVarResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Dict[str, str|None]


class UploadBugCommitResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: dict


class ChecklistReviewResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str

class NewChangeListUpdateRequest(BaseModel):
    input_path: str


class NewChangeListUpdateResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str 
class DnCodecheckResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str

class OSSExaminationResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str

class ChecksheetResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Optional[str] = None

class DscsCodeCheckResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: List[str]
