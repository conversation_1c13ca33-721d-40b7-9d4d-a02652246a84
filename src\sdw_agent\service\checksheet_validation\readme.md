# Test Agent 24 模块

## 概述

Test Agent 24 模块是V字流24号样例的实现，对应V字流中的"24. CheckList記載"和"4.1 检查実施"。该模块提供测试用例信息提取和自动填写CheckSheet功能，通过向量检索技术实现智能化的测试用例匹配和状态判断。

## 功能特点

- **智能向量检索**: 使用Azure OpenAI嵌入模型进行语义相似度匹配
- **CheckSheet自动填写**: 根据测试用例内容自动填充CheckSheet
- **状态智能判断**: 基于匹配结果自动判断OK/NG/O状态
- **Excel文件处理**: 支持.xlsx和.xls格式的Excel文件
- **批量处理**: 支持多个工作表的批量处理
- **缓存机制**: 嵌入向量缓存提高处理效率

## 文件结构

```
test_agent_24/
├── readme.md              # 本说明文档
├── workflow.py            # 主要工作流实现
├── models.py              # 数据模型定义
├── utils.py               # 工具函数和辅助功能
├── config.yaml            # 配置文件
├── __init__.py            # 模块初始化文件
└── __pycache__/           # Python缓存目录
```

## 核心文件说明

### workflow.py
- **功能**: 主要工作流实现，包含完整的业务逻辑
- **主要类**: `TestAgent24Workflow`
- **核心方法**: 
  - `execute()`: 执行工作流主逻辑
  - `_extract_checksheet_content()`: 提取CheckSheet内容
  - `_perform_vector_search()`: 执行向量检索
  - `_process_match_results()`: 处理匹配结果
  - `_generate_output_file()`: 生成输出文件

### models.py
- **功能**: 定义数据模型和枚举类型
- **主要类**:
  - `TestAgent24ConfigModel`: 配置模型
  - `StatusEnum`: 状态枚举 (OK/NG/O/-)
  - `TestCaseInfo`: 测试用例信息
  - `SheetMatchInfo`: Sheet匹配信息
  - `MatchResult`: 匹配结果
  - `StatusDetail`: 状态详情
  - `ResultOutput`: 结果输出
  - `ProcessingResult`: 处理结果

### utils.py
- **功能**: 工具函数和辅助功能
- **主要函数**:
  - `embedding_func()`: 文本向量化
  - `get_valid_sheets()`: 获取有效工作表
  - `extract_sheet_rows_with_merged_cells()`: 提取工作表内容
  - `embed_and_store_all_valid_sheets()`: 嵌入并存储所有有效工作表
  - `get_top_k_semantic_matches()`: 获取语义匹配结果
  - `check_ok_ng()`: 检查OK/NG状态
  - `group_sheet_indices_by_status()`: 按状态分组
  - `write_filenames_to_excel()`: 写入文件名到Excel

### config.yaml
- **功能**: 模块配置文件
- **配置项**:
  - `io`: 输入输出配置
  - `processing`: 处理参数配置
  - `embedding`: 嵌入配置
  - `azure`: Azure OpenAI配置
  - `logging`: 日志配置

## 工作流程

1. **输入验证**: 验证CheckSheet文件和目标文件的有效性
2. **内容提取**: 从CheckSheet中提取测试用例信息（第11行开始，C、D列）
3. **向量化处理**: 对目标文件的所有有效工作表进行向量化
4. **语义匹配**: 对每个测试用例进行向量检索，找到最相似的内容
5. **状态判断**: 根据匹配结果判断每个测试用例的状态（OK/NG/O）
6. **结果输出**: 生成包含填写结果的Excel文件

## 状态判断逻辑

- **OK**: 匹配到状态为"OK"的内容
- **NG**: 匹配到状态为"NG"的内容  
- **O**: 匹配到其他状态或默认状态
- **优先级**: O > NG > OK > -

## 使用示例

```python
from sdw_agent.service.test_agent_24.workflow import main

# 执行CheckSheet对齐
result = await main(
    checksheet_path="path/to/checksheet.xlsx",
    target_filename="target_file.xlsx",
    embeddings_cache_dir="./embeddings_cache",
    output_path="output.xlsx"
)

# 处理结果
if result.status == WorkflowStatus.SUCCESS:
    print(f"输出文件: {result.data.get('output_path')}")
else:
    print(f"处理失败: {result.message}")
```

## API接口

该模块通过 `checksheet_testcase.py` 路由提供RESTful API接口：

- **POST** `/api/sdw/cs24/run_checksheet`
  - 功能: 对指定checksheet和目标embedding文件进行检索和写入
  - 输入: 
    - `checksheet_path`: CheckSheet文件路径
    - `target_filename`: 目标文件名列表
  - 输出: 生成的Excel文件路径

## 技术栈

- **Python**: 主要开发语言
- **FastAPI**: Web框架
- **Azure OpenAI**: 嵌入模型服务
- **openpyxl**: Excel文件处理
- **numpy**: 数值计算
- **PyYAML**: 配置文件处理
- **Pydantic**: 数据验证

## 配置说明

### 重要配置参数

- **similarity_threshold**: 相似度阈值 (默认: 0.5)
- **top_k_global**: 全局前K个结果 (默认: 7)
- **each_file_top_k**: 每个文件前K个结果 (默认: 10)
- **extract_start_row**: 开始提取内容的行号 (默认: 11)
- **content_columns**: 提取内容的列号 (默认: [3, 4])

### Azure OpenAI配置

需要配置以下参数：
- `openai_api_key`: API密钥
- `openai_endpoint`: 端点地址
- `embedding_deployment`: 嵌入模型部署名称
- `embedding_api_version`: API版本

## 注意事项

1. 使用前需要配置有效的Azure OpenAI API密钥
2. 确保CheckSheet文件格式正确（第11行开始有内容）
3. 目标文件需要包含有效的工作表（B6单元格为'No.'）
4. 嵌入向量会缓存到指定目录，提高后续处理效率
5. 支持的文件格式: `.xlsx`, `.xls`

## 输出文件格式

生成的输出文件包含以下列：
- **A-D列**: 原始CheckSheet内容
- **E列**: 判断结果 (OK/NG/O)
- **F列**: 详细匹配信息
- **第8行E列**: 目标文件名

---

**V字流24号样例**: 本模块是V字流开发流程中第24号样例的实现，对应"24. CheckList記載"和"4.1 检查実施"，前端接口为checksheet_testcase，展示了如何构建智能化的测试用例匹配和CheckSheet自动填写系统。
