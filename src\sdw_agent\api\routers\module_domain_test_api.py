"""
模块域测试API路由器

V字对应：
4.1 単体検査
52. モジュール単体検査実施＆結果検証
53. ドメイン単体検査実施＆結果検証

该模块提供模块和域单体测试相关的API接口，用于根据要件一览表填写CheckListSheet。
主要功能：
1. 接收模块域测试文件信息
2. 调用服务层生成测试用例
3. 返回生成的CheckListSheet文件路径

作者: SDW Agent Team
创建时间: 2024
最后修改: 2024
"""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field, field_validator

from sdw_agent.model.response_model import SourceInfo
from sdw_agent.service.module_domain_test.module_domain_test import ModuleDomainService

# 配置日志记录器
logger = logging.getLogger(__name__)

# 常量定义
class APIConstants:
    """API相关常量"""
    SUCCESS_CODE = 0
    SUCCESS_MESSAGE = "处理成功"

    # 错误消息
    EMPTY_URI_ERROR = "输入文件URI不能为空"
    INVALID_FILE_ERROR = "输入文件不存在或无法访问"
    GENERATION_FAILED_ERROR = "生成结果文件失败"
    INTERNAL_ERROR = "服务器内部错误，请稍后重试"

# 创建API路由器，设置前缀和标签
router = APIRouter(prefix="/api/sdw/design", tags=[
    "4.1 単体検査",
    "52. モジュール単体検査実施＆結果検証",
    "53. ドメイン単体検査実施＆結果検証",
])


class ModuleDomainTestRequest(BaseModel):
    """
    模块域测试请求模型

    用于接收客户端发送的模块域测试文件信息。
    包含必要的文件路径验证逻辑。
    """
    module_domain_test_file: SourceInfo = Field(
        ...,
        description="模块域测试文件信息，包含文件URI等元数据"
    )

    @field_validator('module_domain_test_file')
    @classmethod
    def validate_test_file(cls, v):
        """
        验证测试文件信息的完整性

        Args:
            v: SourceInfo对象，包含文件信息

        Returns:
            验证通过的SourceInfo对象

        Raises:
            ValueError: 当文件信息不完整时抛出
        """
        if not v:
            raise ValueError("测试文件信息不能为空")
        if not v.uri or not v.uri.strip():
            raise ValueError(APIConstants.EMPTY_URI_ERROR)
        return v


class ResultFile(BaseModel):
    """
    结果文件模型

    用于封装生成的测试用例文件路径信息。
    """
    result_file: str = Field(
        ...,
        examples=['C:/workspace/generated_test_cases.xlsx'],
        description="生成的测试用例文件的完整路径"
    )

    @field_validator('result_file')
    @classmethod
    def validate_result_file_path(cls, v):
        """
        验证结果文件路径的有效性

        Args:
            v: 文件路径字符串

        Returns:
            清理后的文件路径

        Raises:
            ValueError: 当路径为空时抛出
        """
        if not v or not v.strip():
            raise ValueError("结果文件路径不能为空")
        return v.strip()


class ResultResponse(BaseModel):
    """
    统一的API响应模型

    提供标准化的响应格式，包含状态码、消息和数据。
    遵循统一的错误处理规范。
    """
    code: int = Field(
        APIConstants.SUCCESS_CODE,
        description="状态码，0表示成功，非0表示失败"
    )
    msg: str = Field(
        APIConstants.SUCCESS_MESSAGE,
        description="状态消息，成功时显示成功信息，失败时包含错误详情"
    )
    data: Optional[ResultFile] = Field(
        None,
        description="结果数据，仅在处理成功时返回文件信息"
    )


def _validate_input_file(file_uri: str) -> None:
    """
    验证输入文件的有效性

    Args:
        file_uri: 文件URI路径

    Raises:
        HTTPException: 当文件无效时抛出HTTP异常
    """
    if not file_uri or not file_uri.strip():
        logger.error("输入文件URI为空")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=APIConstants.EMPTY_URI_ERROR
        )

    # 记录文件验证信息
    logger.debug(f"验证输入文件: {file_uri}")


def _create_success_response(result_file_path: str) -> ResultResponse:
    """
    创建成功响应对象

    Args:
        result_file_path: 生成的结果文件路径

    Returns:
        包含成功信息的响应对象

    Raises:
        HTTPException: 当结果文件路径无效时抛出
    """
    if not result_file_path:
        logger.error("生成的结果文件路径为空")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=APIConstants.GENERATION_FAILED_ERROR
        )

    response_data = ResultFile(result_file=result_file_path)
    logger.info(f"成功生成测试用例文件: {result_file_path}")

    return ResultResponse(
        code=APIConstants.SUCCESS_CODE,
        msg=APIConstants.SUCCESS_MESSAGE,
        data=response_data
    )


@router.post(
    "/module_domain_test",
    summary="根据要件一览表填写CheckListSheet",
    description="""
    根据输入的模块域测试文件，生成对应的CheckListSheet文件。

    功能说明：
    1. 解析模块域测试文件中的参数信息
    2. 基于参数范围生成笛卡尔积测试用例
    3. 将测试用例填写到CheckListSheet模板中
    4. 返回生成的文件路径

    支持的文件格式：Excel (.xlsx)
    """,
    response_model=ResultResponse,
    responses={
        200: {
            "description": "成功返回CheckListSheet文件路径",
            "content": {
                "application/json": {
                    "example": {
                        "code": 0,
                        "msg": "处理成功",
                        "data": {
                            "result_file": "C:/workspace/generated_test_cases.xlsx"
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误或验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "输入文件URI不能为空"
                    }
                }
            }
        },
        422: {"description": "请求数据格式错误"},
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "服务器内部错误，请稍后重试"
                    }
                }
            }
        }
    }
)
async def module_domain_test_handler(request: ModuleDomainTestRequest) -> ResultResponse:
    """
    模块域测试处理接口

    这是模块域测试功能的主要入口点，负责协调整个测试用例生成流程。

    处理流程：
    1. 验证请求参数的有效性
    2. 调用服务层生成测试用例
    3. 验证生成结果
    4. 返回标准化响应

    Args:
        request: 包含模块域测试文件信息的请求对象

    Returns:
        ResultResponse: 包含处理结果和生成文件路径的响应对象

    Raises:
        HTTPException: 当请求参数无效或处理过程中发生错误时抛出
    """
    # 记录请求开始信息
    file_uri = request.module_domain_test_file.uri
    logger.info(f"开始处理模块域测试请求")
    logger.info(f"输入文件URI: {file_uri}")
    logger.debug(f"请求对象详情: {request.dict()}")

    try:
        # 验证输入文件
        _validate_input_file(file_uri)

        # 调用服务层处理
        logger.info("开始调用服务层生成测试用例")
        service = ModuleDomainService(file_uri, file_uri)

        # 执行测试用例生成
        generation_result = service.generate_test_cases()
        logger.info(f"服务层处理完成，结果: {generation_result}")

        # 创建成功响应
        return _create_success_response(file_uri)

    except HTTPException:
        # 重新抛出HTTP异常，保持原有的错误信息
        logger.warning("处理过程中发生HTTP异常，重新抛出")
        raise

    except ValueError as ve:
        # 处理数据验证错误
        error_msg = f"数据验证失败: {str(ve)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )

    except FileNotFoundError as fe:
        # 处理文件不存在错误
        error_msg = f"文件不存在: {str(fe)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=APIConstants.INVALID_FILE_ERROR
        )

    except PermissionError as pe:
        # 处理文件权限错误
        error_msg = f"文件访问权限不足: {str(pe)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="文件访问权限不足，请检查文件权限设置"
        )

    except Exception as e:
        # 处理其他未预期的错误
        error_msg = f"模块域测试处理过程中发生未预期错误: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=APIConstants.INTERNAL_ERROR
        )
