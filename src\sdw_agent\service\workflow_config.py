"""
工作流配置管理模块

负责工作流配置的加载、验证和访问。
支持从YAML文件、环境变量和默认值获取配置。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from pydantic import BaseModel, Field, create_model, ValidationError

from loguru import logger


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class ConfigLoadError(Exception):
    """配置加载错误"""
    pass


class WorkflowConfigManager:
    """
    工作流配置管理器
    
    负责工作流配置的加载、验证和访问。
    """
    
    def __init__(self, 
                 base_config_dir: Optional[str] = None, 
                 workflow_name: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            base_config_dir: 基础配置目录，默认为当前目录下的config
            workflow_name: 工作流名称，用于加载特定工作流配置
        """
        self.logger = logger.bind(name="ConfigManager")
        
        # 确定基础配置目录
        if base_config_dir:
            self.base_config_dir = Path(base_config_dir)
        else:
            # 默认配置目录为项目根目录下的config
            current_dir = Path(__file__).parent
            self.base_config_dir = current_dir / "config"
        
        # 初始化配置存储
        self._config: Dict[str, Any] = {}
        self._schema_models: Dict[str, type] = {}
        
        # 如果提供了工作流名称，则加载该工作流配置
        if workflow_name:
            self.load_workflow_config(workflow_name)
    
    def load_base_config(self) -> Dict[str, Any]:
        """
        加载基础配置
        
        从base_config.yaml加载基础配置
        
        Returns:
            基础配置字典
        
        Raises:
            ConfigLoadError: 当配置加载失败时
        """
        base_config_file = self.base_config_dir / "base_config.yaml"
        
        try:
            if not base_config_file.exists():
                self.logger.warning(f"基础配置文件不存在: {base_config_file}")
                return {}
            
            with open(base_config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            
            self.logger.info(f"成功加载基础配置: {base_config_file}")
            return config
        
        except Exception as e:
            error_msg = f"加载基础配置失败: {str(e)}"
            self.logger.error(error_msg)
            raise ConfigLoadError(error_msg)
    
    def load_workflow_config(self, workflow_name: str) -> Dict[str, Any]:
        """
        加载特定工作流配置
        
        从workflow/{workflow_name}/config.yaml加载工作流配置
        
        Args:
            workflow_name: 工作流名称
        
        Returns:
            工作流配置字典
        
        Raises:
            ConfigLoadError: 当配置加载失败时
        """
        # 先加载基础配置
        base_config = self.load_base_config()
        
        # 查找工作流配置文件
        workflow_config_paths = [
            # 1. 配置中心的工作流配置
            self.base_config_dir / "workflows" / workflow_name / "config.yaml",
            # 2. 工作流目录下的配置
            Path(__file__).parent / workflow_name / "config.yaml"
        ]
        
        workflow_config = {}
        config_file_path = None
        
        # 尝试从不同路径加载
        for path in workflow_config_paths:
            if path.exists():
                config_file_path = path
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        workflow_config = yaml.safe_load(f) or {}
                    self.logger.info(f"成功加载工作流配置: {path}")
                    break
                except Exception as e:
                    self.logger.warning(f"尝试加载配置文件失败: {path}, 错误: {e}")
        
        if not workflow_config and not config_file_path:
            self.logger.warning(f"未找到工作流 {workflow_name} 的配置文件")
        
        # 合并配置
        merged_config = self._merge_configs(base_config, workflow_config)
        
        # 应用环境变量覆盖
        final_config = self._apply_env_overrides(merged_config, workflow_name)
        
        # 存储配置
        self._config = final_config
        
        return final_config
    
    def validate_config(self, config: Dict[str, Any], schema_model: type) -> Dict[str, Any]:
        """
        验证配置是否符合模式
        
        Args:
            config: 配置字典
            schema_model: 配置模式模型类
        
        Returns:
            验证后的配置字典
        
        Raises:
            ConfigValidationError: 当验证失败时
        """
        try:
            validated = schema_model(**config)
            return validated.model_dump()
        except ValidationError as e:
            error_msg = f"配置验证失败: {str(e)}"
            self.logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def register_schema(self, section: str, schema_model: type) -> None:
        """
        注册配置模式
        
        Args:
            section: 配置节名称
            schema_model: 配置模式模型类
        """
        self._schema_models[section] = schema_model
        self.logger.debug(f"注册配置模式: {section}")
    
    def get_config(self, section: Optional[str] = None) -> Any:
        """
        获取配置
        
        Args:
            section: 可选，配置节名称，如不提供则返回整个配置
        
        Returns:
            配置值，或整个配置字典
        """
        if section is None:
            return self._config
        
        # 如果有注册模式，则验证配置节
        if section in self._schema_models and section in self._config:
            return self.validate_config(self._config.get(section, {}), self._schema_models[section])
        
        return self._config.get(section, {})

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        通过点号分隔的路径获取配置项的值

        Args:
            key_path: 配置键路径，使用点号分隔，如 'workflow_config.output.path'
            default: 默认值，当键不存在时返回

        Returns:
            配置值，如果路径不存在则返回默认值

        Examples:
            >>> config_manager.get('workflow_config.output.path')
            >>> config_manager.get('server.host', 'localhost')
            >>> config_manager.get('database.port', 5432)
        """
        if not key_path:
            return default

        # 分割路径
        path_parts = key_path.split('.')

        # 从配置根开始遍历
        current_value = self._config

        try:
            for part in path_parts:
                if isinstance(current_value, dict) and part in current_value:
                    current_value = current_value[part]
                else:
                    # 路径不存在，返回默认值
                    self.logger.debug(f"配置路径不存在: {key_path}, 返回默认值: {default}")
                    return default

            return current_value

        except (TypeError, KeyError, AttributeError) as e:
            # 处理路径访问异常
            self.logger.debug(f"访问配置路径时出错: {key_path}, 错误: {str(e)}, 返回默认值: {default}")
            return default

    def _merge_configs(self, base_config: Dict[str, Any],
                      workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并基础配置和工作流配置
        
        工作流配置会覆盖基础配置中的相同键
        
        Args:
            base_config: 基础配置
            workflow_config: 工作流配置
        
        Returns:
            合并后的配置
        """
        result = base_config.copy()
        
        # 递归合并字典
        def _merge_dict(target, source):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    _merge_dict(target[key], value)
                else:
                    target[key] = value
        
        _merge_dict(result, workflow_config)
        return result
    
    def _apply_env_overrides(self, config: Dict[str, Any], 
                            prefix: Optional[str] = None) -> Dict[str, Any]:
        """
        应用环境变量覆盖
        
        环境变量格式为: SDW_WORKFLOW_{PREFIX}_{KEY} 
        例如: SDW_WORKFLOW_MYFLOW_SERVER_HOST
        
        Args:
            config: 配置字典
            prefix: 环境变量前缀
        
        Returns:
            应用环境变量后的配置
        """
        result = config.copy()
        
        # 构建环境变量前缀
        env_prefix = "SDW_WORKFLOW"
        if prefix:
            env_prefix = f"{env_prefix}_{prefix.upper()}"
        
        # 遍历环境变量
        for key, value in os.environ.items():
            if not key.startswith(f"{env_prefix}_"):
                continue
            
            # 提取配置路径
            config_path = key[len(env_prefix) + 1:].lower().split('_')
            
            # 递归设置配置
            current = result
            for i, part in enumerate(config_path):
                if i == len(config_path) - 1:
                    # 转换环境变量值为适当类型
                    typed_value = self._convert_env_value(value)
                    current[part] = typed_value
                    self.logger.debug(f"从环境变量 {key} 覆盖配置: {'.'.join(config_path)} = {typed_value}")
                else:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
        
        return result
    
    def _convert_env_value(self, value: str) -> Any:
        """
        转换环境变量值为适当类型
        
        Args:
            value: 环境变量值
        
        Returns:
            转换后的值
        """
        # 尝试转换为布尔值
        if value.lower() in ('true', 'yes', '1'):
            return True
        if value.lower() in ('false', 'no', '0'):
            return False
        
        # 尝试转换为整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 尝试转换为浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 保持为字符串
        return value