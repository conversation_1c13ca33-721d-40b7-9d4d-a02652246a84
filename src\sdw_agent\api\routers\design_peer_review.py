
from fastapi import HTTPException

from sdw_agent.model.request_model import DesignPeerReviewRequest
from sdw_agent.model.response_model import DesignPeerReviewResponse
from sdw_agent.service.design_peerreview.design_peer_review import Design_PeerreviewWorkflow,BasicDesignInput

from fastapi import APIRouter
from loguru import logger
from sdw_agent.service import WorkflowStatus



router = APIRouter(prefix="/api/sdw/design", tags=["设计评审开催"])


@router.post("/design_peer_review_2_2",
             summary="2.2设计评审开催",
             description="检查2.2设计评审成果物文件夹，生成评审结果Excel文件",
             response_description="返回生成的Excel文件路径",
             response_model=DesignPeerReviewResponse)
async def design_peer_review_2_2(request: DesignPeerReviewRequest):
    try:
        file_folder = request.folderPath

        # 验证输入路径
        if not file_folder or not file_folder.strip():
            raise HTTPException(status_code=400, detail="文件夹路径不能为空")

        # 调用服务函数
        design_peer_review = Design_PeerreviewWorkflow()
        input_data = BasicDesignInput(dir_path=file_folder, peer_review_type=0x01)

        result = design_peer_review.run(input_data)

        # 检查服务函数返回结果
        if result.status == WorkflowStatus.SUCCESS:
            logger.success(result.message)
            # 返回成功结果
            return DesignPeerReviewResponse(
                code=0,
                msg=result.message,
                data=result.data['output_message']
            )
        else:
            logger.error(result.message)
            return DesignPeerReviewResponse(
                code=0,
                msg=result.error,
                data=result.data["output_message"]
            )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"设计评审开催失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.post("/design_peer_review_2_5",
             summary="2.5设计评审开催",
             description="检查2.5设计评审成果物文件夹，生成评审结果Excel文件",
             response_description="返回生成的Excel文件路径",
             response_model=DesignPeerReviewResponse)
async def design_peer_review_2_5(request: DesignPeerReviewRequest):
    try:
        file_folder = request.folderPath
        
        # 验证输入路径
        if not file_folder or not file_folder.strip():
            raise HTTPException(status_code=400, detail="文件夹路径不能为空")
        
        # 调用服务函数
        design_peer_review = Design_PeerreviewWorkflow()
        input_data = BasicDesignInput(dir_path=file_folder, peer_review_type=0x02)

        result = design_peer_review.run(input_data)
        
        # 检查服务函数返回结果
        if result.status == WorkflowStatus.SUCCESS:
            logger.success(result.message)
            # 返回成功结果
            return DesignPeerReviewResponse(
                code=0,
                msg=result.message,
                data=result.data['output_message']
            )
        else:
            logger.error(result.message)
            return DesignPeerReviewResponse(
                code=0,
                msg=result.error,
                data=result.data["output_message"]
            )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"设计评审开催失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/design_peer_review_3_3",
             summary="3.3设计评审开催",
             description="检查3.3设计评审成果物文件夹，生成评审结果Excel文件",
             response_description="返回生成的Excel文件路径",
             response_model=DesignPeerReviewResponse)
async def design_peer_review_3_3(request: DesignPeerReviewRequest):
    try:
        file_folder = request.folderPath
        
        # 验证输入路径
        if not file_folder or not file_folder.strip():
            raise HTTPException(status_code=400, detail="文件夹路径不能为空")
        
        # 调用服务函数
        design_peer_review = Design_PeerreviewWorkflow()
        input_data = BasicDesignInput(dir_path=file_folder, peer_review_type=0x04)

        result = design_peer_review.run(input_data)

        # 检查服务函数返回结果
        if result.status == WorkflowStatus.SUCCESS:
            logger.success(result.message)
            # 返回成功结果
            return DesignPeerReviewResponse(
                code=0,
                msg=result.message,
                data=result.data['output_message']
            )
        else:
            logger.error(result.message)
            return DesignPeerReviewResponse(
                code=0,
                msg=result.error,
                data=result.data["output_message"]
            )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"设计评审开催失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)