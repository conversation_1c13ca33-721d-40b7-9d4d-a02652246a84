from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from docparser.models.document import DocumentObject
from docparser.models.table import TableObject
from docparser.models.text import TextObject

class PluginInterface(ABC):
    """
    Interface for document parser plugins.
    Defines the contract for all plugins that can extend the document parser functionality.
    """
    
    @abstractmethod
    def get_plugin_name(self) -> str:
        """
        Get the name of the plugin.
        
        Returns:
            Plugin name
        """
        pass
    
    @abstractmethod
    def get_plugin_version(self) -> str:
        """
        Get the version of the plugin.
        
        Returns:
            Plugin version
        """
        pass
    
    @abstractmethod
    def get_supported_document_types(self) -> List[str]:
        """
        Get the document types supported by this plugin.
        
        Returns:
            List of supported document types (e.g., ['docx', 'xlsx'])
        """
        pass
    
    @abstractmethod
    def process_document(self, document_data: DocumentObject):
        """
        Process the document data.
        
        Args:
            document_data: Dictionary containing parsed document data
            
        Returns:
            Processed document data
        """
        pass
    
    @abstractmethod
    def validate_document(self, document_data: Dict[str, Any]) -> bool:
        """
        Validate the document data.
        
        Args:
            document_data: Dictionary containing parsed document data
            
        Returns:
            True if document data is valid, False otherwise
        """
        pass

class TextProcessorPlugin(PluginInterface):
    """Interface for plugins that process text objects"""
    
    @abstractmethod
    def process_text_objects(self, text_objects: List[TextObject], file_name: str, block_name: str):
        """
        Process text objects from the document.
        
        Args:
            text_objects: List of text objects

            file_name: file_name of document

            block_name: sheet_name or ''
        Returns:
            Processed text objects
        """
        pass

class TableProcessorPlugin(PluginInterface):
    """Interface for plugins that process table objects"""
    
    @abstractmethod
    def process_table_objects(self, table_objects: List[TableObject], file_name: str, block_name: str):
        """
        Process table objects from the document.
        
        Args:
            table_objects: List of table objects

            file_name: file_name of document

            block_name: sheet_name or ''
        Returns:
            Processed table objects
        """
        pass

class PictureProcessorPlugin(PluginInterface):
    """Interface for plugins that process picture objects"""
    
    @abstractmethod
    def process_picture_objects(self, picture_objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process picture objects from the document.
        
        Args:
            picture_objects: List of picture objects
            
        Returns:
            Processed picture objects
        """
        pass

class GraphicProcessorPlugin(PluginInterface):
    """Interface for plugins that process graphic objects"""
    
    @abstractmethod
    def process_graphic_objects(self, graphic_objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process graphic objects from the document.
        
        Args:
            graphic_objects: List of graphic objects
            
        Returns:
            Processed graphic objects
        """
        pass
