---
type: "always_apply"
---

# 项目规则

1. **禁止生成总结性的 Markdown 文件**  
   请避免生成任何形式的总结性 Markdown 文件。

2. **测试脚本规范**  
   测试脚本应存放在项目中的 `tests` 目录下，而非直接放置在项目根路径中。同时，测试代码需基于 `pytest` 框架编写。

3. **日志记录规范**  
   在代码编写过程中，请不要使用 `print` 输出信息。本项目使用 `loguru` 进行日志记录，请通过以下方式记录日志：
   ```python
      from loguru import logger
      logger.info('xx')
   ```

4. **命令执行规范**  
   本项目使用 `poetry` 进行管理，因此在运行与 Python 相关的命令时，请使用以下格式：
   ```bash
   poetry run python xxx
   ```

5. **Python导包规范**
   本项目中导入项目内部的包必须使用绝对路径导入，例如：
   ```python
      from sdw_agent.util.excel_util import read_excel
      # read_excel()
      # xxx
   ```
   禁止使用相对路径导入！

6. **Pydantic 验证器规范**
   本项目使用 Pydantic V2，必须使用新版的 `@field_validator` 装饰器，禁止使用已弃用的 `@validator` 装饰器。

   **正确用法（必须遵循）：**
   ```python
   from pydantic import BaseModel, field_validator

   class MyModel(BaseModel):
       field_name: str

       @field_validator('field_name')
       @classmethod
       def validate_field_name(cls, v):
           # 验证逻辑
           if not v:
               raise ValueError("字段不能为空")
           return v
   ```

   **禁止使用（已弃用）：**
   ```python
   from pydantic import BaseModel, validator  # ❌ 禁止使用

   class MyModel(BaseModel):
       field_name: str

       @validator('field_name')  # ❌ 已弃用
       def validate_field_name(cls, v):
           return v
   ```

   **关键要点：**
   - 必须导入 `field_validator` 而不是 `validator`
   - 必须使用 `@field_validator('field_name')` 装饰器
   - 必须添加 `@classmethod` 装饰器
   - 验证方法的第一个参数必须是 `cls`