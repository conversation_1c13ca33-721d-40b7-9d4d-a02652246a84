

import asyncio
from loguru import logger
from openpyxl import load_workbook
from typing import Dict, List, Any, Optional
from sdw_agent.llm.openai_qwen import OpenAIQwen
from sdw_agent.service.oss_examination.prompt import judge_licence_prompt
from sdw_agent.service.oss_examination.license_base import license_base
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus


@register_workflow("oss_examination")
class OSSExaminationWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path=config_path)
        self.llm = OpenAIQwen(base_url="http://172.30.19.113:11400/v1", api_key="sk-xxx", model="qwen3")
        logger.info(self.config)

    async def execute(self, file_path):
        records = self.read_fossid_excel(file_path)
        licenses = await self.get_license_results(records)
        save_path = self.writeback2excel(licenses=licenses, contents=records, file_path=file_path)
        return save_path

    def read_fossid_excel(self, file_path):
        contents = []
        wb = load_workbook(filename=file_path)
        ws = wb[self.config['sheet_name']]
        n = 0
        for row in ws.iter_rows(values_only=True):
            n += 1
            if n > 1:
                content = {self.config['protocol_name']: row[9], self.config['result_name']: '', self.config['evidence_name']: ''}
                contents.append(content)
        return contents

    def lookup_table(self, license_names):
        exist_licenses, new_license_names = [], []
        name_list = [lic['协议名称'] for lic in license_base]
        for name in license_names:
            try:
                index = name_list.index(name)
            except ValueError:
                index = -1
            if index >= 0:
                exist_licenses.append(license_base[index])
            else:
                new_license_names.append(name)
        return exist_licenses, new_license_names

    async def get_license_results(self, contents):
        license_names = list(set([content[self.config['protocol_name']] for content in contents]))
        license_names = list(filter(lambda x: x not in ['None', '', ' '], license_names))
        exist_licenses, new_license_names = self.lookup_table(license_names)
        new_licenses = []
        batch_size = 3
        step = len(new_license_names) // batch_size if len(new_license_names) % batch_size == 0 else len(new_license_names) // batch_size + 1
        for i in range(step):
            if i == step - 1:
                input_str = '\n'.join([f"代码片段{index+1}，协议名称：{license_name}" for index, license_name in enumerate(new_license_names[i*3:])])
            else:
                input_str = '\n'.join([f"代码片段{index+1}，协议名称：{license_name}" for index, license_name in enumerate(new_license_names[i*3:(i+1)*3])])
            prompt = judge_licence_prompt.format(input=input_str)
            response = await self.llm.generate_text(system_message=prompt)
            result = self.llm.parse_content2json(responses=self.llm.parse_content_from_response(responses=[response], start_token='[', end_token=']'), default='list')[0]
            new_licenses += result
        logger.info(new_licenses)
        return exist_licenses+new_licenses

    def writeback2excel(self, licenses, contents, file_path):
        for i in range(len(contents)):
            license_names = [license["协议名称"] for license in licenses]
            if contents[i][self.config['protocol_name']] is None or contents[i][self.config['protocol_name']] == '':
                contents[i][self.config['result_name']] = ""
                contents[i][self.config['evidence_name']] = ""
            else:
                try:
                    index = license_names.index(contents[i][self.config['protocol_name']])
                    contents[i][self.config['result_name']] = licenses[index]["判定结果"]
                    contents[i][self.config['evidence_name']] = licenses[index]["判断理由"]
                except ValueError:
                    contents[i][self.config['result_name']] = ""
                    contents[i][self.config['evidence_name']] = ""
        postfix = '.xlsm' if '.xlsm' in file_path else '.xlsx'
        save_path = file_path.split(postfix)[0] + '_output.xlsx'
        wb = load_workbook(filename=file_path)
        other_sheets = wb.sheetnames
        other_sheets.remove(self.config['sheet_name'])
        for sheet in other_sheets:
            wb.remove(wb[sheet])
        ws = wb[self.config['sheet_name']]
        for row in range(2, len(contents)+2):
            ws.cell(row=row, column=24).value = contents[row-2][self.config['result_name']]
            ws.cell(row=row, column=25).value = contents[row-2][self.config['evidence_name']]
        wb.save(save_path)
        return save_path



if __name__ == '__main__':
    examiner = OSSExaminationWorkflow()
    contents = examiner.read_fossid_excel(file_path='FOSSID_result.xlsm')
    licenses = asyncio.run(examiner.get_license_results(contents=contents))
    save_path = examiner.writeback2excel(licenses=licenses, contents=contents, file_path='FOSSID_result.xlsm')