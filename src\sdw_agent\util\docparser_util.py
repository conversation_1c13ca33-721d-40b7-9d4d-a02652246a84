#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : docparser_util.py
@Time    : 2025/7/17 9:29
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 调用解析工具进行文档解析
"""
from docparser import DocumentManager


def parser_get_dict(file_path):
    """将文件解析后保存成字典"""
    doc_parser = DocumentManager()
    parser_res = doc_parser.parse_document_object(file_path)
    return parser_res


if __name__ == '__main__':
    file_ = r"D:\TEST Agent工作相关-wxy\式样涂抹-模板\模板\输入\式样中变更点涂抹\MET-M_DEVILST-CORG-0-XX-X-C1-732D-A_vs_MET-M_DEVILST-CORG-0-XX-X-C1-235D-B.xlsx"
    parser_ = parser_get_dict(file_)
    print(parser_)
