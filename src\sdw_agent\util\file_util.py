import copy
import json
import os
import pathlib
import re
import tempfile
import traceback
from datetime import datetime

import pandas as pd
from fastapi_babel import _ as gettext
from loguru import logger
from openpyxl import load_workbook
from openpyxl.cell import MergedCell
from openpyxl.styles import Alignment, Font, Border, Side, PatternFill
from openpyxl.workbook import Workbook

from sdw_agent.config.env import ENV
from sdw_agent.util.extract_util import extract_req_name
from sdw_agent.util.markdown_util import markdown_to_pandas


def get_output_excel_path(output_data_path: str, subdir: str, file_name: str) -> str:
    """
    生成带时间戳的成果物输出路径，并确保输出目录存在。
    Args:
        output_data_path: 根输出目录
        subdir: 子目录名，默认 communication_can_cs
        file_name: 文件名
    Returns:
        完整的输出文件路径
    """
    output_dir = os.path.join(pathlib.Path(output_data_path), subdir)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    output_path = os.path.join(output_dir, file_name)

    return output_path


class TempFileManager:
    def __init__(self, dir=None, prefix="tmp", suffix=".txt"):
        self.dir = dir
        self.prefix = prefix
        self.suffix = suffix
        self.file = None
        self.file_path = None

    def __enter__(self):
        self.file = tempfile.NamedTemporaryFile(delete=False, dir=self.dir, prefix=self.prefix, suffix=self.suffix)
        self.file_path = self.file.name
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.file:
            self.file.close()
        # if self.file_path and os.path.exists(self.file_path):
        #     os.remove(self.file_path)


def write_file(file_path, content):
    # 如果目录不存在，则创建目录
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)


def add_prefix_to_numbered_lines(text, serial_no):
    '''
    解析设计评价方针内容 给评价方法添加对应设计方法编号
    :param text:
    :param serial_no:
    :return:
    '''
    # 判断是否包含类似 "x." 的序号
    has_numbered_line = re.search(r'^\d+\.', text, re.MULTILINE)

    if not has_numbered_line:
        # 没有序号标注，在整个字符串前加 '1.'
        return str(serial_no) + '. ' + text.strip()

    # 对于已有的数字序号（如 1.、2.），替换为 1.x 格式，并保留后面的内容
    def replace_numbered(match):
        num = int(match.group(1))
        content = match.group(2)
        return f'{serial_no}.{num} {content}'

    # 使用正则表达式匹配所有以序号开头的行，并替换为 1.x 格式
    modified_text = re.sub(r'^(\d+)\.\s*(.+)', replace_numbered, text, flags=re.MULTILINE)

    return modified_text


def generate_design_policy(design_data_list, design_and_evaluate, ar_no, change_info, function_name="", ):
    """
    生成设计评价方针的markdown和excel文件
    参数:
        design_data_list (json 列表): 设计信息列表
        design_and_evaluate (str): 设计方法和评价方法的list
        change_info (str): 变更描述文本
    """
    try:
        # 解析JSON输入
        design_method_data = json.loads(design_and_evaluate)
        # 解析design_and_evaluate
        eva_design = []
        for key in design_method_data.keys():
            design_list = []
            eval_list = []
            pair_dict = {}
            for idx, item in enumerate(design_method_data[key]):
                iterator_val = iter(item.values())
                design_list.append(str(idx + 1) + '.' + next(iterator_val))
                eval_list.append(add_prefix_to_numbered_lines(next(iterator_val), (idx + 1)))
            pair_dict["design_method"] = "\n".join(design_list)
            pair_dict["eva_method"] = "\n".join(eval_list)
            eva_design.append(pair_dict)  # type: ignore

        # 验证数据完整性
        if len(design_data_list) != len(eva_design):
            raise ValueError(gettext("设计方针与设计方法的数量不匹配"))

        # 担心点完整描述列表
        worry_descriptions = {
            ("実行されない", "组件不被执行"): gettext("组件不被执行"),
            ("実行順序が間違っている", "执行顺序错误"): gettext("执行顺序错误"),
            ("処理時間がオーバーしている", "处理时间超出"): gettext("处理时间超出"),
            ("処理が間違っている", "处理错误"): gettext("处理错误"),
            ("データのTimingが間違っている", "Data的Timing错误"): gettext("Data的Timing错误"),
            ("Memoryに誤って書き込まれている", "Memory错误写入"): gettext("Memory错误写入"),
            ("入力データが間違っている", "输入数据错误"): gettext("输入数据错误")
        }

        # 生成表格数据
        table_data = []
        concern_info = []
        for idx, (design_data, method) in enumerate(zip(design_data_list, eva_design), 1):
            # 格式化担心点，显示完整描述及状态(Yes/No)
            worry_points_formatted = []
            for desc in worry_descriptions.keys():
                if design_data['故障模式'] in desc:
                    status = "Yes"
                    concern_info.append({worry_descriptions.get(desc): design_data["担心点具体内容"]})
                else:
                    status = "No"
                worry_points_formatted.append(f"{worry_descriptions.get(desc)} {status}")
            worry_points = '\n'.join(worry_points_formatted)

            table_data.append({
                gettext("No"): str(idx),
                gettext("AR票号"): ar_no,
                gettext("新規/変化点"): change_info,
                gettext("設計方針"): design_data["设计方针"],
                gettext("心配な点"): worry_points,
                gettext("担心点具体内容"): design_data["担心点具体内容"],
                gettext("設計方法"): method["design_method"],
                gettext("評価方法"): method["eva_method"]
            })

        # 生成Markdown
        markdown = """| {serial_num} | {change_point} | {design_mth} | {fault_type} | {worry_des} | {evaluate} |
| :- | :---------- | :------- | :---------------------------------- | :------------- | :------- |\n""" \
            .format(serial_num=gettext("No"), change_point=gettext("新規/変化点"), design_mth=gettext("設計方針"), \
                    fault_type=gettext("心配な点（該当する故障モードに○を記入）"), worry_des=gettext("担心点具体内容"), \
                    evaluate=gettext("設計・評価方法"))

        for row in table_data:
            # 将担心点内容中的换行符替换为HTML换行标签，以便在Markdown中正确显示
            formatted_worry_points = row[gettext("心配な点")].replace('\n', '<br />').replace('\\n', '<br />')
            change_desc = (row[gettext("AR票号")] + ':\n' + row[gettext("新規/変化点")]).replace('\n',
                                                                                                 '<br />').replace(
                '\\n', '<br />')
            design_policy = row[gettext("設計方針")].replace('\n', '<br />').replace('\\n', '<br />')
            content = row[gettext("担心点具体内容")].replace('\n', '<br />').replace('\\n', '<br />')
            design_method = row[gettext("設計方法")].replace('\n', '<br />').replace('\\n', '<br />')
            evaluate_method = row[gettext("評価方法")].replace('\n', '<br />').replace('\\n', '<br />')
            markdown += "| {serial} | {change_desc} | {design_policy} | {formatted_worry_points} | {content} | {design_title}:<br /> {design_method}<br />{evaluate_title}: <br />{evaluate_method} |\n" \
                .format(serial=row['No'], change_desc=change_desc, design_policy=design_policy,
                        formatted_worry_points=formatted_worry_points, content=content, design_title=gettext("設計"),
                        design_method=design_method, evaluate_title=gettext("評価"), evaluate_method=evaluate_method)

        # 生成Excel
        df = pd.DataFrame(table_data)
        df = df[
            [gettext("No"), gettext("新規/変化点"), gettext("設計方針"), gettext("心配な点"), gettext("担心点具体内容"),
             gettext("設計方法"), gettext("評価方法")]]

        # 保存文件时设置Excel单元格格式，确保正确显示换行
        excell_path = f"{ENV.config.output_data_path}/design_policy/{function_name}_design_policy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        if not os.path.exists(os.path.dirname(excell_path)):
            os.makedirs(os.path.dirname(excell_path))
        with pd.ExcelWriter(excell_path, engine="openpyxl") as writer:
            df.to_excel(writer, index=False)
            worksheet = writer.sheets["Sheet1"]
            # 调整单元格样式以支持换行
            for idx, col in enumerate(df.columns):
                max_length = df[col].astype(str).map(len).max()
                max_length = max(max_length, len(col)) + 2
                worksheet.column_dimensions[chr(65 + idx)].width = max_length
                if col == gettext("心配な点"):
                    for cell in worksheet[chr(65 + idx)][1:]:
                        cell.alignment = Alignment(wrapText=True, vertical='top')

        # 保存Markdown文件
        md_path = os.path.join(
            pathlib.Path(ENV.config.output_data_path),
            "design_policy",
            f"{function_name}_design_policy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        )
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(markdown)

        deep_copied = copy.deepcopy(md_path)
        json_path = deep_copied.replace(".md", ".json")
        # 保存Json文件
        with open(json_path, "w", encoding="utf-8") as f2:
            json.dump(concern_info, f2, ensure_ascii=False, indent=4)

        return md_path, excell_path

    except Exception as e:
        logger.error("文件生成失败：", str(e))
        traceback.print_exc()
        return None, None


def read_excel(file_path: str, sheet_name: str | int = 0, header: int = 0, add_uuid: bool = False):
    """
        根据指定路径读取excel文件
        参数:
            file_path (str): 文件路径
            sheet_name (str | int): sheet name
            header (int): 表头行索引
            add_uuid (bool): 是否添加UUID列，默认为False
    """
    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)
    # ['対応イベント', 'ARチケットNO', 'ARチケットNOリンク', 'ARチケットタイトル', 'エピック名', '概要', '要件チケットNO', '要件チケットNOリンク', 'R核', 'A核', '要件需求文档名称', '变更内容所在的章节信息']
    column_names = df.columns.tolist()

    # 加载 Excel 文件
    workbook = load_workbook(file_path)

    # 处理UUID逻辑
    if add_uuid:
        import uuid

        # 检查是否已存在UUID列
        uuid_exists = 'UUID' in column_names

        if not uuid_exists:
            # 确定工作表名
            if isinstance(sheet_name, int):
                sheet_name_str = workbook.sheetnames[sheet_name]
            else:
                sheet_name_str = sheet_name

            sheet = workbook[sheet_name_str]

            # 在插入列前，首先保存所有合并单元格的信息
            merged_cells = []
            for merged_range in sheet.merged_cells.ranges:
                # 记录每个合并区域的起始和结束位置
                merged_cells.append({
                    'min_row': merged_range.min_row,
                    'max_row': merged_range.max_row,
                    'min_col': merged_range.min_col,
                    'max_col': merged_range.max_col
                })

            # 为每行创建UUID
            uuids = [str(uuid.uuid4()) for _ in range(len(df))]

            # 在Excel工作表中直接插入UUID列，插入到K列(第11列)前面
            sheet.insert_cols(11)

            # 设置列宽为32
            sheet.column_dimensions['K'].width = 32

            # 添加列标题
            header_cell = sheet.cell(row=header + 1, column=11, value="UUID")

            # 寻找一个具有良好样式的列作为参考
            style_ref_col = 12  # 默认使用L列(第12列)

            # 尝试使用其他列作为样式参考
            if sheet.cell(row=header + 1, column=10).font and sheet.cell(row=header + 1, column=10).fill:
                style_ref_col = 10  # 使用J列

            source_cell = sheet.cell(row=header + 1, column=style_ref_col)

            # 复制样式
            # 复制字体
            header_cell.font = copy.copy(source_cell.font)
            # 复制填充
            header_cell.fill = copy.copy(source_cell.fill)
            # 复制边框
            header_cell.border = copy.copy(source_cell.border)
            # 复制对齐方式
            header_cell.alignment = copy.copy(source_cell.alignment)

            # 填充UUID值到单元格并复制样式
            for idx, uuid_val in enumerate(uuids):
                current_row = header + idx + 2
                uuid_cell = sheet.cell(row=current_row, column=11, value=uuid_val)
                source_cell = sheet.cell(row=current_row, column=style_ref_col)

                # 复制样式
                uuid_cell.font = copy.copy(source_cell.font)
                uuid_cell.fill = copy.copy(source_cell.fill)
                uuid_cell.border = copy.copy(source_cell.border)
                uuid_cell.alignment = copy.copy(source_cell.alignment)

            # 清除所有现有的合并单元格
            while sheet.merged_cells.ranges:
                sheet.merged_cells.ranges.pop()

            # 重新应用调整后的合并单元格
            for merged_cell in merged_cells:
                min_row = merged_cell['min_row']
                max_row = merged_cell['max_row']
                min_col = merged_cell['min_col']
                max_col = merged_cell['max_col']

                # 调整列索引，如果合并区域在插入列之后或者跨越插入列
                if min_col >= 11:
                    min_col += 1
                if max_col >= 11:
                    max_col += 1

                # 重新合并单元格
                sheet.merge_cells(start_row=min_row, end_row=max_row,
                                  start_column=min_col, end_column=max_col)

            # 保存修改后的文件
            workbook.save(file_path)

            # 重新加载文件
            workbook = load_workbook(file_path)

            # 重新读取数据到DataFrame
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)
            column_names = df.columns.tolist()

    # 获取指定工作表
    if isinstance(sheet_name, int):
        sheet = workbook[workbook.sheetnames[sheet_name]]
    else:
        sheet = workbook[sheet_name]
    hyperlinks = []
    pattern = r'HYPERLINK\("(.*?)"'
    # 读取hyperlink 信息
    for idx, row in enumerate(sheet.iter_rows(min_row=(header + 1))):
        if idx == 0:
            continue
        hyperdict = {}
        for col_idx, col in enumerate(column_names):
            # if row[col_idx].hyperlink:
            #     hyperdict[col] = row[col_idx].hyperlink.target

            if not isinstance(row[col_idx], MergedCell) and isinstance(row[col_idx].internal_value, str):
                match = re.search(pattern, row[col_idx].internal_value)
                if match:
                    hyperlink_url = match.group(1)
                    hyperdict[col] = hyperlink_url
                else:
                    hyperlink_url = ""
                    hyperdict[col] = hyperlink_url

        hyperlinks.append(hyperdict)
    # 返回超链接信息，dataframe
    return hyperlinks, df


def process_merged_cells(file_path, sheet_name, min_guideline_rowidx, max_guideline_rowidx):
    """
    处理 Excel 文件中指定工作表和行范围内的合并单元格，避免。
    只处理 guideline文件所在的行。
    """
    # 加载工作簿
    wb = load_workbook(file_path)

    # 检查是否存在名为"a"的工作表
    if sheet_name not in wb.sheetnames:
        print(f"错误：文件中不存在名为{sheet_name}的工作表！")
        return

    # 获取指定工作表
    ws = wb[sheet_name]

    # 1. 处理合并的单元格，获取所有合并单元格范围
    merged_ranges = list(ws.merged_cells.ranges)

    # 反向遍历合并区域（避免索引变化问题）
    for merged_range in reversed(merged_ranges):
        # 获取合并区域的范围
        min_col, min_row, max_col, max_row = merged_range.min_col, merged_range.min_row, merged_range.max_col, merged_range.max_row

        # 检查合并区域是否与目标行范围有交集
        if (min_row <= max_guideline_rowidx and max_row >= min_guideline_rowidx):
            # 拆分合并单元格
            ws.unmerge_cells(range_string=str(merged_range))

            # 获取左上角单元格的值
            top_left_value = ws.cell(row=min_row, column=min_col).value

            # 填充拆分后的所有单元格
            for row in range(min_row, max_row + 1):
                for col in range(min_col, max_col + 1):
                    ws.cell(row=row, column=col).value = top_left_value

    # 生成新的dataframe
    # 2. 提取数据到列表
    data = []
    for row_idx, row in enumerate(ws.iter_rows(values_only=True), 1):
        # 只提取指定行范围的数据
        if min_guideline_rowidx <= row_idx <= max_guideline_rowidx:
            data.append(row)

    # 3. 转换为 DataFrame
    # 假设第一行是列名（如果不是，可调整）
    if data:
        df = pd.DataFrame(data[1:], columns=data[0])
    else:
        df = pd.DataFrame()

    return df


def design_police_to_excel(content, target_file_path):
    '''
    将.md设计评价方针写入DNKT指定模板的Excel文件中
    :param content:
    :param target_file_path:
    '''
    try:
        # 加载 Excel 文件
        wb = Workbook()
        ws = wb.active

        # 设置列宽 列宽不可修改
        ws.column_dimensions['A'].width = 4.38
        ws.column_dimensions['B'].width = 16.38
        ws.column_dimensions['C'].width = 52.25
        ws.column_dimensions['D'].width = 34
        ws.column_dimensions['E'].width = 16.63
        ws.column_dimensions['F'].width = 4
        ws.column_dimensions['G'].width = 30.75
        ws.column_dimensions['H'].width = 6.38
        ws.column_dimensions['I'].width = 35
        ws.column_dimensions['J'].width = 10.38
        ws.column_dimensions['K'].width = 10.38
        ws.column_dimensions['L'].width = 10.38
        ws.column_dimensions['M'].width = 11.13

        # 设置文件说明样式
        # 定义不同颜色的填充对象
        black_fill = PatternFill(start_color='00000000', end_color='00000000', fill_type='solid')
        for col_idx in range(1, 14, 1):
            cell = ws.cell(row=1, column=col_idx)
            cell.fill = black_fill
            if col_idx == 1:
                cell.value = "機能別設計・評価方針"
                cell.font = Font(bold=True, name="微软雅黑", size=20, color='00FFFFFF')
            elif col_idx == 6:
                cell.value = "※セル幅の変更禁止　※フォントサイズの変更禁止　※セル結合禁止"
                cell.font = Font(bold=True, name="ＭＳ Ｐゴシック", size=22, color='00FF0000')

        # 定义表头
        # 表头样式
        header_fill = PatternFill(start_color='FFF8CBAD', end_color='FFF8CBAD', fill_type='solid')
        head_font = Font(name="Meiryo UI", size=11)
        # 设置单元格边框样式
        thin_border = Border(left=Side(style='thin'),
                             right=Side(style='thin'),
                             top=Side(style='thin'),
                             bottom=Side(style='thin'))
        head_alignment = Alignment(horizontal='center', vertical='center', wrapText=True)
        header_row = 4
        ws.cell(row=header_row, column=2, value=gettext("No"))
        ws.cell(row=header_row, column=3, value=gettext("新規/変化点"))
        ws.cell(row=header_row, column=4, value=gettext("設計方針"))
        ws.cell(row=header_row, column=5, value=gettext('心配な点（該当する故障モードに○を記入）'))
        ws.cell(row=header_row, column=8, value=gettext('設計・評価方法'))
        ws.cell(row=header_row, column=10, value=gettext('担当者'))
        ws.cell(row=(header_row + 1), column=10, value=gettext('実施'))
        ws.cell(row=(header_row + 1), column=11, value=gettext('検証'))
        ws.cell(row=(header_row + 1), column=12, value=gettext('判定'))
        ws.cell(row=header_row, column=13, value=gettext('結果/完了予定日'))
        # 合并表头单元格
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=2, end_column=2)
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=3, end_column=3)
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=4, end_column=4)
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=5, end_column=7)
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=8, end_column=9)
        ws.merge_cells(start_row=header_row, end_row=header_row, start_column=10, end_column=12)
        ws.merge_cells(start_row=header_row, end_row=(header_row + 1), start_column=13, end_column=13)
        for row in ws.iter_rows(min_row=4, max_row=5):
            for col_idx, cell in enumerate(row):
                if col_idx == 0: continue
                cell.font = head_font
                cell.border = thin_border
                cell.fill = header_fill
                cell.alignment = head_alignment

        # 设置单元格字体样式
        font = Font(name="微软雅黑", size=10)
        next_row = ws.max_row + 1
        start_col = 2

        # 写入设计评价方针内容
        for idx, key in enumerate(content.keys()):
            begin_row = next_row
            # 写入No, 新规/变化点
            ws.cell(row=begin_row, column=start_col, value=(idx + 1))
            ws.cell(row=begin_row, column=(start_col + 1), value=key)

            for cn in content.get(key):
                start_row = next_row
                current_row = start_row
                ws.cell(row=current_row, column=(start_col + 2), value=cn.get('設計方針').strip())
                for idx, val in enumerate(cn.get('心配な点（該当する故障モードに○を記入）')):
                    ws.cell(row=current_row, column=(start_col + 3), value=val[0])
                    ws.cell(row=current_row, column=(start_col + 4), value=val[1])
                    # ws.cell(row=current_row, column=(start_col + 5), value=val[2])
                    current_row += 1

                worries_num = len(cn.get('心配な点（該当する故障モードに○を記入）'))

                ws.cell(row=start_row, column=(start_col + 5), value=cn.get('担心点具体内容'))
                # 分类展示 设计
                ws.cell(row=start_row, column=(start_col + 6), value=gettext("設計"))
                ws.merge_cells(
                    start_row=start_row, end_row=(start_row + (worries_num >> 1)),
                    start_column=(start_col + 6), end_column=(start_col + 6))
                # 分类展示 评价
                ws.cell(row=(start_row + ((worries_num >> 1) + 1)), column=(start_col + (worries_num - 1)),
                        value=gettext("評価"))
                ws.merge_cells(
                    start_row=(start_row + ((worries_num >> 1) + 1)), end_row=(current_row - 1),
                    start_column=(start_col + 6), end_column=(start_col + 6))

                # 设计内容
                ws.cell(row=start_row, column=(start_col + 7), value=cn.get("設計"))
                ws.merge_cells(
                    start_row=start_row, end_row=(start_row + (worries_num >> 1)),
                    start_column=(start_col + 7), end_column=(start_col + 7))
                # 评价内容
                ws.cell(row=(start_row + ((worries_num >> 1) + 1)), column=(start_col + 7), value=cn.get("評価"))
                ws.merge_cells(
                    start_row=(start_row + ((worries_num >> 1) + 1)), end_row=(current_row - 1),
                    start_column=(start_col + 7), end_column=(start_col + 7))

                # 合并单元格 担心点描述
                ws.merge_cells(
                    start_row=start_row, end_row=(current_row - 1),
                    start_column=(start_col + 5), end_column=(start_col + 5))
                # 合并单元格 実施 検証 判定 結果/完了予定日
                for col_idx in range(8, 12, 1):
                    ws.merge_cells(
                        start_row=start_row, end_row=(start_row + (worries_num >> 1)),
                        start_column=(start_col + col_idx), end_column=(start_col + col_idx))
                    ws.merge_cells(
                        start_row=(start_row + ((worries_num >> 1) + 1)), end_row=(current_row - 1),
                        start_column=(start_col + col_idx), end_column=(start_col + col_idx))

                next_row = current_row

                # 设置单元格样式
                for row in ws.iter_rows(min_row=start_row, max_row=(current_row - 1)):
                    for col_idx, cell in enumerate(row):
                        if col_idx in [0, 13]: continue
                        cell.font = font
                        cell.border = thin_border
                        if col_idx in [1, 5, 7]:
                            cell.alignment = Alignment(horizontal='center', vertical='center', wrapText=True)
                        else:
                            # 创建样式 - 垂直方向居中 水平方向向左对齐
                            cell.alignment = Alignment(horizontal='left', vertical='center', wrapText=True)

            # No, 新規/変化点 列合并单元格
            ws.merge_cells(
                start_row=begin_row, end_row=(next_row - 1),
                start_column=start_col, end_column=start_col)
            ws.merge_cells(
                start_row=begin_row, end_row=(next_row - 1),
                start_column=(start_col + 1), end_column=(start_col + 1))
            # 加上设计方针 列合并单元格
            ws.merge_cells(
                start_row=begin_row, end_row=(next_row - 1),
                start_column=(start_col + 2), end_column=(start_col + 2))

        wb.save(target_file_path)
        logger.success(f'设计评价方针已写入Excel文件:{target_file_path}')
    except Exception as e:
        traceback.print_exc()
        if isinstance(e, PermissionError):
            raise PermissionError(gettext("写入权限错误，文件写入失败"))
        else:
            raise RuntimeError(gettext("设计评价方针写入失败"))


def change_info_2_excel(change_md_path: str, target_file_path: str, content: str):
    '''
    组件变更修改写入要件分析一览表
    :param change_md_path: 组件变更markdown 文档路径
    :param target_file_path: 目标写入文档路径
    :param content: 组件变更内容
    '''
    try:
        # 将md 转换成 dataframe
        df = markdown_to_pandas(content)
        # 根据change_md_path解析出变更点内容
        change_name = extract_req_name(change_md_path)[:extract_req_name(change_md_path).find('_component_change_info')]
        # 根据change_md_path解析出要件一览表的地址
        epic_path = target_file_path

        wb = load_workbook(epic_path)
        ws = wb.active

        # 要件分析一览表 部分表头合并了单元格
        # main header
        header_row_main = ws[4]
        col_idx_main = {}
        for idx, col in enumerate(header_row_main):
            if col.value:
                col_idx_main[col.value.replace('\\xa0', '').replace('\n', '')] = idx
        # sub header
        header_row_sub = ws[5]
        col_idx_sub = {}
        for idx, col in enumerate(header_row_sub):
            if col.value:
                col_idx_sub[col.value.replace('\xa0', '').replace('\n', '')] = idx

        # 要见一览表中需要修改列的索引 合并main header 和 sub header
        target_field_index = col_idx_sub | col_idx_main

        # 组件变更md中提供写入信息列的索引
        source_field_index = {
            "组件": 3,
            "该组件需要修改的内容描述": 6
        }

        # 优化列宽
        ws.column_dimensions['AE'].width = 50
        ws.column_dimensions['AI'].width = 65
        # 设置长文本单元格样式
        alignment = Alignment(horizontal='left', vertical='center', wrapText=True)
        # 写入Excel
        for row in ws.iter_rows(min_row=6, values_only=False):
            if change_name in row[target_field_index['要件需求文档名称']].value:
                row[target_field_index['SETTING']].value = 'TDD Agent'
                row[target_field_index['SETTING']].alignment = alignment
                row[target_field_index['变更点的理解']].value = row[target_field_index['概要']].value
                row[target_field_index['变更点的理解']].alignment = alignment
                # 判断是否有任意组件需要变更
                if df.shape[0] > 0:
                    row[target_field_index['对应要否']].value = gettext('是')
                    row[target_field_index['对应要否']].alignment = alignment
                    temp = []
                    for idx, comp in df.iterrows():
                        temp.append(comp[source_field_index['组件']])
                        temp.append(comp[source_field_index['该组件需要修改的内容描述']].replace('<br />', '\n'))

                    comp_effect = '\n'.join(temp)
                    # 将文本中markdown 语法符号**替换掉
                    pattern = r'\*\*(.*?)\*\*'
                    row[target_field_index['对应要变更点影响分析']].value = re.sub(pattern, r'\1', comp_effect)
                    row[target_field_index['对应要变更点影响分析']].alignment = alignment
                    break
                else:
                    row[target_field_index['对应要否']].value = gettext('否')

        wb.save(epic_path)
        logger.success('变更需求涉及的待修改软件组件信息成功写入要件一览表:{epic_path}'.format(epic_path=epic_path))
    except Exception as e:
        if isinstance(e, PermissionError):
            raise PermissionError(gettext("文件写入失败，请先关闭要写入的文件"))
        else:
            raise RuntimeError(gettext("变更需求待修改组件信息写入失败"))


# Demo usage
if __name__ == "__main__":
    '''
    policy_json = """{
  "design_policy": [
    {
      "design_policy": "修改DispBuild组件逻辑，将第0阶层定义为第一阶层。确保第1阶层的功能和显示内容正确加载。",
      "worry_point": [1, 2, 4],
      "worry_content": "担心因DispBuild组件未正确执行或顺序错误，导致功能显示层次未能完成调整，例如未准确加载第1阶层内容。可能存在逻辑处理错误使第1阶层功能显示异常。"
    },
    {
      "design_policy": "在dsp_app组件中，调整配置生成表，将所有第0阶层画面切换逻辑重新适配到第1阶层，以支持层次迁移。",
      "worry_point": [2, 4],
      "worry_content": "担心画面配置逻辑未正确调整，导致画面切换过程中层次定义错误或部分功能无法正常切换。可能出现画面显示错误，甚至部分功能不可用。"
    },
    {
      "design_policy": "在GraphicsFW组件中，修改渲染逻辑以适应第1阶层的显示内容，确保绘制的内容完整和准确。",
      "worry_point": [3, 4],
      "worry_content": "担心因处理时间超出或逻辑错误，导致渲染的内容不准确或者部分渲染失败，可能影响用户对功能操作的体验和准确性。"
    },
    {
      "design_policy": "在3D_Graphics组件中，修改数据读取和发送逻辑，以确保第1阶层数据能够正确读取并传递给GraphicsFW。",
      "worry_point": [5, 7],
      "worry_content": "担心数据传输时序未与第1阶层更新周期同步，或输入数据错误导致画面内容不准确，从而影响层次迁移的整体效果。"
    },
    {
      "design_policy": "对DispBuild组件和GraphicsFW组件进行联合测试，验证第1阶层功能显示的正确性以及画面切换逻辑的适配性。",
      "worry_point": [3, 5],
      "worry_content": "担心测试周期不足或关联测试资源未启动，导致未能及时发现层次迁移相关的显示错误或数据传递问题。例如跨组件的功能绘制异常。"
    }
  ]
}"""
    design_json = """{
  "methods": [
    {
      "design_method": "修改DispBuild组件逻辑时，通过详细设计审查明确第0阶层到第1阶层的逻辑迁移，各功能模块有效加载后进行单元测试，逐步验证其逻辑是否准确。",
      "eva_method": "通过单元测试验证每个功能点是否正确显示，同时通过层次迁移的功能点压力测试检查加载逻辑的稳定性和响应性能。并组合成全面的集成测试报告以确保完整性。"
    },
    {
      "design_method": "在dsp_app组件中进行代码重构和全面配置生成表的调整，同时加入自定义日志以捕捉画面切换逻辑中的可能错误，提高故障诊断效率。",
      "eva_method": "进行功能验证测试，检查画面切换过程是否符合预期；在测试环境中模拟多种用户操作情景进行逻辑验证；通过回归测试确保之前未受影响的功能行为保持一致。"
    },
    {
      "design_method": "在GraphicsFW组件中，重新设计渲染逻辑，针对具体功能联动引入缓冲队列机制，确保每次渲染内容均完整并准确生成。引入异常处理模块监控和降级策略。",
      "eva_method": "采用高频度压力测试监测渲染引擎行为，通过日志分析渲染错误发生的原因；进行性能测试评估渲染时间和内容准确性；进行跨模块集成测试以确保显示内容的正确联动。"
    },
    {
      "design_method": "调整3D_Graphics组件的数据读取逻辑，引入数据完整性检查机制，同时确保更新周期和显示周期以设定的时序同步。通过模块间联合测试验证功能逻辑一致性。",
      "eva_method": "开展周期性测试以验证数据传输同步性，通过动态数据模拟验证数据完整性；对跨组件的数据逻辑进行深度调试并分析传输结果；通过验证与GraphicsFW的交互来确保传输的准确性。"
    },
    {
      "design_method": "联合测试DispBuild组件和GraphicsFW组件，设计功能联动测试脚本，模拟复杂的使用场景，检验层次迁移后的功能显示和数据传递之间的适配性。",
      "eva_method": "通过多组件系统集成测试同时验证逻辑的适配性，并通过统计测试用例覆盖率确保功能点的全面验证；进行系统级压力测试确认迁移逻辑在高负载情况下的稳定性。此外，通过实时实验验证功能指标是否符合用户体验规范。"
    }
  ]
}"""
    generate_design_policy(policy_json, design_json, "阶层定义调整")

    '''

    # source_path = 'C:/data_430/design_policy_20250423_202451.md'
    # # 读取设计评价方针
    # with open(source_path, 'r', encoding='utf-8') as f:
    #     data = f.read()
    # df = markdown_to_pandas(data)
    # content = []
    # for idx, row in df.iterrows():
    #     if str(row.get('ARチケットNO')).upper() != 'NAN':
    #         content.append({
    #             "No": str(row.get('No')),
    #             "新規/変化点": str(row.get('新規/変化点')).replace('<br />', '\n'),
    #             "設計方針": str(row.get('設計方針')).replace('<br />', '\n'),
    #             "心配な点（該当する故障モードに○を記入）": [
    #                 [point.split(' ')[0], '〇' if point.split(' ')[1] == 'Yes' else '-'] for point in
    #                 str(row.get('心配な点（該当する故障モードに○を記入）')).split('<br />')
    #             ],
    #             "担心点具体内容": str(row.get('担心点具体内容')).replace('<br />', '\n'),
    #             "設計": str(row.get('設計・評価方法')).split('<br />')[0]
    #             .replace('<br />', '\n')
    #             .replace('；', '；\n'),
    #             "評価": str(row.get('設計・評価方法')).split('<br />')[1]
    #             .replace('<br />', '\n')
    #             .replace('；', '；\n')
    #         })
    #
    # # 生成markdown 并存储在当前文件夹
    # folder_path = f'{ENV.config.input_data_path}/template'
    # if not os.path.exists(folder_path):
    #     os.mkdir(folder_path)
    # dnkt_path = f'{ENV.config.input_data_path}/template/policy_20250425_114251.xlsx'
    #
    # design_police_to_excel(content, target_file_path=dnkt_path)
    #
    # # design_police_to_excel(
    # #     content,
    # #     target_file_path=f"{ENV.config.input_data_path}/template/【19PFv3】基本設計書-SocR_CSTM(MET19PFV3-30609).xlsx",
    # #     target_sheet_name="②機能別設計・評価方針")
