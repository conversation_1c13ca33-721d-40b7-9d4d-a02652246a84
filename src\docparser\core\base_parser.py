import os
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import uuid

from docparser.interfaces.document_interface import DocumentInterface
from docparser.models.document import DocumentObject
from docparser.models.text import TextObject, RunObject
from docparser.models.table import TableObject, RowObject, CellObject
from docparser.models.picture import PictureObject
from docparser.models.graphic import GraphicObject

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('docparser')

class BaseParser(DocumentInterface):
    """
    Base class for document parsers.
    Implements common functionality and defines abstract methods for specific parsers.
    """
    
    def __init__(self):
        self.document: DocumentObject = DocumentObject()
        self.supported_extensions = []
        self.plugins = []
        self._origin_object = None

    def parse_document(self, file_path: str) -> DocumentObject:
        """
        Parse a document from the given file path.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing parsed document data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_extension = os.path.splitext(file_path)[1].lower()
        if file_extension not in self.supported_extensions:
            raise ValueError(f"Unsupported file extension: {file_extension}")
        
        try:
            logger.info(f"Parsing document: {file_path}")
            
            # Initialize document metadata
            self.document = DocumentObject()
            self.document.file_path = file_path
            self.document.file_name = os.path.basename(file_path)
            self.document.file_type = file_extension[1:]  # Remove the dot
            
            # Parse document content
            self._parse_document_content(file_path)

            logger.info(f"Document parsed successfully: {file_path}")
            return self.document
            
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(f"Error parsing document: {e}")
            raise
    
    @abstractmethod
    def _parse_document_content(self, file_path: str) -> None:
        """
        Parse document content from the given file path.
        To be implemented by specific parsers.

        Args:
            file_path: Path to the document file
        """
        pass
    
    def get_text_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all text objects from the parsed document.
        
        Returns:
            List of text objects with their properties
        """
        texts = self.document.get_text_objects(index)
        return [obj.to_dict() for obj in texts]
    
    def get_table_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all table objects from the parsed document.
        
        Returns:
            List of table objects with their properties
        """
        tables = self.document.get_table_objects(index)
        return [obj.to_dict() for obj in tables]
    
    def get_picture_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all picture objects from the parsed document.
        
        Returns:
            List of picture objects with their properties
        """
        pictures = self.document.get_picture_objects(index)
        return [obj.to_dict() for obj in pictures]
    
    def get_graphic_objects(self, index: int) -> List[Dict[str, Any]]:
        """
        Get all graphic objects from the parsed document.
        
        Returns:
            List of graphic objects with their properties
        """
        graphics = self.document.get_graphic_objects(index)
        return [obj.to_dict() for obj in graphics]
    
    def export_to_json(self, output_path: str) -> None:
        """
        Export parsed document data to JSON format.
        
        Args:
            output_path: Path to save the JSON output
        """
        self.document.save_to_json(output_path)
        logger.info(f"Document exported to JSON: {output_path}")
    
    def register_plugin(self, plugin) -> None:
        """
        Register a plugin to be applied during document parsing.
        
        Args:
            plugin: Plugin instance implementing PluginInterface
        """
        self.plugins.append(plugin)
        logger.info(f"Plugin registered: {plugin.get_plugin_name()} v{plugin.get_plugin_version()}")
    
    def _apply_plugins(self) -> None:
        """Apply registered plugins to the parsed document"""
        if not self.plugins:
            return
        
        for plugin in self.plugins:
            try:
                logger.info(f"Applying plugin: {plugin.get_plugin_name()}")
                
                # Check if plugin supports this document type
                if self.document.file_type not in plugin.get_supported_document_types():
                    logger.warning(f"Plugin {plugin.get_plugin_name()} does not support {self.document.file_type} documents")
                    continue
                # Process document with plugin
                plugin.process_document(self.document)
                

                
                logger.info(f"Plugin applied successfully: {plugin.get_plugin_name()}")
                
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                logger.error(f"Error applying plugin {plugin.get_plugin_name()}: {e}")

    def add_text_object(self, text: str, page_number: int, **kwargs) -> TextObject:
        """
        Add a text object to the document.
        
        Args:
            text: Text content
            page_number: Page number where the text appears
            **kwargs: Additional text properties
            
        Returns:
            Created TextObject
        """
        text_obj = TextObject(
            id=str(uuid.uuid4()),
            text=text,
            page_number=page_number,
            **kwargs
        )
        self.document.text_objects.append(text_obj)
        return text_obj

    def add_table_object(self, rows: int, columns: int, page_number: int, **kwargs) -> TableObject:
        """
        Add a table object to the document.
        
        Args:
            rows: Number of rows
            columns: Number of columns
            page_number: Page number where the table appears
            **kwargs: Additional table properties
            
        Returns:
            Created TableObject
        """
        table_obj = TableObject(
            id=str(uuid.uuid4()),
            rows=rows,
            columns=columns,
            page_number=page_number,
            **kwargs
        )
        self.document.table_objects.append(table_obj)
        return table_obj
    
    def add_picture_object(self, data: bytes, format: str, page_number: int, **kwargs) -> PictureObject:
        """
        Add a picture object to the document.
        
        Args:
            data: Image data
            format: Image format
            page_number: Page number where the picture appears
            **kwargs: Additional picture properties
            
        Returns:
            Created PictureObject
        """
        picture_obj = PictureObject(
            id=str(uuid.uuid4()),
            page_number=page_number,
            **kwargs
        )
        picture_obj.set_image_data(data, format)
        self.document.picture_objects.append(picture_obj)
        return picture_obj
    
    def add_graphic_object(self, shape_type: str, page_number: int, **kwargs) -> GraphicObject:
        """
        Add a graphic object to the document.
        
        Args:
            shape_type: Type of shape
            page_number: Page number where the graphic appears
            **kwargs: Additional graphic properties
            
        Returns:
            Created GraphicObject
        """
        graphic_obj = GraphicObject(
            id=str(uuid.uuid4()),
            shape_type=shape_type,
            page_number=page_number,
            **kwargs
        )
        self.document.graphic_objects.append(graphic_obj)
        return graphic_obj
