"""
Warning Code Generation Service Models
警告代码生成服务数据模型
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


@dataclass
class WarningCodeGenConfig:
    """警告代码生成配置"""
    pre_sample_book_url: str = ""  # 变更前告警式样书路径
    after_sample_book_url: str = ""  # 变更后告警式样书路径
    code_gen_tools_url: str = ""  # 告警代码生成工具地址
    relate_book_folder: str = ""  # 关联式样书文件夹
    adas_book_path: str = ""  # ADAS式样书路径
    save_type: str = "new"  # 保存类型
    compare_tools_url: str = "" # 比较工具文件


@dataclass
class WarningCodeGenResult:
    """警告代码生成结果"""
    success: bool = False
    message: str = ""
    after_code_tools_url: str = ""  # 生成后的代码工具文件路径
    pre_code_tools_url: str = ""  # 生成前的代码工具文件路径
    error_details: Optional[str] = None
    
    
@dataclass
class WarningChangeInfo:
    """警告变更信息"""
    pre_sample_book_url: str = ""
    after_sample_book_url: str = ""
    compare_tools_url: str = ""
    code_gen_tools_url: str = ""
    pre_code_tools_url: str = ""
    after_code_tools_url: str = ""
    relate_book_folder: str = ""
    adas_book_path: str = ""
    code_folder: str = ""
    check_file_path: str = ""
    after_warning_num: int = 0
    change_row_result: Dict[str, Dict[str, str]] = field(default_factory=dict)
    compare_tools_code_result: Dict[str, Dict[str, List[str]]] = field(default_factory=dict)


@dataclass
class WarningPropertyInfo:
    """警告属性信息"""
    dual_channel_keys: List[str] = field(default_factory=list)
    adas_keys: List[str] = field(default_factory=list)
    new_rule_keys: List[str] = field(default_factory=list)
    big_intr_keys: List[str] = field(default_factory=list)


@dataclass
class SheetProcessConfig:
    """工作表处理配置"""
    warning_sheet_name: str = "一覧"
    warning_codetool_sheet_name: str = "CONTDISP (源)"
    target_column: tuple = ("A", "Y")
    row_offset: int = -3


"""
Warning Code Generation and Check Service Models
警告代码生成和检查服务数据模型
"""

from pathlib import Path


@dataclass
class WarningCodeGenResult:
    """警告代码生成结果"""
    success: bool = False
    message: str = ""
    after_code_tools_url: str = ""  # 生成后的代码工具文件路径
    pre_code_tools_url: str = ""  # 生成前的代码工具文件路径
    error_details: Optional[str] = None
    warning_change_info: WarningChangeInfo = None  # 警告变更信息


@dataclass
class WarningCodeCheckConfig:
    """警告代码检查配置"""
    code_folder: str = ""  # 代码仓库路径
    warning_change_info: 'WarningChangeInfo' = None  # 警告变更信息
    check_file_path: str = ""  # 检查结果文件路径


@dataclass
class WarningCodeCheckResult:
    """警告代码检查结果"""
    success: bool = False
    message: str = ""
    check_file_path: str = ""  # 检查结果文件路径
    check_results: Dict[str, Any] = field(default_factory=dict)  # 详细检查结果
    error_details: Optional[str] = None


@dataclass
class WarningCodeGenAndCheckConfig:
    """警告代码生成和检查配置"""
    pre_sample_book_url: str = ""  # 变更前告警式样书路径
    after_sample_book_url: str = ""  # 变更后告警式样书路径
    code_gen_tools_url: str = ""  # 告警代码生成工具地址
    relate_book_folder: str = ""  # 关联式样书文件夹
    adas_book_path: str = ""  # ADAS式样书路径
    code_folder: str = ""  # 代码仓库路径
    check_file_path: str = ""  # 检查结果文件路径


@dataclass
class WarningCodeGenAndCheckResult:
    """警告代码生成和检查结果"""
    success: bool = False
    message: str = ""
    check_file_path: str = ""  # 检查结果文件路径
    after_code_tools_url: str = ""  # 生成后的代码工具文件路径
    pre_code_tools_url: str = ""  # 生成前的代码工具文件路径
    check_results: Dict[str, Any] = field(default_factory=dict)  # 详细检查结果
    error_details: Optional[str] = None


@dataclass
class WarningChangeInfo:
    """警告变更信息"""
    pre_sample_book_url: str = ""
    after_sample_book_url: str = ""
    compare_tools_url: str = ""
    code_gen_tools_url: str = ""
    pre_code_tools_url: str = ""
    after_code_tools_url: str = ""
    relate_book_folder: str = ""
    meter_interface_popup_url: str = ""
    adas_book_path: str = ""
    code_folder: str = ""
    check_file_path: str = ""
    after_warning_num: int = 0
    change_row_result: Dict[str, Dict[str, str]] = field(default_factory=dict)
    compare_tools_code_result: Dict[str, Dict[str, List[str]]] = field(default_factory=dict)


@dataclass
class CheckResultInfo:
    """检查结果信息"""
    is_error: bool = False
    description: str = ""
    details: Optional[str] = None


@dataclass
class MacroValueInfo:
    """宏定义值信息"""
    file_path: str = ""
    value: int = 0


@dataclass
class CodeValidationResult:
    """代码验证结果"""
    is_valid: bool = True
    error_messages: List[str] = field(default_factory=list)
    warning_messages: List[str] = field(default_factory=list)


@dataclass
class ExcelSheetCheckResult:
    """Excel工作表检查结果"""
    sheet_name: str = ""
    is_valid: bool = True
    error_count: int = 0
    warning_count: int = 0
    details: List[str] = field(default_factory=list)


@dataclass
class FunctionAnalysisResult:
    """函数分析结果"""
    function_name: str = ""
    is_valid: bool = True
    line_count: int = 0
    error_messages: List[str] = field(default_factory=list)


class RequirementChangeContentFormat(BaseModel):
    """
    LLM响应格式定义
    """
    contdsip_id: str = Field(description="用于记录CONTDSIP-NO")
    contdsip_change_desc: str = Field(description="用于记录CONTDSIP变更内容的描述")
    need_parse_contdsip: str = Field(description="用于记录是否需要处理这个CONTDSIP-NO")

@dataclass
class RequirementChangeContentList(BaseModel):
    """
    LLM响应格式定义
    """
    requirement_change_content_list: list[RequirementChangeContentFormat] = Field(
        description="用于记录CONTDSIP变更内容的列表")