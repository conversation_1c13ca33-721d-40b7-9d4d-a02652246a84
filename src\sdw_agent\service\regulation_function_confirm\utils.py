"""
法规功能确认工具模块

V字对应：
1.3 要件分析
法规确认模块

实现法规确认的核心流程，集成LLM能力进行自动化评估

主要功能：
1. 法规分类
2. LLM调用
3. Excel数据读写
"""
# 标准库
from typing import List, Tuple, Any, Dict, Callable, Optional

# 第三方库
import openpyxl
from loguru import logger
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import AIMessage
from openpyxl import load_workbook
from openai import AzureOpenAI
import numpy as np

# 本地应用
from sdw_agent.llm.llm_util import get_ai_message
from .models import (
    RegulationCategory, RegulationImpact,
    RegulationInputData,ClassificationResult
)



def common_llm(prompt: str, config: Dict[str, Any]) -> str:
    """
    通用LLM调用函数

    Args:
        prompt: 提示文本
        config: LLM配置

    Returns:
        str: LLM响应文本
    """
    # 获取系统提示词
    system_prompt = config.get("llm", {}).get(
        "system_prompt",
        "你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题"
    )

    # 组合成聊天提示模板
    chat_prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("user", "{question}"),
    ])

    invoke_dict = {
        "question": prompt
    }

    # 调用LLM
    try:
        llm_response: AIMessage = get_ai_message(chat_prompt, invoke_dict)
        result = llm_response.content
        return result
    except Exception as e:
        logger.error(f"LLM调用失败: {str(e)}")
        return ""


def embedding_func(texts: list[str], config: Dict[str, Any]) -> np.ndarray:
    """
    将文本转换为向量表示

    Args:
        texts: 文本列表
        config: Azure配置

    Returns:
        np.ndarray: 嵌入向量
    """
    # 获取配置
    azure_config = config.get("azure", {})
    api_key = azure_config.get("openai_api_key")
    api_version = azure_config.get("embedding_api_version")
    endpoint = azure_config.get("openai_endpoint")
    deployment = azure_config.get("embedding_deployment")
    batch_size = config.get("embedding", {}).get("batch_size", 5)

    if not api_key or not api_version or not endpoint or not deployment:
        raise ValueError("请确保所有Azure OpenAI相关配置已设置！")

    client = AzureOpenAI(
        api_key=api_key,
        api_version=api_version,
        azure_endpoint=endpoint,
    )

    all_embeddings = []
    for i in range(0, len(texts), batch_size):
        batch = texts[i:i + batch_size]
        embedding = client.embeddings.create(model=deployment, input=batch)
        all_embeddings.extend([item.embedding for item in embedding.data])

    if not texts:
        logger.warning("未提供文本，返回空嵌入向量")
        return np.array([])

    return np.array(all_embeddings)



def calculate_similarity(
    query_embedding: np.ndarray,
    document_embeddings: np.ndarray,
    regulation_texts: List[str]  # 新增参数
) -> Tuple[int, str, float]:
    """
        计算查询向量与文档向量集的相似度，返回最相关的文档索引、文本和相似度分数

        参数:
            query_embedding: 查询文本的嵌入向量 (1维数组)
            document_embeddings: 文档嵌入向量矩阵 (n×d维数组)
                当为None或空数组时返回默认结果
            regulation_texts: 法规文本列表

        返回:
            Tuple包含：
            - 最相关文档索引（-1表示无匹配）
            - 匹配的法规文本
            - 相似度分数（0-1）
        """

    if document_embeddings is None or document_embeddings.size == 0 or not regulation_texts:
        return -1, "无相关法规数据", 0.0

    query_norm = np.linalg.norm(query_embedding)
    doc_norms = np.linalg.norm(document_embeddings, axis=1)

    # 修复：避免除以零错误
    if query_norm == 0 or np.any(doc_norms == 0):
        return -1, "嵌入向量无效（范数为零）", 0.0

    cosine_similarities = np.dot(document_embeddings, query_embedding) / (query_norm * doc_norms)

    # 获取最相关的文档
    top_index = np.argmax(cosine_similarities)
    top_similarity = cosine_similarities[top_index]
    relevant_text = regulation_texts[top_index] if 0 <= top_index < len(regulation_texts) else "索引越界"

    return top_index, relevant_text, top_similarity


def extract_governing_overview(file_path: str) -> List[str]:
    """
    使用 openpyxl 从 Excel 文件中提取所有工作表的「規制概要」列内容
    :param file_path: Excel 文件路径
    :return: 包含所有提取文本的列表
    """
    # 初始化一个空列表，用于存储所有提取的文本
    all_texts = []

    try:
        # 加载 Excel 文件
        workbook = load_workbook(filename=file_path, data_only=True)

        # 遍历工作簿中的所有工作表名称
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]

            # 初始化目标列索引和标题行索引
            target_col_index = None
            header_row_idx = None
            # 标记是否找到了目标列
            found = False

            # 设置检查的最大行数以防止无限循环
            max_rows_to_check = 200

            # 遍历前 max_rows_to_check 行以查找「規制概要」列
            for row_idx in range(1, min(max_rows_to_check + 1, sheet.max_row)):
                row = sheet[row_idx]
                for col_idx, cell in enumerate(row):
                    if cell.value == "規制概要":
                        target_col_index = col_idx
                        header_row_idx = row_idx
                        found = True
                        break
                if found:
                    break

            # 如果未找到目标列，则跳过当前工作表
            if target_col_index is None:
                continue

            # 遍历目标列的每一行并提取文本
            for row in sheet.iter_rows(min_row=header_row_idx + 1, values_only=True):
                cell_value = row[target_col_index]
                if cell_value is not None:
                    # 清理文本并添加到列表中
                    cleaned_text = str(cell_value).replace('\u3000', '').replace('\n', '')
                    all_texts.append(cleaned_text)

        # 返回提取的文本列表
        return all_texts

    except Exception as e:
        # 记录错误日志并返回空列表
        logger.error(f"[错误] 读取文件失败: {e}")
        return []


def read_excel_columns(file_path: str) -> List[RegulationInputData]:
    """
    读取Excel文件中的指定列数据。

    本函数旨在从给定的Excel文件中提取特定列的数据。这些列由列名标识，列名在文件中的位置是已知的。
    函数首先尝试定位这些列，然后从这些列中提取数据并返回。

    Returns:
        List[RegulationInputData]: 从指定列提取的数据列表
        空列表表示读取失败或数据为空

    Raises:
        FileNotFoundError: 当文件不存在时
        ValueError: 当必要列缺失或数据为空时
    """
    try:
        # 加载工作簿和活动工作表
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active

        # 定义目标列名
        target_columns = {
            "要件の内容": None,
            "ソフトの変更内容": None
        }

        # 遍历第5、6、7行，查找列名对应的列索引
        for row in range(5, 8):  # 行号从5到7
            for col in range(1, ws.max_column + 1):  # 遍历所有列
                cell = ws.cell(row=row, column=col)
                if cell.value in target_columns:
                    target_columns[cell.value] = col

        # 检查是否成功找到两列
        if None in target_columns.values():
            missing_cols = [k for k, v in target_columns.items() if v is None]
            raise ValueError(f"未找到必要列: {', '.join(missing_cols)}")

        # 获取列索引
        req_col = target_columns["要件の内容"]
        chg_col = target_columns["ソフトの変更内容"]

        # 从第8行开始读取数据
        data = []
        for row in range(8, ws.max_row + 1):
            req_val = ws.cell(row=row, column=req_col).value
            chg_val = ws.cell(row=row, column=chg_col).value

            # 当两列同时为None时，停止读取
            if req_val is None and chg_val is None:
                break

            # 否则，添加到结果中
            data.append(RegulationInputData(ReqDetail=req_val, ChgDetail=chg_val))

        # 检查是否提取到数据
        if not data:
            raise ValueError("提取的数据为空")

        return data

    except FileNotFoundError:
        logger.error(f"错误：文件 {file_path} 未找到")
        return []
    except ValueError as ve:
        logger.error(f"数据处理错误: {ve}")
        return []
    except Exception as e:
        logger.error(f"读取文件时发生错误: {e}")
        return []


def write_classification_to_excel(file_path: str, data: List[ClassificationResult]) -> bool:
    """
    将分类结果写入Excel文件的"法規評価の分類"列和"法規機能の変更"列

    Args:
        file_path: Excel文件路径
        data: 包含ClassifiedResult和IsChanged的数据列表

    Returns:
        bool: 写入成功返回True，否则False
    """
    try:
        # 加载工作簿和活动工作表
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active

        # 定义需要查找的目标列名
        target_columns = {
            "法規評価の分類": None,
            "法規機能の変更": None
        }

        # 设置查找行范围（比如第5到10行）
        search_rows = range(5, 11)

        # 遍历查找行和列
        for row in search_rows:
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value is None:
                    continue

                cell_value = str(cell.value).strip()  # 去除前后空格

                # 判断是否为目标列
                for target_name in target_columns:
                    if cell_value == target_name.strip():
                        target_columns[target_name] = col

        # 检查是否找到所有目标列
        missing_cols = [col_name for col_name, col_idx in target_columns.items() if col_idx is None]
        if missing_cols:
            raise ValueError(f"未找到目标列: {', '.join(missing_cols)}")

        # 获取列索引
        target_col_classified = target_columns["法規評価の分類"]
        target_col_ischanged = target_columns["法規機能の変更"]

        # 从第8行开始写入数据
        for row_idx, item in enumerate(data, start=8):
            ws.cell(row=row_idx, column=target_col_classified).value = item.ClassifiedResult.value
            ws.cell(row=row_idx, column=target_col_ischanged).value = item.is_changed.value

        # 保存修改
        wb.save(file_path)
        return True

    except FileNotFoundError:
        logger.error(f"错误：文件 {file_path} 未找到")
        return False
    except Exception as e:
        logger.error(f"写入文件时发生错误: {e}")
        return False


def classify_change(
        input_data: RegulationInputData,
        config: Dict[str, Any],
        llm_functions: Optional[List[Callable[[str, str], str]]] = None
) -> ClassificationResult:
    """
    法规变更分类工作流函数

    根据请求详情和变更内容，调用LLM模型进行分类评估，返回符合数据流规范的分类结果

    Args:
        input_data: 包含要件内容和变更内容的输入数据模型
        config: 配置字典，包含LLM参数和知识库信息
        llm_functions: 可选的LLM模型函数列表，默认使用预定义模型列表

    Returns:
        ClassificationResult: 包含分类结果和变更影响的分类结果对象

    Raises:
        无显式抛出异常，模型调用失败时记录日志并继续执行
    """
    default_llm_functions = [
        rf_itself_llm,
        rf_related_llm,
        rf_priority_llm,
        rf_display_conditions_llm,
        rf_display_location_llm
    ]

    # 使用传入的模型列表或默认列表
    llm_funcs = llm_functions if llm_functions is not None else default_llm_functions

    # 初始化分类结果为默认枚举值
    classified_result_enum = RegulationCategory.NO_EVALUATION  # 对应"⑥追加評価不要"

    # 依次调用模型，直到获得有效结果或所有模型处理完毕
    for func in llm_funcs:
        try:
            # 记录当前正在调用的模型
            func_name = func.__name__
            logger.info(f"开始调用模型: {func_name}")

            # 调用模型函数，传入结构化的输入数据
            raw_result = func(input_data.ReqDetail, input_data.ChgDetail, config)

            # 处理原始结果
            processed_result = raw_result.strip().replace('"', '')
            logger.info(f"模型 {func_name} 返回结果: {processed_result}")

            # 验证结果是否有效（非"NO"且不为空）
            if processed_result and processed_result != "NO":
                # 将字符串结果映射到RegulationCategory枚举
                try:
                    # 查找匹配的枚举值
                    classified_result_enum = next(
                        enum for enum in RegulationCategory
                        if enum.value == processed_result
                    )
                    logger.info(f"模型 {func_name} 确定分类结果: {classified_result_enum}")
                    break
                except StopIteration:
                    logger.warning(f"模型返回未定义的分类结果: {processed_result}")
                    continue

        except Exception as e:
            # 记录模型调用异常，但不中断整个工作流
            logger.error(f"模型 {func.__name__} 调用失败: {str(e)}")
            continue

    # 判断变更影响
    is_changed = RegulationImpact.YES if classified_result_enum != RegulationCategory.NO_EVALUATION else RegulationImpact.NO

    # 构建并返回ClassificationResult对象
    return ClassificationResult(
        ClassifiedResult=classified_result_enum,
        is_changed=is_changed,
        similarity_score=None,
        relevant_regulation=None
    )



def rf_itself_llm(req_detail: str, chg_detail: str, config: Dict[str, Any]) -> ClassificationResult:
    """
    判断变更是否涉及法规功能自身

    Args:
        req_detail: 要件内容描述文本
        chg_detail: 变更内容描述文本
        config: 包含知识库和LLM配置的字典

    Returns:
        str: 返回①法規機能の全評価或NO的评估结果

    Raises:
        无显式抛出异常，错误时返回空字符串
    """
    # 从config获取预处理的嵌入和文本
    regulation_embeddings = config.get("regulation_embeddings")
    regulation_texts = config.get("regulation_texts", [])
    # 构建变更文本
    change_text = f"要件内容：{req_detail}\n变更内容：{chg_detail}"
    change_embedding = embedding_func([change_text], config)[0]
      # 计算相似度
    _, relevant_regulation, similarity_score = calculate_similarity(
        change_embedding,
        regulation_embeddings,
        regulation_texts  # 传入文本列表
    )
    kb = config.get("kb", {})
    relevant_regulation = kb.get("relevant_regulation", "无相关法规数据")
    similarity_score = kb.get("similarity_score", 0.0)
    prompt = f"""
    我需要你帮我根据“要件の内容”和“ソフトの変更内容”中分辨出该变更内容是否时对法规功能本身的变更。输入可能是可能是中文、日文、英文。
    <要件の内容>
        {req_detail}
    </要件の内容>
    <ソフトの変更内容>
        {chg_detail}
    </ソフトの変更内容>
    <法规概要>
        {relevant_regulation}
    </法规概要>
    <相似度>
        {similarity_score}
    </相似度>
    <要求>
        如果是对法规功能自身的变更则回答我“①法規機能の全評価"，如果不是则回答我”NO“，不需要其它任何解释
    </要求>
    """
    result = common_llm(prompt, config)
    return str(result).strip()


def rf_related_llm(req_detail: str, chg_detail: str, config: Dict[str, Any]) -> ClassificationResult:
    """
    判断变更是否对法规功能有间接影响

    Args:
        req_detail: 要件内容描述文本
        chg_detail: 变更内容描述文本
        config: 包含知识库和LLM配置的字典

    Returns:
        str: 返回返回变更结果
    """
    prompt = f"""
    我需要你帮我根据“要件の内容”和“ソフトの変更内容”中分辨出该变更内容是否对法规功能有间接影响。输入可能是中文、日文、英文。
    <要件の内容>
        {req_detail}
    </要件の内容>
    <ソフトの変更内容>
        {chg_detail}
    </ソフトの変更内容>
    <要求>
        如果存在间接影响则回答"②変更点と法規機能の全評価"，否则回答"NO"，不需要其它解释
    </要求>
    """
    result = common_llm(prompt, config)
    return str(result).strip()


def rf_priority_llm(req_detail: str, chg_detail: str, config: Dict[str, Any]) -> ClassificationResult:
    """
    判断变更是否涉及法规功能优先级调整

    Args:
        req_detail: 要件内容描述文本
        chg_detail: 变更内容描述文本
        config: 包含知识库和LLM配置的字典

    Returns:
        str: 返回变更结果
    """
    prompt = f"""
    我需要你帮我根据“要件の内容”和“ソフトの変更内容”中分辨出该变更内容是否对法规功能优先级调整有变更。输入可能是中文、日文、英文。
    <要件の内容>
        {req_detail}
    </要件の内容>
    <ソフトの変更内容>
        {chg_detail}
    </ソフトの変更内容>
    <要求>
        如果存在法规功能更优先级调整则回答"③優先度調停の組合せ評価"，否则回答"NO"，不需要其它解释
    </要求>
    """
    result = common_llm(prompt, config)
    return str(result).strip()


def rf_display_conditions_llm(req_detail: str, chg_detail: str, config: Dict[str, Any]) -> ClassificationResult:
    """
    判断变更是否涉及法规功能显示条件的改变

    Args:
        req_detail: 要件内容描述文本
        chg_detail: 变更内容描述文本
        config: 包含知识库和LLM配置的字典

    Returns:
        str: 返回变更结果
    """
    prompt = f"""
        我需要你帮我根据“要件の内容”和“ソフトの変更内容”中分辨出该变更内容是否是法规功能显示条件的改变。输入可能是中文、日文、英文。
        <要件の内容>
            {req_detail}
        </要件の内容>
        <ソフトの変更内容>
            {chg_detail}
        </ソフトの変更内容>
        <要求>
            如果是对法规功能显示位置的相关改变则回答"④法規機能の全表示条件全評価"，否则回答"NO"，不需要其它解释
        </要求>
        """
    result = common_llm(prompt, config)
    return str(result).strip()


def rf_display_location_llm(req_detail: str, chg_detail: str, config: Dict[str, Any]) -> ClassificationResult:
    """
    判断变更是否涉及法规功能显示位置相关的改变

    Args:
        req_detail: 要件内容描述文本
        chg_detail: 变更内容描述文本
        config: 包含知识库和LLM配置的字典

    Returns:
        str: 返回评估结果
    """
    prompt = f"""
    我需要你帮我根据“要件の内容”和“ソフトの変更内容”中分辨出该变更内容是否是法规功能显示位置相关的改变。输入可能是中文、日文、英文。
    <要件の内容>
        {req_detail}
    </要件の内容>
    <ソフトの変更内容>
        {chg_detail}
    </ソフトの変更内容>
    <要求>
        如果是对法规功能显示位置的相关改变则回答"⑤法規機能の画面検査"，否则回答"NO"，不需要其它解释
    </要求>
    """
    result = common_llm(prompt, config)
    return str(result).strip()
