"""
RAM确认工具函数

提供RAM确认相关的核心功能函数
"""

from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from loguru import logger

from sdw_agent.service.ram_confirm_service.models import GlobalVarChangeInfo
from sdw_agent.util.extract_c_function_calling import CFunctionCallAnalyzer
from sdw_agent.util.extract_code_util import CodeAnalyzer
from sdw_agent.util.file_base_util import create_text_file_link
from sdw_agent.util.git_util import get_git_structured_diff


def search_global_var_from_diff(
        repo_path: str,
        global_vars_info: Dict[str, List[Dict[str, str]]],
        code_diff: List[Dict[str, Any]]
) -> List[GlobalVarChangeInfo]:
    """
    从代码差异中查找全局变量变更

    Args:
        repo_path (str): 仓库路径，用于定位代码文件位置
        global_vars_info (Dict[str, List[Dict[str, str]]]): 全局变量信息字典，
            键为文件路径，值为该文件中的全局变量列表，每个变量包含名称和类型等信息
        code_diff (List[Dict[str, Any]]): 代码差异信息列表，每个元素表示一个文件的变更信息，
            包含文件路径和新增行等内容

    Returns:
        List[GlobalVarChangeInfo]: 全局变量变更信息列表，每个元素表示一个被修改的全局变量及其相关信息
    """
    # 日志记录开始查找全局变量变更
    logger.info("从代码差异中查找全局变量变更")
    # 初始化用于存储变更的全局变量列表
    changed_vars = []
    code_analyzer = CodeAnalyzer()
    # 遍历代码差异中的每个文件信息
    for file_info in code_diff:
        # 获取文件路径、新增行和行号
        file_path = file_info.get('file_path')
        added_lines = file_info.get('added_lines', [])

        # 如果文件路径不存在或不在全局变量信息中，或没有相关的全局变量信息，则跳过
        if not file_path or file_path not in global_vars_info or not global_vars_info[file_path]:
            continue
        # 获取当前文件的全局变量和完整内容
        file_vars = global_vars_info[file_path]
        # 遍历当前文件中的每个变量
        added_lines_content = " ".join(added_lines)
        for var_info in file_vars:
            # 检查变量是否在新增行中
            if var_info["name"] in added_lines_content:
                called_funcs = code_analyzer.find_all_functions(repo_path, var_info["name"])
                for called_func in called_funcs:
                    changed_vars.append(GlobalVarChangeInfo(
                        name=var_info["name"],
                        content=called_func["line_content"],
                        line=called_func["line_number"],
                        file_path=called_func["file_path"],
                        change_func=called_func["function_name"],
                        var_type=var_info["type"]))
    # 返回全局变量变更信息列表
    return changed_vars



def search_global_var(
        repo_path: str,
        commit_id: str,
        compared_commit_id: Optional[str] = None
) -> List[GlobalVarChangeInfo]:
    """
    搜索全局变量变更

    Args:
        repo_path: 仓库路径
        commit_id: 提交ID
        compared_commit_id: 对比提交ID

    Returns:
        List[GlobalVarChangeInfo]: 变更的全局变量列表
    """
    try:
        # 开始搜索全局变量变更的日志记录
        logger.info("开始搜索全局变量变更")

        # 获取代码差异
        code_diff = get_diff_code(repo_path, commit_id, compared_commit_id)
        # 如果没有代码差异，则直接返回空列表
        if not code_diff:
            return []

        # 提取全局变量信息
        global_vars_info = {}
        for file_diff in code_diff:
            # 获取文件路径
            file_path = file_diff.get('file_path')
            if file_path:
                # 获取文件的完整代码
                full_code = file_diff.get('full_code', [])
                # 从完整代码中搜索全局变量
                global_vars = CodeAnalyzer().search_global_vars(full_code)
                # 将全局变量信息和代码内容保存到字典中
                global_vars_info[file_path] = global_vars

        # 查找修改的全局变量
        changed_vars = search_global_var_from_diff(
            repo_path, global_vars_info, code_diff
        )

        # 搜索完成后记录日志
        logger.info(f"搜索全局变量变更完成，找到{len(changed_vars)}个变更")
        # 返回变更的全局变量列表
        return changed_vars

    except Exception as e:
        # 搜索全局变量变更失败时记录错误日志
        logger.error(f"搜索全局变量变更失败: {e}")
        # 返回空列表
        return []



def get_diff_code(repo_path, commit_id, compared_commit_id=None):
    """
    根据commit获取变更代码及所在文件的全量代码

    参数:
    - repo_path: 仓库路径
    - commit_id: 需要比较的commit ID
    - compared_commit_id: 可选的比较对象commit ID，默认为None，如果提供，则比较两个commit之间的差异

    返回:
    - code_diffs: 包含变更代码和全量代码的列表，每个元素对应一个文件
    """
    # 获取结构化的diff信息
    diffs = get_git_structured_diff(repo_path, commit_id, compared_commit_id)
    code_diffs = []

    # 遍历每个文件的diff信息
    for file_path, diff_info in diffs.items():
        added_lines = []
        full_code = []
        hunks = diff_info.get("diff", [])

        # 遍历文件中的每个hunk
        for hunk in hunks:
            content = hunk.get('content', [])
            if hunk['type'] == 'ab':
                # 公共部分，加入全量代码
                full_code.extend(content)
            elif hunk['type'] == 'b':
                # 新增代码，加入新增列表和全量代码
                added_lines.extend(content)
                full_code.extend(content)
            else:
                # 其他类型（如 a: 删除）仅用于构建全量代码
                full_code.extend(content)

        # 将文件路径、新增代码和全量代码打包成字典，添加到结果列表中
        code_diffs.append({
            "file_path": diff_info["file_path"],
            "added_lines": added_lines,
            "full_code": full_code,
            "line_num": diff_info.get("changed_lines", {}).get("added", [])
        })

    return code_diffs



def get_schdlr_rglr_info(schdlr_rglr_tasks: dict[str, str], func_name: str) -> str:
    """
    获取函数的调度信息

    Args:
        schdlr_rglr_tasks: 调度任务集合，键为函数名，值为调度间隔时间
        func_name: 要查询的函数名称

    Returns:
        str: 返回函数对应的调度间隔时间，如果函数不存在则返回空字符串
    """
    # 记录调试日志，用于跟踪函数调度信息的查询过程
    logger.debug(f"检查函数调度信息: {func_name}")

    # 查找函数名是否在调度任务集合中，如果存在则返回对应的调度信息
    if func_name in schdlr_rglr_tasks.keys():
        logger.info(f"在调度任务中找到函数: {func_name} -> {schdlr_rglr_tasks[func_name]}")
        return schdlr_rglr_tasks[func_name]
    return ""



def get_schdlr_rglr_func(
        schdlr_rglr_tasks: dict[str, str],
        repo_path: str,
        func_name: str,
        _visited: Optional[Set[str]] = None,
        _func_cache: Optional[Dict[str, List[str]]] = None
) -> str:
    """
    获取函数的调度表信息（优化版：只返回最终调度源）

    Args:
        schdlr_rglr_tasks: 调度任务集合，键为函数名，值为对应的调度信息
        repo_path: 仓库路径，用于代码分析时定位源文件
        func_name: 当前要查找调度信息的目标函数名
        _visited: 已访问的函数集合，用于防止递归调用中出现循环依赖导致无限递归
        _func_cache: 函数调用关系缓存，避免重复分析同一函数的调用者

    Returns:
        str: 找到的调度信息字符串，若未找到则返回空字符串
    """

    # 初始化默认参数
    if _visited is None:
        _visited = set()
    if _func_cache is None:
        _func_cache = {}

    # 防止因循环调用引起的无限递归
    if func_name in _visited:
        return ""
    _visited.add(func_name)

    try:
        # 1. 首先尝试直接从调度任务中查找当前函数的调度信息
        schdlr_rglr_info = get_schdlr_rglr_info(schdlr_rglr_tasks, func_name)
        if schdlr_rglr_info:
            logger.info(f"找到函数的调度间隔: {func_name} -> {schdlr_rglr_info}")
            return schdlr_rglr_info

        # 2. 若未直接配置调度信息，则查找调用该函数的上层函数
        if func_name not in _func_cache:
            analyzer = CFunctionCallAnalyzer(
                root_path=repo_path,
                target_function=func_name,
                debug=False
            )
            _func_cache[func_name] = analyzer.get_caller_names()

        # 过滤掉自身和已访问过的函数，防止循环或重复处理
        calling_funcs = [f for f in _func_cache[func_name] if f != func_name and f not in _visited]

        if not calling_funcs:
            logger.debug(f"函数 {func_name} 没有找到调用者，可能是入口函数")
            return ""

        # 3. 递归向上查找第一个有效的调度源（广度优先策略）
        for calling_func in calling_funcs:
            result = get_schdlr_rglr_func(
                schdlr_rglr_tasks,
                repo_path,
                calling_func,
                _visited.copy(),  # 使用副本避免影响其他分支的访问记录
                _func_cache
            )
            if result:  # 找到调度源就立即返回，提升效率
                logger.info(f"通过调用链找到函数 {func_name} 的调度: {result}")
                return result

        return ""

    except Exception as e:
        logger.error(f"获取函数调度信息失败: {func_name}, 错误: {str(e)}")
        return ""



def get_schdlr_rglrs(
        schdlr_rglr_tasks: dict[str, str],
        repo_path: str,
        func_names: set[str]
) -> Dict[str, str]:
    """
    批量获取函数的调度信息

    Args:
        schdlr_rglr_tasks: 调度任务集合
        repo_path: 仓库路径
        func_names: 函数名列表

    Returns:
        Dict[str, str]: 函数到调度信息列表的映射
    """
    # 初始化结果字典和函数缓存
    results = {}
    _func_cache = {}

    # 遍历函数名列表
    for func_name in func_names:
        # 忽略空的函数名
        if not func_name:
            continue

        # 调用get_interrupt_table_func函数获取每个函数的调度信息，并存储在结果字典中
        interrupt_info = get_schdlr_rglr_func(
            schdlr_rglr_tasks,
            repo_path,
            func_name,
            None,
            _func_cache
        )
        if not interrupt_info:
            interrupt_info = "-"

        results[func_name] = interrupt_info.lower()

    # 返回包含所有函数调度信息的字典
    return results



def gen_ram_confirm_data(repo_path, global_vars_info, author, regular_task) -> List[List[str]]:
    """
    生成RAM确认数据
    
    Args:
        repo_path: 项目文件路径
        global_vars_info: 全局变量信息列表
        author: 实施者
        regular_task: 调度信息

    Returns:
        List[List[str]]: 格式化的RAM确认数据
    """
    logger.info(f"生成RAM确认数据，共 {len(global_vars_info)} 个变量")
    result = []
    # 获取所有变更函数的集合
    change_funcs = set([global_var.change_func for global_var in global_vars_info])
    # 获取调度规则信息
    schdlr_rglrs = get_schdlr_rglrs(
        regular_task,  repo_path, change_funcs
    )
    # 遍历全局变量信息，构建确认数据
    for global_var in global_vars_info:
        result.append([author,  # 实施者
                       "",  # 地址‌
                       global_var.name,  # 变量名
                       global_var.var_type,  # 变量类型
                       schdlr_rglrs.get(global_var.change_func, "-"),  # 处理时间
                       global_var.file_path,  # 文件名
                       global_var.change_func,  # 函数名
                       {"url": create_text_file_link(str(Path(repo_path) / global_var.file_path), global_var.line,
                                                     "vscode"),
                        "text": str(global_var.line)},  # 行号
                       global_var.content,  # 代码内容
                       get_read_write(global_var.content)  # READ/WRITE属性
                       ])
    return result



def get_read_write(line_content):
    """
    根据行内容判断操作类型是读操作还是写操作

    参数:
        line_content (str): 代码行内容字符串

    返回值:
        str: "R"表示读操作，"W"表示写操作
    """
    if not line_content:
        return "R"

    # 处理包含比较操作符的情况
    if "==" in line_content or "!=" in line_content:
        return "R"

    # 检查是否为赋值操作
    if "=" in line_content:
        return "W"
    else:
        return "R"

