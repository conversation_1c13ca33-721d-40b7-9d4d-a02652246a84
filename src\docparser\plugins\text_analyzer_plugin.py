import re
import logging
from typing import Any, Dict, List, Optional, Union, Literal
from docparser.models.text import TextObject
import json
import copy
from kotei_formal_rule.match_rule import translate_parse
from docparser.models.document import DocumentBlockObject, DocumentObject
from docparser.interfaces.plugin_interface import PluginInterface, TextProcessorPlugin

# Configure logging
logger = logging.getLogger('docparser')

class TextAnalyzerPlugin(TextProcessorPlugin):

    def __init__(self, result=None, document=None, block_name="", path=None):
        if result:
            self._result = result
        else:
            self._result = []
        if document:
            self.document = document
        # word时为空字符串，excel时对应worksheet名称
        self._block_name = block_name
        if path:
            self._path = path
    """
    Plugin for analyzing text objects in documents.
    Adds metadata about text content such as word count, character count, etc.
    """
    
    def get_plugin_name(self) -> str:
        """Get plugin name"""
        return "TextAnalyzer"
    
    def get_plugin_version(self) -> str:
        """Get plugin version"""
        return "1.0.0"
    
    def get_supported_document_types(self) -> List[str]:
        """Get supported document types"""
        return ["docx", "doc", "xlsx", "xls"]  # Support all document types

    def fetch_one(self):
        """ 获取第一个文档对象 """
        if not self._result:
            return None
        return self._result[0]

    def fetch_all(self):
        """ 获取所有的文档对象 """
        if not self._result:
            return None
        return self._result

    @staticmethod
    def full_to_half(text):
        """ 将字符串中的每个全角字符转为半角字符 """
        half = ''
        for char in text:
            if 65281 <= ord(char) <= 65374:
                half += chr(ord(char) - 65248)
            else:
                half += char
        return half.replace("　", " ")

    def query(self, chapter_id: str = "", chapter_parent: str = "", block_name: str = "", range_: str = "single",
              content_type: str = "text"):
        """ 查询文档对象
            :params: chapter_id，字符串类型的章节号，根据章节号查找TextObject
            :params: chapter_parent，根据父章节号查询其下的所有TextObject
            :params: block_name，block对象的名称，word文档不用传参，excel需要传入对应的worksheet名称
            :params: range_，操作范围，如 single表示查询章节文本对象自身，all表示查询章节文本对象+其下的所有目标模型对象
            :params: content_type， range_操作范围的目标模型对象
            :return: TextAnalyzerPlugin 对象
        """
        temp = None
        if not self._block_name:
            self._block_name = block_name
        # 查询block对象，默认返回第一个DocumentBlockObject
        if not block_name:
            temp = self._result[0]
        else:
            for block_obj in self._result:
                if block_obj.name == block_name:
                    temp = block_obj
                    break
        if temp is None:
            return TextAnalyzerPlugin(document=self.document, block_name=self._block_name, path=self._path)

        # 根据章节号查询 文档对象
        if chapter_id:
            for obj in temp.elements:
                cid = getattr(obj.layout, "chapter_id", "")
                if not cid:
                    continue
                # 全角转半角
                cid = self.full_to_half(cid)
                chapter_id = self.full_to_half(chapter_id)
                if cid == chapter_id:
                    # range_表示 单个文本对象或者所有文本对象
                    if range_ == "single":
                        # 返回文本对象自身
                        return TextAnalyzerPlugin(result=[obj], document=self.document, block_name=self._block_name, path=self._path)
                    elif range_ == "all" and content_type == "text":
                        # 返回文本对象自身 + 其下的所有目标文本对象
                        children_TextAnalyzerPlugin = self.query(chapter_parent=chapter_id, content_type=content_type)
                        if children_TextAnalyzerPlugin.fetch_all() is None:
                            return TextAnalyzerPlugin(result=[obj], document=self.document, block_name=self._block_name,
                                       path=self._path)
                        return TextAnalyzerPlugin(result=[obj] + children_TextAnalyzerPlugin.fetch_all(), document=self.document,
                                   block_name=self._block_name,
                                   path=self._path)

        # 根据父章节号 查询其下的所有子内容文本对象
        if chapter_parent and content_type == "text":
            # 查询父章节文本对象自身
            parent_obj = self.query(chapter_id=chapter_parent).fetch_one()
            # 没有父章节对象
            if parent_obj is None:
                return TextAnalyzerPlugin(document=self.document, block_name=self._block_name, path=self._path)
            # 继续查询父章节下的所有子内容
            children_list = []
            flag = False
            for obj in temp.elements:
                if parent_obj == obj:
                    flag = True
                    continue
                elif flag and isinstance(obj, TextObject):  # 当前只找 子TextObject
                    if not getattr(obj.layout, "is_chapter_title", 0):
                        # obj不是章节号时，追加
                        children_list.append(obj)
                    elif getattr(obj.layout, "is_chapter_title", 0):
                        # obj 是章节号
                        if len(obj.layout.chapter_id) > len(chapter_parent):  # 说明是子章节标题
                            children_list.append(obj)
                        else:
                            flag = False
                            break
            return TextAnalyzerPlugin(result=children_list, document=self.document, block_name=self._block_name, path=self._path)

        # 其他参数情况，暂时返回一个空对象
        return TextAnalyzerPlugin(document=self.document, block_name=self._block_name, path=self._path)

    @staticmethod
    def is_demand(txt1: str, txt2: str):
        """ 判定两个文本内容是否满足匹配要求 """
        conditions = [
            txt1 == txt2,
            txt1.endswith(txt2),
            txt1.startswith(txt2)
        ]
        return any(conditions)

    def do_action(self, action: Literal["merge", "ignore", "replace"], origin: str = "", target: str = ""):
        """ 对_result中的目标文档做相应的操作
            :params: action, 操作的动作， 如：
            {
                "range": "all",
                "content_type": "text",
                "content": "",
                "action": "ignore",
            }
            表示忽略所有的空文本对象（TextObject）


            {
                "chapter": "1.1",
                "range": "all",
                "content_type": "text",
                "action": "merge"
            }
            表示对章节1.1及其下的所有子文本内容合并

            {
                "range": "all",
                "content_type": "text",
                "action": "replace",
                "origin":"*1",
                "target":"ABC"
            }
            表示对所有的文本内容，执行替换操作
        """

        logging.info(f"start to do action with current query result: {self._result}")

        if not self._result:
            return self

        if action == "merge" and all([True if isinstance(obj, TextObject) else False for obj in self._result]):
            # 对当前的查询结果(均为TextObject时)执行合并
            merged_obj = self._do_text_merge()
            # 更新文本对象数据 elements & texts
            self._update_text_data(merged_obj)

        if action == "ignore" and all([True if isinstance(obj, TextObject) else False for obj in self._result]):
            # 对当前的查询结果(均为TextObject时)执行删除线文字过滤
            ignored_result = self._do_text_ignore()
            # 更新文本对象数据 elements & texts
            for result in ignored_result:
                self._update_text_data(result[0], reference=result[1])

        if action == "replace" and all([True if isinstance(obj, TextObject) else False for obj in self._result]):
            # 对当前的查询结果(均为TextObject时)执行替换
            logging.info(f"start to do replace from '{origin}' to '{target}'")
            replaced_result = self._do_text_replace(origin, target)
            # 更新文本对象数据 elements & texts
            for result in replaced_result:
                self._update_text_data(result[0], reference=result[1])
        logging.info("complete do action with current query result.")
        return self

    def update(self, *args):
        """ 更新目标文档 """
        pass

    def _do_text_merge(self):
        """ 对result中的文本对象合并，返回一个整体的TextObject对象 """
        logging.info("start to do text merge with current query result.")
        if not self._result:
            return self
        merged_obj = TextObject()
        # 保留原始的文本对象
        merged_obj.origin_text_obj_list = self._result
        first_text_obj = self._result[0]
        # 页码、唯一标识等取自第一个TextObject
        merged_obj.data_id = first_text_obj.data_id
        merged_obj.index = first_text_obj.index if hasattr(first_text_obj, "index") else -1
        merged_obj.layout = first_text_obj.layout
        # 累加文本字符串
        merged_obj.text += first_text_obj.text + "\n"
        # 保留所有子内容的样式
        merged_obj.sub_style_list = []
        merged_obj.sub_style_list.append(first_text_obj.style)
        # 保留所有子内容的布局
        merged_obj.sub_layout_list = []
        merged_obj.sub_layout_list.append(first_text_obj.layout)
        # 保留所有的coordinate
        merged_obj.sub_coordinate_list = []
        merged_obj.sub_coordinate_list.append(first_text_obj.coordinate)
        # 保留所有的run
        merged_obj.runs.extend(first_text_obj.runs)

        # 合并其他的部分
        for i in range(1, len(self._result)):
            cur_obj = self._result[i]
            merged_obj.text += cur_obj.text + "\n"
            merged_obj.sub_style_list.append(cur_obj.style)
            merged_obj.runs.extend(cur_obj.runs)
            merged_obj.sub_coordinate_list.append(cur_obj.coordinate)
            merged_obj.sub_layout_list.append(cur_obj.layout)

        # 去除合并文本最后的\n
        merged_obj.text = merged_obj.text[:-1]
        logging.info("complete do text merge with current query result.")
        return merged_obj

    def _do_text_ignore(self):
        """ 对章节1.1中标记了删除线的文本忽略掉, 返回修改过的result """
        logging.info("start to do text ignore with current query result.")
        if not self._result:
            return self

        ignored_result = []
        for i in range(len(self._result)):
            cur_obj = copy.copy(self._result[i])

            text = ""

            for run in cur_obj.runs:
                run_text = run.text

                if run.style.font_style.strikeout:
                    run_text = ""
                text += run_text

            cur_obj.text = text
            # if cur_obj.text != self._result[i].text and \
            #     cur_obj.text[:-1] != self._result[i].text and \
            #      cur_obj.text[:-1].replace('*1','ABC') != self._result[i].text:
            if cur_obj.text != self._result[i].text:
                ignored_result.append([cur_obj, self._result[i]])
        logging.info("complete do text ingore with current query result.")
        return ignored_result

    def _do_text_replace(self, origin, target):
        """ 对章节1.1下文本内容中"origin"替换为"target"。, 返回修改过的result"""
        logging.info("start to do text ignore with current query result.")
        if not self._result:
            return self
        replaced_result = []
        for i in range(len(self._result)):
            cur_obj = copy.copy(self._result[i])
            text = cur_obj.text
            cur_obj.text = text.replace(origin, target)
            for run in cur_obj.runs:
                run.text = run.text.replace(origin, target)

            if cur_obj.text != self._result[i].text:
                replaced_result.append([cur_obj, self._result[i]])
        logging.info("complete do text replace with current query result.")
        return replaced_result

    def _update_text_data(self, handled_obj: TextObject, reference=None):
        """ 使用处理后的文本对象，替换到原始的文本对象 """
        logging.info("start to update text data after doing action.")
        # 合并后的文本对象，回放置在第一个TextObject出现的位置
        elements_place_index = -1
        texts_place_index = -1

        # 查找block对象
        block_obj = None
        for blk_obj in self.document:  # block对象列表
            if blk_obj.name == self._block_name:
                block_obj = blk_obj
                break
        if block_obj is None:
            return

        # 删除原始文本对象
        if reference is None:
            # 更新elements & texts 列表，查找插入的索引位置
            for idx, ele_obj in enumerate(block_obj.elements):
                if ele_obj is self._result[0]:
                    elements_place_index = idx
                    break
            for idx, txt_obj in enumerate(block_obj.texts):
                if txt_obj is self._result[0]:
                    texts_place_index = idx
                    break
            # 删除原始的文本对象
            for txt_obj in self._result:
                if txt_obj in block_obj.elements:
                    block_obj.elements.remove(txt_obj)
                if txt_obj in block_obj.texts:
                    block_obj.texts.remove(txt_obj)
        else:
            for idx, ele_obj in enumerate(block_obj.elements):
                if ele_obj is reference:
                    elements_place_index = idx
            for idx, txt_obj in enumerate(block_obj.texts):
                if txt_obj is reference:
                    texts_place_index = idx

            if reference in block_obj.elements:
                block_obj.elements.remove(reference)
            if reference in block_obj.texts:
                block_obj.texts.remove(reference)

        # 插入合并后的文本对象
        block_obj.elements.insert(elements_place_index, handled_obj)
        block_obj.texts.insert(texts_place_index, handled_obj)
        logging.info("complete update text data after doing action.")
    
    def _not_in(self, rule_db: dict, text_rules_list: list) -> bool:
        """
        判定该规则是否在列表中
        :param rule_db: 规则字典
        :param text_rules_list: 规则字典的列表
        :return: bool
        """
        for rule in text_rules_list:
            if rule == rule_db:
                return False
        return True

    def query_from_to(self, from_key: str, to_key: str, block_name: str = ""):
        """ 自定义的合并文本，存入result列表中，进行合并
        :params from_key: 字符串类型的起始文本，包含在合并范围内
        :params to_key: 字符串类型的结束文本，不包含在内
        :params block_name: 字符串类型，word对应空字符串，excel对应worksheet名称
        """
        # 存储文本段落的查询结果
        result = []
        # 存储block对象
        temp = None
        if not self._block_name:
            self._block_name = block_name
        # 查询block对象，默认返回第一个DocumentBlockObject
        if not block_name:
            temp = self.document[0] if getattr(self, "document", []) else None
        else:
            for block_obj in getattr(self, "document", []):
                if block_obj.name == block_name:
                    temp = block_obj
                    break
        if temp is None:
            return TextAnalyzerPlugin(document=self.document, block_name=self._block_name, path=self._path)

        # 合并标记
        flag = False
        # 遍历block对象下的所有元素
        for obj in temp.elements:
            if not isinstance(obj, TextObject):
                continue
            if not self.is_demand(getattr(obj, "text", ""), from_key):
                if self.is_demand(getattr(obj, "text", ""), to_key):
                    break
                if not flag:
                    continue
            else:
                flag = True
            # 开始存储目标文本段落
            if flag:
                result.append(obj)
        return TextAnalyzerPlugin(result=result, document=self.document, block_name=self._block_name, path=self._path)
    
    def process_document(self, document: DocumentObject):
        """
        Process document data.
        
        Args:
            document: Dictionary containing parsed document data
            
        Returns:
            Processed document data
        """
        logger.info("Processing document with TextAnalyzer plugin")

        # Process text objects
        for document_data in document.document:
            if document_data.texts:
                if document.file_name.startswith("MET-H_SYSIND-CSTD"):
                    sign_list = [
                        {"start": "【HVS_Val】", "end": "【N】"},
                        {"start": "2-2.表示キャンセル",
                         "end": "但し、ﾏﾙﾁﾃﾞｨｽﾌﾟﾚｲ表示かつ他の表示と併記しない場合は画面遷移から抹消すること。"},
                        {"start": "(2)移動平均化処理", "end": "*初回から【N】回分の平均値計算方法は、特に規定しない。"},
                    ]
                    texts = self.text_parse_merge_start_end_sign_handle(document_data.texts, sign_list)
                    document_data.texts = []
                    # 清理掉所有的text内容，重新赋值
                    document_data.elements = [item for item in document_data.elements if not hasattr(item, "_type") or item._type != "text"]
                    document_data.add_text(texts)
                elif document.file_name in ["MET-M_SP-CSTD-0-00-C-C1.doc", "MET-M_SP-CSTD-0-01-A-C2.doc"]:
                    # 定制处理ft-line14的合并
                    # 查询并合并
                    logging.info("start to query and merge in line14.")
                    self.query_from_to(from_key="MET-M_MCUCONST-*引き当て車両以外（19PFv2までの車両）",
                                       to_key="＊指示公差の検査対象範囲は各車種の「最大表示-20km/h or -10km/h(-20MPH or -10MPH)」までとする。").do_action(
                        "merge")
                    self.query_from_to(from_key="米国カナダ規格",
                                       to_key="MET-M_MCUCONST-*引き当て車両（19PFv3以降の車両）").do_action("merge")
                    self.query_from_to(from_key="MET-M_MCUCONST-*引き当て車両（19PFv3以降の車両）",
                                       to_key="適用仕向：下記定数にて適用仕向(地域名)を指示する。").do_action("merge")
                text_rules_list = []
                for text_obj in document_data.texts:
                    rule_db = translate_parse(text_obj, document.file_name, document_data.name,
                                              text_obj.layout.parent_content, '')

                    # 规则放入列表中，避免重复
                    if rule_db and self._not_in(rule_db, text_rules_list):
                        text_rules_list.append(rule_db)
                logging.info(f"text_rules_list: {text_rules_list}")
                for rule in text_rules_list:
                    filter_data = rule.get("filter_data", {})
                    # 加载出字典
                    filter_data = json.loads(filter_data)

                    # 假数据（参考示例）
                    if not filter_data:
                        filter_data = {
                            "range": ["all"],
                            "chapter_id": "1.1",
                            "content_type": "text",
                            "action": "replace",
                            "origin": "",
                            "target": ""
                        }

                    action = filter_data.get("action", None)
                    chapter_id = filter_data.get("chapter_id", None)
                    range_ = filter_data.get("range", None)
                    content_type = filter_data.get("content_type", None)
                    origin = filter_data.get("origin", None)
                    target = filter_data.get("target", None)

                    # 自然语言规则： 将11.6.1章节下的所有文本进行合并
                    if action == "merge" and chapter_id and content_type == "text" \
                            and (range_ and range_[0] == "all"):
                        # 查询指定章节号下的文本内容，并进行合并
                        logging.info(f"start to merge all text object inside chapter: {chapter_id}")
                        self.query(chapter_parent=chapter_id, range_=range_[0], content_type=content_type) \
                            .do_action(action)

                    # 自然语言规则： 章节1.1下标记了删除线的文本忽略掉
                    if action == "ignore" and chapter_id and content_type == "text" \
                            and (range_ and range_[0] == "all"):
                        logging.info(f"start to ignore all strikeout text inside chapter: {chapter_id}")
                        self.query(chapter_parent=chapter_id, range_=range_[0], content_type=content_type) \
                            .do_action(action)

                    # 自然语言规则： 章节1.1下的文本内容中"*1"替换为"ABC"。
                    if action == "replace" and chapter_id and content_type == "text" \
                            and (range_ and range_[0] == "all"):
                        logging.info(f"start to replace all text object inside chapter: {chapter_id}")
                        self.query(chapter_parent=chapter_id, range_=range_[0], content_type=content_type) \
                            .do_action(action, origin=origin, target=target)

                self.process_text_objects(document_data.texts, document.file_name, document_data.name)


        # # Add document-level statistics
        # document_data['metadata']['text_analysis'] = self._analyze_document_text(document_data)
        #
        # return document_data
    
    def process_text_objects(self, text_objects: List[TextObject], file_name: str, block_name: str):
        """
        Process text objects.
        
        Args:
            text_objects: List of text objects

            file_name: file_name of document

            block_name: sheet_name or ''
        Returns:
            Processed text objects
        """

        for text_obj in text_objects:
            rule_db = translate_parse(text_obj, file_name, block_name, text_obj.layout.parent_content, '')
            rule = rule_db.get("module_name", "")
            if rule == 'text_parse_merge_star':
                self.text_parse_merge_star_handle(text_obj, rule_db["rule_data"])

    def text_parse_merge_start_end_sign_handle(self, data: List[TextObject], rule_data=None) -> List[TextObject]:
        """
       通过特殊标记(起始标记和结束标记)合并数据
       :param data: 文本列表
       :param rule_data: rule
       :return: text_couples 处理完成的列表: [TextObject]
       """
        if not rule_data:
            return data
        curr_sign = None
        temp_list = []
        processed_resources = []

        # 遍历结果列表
        for text_obj in data:
            text_ = text_obj.text
            text_ = self.full_to_half(text_)
            for sign in rule_data:
                start = sign["start"]
                # 查找匹配的起始数据文本
                if text_.startswith(start):
                    curr_sign = sign
                    break
            if curr_sign:
                end = curr_sign["end"]
                temp_list.append(text_obj)
                if text_.startswith(end):
                    processed_resources.append(temp_list)
                    temp_list = []
                    curr_sign = None
            else:
                # 单个文本直接加入结果中
                processed_resources.append([text_obj])

        # 合并数据
        results = []
        results_set = set()
        for item in processed_resources:
            if item:
                result = item[0]
                runs = []
                text = ""
                for row in item:
                    text += str(row.text) + "\n"
                    runs.extend(row.runs)
                result.runs = runs
                result.text = text
                if len(item) > 1:
                    if text in results_set:
                        continue
                    else:
                        results_set.add(text)
                        results.append(result)
                else:
                    results.append(result)
        return results

    @staticmethod
    def full_to_half(text):
        half = ''
        for char in text:
            if 65281 <= ord(char) <= 65374:
                half += chr(ord(char) - 65248)
            else:
                half += char
        return half.replace("　", " ")

    def text_parse_merge_star_handle(self, data: TextObject, rule_data=None) -> TextObject:
        """
       定义文本章节号匹配的方法，中间的分隔符，如 、
       :param data: 文本列表
       :return: 处理完成的文本列表: [TextObject]
       """
        if not data:
            return data
        json_data = self.format_rule_data(rule_data)
        if not json_data:
            return data

        # excel中的文本对象 不需要匹配章节
        # if not hasattr(data.layout, "is_chapter_title"):
        #     return data

        # 遍历所有文本对象列表
        text_obj = data
        #if not text_obj.layout.is_chapter_title:
        # 再次匹配当前文本对象的章节号
        for sep in json_data:  # 符号列表
            pattern = r"^(?: |\u3000)*(\d+\.?\d*\.?\d*\.?(?:\%s))(?: |\u3000)*(?:\w+)" % sep
            match_result = re.findall(pattern, text_obj.text, re.I)
            if match_result:
                chapter_id = match_result[0].rstrip(sep)
                text_obj.layout.is_chapter_title = 1
                text_obj.layout.chapter_id = chapter_id
                text_obj.customized_sep = sep
                break
        logging.info("customized chapter match completed.")
        return data

    def format_rule_data(self, rule_data):
        """
        rule_data: json字符串：[","]
        return json [","]
        """
        data = json.loads(rule_data)
        return data
    
    def validate_document(self, document_data: Dict[str, Any]) -> bool:
        """
        Validate document data.
        
        Args:
            document_data: Dictionary containing parsed document data
            
        Returns:
            True if document data is valid, False otherwise
        """
        # Check if document has text objects
        if 'text_objects' not in document_data or not document_data['text_objects']:
            logger.warning("Document has no text objects")
            return False
        
        return True
    
    def _analyze_text(self, text: str) -> Dict[str, Any]:
        """
        Analyze text content.
        
        Args:
            text: Text content
            
        Returns:
            Dictionary containing text analysis results
        """
        # Count words
        words = re.findall(r'\b\w+\b', text)
        word_count = len(words)
        
        # Count characters
        char_count = len(text)
        char_count_no_spaces = len(text.replace(' ', ''))
        
        # Count sentences
        sentences = re.split(r'[.!?]+', text)
        sentence_count = len([s for s in sentences if s.strip()])
        
        # Calculate average word length
        avg_word_length = sum(len(word) for word in words) / word_count if word_count > 0 else 0
        
        # Calculate average sentence length
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
        
        # Find most common words
        word_freq = {}
        for word in words:
            word_lower = word.lower()
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
        
        most_common_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'word_count': word_count,
            'char_count': char_count,
            'char_count_no_spaces': char_count_no_spaces,
            'sentence_count': sentence_count,
            'avg_word_length': avg_word_length,
            'avg_sentence_length': avg_sentence_length,
            'most_common_words': most_common_words
        }
    
    def _analyze_document_text(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze all text in the document.
        
        Args:
            document_data: Dictionary containing parsed document data
            
        Returns:
            Dictionary containing document text analysis results
        """
        # Combine all text
        all_text = ""
        
        # Add text from text objects
        for text_obj in document_data.get('text_objects', []):
            all_text += text_obj.get('text', '') + " "
        
        # Add text from table cells
        for table_obj in document_data.get('table_objects', []):
            for cell in table_obj.get('cells', []):
                all_text += cell.get('text', '') + " "
        
        # Analyze combined text
        return self._analyze_text(all_text)
