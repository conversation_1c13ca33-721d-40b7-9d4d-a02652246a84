"""
Excel工具类测试文件

这个文件包含了ExcelUtil类的基本测试用例，演示了如何使用各种功能。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange, CommonStyles


def test_basic_operations():
    """测试基本操作"""
    print("=== 测试基本操作 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 创建Excel工具实例
        with ExcelUtil(temp_path, auto_create=True) as excel:
            # 测试写入单元格
            excel.write_cell("Sheet1", 1, 1, "姓名")
            excel.write_cell("Sheet1", 1, 2, "年龄")
            excel.write_cell("Sheet1", 1, 3, "城市")
            
            # 写入数据行
            data = [
                ["张三", 25, "北京"],
                ["李四", 30, "上海"],
                ["王五", 28, "广州"]
            ]
            
            for i, row_data in enumerate(data, start=2):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", i, j, value)
            
            # 测试读取单元格
            name = excel.read_cell("Sheet1", 2, 1)
            print(f"读取到的姓名: {name}")
            
            # 测试读取区域
            header_range = CellRange(1, 1, 1, 3)
            headers = excel.read_range("Sheet1", header_range)
            print(f"表头数据: {headers}")
            
            # 测试样式设置
            header_style = CommonStyles.HEADER
            for col in range(1, 4):
                excel.set_cell_style("Sheet1", 1, col, header_style)
            
            # 设置数据样式
            data_style = CommonStyles.DATA
            for row in range(2, 5):
                for col in range(1, 4):
                    excel.set_cell_style("Sheet1", row, col, data_style)
            
            # 自动调整列宽
            excel.auto_fit_columns("Sheet1")
            
            # 保存文件
            excel.save()
            
        print("基本操作测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_dataframe_operations():
    """测试DataFrame操作"""
    print("\n=== 测试DataFrame操作 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        import pandas as pd
        
        # 创建测试数据
        df = pd.DataFrame({
            '产品名称': ['产品A', '产品B', '产品C'],
            '价格': [100, 200, 150],
            '库存': [50, 30, 80]
        })
        
        with ExcelUtil(temp_path, auto_create=True) as excel:
            # 写入DataFrame
            excel.write_dataframe("Sheet1", df, start_row=1, include_header=True)
            
            # 读取为DataFrame
            read_df = excel.read_range_as_dataframe("Sheet1", CellRange(1, 1, 4, 3))
            print("读取到的DataFrame:")
            print(read_df)
            
            # 设置表头样式
            excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
            
            # 保存文件
            excel.save()
            
        print("DataFrame操作测试完成")
        
    except ImportError:
        print("pandas未安装，跳过DataFrame测试")
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_sheet_management():
    """测试工作表管理"""
    print("\n=== 测试工作表管理 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        with ExcelUtil(temp_path, auto_create=True) as excel:
            # 获取工作表列表
            sheets = excel.get_sheet_names()
            print(f"初始工作表: {sheets}")
            
            # 创建新工作表
            success = excel.create_sheet("数据表")
            print(f"创建工作表成功: {success}")
            
            # 再次获取工作表列表
            sheets = excel.get_sheet_names()
            print(f"创建后工作表: {sheets}")
            
            # 在新工作表中写入数据
            excel.write_cell("数据表", 1, 1, "这是新工作表")
            
            # 测试模糊匹配
            matched_name = excel.get_sheet_name_fuzzy("数据")
            print(f"模糊匹配结果: {matched_name}")
            
            # 保存文件
            excel.save()
            
        print("工作表管理测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_custom_styles():
    """测试自定义样式"""
    print("\n=== 测试自定义样式 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        with ExcelUtil(temp_path, auto_create=True) as excel:
            # 创建自定义样式
            title_style = CellStyle(
                font_name="Arial",
                font_size=16,
                font_bold=True,
                font_color="FFFFFF",
                bg_color="4472C4",
                alignment_horizontal="center",
                border_style="thick"
            )
            
            # 应用标题样式
            excel.write_cell("Sheet1", 1, 1, "销售报表")
            excel.set_cell_style("Sheet1", 1, 1, title_style)
            
            # 合并单元格作为标题
            excel.merge_cells("Sheet1", "A1:D1")
            
            # 写入表头
            headers = ["月份", "销售额", "成本", "利润"]
            for i, header in enumerate(headers, start=1):
                excel.write_cell("Sheet1", 2, i, header)
                excel.set_cell_style("Sheet1", 2, i, CommonStyles.HEADER)
            
            # 写入数据
            data = [
                ["1月", 10000, 6000, 4000],
                ["2月", 12000, 7000, 5000],
                ["3月", 15000, 8000, 7000]
            ]
            
            for row_idx, row_data in enumerate(data, start=3):
                for col_idx, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", row_idx, col_idx, value)
                    if col_idx == 1:  # 月份列
                        excel.set_cell_style("Sheet1", row_idx, col_idx, CommonStyles.DATA)
                    else:  # 数字列
                        excel.set_cell_style("Sheet1", row_idx, col_idx, CommonStyles.NUMBER)
            
            # 自动调整列宽
            excel.auto_fit_columns("Sheet1")
            
            # 保存文件
            excel.save()
            
        print("自定义样式测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_range_operations():
    """测试区域操作"""
    print("\n=== 测试区域操作 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        with ExcelUtil(temp_path, auto_create=True) as excel:
            # 批量写入数据
            data = [
                ["A", "B", "C", "D"],
                [1, 2, 3, 4],
                [5, 6, 7, 8],
                [9, 10, 11, 12]
            ]
            
            excel.write_range("Sheet1", 1, 1, data)
            
            # 读取整个区域
            read_data = excel.read_range("Sheet1", "A1:D4")
            print("读取的区域数据:")
            for row in read_data:
                print(row)
            
            # 设置区域样式
            excel.set_range_style("Sheet1", "A1:D1", CommonStyles.HEADER)
            excel.set_range_style("Sheet1", "A2:D4", CommonStyles.DATA)
            
            # 保存文件
            excel.save()
            
        print("区域操作测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


if __name__ == "__main__":
    print("开始Excel工具类测试...")
    
    try:
        test_basic_operations()
        test_dataframe_operations()
        test_sheet_management()
        test_custom_styles()
        test_range_operations()
        
        print("\n所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
