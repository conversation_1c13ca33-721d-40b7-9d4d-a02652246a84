# 警告代码生成和检查服务

## 概述

警告代码生成和检查服务用于自动化警告代码的生成和验证，确保代码质量和规范性。

## V字对应

- **V字阶段**: V3.1 详细设计
- **V字项目**: 警告代码生成和检查

## 主要功能

1. **警告代码检查** - 检查警告代码的正确性和规范性
2. **Excel数据验证** - 验证Excel文件中的数据类型和格式
3. **代码分析** - 分析C代码中的宏定义和函数结构
4. **生成检查报告** - 生成详细的检查结果报告

## 文件结构

```
warning_code_gen_check_service/
├── models.py                          # 数据模型定义
├── workflow_warning_code_check.py     # 主工作流逻辑
├── workflow_config.py                 # 工作流配置
├── util/
│   ├── warning_code_check_util.py     # 警告代码检查工具
│   ├── excel_validation_util.py       # Excel验证工具
│   └── code_analysis_util.py          # 代码分析工具
└── README.md                          # 说明文档
```

## 使用方法

### 基本使用

```python
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_check import WarningCodeCheckWorkflow
from sdw_agent.service.warning_code_gen_check.models import WarningCodeCheckConfig, WarningChangeInfo

# 创建工作流实例
workflow = WarningCodeCheckWorkflow()

# 准备输入数据
warning_change_info = WarningChangeInfo(
    code_folder="/path/to/code",
    after_code_tools_url="/path/to/excel.xlsx",
    # ... 其他参数
)

config = WarningCodeCheckConfig(
    code_folder="/path/to/code",
    warning_change_info=warning_change_info,
    check_file_path="/path/to/output/check_result.txt"
)

# 执行工作流
result = workflow.execute(config)

if result.success:
    print(f"检查完成，结果文件: {result.check_file_path}")
else:
    print(f"检查失败: {result.message}")
```

### 生成和检查一体化

```python
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_check import WarningCodeGenAndCheckWorkflow
from sdw_agent.service.warning_code_gen_check.models import WarningCodeGenAndCheckConfig

# 创建工作流实例
workflow = WarningCodeGenAndCheckWorkflow()

# 准备配置
config = WarningCodeGenAndCheckConfig(
    after_sample_book_url="/path/to/sample.xlsx",
    code_folder="/path/to/code",
    check_file_path="/path/to/output/check_result.txt"
    # ... 其他参数
)

# 执行工作流
result = workflow.execute(config)
```

## 检查项目

### Excel数据检查
- 源工作表数据类型检查
- 接口弹窗数据类型检查
- Toyota工作表数据类型检查
- 工作表排序检查
- 显示消息长度检查

### 代码检查
- 宏定义值检查
- 函数代码结构检查
- 警告数量限制检查
- MSG2代码检查

### 属性检查
- 警告属性配置检查
- 双通道配置检查
- ADAS配置检查
- 全屏中断配置检查

## 配置说明

工作流配置在 `workflow_config.py` 中定义，包括：

- 工作表名称配置
- 检查规则配置
- 代码文件路径配置
- 宏定义配置
- 函数配置

## 兼容性

为了保持向后兼容性，提供了以下兼容性接口：

- `warning_code_check_generate_service()` - 警告代码检查生成服务
- `check_all_warning_code_service()` - 检查所有警告代码服务
- `gen_and_check_all_warning_code_service()` - 生成和检查所有警告代码服务

## 错误处理

工作流包含完整的错误处理机制：

1. **输入验证** - 验证配置参数的有效性
2. **异常捕获** - 捕获并记录执行过程中的异常
3. **错误报告** - 生成详细的错误信息和堆栈跟踪
4. **资源清理** - 确保Excel应用程序等资源得到正确释放

## 日志记录

使用 loguru 进行日志记录，支持：

- 不同级别的日志输出
- 结构化日志信息
- 错误堆栈跟踪
- 执行过程追踪

## 扩展性

工作流设计支持：

- 新增检查项目
- 自定义检查规则
- 插件式工具类
- 配置化参数管理