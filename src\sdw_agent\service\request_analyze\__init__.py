"""
要件分析工作流模块

V字对应：
1.3 要件分析
2. 要求仕様読み合わせ（客先、部内、チーム内）

提供基于guideline的变更需求分析和优化功能。

主要功能：
1. 基于guideline对变更需求进行分类打分
2. 基于guideline分类对应的内容结合变更需求的描述进行润色
3. 将优化后的内容写入要件一览表
4. 生成设计评价方针文件
"""

from .request_analyze import RequestAnalyzeWorkflow
from .models import (
    TaskInfo,
    SourceInfo,
    WriteEpicRequest,
    ClassifyScoreResult,
    ClassifyOutputData,
    OptimizeOutputData,
    PolishedContent,
    EpicWriteData,
    DesignPolicyData,
    RequestAnalyzeConfigModel
)
from .utils import EpicWriter, DesignPolicyWriter, RequirementAnalyzer

__all__ = [
    # 工作流类
    "RequestAnalyzeWorkflow",

    # 数据模型
    "TaskInfo",
    "SourceInfo",
    "WriteEpicRequest",
    "ClassifyScoreResult",
    "ClassifyOutputData",
    "OptimizeOutputData",
    "PolishedContent",
    "EpicWriteData",
    "DesignPolicyData",
    "RequestAnalyzeConfigModel",

    # 工具类
    "EpicWriter",
    "DesignPolicyWriter",
    "RequirementAnalyzer",
]