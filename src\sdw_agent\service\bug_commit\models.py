"""
Bug提交工作流数据模型

定义工作流使用的数据模型和验证模式
"""

from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel, Field, validator


class BugCommitConfigModel(BaseModel):
    """Bug提交配置模型"""
    
    # 基本配置
    name: str = Field(default="Bug提交")
    description: str = Field(default="分析测试用例中的NG结果，并生成标准化的Bug提交内容")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")
    
    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)
    
    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)
    
    # LLM配置
    llm: Dict[str, Any] = Field(default_factory=dict)
    
    # 日志配置
    logging: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        extra = "allow"


class BugSeverity(str, Enum):
    """Bug严重度枚举"""
    S = "S"  # 非常严重，可能导致用户有生命危险
    A = "A"  # 仪表花屏、黑屏、死机、卡滞、不能休眠（24h内解决）
    B = "B"  # 功能不动作
    C = "C"  # 显示问题
    D = "D"  # 建议修改类问题


class BugInfluence(str, Enum):
    """Bug影响度枚举"""
    A0 = "A0"  # 重大问题（reset、黑画）
    A1 = "A1"  # 对车完有重要影响的S级问题
    A2 = "A2"  # 对车完有影响的A级问题
    B0 = "B0"  # 功能完全不动作
    B1 = "B1"  # 功能部分动作
    C = "C"    # 显示问题，功能本身已实装


class BugStatus(str, Enum):
    """Bug状态枚举"""
    NEW = "新建"
    IN_PROGRESS = "进行中"
    RESOLVED = "已解决"
    CLOSED = "已关闭"
    FEEDBACK = "需要反馈"


class BugCaseInfo(BaseModel):
    """Bug案例信息"""
    case_id: str
    module: str
    test_case: str
    actual_result: str
    expected_result: str
    remarks: Optional[str] = None
    date: Optional[str] = None
    book_orca: Optional[str] = None
    severity: Optional[BugSeverity] = None
    influence: Optional[BugInfluence] = None
    
    @validator('severity', pre=True, always=True)
    def validate_severity(cls, v):
        """验证严重度"""
        if v is None:
            return None
        try:
            return BugSeverity(v)
        except ValueError:
            return None
    
    @validator('influence', pre=True, always=True)
    def validate_influence(cls, v):
        """验证影响度"""
        if v is None:
            return None
        try:
            return BugInfluence(v)
        except ValueError:
            return None


class BugSubmitInfo(BaseModel):
    """Bug提交信息"""
    bug_id: Optional[str] = None
    theme: str
    priority: str
    status: BugStatus = BugStatus.NEW
    assignee: str = ""
    created_at: datetime = Field(default_factory=datetime.now)
    start_date: Optional[str] = None
    due_date: Optional[str] = None
    estimated_hours: str = "4.0h"
    tracking: str = "Bug"
    category: str = "QA"
    target_version: str = ""
    component: str
    related_screen: str
    bug_influence: str
    creator: str = "DEV AGENT"
    completion_percentage: str = "0%"
    remarks: str = ""
    
    @validator('bug_id')
    def validate_bug_id(cls, v):
        """验证Bug ID"""
        if v is None:
            return ""
        return v


class BugAnalysisResult(BaseModel):
    """Bug分析结果"""
    case_id: str
    theme: str
    severity: BugSeverity
    influence: BugInfluence
    component: str
    procedure: str
    occur_rate: str
    related_screen: str
    

class BugCommitResult(BaseModel):
    """Bug提交结果"""
    bug_cases: List[Any]
    # analysis_results: List[BugAnalysisResult]
    # submit_infos: List[BugSubmitInfo]
    test_case_overwrite: str
    output_file: str 