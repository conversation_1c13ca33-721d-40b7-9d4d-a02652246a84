# -*- coding: utf-8 -*-
"""
@File    : llm.py
<AUTHOR> zhenp
@Date    : 2025-06-13 10:53
@Desc    : Description of the file
"""

import requests

def call_azure_model(url, payload, headers=None, httpx=None):
    """Send a POST request to Azure's OpenAI service."""
    try:
        response = requests.post(url, json=payload, headers=headers)
        # 检查 HTTP 状态码
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        print(f"Request failed: {e}")
        return None
    except httpx.HTTPStatusError as e:
        print(f"HTTP error occurred: {e}")
        return None

def query_model(content: str, test_prompt: str):
    """
    Call Azure OpenAI model to get user's intent.

    Args:
        content (str): The input message for intent prediction.

    Returns:
        str: The user's intent extracted by the model.
    """
    try:
        print(f"🌐 Analyzing users file")

        # Configuration for Azure model
        config = {
            "title": "gpt-4o",
            "model": "gpt-4o",
            "provider": "azure",
            "apiBase": "https://sdw-dev.openai.azure.com",
            "apiVersion": "2025-01-01-preview",
            "deployment": "gpt-4o",
            "contextLength": 32764,
            "apiKey": "dIAz3SnaOXxdmE02SyfW36TDVmCtlOxdO7IKDM2n1RpZZaFlGsmEJQQJ99BCACHYHv6XJ3w3AAABACOGEbzZ",
            "apiType": "azure",
            "systemMessage": test_prompt,
            "completionOptions": {
                "maxTokens": 16384
            }
        }

        # Construct the URL for Azure API
        url = f"{config['apiBase']}/openai/deployments/{config['deployment']}/chat/completions?api-version={config['apiVersion']}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {config['apiKey']}"
        }

        # Payload for the request
        payload = {
            "messages": [
                {"role": "system", "content": config["systemMessage"]},
                {"role": "user", "content": content}
            ],
            "max_tokens": config["completionOptions"]["maxTokens"]
        }

        # Call the Azure model
        response_data = call_azure_model(url, payload, headers)

        # Extract the generated response from the model
        if response_data and 'choices' in response_data:
            message_content = response_data['choices'][0]['message']['content']
            return message_content
        else:
            print(f"Incomplete response from Azure model: {response_data}")
            return None

    except Exception as e:
        print(f"Error occurred while querying the model: {e}")
        return None


def query_llama(user_message, prompt):
    max_tokens = 10240
    api_url = "http://192.168.52.165:11500/v1/chat/completions"
    model_name = "llama3.3:70B"
    headers = {
        'Content-Type': 'application/json',
    }

    payload = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_message},
        ],
        "max_token": max_tokens
    }

    try:
        # 异步创建 HTTP 会话并发送请求
        response_data = call_azure_model(api_url, payload, headers)

        # Extract the generated response from the model
        if response_data and 'choices' in response_data:
            message_content = response_data['choices'][0]['message']['content']
            return message_content
        else:
            print(f"Incomplete response from Azure model: {response_data}")
            return None

    except Exception as e:
        return {"error": f"连接 API 过程中发生错误: {str(e)}"}

# async def connect_V3(input_text,prompt, model="DeepSeek-V3-W8A8", max_tokens=1024, temperature=0.5, presence_penalty=0, stream=False):
#     """
#     Generates a response from the LLM based on the input provided.
#
#     Parameters:
#         input_text (str): The text to send to the LLM.
#         model (str): The model to use. Default is "DeepSeek-V3-W8A8".
#         max_tokens (int): Maximum tokens for the response. Default is 1024.
#         temperature (float): Sampling temperature. Default is 0.5.
#         presence_penalty (float): Penalizes new tokens based on presence in input. Default is 0.
#         stream (bool): Whether to stream responses. Default is False.
#
#     Returns:
#         str: Response content from the LLM.
#     """
#     url = "http://192.168.146.16/sdw/chatbot/sysai/v1/chat/completions"
#     prompttext= prompt+input_text
#     payload = {
#         "messages": [
#             {
#                 "role": "user",
#                 "content": prompttext
#             }
#         ],
#         "stream": stream,
#         "model": model,
#         "max_tokens": max_tokens,
#         "temperature": temperature,
#         "presence_penalty": presence_penalty
#     }
#
#     headers = {"Content-Type": "application/json"}
#
#     try:
#         response = requests.post(url, json=payload, headers=headers, verify=False)
#         response.raise_for_status()  # Raises an HTTPError for non-200 status codes
#         result = response.json()
#         content = result.get('choices', [{}])[0].get('message', {}).get('content', "")
#         return content
#     except requests.exceptions.RequestException as e:
#         return f"An error occurred: {e}"