# 要件分析工作流

## 概述

要件分析工作流（RequestAnalyzeWorkflow）是SDW Agent中负责处理变更需求分析和优化的核心工作流。

### V字对应
- **1.3 要件分析**
- **2. 要求仕様読み合わせ（客先、部内、チーム内）**

## 主要功能

1. **分类打分**：基于guideline对变更需求进行分类打分
2. **内容优化**：基于guideline分类对应的内容结合变更需求的描述进行润色
3. **要件一览表写入**：将优化后的内容写入要件一览表
4. **设计评价方针生成**：生成设计评价方针文件

## 工作流架构

```
RequestAnalyzeWorkflow
├── execute_classify()    # 分类打分
└── execute_optimize()    # 内容优化
```

## 使用方法

### 1. 分类打分

```python
from sdw_agent.service.request_analyze import RequestAnalyzeWorkflow, TaskInfo

# 创建工作流实例
workflow = RequestAnalyzeWorkflow()

# 准备输入数据
task_info = TaskInfo(
    row_idx=1,
    ar_no="AR-12345",
    ar_title="变更标题",
    ar_link="http://example.com",
    epic_name="Epic名称",
    req_change_content="需求变更内容描述",
    p_no="PROCMGT-001",
    guideline_key="CSTM"
)

# 执行分类打分
result = workflow.execute_classify(task_info)
```

### 2. 内容优化

```python
from sdw_agent.service.request_analyze import WriteEpicRequest, SourceInfo

# 准备输入数据
request = WriteEpicRequest(
    keySource=SourceInfo(type="local", uri="/path/to/epic.xlsx"),
    change_summary="变更摘要",
    classify_name="分类名称",
    guideline_key="CSTM"
)

# 执行内容优化
result = workflow.execute_optimize(request)
```

## 配置说明

工作流配置文件位于 `config.yaml`，包含以下主要配置项：

- `default_guideline_key`: 默认Guideline键
- `min_score_threshold`: 最低分数阈值
- `max_category_results`: 最大分类结果数
- `timeout`: 超时时间

## 数据模型

### 输入模型

- **TaskInfo**: 任务信息请求模型
- **WriteEpicRequest**: 回写要件一览表请求模型
- **SourceInfo**: 源信息模型

### 输出模型

- **ClassifyOutputData**: 分类输出数据模型
- **OptimizeOutputData**: 优化输出数据模型
- **ClassifyScoreResult**: 分类打分结果

## 工具类

- **EpicWriter**: 要件一览表写入工具
- **DesignPolicyWriter**: 设计评价方针写入工具
- **RequirementAnalyzer**: 需求分析工具

## 配置管理

工作流使用 `config.yaml` 文件进行配置管理，通过 BaseWorkflow 的 `self.config` 访问配置项。主要配置包括：

- **excel_constants**: Excel操作相关配置
- **workflow_constants**: 工作流业务逻辑配置
- **validation_constants**: 验证相关配置
- **message_constants**: 消息配置

## 错误处理

工作流包含完善的错误处理机制：

1. **输入验证**：使用Pydantic模型进行输入验证
2. **异常捕获**：捕获并记录所有异常
3. **错误返回**：返回标准的WorkflowResult错误结果

## 日志记录

使用loguru进行日志记录，支持以下日志级别：

- `INFO`: 正常执行信息
- `ERROR`: 错误信息
- `DEBUG`: 调试信息（需要在配置中启用）

## 性能优化

- 支持缓存机制（可配置）
- 支持并发任务限制
- 支持超时控制

## 注意事项

1. 确保Excel文件路径正确且文件可访问
2. 确保Guideline配置正确
3. 注意文件权限和磁盘空间
4. 建议在生产环境中启用缓存以提高性能
