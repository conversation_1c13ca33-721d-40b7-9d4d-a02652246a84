#!/usr/bin/env python
"""
Setup script for DocParser.
"""

import os
import subprocess
from datetime import datetime

from setuptools import setup, find_packages

def get_version():
    from datetime import datetime
    # 获取当前日期和分钟：YYYYMMDDHHMM
    date_minute = datetime.now().strftime("%Y%m%d%H%M")
    # 生成符合规则的版本号
    version = f"1.0.{date_minute}"
    return version

# Read the contents of README.md
with open('README.md', encoding='utf-8') as f:
    long_description = f.read()

# Read the requirements
with open('requirements.txt', encoding='utf-8') as f:
    requirements = f.read().splitlines()

setup(
    name='docparser',
    version=get_version(),
    description='Document parsing tool for Word and Excel documents',
    long_description=long_description,
    long_description_content_type='text/markdown',
    author='Your Name',
    author_email='<EMAIL>',
    url='https://github.com/yourusername/docparser',
    packages=find_packages(),
    install_requires=requirements,
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Topic :: Office/Business',
        'Topic :: Software Development :: Libraries',
        'Topic :: Text Processing',
    ],
    python_requires='>=3.7',
    entry_points={
        'console_scripts': [
            'docparser=docparser.main:main',
        ],
    },
)
