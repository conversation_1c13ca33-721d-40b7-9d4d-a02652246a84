# Draw.io XML 文件格式详细说明文档

## 📋 目录

1. [基本结构](#基本结构)
2. [核心标签说明](#核心标签说明)
3. [位置和几何属性](#位置和几何属性)
4. [样式属性](#样式属性)
5. [文本和标签](#文本和标签)
6. [连接线和关系](#连接线和关系)
7. [实际示例分析](#实际示例分析)
8. [常用操作指南](#常用操作指南)

---

## 基本结构

Draw.io XML文件采用标准的XML格式，主要包含以下层级结构：

```xml
<mxfile>
  <diagram>
    <mxGraphModel>
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <!-- 图形元素 -->
        <mxCell id="..." .../>
        <mxCell id="..." .../>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
```

### 🏗️ 结构说明

| 标签               | 作用                     | 必需性 |
| ------------------ | ------------------------ | ------ |
| `<mxfile>`       | 根容器，包含整个文件     | 必需   |
| `<diagram>`      | 图表容器，可包含多个图表 | 必需   |
| `<mxGraphModel>` | 图形模型容器             | 必需   |
| `<root>`         | 所有图形元素的根节点     | 必需   |

---

## 核心标签说明

### 🎯 `<mxCell>` 标签

`<mxCell>` 是Draw.io中最重要的标签，代表图表中的每个元素。

#### 基本属性

| 属性       | 说明            | 示例值                              | 必需性       |
| ---------- | --------------- | ----------------------------------- | ------------ |
| `id`     | 唯一标识符      | `"1-0bCCSwIkjvpjtFy3q8-69"`       | 必需         |
| `value`  | 显示的文本内容  | `"Dsp_app"`                       | 可选         |
| `style`  | 样式定义        | `"rounded=0;whiteSpace=wrap;..."` | 可选         |
| `parent` | 父元素ID        | `"1"`                             | 必需         |
| `vertex` | 是否为顶点      | `"1"`                             | 图形元素必需 |
| `edge`   | 是否为边/连接线 | `"1"`                             | 连接线必需   |
| `source` | 连接线起点ID    | `"source-id"`                     | 连接线必需   |
| `target` | 连接线终点ID    | `"target-id"`                     | 连接线必需   |

#### 特殊元素

```xml
<!-- 根元素 -->
<mxCell id="0"/>

<!-- 默认父元素 -->
<mxCell id="1" parent="0"/>

<!-- 图形元素 -->
<mxCell id="shape-1" value="文本" style="..." vertex="1" parent="1">
  <mxGeometry x="100" y="50" width="120" height="60" as="geometry"/>
</mxCell>

<!-- 连接线 -->
<mxCell id="edge-1" style="..." edge="1" parent="1" source="shape-1" target="shape-2">
  <mxGeometry relative="1" as="geometry"/>
</mxCell>
```

---

## 位置和几何属性

### 📐 `<mxGeometry>` 标签

控制图形的位置、大小和几何属性。

#### 基本几何属性

| 属性       | 说明        | 单位   | 示例              |
| ---------- | ----------- | ------ | ----------------- |
| `x`      | 左上角X坐标 | 像素   | `x="100"`       |
| `y`      | 左上角Y坐标 | 像素   | `y="50"`        |
| `width`  | 宽度        | 像素   | `width="120"`   |
| `height` | 高度        | 像素   | `height="60"`   |
| `as`     | 几何类型    | 固定值 | `as="geometry"` |

#### 相对位置和特殊几何

```xml
<!-- 绝对位置 -->
<mxGeometry x="100" y="50" width="120" height="60" as="geometry"/>

<!-- 相对位置（用于连接线） -->
<mxGeometry relative="1" as="geometry">
  <mxPoint x="0" y="0" as="sourcePoint"/>
  <mxPoint x="100" y="0" as="targetPoint"/>
</mxGeometry>

<!-- 带偏移的几何 -->
<mxGeometry x="0.5" y="0" width="0" height="0" relative="1" as="geometry">
  <mxPoint x="10" y="20" as="offset"/>
</mxGeometry>
```

### 🎯 坐标系统

Draw.io使用标准的计算机图形坐标系：

```
(0,0) ────────► X轴
  │
  │
  │
  ▼
 Y轴
```

- **原点(0,0)**：位于画布左上角
- **X轴**：向右为正方向
- **Y轴**：向下为正方向

### 📏 相对位置计算

#### 图形中心点计算

```javascript
centerX = x + width / 2
centerY = y + height / 2
```

#### 图形边界计算

```javascript
left = x
top = y
right = x + width
bottom = y + height
```

---

## 样式属性

### 🎨 样式字符串格式

样式以分号分隔的键值对形式定义：

```
style="key1=value1;key2=value2;key3=value3;"
```

### 📋 常用样式属性

#### 基础图形样式

| 属性            | 说明     | 可选值                   | 示例                    |
| --------------- | -------- | ------------------------ | ----------------------- |
| `rounded`     | 圆角     | `0`(直角), `1`(圆角) | `rounded=1`           |
| `whiteSpace`  | 文本换行 | `wrap`, `nowrap`     | `whiteSpace=wrap`     |
| `html`        | HTML渲染 | `0`, `1`             | `html=1`              |
| `fillColor`   | 填充颜色 | 十六进制颜色             | `fillColor=#dae8fc`   |
| `strokeColor` | 边框颜色 | 十六进制颜色             | `strokeColor=#6c8ebf` |
| `strokeWidth` | 边框宽度 | 数值                     | `strokeWidth=2`       |

#### 文本对齐样式

| 属性                      | 说明         | 可选值                          | 示例                          |
| ------------------------- | ------------ | ------------------------------- | ----------------------------- |
| `align`                 | 水平对齐     | `left`, `center`, `right` | `align=center`              |
| `verticalAlign`         | 垂直对齐     | `top`, `middle`, `bottom` | `verticalAlign=top`         |
| `labelPosition`         | 标签位置     | `left`, `right`, `center` | `labelPosition=center`      |
| `verticalLabelPosition` | 标签垂直位置 | `top`, `bottom`, `middle` | `verticalLabelPosition=top` |

#### 字体样式

| 属性           | 说明     | 可选值                                               | 示例                  |
| -------------- | -------- | ---------------------------------------------------- | --------------------- |
| `fontFamily` | 字体族   | 字体名称                                             | `fontFamily=Arial`  |
| `fontSize`   | 字体大小 | 数值                                                 | `fontSize=12`       |
| `fontStyle`  | 字体样式 | `0`(普通), `1`(粗体), `2`(斜体), `3`(粗斜体) | `fontStyle=1`       |
| `fontColor`  | 字体颜色 | 十六进制颜色                                         | `fontColor=#000000` |

#### 连接线样式

| 属性           | 说明       | 可选值                                               | 示例                              |
| -------------- | ---------- | ---------------------------------------------------- | --------------------------------- |
| `edgeStyle`  | 连接线样式 | `orthogonalEdgeStyle`, `entityRelationEdgeStyle` | `edgeStyle=orthogonalEdgeStyle` |
| `curved`     | 是否弯曲   | `0`, `1`                                         | `curved=1`                      |
| `endArrow`   | 终点箭头   | `classic`, `block`, `none`                     | `endArrow=classic`              |
| `startArrow` | 起点箭头   | `classic`, `block`, `none`                     | `startArrow=none`               |

---

## 文本和标签

### 📝 文本内容格式

#### 纯文本

```xml
<mxCell id="1" value="简单文本" .../>
```

#### HTML格式文本

```xml
<mxCell id="1" value="<b>粗体文本</b>" style="html=1;" .../>
```

#### 复杂HTML文本

```xml
<mxCell id="1" value="<p style="margin-top: 0pt ; margin-bottom: 0pt">
<span style="font-size: 12pt ; font-weight: bold">标题</span>
</p>" style="html=1;" .../>
```

### 🔤 HTML实体编码

Draw.io中的HTML需要进行实体编码：

| 字符  | 编码       | 说明   |
| ----- | ---------- | ------ |
| `<` | `&lt;`   | 小于号 |
| `>` | `&gt;`   | 大于号 |
| `"` | `&quot;` | 双引号 |
| `&` | `&amp;`  | 和号   |
| `'` | `&#39;`  | 单引号 |

---

## 连接线和关系

### 🔗 连接线基本结构

```xml
<mxCell id="edge-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" 
       edge="1" parent="1" source="source-id" target="target-id">
  <mxGeometry relative="1" as="geometry"/>
</mxCell>
```

### 📍 连接点定义

#### 自动连接点

```xml
<mxGeometry relative="1" as="geometry"/>
```

#### 手动连接点

```xml
<mxGeometry relative="1" as="geometry">
  <mxPoint x="100" y="50" as="sourcePoint"/>
  <mxPoint x="200" y="150" as="targetPoint"/>
</mxGeometry>
```

#### 带控制点的连接线

```xml
<mxGeometry relative="1" as="geometry">
  <Array as="points">
    <mxPoint x="150" y="100"/>
    <mxPoint x="250" y="100"/>
  </Array>
</mxGeometry>
```

### 🎯 连接线样式类型

| 样式                        | 说明       | 外观     |
| --------------------------- | ---------- | -------- |
| `orthogonalEdgeStyle`     | 直角连接线 | └─┐   |
| `entityRelationEdgeStyle` | 实体关系线 | ──── |
| `segmentEdgeStyle`        | 分段连接线 | ╱╲╱╲ |
| `elbowEdgeStyle`          | 肘形连接线 | └──┐ |

---

## 实际示例分析

### 🔍 分析你提供的代码

```xml
<mxCell id="1-0bCCSwIkjvpjtFy3q8-69" 
       value="<p style="margin-top: 0pt ; margin-bottom: 0pt ; margin-left: 0in ; text-indent: 0in ; text-align: left"><span style="font-size: 12pt ; font-family: &#34;tahoma bold&#34; ; font-weight: bold">Dsp_app</span></p>" 
       style="rounded=0;whiteSpace=wrap;html=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;" 
       parent="1" vertex="1">
```

#### 解析结果：

| 属性       | 值                          | 说明                   |
| ---------- | --------------------------- | ---------------------- |
| `id`     | `1-0bCCSwIkjvpjtFy3q8-69` | 唯一标识符             |
| `value`  | HTML格式的"Dsp_app"文本     | 粗体、12pt、Tahoma字体 |
| `parent` | `1`                       | 父元素为默认画布       |
| `vertex` | `1`                       | 这是一个图形元素       |

#### 样式解析：

| 样式属性                  | 值         | 效果             |
| ------------------------- | ---------- | ---------------- |
| `rounded`               | `0`      | 直角矩形         |
| `whiteSpace`            | `wrap`   | 文本可换行       |
| `html`                  | `1`      | 启用HTML渲染     |
| `labelPosition`         | `center` | 标签水平居中     |
| `verticalLabelPosition` | `top`    | 标签在顶部       |
| `align`                 | `center` | 内容水平居中     |
| `verticalAlign`         | `bottom` | 内容垂直底部对齐 |

### 📊 完整示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net">
  <diagram name="示例图表">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
      
        <!-- 矩形图形 -->
        <mxCell id="rect-1" value="矩形" 
               style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" 
               vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="120" height="60" as="geometry"/>
        </mxCell>
      
        <!-- 圆形图形 -->
        <mxCell id="circle-1" value="圆形" 
               style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" 
               vertex="1" parent="1">
          <mxGeometry x="300" y="100" width="80" height="80" as="geometry"/>
        </mxCell>
      
        <!-- 连接线 -->
        <mxCell id="edge-1" 
               style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;" 
               edge="1" parent="1" source="rect-1" target="circle-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
      
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
```

---

## 常用操作指南

### 🛠️ 创建基本图形

#### 1. 矩形

```xml
<mxCell id="rect-id" value="文本" 
       style="rounded=0;whiteSpace=wrap;html=1;" 
       vertex="1" parent="1">
  <mxGeometry x="100" y="100" width="120" height="60" as="geometry"/>
</mxCell>
```

#### 2. 圆形

```xml
<mxCell id="circle-id" value="文本" 
       style="ellipse;whiteSpace=wrap;html=1;" 
       vertex="1" parent="1">
  <mxGeometry x="100" y="100" width="80" height="80" as="geometry"/>
</mxCell>
```

#### 3. 菱形

```xml
<mxCell id="diamond-id" value="文本" 
       style="rhombus;whiteSpace=wrap;html=1;" 
       vertex="1" parent="1">
  <mxGeometry x="100" y="100" width="80" height="80" as="geometry"/>
</mxCell>
```

### 🔗 创建连接线

#### 1. 简单连接线

```xml
<mxCell id="edge-id" 
       style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;" 
       edge="1" parent="1" source="source-id" target="target-id">
  <mxGeometry relative="1" as="geometry"/>
</mxCell>
```

#### 2. 带箭头的连接线

```xml
<mxCell id="edge-id" 
       style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=classic;startArrow=none;" 
       edge="1" parent="1" source="source-id" target="target-id">
  <mxGeometry relative="1" as="geometry"/>
</mxCell>
```

### 🎨 常用颜色方案

| 用途     | 填充色      | 边框色      | 代码                                      |
| -------- | ----------- | ----------- | ----------------------------------------- |
| 蓝色主题 | `#dae8fc` | `#6c8ebf` | `fillColor=#dae8fc;strokeColor=#6c8ebf` |
| 绿色主题 | `#d5e8d4` | `#82b366` | `fillColor=#d5e8d4;strokeColor=#82b366` |
| 橙色主题 | `#ffe6cc` | `#d79b00` | `fillColor=#ffe6cc;strokeColor=#d79b00` |
| 红色主题 | `#f8cecc` | `#b85450` | `fillColor=#f8cecc;strokeColor=#b85450` |
| 紫色主题 | `#e1d5e7` | `#9673a6` | `fillColor=#e1d5e7;strokeColor=#9673a6` |

### 📐 位置计算工具

#### JavaScript辅助函数

```javascript
// 计算图形中心点
function getCenter(geometry) {
  return {
    x: geometry.x + geometry.width / 2,
    y: geometry.y + geometry.height / 2
  };
}

// 检查两个图形是否重叠
function isOverlapping(rect1, rect2) {
  return !(rect1.x + rect1.width < rect2.x || 
           rect2.x + rect2.width < rect1.x || 
           rect1.y + rect1.height < rect2.y || 
           rect2.y + rect2.height < rect1.y);
}

// 计算两点间距离
function getDistance(point1, point2) {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
}
```

---

## 📚 总结

Draw.io XML文件格式的核心要点：

1. **结构清晰**：采用标准XML层级结构
2. **元素统一**：所有图形都是 `<mxCell>`元素
3. **样式灵活**：通过style属性控制外观
4. **位置精确**：使用像素级坐标定位
5. **关系明确**：通过source/target定义连接关系

掌握这些基础知识后，你就可以：

- 📖 读懂任何Draw.io XML文件
- ✏️ 手动编辑图形属性
- 🔧 编程生成Draw.io文件
- 🔄 在不同格式间转换图表

这个文档应该能帮助你深入理解Draw.io的XML格式！
