"""
IF整合性确认 Workflow

V字对应：
2.5.26 I/F整合性確認

该模块提供接口整合性确认功能，检查代码中接口使用与IF设计书的一致性。
1. 从I/F设计书中提取IF一览中函数名、函数参数类型、值域范围等信息
2. 从git仓库下载代码，根据函数名从代码中查找所有的调用的源码片段
3. 根据调用源码片段内容，结合整个.c文件内容与I/F设计书该函数的约束条件由大模型分析调用参数是否合理，输出判定结果
4. 将判定结果整合保存在模板文件中，输出I/F整合性确认报告书
"""
from sdw_agent.service.if_integration_service.models import IfIntegrationRequest
from sdw_agent.service.if_integration_service.workflow_if_integration import IFIntegrationWorkflow, do_if_integration

__all__ = ['IFIntegrationWorkflow', 'do_if_integration', 'IfIntegrationRequest']
