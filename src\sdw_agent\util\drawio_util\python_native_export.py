#!/usr/bin/env python3
"""
使用纯 Python 库将 Draw.io 文件导出为图像
不依赖桌面软件的解决方案
"""

import os
import base64
import gzip
import xml.etree.ElementTree as ET
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any
import tempfile
import time

from loguru import logger


def install_required_packages():
    """安装所需的第三方包"""
    packages = [
        "playwright",
        "cairosvg", 
        "pillow",
        "requests"
    ]
    
    import subprocess
    import sys
    
    for package in packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} 已安装")
        except ImportError:
            logger.info(f"📦 正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])


def parse_drawio_xml(drawio_file: str) -> Optional[str]:
    """
    解析 Draw.io 文件，提取 XML 内容
    
    Args:
        drawio_file: Draw.io 文件路径
        
    Returns:
        解析后的 XML 字符串，失败返回 None
    """
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Draw.io 文件可能是压缩的或未压缩的 XML
        if content.startswith('<?xml'):
            # 未压缩的 XML
            return content
        else:
            # 可能是压缩的，尝试解压
            try:
                # 尝试 base64 解码 + gzip 解压
                decoded = base64.b64decode(content)
                decompressed = gzip.decompress(decoded).decode('utf-8')
                return decompressed
            except:
                # 尝试直接 URL 解码
                try:
                    decoded = urllib.parse.unquote(content)
                    return decoded
                except:
                    logger.error("无法解析 Draw.io 文件格式")
                    return None
                    
    except Exception as e:
        logger.error(f"解析 Draw.io 文件失败: {e}")
        return None


def export_via_playwright(drawio_file: str, output_png: str, timeout: int = 30) -> bool:
    """
    使用 Playwright 无头浏览器导出 Draw.io 图像
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出 PNG 文件路径
        timeout: 超时时间（秒）
        
    Returns:
        是否成功导出
    """
    try:
        from playwright.sync_api import sync_playwright
        
        # 1. 读取 Draw.io 文件内容
        xml_content = parse_drawio_xml(drawio_file)
        if not xml_content:
            return False
        
        # 2. URL 编码
        encoded_content = urllib.parse.quote(xml_content)
        
        # 3. 构建 draw.io URL
        drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=diagram#R{encoded_content}"
        
        logger.info("正在启动无头浏览器...")
        
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 设置视口大小
            page.set_viewport_size({"width": 1920, "height": 1080})
            
            logger.info("正在加载 Draw.io...")
            
            # 访问页面
            page.goto(drawio_url, timeout=timeout * 1000)
            
            # 等待页面加载完成
            page.wait_for_timeout(5000)  # 等待 5 秒
            
            # 等待图表渲染完成
            try:
                # 等待 SVG 元素出现
                page.wait_for_selector("svg", timeout=10000)
                logger.info("图表已加载")
            except:
                logger.warning("未检测到 SVG 元素，继续尝试截图")
            
            # 截图
            logger.info(f"正在截图到: {output_png}")
            page.screenshot(path=output_png, full_page=True, type="png")
            
            browser.close()
            
        if Path(output_png).exists():
            logger.success(f"Playwright 导出成功: {output_png}")
            return True
        else:
            logger.error("截图文件未生成")
            return False
            
    except ImportError:
        logger.error("Playwright 未安装，请运行: pip install playwright && playwright install chromium")
        return False
    except Exception as e:
        logger.error(f"Playwright 导出失败: {e}")
        return False


def export_via_mxgraph_svg(drawio_file: str, output_png: str) -> bool:
    """
    通过解析 mxGraph XML 并转换为 SVG，再转换为 PNG
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出 PNG 文件路径
        
    Returns:
        是否成功导出
    """
    try:
        # 1. 解析 XML
        xml_content = parse_drawio_xml(drawio_file)
        if not xml_content:
            return False
        
        root = ET.fromstring(xml_content)
        
        # 2. 提取图表信息
        diagram = root.find('.//diagram')
        if diagram is None:
            logger.error("未找到 diagram 元素")
            return False
        
        # 3. 获取 mxGraphModel
        mxgraph_model = diagram.find('.//mxGraphModel')
        if mxgraph_model is None:
            logger.error("未找到 mxGraphModel 元素")
            return False
        
        # 4. 这里需要实现 mxGraph 到 SVG 的转换
        # 这是一个复杂的过程，需要解析所有的 mxCell 元素
        # 并根据它们的几何信息和样式生成 SVG
        
        logger.warning("mxGraph 到 SVG 的转换需要复杂的实现，建议使用其他方法")
        return False
        
    except Exception as e:
        logger.error(f"mxGraph SVG 导出失败: {e}")
        return False


def export_via_requests_api(drawio_file: str, output_png: str) -> bool:
    """
    尝试使用 draw.io 的 API 接口导出
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出 PNG 文件路径
        
    Returns:
        是否成功导出
    """
    try:
        import requests
        
        # 1. 读取文件内容
        xml_content = parse_drawio_xml(drawio_file)
        if not xml_content:
            return False
        
        # 2. 尝试使用 draw.io 的导出 API
        export_url = "https://app.diagrams.net/export"
        
        params = {
            'format': 'png',
            'scale': '2',
            'border': '10',
            'bg': 'white'
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        data = f"xml={urllib.parse.quote(xml_content)}"
        
        logger.info("正在通过 API 导出...")
        
        response = requests.post(
            export_url,
            params=params,
            data=data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            with open(output_png, 'wb') as f:
                f.write(response.content)
            
            logger.success(f"API 导出成功: {output_png}")
            return True
        else:
            logger.error(f"API 导出失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"API 导出失败: {e}")
        return False


def export_drawio_python_native(drawio_file: str, 
                               output_png: str,
                               method: str = "auto") -> Dict[str, Any]:
    """
    使用纯 Python 方法导出 Draw.io 文件
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出 PNG 文件路径
        method: 导出方法 ("auto", "playwright", "api", "mxgraph")
        
    Returns:
        导出结果
    """
    if not Path(drawio_file).exists():
        return {
            "success": False,
            "message": f"Draw.io 文件不存在: {drawio_file}",
            "method": method
        }
    
    # 确保输出目录存在
    Path(output_png).parent.mkdir(parents=True, exist_ok=True)
    
    methods_to_try = []
    
    if method == "auto":
        methods_to_try = ["api", "playwright", "mxgraph"]
    else:
        methods_to_try = [method]
    
    for current_method in methods_to_try:
        logger.info(f"尝试使用方法: {current_method}")
        
        success = False
        
        if current_method == "playwright":
            success = export_via_playwright(drawio_file, output_png)
        elif current_method == "api":
            success = export_via_requests_api(drawio_file, output_png)
        elif current_method == "mxgraph":
            success = export_via_mxgraph_svg(drawio_file, output_png)
        
        if success:
            return {
                "success": True,
                "message": f"使用 {current_method} 方法导出成功",
                "method": current_method,
                "output_file": output_png
            }
        else:
            logger.warning(f"方法 {current_method} 失败，尝试下一个方法")
    
    return {
        "success": False,
        "message": "所有导出方法都失败了",
        "method": method,
        "tried_methods": methods_to_try
    }


def demo_python_native_export():
    """演示纯 Python 导出功能"""
    print("🎯 纯 Python Draw.io 导出演示")
    print("=" * 50)
    
    from sdw_agent.service.template_manager import template_manager
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用 Draw.io 文件: {drawio_file}")
    
    # 2. 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 3. 测试不同方法
    methods = ["api", "playwright"]
    
    for method in methods:
        print(f"\n🔄 测试方法: {method}")
        print("-" * 30)
        
        output_png = output_dir / f"python_native_{method}.png"
        
        result = export_drawio_python_native(
            drawio_file=drawio_file,
            output_png=str(output_png),
            method=method
        )
        
        print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   消息: {result['message']}")
        
        if result['success']:
            print(f"   输出文件: {result['output_file']}")
            
            # 如果成功，插入到 Excel
            try:
                from sdw_agent.util.drawio_util.manual_image_insert import insert_manual_image_to_excel
                
                excel_file = output_dir / f"python_native_{method}.xlsx"
                excel_result = insert_manual_image_to_excel(
                    image_file=str(output_png),
                    excel_file=str(excel_file),
                    title=f"Draw.io 图表 ({method.upper()} 方法)"
                )
                
                if excel_result['success']:
                    print(f"   Excel文件: {excel_result['excel_file']}")
                
            except Exception as e:
                print(f"   Excel插入失败: {e}")
    
    print(f"\n🎉 测试完成！请查看 output 目录中的文件。")


if __name__ == "__main__":
    # 首先尝试安装必要的包
    try:
        install_required_packages()
    except Exception as e:
        print(f"⚠️  安装依赖包时出现问题: {e}")
        print("请手动安装: pip install playwright cairosvg pillow requests")
        print("然后运行: playwright install chromium")
    
    demo_python_native_export()
