"""
分类规则管理API

提供分类规则的CRUD操作接口，支持规则的创建、查询、更新和删除。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field, field_validator
from loguru import logger

from sdw_agent.service.guideline_manager import GuidelineManager

# 创建路由器
router = APIRouter(prefix="/api/sdw/guideline", tags=["分类规则管理"])


# 请求模型定义
class CreateRuleRequest(BaseModel):
    """创建规则请求模型"""
    rule_key: str = Field(..., description="规则唯一标识key", min_length=1, max_length=100)
    rule_name: str = Field(..., description="规则名称", min_length=1, max_length=200)
    rule_description: str = Field(..., description="规则描述", max_length=1000)
    role_name: str = Field(..., description="角色名称", min_length=1, max_length=100)
    excel_file_path: str = Field(..., description="规则文件Excel路径")
    auto_enhance: bool = Field(True, description="是否自动使用AI增强描述")

    @field_validator('rule_key')
    @classmethod
    def validate_rule_key(cls, v):
        """验证规则key格式"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError("规则key只能包含字母、数字、下划线和连字符")
        return v


class UpdateRuleRequest(BaseModel):
    """更新规则请求模型"""
    rule_name: Optional[str] = Field(None, description="规则名称", min_length=1, max_length=200)
    rule_description: Optional[str] = Field(None, description="规则描述", max_length=1000)
    role_name: Optional[str] = Field(None, description="角色名称", min_length=1, max_length=100)
    excel_file_path: Optional[str] = Field(None, description="规则文件Excel路径")


class RuleQueryRequest(BaseModel):
    """规则查询请求模型"""
    rule_key: Optional[str] = Field(None, description="规则key")
    rule_name: Optional[str] = Field(None, description="规则名称（支持模糊查询）")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(10, description="每页大小", ge=1, le=100)


# 响应模型定义
class RuleInfo(BaseModel):
    """规则信息模型"""
    rule_key: str = Field(..., description="规则唯一标识key")
    rule_name: str = Field(..., description="规则名称")
    rule_description: str = Field(..., description="规则描述")
    role_name: str = Field(..., description="角色名称")
    excel_file_path: str = Field(..., description="规则文件Excel路径")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class CreateRuleResponse(BaseModel):
    """创建规则响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")


class GetRuleResponse(BaseModel):
    """获取规则响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: RuleInfo = Field(..., description="规则信息")


class ListRulesResponse(BaseModel):
    """规则列表响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(..., description="规则列表数据")


class UpdateRuleResponse(BaseModel):
    """更新规则响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")


class DeleteRuleResponse(BaseModel):
    """删除规则响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")


class EnhanceRuleRequest(BaseModel):
    """AI增强规则请求模型"""
    auto_enhance: bool = Field(True, description="是否自动写入Excel文件")


class EnhanceRuleResponse(BaseModel):
    """AI增强规则响应模型"""
    code: int = Field(0, description="响应码")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(..., description="增强结果数据")


# CRUD接口定义
@router.post("/rules",
             summary="创建分类规则",
             description="创建新的分类规则，需要提供规则key、名称、描述和Excel文件路径",
             response_model=CreateRuleResponse)
async def create_rule(request: CreateRuleRequest):
    """
    创建分类规则

    Args:
        request: 创建规则请求参数

    Returns:
        创建结果
    """
    try:
        logger.info(f"创建规则请求: {request.rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 调用业务逻辑创建规则
        result = manager.create_rule(
            rule_key=request.rule_key,
            rule_name=request.rule_name,
            rule_description=request.rule_description,
            role_name=request.role_name,
            excel_file_path=request.excel_file_path,
            auto_enhance=request.auto_enhance
        )

        return {
            "code": 0,
            "msg": "规则创建成功",
            "data": result
        }

    except ValueError as e:
        logger.error(f"创建规则参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Excel文件不存在: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"创建规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")


@router.get("/rules/{rule_key}",
            summary="获取单个分类规则",
            description="根据规则key获取指定的分类规则信息",
            response_model=GetRuleResponse)
async def get_rule(rule_key: str):
    """
    获取单个分类规则

    Args:
        rule_key: 规则唯一标识

    Returns:
        规则详细信息
    """
    try:
        logger.info(f"获取规则请求: {rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 获取规则信息
        rule_metadata = manager.get_rule(rule_key)

        if rule_metadata is None:
            raise HTTPException(status_code=404, detail=f"规则不存在: {rule_key}")

        # 转换为响应格式
        rule_info = RuleInfo(
            rule_key=rule_metadata.rule_key,
            rule_name=rule_metadata.rule_name,
            rule_description=rule_metadata.rule_description,
            role_name=rule_metadata.role_name,
            excel_file_path=rule_metadata.excel_file_path,
            created_at=rule_metadata.created_at,
            updated_at=rule_metadata.updated_at
        )

        return {
            "code": 0,
            "msg": "获取规则成功",
            "data": rule_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取规则失败: {str(e)}")


@router.get("/rules",
            summary="获取分类规则列表",
            description="获取分类规则列表，支持分页和条件查询",
            response_model=ListRulesResponse)
async def list_rules(
    rule_key: Optional[str] = Query(None, description="规则key"),
    rule_name: Optional[str] = Query(None, description="规则名称（支持模糊查询）"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(10, description="每页大小", ge=1, le=100)
):
    """
    获取分类规则列表

    Args:
        rule_key: 规则key（可选）
        rule_name: 规则名称，支持模糊查询（可选）
        page: 页码
        page_size: 每页大小

    Returns:
        规则列表和分页信息
    """
    try:
        logger.info(f"获取规则列表请求: page={page}, page_size={page_size}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 获取规则列表
        result = manager.list_rules(
            rule_key=rule_key,
            rule_name=rule_name,
            page=page,
            page_size=page_size
        )

        return {
            "code": 0,
            "msg": "获取规则列表成功",
            "data": result
        }

    except Exception as e:
        logger.error(f"获取规则列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")


@router.put("/rules/{rule_key}",
            summary="更新分类规则",
            description="更新指定的分类规则信息",
            response_model=UpdateRuleResponse)
async def update_rule(rule_key: str, request: UpdateRuleRequest):
    """
    更新分类规则

    Args:
        rule_key: 规则唯一标识
        request: 更新规则请求参数

    Returns:
        更新结果
    """
    try:
        logger.info(f"更新规则请求: {rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 调用业务逻辑更新规则
        result = manager.update_rule(
            rule_key=rule_key,
            rule_name=request.rule_name,
            rule_description=request.rule_description,
            role_name=request.role_name,
            excel_file_path=request.excel_file_path
        )

        return {
            "code": 0,
            "msg": "规则更新成功",
            "data": result
        }

    except ValueError as e:
        logger.error(f"更新规则参数错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Excel文件不存在: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"更新规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")


@router.delete("/rules/{rule_key}",
               summary="删除分类规则",
               description="删除指定的分类规则",
               response_model=DeleteRuleResponse)
async def delete_rule(rule_key: str):
    """
    删除分类规则

    Args:
        rule_key: 规则唯一标识

    Returns:
        删除结果
    """
    try:
        logger.info(f"删除规则请求: {rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 调用业务逻辑删除规则
        result = manager.delete_rule(rule_key)

        return {
            "code": 0,
            "msg": "规则删除成功",
            "data": result
        }

    except ValueError as e:
        logger.error(f"删除规则参数错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除规则失败: {str(e)}")


@router.post("/rules/{rule_key}/enhance",
             summary="AI增强分类规则描述",
             description="使用AI自动生成和补全规则文件中空白的分类描述",
             response_model=EnhanceRuleResponse)
async def enhance_rule_descriptions(rule_key: str, request: EnhanceRuleRequest):
    """
    AI增强分类规则描述

    Args:
        rule_key: 规则唯一标识
        request: 增强请求参数

    Returns:
        增强结果
    """
    try:
        logger.info(f"AI增强规则描述请求: {rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 调用业务逻辑进行AI增强
        result = manager.enhance_rule_descriptions(
            rule_key=rule_key,
            auto_enhance=request.auto_enhance
        )

        return {
            "code": 0,
            "msg": "AI增强完成",
            "data": result
        }

    except ValueError as e:
        logger.error(f"AI增强参数错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Excel文件不存在: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"AI增强失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI增强失败: {str(e)}")


@router.get("/rules/{rule_key}/enhance/preview",
            summary="预览AI增强结果",
            description="预览AI增强的分类描述，不会写入Excel文件",
            response_model=EnhanceRuleResponse)
async def preview_rule_enhancements(rule_key: str):
    """
    预览AI增强结果

    Args:
        rule_key: 规则唯一标识

    Returns:
        预览结果
    """
    try:
        logger.info(f"预览AI增强结果请求: {rule_key}")

        # 创建规则管理器实例
        manager = GuidelineManager()

        # 调用业务逻辑预览增强结果
        result = manager.preview_ai_enhancements(rule_key)

        return {
            "code": 0,
            "msg": "预览生成成功",
            "data": result
        }

    except ValueError as e:
        logger.error(f"预览参数错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Excel文件不存在: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")