from langchain_core.messages import AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from tiktoken import get_encoding

from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.dscs_codecheck.models import ConfirmFormatDSCSChange



def truncate_text(text, max_tokens=10000):
    """截断文本到指定token数"""
    encoding = get_encoding("cl100k_base")
    if(text==None):
        return text
    tokens = encoding.encode(text)
    if len(tokens) > max_tokens:
        truncated_tokens = tokens[:max_tokens]
        return encoding.decode(truncated_tokens)
    return text 

def requests_llm_change(file_content_after,file_content,prompts):

    # 截断过长的代码内容
    file_content_after = truncate_text(file_content_after, max_tokens=50000)
    file_content = truncate_text(file_content, max_tokens=50000)
    
    # 创建一个聊天提示模板，用于生成变更摘要
    template = ChatPromptTemplate(
        [
            ("user", prompts)
        ],
        template_format="mustache",
    )
    
    if(file_content_after==''):
        file_content_after = '新增文件，无变更代码片段'
    invoke_data = {
        "code_after": file_content_after,
        "code_full": file_content
    }

    result: ConfirmFormatDSCSChange = get_ai_message_with_structured_output(
        template,
        invoke_data,
        ConfirmFormatDSCSChange,
        llm_model=None
    )
    return {
        "status": result.status,
        "reason": result.reason
    }