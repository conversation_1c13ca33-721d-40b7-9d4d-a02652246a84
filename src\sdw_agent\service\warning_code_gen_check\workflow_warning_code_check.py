"""
Warning Code Check Workflow
警告代码检查工作流
"""
import os
import traceback
from typing import Optional
from loguru import logger

from sdw_agent.service import BaseWorkflow
from sdw_agent.service.warning_code_gen_check.models import (
    WarningCodeCheckConfig, WarningCodeCheckResult, WarningCodeGenAndCheckConfig,
    WarningCodeGenAndCheckResult, WarningChangeInfo
)
from sdw_agent.service.warning_code_gen_check.util.warning_code_check_util import WarningCodeCheckUtils
from sdw_agent.service.warning_code_gen_check.util.excel_validation_util import ExcelValidationUtils
from sdw_agent.service.warning_code_gen_check.util.code_analysis_util import CodeAnalysisUtils
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_gen import gen_all_warning_code_service


class WarningCodeCheckWorkflow(BaseWorkflow):
    """警告代码检查工作流"""

    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.check_utils = WarningCodeCheckUtils()
        self.excel_utils = ExcelValidationUtils()
        self.code_utils = CodeAnalysisUtils()

    def execute(self, config: WarningCodeCheckConfig) -> WarningCodeCheckResult:
        """
        执行警告代码检查工作流
        
        Args:
            config: 警告代码检查配置
            
        Returns:
            警告代码检查结果
        """
        try:
            self.logger.info("开始执行警告代码检查工作流")

            # 验证输入
            self._validate_config(config)

            # 执行检查
            check_results = self._perform_comprehensive_check(config)

            # 生成检查报告
            check_file_path = self._generate_check_report(check_results, config.check_file_path)

            return WarningCodeCheckResult(
                success=True,
                message="警告代码检查完成",
                check_file_path=check_file_path,
                check_results=check_results
            )

        except Exception as e:
            self.logger.error(f"警告代码检查工作流执行失败: {str(e)}")
            return WarningCodeCheckResult(
                success=False,
                message=f"执行失败: {str(e)}",
                error_details=traceback.format_exc()
            )

    def _validate_config(self, config: WarningCodeCheckConfig) -> None:
        """验证配置参数"""
        if not config.code_folder:
            raise ValueError("代码仓库路径不能为空")
        if not config.warning_change_info:
            raise ValueError("警告变更信息不能为空")
        if not os.path.exists(config.code_folder):
            raise FileNotFoundError(f"代码仓库路径不存在: {config.code_folder}")

    def _perform_comprehensive_check(self, config: WarningCodeCheckConfig) -> dict:
        """执行综合检查"""
        check_results = {}
        warning_info = config.warning_change_info

        try:
            # Excel数据类型检查
            self.logger.info("开始Excel数据类型检查")
            check_results["source_sheet_data_type_err"] = self.excel_utils.check_source_sheet_data_type(
                warning_info.after_code_tools_url
            )

            # 排序检查
            self.logger.info("开始排序检查")
            check_results["sort_err"] = self.excel_utils.check_sorting(
                warning_info.after_code_tools_url, "CONTDISP (源)", "CONTDISP（MET）", ["No."]
            )

            # 接口弹窗数据类型检查
            self.logger.info("开始接口弹窗数据类型检查")
            check_results[
                "interafce_popup_sheet_data_type_err"] = self.excel_utils.check_interafce_popup_sheet_data_type(
                warning_info.after_code_tools_url
            )

            # Toyota工作表数据类型检查
            self.logger.info("开始Toyota工作表数据类型检查")
            check_results["toyota_sheet_data_type_err"] = self.excel_utils.check_toyota_sheet_data_type(
                warning_info.after_code_tools_url
            )

            # 警告属性检查
            self.logger.info("开始警告属性检查")
            check_results["warning_property_err"] = (False, "不用检查")

            # 代码生成结果检查
            self.logger.info("开始代码生成结果检查")
            check_results["gen_warning_code_result_err"] = self.check_utils.check_gen_warning_code_result(
                warning_info
            )

            # 警告数量检查
            self.logger.info("开始警告数量检查")
            check_results["warning_num_code_exceeds_err"] = self.code_utils.is_warning_num_code_exceeds(
                config.code_folder, warning_info
            )

            # 函数代码检查
            self.logger.info("开始函数代码检查")
            check_results["wrndtcfggetreq_code_err"] = self.code_utils.is_wrndtcfggetreq_code_error(config.code_folder)

            # 显示消息长度检查
            self.logger.info("开始显示消息长度检查")
            check_results["dspmnscrl_msg_max_length_err"] = self.excel_utils.check_dspmnscrl_msg_max_length(
                warning_info.after_code_tools_url
            )

            self.logger.info("开始u4_dwtt_exceed_max_size_err检查")
            u4_dwtt_exceed_max_size_err = CodeAnalysisUtils.is_u4_dwtt_exceed_max_size(config.code_folder, warning_info)
            check_results["u4_dwtt_exceed_max_size_err"] = u4_dwtt_exceed_max_size_err
            logger.info(f"u4_dwtt_exceed_max_size_err_check 完成 {u4_dwtt_exceed_max_size_err}")

            # MSG2代码检查
            self.logger.info("开始MSG2代码检查")
            check_results["msg2_code_size_err"] = self.code_utils.check_msg2_code(
                warning_info.after_code_tools_url, config.code_folder
            )

            # 工作表代码检查
            self.logger.info("开始工作表代码检查")
            check_results["code_sheet_err"] = self.excel_utils.sheet_code_check(
                warning_info.after_code_tools_url,
                warning_info.pre_code_tools_url,
                warning_info
            )

            self.logger.info(f"所有检查完成: {check_results}")
            return check_results

        except Exception as e:
            self.logger.error(f"执行综合检查失败: {str(e)}")
            raise

    def _generate_check_report(self, check_results: dict, output_path: str) -> str:
        """生成检查报告"""
        try:
            return self.check_utils.write_warning_check_to_file(check_results, output_path)
        except Exception as e:
            self.logger.error(f"生成检查报告失败: {str(e)}")
            raise


class WarningCodeGenAndCheckWorkflow(BaseWorkflow):
    """警告代码生成和检查工作流"""

    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.check_workflow = WarningCodeCheckWorkflow(config_path)

    def execute(self, config: WarningCodeGenAndCheckConfig) -> WarningCodeGenAndCheckResult:
        """
        执行警告代码生成和检查工作流
        
        Args:
            config: 警告代码生成和检查配置
            
        Returns:
            警告代码生成和检查结果
        """
        try:
            self.logger.info("开始执行警告代码生成和检查工作流")

            # 验证输入
            self._validate_config(config)

            # 创建警告变更信息
            warning_change_info = self._create_warning_change_info(config)

            # # 生成警告列表变更信息
            # gen_warning_list_change_info(warning_change_info)
            #
            # 生成警告代码
            warning_change_info = gen_all_warning_code_service(warning_change_info)
            self.logger.info("警告代码生成完成")

            # 执行检查
            check_config = WarningCodeCheckConfig(
                code_folder=config.code_folder,
                warning_change_info=warning_change_info,
                check_file_path=config.check_file_path
            )

            check_result = self.check_workflow.execute(check_config)

            if not check_result.success:
                raise Exception(f"检查失败: {check_result.message}")

            return WarningCodeGenAndCheckResult(
                success=True,
                message="警告代码生成和检查完成",
                check_file_path=check_result.check_file_path,
                after_code_tools_url=warning_change_info.after_code_tools_url,
                pre_code_tools_url=warning_change_info.pre_code_tools_url,
                check_results=check_result.check_results
            )

        except Exception as e:
            self.logger.error(f"警告代码生成和检查工作流执行失败: {str(e)}")
            return WarningCodeGenAndCheckResult(
                success=False,
                message=f"执行失败: {str(e)}",
                error_details=traceback.format_exc()
            )

    def _validate_config(self, config: WarningCodeGenAndCheckConfig) -> None:
        """验证配置参数"""
        if not config.after_sample_book_url:
            raise ValueError("变更后告警式样书路径不能为空")
        if not config.code_folder:
            raise ValueError("代码仓库路径不能为空")
        if not os.path.exists(config.after_sample_book_url):
            raise FileNotFoundError(f"变更后告警式样书文件不存在: {config.after_sample_book_url}")
        if not os.path.exists(config.code_folder):
            raise FileNotFoundError(f"代码仓库路径不存在: {config.code_folder}")

    def _create_warning_change_info(self, config: WarningCodeGenAndCheckConfig) -> WarningChangeInfo:
        """创建警告变更信息对象"""
        return WarningChangeInfo(
            pre_sample_book_url=config.pre_sample_book_url,
            after_sample_book_url=config.after_sample_book_url,
            code_gen_tools_url=config.code_gen_tools_url,
            relate_book_folder=config.relate_book_folder,
            adas_book_path=config.adas_book_path,
            code_folder=config.code_folder,
            check_file_path=config.check_file_path
        )


# 兼容性接口
def warning_code_check_generate_service(code_folder: str, warning_change_info: WarningChangeInfo,
                                        check_file_path: str) -> str:
    """
    警告代码检查生成服务（兼容性接口）
    
    Args:
        code_folder: 代码仓库路径
        warning_change_info: 警告变更信息
        check_file_path: 检查文件路径
        
    Returns:
        检查结果文件路径
    """
    workflow = WarningCodeCheckWorkflow()

    config = WarningCodeCheckConfig(
        code_folder=code_folder,
        warning_change_info=warning_change_info,
        check_file_path=check_file_path
    )

    result = workflow.execute(config)

    if result.success:
        return result.check_file_path
    else:
        raise Exception(result.message)


def check_all_warning_code_service(warning_change_info: WarningChangeInfo) -> None:
    """
    检查所有警告代码服务（兼容性接口）
    
    Args:
        warning_change_info: 警告变更信息
    """
    workflow = WarningCodeCheckWorkflow()

    config = WarningCodeCheckConfig(
        code_folder=warning_change_info.code_folder,
        warning_change_info=warning_change_info,
        check_file_path=warning_change_info.check_file_path
    )

    result = workflow.execute(config)

    if not result.success:
        raise Exception(result.message)


def gen_and_check_all_warning_code_service(warning_change_info: WarningChangeInfo) -> str:
    """
    生成和检查所有警告代码服务（兼容性接口）
    
    Args:
        warning_change_info: 警告变更信息
        
    Returns:
        检查结果文件路径
    """
    workflow = WarningCodeGenAndCheckWorkflow()

    config = WarningCodeGenAndCheckConfig(
        pre_sample_book_url=warning_change_info.pre_sample_book_url,
        after_sample_book_url=warning_change_info.after_sample_book_url,
        code_gen_tools_url=warning_change_info.code_gen_tools_url,
        relate_book_folder=warning_change_info.relate_book_folder,
        adas_book_path=warning_change_info.adas_book_path,
        code_folder=warning_change_info.code_folder,
        check_file_path=warning_change_info.check_file_path
    )

    result = workflow.execute(config)

    if result.success:
        return result.check_file_path
    else:
        raise Exception(result.message)
