"""
通信故障安全CS工作流数据模型

V字对应：
2.1 基本設計 通信故障安全 CS

模块简介和主要功能说明：
定义通信故障安全CS工作流中使用的数据模型，包括输入参数、输出结果和配置模型。

主要功能：
1. 定义输入参数验证模型
2. 定义输出结果数据模型
3. 定义配置参数模型
4. 提供数据验证和序列化功能
"""

from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class SourceType(str, Enum):
    """数据源类型枚举"""
    BIT_ASSIGN = "bit_assign"
    SPI_JSON = "spi_json"


class InputDataModel(BaseModel):
    """工作流输入数据模型"""
    source_type: SourceType = Field(..., description="数据源类型")
    commit_id: str = Field(..., description="提交ID", min_length=1)
    compared_commit_id: Optional[str] = Field(None, description="对比的提交ID，如果为None则与上一个提交比较")
    repository_path: str = Field(None, description="本地仓库路径")
    
    # BitAssign相关参数
    bit_assign_path: Optional[str] = Field(None, description="BitAssign文件路径")
    
    # SPI JSON相关参数
    can_json_path: Optional[str] = Field(None, description="CAN信号JSON文件路径")
    ctrl_json_path: Optional[str] = Field(None, description="控制信号JSON文件路径")
    
    # 搜索选项
    case_sensitive: bool = Field(True, description="是否区分大小写")

    @field_validator('source_type')
    @classmethod
    def validate_source_type(cls, v):
        """验证数据源类型"""
        if v not in SourceType:
            raise ValueError(f"不支持的数据源类型: {v}")
        return v

    @field_validator('bit_assign_path')
    @classmethod
    def validate_bit_assign_path(cls, v, info):
        """验证BitAssign文件路径"""
        if info.data.get('source_type') == SourceType.BIT_ASSIGN and not v:
            raise ValueError("当数据源类型为bit_assign时，bit_assign_path不能为空")
        return v

    @field_validator('can_json_path')
    @classmethod
    def validate_can_json_path(cls, v, info):
        """验证CAN JSON文件路径"""
        if info.data.get('source_type') == SourceType.SPI_JSON and not v:
            raise ValueError("当数据源类型为spi_json时，can_json_path不能为空")
        return v

    @field_validator('ctrl_json_path')
    @classmethod
    def validate_ctrl_json_path(cls, v, info):
        """验证控制信号JSON文件路径"""
        if info.data.get('source_type') == SourceType.SPI_JSON and not v:
            raise ValueError("当数据源类型为spi_json时，ctrl_json_path不能为空")
        return v


class MatchedSignalModel(BaseModel):
    """匹配到的信号模型"""
    commit_id: str = Field(..., description="提交ID")
    file_path: str = Field(..., description="文件路径")
    file_name: str = Field(..., description="文件名")
    line_number: int = Field(..., description="行号")
    line_content: str = Field(..., description="行内容")
    frame_id: str = Field(..., description="帧ID")
    matched_signal: str = Field(..., description="匹配的信号名称")
    
    # BitAssign特有字段
    signal_name_en: Optional[str] = Field(None, description="信号英文名称")
    signal_name_ja: Optional[str] = Field(None, description="信号日文名称")
    
    # SPI JSON特有字段
    signal_name: Optional[str] = Field(None, description="信号名称")
    max_value: Optional[Union[str, int, float]] = Field(None, description="最大值")
    min_value: Optional[Union[str, int, float]] = Field(None, description="最小值")
    notes: Optional[str] = Field(None, description="备注")
    main_to_sub_com_id: Optional[Union[str, int, float]] = Field(None, description="主到子通信ID")
    sub_to_main_com_id: Optional[Union[str, int, float]] = Field(None, description="子到主通信ID")
    fld_len: Optional[Union[str, int]] = Field(None, description="字段长度")
    fld_strt: Optional[Union[str, int]] = Field(None, description="字段起始位置")
    
    # 通用字段
    init_value: Optional[Union[str, int, float]] = Field(None, description="初始值")
    text_file_link:  str = Field(..., description="代码行跳转链接")


class OutputDataModel(BaseModel):
    """工作流输出数据模型"""
    output_file_path: Optional[str] = Field(None, description="输出文件路径")
    total_files_processed: int = Field(0, description="处理的文件总数")
    total_matches_found: int = Field(0, description="找到的匹配总数")
    can_matches: List[MatchedSignalModel] = Field(default_factory=list, description="CAN信号匹配结果")
    ctrl_matches: Optional[List[MatchedSignalModel]] = Field(None, description="控制信号匹配结果")


class ConfigModel(BaseModel):
    """工作流配置模型"""
    name: str = Field("通信故障安全CS工作流", description="工作流名称")
    description: str = Field("分析代码变更中的CAN信号匹配", description="工作流描述")
    version: str = Field("1.0.0", description="版本号")
    author: str = Field("qiliang_zhou", description="作者")
    
    # 处理配置
    max_workers: int = Field(10, ge=1, le=20, description="最大并发线程数")
    default_case_sensitive: bool = Field(True, description="默认是否区分大小写")
    
    # 输出配置
    output_format: str = Field("xlsx", description="输出文件格式")
    include_summary: bool = Field(True, description="是否包含摘要信息")
    
    # Gerrit配置（从环境变量获取）
    gerrit_enabled: bool = Field(True, description="是否启用Gerrit集成")
    
    # 文件处理配置
    supported_file_extensions: List[str] = Field(
        default=[".c", ".cpp", ".h", ".hpp"], 
        description="支持的文件扩展名"
    )

    @field_validator('max_workers')
    @classmethod
    def validate_max_workers(cls, v):
        """验证最大工作线程数"""
        if v < 1 or v > 20:
            raise ValueError("最大工作线程数必须在1-20之间")
        return v


class SignalInfo(BaseModel):
    """信号信息基础模型"""
    frame_id: List[str] = Field(..., description="帧ID列表")
    label: str = Field(..., description="信号标签")
    init_value: List[Union[str, int, float]] = Field(..., description="初始值列表")


class BitAssignSignalInfo(SignalInfo):
    """BitAssign信号信息模型"""
    name_en: List[str] = Field(..., description="英文名称列表")
    name_ja: List[str] = Field(..., description="日文名称列表")


class SPISignalInfo(SignalInfo):
    """SPI信号信息模型"""
    name: List[str] = Field(..., description="名称列表")
    max_value: List[Union[int, float]] = Field(..., description="最大值列表")
    min_value: List[Union[int, float]] = Field(..., description="最小值列表")
    notes: List[str] = Field(..., description="备注列表")
    main_to_sub_com_id: List[str] = Field(..., description="主到子通信ID列表")
    sub_to_main_com_id: List[str] = Field(..., description="子到主通信ID列表")
    fld_len: List[int] = Field(..., description="字段长度列表")
    fld_strt: List[int] = Field(..., description="字段起始位置列表")
