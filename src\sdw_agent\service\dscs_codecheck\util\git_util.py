import subprocess
from typing import Dict, List, Tuple
from pathlib import Path
from sdw_agent.service.dscs_codecheck.models import GitCommit<PERSON>hange

def get_commit_changes_between(
    repo_path: str, 
    start_commit: str, 
    end_commit: str
) -> List[GitCommitChange]:
    """
    获取两个commit之间的所有变更详情
    
    Args:
        repo_path: Git仓库本地路径
        start_commit: 起始commit id
        end_commit: 结束commit id
        
    Returns:
        按提交顺序排列的变更列表(从旧到新)
    """
    # 验证仓库和commit id
    _validate_git_repo(repo_path)
    _validate_commit_id(repo_path, start_commit)
    _validate_commit_id(repo_path, end_commit)
    
    # 获取commit列表
    commit_ids = _get_commit_range(repo_path, start_commit, end_commit)
    
    changes = []
    for commit_id in commit_ids:
        # 获取提交元信息
        author = _get_commit_author(repo_path, commit_id)
        
        # 获取变更文件列表
        changed_files = _get_changed_files(repo_path, commit_id)
        
        file_changes = {}
        for file_path in changed_files:
            diff = _get_file_diff(repo_path, commit_id, file_path)
            code_before = _get_file_at_commit(repo_path, f"{commit_id}^", file_path)
            code_after = _get_file_at_commit(repo_path, commit_id, file_path)
            
            file_changes[file_path] = {
                'diff': diff,
                'code_before': code_before,
                'code_after': code_after,
            }
        
        changes.append(GitCommitChange(
            commit_id=commit_id,
            author=author,
            changes=file_changes
        ))
    
    return changes

# ---------------------- 辅助函数 ----------------------

def _get_file_diff(repo_path: str, commit_id: str, file_path: str) -> str:
    """获取单个文件的变更差异"""
    cmd = ["git", "-C", repo_path, "diff", f"{commit_id}^", commit_id, "--", file_path]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
    return result.stdout

def _get_full_tree_at_commit(repo_path: str, commit_id: str) -> Dict[str, str]:
    """获取commit时的完整代码树"""
    cmd = ["git", "-C", repo_path, "ls-tree", "-r", "--name-only", commit_id]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
    
    full_tree = {}
    for file_path in result.stdout.splitlines():
        content = _get_file_at_commit(repo_path, commit_id, file_path)
        full_tree[file_path] = content
    
    return full_tree

def _validate_git_repo(repo_path: str):
    """验证是否为有效的Git仓库"""
    git_dir = Path(repo_path) / ".git"
    if not git_dir.exists():
        raise ValueError(f"不是有效的Git仓库: {repo_path}")

def _validate_commit_id(repo_path: str, commit_id: str):
    """验证commit id是否存在"""
    cmd = ["git", "-C", repo_path, "cat-file", "-e", f"{commit_id}^{{commit}}"]
    try:
        subprocess.run(cmd, check=True, capture_output=True)
    except subprocess.CalledProcessError:
        raise ValueError(f"Commit不存在: {commit_id}")

def _get_commit_range(repo_path: str, start: str, end: str) -> List[str]:
    """获取两个commit之间的commit列表(包含start和end)"""
    if start == end:
        return [start]
    
    try:
        # 获取start..end的所有提交（不包含start），然后手动添加start
        cmd = ["git", "-C", repo_path, "rev-list", "--reverse", f"{start}..{end}"]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        commits = [start] + [line.strip() for line in result.stdout.split('\n') if line.strip()]
        
        # 验证end是否在结果中（因为某些情况下可能不会包含end）
        if end not in commits:
            commits.append(end)
            
        return commits
    except subprocess.CalledProcessError as e:
        raise ValueError(f"Invalid commit range: {e.stderr.strip()}")

def _get_commit_author(repo_path: str, commit_id: str) -> str:
    """获取提交作者信息"""
    cmd = ["git", "-C", repo_path, "show", "-s", "--format=%an <%ae>", commit_id]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0 or not result.stdout:
        return f"Unknown <unknown>"
    return result.stdout.strip()

def _get_changed_files(repo_path: str, commit_id: str) -> List[str]:
    """获取提交中变更的文件列表"""
    cmd = ["git", "-C", repo_path, "diff-tree", "--no-commit-id", "--name-only", "-r", commit_id]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
    return [f for f in result.stdout.split('\n') if f]

def _get_file_at_commit(repo_path: str, commit_id: str, file_path: str) -> str:
    """获取指定commit时的文件内容"""
    cmd = ["git", "-C", repo_path, "show", f"{commit_id}:{file_path}"]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError:
        return ""  # 文件可能被删除
