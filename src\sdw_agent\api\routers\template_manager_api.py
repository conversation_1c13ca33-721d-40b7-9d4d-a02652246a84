"""
模板文件管理API路由

V字对应：
x.x 对应的V字阶段
x. 对应的V字项目

模板文件管理相关的API接口，提供模板文件的配置、查询和管理功能。

主要功能：
1. 获取模板文件列表
2. 设置或重置模板文件（设置后自动复制到输入目录，传null重置为内置模板）
3. 获取指定工作流的模板信息
"""

import os
from typing import Dict, Any, Optional
from fastapi import APIRouter
from pydantic import BaseModel, Field
from loguru import logger

from sdw_agent.service.template_manager import template_manager


# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/template", tags=["模板文件管理"])


# 定义请求和响应模型
class SetCustomTemplateRequest(BaseModel):
    """设置自定义模板请求模型"""
    template_file_name: str = Field(..., description="模板文件名称标识", examples=["design_cs_file"])
    template_path: Optional[str] = Field(None, description="自定义模板文件路径，null或不传表示重置为内置模板", examples=["C:/templates/custom_template.xlsm"])





class TemplateResponse(BaseModel):
    """模板管理响应模型"""
    code: int = Field(0, description="状态码，0表示成功，其他表示失败")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")


@router.get("/list",
            summary="获取模板文件列表",
            description="获取所有工作流的模板文件配置信息",
            response_model=TemplateResponse)
async def get_template_list():
    """
    获取模板文件列表
    
    Returns:
        包含所有模板配置信息的响应
    """
    try:
        templates = template_manager.get_template_list()
        
        return TemplateResponse(
            code=0,
            msg="获取模板列表成功",
            data={
                "templates": templates,
                "total_count": len(templates)
            }
        )
        
    except Exception as e:
        logger.exception(f"获取模板列表失败: {str(e)}")
        return TemplateResponse(
            code=1,
            msg=f"获取模板列表失败: {str(e)}",
            data={}
        )


@router.post("/set_custom",
             summary="设置或重置模板文件",
             description="为指定模板文件设置自定义路径或重置为内置模板。设置自定义模板后自动复制到输入目录，传入null则重置为内置模板",
             response_model=TemplateResponse)
async def set_custom_template(request: SetCustomTemplateRequest):
    """
    设置或重置模板文件

    Args:
        request: 设置模板的请求参数，template_path为null时重置为内置模板

    Returns:
        设置结果响应
    """
    try:
        if request.template_path == "":
            request.template_path = None

        # 验证文件路径（如果不为None）
        if request.template_path is not None and not os.path.exists(request.template_path):
            return TemplateResponse(
                code=1,
                msg=f"模板文件不存在: {request.template_path}",
                data={}
            )

        if request.template_path is not None:
            # 如果提供了模板路径，先复制到输入目录，然后设置复制后的路径为自定义模板
            from datetime import datetime

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            original_name = os.path.splitext(os.path.basename(request.template_path))[0]
            extension = os.path.splitext(request.template_path)[1]
            filename = f"{original_name}_{timestamp}{extension}"

            # 先复制文件到输入目录
            copied_path = template_manager.copy_template_to_input_dir_from_source(
                request.template_path,
                filename
            )

            if copied_path:
                logger.info(f"模板文件已自动复制到输入目录: {copied_path}")

                # 设置复制后的路径为自定义模板路径
                success = template_manager.set_custom_template(
                    request.template_file_name,
                    copied_path
                )

                if success:
                    logger.info(f"成功设置模板文件 {request.template_file_name} 的自定义路径: {copied_path}")
                    return TemplateResponse(
                        code=0,
                        msg="设置自定义模板成功，并已复制到输入目录",
                        data={
                            "template_file_name": request.template_file_name,
                            "template_path": request.template_path,
                            "copied_to_input_dir": copied_path
                        }
                    )
                else:
                    return TemplateResponse(
                        code=1,
                        msg="设置模板失败",
                        data={}
                    )
            else:
                return TemplateResponse(
                    code=1,
                    msg="复制模板文件到输入目录失败",
                    data={}
                )
        else:
            # 重置为内置模板
            success = template_manager.set_custom_template(
                request.template_file_name,
                None
            )

            if success:
                logger.info(f"成功重置模板文件 {request.template_file_name} 为使用内置模板")
                return TemplateResponse(
                    code=0,
                    msg="重置为内置模板成功",
                    data={
                        "template_file_name": request.template_file_name,
                        "template_path": None,
                        "current_template": template_manager.get_template_path(request.template_file_name)
                    }
                )
            else:
                return TemplateResponse(
                    code=1,
                    msg="重置模板失败",
                    data={}
                )

    except Exception as e:
        logger.exception(f"设置模板失败: {str(e)}")
        return TemplateResponse(
            code=1,
            msg=f"设置模板失败: {str(e)}",
            data={}
        )



@router.get("/info/{template_file_name}",
            summary="获取指定模板文件的信息",
            description="获取指定模板文件当前使用的路径信息",
            response_model=TemplateResponse)
async def get_template_info(template_file_name: str):
    """
    获取指定模板文件的信息

    Args:
        template_file_name: 模板文件名称标识

    Returns:
        模板信息响应
    """
    try:
        # 获取当前使用的模板路径
        current_template = template_manager.get_template_path(template_file_name)

        # 获取模板配置信息
        templates = template_manager.get_template_list()
        template_info = templates.get(template_file_name)

        if template_info:
            return TemplateResponse(
                code=0,
                msg="获取模板信息成功",
                data={
                    "template_file_name": template_file_name,
                    "current_template_path": current_template,
                    "template_info": template_info
                }
            )
        else:
            return TemplateResponse(
                code=1,
                msg=f"未找到模板文件 {template_file_name} 的配置",
                data={}
            )

    except Exception as e:
        logger.exception(f"获取模板信息失败: {str(e)}")
        return TemplateResponse(
            code=1,
            msg=f"获取模板信息失败: {str(e)}",
            data={}
        )
