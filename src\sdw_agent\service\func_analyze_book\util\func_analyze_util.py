"""
函数分析工作流工具函数
"""
# ... existing code ...
import os
import re
from typing import Dict, List, Any, Optional

from langchain_core.prompts import Chat<PERSON>romptTemplate
from loguru import logger
from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_result
import pandas as pd

from sdw_agent.config.env import ENV
from sdw_agent.llm import kt_azure_azure_gpt4o
from sdw_agent.service.func_analyze_book.models import FunctionDetailData, FuncDescResult, \
    FuncReturnResult, FuncVarReturnResult, FuncProcessReturnResult, FuncParamResult, \
    FuncCalledResult
from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import \
    write_func_book_sheet
from sdw_agent.service.func_analyze_book.util.func_file_parser_util import \
    is_function_in_header, analyze_dir, parse_external_functions
from sdw_agent.util.git_util import get_git_structured_diff
from sdw_agent.util.gerrit_util import get_diff_code_from_gerrit


class FunctionAnalyzeUtils:
    """函数分析工具类"""

    @staticmethod
    def gen_func_return_varname(func_body: str) -> Optional[str]:
        """
        从嵌入式 C 函数体中找到所有 `return` 语句，并提取最后一个非数字、非常量的变量名。
        """
        return_statements = re.findall(r'\breturn\s+([a-zA-Z_][a-zA-Z0-9_]*|[\d.]+)', func_body)
        variables = [var for var in return_statements if not re.match(r'^\d+$|^\d+\.\d+$', var)]
        return variables[-1] if variables else None

    @staticmethod
    def get_function_open_type(code_path: str, func_name: str) -> str:
        """获取函数开放类型"""
        is_global = is_function_in_header(code_path, func_name)
        if not is_global:
            return "内部関数"

        name_info = func_name.split("_")
        if len(name_info) < 2 or name_info[1] == "g":
            return "ドメイン外公開IF"
        else:
            return "ドメイン内公開IF"

    @staticmethod
    def _mark_code_regions(content: str) -> List[bool]:
        """
        标记代码中的字符串和注释区域

        Args:
            content: 源代码内容

        Returns:
            布尔列表，True表示该位置是有效代码区域
        """
        in_string = False
        in_comment_line = False
        in_comment_block = False
        string_char = ''
        code_regions = [False] * len(content)

        for i, c in enumerate(content):
            # 处理字符串
            if not in_comment_block and not in_comment_line:
                if c in ['"', "'"] and (i == 0 or content[i - 1] != '\\'):
                    if not in_string:
                        in_string = True
                        string_char = c
                    elif c == string_char:
                        in_string = False
                        string_char = ''

            # 处理注释
            if not in_string:
                if i < len(content) - 1:
                    if content[i:i + 2] == '/*':
                        in_comment_block = True
                    elif content[i:i + 2] == '*/':
                        in_comment_block = False
                    elif content[i:i + 2] == '//':
                        in_comment_line = True

            code_regions[i] = not (in_string or in_comment_block or in_comment_line)

            if c == '\n':
                in_comment_line = False

        return code_regions

    @staticmethod
    def _find_function_end_position(content: str, brace_start_pos: int, code_regions: List[bool]) -> Optional[int]:
        """
        查找函数体结束位置

        Args:
            content: 源代码内容
            brace_start_pos: 函数体开始大括号位置
            code_regions: 代码区域标记列表

        Returns:
            函数体结束位置，如果未找到返回None
        """
        brace_count = 1
        current_pos = brace_start_pos + 1

        while current_pos < len(content):
            if code_regions[current_pos]:
                if content[current_pos] == '{':
                    brace_count += 1
                elif content[current_pos] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        return current_pos
            current_pos += 1

        return None

    @staticmethod
    def _extract_functions_from_content(content: str, code_regions: List[bool]) -> List[Dict[str, Any]]:
        """
        从内容中提取所有函数信息

        Args:
            content: 源代码内容
            code_regions: 代码区域标记列表

        Returns:
            函数信息列表
        """
        functions = []
        func_pattern = re.compile(r'''
            ^\s*                  # 行首空格
            ([\w\*\s]+?)\s+       # 返回类型
            (\w+)\s*             # 函数名
            \([^)]*\)\s*         # 参数列表
            \{                   # 函数体开始
        ''', re.VERBOSE | re.MULTILINE)

        for match in func_pattern.finditer(content):
            func_name = match.group(2)
            func_start_pos = match.start()
            brace_start_pos = match.end() - 1

            if not code_regions[brace_start_pos]:
                continue

            func_start_line = content[:func_start_pos].count('\n') + 1

            # 查找函数体结束位置
            func_end_pos = FunctionAnalyzeUtils._find_function_end_position(
                content, brace_start_pos, code_regions
            )

            if func_end_pos is None:
                logger.warning(f"函数 {func_name} 未找到匹配的闭合括号")
                continue

            func_end_line = content[:func_end_pos].count('\n') + 1

            functions.append({
                "name": func_name,
                "start_line": func_start_line,
                "end_line": func_end_line,
                "start_pos": func_start_pos,
                "end_pos": func_end_pos
            })

        return functions

    @staticmethod
    def _match_lines_to_functions(line_numbers: List[int], functions: List[Dict[str, Any]],
                                  lines: List[str]) -> Dict[int, Dict[str, Any]]:
        """
        将行号匹配到对应的函数

        Args:
            line_numbers: 要匹配的行号列表
            functions: 函数信息列表
            lines: 源代码行列表

        Returns:
            行号到函数信息的映射
        """
        result = {}

        for line_num in line_numbers:
            line_idx = line_num - 1
            if line_idx < 0 or line_idx >= len(lines):
                result[line_num] = {
                    "function_name": "行号超出文件范围",
                    "function_start": None,
                    "function_end": None
                }
                continue

            found = False
            for func in functions:
                if func["start_line"] <= line_num <= func["end_line"]:
                    result[line_num] = {
                        "function_name": func["name"],
                        "function_sign": lines[func["start_line"]] if func["name"] in lines[func["start_line"]] else
                        lines[func["start_line"] - 1],
                        "function_start": func["start_line"],
                        "function_end": func["end_line"]
                    }
                    found = True
                    break

            if not found:
                pass

        return result

    @staticmethod
    def find_function_by_line(content: str, line_numbers: List[int]) -> Dict[int, Dict[str, Any]]:
        """
        根据行号查找所在函数（优化括号匹配，支持字符串和注释中的括号忽略）
        """
        result = {}

        try:
            lines = content.split('\n')

            # 预处理：标记字符串和注释区域
            code_regions = FunctionAnalyzeUtils._mark_code_regions(content)

            # 提取函数定义
            functions = FunctionAnalyzeUtils._extract_functions_from_content(content, code_regions)

            # 匹配变更行号所属的函数
            result = FunctionAnalyzeUtils._match_lines_to_functions(line_numbers, functions, lines)

        except Exception as e:
            logger.error(f"处理文件时出错: {e}")

        return result


class GitDiffAnalyzer:
    """Git差异分析器"""

    @staticmethod
    def get_gerrit_diff_code(gerrit_url: str, commit_id: str, username: str = '', password: str = '') -> Dict[
        str, List]:
        """从Gerrit获取差异代码"""
        result = get_diff_code_from_gerrit(gerrit_url, commit_id, username, password)
        code_map = {}

        for key, _ in result.items():
            code_map[key] = {
                "after_code": [],
                "before_code": [],
                "add_code": [],
                "delete_code": [],
                "delete_lines": result[key]['changed_lines']['deleted'],
                "add_lines": result[key]['changed_lines']['added']
            }

            for code_block in result[key]['diff']['content']:
                if 'ab' in code_block:
                    code_map[key]["after_code"].extend(code_block['ab'])
                    code_map[key]["before_code"].extend(code_block['ab'])
                else:
                    code_map[key]["add_code"].extend(code_block.get('b', []))
                    code_map[key]["delete_code"].extend(code_block.get('a', []))
                    code_map[key]["after_code"].extend(code_block.get('b', []))
                    code_map[key]["before_code"].extend(code_block.get('a', []))

        change_func_map = {}
        for key, value in code_map.items():
            after_change = FunctionAnalyzeUtils.find_function_by_line(
                "\n".join(value["after_code"]), value["add_lines"])
            if after_change:
                change_func_map[key] = list({d["function_name"]: d for d in after_change.values()}.values())

        return change_func_map

    @staticmethod
    def get_local_diff_code(local_folder_path: str, commit_id_before: str, commit_id_after: str) -> Dict[str, List]:
        """获取本地代码差异"""
        diffs = get_git_structured_diff(local_folder_path, commit_id_after, commit_id_before)
        change_func_map = {}

        for file_path, diff_info in diffs.items():
            added_lines = []
            full_code = []
            hunks = diff_info.get("diff", [])

            for hunk in hunks:
                content = hunk.get('content', [])
                if hunk['type'] == 'ab':
                    full_code.extend(content)
                elif hunk['type'] == 'b':
                    added_lines.extend(content)
                    full_code.extend(content)
                else:
                    full_code.extend(content)

            if full_code:
                line_numbers = diff_info.get("changed_lines", {}).get("added", [])
                after_change = FunctionAnalyzeUtils.find_function_by_line("\n".join(full_code), line_numbers)
                if after_change:
                    change_func_map[file_path] = list({d["function_name"]: d for d in after_change.values()}.values())

        return change_func_map


class ComponentUtils:
    """组件相关工具类"""

    @staticmethod
    def extract_component_data(file_path: str, sheet_name: str) -> pd.DataFrame:
        """提取组件数据"""
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

        # 找到包含 "コンポーネント" 的行索引
        header_row_index = None
        for index, row in df.iterrows():
            if "コンポーネント" in row.values:
                header_row_index = index
                break

        if header_row_index is None:
            raise ValueError("未找到包含 'コンポーネント' 的行")

        # 重新设置表头
        df.columns = df.iloc[header_row_index]
        df = df.iloc[header_row_index + 1:]

        if "文件路径" not in df.columns or "コンポーネント" not in df.columns:
            raise ValueError("未找到 '文件路径' 或 'コンポーネント' 列")

        df["文件路径"] = df["文件路径"].apply(lambda path: os.path.normpath(path) if isinstance(path, str) else path)
        result = df[["文件路径", "コンポーネント"]]

        return result

    @staticmethod
    def find_module_in_string(paths_and_modules: pd.DataFrame, caller_path: str) -> str:
        """根据文件路径匹配字符串内容，查找模块名称"""
        for _, row in paths_and_modules.iterrows():
            try:
                file_path = row["文件路径"]
                module_name = row["コンポーネント"]
                if file_path in caller_path:
                    logger.info(f"{file_path}   {caller_path}")
                    return module_name
            except Exception as e:
                logger.error(f"find module error {file_path} {caller_path}: {e}")
                continue

        return ""


class FunctionRelationshipAnalyzer:
    """函数关系分析器"""

    @staticmethod
    def gen_change_parents_service(func_detail: FunctionDetailData, root_dir: str, component_book_url: str) -> Dict[
        str, Any]:
        """生成父函数调用关系"""

        father_called_dict = analyze_dir(root_dir, func_detail.func_name)
        module_df = ComponentUtils.extract_component_data(component_book_url, "SW-C List_R-Car")

        for called_func, called_funcs_info in father_called_dict.items():
            called_mean = parse_func_called_mean_LLM(
                func_detail.func_name,
                func_detail.func_body,
                called_func,
                called_funcs_info["function_body"]
            )

            called_funcs_info["mean"] = called_mean
            called_funcs_info["module"] = ComponentUtils.find_module_in_string(module_df, called_funcs_info["file"])
            logger.info(f"{called_func} 模块: {called_funcs_info['module']}")

        return father_called_dict

    @staticmethod
    def gen_calling_func_service(func_detail: FunctionDetailData, root_dir: str, component_book_url: str) -> Dict[
        str, Any]:
        """生成子函数调用关系"""
        # var_name_list = []
        # if func_detail.allVariables['global_variables'] :
        #     var_name_list.append([
        #         item['name'] for key in ['global_variables']
        #         for item in func_detail.allVariables.get(key, []) if 'name' in item
        #     ])
        # if func_detail.allVariables['local_variables'] :
        #     var_name_list.append([
        #         item['name'] for key in ['local_variables']
        #         for item in func_detail.allVariables.get(key, []) if 'name' in item
        #     ])
        var_name_list = [
            item['name'] for key in ['global_variables', 'local_variables']
            for item in func_detail.allVariables.get(key, []) if 'name' in item
        ]

        var_name_list.append(func_detail.func_name)
        father_called_dict = parse_external_functions(func_detail.func_body, root_dir, var_name_list)
        module_df = ComponentUtils.extract_component_data(component_book_url, "SW-C List_R-Car")

        for called_func, called_funcs_info in father_called_dict.items():
            called_mean = parse_func_sub_calling_mean_LLM(
                func_detail.func_name,
                func_detail.func_body,
                called_func,
                called_funcs_info["function_body"]
            )

            called_funcs_info["mean"] = called_mean
            called_funcs_info["module"] = ComponentUtils.find_module_in_string(module_df, called_funcs_info["file"])
            logger.info(f"{called_func} 模块: {called_funcs_info['module']}")

        return father_called_dict


class ExcelBookUtils:
    """Excel表格操作工具"""

    @staticmethod
    def write_function_book(pre_book_url: str, func_change_info_list: List[FunctionDetailData]) -> str:
        """写入函数分析书"""
        return write_func_book_sheet(pre_book_url, func_change_info_list)

    @staticmethod
    def get_sample_book_path() -> str:
        """获取样本书路径"""
        return os.path.join(os.path.dirname(__file__), os.path.normpath('../book_data/func_book_sample.xlsx'))


def parse_func_body(func_name, func_body):
    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt.function_book_analysis_prompts.function_desc_prompts),
        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncDescResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncDescResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.design_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_return_variavle(func_name, func_body):
    """
    解析函数返回值信息，通过大模型生成返回值描述

    参数:
        func_name (str): 需要分析的函数名称
        func_body (str): 函数体内容字符串

    返回:
        str: 解析得到的函数返回值描述信息

    异常:
        抛出任何执行过程中发生的异常
    """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_return_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncReturnResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncReturnResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.return_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_variavle(func_name, func_body, func_variavle):
    """
    解析函数变量信息，通过大模型生成变量描述

    参数:
        func_name (str): 需要分析的函数名称
        func_body (str): 函数体内容字符串
        func_variavle (str): 需要解析的函数变量名称

    返回:
        str: 解析得到的变量描述信息

    异常:
        抛出任何执行过程中发生的异常
    """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_variable_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncVarReturnResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncVarReturnResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "lang": lang, "func_var": func_variavle})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.return_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_return_variavle(func_name, func_body):
    """
    解析函数返回值信息，通过大模型生成返回值描述

    参数:
        func_name (str): 需要分析的函数名称
        func_body (str): 函数体内容字符串

    返回:
        str: 解析得到的函数返回值描述信息

    异常:
        抛出任何执行过程中发生的异常
    """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_return_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncReturnResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncReturnResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.return_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_Process(func_name, func_body):
    """
    解析函数的处理流程，调用大模型生成函数处理结果。

    :param func_name: 函数名称
    :param func_body: 函数体内容
    :return: 解析后的函数处理结果（UML）
    """
    # 创建一个 ChatPromptTemplate，用于构造大模型的输入模板
    template = ChatPromptTemplate(
        [
            # 系统消息部分（注释掉，可能是环境配置语言提示）
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_process_prompts),
        ],
        template_format="mustache"  # 使用 Mustache 模板格式
    )

    # 定义语言映射表，用于选择语言
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    # 将模板与 Azure GPT-4 的调用链结合，指定输出结构为 FuncProcessReturnResult
    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncProcessReturnResult)

    # 默认语言设置为中文，如果未找到对应语言则使用中文
    lang = lang_map.get("", "中文")
    resp = None  # 初始化返回值

    # 定义自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        """
        判断返回结果是否为 None，用于重试条件。
        :param result: 调用链的返回结果
        :return: True 表示需要重试，False 表示不需要重试
        """
        return result is None

    # 定义重试机制
    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待时间（起始1秒，上限10秒）
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 自定义重试条件：返回值是 None 时重试
    )
    def _invoke():
        """
        调用大模型生成函数处理结果。
        :return: FuncProcessReturnResult 的 UML 结果
        """
        resp: FuncProcessReturnResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body, "lang": lang}
        )
        # 如果返回结果为 None，记录警告信息并触发重试
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.func_proc_UML  # 返回解析后的 UML 结果

    try:
        # 调用 _invoke 函数并获取返回结果
        resp = _invoke()
        return resp
    except Exception as e:
        # 捕获异常并抛出
        raise Exception(e)

def parse_func_param(func_name, func_body, func_param):
    """
    解析函数参数信息，通过大模型生成参数描述

    参数:
        func_name (str): 需要分析的函数名称
        func_body (str): 函数体内容字符串
        func_param (str): 需要解析的函数参数名称

    返回:
        str: 解析得到的函数参数描述信息

    异常:
        抛出任何执行过程中发生的异常
    """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_praram_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncParamResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncParamResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body, "func_param": func_param,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.param_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_called_mean_LLM(func_name, func_body, father_func_name, father_func_body):
    """
     使用大模型解析函数调用关系，生成函数调用的描述信息。

     :param func_name: str，当前函数的名称。
     :param func_body: str，当前函数的代码内容。
     :param father_func_name: str，父函数的名称（调用当前函数的函数）。
     :param father_func_body: str，父函数的代码内容。
     :return: str，解析后的函数调用描述信息。

     功能说明：
     - 通过构造模板和调用大模型，解析函数的调用关系。
     - 支持多语言（中文、日文、英文），默认语言为中文。
     - 使用重试机制，确保在调用失败时能够自动重试。

     主要逻辑：
     1. 构造 `ChatPromptTemplate`，用于生成大模型的输入模板。
     2. 定义语言映射表，支持多语言选择。
     3. 使用 `retry` 装饰器实现重试机制：
        - 当返回结果为 `None` 时触发重试。
        - 最大重试次数为 5 次，等待时间采用指数增长。
     4. 调用大模型生成函数调用描述信息。
     5. 捕获异常并抛出，确保错误信息不会丢失。

     异常处理：
     - 如果调用链发生异常，抛出 `Exception` 并记录错误信息。
     """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_called_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncCalledResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncCalledResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "father_func_name": father_func_name, "father_func_body": father_func_body,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.called_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)


def parse_func_sub_calling_mean_LLM(func_name, func_body, sub_func_name, sub_func_body):
    """
    解析函数中子函数调用的意义，通过大模型生成调用描述

    参数:
        func_name (str): 主函数名称
        func_body (str): 主函数体内容字符串
        sub_func_name (str): 被调用的子函数名称
        sub_func_body (str): 子函数体内容字符串

    返回:
        str: 解析得到的子函数调用描述信息

    异常:
        抛出任何执行过程中发生的异常
    """
    template = ChatPromptTemplate(
        [
            # ('system', ENV.config.language_prompts),
            ("user", ENV.prompt.function_book_analysis_prompts.function_sub_calling_prompts),

        ],
        template_format="mustache"
    )
    lang_map = {"ja": "日本語", "zh": "中文", "en": "英文"}

    chain = template | kt_azure_azure_gpt4o.with_structured_output(FuncCalledResult)
    lang = lang_map.get("", "中文")
    resp = None

    # 新增自定义重试条件：当函数返回 None 时触发重试
    def is_none(result):
        return result is None

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=10),  # 指数增长等待 (起始1秒，上限60秒)
        stop=stop_after_attempt(5),  # 最多重试5次
        retry=retry_if_result(is_none)  # 新增：返回值是 None 时重试
    )
    def _invoke():
        resp: FuncCalledResult = chain.invoke(
            {"func_name": func_name, "func_body": func_body,
             "calling_func_name": sub_func_name, "calling_func_body": sub_func_body,
             "lang": lang})
        if resp is None:
            resp.warning("大模型调用异常, 现在重试")
        return resp.called_desc

    try:
        resp = _invoke()
        return resp
    except Exception as e:
        raise Exception(e)
