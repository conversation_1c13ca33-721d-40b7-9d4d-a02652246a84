"""
模块名称 Denso code rule check

V字对应：【FID_DEV_0024】基于编码规范的自动化代码检查

主要功能：
基于Git差分代码和预定义的软件设计规范，通过AI模型自动生成代码规范检查报告。

"""

import sys
import requests
import json
import datetime

from pathlib import Path
# project_root = Path(__file__).parent.parent.parent.parent
# sys.path.append(str(project_root))
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from typing import Optional  # Dict, List, Any, Optional
from urllib.parse import quote
from loguru import logger
from sdw_agent.config.env import ENV  # noqa
from langchain_core.prompts import ChatPromptTemplate
from sdw_agent.llm.llm_util import get_ai_message


@register_workflow("dn_codecheck")
class CodeCheckerWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.REVISION_ID = "current"
        self.prompt = ""

    def validate_input(self, change_id) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据模型

        Returns:
            bool: 验证是否通过
        """
        try:
            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, change_id):

        diff_info = self.get_gerrit_diff_code(change_id)
        result_array = []

        for file_name, info in diff_info.items():
            if file_name == 'COMMIT_MSG':
                continue

            file_content = ''.join([line + '\n' for line in info['code_all']])
            file_content_after = ''.join([line + '\n' for line in info['code_after']])

            results = self.send_to_llm(file_content_after, file_content)
            for i, result in enumerate(results):
                result_array.append(f"{file_name}:{result}")

        exist_list, not_exist_list = self.extract_results(result_array)

        # 输出结果
        self.logger.info("存在列表：")
        for item in exist_list:
            self.logger.info(item)

        self.logger.info("\n不存在列表：")
        for item in not_exist_list:
            self.logger.info(item)

        codecheck_report = self.format_output(exist_list)
        self.logger.info(codecheck_report)

        output_dir = Path(ENV.config.output_data_path) / "dn_codecheck"
        output_dir.mkdir(parents=True, exist_ok=True)  # 确保目录存在   
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"codecheck_{change_id}_{timestamp}.txt"
        file_path = output_dir / file_name  # 使用 Path 的 / 运算符

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(codecheck_report)
            self.logger.info(f"文件已写入：{file_path}")
        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message="DN编码规范检查成功",
            data={"file_path": str(file_path)}
        )

    def requests_llm(self, query: str, system_prompt: str) -> str:
        """调用 AI 模型处理查询"""
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", "{system_prompt}"),
            ("user", "{query}"),
        ])
        invoke_dict = {
            "system_prompt": system_prompt,
            "query": query
        }
        llm_response = get_ai_message(chat_prompt, invoke_dict)
        return llm_response.content

    def send_to_llm(self, file_content_after, file_content):
        """发送代码到LLM进行规则检查"""
        rule_ids = list(self.config['codereviewpoint'].keys())
        list_result = [None] * (max(map(int, rule_ids)) + 1) if rule_ids else [None]
        if file_content_after == file_content:  # 新增文件
            for rule_id, rule_details in self.config['codereviewpoint'].items():
                # 构造查询内容
                system_prompt = rule_details['rule']
                query = f'以下是新增代码文件：\n{file_content}'
                result = self.requests_llm(query, system_prompt)
                list_result[rule_id] = f'规则编号：{rule_details["name"]} \n{result}'            
        else:  # 变更文件
            for rule_id, rule_details in self.config['codereviewpoint'].items():
                # 构造查询内容
                system_prompt = rule_details['rule']
                query = f"\n变更代码: {file_content_after}\n原始代码: {file_content}"
                result = self.requests_llm(query, system_prompt)
                list_result[rule_id] = f'规则编号：{rule_details["name"]} \n{result}'                  
        return list_result

    def get_gerrit_diff_code(self, change_id):
        """获取Gerrit代码差异信息"""
        gerrit_url = self.config['gerrit']['url']
        auth = (self.config['gerrit']['username'], self.config['gerrit']['password'])

        files_url = f"{gerrit_url}/a/changes/{change_id}/revisions/{self.REVISION_ID}/files/"
        response = requests.get(files_url, headers={"Accept": "application/json"}, auth=auth)

        file_info_dict = {}

        if response.status_code == 200:
            # 去除Gerrit的JSON安全前缀
            text = response.text.lstrip(")]}'")
            files_data = json.loads(text)

            # 初始化文件信息字典
            for file_path, file_meta in files_data.items():
                try:
                    file_name = file_path.split('/')[-1]
                except IndexError:
                    file_name = file_path
                file_info_dict[file_name] = {
                    'path': file_path,
                    'code_all': [],
                    'code_after': [],
                    'code_before': []
                }

            # 下载并处理每个文件内容
            for file_name, file_info in file_info_dict.items():
                encoded_path = quote(file_info['path'], safe="")
                content_url = f"{gerrit_url}/a/changes/{change_id}/revisions/{self.REVISION_ID}/files/{encoded_path}/diff"

                content_response = requests.get(content_url, headers={"Accept": "text/plain"}, auth=auth)

                if content_response.status_code == 200:
                    text = content_response.text.lstrip(")]}'")
                    text = text.replace('\\u003d', '=').replace('\\u0026', '&')  # 还原特殊字符
                    diff_data = json.loads(text)

                    for item in diff_data['content']:
                        if 'b' in item:
                            for line in item['b']:
                                line = self._process_line(line)
                                file_info['code_after'].append(line)
                                file_info['code_all'].append(line)
                        elif 'ab' in item:
                            for line in item['ab']:
                                line = self._process_line(line)
                                file_info['code_all'].append(line)
                        elif 'a' in item:
                            for line in item['a']:
                                line = self._process_line(line)
                                file_info['code_before'].append(line)
                else:
                    logger.error(f"  Failed to fetch {file_name}: {content_response.status_code}")
        else:
            logger.error("Failed to fetch files list:", response.status_code)

        return file_info_dict

    def _process_line(self, line):
        """处理diff行数据"""
        if line.endswith(','):
            line = line[:-1]  # 去除末尾逗号
        if line.startswith("'") and line.endswith("'"):
            line = line[1:-1]  # 去除首尾单引号
        return ''.join(line)

    def format_output(self, patch_content):
        """格式化LLM输出结果"""
        # 构造查询内容（根据新提示词格式）
        system_prompt = f"""下面是一段代码规则检查报告，请重新整理报告格式，同一个文件的问题放到一起，格式要求如下：
        文件名: XXX
        违反规则编号: XXX
        理由: XXX
        """
        return self.requests_llm(patch_content, system_prompt)

    def extract_results(self, result_array):
        """提取并分类检查结果"""
        exist_list = []
        not_exist_list = []

        for result in result_array:
            if 'None' in result or not result.strip():
                continue

            # 提取规则编号
            rule_number = None
            rule_number_start = result.find("规则编号：")
            if rule_number_start != -1:
                rule_number_text = result[rule_number_start + len("规则编号："):].strip()
                newline_pos = rule_number_text.find("\n")
                filename_pos = rule_number_text.find("文件名：")

                end_pos = min(
                    [pos for pos in [newline_pos, filename_pos] if pos != -1] or 
                    [len(rule_number_text)]
                )
                rule_number = rule_number_text[:end_pos].strip()
            else:
                rule_number = result.split(':')[0]

            # 提取文件名
            filename = ""
            filename_start = result.find("文件名：")
            if filename_start != -1:
                filename = result[filename_start + len("文件名："):].split('\n')[0].strip()
            else:
                filename = result.split(':')[0]

            # 提取结论
            conclusion_start = result.find("结论：")
            if conclusion_start == -1:
                continue
            conclusion_text = result[conclusion_start + 3:].split('\n')[0].strip()

            # 提取理由
            reason_start = result.find("理由：")
            reason_text = result[reason_start + 3:].strip() if reason_start != -1 else ""

            # 分类存储
            if "不存在" in conclusion_text:
                not_exist_list.append({
                    "编号": rule_number,
                    "文件名": filename,
                    "结论": conclusion_text,
                    "理由": reason_text
                })
            elif "存在" in conclusion_text:
                exist_list.append({
                    "编号": rule_number,
                    "文件名": filename,
                    "结论": conclusion_text,
                    "理由": reason_text
                })

        return exist_list, not_exist_list



if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python script.py <change_id>")
        sys.exit(1)
    change_id = sys.argv[1]
    workflow = CodeCheckerWorkflow()
    result_path = workflow.run(change_id)
    print(f"Report saved to: {result_path}")