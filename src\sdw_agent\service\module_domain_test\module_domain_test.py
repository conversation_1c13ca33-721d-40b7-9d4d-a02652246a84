"""
模块域测试服务

V字对应：
4.1 単体検査
52. モジュール単体検査実施＆結果検証
53. ドメイン単体検査実施＆結果検証

该模块提供模块和域单体测试相关的服务，用于根据要件一览表填写CheckListSheet。

主要功能：
1. 解析Excel文件中的模块参数信息
2. 基于参数范围生成测试用例（支持LLM和规则两种解析方式）
3. 生成笛卡尔积测试用例组合
4. 将测试用例写入CheckListSheet模板

技术特点：
- 支持大模型智能解析参数范围
- 提供规则解析作为备用方案
- 自动处理Excel模板格式
- 支持复杂参数组合的测试用例生成

"""

import itertools
from typing import List, Dict, Any, Union, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

import pandas as pd
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from pydantic import Field, BaseModel, field_validator

from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.util.excel.core import ExcelUtil
from sdw_agent.util.select_util import select_one


# 自定义异常类
class ModuleTestError(Exception):
    """模块测试相关的基础异常类"""
    pass


class ParameterParsingError(ModuleTestError):
    """参数解析错误"""
    pass


class ExcelProcessingError(ModuleTestError):
    """Excel处理错误"""
    pass


class TestCaseGenerationError(ModuleTestError):
    """测试用例生成错误"""
    pass


# 常量定义
class ModuleTestConstants:
    """模块测试相关常量"""

    # Excel列名常量
    MODULE_NAME_COLUMN = "功能模块"
    PARAM_TYPE_COLUMN = "参数类型"
    PARAM_NAME_COLUMN = "参数名称"
    PARAM_RANGE_COLUMN = "参数取值范围"

    # 参数类型常量
    INPUT_PARAM_TYPE = "入参"
    OUTPUT_PARAM_TYPE = "出参"

    # 范围解析相关常量
    RANGE_SEPARATOR = "~"
    VALUE_SEPARATOR = ","
    SMALL_RANGE_THRESHOLD = 10

    # Excel模板相关常量
    CASE_START_ROW = 3
    CASE_START_COLUMN = 16
    MAX_SHEET_NAME_LENGTH = 31

    # 模板关键字
    TEMPLATE_CASE_SHEET_KEYWORD = "用例"
    INPUT_ARGS_KEYWORD = "入参结果"
    OUTPUT_ARGS_KEYWORD = "出参结果"
    CATEGORY_KEYWORD = "分类"

    # 默认文件名
    DEFAULT_OUTPUT_FILE = "generated_test_cases.xlsx"


class ParameterType(Enum):
    """参数类型枚举"""
    INPUT = "input"
    OUTPUT = "output"


@dataclass
class ModuleInfo:
    """模块信息数据类"""
    name: str
    inputs: Dict[str, List[Any]]
    outputs: Dict[str, List[Any]]

    @property
    def input_count(self) -> int:
        """获取输入参数总数量"""
        return sum(len(values) for values in self.inputs.values())

    @property
    def output_count(self) -> int:
        """获取输出参数总数量"""
        return sum(len(values) for values in self.outputs.values())


@dataclass
class TestCase:
    """测试用例数据类"""
    case_id: int
    module: str
    inputs: Dict[str, Any]
    expected_outputs: Dict[str, List[Any]]


class ParameterValuesResult(BaseModel):
    """
    参数取值提取结果模型

    用于封装大模型解析参数范围后的结果，包含具体的取值列表和推理过程。
    """
    values: List[Union[int, str]] = Field(
        description="从参数范围字符串中提取的具体取值列表，支持数字和字符串类型"
    )
    reasoning: str = Field(
        description="选择这些值的理由和推理过程，用于调试和验证"
    )

    @field_validator('values')
    @classmethod
    def validate_values_not_empty(cls, v):
        """验证取值列表不能为空"""
        if not v:
            raise ValueError("参数取值列表不能为空")
        return v


class ParameterRangeParser:
    """
    参数范围解析器

    负责解析各种格式的参数范围字符串，支持LLM和规则两种解析方式。
    """

    def __init__(self, use_llm: bool = True):
        """
        初始化解析器

        Args:
            use_llm: 是否优先使用大模型解析，默认为True
        """
        self.use_llm = use_llm

    def parse_range_value(self, range_str: str) -> List[Any]:
        """
        解析参数范围字符串的主入口

        Args:
            range_str: 参数范围字符串

        Returns:
            解析后的参数值列表
        """
        if pd.isna(range_str) or not str(range_str).strip():
            logger.debug("参数范围为空，返回空列表")
            return []

        range_str = str(range_str).strip()
        logger.debug(f"开始解析参数范围: '{range_str}'")

        if self.use_llm:
            try:
                return self._parse_with_llm(range_str)
            except Exception as e:
                logger.warning(f"大模型解析失败，回退到规则解析: {e}")
                return self._parse_with_rules(range_str)
        else:
            return self._parse_with_rules(range_str)

    def _parse_with_llm(self, range_str: str) -> List[Any]:
        """
        使用大模型解析参数范围

        Args:
            range_str: 参数范围字符串

        Returns:
            解析后的参数值列表

        Raises:
            ParameterParsingError: 当大模型解析失败时抛出
        """
        logger.debug(f"使用大模型解析参数范围: '{range_str}'")
        template = self._create_llm_prompt_template()

        try:
            resp: ParameterValuesResult = get_ai_message_with_structured_output(
                template,
                {"range_str": range_str},
                ParameterValuesResult
            )

            if not resp.values:
                raise ParameterParsingError(f"大模型返回空的参数值列表: '{range_str}'")

            logger.info(f"大模型解析成功 '{range_str}' -> {resp.values}")
            logger.debug(f"解析理由: {resp.reasoning}")
            return resp.values

        except Exception as e:
            error_msg = f"大模型解析参数范围失败 '{range_str}': {str(e)}"
            logger.error(error_msg)
            raise ParameterParsingError(error_msg) from e

    def _parse_with_rules(self, range_str: str) -> List[Any]:
        """使用规则解析参数范围"""
        logger.debug(f"使用规则解析: '{range_str}'")

        # 尝试不同的解析策略
        parsers = [
            self._parse_range_format,
            self._parse_comma_separated,
            self._parse_single_value
        ]

        for parser in parsers:
            try:
                result = parser(range_str)
                if result is not None:
                    logger.debug(f"规则解析成功: '{range_str}' -> {result}")
                    return result
            except Exception as e:
                logger.debug(f"解析器 {parser.__name__} 失败: {e}")
                continue

        # 如果所有解析器都失败，返回原字符串
        logger.warning(f"所有规则解析器都失败，返回原字符串: '{range_str}'")
        return [range_str]

    def _parse_range_format(self, range_str: str) -> Optional[List[Any]]:
        """
        解析范围格式 "0~8"

        Args:
            range_str: 范围字符串

        Returns:
            解析后的数值列表，解析失败时返回None
        """
        if ModuleTestConstants.RANGE_SEPARATOR not in range_str:
            return None

        try:
            parts = range_str.split(ModuleTestConstants.RANGE_SEPARATOR)
            if len(parts) != 2:
                return None

            start, end = int(parts[0].strip()), int(parts[1].strip())
            range_size = end - start + 1

            if range_size <= ModuleTestConstants.SMALL_RANGE_THRESHOLD:
                # 小范围：返回所有值
                return list(range(start, end + 1))
            else:
                # 大范围：选择代表性值
                return self._select_representative_values(start, end)

        except (ValueError, IndexError) as e:
            logger.debug(f"范围格式解析失败: {e}")
            return None

    def _parse_comma_separated(self, range_str: str) -> Optional[List[Any]]:
        """
        解析逗号分隔格式 "0, 1" 或 "5, 6, 7, 8"

        Args:
            range_str: 逗号分隔的字符串

        Returns:
            解析后的值列表，解析失败时返回None
        """
        if ModuleTestConstants.VALUE_SEPARATOR not in range_str:
            return None

        try:
            values = []
            for val in range_str.split(ModuleTestConstants.VALUE_SEPARATOR):
                val = val.strip()
                if val.isdigit():
                    values.append(int(val))
                else:
                    values.append(val)
            return values

        except Exception as e:
            logger.debug(f"逗号分隔格式解析失败: {e}")
            return None

    def _parse_single_value(self, range_str: str) -> List[Any]:
        """
        解析单个值

        Args:
            range_str: 单个值字符串

        Returns:
            包含单个值的列表
        """
        try:
            if range_str.isdigit():
                return [int(range_str)]
            else:
                return [range_str]
        except Exception:
            return [range_str]

    def _select_representative_values(self, start: int, end: int) -> List[int]:
        """
        为大范围选择代表性值

        Args:
            start: 起始值
            end: 结束值

        Returns:
            代表性值列表
        """
        values = [start, start + 1]
        mid = (start + end) // 2

        if mid not in values:
            values.append(mid)
        if end - 1 not in values:
            values.append(end - 1)
        if end not in values:
            values.append(end)

        return sorted(list(set(values)))

    def _create_llm_prompt_template(self) -> ChatPromptTemplate:
        """创建大模型提示模板"""
        return ChatPromptTemplate(
            [
                ("system", """
# 参数取值范围解析任务

## 背景
在软件测试中，需要根据参数的取值范围生成测试用例。为了确保测试的全面性和效率，需要从参数范围中选择具有代表性的值来构造笛卡尔积测试用例。

## 目标
根据给定的参数取值范围字符串，提取出适合用于构造笛卡尔积测试用例的具体数值或字符串列表。

## 解析规则
1. **范围格式（如 "0~8"）**：
   - 小范围（≤10个值）：返回所有值，如 "0~3" → [0, 1, 2, 3]
   - 大范围（>10个值）：选择代表性值，包括起始值、起始值+1、中间值、结束值-1、结束值
   - 例如："0~100" → [0, 1, 50, 99, 100]

2. **逗号分隔格式（如 "0, 1" 或 "5, 6, 7, 8"）**：
   - 直接返回所有指定的值
   - 例如："0, 1" → [0, 1]，"5, 6, 7, 8" → [5, 6, 7, 8]

3. **单个值（如 "10"）**：
   - 返回该单个值
   - 例如："10" → [10]

4. **字符串值**：
   - 保持原样返回
   - 例如："abc" → ["abc"]

## 要求
- 返回的值列表应该能够有效覆盖参数的取值空间
- 对于数值范围，优先选择边界值和中间值
- 确保返回的值列表不为空
- 如果是数值，返回整数类型；如果是字符串，保持字符串类型

## 示例
输入："0~8" → 输出：[0, 1, 2, 3, 4, 5, 6, 7, 8]
输入："0~100" → 输出：[0, 1, 50, 99, 100]
输入："0, 1" → 输出：[0, 1]
输入："5, 6, 7, 8" → 输出：[5, 6, 7, 8]
输入："10" → 输出：[10]
                """),
                ('user', '请解析以下参数取值范围字符串：{{range_str}}')
            ],
            template_format="mustache"
        )


class TestCaseGenerator:
    """
    测试用例生成器

    基于参数范围生成笛卡尔积测试用例，支持多种参数格式和解析方式。
    """

    def __init__(self, arg_file: str, result_file: Optional[str] = None):
        """
        初始化测试用例生成器

        Args:
            arg_file: 参数模板文件路径
            result_file: 结果文件路径，如果为None则使用默认路径
        """
        self.arg_file = arg_file
        self.result_file = result_file or ModuleTestConstants.DEFAULT_OUTPUT_FILE
        self.modules_data: Dict[str, ModuleInfo] = {}
        self.parser = ParameterRangeParser()

    def read_arg_file(self) -> Optional[pd.DataFrame]:
        """
        读取参数模板文件

        Returns:
            包含参数信息的DataFrame，读取失败时返回None
        """
        try:
            logger.info(f"开始读取参数文件: {self.arg_file}")
            df = pd.read_excel(self.arg_file, sheet_name=0)

            logger.info(f"成功读取文件，共{len(df)}行数据")
            logger.debug(f"文件列名: {df.columns.tolist()}")

            # 验证必要的列是否存在
            required_columns = [
                ModuleTestConstants.MODULE_NAME_COLUMN,
                ModuleTestConstants.PARAM_TYPE_COLUMN,
                ModuleTestConstants.PARAM_NAME_COLUMN,
                ModuleTestConstants.PARAM_RANGE_COLUMN
            ]

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"文件缺少必要的列: {missing_columns}")

            return df

        except FileNotFoundError:
            logger.error(f"参数文件不存在: {self.arg_file}")
            return None
        except Exception as e:
            logger.error(f"读取参数文件失败: {e}")
            return None

    def parse_range_value(self, range_str: str, use_llm: bool = True) -> List[Any]:
        """
        解析参数范围字符串（兼容性方法）

        Args:
            range_str: 参数范围字符串
            use_llm: 是否使用大模型进行解析，默认为True

        Returns:
            解析后的参数值列表
        """
        # 更新解析器配置
        self.parser.use_llm = use_llm
        return self.parser.parse_range_value(range_str)



    def parse_modules_data(self, df: pd.DataFrame, use_llm: bool = True) -> Dict[str, ModuleInfo]:
        """
        解析模块数据，按模块分组参数

        Args:
            df: 包含模块参数信息的DataFrame
            use_llm: 是否使用大模型解析参数范围，默认为True

        Returns:
            解析后的模块信息字典，键为模块名，值为ModuleInfo对象
        """
        modules: Dict[str, ModuleInfo] = {}

        logger.info(f"开始解析模块数据，共{len(df)}行记录")

        for index, row in df.iterrows():
            try:
                module_info = self._parse_single_row(row, use_llm)
                if module_info:
                    module_name, param_type, param_name, param_values = module_info

                    # 确保模块存在
                    if module_name not in modules:
                        modules[module_name] = ModuleInfo(
                            name=module_name,
                            inputs={},
                            outputs={}
                        )

                    # 添加参数到对应的类型
                    if param_type == ParameterType.INPUT:
                        modules[module_name].inputs[param_name] = param_values
                    elif param_type == ParameterType.OUTPUT:
                        modules[module_name].outputs[param_name] = param_values

            except Exception as e:
                logger.error(f"解析第{index + 1}行数据失败: {e}")
                continue

        # 记录解析结果统计
        self._log_parsing_summary(modules)

        return modules

    def _parse_single_row(self, row: pd.Series, use_llm: bool) -> Optional[Tuple[str, ParameterType, str, List[Any]]]:
        """
        解析单行数据

        Args:
            row: DataFrame的一行数据
            use_llm: 是否使用大模型解析

        Returns:
            解析结果元组：(模块名, 参数类型, 参数名, 参数值列表)，解析失败时返回None
        """
        # 提取基本信息
        module_name = self._get_cell_value(row, ModuleTestConstants.MODULE_NAME_COLUMN)
        param_type_str = self._get_cell_value(row, ModuleTestConstants.PARAM_TYPE_COLUMN)
        param_name = self._get_cell_value(row, ModuleTestConstants.PARAM_NAME_COLUMN)
        param_range = self._get_cell_value(row, ModuleTestConstants.PARAM_RANGE_COLUMN)

        # 验证必要字段
        if not module_name or not param_name:
            logger.debug(f"跳过无效行：模块名='{module_name}', 参数名='{param_name}'")
            return None

        # 转换参数类型
        param_type = self._convert_param_type(param_type_str)
        if param_type is None:
            logger.warning(f"未知的参数类型: '{param_type_str}'，跳过参数 {module_name}.{param_name}")
            return None

        # 解析参数范围
        try:
            param_values = self.parse_range_value(param_range, use_llm)
            logger.debug(f"解析参数 {module_name}.{param_name}: '{param_range}' -> {param_values}")
            return module_name, param_type, param_name, param_values

        except Exception as e:
            logger.error(f"解析参数范围失败 {module_name}.{param_name}: {e}")
            return module_name, param_type, param_name, []

    def _get_cell_value(self, row: pd.Series, column: str) -> str:
        """安全获取单元格值"""
        value = row.get(column, '')
        return str(value).strip() if not pd.isna(value) else ''

    def _convert_param_type(self, param_type_str: str) -> Optional[ParameterType]:
        """转换参数类型字符串为枚举"""
        if param_type_str == ModuleTestConstants.INPUT_PARAM_TYPE:
            return ParameterType.INPUT
        elif param_type_str == ModuleTestConstants.OUTPUT_PARAM_TYPE:
            return ParameterType.OUTPUT
        else:
            return None

    def _log_parsing_summary(self, modules: Dict[str, ModuleInfo]) -> None:
        """记录解析结果摘要"""
        logger.info(f"模块数据解析完成，共解析{len(modules)}个模块:")

        for module_name, module_info in modules.items():
            logger.info(f"  模块 {module_name}: "
                       f"{len(module_info.inputs)}个输入参数, "
                       f"{len(module_info.outputs)}个输出参数")
            logger.debug(f"    输入参数: {list(module_info.inputs.keys())}")
            logger.debug(f"    输出参数: {list(module_info.outputs.keys())}")

    def generate_cartesian_product(self, input_params: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """
        生成输入参数的笛卡尔积

        Args:
            input_params: 输入参数字典，键为参数名，值为参数值列表

        Returns:
            笛卡尔积组合列表，每个元素是一个参数组合字典
        """
        if not input_params:
            logger.debug("输入参数为空，返回空列表")
            return []

        param_names = list(input_params.keys())
        param_values = list(input_params.values())

        logger.debug(f"生成笛卡尔积，参数: {param_names}")

        # 生成笛卡尔积
        combinations = list(itertools.product(*param_values))
        logger.debug(f"生成了 {len(combinations)} 个参数组合")

        # 转换为字典格式
        test_cases = []
        for i, combination in enumerate(combinations):
            test_case = {
                "case_id": i + 1,
                "inputs": dict(zip(param_names, combination))
            }
            test_cases.append(test_case)

        return test_cases

    def generate_test_cases_for_module(self, module_info: ModuleInfo) -> List[TestCase]:
        """
        为单个模块生成测试用例

        Args:
            module_info: 模块信息对象

        Returns:
            测试用例列表
        """
        logger.debug(f"为模块 {module_info.name} 生成测试用例")

        # 生成输入参数的笛卡尔积
        input_combinations = self.generate_cartesian_product(module_info.inputs)

        # 转换为TestCase对象
        test_cases = []
        for combination in input_combinations:
            test_case = TestCase(
                case_id=combination["case_id"],
                module=module_info.name,
                inputs=combination["inputs"],
                expected_outputs=module_info.outputs
            )
            test_cases.append(test_case)

        logger.debug(f"为模块 {module_info.name} 生成了 {len(test_cases)} 个测试用例")
        return test_cases

    def generate_all_test_cases(self, use_llm: bool = True) -> Tuple[Dict[str, ModuleInfo], Dict[str, List[TestCase]]]:
        """
        为所有模块生成测试用例

        Args:
            use_llm: 是否使用大模型解析参数范围，默认为True

        Returns:
            元组：(模块信息字典, 测试用例字典)
        """
        logger.info("开始生成所有模块的测试用例")

        # 读取参数文件
        df = self.read_arg_file()
        if df is None:
            logger.error("无法读取参数文件，返回空结果")
            return {}, {}

        # 解析模块数据
        modules_data = self.parse_modules_data(df, use_llm=use_llm)
        if not modules_data:
            logger.warning("没有解析到任何模块数据")
            return {}, {}

        # 为每个模块生成测试用例
        all_test_cases: Dict[str, List[TestCase]] = {}

        for module_name, module_info in modules_data.items():
            logger.info(f"处理模块: {module_name}")
            logger.debug(f"输入参数: {list(module_info.inputs.keys())}")
            logger.debug(f"输出参数: {list(module_info.outputs.keys())}")

            try:
                test_cases = self.generate_test_cases_for_module(module_info)
                all_test_cases[module_name] = test_cases

                logger.info(f"模块 {module_name} 生成测试用例数量: {len(test_cases)}")
                if test_cases:
                    logger.debug(f"示例用例: case_id={test_cases[0].case_id}, inputs={test_cases[0].inputs}")

            except Exception as e:
                logger.error(f"为模块 {module_name} 生成测试用例失败: {e}")
                all_test_cases[module_name] = []

        logger.info(f"测试用例生成完成，共处理 {len(modules_data)} 个模块")
        return modules_data, all_test_cases

    def save_test_cases_to_excel(
            self,
            modules_data: Dict[str, ModuleInfo],
            all_test_cases: Dict[str, List[TestCase]],
            output_file: Optional[str] = None
    ):
        """
        将测试用例保存到Excel文件

        Args:
            modules_data: 模块信息字典
            all_test_cases: 测试用例字典
            output_file: 输出文件路径，如果为None则使用默认路径
        """
        # 确定输出文件路径
        output_file = output_file or self.result_file or ModuleTestConstants.DEFAULT_OUTPUT_FILE
        logger.info(f"开始将测试用例保存到Excel文件: {output_file}")

        # 使用ExcelUtil处理Excel文件
        try:
            with ExcelUtil(output_file) as excel:
                # 处理每个模块的测试用例
                for module_name, test_cases in all_test_cases.items():
                    if not test_cases:
                        logger.info(f"模块 {module_name} 没有测试用例，跳过")
                        continue

                    try:
                        # 获取模块信息
                        module_info = modules_data[module_name]
                        logger.info(f"处理模块 {module_name} 的测试用例，共 {len(test_cases)} 个")

                        # 准备工作表
                        sheet_name = self._prepare_sheet(excel, module_name, len(test_cases))

                        # 处理工作表内容
                        with excel.worksheet(sheet_name) as worksheet:
                            # 获取模板关键位置
                            template_positions = self._get_template_positions(worksheet, excel, output_file)

                            # 调整工作表结构以适应测试用例数量
                            adjusted_positions = self._adjust_worksheet_structure(
                                worksheet,
                                template_positions,
                                module_info.input_count,
                                module_info.output_count,
                                len(test_cases)
                            )

                            # 写入参数信息
                            self._write_parameters(worksheet, module_info, adjusted_positions)

                            # 写入测试用例
                            self._write_test_cases(worksheet, module_info, test_cases, adjusted_positions)

                            # 保存更改
                            excel.save()

                        logger.info(f"模块 {module_name} 处理完成")

                    except Exception as e:
                        error_msg = f"处理模块 {module_name} 时发生错误: {str(e)}"
                        logger.error(error_msg)
                        raise ExcelProcessingError(error_msg) from e

        except Exception as e:
            error_msg = f"Excel文件处理失败: {str(e)}"
            logger.error(error_msg)
            raise ExcelProcessingError(error_msg) from e

        logger.info(f"测试用例已成功保存到: {output_file}")

    def _prepare_sheet(self, excel: ExcelUtil, module_name: str, case_count: int) -> str:
        """
        准备工作表

        Args:
            excel: Excel工具实例
            module_name: 模块名称
            case_count: 测试用例数量

        Returns:
            工作表名称
        """
        # 截断工作表名称以符合Excel限制
        sheet_name = module_name[:ModuleTestConstants.MAX_SHEET_NAME_LENGTH]
        logger.debug(f"为模块 {module_name} 创建工作表 {sheet_name}")

        # 获取模板工作表
        template_sheet_name = excel.get_sheet_name_fuzzy(ModuleTestConstants.TEMPLATE_CASE_SHEET_KEYWORD)

        # 如果已存在同名工作表，先删除
        if sheet_name in excel.get_sheet_names():
            logger.debug(f"删除已存在的工作表: {sheet_name}")
            excel.delete_sheet(sheet_name)

        # 复制模板工作表
        logger.debug(f"从模板 {template_sheet_name} 复制工作表到 {sheet_name}")
        excel.copy_sheet(template_sheet_name, sheet_name)

        return sheet_name

    def _get_template_positions(self, worksheet, excel: ExcelUtil, output_file: str) -> Dict[str, int]:
        """
        获取模板中的关键位置

        Args:
            worksheet: 工作表对象
            excel: Excel工具实例
            output_file: Excel文件路径

        Returns:
            包含关键位置的字典
        """
        # 设置测试用例起始位置
        case_start_row_index = ModuleTestConstants.CASE_START_ROW
        case_start_col_index = ModuleTestConstants.CASE_START_COLUMN

        # 获取模板列数
        template_sheet_name = excel.get_sheet_name_fuzzy(ModuleTestConstants.TEMPLATE_CASE_SHEET_KEYWORD)
        col_count = len(pd.read_excel(output_file, sheet_name=template_sheet_name).columns)

        # 读取第一列以查找关键行
        column_values = worksheet.read_range('A1:A100')
        column_values = [i[0] for i in column_values if i and len(i) > 0]
        non_empty_values = [i for i in column_values if i]

        # 查找关键行位置
        input_args_value = select_one(ModuleTestConstants.INPUT_ARGS_KEYWORD, non_empty_values)
        input_args_row_index = column_values.index(input_args_value) + 1
        logger.debug(f'模板入参行位置：{input_args_row_index}')

        output_args_value = select_one(ModuleTestConstants.OUTPUT_ARGS_KEYWORD, non_empty_values)
        output_args_row_index = column_values.index(output_args_value) + 1
        logger.debug(f'模板出参行位置：{output_args_row_index}')

        end_flag_value = select_one(ModuleTestConstants.CATEGORY_KEYWORD, non_empty_values)
        end_flag_row_index = column_values.index(end_flag_value) + 1
        logger.debug(f'模板分类行位置：{end_flag_row_index}')

        # 返回位置信息
        return {
            'case_start_row': case_start_row_index,
            'case_start_col': case_start_col_index,
            'input_args_row': input_args_row_index,
            'output_args_row': output_args_row_index,
            'end_flag_row': end_flag_row_index,
            'col_count': col_count,
            'current_input_count': output_args_row_index - input_args_row_index,
            'current_output_count': end_flag_row_index - output_args_row_index
        }

    def _adjust_worksheet_structure(
            self,
            worksheet,
            positions: Dict[str, int],
            input_count: int,
            output_count: int,
            case_count: int
    ) -> Dict[str, int]:
        """
        调整工作表结构以适应测试用例

        Args:
            worksheet: 工作表对象
            positions: 关键位置字典
            input_count: 输入参数数量
            output_count: 输出参数数量
            case_count: 测试用例数量

        Returns:
            调整后的位置字典
        """
        # 复制位置字典以避免修改原始数据
        adjusted = positions.copy()

        # 调整列数以适应测试用例数量
        current_case_count = positions['col_count'] - positions['case_start_col'] + 1
        if current_case_count < case_count:
            logger.debug(f"插入列以适应测试用例数量: 从 {current_case_count} 到 {case_count}")
            worksheet.insert_columns(
                positions['case_start_col'] + 1,
                case_count - current_case_count
            )
            current_case_count = case_count

        # 写入测试用例编号
        worksheet.write_range(
            positions['case_start_row'],
            positions['case_start_col'],
            [list(range(current_case_count))]
        )

        # 调整行数以适应参数数量
        if output_count > positions['current_output_count']:
            logger.debug(f"插入行以适应输出参数: 从 {positions['current_output_count']} 到 {output_count}")
            worksheet.insert_rows(
                positions['output_args_row'] + 2,
                output_count - positions['current_output_count']
            )
            adjusted['current_output_count'] = output_count

        if input_count > positions['current_input_count']:
            logger.debug(f"插入行以适应输入参数: 从 {positions['current_input_count']} 到 {input_count}")
            worksheet.insert_rows(
                positions['input_args_row'] + 2,
                input_count - positions['current_input_count']
            )
            adjusted['current_input_count'] = input_count

        # 更新行位置
        adjusted['output_args_row'] = positions['input_args_row'] + adjusted['current_input_count']
        adjusted['end_flag_row'] = adjusted['output_args_row'] + adjusted['current_output_count']

        logger.debug(f'调整后入参行位置：{adjusted["input_args_row"]}')
        logger.debug(f'调整后出参行位置：{adjusted["output_args_row"]}')
        logger.debug(f'调整后分类行位置：{adjusted["end_flag_row"]}')

        # 写入分类标记
        worksheet.write_range(
            adjusted['end_flag_row'],
            'P',
            [['N' for _ in range(case_count)]]
        )

        return adjusted

    def _write_parameters(self, worksheet, module_info: ModuleInfo, positions: Dict[str, int]) -> None:
        """
        写入参数信息到工作表

        Args:
            worksheet: 工作表对象
            module_info: 模块信息
            positions: 位置信息字典
        """
        logger.debug(f"开始写入模块 {module_info.name} 的参数信息")

        # 写入输入参数
        current_row = positions['input_args_row']
        for param_name, param_values in module_info.inputs.items():
            for param_value in param_values:
                worksheet.write_cell(current_row, 'B', param_name)
                worksheet.write_cell(current_row, 'I', param_value)
                current_row += 1

        logger.debug(f"写入了 {module_info.input_count} 个输入参数")

        # 写入输出参数
        current_row = positions['output_args_row']
        for param_name, param_values in module_info.outputs.items():
            for param_value in param_values:
                worksheet.write_cell(current_row, 'B', param_name)
                worksheet.write_cell(current_row, 'I', param_value)
                current_row += 1

        logger.debug(f"写入了 {module_info.output_count} 个输出参数")

    def _write_test_cases(
            self,
            worksheet,
            module_info: ModuleInfo,
            test_cases: List[TestCase],
            positions: Dict[str, int]
    ) -> None:
        """
        写入测试用例到工作表

        Args:
            worksheet: 工作表对象
            module_info: 模块信息
            test_cases: 测试用例列表
            positions: 位置信息字典
        """
        logger.debug(f"开始写入 {len(test_cases)} 个测试用例")

        # 构建参数索引映射
        input_index_map = self._build_parameter_index_map(module_info.inputs)
        output_index_map = self._build_parameter_index_map(module_info.outputs)

        # 写入每个测试用例
        current_col = positions['case_start_col']

        try:
            for test_case in test_cases:
                self._write_single_test_case(
                    worksheet,
                    test_case,
                    current_col,
                    positions,
                    input_index_map,
                    output_index_map,
                    module_info
                )
                current_col += 1

            logger.debug(f"成功写入所有测试用例")

        except Exception as e:
            error_msg = f"写入测试用例时发生错误: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def _build_parameter_index_map(self, parameters: Dict[str, List[Any]]) -> Dict[str, int]:
        """
        构建参数索引映射

        Args:
            parameters: 参数字典

        Returns:
            参数名到起始索引的映射
        """
        index_map = {}
        current_index = 0

        for param_name, param_values in parameters.items():
            index_map[param_name] = current_index
            current_index += len(param_values)

        return index_map

    def _write_single_test_case(
            self,
            worksheet,
            test_case: TestCase,
            col_index: int,
            positions: Dict[str, int],
            input_index_map: Dict[str, int],
            output_index_map: Dict[str, int],
            module_info: ModuleInfo
    ) -> None:
        """
        写入单个测试用例

        Args:
            worksheet: 工作表对象
            test_case: 测试用例
            col_index: 列索引
            positions: 位置信息
            input_index_map: 输入参数索引映射
            output_index_map: 输出参数索引映射
            module_info: 模块信息
        """
        # 写入输入参数标记
        for param_name, param_value in test_case.inputs.items():
            if param_name in input_index_map and param_name in module_info.inputs:
                try:
                    value_index = module_info.inputs[param_name].index(param_value)
                    row_index = positions['input_args_row'] + input_index_map[param_name] + value_index
                    worksheet.write_cell(row_index, col_index, '○')
                except ValueError:
                    logger.warning(f"参数值 {param_value} 不在参数 {param_name} 的值列表中")

        # 写入输出参数标记
        for param_name, param_values in test_case.expected_outputs.items():
            if param_name in output_index_map and param_name in module_info.outputs:
                for param_value in param_values:
                    try:
                        value_index = module_info.outputs[param_name].index(param_value)
                        row_index = positions['output_args_row'] + output_index_map[param_name] + value_index
                        worksheet.write_cell(row_index, col_index, '○')
                    except ValueError:
                        logger.warning(f"输出参数值 {param_value} 不在参数 {param_name} 的值列表中")

    def print_test_cases_summary(self, all_test_cases: Dict[str, List[TestCase]]) -> None:
        """
        打印测试用例生成摘要

        Args:
            all_test_cases: 所有模块的测试用例字典
        """
        logger.info("=== 测试用例生成摘要 ===")
        total_cases = 0

        for module_name, test_cases in all_test_cases.items():
            case_count = len(test_cases)
            total_cases += case_count
            logger.info(f"模块 {module_name}: {case_count} 个测试用例")

            if test_cases:
                # 显示前几个用例作为示例
                logger.info("  示例用例:")
                for case in test_cases[:3]:
                    inputs_str = ", ".join([f"{k}={v}" for k, v in case.inputs.items()])
                    logger.info(f"    用例{case.case_id}: ({inputs_str})")
                if len(test_cases) > 3:
                    logger.info(f"    ... 还有 {len(test_cases) - 3} 个用例")

        logger.info(f"总计: {total_cases} 个测试用例")
        logger.info("=== 摘要结束 ===")


class ModuleDomainService:
    """
    模块域测试服务类

    提供模块和域单体测试的主要服务接口，协调测试用例生成和Excel处理流程。
    """

    def __init__(self, arg_file: str, result_file: Optional[str] = None):
        """
        初始化模块域测试服务

        Args:
            arg_file: 参数模板文件路径
            result_file: 结果文件路径，如果为None则使用默认路径
        """
        logger.info(f"初始化模块域测试服务")
        logger.info(f"参数文件: {arg_file}")
        logger.info(f"结果文件: {result_file or '(使用默认)'}")

        self.arg_file = arg_file
        self.result_file = result_file
        self.generator = TestCaseGenerator(arg_file, result_file)

    def read_arg_file(self) -> Optional[pd.DataFrame]:
        """
        读取参数模板文件

        Returns:
            包含参数信息的DataFrame，读取失败时返回None
        """
        logger.info(f"读取参数文件: {self.arg_file}")
        return self.generator.read_arg_file()

    def generate_test_cases(self, use_llm: bool = True) -> bool:
        """
        生成测试用例的主入口

        处理流程:
        1. 解析参数文件
        2. 生成测试用例
        3. 保存到Excel文件

        Args:
            use_llm: 是否使用大模型解析参数范围，默认为True

        Returns:
            处理成功返回True，失败返回False

        Raises:
            ModuleTestError: 当处理过程中发生错误时抛出
        """
        logger.info(f"开始生成测试用例，使用大模型解析: {use_llm}")

        try:
            # 生成所有测试用例
            modules_data, all_test_cases = self.generator.generate_all_test_cases(use_llm=use_llm)

            # 检查是否有测试用例生成
            total_cases = sum(len(cases) for cases in all_test_cases.values())
            if total_cases == 0:
                logger.warning("没有生成任何测试用例")
                return False

            # 打印摘要
            self.generator.print_test_cases_summary(all_test_cases)

            # 保存到Excel文件
            self.generator.save_test_cases_to_excel(modules_data, all_test_cases)

            logger.info("测试用例生成和保存成功")
            return True

        except ParameterParsingError as e:
            logger.error(f"参数解析错误: {e}")
            if use_llm:
                logger.info("尝试使用规则解析模式重新生成")
                return self.generate_test_cases(use_llm=False)
            raise

        except ExcelProcessingError as e:
            logger.error(f"Excel处理错误: {e}")
            raise

        except Exception as e:
            logger.error(f"生成测试用例过程中发生未预期错误: {e}", exc_info=True)
            if use_llm:
                logger.info("尝试使用规则解析模式重新生成")
                return self.generate_test_cases(use_llm=False)
            raise ModuleTestError(f"测试用例生成失败: {str(e)}") from e


if __name__ == '__main__':
    # 使用示例
    arg_file = r"C:\Users\<USER>\Desktop\workflows\单体式样书参数模板.xlsx"
    result_file = r"C:\Users\<USER>\Desktop\workflows\单体式样书参数模板.xlsx"
    # result_file = r"C:\Users\<USER>\Desktop\workflows\generated_test_cases.xlsx"

    generator = TestCaseGenerator(arg_file, result_file)

    logger.info("=== 测试用例生成器演示 ===")
    logger.info("支持两种模式：")
    logger.info("1. 大模型解析模式（推荐）")
    logger.info("2. 规则解析模式（备用）")

    # 演示大模型解析单个参数范围
    logger.info("=== 大模型解析演示 ===")
    test_ranges = ["0~8", "0~100", "0, 1", "5, 6, 7, 8"]
    for range_str in test_ranges:
        try:
            values_llm = generator.parse_range_value(range_str, use_llm=True)
            values_rule = generator.parse_range_value(range_str, use_llm=False)
            logger.info(f"'{range_str}':")
            logger.info(f"  大模型解析: {values_llm}")
            logger.info(f"  规则解析:   {values_rule}")
        except Exception as e:
            logger.error(f"解析 '{range_str}' 失败: {e}")

    # 生成所有测试用例（使用大模型）
    logger.info("=== 使用大模型生成测试用例 ===")
    try:
        modules_data, all_test_cases = generator.generate_all_test_cases(use_llm=True)

        # 打印摘要
        generator.print_test_cases_summary(all_test_cases)

        # 保存到Excel文件
        generator.save_test_cases_to_excel(modules_data, all_test_cases)

        logger.info("✅ 测试用例生成完成！")

    except Exception as e:
        logger.error(f"❌ 大模型生成失败: {e}")
        logger.warning("回退到规则解析模式...")

        # 回退到规则解析
        modules_data, all_test_cases = generator.generate_all_test_cases(use_llm=False)
        generator.print_test_cases_summary(all_test_cases)
        generator.save_test_cases_to_excel(modules_data, all_test_cases)
        logger.info("✅ 使用规则解析完成测试用例生成！")
