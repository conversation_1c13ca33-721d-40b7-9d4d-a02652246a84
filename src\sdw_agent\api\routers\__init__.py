"""
路由模块 - 自动导入和注册所有路由
"""
import os
import importlib
from typing import List
from fastapi import APIRouter
from loguru import logger


def auto_discover_routers() -> List[APIRouter]:
    """
    自动发现并导入所有路由模块

    Returns:
        自动发现的路由器列表
    """
    routers = []
    current_dir = os.path.dirname(__file__)

    # 遍历当前目录下的所有Python文件
    for filename in os.listdir(current_dir):
        if (filename.endswith('.py') and
            filename != '__init__.py' and
            not filename.startswith('_')):

            module_name = filename[:-3]  # 移除.py扩展名

            try:
                # 动态导入模块
                module = importlib.import_module(f'.{module_name}', package=__name__)

                # 检查模块是否有router属性
                if hasattr(module, 'router') and isinstance(module.router, APIRouter):
                    routers.append(module.router)
                    logger.info(f"✓ 自动发现路由: {module_name}")

            except Exception as e:
                logger.exception(f"导入路由模块 {module_name} 失败: {e}")

    return routers


# 自动发现所有路由
ALL_ROUTERS = auto_discover_routers()

def get_all_routers() -> List[APIRouter]:
    """
    获取所有路由器

    Returns:
        所有路由器的列表
    """
    return ALL_ROUTERS

# 动态导出所有发现的路由
__all__ = ["get_all_routers", "auto_discover_routers", "ALL_ROUTERS"]
