"""
CSTM 配置工具工作流

V字对应：
2.1 基本設計 ソフトウェア設計書作成（I/F含む） cstm 配置工具自动化生成

模块简介和主要功能说明：
用户从要件一览表中指定待分析的变更点，自动匹配相关SCL文件中与变更点相关的内容，解析变更前后CSTM原始式样书
从式样书中定位到与变更点相关的选项信息，通过对比变更前后式样书内容，得到CSTM 变更类型，根据变更内容自动更新CSTM配置工具文件
（目前支持：选项属性变更，选项位置变更，选项新增，选项删除，画面追加，画面消除场景）

主要功能：
1. 根据用户所选变更点从SCL文件中匹配出相关变更内容
2. 解析CSTM 原始式样书输出结构化文件
3. 对比分析变更前后原始式样书，分析变更类型
4. 自动化更新CSTM 配置工具表，并生成更新后的代码
"""
import os
import time
from typing import Optional, Union, Dict, Any

from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.service.cstm_tool.model import ConfigModel, InputDataModel
from sdw_agent.service.cstm_tool.utils.cstm_extract_table import analyze_requirement_changes
from sdw_agent.service.cstm_tool.utils.cstm_tool import sanitize_filename, read_excel_input_cst_data, get_change_result, \
    make_excel_update
from sdw_agent.service.cstm_tool.utils.cstm_excel_util import CSTMExcelUtil
from sdw_agent.service.cstm_tool.utils.generate_cstm_change_tree import generate_cstm_json_tree
from sdw_agent.service.workflow_config import WorkflowConfigManager


@register_workflow("cstm_tool")
class CstmToolWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化CSTM Tool 工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)

        # 注册配置模型
        self.register_config_model()

    def register_config_model(self):
        """注册配置模型用于验证"""
        try:
            config_manager = WorkflowConfigManager(workflow_name="cstm_tool")
            config_manager.register_schema("workflow_config", ConfigModel)
            self.logger.info("配置模型注册成功")
        except Exception as e:
            self.logger.warning(f"配置模型注册失败: {e}")

    def validate_input(self, input_data: Union[Dict[str, Any], InputDataModel]) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据，可以是字典或InputDataModel实例

        Returns:
            bool: 验证是否通过
        """
        try:
            # 如果是字典，转换为模型进行验证
            if isinstance(input_data, dict):
                validated_data = InputDataModel(**input_data)
            elif isinstance(input_data, InputDataModel):
                validated_data = input_data
            else:
                self.logger.error("输入数据类型不正确")
                return False

            # 验证文件是否存在
            if validated_data.scl_path:
                if not validated_data.scl_path or not os.path.exists(validated_data.scl_path):
                    self.logger.error(f"SCL文件不存在: {validated_data.scl_path}")
                    return False

            if validated_data.before_cstm:
                if not validated_data.before_cstm or not os.path.exists(validated_data.before_cstm):
                    self.logger.error(f"变更前原始式样书路径不存在: {validated_data.before_cstm}")
                    return False

            if validated_data.after_cstm:
                if not validated_data.after_cstm or not os.path.exists(validated_data.after_cstm):
                    self.logger.error(f"变更后原始式样书路径不存在: {validated_data.after_cstm}")
                    return False

            if validated_data.base_cstm:
                if not validated_data.base_cstm or not os.path.exists(validated_data.base_cstm):
                    self.logger.error(f"变更前cstm tool文件路径不存在: {validated_data.base_cstm}")
                    return False

            # 验证change_summary参数
            if not validated_data.change_summary:
                self.logger.error("change_summary参数无效")
                return False

            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入参数验证失败: {e}")
            return False

    def execute(self, input_data: Union[Dict[str, Any], InputDataModel]) -> WorkflowResult:
        """
        执行工作流核心逻辑

        Args:
            input_data: 输入数据

        Returns:
            WorkflowResult: 工作流执行结果
        """
        start_time = time.time()

        try:
            # 转换输入数据
            if isinstance(input_data, dict):
                validated_input = InputDataModel(**input_data)
            else:
                validated_input = input_data

            self.logger.info(f"开始执行CSTM Tool工作流")

            # 解析变更前后原始式样书得到变更前后json 结构的目录树
            before_cstm_json_path, after_cstm_json_path, pair_tree = generate_cstm_json_tree(
                validated_input, self.config
            )

            # 解析出变更后原始式样书 所在文件夹路径
            after_cstm_dir = os.path.dirname(validated_input.after_cstm)
            self.logger.info(f"变更后式样书所在文件夹路径: {after_cstm_dir}")

            req_name = sanitize_filename(validated_input.change_summary)
            self.logger.info(f"需求变更点名称：{req_name}")

            # 对比变更前后需求文档，将层级变更转换成json形式
            # 初始化数据结构
            cus_opt_cntt_info_list = []
            # 创建base cstm 配置工具excel对象
            excel_util = CSTMExcelUtil(validated_input.base_cstm, sheet_name="InputData")
            read_excel_input_cst_data(excel_util.sheet, cus_opt_cntt_info_list)

            new_file = None
            for idx, pair in enumerate(pair_tree):
                _, cstm_data_file, objects_pairs = analyze_requirement_changes(
                    idx, pair, req_name, before_cstm_json_path, after_cstm_json_path, cus_opt_cntt_info_list,
                    target_folder=self.config.get('workflow_config', {}).get('output', {}).get('req_analyse')
                )

                cstm_end_flag = True if idx == len(pair_tree) - 1 else False

                # 得到变更的模块list
                need_change_opt_info_list = get_change_result(cstm_data_file, objects_pairs, after_cstm_dir,
                                                              cus_opt_cntt_info_list, validated_input.base_cstm)

                if cstm_end_flag:
                    new_file, excel_util = make_excel_update(need_change_opt_info_list, excel_util, cstm_end_flag)
                    break
                else:
                    _, excel_util = make_excel_update(need_change_opt_info_list, excel_util, cstm_end_flag)
                    # 从excel_util内存中加载新的cus_opt_cntt_info_list 用于下一次操作
                    cus_opt_cntt_info_list = []
                    read_excel_input_cst_data(excel_util.sheet, cus_opt_cntt_info_list)


            # 计算处理时间
            processing_time = time.time() - start_time

            self.logger.info(f"工作流执行完成，耗时: {processing_time:.2f}秒")

            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="通信故障安全CS工作流执行成功",
                data= {
                    'cstm_path': new_file.replace('/', '\\') if new_file else ''
                }
            )

        except Exception as e:
            self.logger.exception(f"工作流执行失败: {e}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"工作流执行失败: {str(e)}",
                error=str(e)
            )






