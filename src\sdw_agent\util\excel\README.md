# Excel工具类 (ExcelUtil)

这是一个通用的Excel读写工具类，提供了丰富的Excel操作功能，设计为便于继承和扩展。

## 特性

- 🔧 **多引擎支持**: 支持win32com、openpyxl和pandas三种操作引擎
- 🛡️ **宏和图片保护**: 默认使用win32com引擎，完美保护Excel文件中的宏和图片
- 📊 **丰富的操作**: 单元格读写、区域操作、样式设置、工作表管理
- 🎨 **样式系统**: 完整的单元格样式支持，包含预定义常用样式
- 🔍 **智能匹配**: 支持工作表名称的模糊匹配
- 📈 **DataFrame集成**: 与pandas DataFrame无缝集成
- 🛡️ **异常处理**: 完善的错误处理和资源管理
- 🔄 **上下文管理**: 支持with语句自动资源清理
- 📝 **详细日志**: 完整的操作日志记录，便于调试和监控

## 快速开始

### 基本使用

```python
from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange, CommonStyles

# 创建Excel工具实例（默认使用win32com引擎，保护宏和图片）
with ExcelUtil("data.xlsx", auto_create=True) as excel:
    # 写入数据
    excel.write_cell("Sheet1", 1, 1, "姓名")
    excel.write_cell("Sheet1", 1, 2, "年龄")

    # 读取数据
    name = excel.read_cell("Sheet1", 1, 1)

    # 设置样式
    excel.set_cell_style("Sheet1", 1, 1, CommonStyles.HEADER)

    # 保存文件
    excel.save()
```

### 引擎选择

```python
# 使用win32com引擎（默认，推荐用于包含宏和图片的文件）
excel = ExcelUtil("data.xlsx", engine="win32com")

# 使用openpyxl引擎（纯Python实现，不依赖Excel程序）
excel = ExcelUtil("data.xlsx", engine="openpyxl")

# 使用pandas引擎（高效读取，不支持写入）
excel = ExcelUtil("data.xlsx", engine="pandas")
```

### 区域操作

```python
# 批量写入数据
data = [
    ["姓名", "年龄", "城市"],
    ["张三", 25, "北京"],
    ["李四", 30, "上海"]
]
excel.write_range("Sheet1", 1, 1, data)

# 读取区域数据
cell_range = CellRange(1, 1, 3, 3)  # A1:C3
data = excel.read_range("Sheet1", cell_range)

# 也可以使用Excel格式字符串
data = excel.read_range("Sheet1", "A1:C3")
```

### DataFrame操作

```python
import pandas as pd

# 创建DataFrame
df = pd.DataFrame({
    '产品': ['A', 'B', 'C'],
    '价格': [100, 200, 150]
})

# 写入DataFrame
excel.write_dataframe("Sheet1", df, start_row=1, include_header=True)

# 读取为DataFrame
read_df = excel.read_range_as_dataframe("Sheet1", "A1:B4", has_header=True)
```

### 样式设置

```python
# 使用预定义样式
excel.set_cell_style("Sheet1", 1, 1, CommonStyles.HEADER)
excel.set_cell_style("Sheet1", 2, 1, CommonStyles.DATA)
excel.set_cell_style("Sheet1", 2, 2, CommonStyles.NUMBER)

# 创建自定义样式
custom_style = CellStyle(
    font_name="Arial",
    font_size=14,
    font_bold=True,
    font_color="FFFFFF",
    bg_color="4472C4",
    alignment_horizontal="center",
    border_style="thick"
)
excel.set_cell_style("Sheet1", 1, 1, custom_style)

# 设置区域样式
excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
```

### 工作表管理

```python
# 获取所有工作表
sheets = excel.get_sheet_names()

# 创建新工作表
excel.create_sheet("数据表")

# 删除工作表
excel.delete_sheet("Sheet2")

# 复制工作表
excel.copy_sheet("Sheet1", "Sheet1_Copy")

# 模糊匹配工作表名称
sheet_name = excel.get_sheet_name_fuzzy("数据")  # 匹配"数据表"
```

### 高级功能

```python
# 合并单元格
excel.merge_cells("Sheet1", "A1:C1")

# 取消合并
excel.unmerge_cells("Sheet1", "A1:C1")

# 自动调整列宽
excel.auto_fit_columns("Sheet1")  # 调整所有列
excel.auto_fit_columns("Sheet1", ["A", "B"])  # 调整指定列

# 批量更新单元格
from sdw_agent.util.excel.core import batch_update_cells
updates = {
    "A1": "标题",
    "B1": "副标题",
    "A2": "数据1",
    "B2": "数据2"
}
batch_update_cells(excel, "Sheet1", updates)
```

## 类结构

### 核心类

- **ExcelUtil**: 主要的Excel操作类
- **CellStyle**: 单元格样式配置类
- **CellRange**: 单元格区域类
- **BaseExcelEngine**: Excel操作引擎基类
- **OpenpyxlEngine**: 基于openpyxl的引擎实现
- **PandasEngine**: 基于pandas的引擎实现

### 预定义样式

- **CommonStyles.HEADER**: 表头样式
- **CommonStyles.DATA**: 数据样式
- **CommonStyles.NUMBER**: 数字样式
- **CommonStyles.WARNING**: 警告样式
- **CommonStyles.SUCCESS**: 成功样式

## 引擎选择

### win32com引擎 (默认，推荐)
- ✅ **完美保护宏和图片**: 不会破坏Excel文件中的VBA宏和嵌入对象
- ✅ **Excel原生功能**: 支持Excel的所有原生功能和格式
- ✅ **完整兼容性**: 与现有Excel文件100%兼容
- ✅ 支持完整的读写操作
- ✅ 支持样式设置
- ✅ 支持工作表管理
- ✅ 支持合并单元格
- ✅ 支持自动列宽调整
- ⚠️ 需要安装Microsoft Excel和pywin32

### openpyxl引擎
- ✅ 纯Python实现，不依赖Excel程序
- ✅ 支持完整的读写操作
- ✅ 支持样式设置
- ✅ 支持工作表管理
- ✅ 支持合并单元格
- ✅ 支持列宽调整
- ❌ 可能破坏宏和复杂格式

### pandas引擎
- ✅ 高效的数据读取
- ✅ 与DataFrame无缝集成
- ❌ 不支持写入操作
- ❌ 不支持样式设置

## 继承扩展

ExcelUtil类设计为便于继承，你可以根据业务需求扩展功能：

```python
class BusinessExcelUtil(ExcelUtil):
    """业务专用Excel工具类"""
    
    def write_report_header(self, sheet_name: str):
        """写入报表表头"""
        headers = ["日期", "销售额", "成本", "利润"]
        for i, header in enumerate(headers, start=1):
            self.write_cell(sheet_name, 1, i, header)
            self.set_cell_style(sheet_name, 1, i, CommonStyles.HEADER)
    
    def calculate_profit_margin(self, sheet_name: str, sales_col: int, cost_col: int, result_col: int):
        """计算利润率"""
        # 实现业务逻辑
        pass
```

## 注意事项

1. **资源管理**: 建议使用with语句确保资源正确释放
2. **引擎选择**: 需要写入操作时使用openpyxl引擎
3. **大文件处理**: 对于大文件，pandas引擎读取性能更好
4. **样式兼容性**: 样式功能仅在openpyxl引擎下可用
5. **工作表名称**: 支持模糊匹配，但建议使用准确名称

## 依赖

- pandas
- openpyxl
- sdw_agent.util.select_util (用于模糊匹配)

## 测试

运行测试文件查看使用示例：

```bash
cd src/sdw_agent/util/excel
python test_excel_util.py
```
