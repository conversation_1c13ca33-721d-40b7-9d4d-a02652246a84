# RAM确认工作流

## V字对应
- 4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証

## 概述

RAM确认工作流专门处理RAM确认检查任务。通过分析代码中的全局变量变更，确认变量的初始化时机和位置，并生成详细的确认报告。

## 主要功能

### 核心功能：RAM确认检查
- **全局变量变更检测**: 根据Git差异分析全局变量的变更情况
- **初始化位置检索**: 查找全局变量的初始化位置
- **初始化时机确认**: 确认全局变量的初始化时机
- **报告生成**: 生成包含确认结果的Excel报告

### 分析流程
1. **检测变量变更**: 分析Git差异，找出变更的全局变量
2. **位置检索**: 查找变量的初始化位置和相关函数
3. **时机确认**: 分析变量的初始化时机
4. **生成报告**: 将确认结果整合到Excel模板中

## 使用方法

### 基本使用

```python
from sdw_agent.service.ram_confirm_service import RAMConfirmWorkflow
from sdw_agent.service.ram_confirm_service.models import RepositoryInfo

# 创建工作流实例
workflow = RAMConfirmWorkflow()

# 准备仓库信息
repo_info = RepositoryInfo(
    repo_path="/path/to/repository",
    commit_id="abc123def456",
    compared_commit_id="def456abc123"  # 可选
)

# 执行工作流
result = workflow.execute(repo_info)

if result.status == WorkflowStatus.SUCCESS:
    # 获取输出文件路径
    output_file = result.data['output_file']
    logger.info(f"RAM确认报告已生成: {output_file}")
    
    # 获取统计信息
    stats = result.data['statistics']
    logger.info(f"处理的全局变量数: {stats['total_global_vars']}")
    logger.info(f"确认记录数: {stats['confirm_records']}")
else:
    logger.error(f"执行失败: {result.error}")
```

### 使用兼容接口

```python
from sdw_agent.service.ram_confirm_service import do_ram_confirm

# 使用旧版兼容接口
try:
    output_file = do_ram_confirm(repo_info)
    logger.info(f"报告已生成: {output_file}")
except Exception as e:
    logger.error(f"执行失败: {str(e)}")
```

## 配置说明

### 配置文件结构
```yaml
# 基本配置
name: "RAM确认"
description: "根据Gerrit Diff文档，获取变更的全局变量并确认初始化时机"
version: "1.0.0"

# 输入输出配置
io:
  input:
    repo_extensions: [".c", ".h", ".prm"]
  output:
    report_base_name: "【Orca#2】RAM確認表"
    template_file: "templates/RAM確認表_模板.xlsx"

# 处理参数
processing:
  excel_output:
    default_sheet: " ｲﾍﾞﾝﾄ(DEFAULT)"
    data_start_row: 21
    data_start_col: 2
```

## 输出格式

### Excel报告结构
生成的Excel文件包含以下列：
- **全局变量名**: 检测到的全局变量名称
- **初始化时机**: 变量的初始化时机
- **初始化位置**: 变量的初始化位置
- **文件路径**: 变量所在的文件路径
- **变更函数**: 修改该变量的函数
- **行号**: 变更发生的行号
- **代码内容**: 具体的代码内容
- **备注**: 额外说明信息
- **结果**: 确认结果

### 返回数据结构
```python
{
    "output_file": "/path/to/RAM確認表_20240101_120000.xlsx",
    "ram_confirm_data": [...],  # 原始数据
    "statistics": {
        "total_global_vars": 10,
        "confirm_records": 10
    },
    "global_vars_info": [...]  # 全局变量详细信息
}
```

## API接口

### 主要接口
- `POST /api/sdw/ram_confirm/ram_confirm`: 执行RAM确认工作流
- `POST /api/sdw/ram_confirm/ram_confirm_legacy`: 兼容旧版本的接口

### 请求格式
```json
{
    "repoInfo": {
        "repo_path": "/path/to/repository",
        "commit_id": "abc123def456",
        "compared_commit_id": "def456abc123"
    }
}
```

### 响应格式
```json
{
    "code": 0,
    "msg": "RAM确认执行成功",
    "data": {
        "ram_confirm_uri": "/path/to/output.xlsx",
        "statistics": {
            "total_global_vars": 10,
            "confirm_records": 10
        }
    }
}
```