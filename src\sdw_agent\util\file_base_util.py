import os
import pathlib
from datetime import datetime


def create_text_file_link(file_path: str, line_number: int = None, editor: str = "default") -> str:
    """
    创建指向文本文件特定行的超链接

    参数:
    file_path: str - 文件路径
    line_number: int - 指定行号，默认为None
    editor: str - 指定编辑器类型，默认为"default"

    返回:
    str - 文件链接字符串
    """
    # 获取文件的绝对路径并统一路径格式
    abs_path = os.path.abspath(file_path).replace("\\", "/")

    # 如果未指定行号，直接返回文件链接
    if line_number is None:
        return f"file:///{abs_path}"

    # 根据指定的编辑器类型，生成对应的文件链接
    if editor == "vscode":
        # 生成VSCode编辑器中的文件链接
        return f"vscode://file/{abs_path}:{line_number}:1"
    elif editor == "notepad++":
        # 生成Notepad++编辑器中的文件链接
        return f"notepad++://open/{abs_path} -n{line_number}"
    else:
        # 默认只返回文件链接
        return f"file:///{abs_path}"


def get_output_dir_path(output_data_path: str, subdir: str) -> str:
    """
    生成成果物存放的目录, 可与gen_output_path配合使用
    Args:
        output_data_path: 根输出目录
        subdir: 子目录名，默认 communication_can_cs
    Returns:
        完整的输出文件路径
    """
    output_dir = os.path.join(pathlib.Path(output_data_path), subdir)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    return output_dir

def gen_output_path(output_dir: str, report_base_name: str, file_ext: str = ".xlsx") -> str:
    """
    生成完整的输出文件路径

    该函数根据指定的输出目录和报告基础名称，生成一个包含时间戳的完整文件路径。
    如果输出目录不存在则会自动创建，文件扩展名固定为.xlsx

    参数:
        output_dir (str): 输出目录路径，必须为非空字符串
        report_base_name (str): 报告文件的基础名称，必须为非空字符串

    返回:
        str: 完整的输出文件路径，格式为：目录路径/基础名称_YYYYMMDD_HHMMSS.xlsx

    异常:
        ValueError: 当参数无效或目录创建失败时抛出
    """
    # 生成完整的输出文件名，包括路径、基础名称、当前时间和文件扩展名
    if not output_dir or not isinstance(output_dir, str):
        raise ValueError("output_dir must be a non-empty string")

    if not report_base_name or not isinstance(report_base_name, str):
        raise ValueError("report_base_name must be a non-empty string")

    # 验证并创建输出目录
    try:
        # 验证路径是否有效
        path_obj = pathlib.Path(output_dir)
        if not path_obj.exists():
            path_obj.mkdir(parents=True, exist_ok=True)
        elif not path_obj.is_dir():
            raise ValueError("output_dir must be a directory")
    except Exception as e:
        raise ValueError(f"Invalid output directory: {str(e)}")

    return str(path_obj / f"{report_base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}")



def is_safe_path(file_path: str, base_path: str = os.getcwd()) -> bool:
    """
    检查文件路径是否安全

    Args:
        file_path: 文件路径
        base_path: 基础路径

    Returns:
        bool: 是否安全
    """
    try:
        # 获取规范化的完整路径
        real_path = os.path.realpath(file_path)
        base_path = os.path.realpath(base_path)

        # 检查路径是否在基础路径下
        common_path = os.path.commonpath([real_path, base_path])
        return common_path == base_path
    except ValueError:
        # 路径不在同一驱动器或不兼容
        return False
