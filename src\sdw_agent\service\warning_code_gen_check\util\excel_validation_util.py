"""
Excel Validation Utilities
Excel验证工具类
"""
import json
import re
import traceback
from typing import Dict, Tuple, Any, Optional

import pandas as pd
from loguru import logger
import xlwings as xw

from sdw_agent.config.env import ENV
from sdw_agent.service.warning_code_gen_check.models import (WarningChangeInfo)
from sdw_agent.service.warning_code_gen_check.util.warning_code_check_util import WarningCodeCheckUtils


class ExcelValidationUtils:
    """Excel验证工具类"""

    @staticmethod
    def check_correct_data(file_path, sheet_name, columns_to_check, correction_rules):
        """
        筛选并修正指定 sheet 的数据。

        参数：
            file_path (str): Excel 文件路径。
            sheet_name (str): 要处理的 sheet 名称。
            columns_to_check (list): 需要检查的列名。
            correction_rules (dict): 校正规则，键为错误值，值为正确值。

        返回：
            bool: 如果有错误类型被修正，返回 True，否则返回 False。
        """
        # 打开 Excel 文件
        app = xw.App(visible=False)  # 设置为不可见
        app.display_alerts = False  # 禁用警告弹窗
        app.enable_events = False  # 禁用事件
        app.screen_updating = False  # 禁用屏幕更新
        wb = app.books.open(file_path, read_only=False, update_links=False, ignore_read_only_recommended=True,
                            notify=False, corrupt_load=0)

        try:
            # 检查 sheet 是否存在
            if sheet_name not in [sheet.name for sheet in wb.sheets]:
                logger.error(f"Sheet {sheet_name} 不存在于文件中。")
                return False

            sheet = wb.sheets[sheet_name]
            is_have_wrong_type = False  # 标志是否有错误类型
            err_info = []
            # 遍历需要检查的列
            for column in columns_to_check:
                # 获取列数据
                col_range = sheet.range(f"{column}2:{column}{sheet.used_range.last_cell.row}")  # 假设第1行为表头
                for cell in col_range:
                    original_value = str(cell.value) if cell.value is not None else None
                    if original_value in correction_rules:
                        # 修正错误值
                        cell.value = correction_rules[original_value]
                        is_have_wrong_type = True
                        err_info.append(f"行:{cell.row}, 列:{cell.column}, 值:{original_value}")

            # 保存文件
            if is_have_wrong_type:
                wb.save(file_path)
                logger.info(f"修正后的数据已保存到 {file_path}")
            ret_desc = "".join(err_info) if is_have_wrong_type else "原表检查没有错误"
            return is_have_wrong_type, ret_desc
        except Exception as e:
            traceback.print_exc()
            raise e
        finally:
            # 关闭 Excel 应用
            wb.close()
            app.quit()

    @staticmethod
    def check_continuous_text_in_column(wb, msg_sheets, col_letter="E", limit=255):
        """
        检查 Excel 文件中多个表单的指定列是否存在连续有内容的行数超过指定限制。

        参数：
            file_path (str): Excel 文件路径。
            sheet_names (list): 要检查的表单名称列表。
            col_letter (str): 要检查的列（默认是 "E" 列）。
            limit (int): 连续有内容的行数限制（默认是 255）。

        返回：
            dict: 每个表单的检查结果，格式为 {表单名称: (连续有内容的行数, 是否超过限制)}。
        """
        # 打开 Excel 文件

        results = {}
        max_key = max(msg_sheets, key=msg_sheets.get)
        try:
            for sheet_name in msg_sheets.keys():
                # 获取表单
                sheet = wb.sheets[sheet_name]

                # 获取 E 列数据（从第 1 行到最后一行）
                col_index = ord(col_letter.upper()) - ord("A") + 1  # 将列字母转为索引
                last_row = sheet.used_range.last_cell.row  # 获取最后一行
                column_data = sheet.range((1, col_index), (last_row, col_index)).value

                # 如果只有一行数据，确保 column_data 是列表
                if not isinstance(column_data, list):
                    column_data = [column_data]

                # 检查连续有内容的行数
                max_continuous = 0
                current_continuous = 0
                for cell in column_data:
                    if cell and isinstance(cell, str) and cell.strip():  # 有内容的单元格
                        current_continuous += 1
                        max_continuous = max(max_continuous, current_continuous)
                    else:
                        current_continuous = 0
                if max_key != sheet_name:
                    check_254 = "检查OK"
                else:
                    check_254 = "最后一页达到254行" if max_continuous >= 254 else "最后一页未达到254行"

                # 判断是否超过限制
                exceeds_limit = max_continuous > limit
                results[sheet_name] = (max_continuous, exceeds_limit, check_254)
            return results
        except Exception as e:
            traceback.print_exc()
            raise e

    @staticmethod
    def check_source_sheet_data_type(file_path: str) -> Tuple[bool, str]:
        """
        检查源工作表数据类型
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            (是否有错误, 错误描述)
        """
        try:

            sheet_name = "CONTDISP (源)"
            columns_to_check = ['E', 'F', 'G', 'H']
            correction_rules = {
                "O": "○",
                "II": "Ⅱ",
            }

            return ExcelValidationUtils.check_correct_data(file_path, sheet_name, columns_to_check, correction_rules)

        except Exception as e:
            logger.error(f"检查源工作表数据类型失败: {str(e)}")
            return True, f"检查失败: {str(e)}"

    @staticmethod
    def check_interafce_popup_sheet_data_type(file_path: str) -> Tuple[bool, str]:
        """
        检查接口弹窗工作表数据类型
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            (是否有错误, 错误描述)
        """
        try:
            sheet_name = "Interface_Popup"
            columns_to_check = ['E', 'F', 'G', 'H']
            correction_rules = {
                "O": "○",
                "II": "Ⅱ",
            }

            return ExcelValidationUtils.check_correct_data(file_path, sheet_name, columns_to_check, correction_rules)

        except Exception as e:
            logger.error(f"检查接口弹窗工作表数据类型失败: {str(e)}")
            return True, f"检查失败: {str(e)}"

    @staticmethod
    def check_toyota_sheet_data_type(file_path: str, sheet_name: str = "Toyota") -> Tuple[bool, str]:
        """
        检查Toyota工作表数据类型

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称

        Returns:
            (是否有错误, 错误描述)
        """
        try:
            app = xw.App(visible=False)
            app.display_alerts = False  # 禁用警告弹窗
            app.enable_events = False  # 禁用事件
            app.screen_updating = False  # 禁用屏幕更新
            wb = app.books.open(file_path)

            try:
                ws = wb.sheets[sheet_name]

                # 获取数据范围
                last_row = ws.used_range.last_cell.row

                column_combined = []  # 存储每列拼接的字符串

                # 按列拼接
                for col in range(1, 8):  # A-Z列
                    logger.debug(f"正在处理列 {chr(ord('A') + col - 1)}...")
                    col_values = [str(ws.range((row, col)).value or "") for row in range(2, last_row + 1)]
                    column_combined.append("".join(col_values))

                # 按行拼接
                # for row in range(2, last_row + 1):
                #     row_values = [str(ws.range((row, col)).value or "") for col in range(1, 15)]  # A-Z列
                #     row_combined.append("".join(row_values))

                # 最终拼接列和行的字符串
                final_combined = "".join(column_combined)
                final_combined = final_combined.replace("-", "").replace(" ", "")  # 替换"-"为空字符串

                # 检查最终拼接的字符串是否只包含数字和小数点
                if not re.match(r'^[\d.]*$', final_combined):
                    return True, "最终拼接的字符串包含非法字符"

                return False, "Toyota工作表数据类型检查通过"

            finally:
                wb.close()
                app.quit()
        except Exception as e:
            return True, f"发生异常: {str(e)}"

    @staticmethod
    def check_sorting(file_path, sheet_aa, sheet_bb, columns_to_check):
        """
        检查 sheet_bb 的数据是否按照 sheet_aa 的顺序排列（允许插入多余行）。
        如果不正确，输出从哪一行开始不正确以及行内容。

        参数:
        - file_path: Excel 文件路径
        - sheet_aa: 参考顺序的 sheet 名称
        - sheet_bb: 被检查顺序的 sheet 名称
        - columns_to_check: 要检查的列（列表形式，如 ['A', 'W']）

        返回:
        - True: 如果 sheet_bb 的数据按 sheet_aa 的顺序排列
        - False: 如果顺序不匹配，并输出从哪一行开始不正确
        """
        # 读取两个 sheet 数据
        try:
            df_source = pd.read_excel(file_path, sheet_name=sheet_aa, usecols=columns_to_check)
            df_target = pd.read_excel(file_path, sheet_name=sheet_bb, usecols=columns_to_check)
        except Exception as e:
            logger.error(f"读取 Excel 文件失败: {e}")
            return False

        # 提取参考列（以第一个列为主）
        aa_values = df_source[columns_to_check[0]].dropna().tolist()
        bb_values = df_target[columns_to_check[0]].dropna().tolist()

        # 检查 bb 是否按照 aa 的顺序排列（允许插入多余行）
        aa_index = 0
        for index, bb_value in enumerate(bb_values):
            # 如果当前 bb 的值匹配当前 aa 的值
            if aa_index < len(aa_values) and bb_value == aa_values[aa_index]:
                aa_index += 1
            # 如果 aa 的所有值都匹配完成，直接结束
            if aa_index == len(aa_values):
                break
        else:
            # 如果遍历完 bb 但 aa 的值没有完全匹配
            if aa_index < len(aa_values):
                ret_desc = f"从源表第 {aa_index + 2} 行开始不正确，{aa_values[aa_index]}值异常, 内容为：{df_target.iloc[index].to_dict()}"
                return True, ret_desc

        return False, "sheet_MET 的数据按 sheet_源 的顺序排列,检查OK"

    @staticmethod
    def check_dspmnscrl_msg_max_length(file_path: str) -> Tuple[int, str]:
        """
        检查显示消息最大长度
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            (是否有错误, 错误描述)
        """
        try:

            pattern = "dspmnscrl_msg(\d*)\.prm"
            # pattern = ENV.config.warning_code_check.msg_sheets
            msg_sheets = {}
            app = xw.App(visible=False)  # 设置为不可见
            wb = app.books.open(file_path)

            for sheet_name in wb.sheets:
                sheet_name_str = str(sheet_name.name)
                match = re.match(pattern, sheet_name_str)

                if match:
                    number_part = match.group(1)
                    if number_part:  # 有数字
                        sheet_id = int(number_part)
                    else:  # 没有数字，默认为1
                        sheet_id = 1

                    msg_sheets[sheet_name_str] = sheet_id

            is_dspmnscrl_msg_length_exceeded = ExcelValidationUtils.check_continuous_text_in_column(wb, msg_sheets)

            # 提取所有 exceeds_limit 的值
            exceeds_limit_values = [value[1] for value in is_dspmnscrl_msg_length_exceeded.values()]

            if exceeds_limit_values:
                bitwise_and_result = any(exceeds_limit_values)
            else:
                bitwise_and_result = False

            return bitwise_and_result, str(is_dspmnscrl_msg_length_exceeded)

        except Exception as e:
            logger.error(f"检查显示消息最大长度失败: {str(e)}")
            return True, f"检查失败: {str(e)}"
        finally:
            # 关闭 Excel 应用
            wb.close()
            app.quit()

    @staticmethod
    def compare_excel_sheets(file1, file2):
        # 读取两个 Excel 文件
        excel1 = pd.ExcelFile(file1)
        excel2 = pd.ExcelFile(file2)

        # 获取所有 sheet 名称
        sheets1 = set(excel1.sheet_names)
        sheets2 = set(excel2.sheet_names)

        # 结果字典
        result = {}

        # 遍历两个文件中共有的 sheet
        for sheet in sheets1.intersection(sheets2):
            # 读取 sheet 内容
            df1 = excel1.parse(sheet)
            df2 = excel2.parse(sheet)
            df1 = df1.fillna("")
            df2 = df2.fillna("")
            if df1.shape[0] == 0 or df2.shape[0] == 0:
                added_rows = []
                deleted_rows = []
            else:
                # 将 DataFrame 转为字符串行列表，方便比较
                rows1 = df1.astype(str).apply(lambda x: ','.join(x), axis=1).tolist()
                rows2 = df2.astype(str).apply(lambda x: ','.join(x), axis=1).tolist()

                # 找出新增的行和删除的行
                added_rows = [row for row in rows2 if row not in rows1]
                deleted_rows = [row for row in rows1 if row not in rows2]

            # 如果有新增或删除的行，记录到结果中
            if added_rows or deleted_rows:
                result[sheet] = {
                    "新增": added_rows,
                    "删除": deleted_rows
                }
        return result

    @staticmethod
    def sheet_code_check(new_warning_tool_url, old_warning_tool_url, warning_info_dict: WarningChangeInfo):
        sheet_compare_code_dict = ExcelValidationUtils.compare_excel_sheets(old_warning_tool_url, new_warning_tool_url)
        WarningCodeCheckUtils.replace_nan_in_dict(sheet_compare_code_dict)
        warning_info_dict.compare_tools_code_result = sheet_compare_code_dict

        check_results = {
            "tftwarning_cfg_private.h": ExcelValidationUtils.tftwarning_cfg_private_code_check(
                sheet_compare_code_dict.get("tftwarning_cfg_private.h", None),
                warning_info_dict),
            "tftwarning.c": ExcelValidationUtils.tftwarnin_code_check(
                sheet_compare_code_dict.get("tftwarning.c", None),
                warning_info_dict),
            "dspwrn_cfg.prm": ExcelValidationUtils.tdspwrn_cfg_prm_code_check(
                sheet_compare_code_dict.get("dspwrn_cfg.prm", None),
                warning_info_dict),
            "dspwrn_cfg.h": ExcelValidationUtils.dspwrn_cfg_h_code_check(
                sheet_compare_code_dict.get("dspwrn_cfg.h", None),
                warning_info_dict),
            "dspbld_cnttdatabldr_wrn.prm": ExcelValidationUtils.dspbld_cnttdatabldr_wrn_prm_code_check(
                sheet_compare_code_dict.get("dspbld_cnttdatabldr_wrn.prm", None),
                warning_info_dict),
            "dwstoutgntr.prm": ExcelValidationUtils.dwstoutgntr_prm_code_check(
                sheet_compare_code_dict.get("dwstoutgntr.prm", None),
                warning_info_dict),
            "wrndtcfg.prm": ExcelValidationUtils.wrndtcfg_prm_code_check(
                sheet_compare_code_dict.get("wrndtcfg.prm", None),
                warning_info_dict),
            "dspmnscrldef&dspmnscrl_msg": ExcelValidationUtils.wrndtcfg_prm_code_check(
                sheet_compare_code_dict.get("dspmnscrl_msg2.prm", None),
                warning_info_dict),
        }

        # 输出字典
        logger.info("检查结果字典:", check_results)
        return check_results

    @staticmethod
    def tftwarning_cfg_private_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        # 提取每个子字典中键 "①" 的值
        result = [v.get("①").replace("-", "_").strip("_") for v in warning_change_info.change_row_result.values() if
                  v is not None]
        logger.debug(result)
        # 遍历并打印 '新增' 列表中的每一行
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                pattern = r"#define\s+TFTWARNING_([A-Za-z]\S+)_[A-Za-z]+\s+\(\s*(\d+)\s*\)"

                # 使用 re.findall 提取所有匹配的数据
                matches = re.findall(pattern, line)
                # 打印提取的结果
                for identifier, value in matches:
                    logger.debug(f"标识符: {identifier}, 数值: {value}")
                    if identifier not in result:
                        logger.error(f"标识符{identifier} 新增不在列表{result}中")
                        return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def tftwarnin_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]

        # 遍历并打印 '新增' 列表中的每一行
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                pattern = r"TFTWARNING_(\d+)(?:_[A-Z])?_KANZIID"
                numbers = re.findall(pattern, line)
                # 打印提取的结果
                logger.debug(f"标识符: {numbers} {warning_id}")
                if int(numbers[0]) not in warning_id:
                    logger.error(f"标识符: {line} 不在 {warning_id}")
                    return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def tdspwrn_cfg_prm_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]
        logger.debug(warning_id)
        # 遍历并打印 '新增' 列表中的每一行
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                pattern = r"/\*.*?:\s*(\d+)(?:\.\d+)?\s+DWRNID_WARNING_\d+.*?\*/"

                # 查找匹配项
                match = re.search(pattern, line)

                if match:
                    # 提取匹配到的数字
                    extracted_number = match.group(1)

                    # 输出结果
                    if int(extracted_number) not in warning_id:
                        logger.error(f"标识符: {line} 不在 {warning_id}")
                        return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def dspwrn_cfg_h_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]
        logger.debug(warning_id)
        # 遍历并打印 '新增' 列表中的每一行
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                pattern = r"/\*.*?:\s*(\d+)(?:\.\d+)?\s+DWRNID_WARNING_\d+.*?\*/"

                # 查找匹配项
                match = re.search(pattern, line)

                if match:
                    # 提取匹配到的数字
                    extracted_number = match.group(1)

                    # 输出结果
                    if int(extracted_number) not in warning_id:
                        logger.error(f"标识符: {line} 不在 {warning_id}")
                        return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def dspbld_cnttdatabldr_wrn_prm_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]
        logger.debug(warning_id)
        # 遍历并打印 '新增' 列表中的每一行
        pattern_code = r"DSPBLD_DATABLDR_WRN_WRNIDDATA_WARNING_(\d+)"
        pattern_desc = r"/\*.*?:\s*(\d+)(?:\.\d+)?\s+DWRNID_WARNING_\d+.*?\*/"
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                # 查找匹配项
                match1 = re.search(pattern_code, line)
                if match1:
                    # 提取匹配到的数字
                    extracted_number = match1.group(1)
                    # 输出结果
                    if int(extracted_number) not in warning_id:
                        logger.error(f"标识符: {line} 不在 {warning_id}")
                        return True, code_change_dict

                match2 = re.search(pattern_desc, line)
                if match2:
                    # 提取匹配到的数字
                    extracted_number = match2.group(1)

                    # 输出结果
                    if int(extracted_number) not in warning_id:
                        logger.error(f"标识符: {line} 不在 {warning_id}")
                        return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def dwstoutgntr_prm_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]
        logger.debug(warning_id)
        # 遍历并打印 '新增' 列表中的每一行
        if code_change_dict.get('新增'):  # 检查 '新增' 列表是否非空
            for line in code_change_dict['新增']:
                # 正则表达式提取格式：`#define 标识符 (数字)`
                pattern = r"/\*.*?:\s*(\d+)(?:\.\d+)?\s+DWRNID_WARNING_\d+.*?\*/"

                # 使用 re.findall 提取所有匹配的数据
                matches = re.findall(pattern, line)
                # 打印提取的结果
                for identifier in matches:
                    real_line = re.sub(r",(?=\s*/\*)", " ", line)  # 去掉句末,
                    if int(identifier) not in warning_id and real_line not in code_change_dict.get('删除', []):
                        logger.error(f"标识符{identifier} 新增不在列表{warning_id}中")
                        return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def wrndtcfg_prm_code_check(code_change_dict, warning_change_info: WarningChangeInfo):
        if code_change_dict is None:
            return False, code_change_dict
        logger.debug(json.dumps(code_change_dict, ensure_ascii=False, indent=4))
        warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]
        logger.debug(warning_id)
        # 正则表达式
        pattern = r"u1_GETREQ_ID(\d+).*?/\*\s*(\d+):\s*([\d.]+)"
        # 提取数据
        for line in code_change_dict.get('新增'):
            match = re.search(pattern, line)
            if match:
                id_number = int(match.group(1))  # 提取并转换 `u1_GETREQ_IDXXXX` 中的数字为整数
                first_number = int(match.group(2))  # 提取 `XX:` 前的数字为整数
                second_number = float(match.group(3))  # 提取 `:` 后的数字（可能是小数）
                results = (id_number, first_number, second_number)
                logger.debug(f"标识符: {results}")
                if int(results[2]) not in warning_id or int(results[1]) != int(results[0]):
                    logger.error(f"标识符{results} 新增不在列表{warning_id}中")
                    return True, code_change_dict
        return False, code_change_dict

    @staticmethod
    def _wrndt_msg_code_check(code_change_dict: Optional[Dict],
                              warning_change_info: WarningChangeInfo) -> Tuple[bool, Any]:
        """检查wrndt_msg.prm代码"""
        if code_change_dict is None:
            return False, code_change_dict

        try:
            warning_id = [int(float(item)) for item in warning_change_info.change_row_result.keys()]

            pattern = r"DWRNID_WARNING_([\d]+)"

            for line in code_change_dict.get('新增', []):
                match = re.search(pattern, line)
                if match:
                    id_number = int(match.group(1))
                    if id_number not in warning_id:
                        logger.error(f"标识符{id_number} 新增不在列表{warning_id}中")
                        return True, code_change_dict

            return False, code_change_dict

        except Exception as e:
            logger.error(f"检查wrndt_msg.prm失败: {str(e)}")
            return True, f"检查失败: {str(e)}"
