# 需求分析工作流

## V字对应
- 4.2 検査実施
- 65 Bug提交確認

## 概述

该模块提供Bug提交功能，支持分析测试用例中的NG结果，并生成标准化的Bug提交内容。

## 主要功能

### 核心功能：测试用例NG项处理
- **文件输入**: 测试用例
- **提取NG项**: 筛选出测试用例里NG项用例
- **生成redmine关键词**: 生成redmine关键词
- **结果输出**: 输出redmine关键词表格以及BUG ID回填测试用例

### 分析流程
1. **读取测试用例**: 支持Excel格式文件
2. **NG项提取**: 从指定列提取NG项用例
3. **生成redmine关键词**: 基于NG项测试内容按照规则或AI生成redmine关键词
4. **结果写入**: 将生成的redmine结果写入表格，bug id回填测试用例
5. **文件保存**: 保存redmine关键词和测试用例

## 使用方法

### 基本使用

```python
from sdw_agent.service.bug_commit import BugCommitWorkflow

# 创建工作流实例
workflow = BugCommitWorkflow()

# 处理要件一览表
result = workflow.run("/origin_case_path",  # 输入文件名
                      "output_dir"          # 输出路径
                      )

print(f"处理结果: {result.status.value}")
print(f"处理了 {result.data['processed_count']} 行")
```

## 输入参数说明

| 参数名 | 类型 | 必需 | 描述                        |
|--------|------|------|---------------------------|
| `requirement_file_path` | str | 是 | 测试用例文件名                   |
| `output_file_path` | str | 否 | 输出文件路径，为空则使用默认路径 ./output |


## 配置说明

工作流配置文件 `config.yaml` 包含以下配置项：

```yaml
# 基本配置
name: "Bug提交"
description: "分析测试用例中的NG结果，并生成标准化的Bug提交内容"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  # 输入文件格式
  input:
    excel_extensions: [".xlsx", ".xls"]  # 测试用例文件支持的格式
    
  # 输出文件配置
  output:
    default_output_dir: "./output"  # 默认输出目录
    output_filename_redmine: "bug_commit_results.xlsx" # 输出文件名
    output_filename_testcase: "bug_id_to_testcase.xlsx"
```

## 输入文件格式
### 
测试用例应为Excel格式，至少包含以下列：
- `手动确认结果` (或自定义列名): 手工验证测试用例结果
- 其他业务相关列...


## 输出文件格式
处理后的测试用例会新增 bug id字段
生成的redmine关键词表格会包含如下字段
- `sheet名`: bug来自的sheet页，规则生成
- `测试用例No.`: NG测试用例编号，规则生成
- `跟踪`: 固定`ST_Defect`，规则生成
- `主题`: AI生成，【车型名】【模块名】AI总结bug描述 （【车型名】：参照“车型（orca)”关键字；【模块名】：参照“Module”关键字）
- `状态`: 固定`Assigned`
- `指派给`: 规则生成，Module + "担当"（例：车速开发/CTSM开发）
- `目标版本`: 固定`XXX_NO_PLAN`
- `Submitter Group`: 固定输出为：`DNKT`
- `Module`: 测试用例“Cover”sheet名中提取
- `Found Version`: 测试用例中测试结果处"Version"列，通常是E3版本
- `Test Environment`: 固定输出为：`ASSY`
- `测试类别`:测试用例文件名中带基本测试字样输出为“基本功能测试”，不带的输出为“其他”
- `Occur Rate`: 规则生成，参照测试用例文档中“Remarks”列，如果为空则100%复现，反之有内容，就读取发生概率：20%、50%
- `Severity`: AI生成
- `Bug影响度`: AI生成
- `计划完成日期`: 规则生成，输出： 提出日期+ "Severity" (A:1天；B：2天；C:3天；D：一周)
- `Submitted Date`: 测试用例中找，测试结果sheet中"Date"列
- `Submitter`: 测试用例中找，测试结果sheet中"Tester"列
- `Assignee`: 规则生成，同Submitter
- `车型(orca)`: 规则生成，在测试用例文档中 “Version”中获取
- `D1L1版本`: 规则生成，测试用例中找，测试用例中测试结果处"Version"列
- `DTF版本`: 规则生成，测试用例中找，测试用例中测试结果处"Version"列
- `式样书名(Orca)`: 规则生成，基本功能测试用例参照“对应式样书”列内容；其他以外用例参照用例文档名
- `Found Defect Methods`: 固定输出为：`人工检查`
- `Procedure`: AI生成，手顺
- `Bug_ID`: 规则生成

## 错误处理

工作流包含完善的错误处理机制：
- 输入参数验证
- 文件操作异常处理
- AI调用异常处理
- 详细的错误日志记录

## 注意事项

1. 测试用例文件必须是有效的Excel格式
2. 所有文件操作都有安全检查和异常处理
