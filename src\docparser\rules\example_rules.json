[{"id": "json_header_rule", "name": "JSON Header Rule", "description": "Identifies headers in text objects using JSON configuration", "priority": 95, "target": "text", "enabled": true, "condition": {"type": "or", "conditions": [{"type": "field_equals", "field": "style", "value": "Heading1"}, {"type": "field_equals", "field": "style", "value": "Heading2"}, {"type": "and", "conditions": [{"type": "field_exists", "field": "font_size"}, {"type": "field_equals", "field": "bold", "value": true}]}]}, "transform": {"type": "chain", "transforms": [{"type": "set_field", "field": "is_header", "value": true}, {"type": "set_field", "field": "importance", "value": "high"}, {"type": "set_field", "field": "source", "value": "json_rule"}]}}, {"id": "json_table_formatter", "name": "JSON Table Formatter", "description": "Formats table objects using JSON configuration", "priority": 75, "target": "table", "enabled": true, "condition": {"type": "always"}, "transform": {"type": "chain", "transforms": [{"type": "set_field", "field": "formatted", "value": true}, {"type": "set_field", "field": "source", "value": "json_rule"}]}}, {"id": "json_image_classifier", "name": "JSON Image Classifier", "description": "Classifies images based on their filename", "priority": 65, "target": "picture", "enabled": true, "condition": {"type": "field_exists", "field": "file_path"}, "transform": {"type": "chain", "transforms": [{"type": "set_field", "field": "classified", "value": true}, {"type": "set_field", "field": "source", "value": "json_rule"}, {"type": "replace_text", "field": "file_path", "pattern": ".*/(.*?)\\.(jpg|jpeg|png|gif|bmp)$", "replacement": "$1"}]}}, {"id": "json_graphic_enhancer", "name": "JSON Graphic Enhancer", "description": "Enhances graphics with additional metadata", "priority": 55, "target": "graphic", "enabled": true, "condition": {"type": "always"}, "transform": {"type": "chain", "transforms": [{"type": "set_field", "field": "enhanced", "value": true}, {"type": "set_field", "field": "source", "value": "json_rule"}]}}, {"id": "json_document_classifier", "name": "JSON Document Classifier", "description": "Classifies documents based on their content", "priority": 85, "target": "document", "enabled": true, "condition": {"type": "always"}, "transform": {"type": "chain", "transforms": [{"type": "set_field", "field": "processed_by", "value": "json_rules"}, {"type": "set_field", "field": "processing_timestamp", "value": "2025-05-16T11:00:00Z"}]}}]