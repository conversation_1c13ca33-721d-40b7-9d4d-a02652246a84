"""
IF整合性确认工作流

V字对应：
2.5.26 I/F整合性確認

该模块提供接口整合性确认功能，检查代码中接口使用与IF设计书的一致性。
1. 从I/F设计书中提取IF一览中函数名、函数参数类型、值域范围等信息
2. 从git仓库下载代码，根据函数名从代码中查找所有的调用的源码片段
3. 根据调用源码片段内容，结合整个.c文件内容与I/F设计书该函数的约束条件由大模型分析调用参数是否合理，输出判定结果
4. 将判定结果整合保存在模板文件中，输出I/F整合性确认报告书
"""
from fastapi import APIRouter

from sdw_agent.config.env import ENV
from sdw_agent.service.if_integration_service import IfIntegrationRequest, do_if_integration

router = APIRouter(prefix="/api/sdw/integration", tags=["I/F整合工作流"])


@router.post("/if_integration",
             summary="2.5.26 I/F整合性確認",
             description="",
             response_description="")
async def if_integration(request:IfIntegrationRequest):
    """I/F整合性确认"""
    if_stylebook = request.ifStylebook
    clone_repo_info = request.cloneRepoInfo

    if_integration_uri = do_if_integration(if_stylebook, clone_repo_info)
    return {
        "code": 0,
        "msg": "",
        "data": {
            "if_integration_uri": if_integration_uri
        }
    }

@router.get("/get_git_repository_url",
             summary="2.5.26 获取git仓库地址",
             description="",
             response_description="")
async def get_git_repository_url():
    """I/F整合性确认 获取git仓库地址"""

    return {
        "code": 0,
        "msg": "",
        "data": {
            "git_repository_url": ENV.config.git_repository_url
        }
    }
