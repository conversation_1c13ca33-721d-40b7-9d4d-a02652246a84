#!/usr/bin/env python3
"""
使用在线服务导出 Draw.io 图像
无需安装 draw.io desktop
"""

import os
import base64
import requests
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any
from urllib.parse import quote

from loguru import logger
from openpyxl.drawing.image import Image as OpenpyxlImage

from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def export_drawio_via_online_service(drawio_file: str, 
                                    output_png: str,
                                    format: str = "png",
                                    scale: float = 2.0) -> bool:
    """
    使用在线服务导出 Draw.io 文件
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出图像文件路径
        format: 输出格式 (png, jpg, svg)
        scale: 缩放比例
        
    Returns:
        是否成功导出
    """
    try:
        # 1. 读取 Draw.io 文件内容
        with open(drawio_file, 'r', encoding='utf-8') as f:
            drawio_content = f.read()
        
        # 2. 对内容进行 URL 编码
        encoded_content = quote(drawio_content)
        
        # 3. 构建在线导出 URL
        # 使用 draw.io 的官方导出 API
        export_url = f"https://app.diagrams.net/export"
        
        # 4. 准备请求参数
        params = {
            'format': format,
            'scale': str(scale),
            'border': '10',  # 边框
            'bg': 'white',   # 背景色
            'from': 'data'   # 数据来源
        }
        
        # 5. 发送 POST 请求
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        data = f"xml={encoded_content}"
        
        logger.info(f"正在通过在线服务导出 Draw.io 图像...")
        response = requests.post(export_url, 
                               params=params,
                               data=data,
                               headers=headers,
                               timeout=30)
        
        if response.status_code == 200:
            # 6. 保存图像
            with open(output_png, 'wb') as f:
                f.write(response.content)
            
            logger.success(f"在线导出成功: {output_png}")
            return True
        else:
            logger.error(f"在线导出失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        logger.error(f"在线导出异常: {str(e)}")
        return False


def export_drawio_via_puppeteer(drawio_file: str, output_png: str) -> bool:
    """
    使用 Puppeteer 方式导出（需要 Node.js）
    
    这是一个备选方案，需要安装 Node.js 和 puppeteer
    """
    try:
        # 创建临时的 JavaScript 脚本
        js_script = f"""
const puppeteer = require('puppeteer');
const fs = require('fs');

(async () => {{
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    
    // 读取 drawio 文件
    const drawioContent = fs.readFileSync('{drawio_file}', 'utf8');
    
    // 构建 draw.io URL
    const encodedContent = encodeURIComponent(drawioContent);
    const url = `https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=diagram#R${{encodedContent}}`;
    
    await page.goto(url);
    await page.waitForTimeout(3000); // 等待加载
    
    // 截图
    await page.screenshot({{
        path: '{output_png}',
        fullPage: true,
        type: 'png'
    }});
    
    await browser.close();
    console.log('Export completed');
}})();
"""
        
        # 保存脚本到临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
            f.write(js_script)
            script_path = f.name
        
        try:
            # 执行 Node.js 脚本
            import subprocess
            result = subprocess.run(['node', script_path], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=60)
            
            if result.returncode == 0 and Path(output_png).exists():
                logger.success(f"Puppeteer 导出成功: {output_png}")
                return True
            else:
                logger.error(f"Puppeteer 导出失败: {result.stderr}")
                return False
                
        finally:
            # 清理临时文件
            os.unlink(script_path)
            
    except Exception as e:
        logger.error(f"Puppeteer 导出异常: {str(e)}")
        return False


def insert_online_exported_drawio_to_excel(drawio_file: str,
                                         excel_file: str,
                                         sheet_name: str = "架构图",
                                         title: Optional[str] = None,
                                         export_method: str = "online",
                                         scale: float = 2.0,
                                         max_width: int = 1200,
                                         max_height: int = 800) -> Dict[str, Any]:
    """
    使用在线服务导出 Draw.io 并插入到 Excel
    
    Args:
        drawio_file: Draw.io 文件路径
        excel_file: Excel 文件路径
        sheet_name: 工作表名称
        title: 图表标题
        export_method: 导出方法 ("online" 或 "puppeteer")
        scale: 缩放比例
        max_width: 图像最大宽度
        max_height: 图像最大高度
        
    Returns:
        操作结果
    """
    try:
        # 1. 导出 Draw.io 为图像
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_png = os.path.join(temp_dir, "drawio_export.png")
            
            if export_method == "online":
                success = export_drawio_via_online_service(drawio_file, temp_png, "png", scale)
            elif export_method == "puppeteer":
                success = export_drawio_via_puppeteer(drawio_file, temp_png)
            else:
                logger.error(f"不支持的导出方法: {export_method}")
                return {
                    "success": False,
                    "message": f"不支持的导出方法: {export_method}",
                    "excel_file": excel_file
                }
            
            if not success:
                return {
                    "success": False,
                    "message": f"使用 {export_method} 方法导出图像失败",
                    "excel_file": excel_file
                }
            
            # 2. 插入图像到 Excel
            with ExcelUtil(excel_file, auto_create=True) as excel:
                # 确保工作表存在
                if sheet_name not in excel.get_sheet_names():
                    excel.create_sheet(sheet_name)
                
                current_row = 1
                
                # 添加标题（如果提供）
                if title:
                    excel.write_cell(sheet_name, current_row, 1, title)
                    title_style = CellStyle(
                        font_size=16,
                        font_bold=True,
                        alignment_horizontal="center"
                    )
                    excel.set_cell_style(sheet_name, current_row, 1, title_style)
                    current_row += 2  # 留出空行
                
                # 插入图像
                from sdw_agent.util.drawio_util.export_original_image import insert_image_to_excel_sheet
                success = insert_image_to_excel_sheet(
                    excel=excel,
                    sheet_name=sheet_name,
                    image_path=temp_png,
                    row=current_row,
                    col=1,
                    max_width=max_width,
                    max_height=max_height
                )
                
                if success:
                    excel.save()
                    logger.success(f"Draw.io 图表已成功插入到 Excel: {excel_file}")
                    return {
                        "success": True,
                        "message": f"使用 {export_method} 方法导出并插入成功",
                        "excel_file": excel_file,
                        "sheet_name": sheet_name,
                        "image_position": f"行{current_row}, 列1"
                    }
                else:
                    return {
                        "success": False,
                        "message": "图像插入 Excel 失败",
                        "excel_file": excel_file
                    }
                    
    except Exception as e:
        error_msg = f"在线导出 Draw.io 图表到 Excel 时发生异常: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "message": error_msg,
            "excel_file": excel_file
        }


def demo_online_export():
    """演示在线导出 Draw.io 图像"""
    print("🎯 在线导出 Draw.io 图像演示")
    print("=" * 50)
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用 Draw.io 文件: {drawio_file}")
    
    # 2. 尝试在线导出
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    excel_file = output_dir / "online_architecture_diagram.xlsx"
    
    result = insert_online_exported_drawio_to_excel(
        drawio_file=drawio_file,
        excel_file=str(excel_file),
        sheet_name="在线导出架构图",
        title="系统架构图（在线导出版本）",
        export_method="online",
        scale=2.0,
        max_width=1400,
        max_height=1000
    )
    
    # 3. 显示结果
    print(f"\n📊 导出结果:")
    print(f"   成功: {result['success']}")
    print(f"   消息: {result['message']}")
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   图像位置: {result['image_position']}")
        print(f"\n🎉 请打开 Excel 文件查看原始图表！")
    else:
        print(f"   错误详情: {result['message']}")


if __name__ == "__main__":
    demo_online_export()
