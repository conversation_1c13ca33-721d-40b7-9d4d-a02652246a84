# 通信故障安全CS工作流配置文件

# 基本配置
name: "通信故障安全CS工作流"
description: "分析代码变更中的CAN信号匹配，支持BitAssign和SPI JSON两种数据源"
version: "1.0.0"
author: "qiliang_zhou"

# 工作流特定配置
workflow_config:
  # 处理配置
  max_workers: 10  # 最大并发线程数
  default_case_sensitive: true  # 默认是否区分大小写
  
  # 输出配置
  output_format: "xlsx"  # 输出文件格式
  include_summary: true  # 是否包含摘要信息
  
  # Gerrit配置
  gerrit_enabled: true  # 是否启用Gerrit集成
  
  # 文件处理配置
  supported_file_extensions:
    - ".c"
    - ".cpp" 
    - ".h"
    - ".hpp"

  # 点击代码链接，渲染代码的工具
  code_renderer: "vscode"

  bit_assign:
    excluded_files:
      - 'signal_name'
      - 'max_value'
      - 'min_value'
      - 'notes'
      - 'main_to_sub_com_id'
      - 'sub_to_main_com_id'
      - 'fld_len'
      - 'fld_strt'

  spi_json:
    excluded_files:
  
  # 信号匹配配置
  signal_matching:
    # 搜索模式配置
    patterns:
      # 精确匹配（单词边界）
      exact_match:
        enabled: true
        pattern: "\\b{signal_name}\\b"
        description: "精确匹配信号名称，使用单词边界"

      # 匹配赋值语句
      assignment_match:
        enabled: true
        pattern: "{signal_name}\\s*[=:]\\s*"
        description: "匹配赋值语句，信号名后跟等号或冒号"

      # 匹配指针访问（C/C++）
      pointer_access_match:
        enabled: true
        pattern: "->{signal_name}\\b"
        description: "匹配指针访问，箭头操作符后的信号名"

      # 匹配带(U数字)前缀的信号
      prefix_match:
        enabled: true
        pattern: "\\(U[0-9]\\)\\s*{signal_name}\\b"
        description: "匹配带(U数字)前缀的信号名"

      # 匹配结构体成员访问
      struct_member_match:
        enabled: false
        pattern: "\\.{signal_name}\\b"
        description: "匹配结构体成员访问，点操作符后的信号名"

      # 匹配函数调用参数
      function_param_match:
        enabled: false
        pattern: "\\({signal_name}\\)"
        description: "匹配函数调用中的信号名参数"

      # 匹配数组访问
      array_access_match:
        enabled: false
        pattern: "{signal_name}\\[.*?\\]"
        description: "匹配数组访问，信号名后跟方括号"
    
    # 超时配置
    timeout_seconds: 300  # 处理超时时间（秒）
    
    # 结果过滤配置
    min_line_length: 3  # 最小行长度
    exclude_comments: true  # 排除注释行
  
  # 输出配置
  output:
    # Excel配置
    excel:
      sheet_names:
        can_signals: "Matched_CAN"
        ctrl_signals: "Matched_CTRL"
      include_timestamp: true
      include_commit_id: true
    
    # 日志配置
    logging:
      level: "INFO"
      include_debug_info: false
      log_file_matches: true

# 错误处理配置
error_handling:
  retry_attempts: 3  # 重试次数
  retry_delay: 1  # 重试延迟（秒）
  continue_on_error: true  # 遇到错误时是否继续处理其他文件
  
# 性能配置
performance:
  batch_size: 1000  # 批处理大小
  memory_limit_mb: 512  # 内存限制（MB）
  enable_caching: true  # 是否启用缓存
