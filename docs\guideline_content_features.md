# 分类规则内容功能说明

## 概述

为`GuidelineManager`新增了4个关于规则内容的核心功能，支持更深入的规则内容管理和智能化处理。

## 新增功能

### 1. 获取规则的所有分类和描述

**方法**: `get_rule_categories(rule_key: str) -> List[CategoryContent]`

**功能**: 获取指定规则的所有分类信息，包括分类名称、描述和扩展字段。

**使用示例**:
```python
manager = GuidelineManager()
categories = manager.get_rule_categories("automotive_change_rule")

for category in categories:
    print(f"分类: {category.category_name}")
    print(f"描述: {category.description}")
    print(f"扩展字段: {category.additional_fields}")
```

### 2. 获取具体分类的详细内容

**方法**: `get_category_content(rule_key: str, category_name: str) -> Optional[CategoryContent]`

**功能**: 获取指定规则中特定分类的详细内容。

**使用示例**:
```python
category = manager.get_category_content("automotive_change_rule", "功能需求变更")
if category:
    print(f"分类名称: {category.category_name}")
    print(f"描述: {category.description}")
    for field, value in category.additional_fields.items():
        print(f"{field}: {value}")
```

### 3. 基于变更需求润色分类内容

**方法**: `polish_category_content(rule_key: str, category_name: str, change_request: Union[str, Dict], fields_to_polish: Optional[List[str]] = None) -> List[ContentPolishResult]`

**功能**: 根据具体的变更需求信息，使用AI对分类内容进行润色优化。

**使用示例**:
```python
change_request = {
    "title": "车载娱乐系统音频播放功能优化",
    "description": "优化音质和响应速度",
    "affected_modules": ["音频处理模块", "用户界面模块"]
}

results = manager.polish_category_content(
    "automotive_change_rule", 
    "功能需求变更", 
    change_request,
    ["description", "变更点理解", "影响范围分析"]
)

for result in results:
    print(f"字段: {result.field_name}")
    print(f"原始: {result.original_content}")
    print(f"润色后: {result.polished_content}")
    print(f"理由: {result.reasoning}")
```

### 4. AI智能匹配规则和分类

#### 4.1 匹配规则

**方法**: `match_rule_by_change_request(change_request: Union[str, Dict], top_k: int = 3) -> List[RuleMatchResult]`

**功能**: 基于变更需求信息，使用AI判断最匹配的规则。

**使用示例**:
```python
change_request = "需要为车载系统添加语音识别功能"
matches = manager.match_rule_by_change_request(change_request, top_k=3)

for match in matches:
    print(f"规则: {match.rule_name}")
    print(f"置信度: {match.confidence}")
    print(f"理由: {match.reasoning}")
```

#### 4.2 匹配分类

**方法**: `match_category_by_change_request(rule_key: str, change_request: Union[str, Dict], top_k: int = 3) -> List[CategoryMatchResult]`

**功能**: 在指定规则中，使用AI判断最匹配的分类。

**使用示例**:
```python
change_request = {
    "title": "添加用户登录功能",
    "type": "功能新增"
}

matches = manager.match_category_by_change_request(
    "automotive_change_rule", 
    change_request, 
    top_k=3
)

for match in matches:
    print(f"分类: {match.category_name}")
    print(f"置信度: {match.confidence}")
    print(f"理由: {match.reasoning}")
```

## Excel文件格式要求

### 基础格式
- **第1列**: 分类名称（必填）
- **第2列**: 分类描述（可选）

### 扩展格式
支持更多自定义字段，例如：
- **第3列**: 变更点理解
- **第4列**: 影响范围分析
- **第5列**: 风险评估
- **第6列**: 处理建议

**示例**:
```
| 分类名称     | 分类描述           | 变更点理解         | 影响范围分析       | 风险评估         |
|-------------|-------------------|-------------------|-------------------|------------------|
| 功能需求变更 | 功能特性的变更     | 理解业务价值       | 分析模块影响       | 评估质量风险      |
| 性能需求变更 | 性能指标的调整     | 理解性能目标       | 分析资源消耗       | 评估性能风险      |
```

## 数据模型

### CategoryContent
```python
class CategoryContent(BaseModel):
    category_name: str              # 分类名称
    description: str                # 分类描述
    additional_fields: Dict[str, Any]  # 扩展字段
```

### ContentPolishResult
```python
class ContentPolishResult(BaseModel):
    field_name: str          # 字段名称
    original_content: str    # 原始内容
    polished_content: str    # 润色后内容
    reasoning: str           # 润色理由
```

### RuleMatchResult
```python
class RuleMatchResult(BaseModel):
    rule_key: str           # 规则key
    rule_name: str          # 规则名称
    confidence: float       # 置信度(0-1)
    reasoning: str          # 匹配理由
```

### CategoryMatchResult
```python
class CategoryMatchResult(BaseModel):
    category_name: str      # 分类名称
    confidence: float       # 置信度(0-1)
    reasoning: str          # 匹配理由
```

## 使用场景

### 1. 规则内容查询
- 查看规则包含的所有分类
- 获取特定分类的详细信息
- 了解规则的覆盖范围和结构

### 2. 内容智能优化
- 根据具体变更需求润色规则内容
- 提升规则描述的针对性和实用性
- 保持规则内容与实际需求的一致性

### 3. 智能匹配和推荐
- 自动识别变更需求适用的规则
- 智能推荐最匹配的分类
- 提升规则使用的准确性和效率

### 4. 变更管理流程
- 变更需求分析和分类
- 规则内容的动态优化
- 自动化的规则匹配和应用

## 注意事项

1. **Excel文件格式**: 确保Excel文件格式正确，第一行为表头
2. **字段验证**: 润色功能会验证字段是否存在
3. **AI依赖**: 润色和匹配功能依赖AI模型，需要配置相应的AI服务
4. **性能考虑**: 大量规则匹配时可能需要较长时间
5. **数据一致性**: 确保Excel文件内容与实际需求保持一致

## 错误处理

- `ValueError`: 规则或分类不存在
- `FileNotFoundError`: Excel文件不存在
- `AI调用异常`: AI服务不可用或响应异常

所有方法都包含完整的错误处理和日志记录，便于调试和监控。
