"""
IF整合性确认工作流

V字对应：
2.5.26 I/F整合性確認

该模块提供接口整合性确认功能，检查代码中接口使用与IF设计书的一致性。
1. 从I/F设计书中提取IF一览中函数名、函数参数类型、值域范围等信息
2. 从git仓库下载代码，根据函数名从代码中查找所有的调用的源码片段
3. 根据调用源码片段内容，结合整个.c文件内容与I/F设计书该函数的约束条件由大模型分析调用参数是否合理，输出判定结果
4. 将判定结果整合保存在模板文件中，输出I/F整合性确认报告书
"""

from pathlib import Path
from typing import Dict, List, Any, Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.service.template_manager import template_manager
from sdw_agent.util.extract_code_util import CodeAnalyzer
from sdw_agent.util.file_base_util import create_text_file_link, gen_output_path
from sdw_agent.util.git_util import clone_repo_of_branch
from sdw_agent.config.env import ENV

from sdw_agent.service.if_integration_service.models import IFIntegrationConfigModel, IFStylebookInfo, CloneRepoInfo
from sdw_agent.service.if_integration_service.utils import if_stylebook_to_json, llm_integration, \
    IFIntegrationExcelUtil


@register_workflow("if_integration")
class IFIntegrationWorkflow(BaseWorkflow):
    """
    IF整合性确认工作流类
    
    提供检查代码中接口使用与IF设计书一致性的功能。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化IF整合性确认工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        # 调用父类构造方法，初始化通用属性
        super().__init__(config_path)

        # 注册配置模型，为后续的配置解析和验证做准备
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """
        注册配置模型用于验证

        本函数通过调用配置管理器，注册一个特定的配置模型，用于后续的配置验证
        """
        # 导入配置管理器类，用于处理工作流配置
        from sdw_agent.service.workflow_config import WorkflowConfigManager

        # 创建临时配置管理器实例，指定工作流名称为"if_integration"
        # 此步是为了验证特定工作流的配置模型
        config_manager = WorkflowConfigManager(workflow_name="if_integration")

        # 注册配置模型
        # 这里将"if_integration"工作流与IFIntegrationConfigModel配置模型绑定
        # 以便在验证阶段使用该模型对配置进行校验
        config_manager.register_schema("if_integration", IFIntegrationConfigModel)

    def validate_input(self, if_stylebook_obj: IFStylebookInfo, clone_repo_info: CloneRepoInfo) -> bool:
        """
        验证输入参数
        
        Args:
            if_stylebook_obj: IF设计书信息对象
            clone_repo_info: 克隆代码仓库信息对象
            
        Returns:
            bool: 验证是否通过
        """
        # 验证IF设计书
        try:
            # 获取IF设计书的URI并转换为Path对象
            if_stylebook = if_stylebook_obj.uri
            if_stylebook_path = Path(if_stylebook)
            # 检查IF设计书文件是否存在
            if not if_stylebook_path.exists():
                self.logger.error(f"IF设计书不存在: {if_stylebook}")
                return False

            # 验证文件类型
            # 从配置中获取允许的IF设计书文件扩展名列表
            allowed_exts = self.config.get("io", {}).get("input", {}).get(
                "if_stylebook_extensions", [".xlsx", ".xls"]
            )
            # 检查IF设计书文件扩展名是否在允许列表中
            if if_stylebook_path.suffix.lower() not in allowed_exts:
                self.logger.error(
                    f"不支持的IF设计书格式: {if_stylebook_path.suffix}, 支持的格式: {allowed_exts}"
                )
                return False
        except Exception as e:
            self.logger.error(f"验证IF设计书失败: {str(e)}")
            return False

        # 验证克隆仓库信息
        # 检查仓库URL是否为空
        if not clone_repo_info.repo_url:
            self.logger.error("仓库URL不能为空")
            return False

        # 检查仓库分支是否为空
        if not clone_repo_info.branch:
            self.logger.error("仓库分支不能为空")
            return False

        # 所有验证通过，返回True
        return True

    def execute(self, if_stylebook_obj: IFStylebookInfo, clone_repo_info: CloneRepoInfo) -> WorkflowResult:
        """
        执行IF整合性确认工作流
        
        Args:
            if_stylebook_obj: IF设计书信息对象
            clone_repo_info: 克隆代码仓库信息对象

        Returns:
            WorkflowResult: 工作流执行结果
        """
        # 记录工作流开始执行的信息
        self.logger.info(f"开始执行IF整合性确认工作流")
        # 记录IF设计书的来源信息
        self.logger.info(f"IF设计书: {if_stylebook_obj.uri}")
        # 记录代码仓库的来源信息
        self.logger.info(f"代码仓库: {clone_repo_info.repo_url}, 分支: {clone_repo_info.branch}")

        try:
            # 1. 获取IF设计书数据
            self.logger.info("步骤1: 获取IF设计书数据")
            # 从IF设计书中获取数据，排除特定名称的项
            if_stylebook_data = self._get_if_stylebook_data(if_stylebook_obj)
            if_data = [item for item in if_stylebook_data.keys() if "_s_" not in item]

            # 2. 克隆代码仓库
            self.logger.info("步骤2: 克隆代码仓库")
            # 克隆指定的代码仓库并获取本地路径
            code_path = self._clone_repo(clone_repo_info)

            # 3. 获取代码匹配信息
            self.logger.info("步骤3: 获取代码匹配信息")
            # 在克隆的代码中查找与IF设计书数据匹配的信息
            match_data, match_file = self._get_match_data(if_data, code_path)

            # 4. 确认集成结果
            self.logger.info("步骤4: 确认集成结果")
            # 根据匹配信息生成集成结果数据
            integration_data = self._make_integration_data(if_stylebook_obj, if_stylebook_data, match_data, match_file)

            # 5. 生成输出报告
            self.logger.info("步骤5: 生成输出报告")
            # 根据集成结果生成输出文件
            output_file = self._generate_output_file(integration_data)

            # 返回成功执行的结果
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message="IF整合性确认工作流执行成功",
                data={
                    "output_file": output_file,
                    "integration_count": len(integration_data)
                }
            )

        except Exception as e:
            # 记录异常信息并返回失败执行的结果
            self.logger.exception(f"IF整合性确认工作流执行异常")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"IF整合性确认工作流执行失败: {str(e)}",
                error=str(e)
            )

    def _get_if_stylebook_data(self, if_stylebook_obj: IFStylebookInfo) -> Dict[str, Any]:
        """
        获取IF设计书数据
        
        Args:
            if_stylebook_obj: IF设计书信息对象
            
        Returns:
            Dict[str, Any]: IF设计书数据
        """
        # 获取IF设计书的URI
        if_stylebook = if_stylebook_obj.uri
        # 从配置中获取工作表名称，默认为“IF一覧”
        sheet_name = self.config.get("processing", {}).get("if_stylebook", {}).get("sheet_name", "IF一覧")
        # 从配置中获取接口名称的关键字，默认为“I/F名”
        if_name_keyword = self.config.get("processing", {}).get("if_stylebook", {}).get("if_name_keyword", "I/F名")

        # 记录IF设计书的数据提取开始日志
        self.logger.info(f"从IF设计书提取数据, 文件: {if_stylebook}, 工作表: {sheet_name}")
        # 调用函数将IF设计书转换为JSON格式的数据
        if_stylebook_data = if_stylebook_to_json(if_stylebook, sheet_name, if_name_keyword)
        # 记录成功提取数据的日志，包括接口数量
        self.logger.info(f"成功提取IF设计书数据, 共 {len(if_stylebook_data)} 个接口")

        # 返回提取的IF设计书数据
        return if_stylebook_data

    def _clone_repo(self, clone_repo_info: CloneRepoInfo) -> str:
        """
        克隆代码仓库
        
        Args:
            clone_repo_info: 克隆代码仓库信息对象
            
        Returns:
            str: 代码路径
        """
        # 获取代码仓库的URL
        repo_url = clone_repo_info.repo_url
        # 获取需要克隆的分支名称
        branch = clone_repo_info.branch
        # 使用提供的用户名或环境变量中的默认用户名
        username = clone_repo_info.username or ENV.config.gerrit.username
        # 使用提供的密码或环境变量中的默认密码
        password = clone_repo_info.password or ENV.config.gerrit.password
        # 获取输出路径
        output_path = ENV.config.output_data_path

        # 记录克隆代码仓库的日志信息
        self.logger.info(f"克隆代码仓库, URL: {repo_url}, 分支: {branch}")
        # 执行克隆操作，并获取克隆后的代码路径
        code_path = clone_repo_of_branch(repo_url, branch, output_path, username, password)
        # 记录克隆成功后的日志信息
        self.logger.info(f"代码仓库克隆成功, 路径: {code_path}")

        # 返回克隆后的代码路径
        return code_path

    def _get_match_data(self, if_data: List[str], code_path: str) -> tuple:
        """
        获取代码匹配信息
        
        Args:
            if_data: 接口数据列表
            code_path: 代码路径
            
        Returns:
            tuple: (匹配数据, 匹配文件)
        """
        # 记录查找接口使用情况的开始日志
        self.logger.info(f"从代码中查找接口使用情况, 代码路径: {code_path}")

        # 调用get_content_from_code函数从代码中获取匹配数据和匹配文件
        match_data, match_file = CodeAnalyzer().get_content_from_code(if_data, code_path)

        # 记录查找完成的日志，并指出找到匹配的文件数量
        self.logger.info(f"接口使用情况查找完成, 找到 {len(match_data)} 个文件中的匹配")

        # 返回匹配数据和匹配文件
        return match_data, match_file

    def _make_integration_data(
            self,
            if_stylebook_obj: IFStylebookInfo,
            if_stylebook_data: Dict[str, Any],
            match_data: Dict[str, List],
            match_file: Dict[str, str]
    ) -> List[List[str]]:
        """
        生成集成数据
        
        Args:
            if_stylebook_obj: IF设计书信息对象
            if_stylebook_data: IF设计书数据
            match_data: 匹配数据
            match_file: 匹配文件
            
        Returns:
            List[List[str]]: 集成数据
        """
        # 获取IF设计书的名称
        if_stylebook_name = self._get_stylebook_name(if_stylebook_obj)
        # 获取集成配置
        config = self._get_integration_config()

        # 准备LLM任务
        llm_tasks = self._prepare_llm_tasks(match_data, match_file, if_stylebook_data)
        self.logger.info(f"准备并行处理 {len(llm_tasks)} 个LLM任务")

        # 并行处理LLM任务
        llm_results = self._process_llm_tasks_parallel(llm_tasks, config)
        self.logger.info(f"llm_results:{llm_results}")

        # 构建最终结果
        integration_data = self._build_integration_data(
            match_data, llm_results, if_stylebook_name, config["default_status"]
        )
        self.logger.info(f"集成数据生成完成，共处理 {len(integration_data)} 条记录")
        return integration_data

    @staticmethod
    def _get_stylebook_name(if_stylebook_obj: IFStylebookInfo) -> str:
        """
        获取IF设计书名称

        通过IFStylebookInfo对象中的URI信息提取设计书名称

        参数:
        if_stylebook_obj (IFStylebookInfo): IF设计书信息对象

        返回:
        str: 设计书的名称
        """
        # 获取IF设计书的URI
        if_stylebook = if_stylebook_obj.uri
        # 通过Path的name属性获取URI中的文件名，如果为空，则通过split方法获取最后一个路径段作为名称
        return Path(if_stylebook).name or if_stylebook.split('/')[-1]

    def _get_integration_config(self) -> Dict[str, Any]:
        """获取集成配置

        该方法从配置字典中提取与集成相关的信息，包括状态值、默认状态和最大工作线程数
        """
        # 从配置中获取集成配置部分，如果不存在，则默认为空字典
        integration_config = self.config.get("processing", {}).get("integration", {})

        # 构建并返回集成配置字典
        return {
            # 获取状态值列表，如果没有配置，则使用默认值["OK", "NG", "Pending"]
            "status_values": integration_config.get("status_values", ["OK", "NG", "Pending"]),
            # 获取默认状态值，如果没有配置，则使用默认值"Pending"
            "default_status": integration_config.get("default_status", "Pending"),
            # 获取最大工作线程数，如果没有配置，则使用默认值8，并确保不超过50
            "max_workers": min(
                self.config.get("processing", {}).get("max_workers", 8),
                50  # 限制最大并发数
            )
        }

    @staticmethod
    def _prepare_llm_tasks(
            match_data: Dict[str, List],
            match_file: Dict[str, str],
            if_stylebook_data: Dict[str, Any]
    ) -> List[tuple]:
        """
        准备LLM处理任务

        该函数的作用是根据匹配的数据和文件内容，以及接口风格书数据，准备并返回一系列供LLM处理的任务
        每个任务包含了一个唯一的任务ID和任务所需的数据

        参数:
        match_data: 包含匹配结果的字典，键为文件名，值为匹配到的数据列表
        match_file: 包含文件内容的字典，键为文件名，值为文件内容字符串
        if_stylebook_data: 接口风格书数据，键为接口名称，值为该接口的相关数据

        返回:
        llm_tasks: 一个元组列表，每个元组包含任务ID和任务数据
        """
        # 初始化LLM任务列表
        llm_tasks = []

        # 遍历匹配数据字典，键为文件名，值为匹配到的数据列表
        for file_name, data in match_data.items():
            # 从match_file字典中获取当前文件的内容
            file_content = match_file[file_name]
            # 遍历匹配到的数据列表，同时使用enumerate函数获取索引作为任务序号
            for index, match_line in enumerate(data, start=1):
                # 从匹配行中提取匹配到的模式名称
                if_name = match_line["matched_pattern"]
                # 从接口风格书数据中获取对应接口的数据，如果不存在，则使用空字典
                if_stylebook_entry = if_stylebook_data.get(if_name, {})

                # 构造任务ID，由文件名和任务序号组成，确保唯一性
                task_id = f"{file_name}_{index}"
                # 构造任务数据，包含匹配行数据、文件内容和接口风格书数据
                task_data = (match_line, file_content, if_stylebook_entry)
                # 将任务ID和任务数据组成的元组添加到LLM任务列表中
                llm_tasks.append((task_id, task_data))

        # 返回构造好的LLM任务列表
        return llm_tasks

    def _process_llm_tasks_parallel(self, llm_tasks: List[tuple], config: Dict[str, Any]) -> Dict[str, str]:
        """
        并行处理LLM任务

        使用线程池并行处理多个LLM任务，并安全地收集和返回所有任务的处理结果。

        参数:
        llm_tasks: 包含多个LLM任务信息的列表，每个任务信息为一个元组。
        config: 包含配置信息的字典，用于控制任务处理过程中的参数。

        返回:
        llm_results: 包含所有任务处理结果的字典，键为任务ID，值为处理结果。
        """
        from concurrent.futures import ThreadPoolExecutor
        import threading

        # 初始化用于存储处理结果的字典和线程安全锁
        llm_results = {}
        result_lock = threading.Lock()

        # 根据任务数量和配置中的最大工作线程数，确定实际使用的最大工作线程数
        max_workers = min(len(llm_tasks), config["max_workers"])

        def process_single_task(task_info):
            """
            处理单个LLM任务

            尝试执行单个LLM任务并捕获可能的异常，确保结果线程安全地更新到共享字典中。

            参数:
            task_info: 包含任务ID和任务具体内容的元组。

            返回:
            任务ID和处理结果的元组。
            """
            task_id, (match_line, file_content, if_stylebook_entry) = task_info
            try:
                # 执行LLM任务并获取结果
                result = llm_integration(match_line, file_content, if_stylebook_entry)
                # 线程安全地更新共享结果字典
                with result_lock:
                    llm_results[task_id] = result
                return task_id, result
            except Exception as e:
                # 异常处理：记录错误日志，并设置任务结果为默认状态
                self.logger.error(f"LLM处理任务 {task_id} 失败: {e}")
                with result_lock:
                    llm_results[task_id] = config["default_status"]
                return task_id, config["default_status"]

        # 使用线程池执行任务
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池，并保存每个任务的未来对象到字典中，以便监控任务进度
            future_to_task = {
                executor.submit(process_single_task, task_info): task_info[0]
                for task_info in llm_tasks
            }

            # 调用内部方法监控任务进度
            self._monitor_task_progress(future_to_task, len(llm_tasks))

        # 返回所有任务的处理结果
        return llm_results

    def _monitor_task_progress(self, future_to_task: Dict, total_tasks: int) -> None:
        """
        监控任务进度

        使用concurrent.futures的as_completed函数来监控任务的完成情况，并记录进度日志
        当完成的任务数达到总数的倍数或所有任务完成时，记录一次日志

        参数:
        future_to_task: Dict - 一个字典，键是future对象，值是任务ID
        total_tasks: int - 总任务数

        返回值:
        None
        """
        from concurrent.futures import as_completed

        # 初始化已完成任务计数器
        completed_tasks = 0
        # 遍历完成的任务
        for future in as_completed(future_to_task):
            try:
                # 增加已完成任务数量
                completed_tasks += 1
                # 当完成的任务数达到10的倍数或所有任务完成时，记录日志
                if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                    self.logger.info(f"LLM处理进度: {completed_tasks}/{total_tasks}")
            except Exception as e:
                # 获取出错任务的ID
                task_id = future_to_task[future]
                # 记录错误日志
                self.logger.error(f"处理任务 {task_id} 时发生错误: {e}")

    def _build_integration_data(
            self,
            match_data: Dict[str, List],
            llm_results: Dict[str, str],
            if_stylebook_name: str,
            default_status: str
    ) -> List[List[str]]:
        """
        构建集成数据

        该函数根据匹配数据和语言模型结果生成集成数据，用于后续处理和分析。

        参数:
            match_data (Dict[str, List]): 匹配数据，键为文件名，值为匹配到的数据列表
            llm_results (Dict[str, str]): 语言模型结果，键为任务ID，值为结果字符串
            if_stylebook_name (str): 配置项风格簿名称
            default_status (str): 默认状态

        返回:
            List[List[str]]: 集成数据，键为工作表名称，值为行数据列表
        """
        # 初始化集成数据字典
        integration_data = []

        # 遍历匹配数据，构建集成数据
        for file_name, data in match_data.items():
            # 确保工作表名称作为键存在于集成数据中，并初始化为空列表
            # 计算当前工作表中数据的起始索引
            start = len(integration_data)
            # 遍历当前文件的数据，构建行数据
            for index, match_line in enumerate(data, start=1):
                # 生成任务ID
                task_id = f"{file_name}_{index}"
                # 根据任务ID获取确认结果，如果没有则使用默认状态
                confirm_result = llm_results.get(task_id, default_status)

                # 创建行数据
                row_data = self._create_row_data(
                    start + index, match_line, file_name, confirm_result, if_stylebook_name
                )
                # 将行数据添加到当前工作表中
                integration_data.append(row_data)

        # 返回构建好的集成数据
        return integration_data

    @staticmethod
    def _generate_sheet_name(file_name: str) -> str:
        """
        生成工作表名称

        根据文件名生成一个合适的工作表名称。为了防止工作表名称过长，
        只使用文件名的前28个字符，并添加" IF"后缀，以指示这是经过特定处理的文件。

        参数:
        file_name: str - 文件名，用作生成工作表名称的基础

        返回:
        str - 生成的工作表名称
        """
        # 使用文件名的前28个字符加上" IF"后缀作为工作表名称
        return file_name.split('.')[0][:28] + " IF"

    @staticmethod
    def _create_row_data(
            index: int,
            match_line: Dict,
            file_name: str,
            confirm_result: str,
            if_stylebook_name: str
    ) -> List[str]:
        """
        创建行数据

        参数:
        - index: int 给定的序号
        - match_line: Dict 包含匹配信息的字典
        - file_name: str 文件名
        - confirm_result: str 确认结果
        - if_stylebook_name: str IF设计书名称

        返回:
        - List[str] 一个包含行数据的字符串列表
        """
        text_file_link = create_text_file_link(match_line["file_path"], match_line["line_num"], "vscode")
        return [
            str(index),  # 序号
            match_line["matched_pattern"],  # IF名称
            {"url": text_file_link, "text": f'{file_name}:{match_line["line_num"]}'},  # 代码位置
            match_line["line"],  # 代码内容
            confirm_result,  # 确认结果
            if_stylebook_name,  # IF设计书
            "-",  # IF版本
            "-",  # 确认日期
            "DEV AGENT",  # 确认者
            "",  # 备注
            "-"  # 状态
        ]

    def _generate_output_file(self, integration_data: List[List[str]]) -> str:
        """
        生成输出文件
        
        Args:
            integration_data: 集成数据
            
        Returns:
            str: 输出文件路径
        """
        # 获取输出目录
        output_dir = self.config.get("io", {}).get("output", {}).get(
            "default_output_dir", ENV.config.output_data_path
        )

        # 获取报告基础名称
        report_base_name = self.config.get("io", {}).get("output", {}).get(
            "report_base_name", "IF整合性CS兼結果報告書"
        )

        # 生成唯一的文件名
        output_file = gen_output_path(output_dir, report_base_name)
        template_file = template_manager.get_template_path("if_integration_file")
        excel_style = self.config.get("processing", {}).get("output_excel_style", {})
        # 保存到Excel
        excel_util = IFIntegrationExcelUtil(
            output_file=output_file,
            template_file=template_file,
            excel_style=excel_style,
            engine="win32com"  # 使用win32com引擎以保持模板格式
        )
        # 使用上下文管理器确保资源正确释放
        with excel_util:
            excel_util.save_integration_data(integration_data)

        self.logger.info(f"集成结果已保存到: {output_file}")

        return output_file


def do_if_integration(if_stylebook_obj, clone_repo_info):
    """
    执行IF整合性确认(兼容旧接口)
    
    Args:
        if_stylebook_obj: IF设计书对象
        clone_repo_info: 克隆代码仓库信息对象
        
    Returns:
        str: 输出文件路径
    """
    # 初始化IF整合性工作流
    workflow = IFIntegrationWorkflow()

    # 执行IF整合性工作流并获取结果
    result = workflow.run(if_stylebook_obj, clone_repo_info)

    # 根据工作流执行结果判断是否成功
    if result.status == WorkflowStatus.SUCCESS:
        # 如果成功，返回输出文件路径
        return result.data["output_file"]
    else:
        # 如果失败，抛出异常并返回错误信息
        raise Exception(result.message)
