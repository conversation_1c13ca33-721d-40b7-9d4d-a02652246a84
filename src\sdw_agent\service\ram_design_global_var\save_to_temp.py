"""
@File    : save_to_temp.py
@Time    : 2025/7/16 22:01
<AUTHOR> qiliang.zhou
@Contact : <EMAIL>
@Desc    : 
"""

from pathlib import Path
from loguru import logger
import win32com.client as win32

SHEET_NAME = "データ構造"
START_ROW = 5
START_COL = 2

WRITE_SEQ = [
    'domain',
    'component',
    'data_type',
    'name',
    'struct_or_union',
    'member_data_type',
    'member_bit_len',
    'member_name',
    'var_desc',
    'b_backup',
    'min',
    'max',
    'lsb',
    'unit'
]

class ExcelApp:
    """上下文管理器，用于安全启动和关闭 Excel 应用程序"""

    def __init__(self, visible=False):
        self.excel = win32.DispatchEx("Excel.Application")
        self.excel.Visible = visible
        self.excel.DisplayAlerts = False  # 禁止弹窗提示

    def __enter__(self):
        return self.excel

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.excel.Quit()
        del self.excel

def save_to_excel(to_save_data, file_path):
    """
    将集成数据保存到Excel文件中（使用 win32com 实现）。

    参数:
    ram_var_data (list): 包含ram 全局变量的对象list，用于填充到Excel模板中。
    file_path (str): Excel文件的保存路径，包括文件名和扩展名。
    """
    # 模板文件路径
    template_path = Path(__file__).parent / "temp" / "RAM設計書_Dbus.xls"
    # 定义常量用于工作表名称

    # 使用ExcelApp打开Excel应用程序，不显示界面
    with ExcelApp(visible=False) as excel:
        # 打开模板文件
        wb = excel.Workbooks.Open(template_path)

        # 获取指定名称的工作表
        ws = wb.Sheets(SHEET_NAME)

        # 填充工作表数据
        _fill_sheet_data(ws, to_save_data)

        # 合并单元格操作
        # 对第2、3列合并单元格从第5行开始
        merge_cells_by_columns(ws, [2, 3], start_row=5)

        # 保存工作簿到指定路径
        wb.SaveAs(file_path)


def _fill_sheet_data(sheet, data):
    """
    填充工作表数据

    根据给定的数据填充工作表。每行数据在工作表中占用4行，以适应特定的格式需求。

    参数:
    - sheet: 工作表对象，用于操作Excel文件中的工作表。
    - data: 要填充到工作表的数据，是一个二维列表，其中每个列表代表一行数据。
    """
    # 从第指定行开始填充数据，因为前面的行可能包含标题或其他固定内容
    for row_index, obj in enumerate(data, start=START_ROW):
        for col_idx, attr in enumerate(WRITE_SEQ, start=START_COL):
            value = getattr(obj, attr, "")
            if isinstance(value, list):
                if not value:
                    value = '-'
                else:
                    value = ', '.join(str(v) for v in value)
            _fill_column(sheet, row_index, col_idx, value)


def _fill_column(sheet, row_index, col_idx, value):
    """
    填充特殊列：仅在指定行号写入内容

    参数:
    sheet: 工作表对象，用于操作Excel的工作表
    row_index: 行号，仅在该行写入 value
    col_idx: 列索引，指定需要填充的列
    value: 填充值，首行将填充此值
    """

    # 获取当前单元格
    cell = sheet.Cells(row_index, col_idx)
    # 根据行号决定单元格的值：指定行填充 value
    cell.Value = value


def merge_cells_by_columns(ws, cols, start_row=START_ROW, end_row=None):
    """
    对ws的cols列做分组合并，后面的列合并范围不能超出前一列的合并范围（层级合并）。
    例如cols=[2,3]，则第3列的合并范围是第2列分组内的子分组。
    注意：只有单元格有值时才进行合并操作。

    :param ws: worksheet对象
    :param cols: 需要合并的列索引（1-based），如[2,3]
    :param start_row: 数据起始行
    :param end_row: 数据结束行（默认自动检测到最后一行）
    """
    if end_row is None:
        # 自动检测最后一行（假设第一列有数据）
        end_row = ws.UsedRange.Rows.Count

    def merge_range(col, row_start, row_end):
        """合并指定列的行区间单元格（仅当区间大于1行且单元格有值）"""
        if row_end > row_start:
            # 检查起始单元格是否有值
            start_val = ws.Cells(row_start, col).Value
            if start_val is not None and str(start_val).strip():
                ws.Range(ws.Cells(row_start, col), ws.Cells(row_end, col)).Merge()

    def recursive_merge(col_idx, row_start, row_end):
        """
        递归分组合并：对当前列在[row_start, row_end]区间内分组，
        并对子列在每个分组内递归分组合并。
        只有单元格有值时才进行合并。
        """
        col = cols[col_idx]
        curr_val = None
        group_start = row_start
        for r in range(row_start, row_end + 1):
            val = ws.Cells(r, col).Value
            # 跳过空值或None
            # if val is None or str(val).strip() == "":
            #     break

            if val != curr_val or val is None or str(val).strip() == "":
                if curr_val is not None:
                    merge_range(col, group_start, r - 1)
                    # 递归对子列分组
                    if col_idx + 1 < len(cols):
                        recursive_merge(col_idx + 1, group_start, r - 1)
                curr_val = val
                group_start = r
        # 处理最后一组
        if curr_val is not None:
            merge_range(col, group_start, row_end)
            if col_idx + 1 < len(cols):
                recursive_merge(col_idx + 1, group_start, row_end)

    if cols:
        recursive_merge(0, start_row, end_row)
