import copy
import re
import traceback

from fastapi_babel import _ as gettext
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from loguru import logger

from sdw_agent.llm import kt_azure_azure_gpt4o as azure

__all__ = [
    'extract_result',
    'extract_json_info',
    'extract_json_by_regex',
    'extract_plantuml_by_regex',
    'extract_think',
    'preprocess_seq_diagram',
    'parse_line_sequence_diagram',
    'extract_req_name'
]

from enum import Enum


class ParseMessage(Enum):
    # 解析时序图中每条message的正则表达式
    parseMessageRegular = r'^([^\->]+)\s*->\s*([^:]+):\s*(?:([^:]+)::)?([^\n]+)\n?([^\(]+)(?:\(([^$]*)\))?(?:\nreturn\s+([^\n]+))?'


def extract_result(text):
    # 使用正则表达式提取内容
    pattern = r"<result>([\s\S]*?)</result>"
    result = re.search(pattern, text)
    if result and result.group(1):
        return result.group(1).strip()


def extract_think(text):
    # 使用正则表达式提取内容
    pattern = r"<think>([\s\S]*?)</think>"
    result = re.search(pattern, text)
    if result and result.group(1):
        return result.group(1).strip()


def extract_req_name(uri):
    """
    从uri中提取出需求名称（不包括路径和扩展名）
    :param uri:
    :return:
    """
    # 使用正则表达式提取文件名（不包括扩展名）
    pattern = r"[^/\\]+(?=\.[^.]*$)|[^/\\]+$"
    result = re.search(pattern, uri)
    if result:
        return result.group().strip()


def extract_json_by_regex(content):
    """
    从文本中提取json格式的内容信息
    :content: 一段通用文本
    :obj: pydantic BaseModel 对象
    :return:抽取到的对象实例
    """
    pattern = r"```(json)?\n(.*?)\n```"
    match = re.search(pattern, content, re.DOTALL)
    if match:
        json_str = match.group(2)
        return json_str
    else:
        return None


def extract_markdown_by_regex(content):
    """
    从文本中提取markdown格式的内容信息
    :content: 一段通用文本
    :obj: pydantic BaseModel 对象
    :return:抽取到的对象实例
    """
    pattern = r"```(markdown)?\n(.*?)\n```"
    match = re.search(pattern, content, re.DOTALL)
    if match:
        json_str = match.group(2)
        return json_str
    else:
        return content


def extract_plantuml_by_regex(content):
    """
    从文本中提取json格式的内容信息
    :content: 一段通用文本
    :obj: pydantic BaseModel 对象
    :return:抽取到的对象实例
    """
    pattern = r"```(plantuml)?\n(.*?)\n```"
    match = re.search(pattern, content, re.DOTALL)
    if match:
        json_str = match.group(2)
        return json_str
    else:
        return content


def extract_json_info(content, obj):
    """
    从文本中提取json格式的内容信息
    :content: 一段通用文本
    :obj: pydantic BaseModel 对象

    :return:抽取到的对象实例
    """
    prompt = ChatPromptTemplate(
        [
            ("user", """
                        你是一个信息提取专家，你擅长分析文本并从中提取信息，
                        现在，我将给你一段文本内容，帮我从中提取出结构化的json格式的信息
                        接口信息文本：
                        {{content}}
                        """),
        ],
        template_format="mustache",
    )

    chain = {"content": RunnablePassthrough()} | prompt | azure.with_structured_output(obj).with_config(
        tags=["inner"]
    )
    result = chain.invoke({'content': content})
    return result


def extract_participant_in_uml(content):
    """
    从plantuml的时序图中提取组件/单元
    :param content:
    :return:
    """
    # 解析plantuml中的participant
    participants = []
    for line in content.split('\n'):
        line = line.strip()
        if line.startswith('participant'):
            # 移除participant关键字
            participant_part = line.replace('participant', '').strip()

            # 处理带引号的情况
            if '"' in participant_part:
                # 提取引号中的实际名称
                actual_name = participant_part.split('"')[1]
                # 检查是否有as别名
                if ' as ' in participant_part:
                    # 使用实际名称，忽略别名
                    participants.append(actual_name)
                else:
                    participants.append(actual_name)
            else:
                # 不带引号的情况
                parts = participant_part.split(' as ')
                if len(parts) > 1:
                    # 有别名，使用第一部分作为实际名称
                    participants.append(parts[0].strip())
                else:
                    # 没有别名，直接使用第一个词
                    participants.append(parts[0].split()[0].strip())

    # 处理name中有\n的情况，如DispFW_swdtctr\n(50ms)
    result = []
    for participant in participants:
        result.append(participant.split("\\n")[0])
    return result


def parse_line_sequence_diagram(content):
    """
    从时序图语句中提取信息
    格式：关联组件名->目标组件名:[通信方式::]接口描述\n接口名称([入参名称1:入参值1,入参名称2:入参值2])\n[return 返回值信息]
    其中[]内容表示可以根据实际情况省略
    :param content:
    :return:
    """
    # print("=================================")
    # print(content)
    # print("=================================")
    content = content.replace('\\n', '\n')
    # 正则表达式模式 匹配结果
    match = re.match(ParseMessage.parseMessageRegular.value, content)
    extract_res = {
        "source_participant": "",
        "target_participant": "",
        "communication_method": "",
        "interface_description": "",
        "interface_name": "",
        "input_params": "",
        "return_value": "",
    }

    if match:
        # 提取各组信息
        extract_res["source_participant"] = match.group(1).strip()
        extract_res["target_participant"] = match.group(2).strip()
        extract_res["communication_method"] = match.group(3).strip() if match.group(3) else None
        extract_res["interface_description"] = match.group(4).strip()
        extract_res["interface_name"] = match.group(5).strip()
        extract_res["input_params"] = match.group(6).strip() if match.group(6) else None
        extract_res["return_value"] = match.group(7).strip() if match.group(7) else None
    else:
        logger.error(f"{content} 未匹配到有效信息")
    return extract_res


def parse_interface_params(input_params) -> dict:
    """
    解析接口的参数
    :param input_params:
    :return:
    """
    try:
        # 正则表达式匹配键值对（兼容键的字母/数字/下划线，值的任意非逗号字符）
        pattern = r"(\w+)\s*:\s*([^,]+)"
        matches = re.findall(pattern, input_params)
        params_dict = {key.strip(): value.strip() for key, value in matches}
        return params_dict

    except Exception as e:
        logger.error(input_params)
        logger.error(traceback.format_exc())


def extract_interface_in_uml(content, name_labels_map: dict) -> list:
    """
    从时序图中提取接口信息
    :param content:
    :param name_labels_map: 参与者在neo4j中的name, labels
    :return:
    """

    interfaces = []
    for line in content.split('\n'):
        line = line.strip()
        if line.startswith('participant') or line.startswith("'") or line.startswith("box") or line == "":
            continue

        extract_line_res = parse_line_sequence_diagram(line)
        # 自己调用自己的接口，可以忽略
        if extract_line_res.get("source_participant") == extract_line_res["target_participant"]:
            continue
        if extract_line_res["source_participant"] in name_labels_map.keys() and extract_line_res[
            "target_participant"] in name_labels_map.keys():
            # 只考虑组件提供的接口
            if ("SoftwareComponent" in name_labels_map[extract_line_res["source_participant"]]) and (
                    "SoftwareComponent" in name_labels_map[extract_line_res["target_participant"]]):
                temp_extract_line = copy.deepcopy(extract_line_res)
                temp_extract_line["message"] = line.strip()
                interfaces.append(temp_extract_line)

    interfaces_res = []
    for interface in interfaces:
        interfaces_res.append({
            "interface_provider": interface["target_participant"],
            "interface_description": interface["interface_description"],
            'interface_name': interface["interface_name"],
            'communication_method': interface["communication_method"],
            'input_params': interface["input_params"],
            'return_value': interface["return_value"],
            'message': interface["message"]
        })

    return interfaces_res


def extract_change_ar_no(file_path):
    """
    从需求变更描述文件中提取ar 票号
    :param file_path: 文件路径
    :return:
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 查找ar票号部分
        start_marker = "## " + gettext("AR票号")
        if start_marker in content:
            # 获取ar票号部分后面的内容
            ar_no_section = content.split(start_marker)[1].strip()

            # 提取ar票号
            ar_no = ar_no_section.split('\n')[0].strip()
            return ar_no
        else:
            return ""
    except Exception as e:
        logger.error(f"读取文件时发生错误: {e}")
        return ""


def extract_change_type(file_path):
    """
    从需求变更描述文件中提取变更类型

    参数:
        file_path (str): 文件路径

    返回:
        str: 变更类型，如果没有找到则返回空字符串
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 查找变更类型部分
        start_marker = "## " + gettext("变更类型")
        if start_marker in content:
            # 获取变更类型部分后面的内容
            change_type_section = content.split(start_marker)[1].strip()

            # 提取变更类型（假设变更类型在该部分的第一行）
            change_type = change_type_section.split('\n')[0].strip()
            return change_type
        else:
            return ""
    except Exception as e:
        logger.error(f"读取文件时发生错误: {e}")
        return ""


def extract_change_info(file_path):
    """
    从需求变更描述文件中提取变更概要

    参数:
        file_path (str): 文件路径

    返回:
        str: 变更概要，如果没有找到则返回空字符串
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 查找变更类型部分
        start_marker = "## " + gettext("変更内容") + "\n"
        if start_marker in content:
            # 获取变更类型部分后面的内容
            change_type_section = content.split(start_marker)[1].strip()

            # 提取变更类型（假设变更类型在该部分的第一行）
            change_type = change_type_section.split('\n')[0].strip()
            return change_type
        else:
            return ""
    except Exception as e:
        logger.error(f"读取文件时发生错误: {e}")
        return ""


def preprocess_seq_diagram(content, affected_participants: list) -> str:
    """
    预处理时序图
    根据组件/单元的影响分析报告，基于报告中的【组件/单元】对时序图进行预处理
    在组件/单元提供的接口信息上面添加一条注释信息，表示该条时序图可能会变化
    :param content:
    :return:
    """
    new_contents_list = []
    start_message = "'<changed>This message needs to be modified or deleted according to the actual situation."
    end_message = "'</changed>"
    for line in content.split('\n'):
        line = line.strip()
        if line.startswith('participant') or line.startswith("'") or line.startswith("box") or line == "":
            new_contents_list.append(line)
            continue
        extract_line_res = parse_line_sequence_diagram(line)
        if extract_line_res["target_participant"] in affected_participants:
            new_contents_list.append(start_message)
            new_contents_list.append(line)
            new_contents_list.append(end_message)
        else:
            new_contents_list.append(line)

    return '\n'.join(new_contents_list)


def find_affected_messages_in_seq(content, affected_participants: list) -> list:
    """
    寻找时序图中受影响的messages
    根据组件/单元的影响分析报告，基于报告中的【组件/单元】对时序图进行提取
    寻找可能受影响的messages信息
    :param content:
    :return:
    """
    affected_messages_list = []
    # 行数从1开始
    for index, line in enumerate(content.split('\n')):
        processed_line = line.strip()
        if processed_line.startswith('participant') or processed_line.startswith("'") or processed_line.startswith(
                "box") or processed_line == "":
            continue
        extract_line_res = parse_line_sequence_diagram(processed_line)
        if extract_line_res["target_participant"] in affected_participants:
            affected_messages_list.append({
                "line_number": index + 1,
                "message": line,
            })

    return affected_messages_list


if __name__ == '__main__':
    path = r"C:\Users\<USER>\Desktop\TDD\Roc_Doc\02DESIGN\Sample_DATA_20250408\02_ArchDesign\CR7_System\ComponentSequence\1500Wor2400Wor7200W从0阶层变更为1阶层\1500Wor2400Wor7200W从0阶层变更为1阶层_变更前.pu"
    with open(path, 'r', encoding='utf-8') as f:
        content = f.read()

    res = find_affected_messages_in_seq(content, ["Dsp_App"])
    logger.info(res)
