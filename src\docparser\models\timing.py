from .base_object import BaseObject
from .coordinate import CoordinateObject
from .layout import LayoutObject
from .text import TextObject


class TimingWaveObject(BaseObject):
    def __init__(self):
        self._type = "timing_wave"  # timing_wave 波形图数据类型
        self._name = ""  # 时序图名称
        self._one_mean = ""  # 波形图中1的含义
        self._zero_mean = ""  # 波形图中0的含义
        self._data = []  # 时序图的数据对象TimingDataObject
        self._content = []  # 其它的内容组成列表，当前是文本对象列表
        self._layout = LayoutObject()  # 布局信息
        self._coordinate = CoordinateObject()  # 坐标
        self._data_id = None  # 唯一标识


    def to_dict(self):
        """
        将 TimingWaveObject 对象转换为字典
        """
        return {
            "type": self._type,
            "name": self._name,
            "one_mean": self._one_mean,
            "zero_mean": self._zero_mean,
            "data": [item.to_dict() for item in self._data],  # 递归转换 TimingDataObject 列表
            "content": [item.to_dict() for item in self._content],  # 递归转换 content 列表中的文本对象
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
            "data_id": self._data_id
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TimingWaveObject 实例
        """
        obj = cls()
        obj._type = data.get("type", "timing_wave")
        obj._name = data.get("name", "")
        obj._one_mean = data.get("one_mean", "")
        obj._zero_mean = data.get("zero_mean", "")
        obj._data = [TimingDataObject.from_dict(item) for item in data.get("data", [])]  # 恢复 TimingDataObject 列表
        obj._content = [TextObject.from_dict(item) for item in data.get("content", [])]  # 恢复 TextObject 列表
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        obj._data_id = data.get("data_id", None)

        return obj

    @property
    def data_id(self):
        return self._data_id

    @data_id.setter
    def data_id(self, new_value):
        assert type(new_value) == int
        self._data_id = new_value

    @property
    def type(self):
        return self._type

    @type.setter
    def type(self, new_value):
        self._type = new_value

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, new_value):
        self._name = new_value

    @property
    def one_mean(self):
        return self._one_mean

    @one_mean.setter
    def one_mean(self, new_value):
        self._one_mean = new_value

    @property
    def zero_mean(self):
        return self._zero_mean

    @zero_mean.setter
    def zero_mean(self, new_value):
        self._zero_mean = new_value

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, new_value):
        self._data = new_value

    @property
    def content(self):
        return self._content

    @content.setter
    def content(self, new_value):
        self._content = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value


class TimingTextObject(BaseObject):
    def __init__(self):
        self._type = "timing_text"  # timing_text 按照行处理的文本对象
        self._name = ""  # 时序图名称
        self._data = []  # 时序图中的文本类型的数据对象TimingTextDataObject
        self._content = []  # 其它的内容组成列表，当前是文本对象列表
        self._layout = LayoutObject()  # 布局信息
        self._coordinate = CoordinateObject()  # 坐标
        self._data_id = None  # 唯一标识

    def to_dict(self):
        """
        将 TimingTextObject 对象转换为字典
        """
        return {
            "type": self._type,
            "name": self._name,
            "data": [item.to_dict() for item in self._data],  # 递归转换 TimingTextDataObject 列表
            "content": [item.to_dict() for item in self._content],  # 递归转换 content 列表中的 TextObject
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
            "data_id": self._data_id
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TimingTextObject 实例
        """
        obj = cls()
        obj._type = data.get("type", "timing_text")
        obj._name = data.get("name", "")
        obj._data = [TimingTextDataObject.from_dict(item) for item in data.get("data", [])]  # 恢复 TimingTextDataObject 列表
        obj._content = [TextObject.from_dict(item) for item in data.get("content", [])]  # 恢复 TextObject 列表
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        obj._data_id = data.get("data_id", None)

        return obj

    @property
    def data_id(self):
        return self._data_id

    @data_id.setter
    def data_id(self, new_value):
        assert type(new_value) == int
        self._data_id = new_value

    @property
    def type(self):
        return self._type

    @type.setter
    def type(self, new_value):
        self._type = new_value

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, new_value):
        self._name = new_value

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, new_value):
        self._data = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value

    @property
    def content(self):
        return self._content

    @content.setter
    def content(self, new_value):
        self._content = new_value


class TimingDataObject(BaseObject):
    def __init__(self):
        self._index = ''  # 序号
        self._data = -1  # 时序图的数据内容
        self._layout = LayoutObject()  # 布局信息
        self._coordinate = CoordinateObject()  # 坐标

    def to_dict(self):
        """
        将 TimingDataObject 对象转换为字典
        """
        return {
            "index": self._index,
            "data": self._data,
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TimingDataObject 实例
        """
        obj = cls()
        obj._index = data.get("index", '')
        obj._data = data.get("data", -1)
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        return obj

    @property
    def index(self):
        return self._index

    @index.setter
    def index(self, new_value):
        self._index = new_value

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, new_value):
        self._data = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value


class TimingTextDataObject(TextObject):
    def __init__(self):
        super().__init__()
        self._length = 1  # 时序图的数据内容

    @property
    def length(self):
        return self._length

    @length.setter
    def length(self, new_value):
        self._length = new_value
