# 基本配置
name: "RAM确认"
description: "根据Gerrit Diff文档，获取变更的全局变量并确认初始化时机"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  input:
    repo_extensions: [ ".c", ".h", ".prm" ]
    default_regular_task:
      vd_g_IntHndlrIRQTftdrawmgrTask: 16.6MS
  output:
    default_output_dir: "C:\\sdw_output\\ram_confirm" # 默认输出目录
    report_base_name: "【Orca#2】RAM確認表"
    template_file: "templates/【Orca#2】RAM確認表_模板.xlsx"

# 处理参数
processing:
  excel_output:
    default_sheet: " ｲﾍﾞﾝﾄ(DEFAULT)"
    data_start_row: 6
    data_cols: [ 1,2,3,4,17,18,19,20,21,22 ]

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {name} | {message}"

