"""
设计基准Check Sheet专用Excel工具类

继承ExcelUtil基类，提供设计基准Check Sheet工作流专用的Excel操作功能。
"""

import os
from typing import Dict, List, Any, Optional, Tuple

from loguru import logger

from sdw_agent.util.excel.core import ExcelUtil, CellRange


class DesignCSExcelUtil(ExcelUtil):
    """
    设计基准Check Sheet专用Excel工具类
    
    继承ExcelUtil基类，提供专门针对设计基准Check Sheet的Excel操作功能：
    1. Excel文件结构分析
    2. 表头映射和数据提取
    3. 隐藏行和隐藏列检测
    4. 数据过滤和导出
    """

    def __init__(self, file_path: str, engine: str = "openpyxl", auto_create: bool = False):
        """
        初始化设计基准Check Sheet Excel工具
        
        Args:
            file_path: Excel文件路径
            engine: 操作引擎类型（默认openpyxl，避免VBA问题）
            auto_create: 如果文件不存在是否自动创建
        """
        super().__init__(file_path, engine, auto_create)
        self.header_mapping_cache = {}
        self.english_fields_cache = []
        
    def analyze_excel_structure(self) -> Dict[str, Any]:
        """
        分析Excel文件的结构，包括所有sheet名称和基本信息
        
        Returns:
            Dict[str, Any]: 包含工作表信息的字典
        """
        logger.info(f"正在分析文件结构: {self.file_path}")
        
        try:
            sheet_names = self.get_sheet_names()
            structure_info = {
                "file_path": self.file_path,
                "total_sheets": len(sheet_names),
                "sheet_names": sheet_names,
                "related_sheets": self._find_related_sheets(sheet_names)
            }
            
            logger.info(f"文件结构分析完成，包含 {len(sheet_names)} 个工作表")
            return structure_info
            
        except Exception as e:
            logger.error(f"分析Excel文件结构失败: {str(e)}")
            raise

    def _find_related_sheets(self, sheet_names: List[str]) -> List[str]:
        """
        查找与设计基准相关的工作表
        
        Args:
            sheet_names: 所有工作表名称列表
            
        Returns:
            List[str]: 相关工作表名称列表
        """
        related_keywords = ['設計基準', '要件分析', 'CS', '基本設計']
        related_sheets = []
        
        for sheet_name in sheet_names:
            for keyword in related_keywords:
                if keyword in sheet_name:
                    related_sheets.append(sheet_name)
                    break
                    
        return related_sheets

    def determine_target_sheet(self, target_sheet: Optional[str] = None) -> Optional[str]:
        """
        确定目标工作表
        
        Args:
            target_sheet: 指定的目标工作表名称
            
        Returns:
            Optional[str]: 确定的工作表名称或None
        """
        sheet_names = self.get_sheet_names()
        
        if target_sheet and target_sheet in sheet_names:
            logger.info(f"使用指定的工作表: {target_sheet}")
            return target_sheet
        
        if target_sheet:
            logger.warning(f"指定的工作表 '{target_sheet}' 不存在")
        
        # 尝试查找相似的工作表
        related_sheets = self._find_related_sheets(sheet_names)
        
        if related_sheets:
            selected_sheet = related_sheets[0]
            logger.info(f"找到相关工作表，使用: {selected_sheet}")
            return selected_sheet
        
        logger.error("未找到合适的工作表")
        return None

    def analyze_sheet_structure(self, sheet_name: str) -> Dict[str, Any]:
        """
        分析指定sheet的结构
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            Dict[str, Any]: 工作表结构信息
        """
        if sheet_name not in self.get_sheet_names():
            logger.error(f"工作表 '{sheet_name}' 不存在")
            return {}

        try:
            # 获取工作表基本信息
            max_row = self._get_max_row(sheet_name)
            max_col = self._get_max_column(sheet_name)
            
            structure_info = {
                "sheet_name": sheet_name,
                "max_row": max_row,
                "max_column": max_col,
                "has_data": max_row > 0 and max_col > 0
            }
            
            logger.info(f"工作表 {sheet_name} 结构: 最大行数={max_row}, 最大列数={max_col}")
            return structure_info
            
        except Exception as e:
            logger.error(f"分析工作表结构失败: {str(e)}")
            return {}

    def _get_max_row(self, sheet_name: str) -> int:
        """获取工作表最大行数"""
        try:
            # 对于openpyxl引擎，直接使用工作表的max_row属性
            if hasattr(self.engine, 'workbook'):
                worksheet = self.engine.workbook[sheet_name]
                return worksheet.max_row

            # 如果不是openpyxl引擎，使用范围读取方式估算
            test_range = CellRange(1, 1, 1000, 1)
            data = self.read_range(sheet_name, test_range)

            # 找到最后一个非空行
            max_row = 0
            for i, row in enumerate(data, 1):
                if any(cell for cell in row if cell is not None and str(cell).strip()):
                    max_row = i

            return max_row
        except:
            return 0

    def _get_max_column(self, sheet_name: str) -> int:
        """获取工作表最大列数"""
        try:
            # 对于openpyxl引擎，直接使用工作表的max_column属性
            if hasattr(self.engine, 'workbook'):
                worksheet = self.engine.workbook[sheet_name]
                return worksheet.max_column

            # 如果不是openpyxl引擎，使用范围读取方式估算
            # 读取更大的范围来确保不会遗漏数据
            test_range = CellRange(1, 1, 100, 100)  # 读取前100行100列
            data = self.read_range(sheet_name, test_range)

            max_col = 0
            if data:
                for row in data:
                    for i, cell in enumerate(row, 1):
                        if cell is not None and str(cell).strip():
                            max_col = max(max_col, i)

            return max_col
        except:
            return 0

    def analyze_hidden_rows(self, sheet_name: str, start_row: int = 6, end_row: Optional[int] = None) -> Tuple[List[int], List[int]]:
        """
        分析工作表中的隐藏行情况
        
        Args:
            sheet_name: 工作表名称
            start_row: 开始分析的行号
            end_row: 结束分析的行号，None表示到最后一行
            
        Returns:
            Tuple[List[int], List[int]]: (可见行列表, 隐藏行列表)
        """
        if end_row is None:
            end_row = self._get_max_row(sheet_name)

        logger.info(f"分析隐藏行情况 (第{start_row}行到第{end_row}行)")

        hidden_rows = []
        visible_rows = []

        for row_num in range(start_row, end_row + 1):
            is_hidden = self._check_row_hidden(sheet_name, row_num)

            if is_hidden:
                hidden_rows.append(row_num)
            else:
                visible_rows.append(row_num)

        logger.info(f"可见行数: {len(visible_rows)}, 隐藏行数: {len(hidden_rows)}")
        return visible_rows, hidden_rows

    def _check_row_hidden(self, sheet_name: str, row_num: int) -> bool:
        """
        检查指定行是否隐藏
        
        Args:
            sheet_name: 工作表名称
            row_num: 行号
            
        Returns:
            bool: True表示隐藏，False表示可见
        """
        try:
            # 对于openpyxl引擎，需要直接访问工作表对象
            if hasattr(self.engine, 'workbook'):
                worksheet = self.engine.workbook[sheet_name]
                row_dimension = worksheet.row_dimensions.get(row_num)
                if row_dimension is None:
                    return False
                return getattr(row_dimension, 'hidden', False)
            return False
        except Exception as e:
            logger.warning(f"检查第{row_num}行隐藏状态时出错: {e}")
            return False

    def check_columns_hidden(self, sheet_name: str, target_columns: List[int] = None) -> List[Dict[str, Any]]:
        """
        检查指定列是否隐藏
        
        Args:
            sheet_name: 工作表名称
            target_columns: 目标列号列表，默认为[13, 14, 15] (M, N, O列)
            
        Returns:
            List[Dict[str, Any]]: 列隐藏状态列表
        """
        if target_columns is None:
            target_columns = [13, 14, 15]  # M, N, O列
            
        result = []
        
        for col_num in target_columns:
            # 将数字列号转换为字母
            col_letter = chr(ord('A') + col_num - 1)
            is_hidden = self._check_column_hidden(sheet_name, col_letter)
            
            result.append({
                'order': col_num,
                'column_letter': col_letter,
                'is_hidden': is_hidden
            })
            
        return result

    def _check_column_hidden(self, sheet_name: str, col_identifier: str) -> bool:
        """
        检查指定列是否隐藏
        
        Args:
            sheet_name: 工作表名称
            col_identifier: 列标识符（字母）
            
        Returns:
            bool: True表示隐藏，False表示可见
        """
        try:
            # 对于openpyxl引擎，需要直接访问工作表对象
            if hasattr(self.engine, 'workbook'):
                worksheet = self.engine.workbook[sheet_name]
                col_dimension = worksheet.column_dimensions.get(col_identifier)
                if col_dimension is None:
                    return False
                return getattr(col_dimension, 'hidden', False)
            return False
        except Exception as e:
            logger.warning(f"检查列{col_identifier}隐藏状态时出错: {e}")
            return False

    def extract_header_mapping(self, sheet_name: str, header_row: int = 6) -> Tuple[List[str], List[str], Dict[str, str]]:
        """
        提取表头并创建英文字段映射

        Args:
            sheet_name: 工作表名称
            header_row: 表头行号

        Returns:
            Tuple[List[str], List[str], Dict[str, str]]: (原始表头, 英文字段, 表头映射)
        """
        try:
            # 对于openpyxl引擎，直接使用工作表对象读取表头
            if hasattr(self.engine, 'workbook'):
                worksheet = self.engine.workbook[sheet_name]
                max_col = worksheet.max_column

                headers = []
                for col_num in range(1, max_col + 1):
                    cell_value = worksheet.cell(row=header_row, column=col_num).value
                    if cell_value is None:
                        cell_value = ""
                    headers.append(str(cell_value).strip())
            else:
                # 对于其他引擎，读取一个较大的范围来确保不遗漏表头
                # 读取前50列，这应该足够覆盖大多数Excel表格的列数
                header_range = CellRange(header_row, 1, header_row, 50)
                header_data = self.read_range(sheet_name, header_range)

                if not header_data or len(header_data) == 0:
                    logger.warning(f"无法读取表头数据，行号: {header_row}")
                    return [], [], {}

                headers = []
                for cell_value in header_data[0]:
                    if cell_value is None:
                        cell_value = ""
                    headers.append(str(cell_value).strip())

                # 移除末尾的空表头
                while headers and not headers[-1]:
                    headers.pop()

            # 创建英文字段映射
            header_mapping = {}
            english_fields = []

            for i, header in enumerate(headers):
                if header:  # 只处理非空表头
                    english_field = self._map_header_to_english(header, i)
                    header_mapping[header] = english_field
                    english_fields.append(english_field)
                else:
                    english_fields.append(f'empty_col_{i + 1}')

            # 缓存结果
            self.header_mapping_cache = header_mapping
            self.english_fields_cache = english_fields

            logger.info(f"提取到 {len([h for h in headers if h])} 个有效表头")
            return headers, english_fields, header_mapping

        except Exception as e:
            logger.error(f"提取表头映射失败: {str(e)}")
            return [], [], {}

    def _map_header_to_english(self, header: str, index: int) -> str:
        """
        将日文表头映射为英文字段名

        Args:
            header: 日文表头
            index: 列索引

        Returns:
            str: 英文字段名
        """
        # 根据实际的日文表头创建英文映射
        mapping_rules = {
            '大項目': 'major_category',
            '中項目': 'middle_category',
            '小項目': 'minor_category',
            '失敗事例': 'failure_case_old_no',
            'PCSⅢ-2': 'pcs3_2_old_no',
            '旧No': 'old_no',
            '__No.__': 'current_no',
            '目的': 'purpose',
            '規定': 'regulation',
            '該当/非該当': 'applicable_auto',
            '基盤ソフト流用': 'base_software_reuse',
            'フィルタリング': 'filtering_criteria',
            'イベント定義': 'event_definition_start',
            'AS実施': 'as_implementation',
            'R4小実施': 'r4_implementation',
            '1A実施': '1a_implementation',
            '品確実施': 'quality_implementation',
            'イベント定義End': 'event_definition_end',
            '実施否理由': 'non_implementation_reason',
            '設計/検証方針': 'design_verification_policy',
            'DR要否': 'dr_required',
            'DR結果': 'dr_result',
            '設計方針結果': 'design_policy_result',
            '設計処置期限': 'design_deadline',
            '評価方針結果': 'evaluation_policy_result',
            '評価処置期限': 'evaluation_deadline',
            '処置状況': 'action_status'
        }

        # 查找匹配的映射规则
        for key, value in mapping_rules.items():
            if key in header:
                # 处理特殊情况
                if '失敗事例' in header and 'No' in header:
                    return 'failure_case_old_no'
                elif 'PCSⅢ-2' in header and 'No' in header:
                    return 'pcs3_2_old_no'
                elif '旧No' in header and 'PCSⅢ-2' not in header:
                    return 'old_no'
                elif '目的' in header and len(header) < 5:
                    return 'purpose'
                elif '該当/非該当' in header and '自動判定' in header:
                    return 'applicable_auto'
                elif 'フィルタリング' in header and '非該当基準' in header:
                    return 'filtering_criteria'
                elif 'イベント定義' in header and 'End' not in header:
                    return 'event_definition_start'
                else:
                    return value

        # 如果无法识别，使用通用命名
        return f'field_{index + 1}'

    def extract_data_from_worksheet(self, sheet_name: str, english_fields: List[str], start_row: int = 7,
                                   include_hidden: bool = False, max_rows: int = 10000) -> List[Dict[str, Any]]:
        """
        从工作表中提取数据

        Args:
            sheet_name: 工作表名称
            english_fields: 英文字段名列表
            start_row: 开始行号
            include_hidden: 是否包含隐藏行
            max_rows: 最大处理行数

        Returns:
            List[Dict[str, Any]]: 提取的数据列表
        """
        logger.info(f"从第{start_row}行开始提取数据")
        logger.info(f"隐藏行处理: {'包含隐藏行' if include_hidden else '跳过隐藏行'}")

        data_list = []
        hidden_rows_count = 0
        max_row = min(self._get_max_row(sheet_name), start_row + max_rows - 1)

        for row_num in range(start_row, max_row + 1):
            # 检查行是否隐藏
            is_hidden = self._check_row_hidden(sheet_name, row_num)

            if is_hidden:
                hidden_rows_count += 1
                if not include_hidden:
                    continue  # 跳过隐藏行

            # 读取行数据
            row_range = CellRange(row_num, 1, row_num, len(english_fields))
            row_data_raw = self.read_range(sheet_name, row_range)

            if not row_data_raw or len(row_data_raw) == 0:
                continue

            row_values = row_data_raw[0]
            row_data = {}
            has_data = False

            for col_num, field_name in enumerate(english_fields):
                if col_num < len(row_values):
                    cell_value = row_values[col_num]
                else:
                    cell_value = None

                # 处理不同类型的数据
                if cell_value is None:
                    cell_value = ""
                elif isinstance(cell_value, (int, float)):
                    cell_value = str(cell_value)
                else:
                    cell_value = str(cell_value).strip()

                row_data[field_name] = cell_value

                # 检查是否有实际数据（非空且不只是空格）
                if cell_value and cell_value.strip():
                    has_data = True

            # 只添加包含数据的行
            if has_data:
                row_data['row_number'] = row_num
                row_data['is_hidden'] = is_hidden
                data_list.append(row_data)

        logger.info(f"发现隐藏行: {hidden_rows_count} 行")
        logger.info(f"提取到 {len(data_list)} 行有效数据")

        return data_list

    def filter_data(self, data_list: List[Dict[str, Any]], filter_major_category: Optional[str] = None,
                   filter_middle_category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据条件过滤数据

        Args:
            data_list: 原始数据列表
            filter_major_category: 过滤大項目
            filter_middle_category: 过滤中項目

        Returns:
            List[Dict[str, Any]]: 过滤后的数据列表
        """
        filtered_data = data_list

        if filter_major_category:
            filtered_data = [
                row for row in filtered_data
                if row.get('major_category', '').strip() == filter_major_category
            ]
            logger.info(f"按大項目 '{filter_major_category}' 过滤后剩余 {len(filtered_data)} 行")

        if filter_middle_category:
            filtered_data = [
                row for row in filtered_data
                if row.get('middle_category', '').strip() == filter_middle_category
            ]
            logger.info(f"按中項目 '{filter_middle_category}' 过滤后剩余 {len(filtered_data)} 行")

        return filtered_data
