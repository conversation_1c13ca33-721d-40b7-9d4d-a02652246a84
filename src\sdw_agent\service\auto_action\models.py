from enum import Enum
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

class QueryStatus(str, Enum):
    """查询状态枚举"""
    SUCCESS = "成功"
    FAILED = "失败"
    NO_RESULT = "无相关结果"

class QueryConfigModel(BaseModel):
    """配置模型"""
    base_dir: str = Field(default="性能测试手顺", description="基础目录")
    allowed_extensions: List[str] = Field(default=[".xlsx", ".xls"], description="允许的文件扩展名")
    database_names: List[str] = Field(
        default=[
            "1-ROM&RAM测试",
            "2-Stack统计",
            "3-负荷测试相关资料",
            "4-启动时间测试"
        ],
        description="数据库名称列表"
    )

class QueryResult(BaseModel):
    """查询结果"""
    file_paths: List[str] = Field(default_factory=list, description="相关文件路径")
    content: str = Field(default="", description="提取的内容")
    md_path: Optional[str] = Field(default=None, description="生成的markdown文件路径")

class AutoActionResult(BaseModel):
    """工作流执行结果"""
    status: QueryStatus
    message: str
    data: Optional[QueryResult] = None
    error: Optional[str] = None