from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
import json
import base64
import hashlib

@dataclass
class BaseObject:
    """Base class for all document objects"""
    id: str = ""
    name: str = ""
    type: str = ""
    page_number: int = 0
    position: Dict[str, float] = field(default_factory=lambda: {"x": 0, "y": 0})
    size: Dict[str, float] = field(default_factory=lambda: {"width": 0, "height": 0})
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "page_number": self.page_number,
            "position": self.position,
            "size": self.size,
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """Convert object to JSON string"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseObject':
        """Create object from dictionary"""
        obj = cls()
        obj.id = data.get("id", "")
        obj.name = data.get("name", "")
        obj.type = data.get("type", "")
        obj.page_number = data.get("page_number", 0)
        obj.position = data.get("position", {"x": 0, "y": 0})
        obj.size = data.get("size", {"width": 0, "height": 0})
        obj.metadata = data.get("metadata", {})
        return obj

@dataclass
class TextObject(BaseObject):
    """Class representing a text object in a document"""
    text: str = ""
    style: Dict[str, Any] = field(default_factory=dict)
    runs: List[Dict[str, Any]] = field(default_factory=list)
    
    def __post_init__(self):
        self.type = "text"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary"""
        data = super().to_dict()
        data.update({
            "text": self.text,
            "style": self.style,
            "runs": self.runs
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TextObject':
        """Create object from dictionary"""
        obj = super().from_dict(data)
        obj.text = data.get("text", "")
        obj.alignment = data.get("alignment", "")
        obj.style = data.get("style", {})
        obj.runs = data.get("runs", [])
        return obj

@dataclass
class TableObject(BaseObject):
    """Class representing a table object in a document"""
    rows: int = 0
    columns: int = 0
    cells: List[Dict[str, Any]] = field(default_factory=list)
    header_row: bool = False
    borders: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        self.type = "table"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary"""
        data = super().to_dict()
        data.update({
            "rows": self.rows,
            "columns": self.columns,
            "cells": self.cells,
            "header_row": self.header_row,
            "borders": self.borders
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableObject':
        """Create object from dictionary"""
        obj = super().from_dict(data)
        obj.rows = data.get("rows", 0)
        obj.columns = data.get("columns", 0)
        obj.cells = data.get("cells", [])
        obj.header_row = data.get("header_row", False)
        obj.borders = data.get("borders", {})
        return obj

@dataclass
class PictureObject(BaseObject):
    """Class representing a picture object in a document"""
    data: str = ""  # Base64 encoded image data
    format: str = ""  # Image format (png, jpeg, etc.)
    original_path: str = ""
    digest: str = ""  # MD5 hash of image data
    
    def __post_init__(self):
        self.type = "picture"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary"""
        data = super().to_dict()
        data.update({
            "data": self.data,
            "format": self.format,
            "original_path": self.original_path,
            "digest": self.digest
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PictureObject':
        """Create object from dictionary"""
        obj = super().from_dict(data)
        obj.data = data.get("data", "")
        obj.format = data.get("format", "")
        obj.original_path = data.get("original_path", "")
        obj.digest = data.get("digest", "")
        return obj
    
    def set_image_data(self, image_data: bytes, format: str) -> None:
        """Set image data and calculate digest"""
        self.data = base64.b64encode(image_data).decode('utf-8')
        self.format = format
        self.digest = hashlib.md5(image_data).hexdigest()

@dataclass
class GraphicObject(BaseObject):
    """Class representing a graphic object in a document"""
    data: str = ""  # Base64 encoded graphic data or SVG
    format: str = ""  # Graphic format
    shape_type: str = ""  # Type of shape (rectangle, circle, etc.)
    fill_color: str = ""
    line_color: str = ""
    line_width: float = 0
    
    def __post_init__(self):
        self.type = "graphic"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary"""
        data = super().to_dict()
        data.update({
            "data": self.data,
            "format": self.format,
            "shape_type": self.shape_type,
            "fill_color": self.fill_color,
            "line_color": self.line_color,
            "line_width": self.line_width
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphicObject':
        """Create object from dictionary"""
        obj = super().from_dict(data)
        obj.data = data.get("data", "")
        obj.format = data.get("format", "")
        obj.shape_type = data.get("shape_type", "")
        obj.fill_color = data.get("fill_color", "")
        obj.line_color = data.get("line_color", "")
        obj.line_width = data.get("line_width", 0)
        return obj

@dataclass
class Document:
    """Class representing a parsed document"""
    file_path: str = ""
    file_name: str = ""
    file_type: str = ""
    page_count: int = 0
    text_objects: List[TextObject] = field(default_factory=list)
    table_objects: List[TableObject] = field(default_factory=list)
    picture_objects: List[PictureObject] = field(default_factory=list)
    graphic_objects: List[GraphicObject] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return {
            "file_path": self.file_path,
            "file_name": self.file_name,
            "file_type": self.file_type,
            "page_count": self.page_count,
            "text_objects": [obj.to_dict() for obj in self.text_objects],
            "table_objects": [obj.to_dict() for obj in self.table_objects],
            "picture_objects": [obj.to_dict() for obj in self.picture_objects],
            "graphic_objects": [obj.to_dict() for obj in self.graphic_objects],
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """Convert document to JSON string"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    def save_to_json(self, output_path: str) -> None:
        """Save document to JSON file"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(self.to_json())
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """Create document from dictionary"""
        doc = cls()
        doc.file_path = data.get("file_path", "")
        doc.file_name = data.get("file_name", "")
        doc.file_type = data.get("file_type", "")
        doc.page_count = data.get("page_count", 0)
        doc.text_objects = [TextObject.from_dict(obj) for obj in data.get("text_objects", [])]
        doc.table_objects = [TableObject.from_dict(obj) for obj in data.get("table_objects", [])]
        doc.picture_objects = [PictureObject.from_dict(obj) for obj in data.get("picture_objects", [])]
        doc.graphic_objects = [GraphicObject.from_dict(obj) for obj in data.get("graphic_objects", [])]
        doc.metadata = data.get("metadata", {})
        return doc
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Document':
        """Create document from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    @classmethod
    def load_from_json(cls, input_path: str) -> 'Document':
        """Load document from JSON file"""
        with open(input_path, 'r', encoding='utf-8') as f:
            return cls.from_json(f.read())
