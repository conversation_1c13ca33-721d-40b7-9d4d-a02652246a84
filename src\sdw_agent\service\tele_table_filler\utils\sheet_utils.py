"""
Excel工作表处理工具模块

V字对应：
4.4 对应的V字阶段
64. 对应的V字项目


模块简介：提供Excel表头查找和列填充功能。

主要功能：
1. 表头查找函数
2. 列填充函数（如fill_igr_igp等）
"""

from __future__ import annotations  # 添加这一行以启用延迟注解

import json
import re
import asyncio
from typing import Dict, List, Optional, TYPE_CHECKING, Any
from openpyxl.worksheet.worksheet import Worksheet
from sdw_agent.service.tele_table_filler.utils.file_utils import normalize_text
from sdw_agent.service.tele_table_filler.utils.rag_utils import get_rag_instance, query_rag_with_retry

if TYPE_CHECKING:
    from ..tele_table_filler import TeleTableFillerWorkflow  # 只用于类型检查


def find_merged_header(sheet: Worksheet, header_name: str, workflow: TeleTableFillerWorkflow) -> Optional[
    Dict[str, int]]:
    """
    查找合并单元格表头

    Args:
        sheet: 工作表
        header_name: 表头名称
        workflow: 工作流实例，用于日志

    Returns:
        Optional[Dict[str, int]]: 表头范围或None
    """
    target = normalize_text(header_name)
    workflow.add_debug_info(f"===== 开始查找合并表头: '{header_name}' (标准化: '{target}') =====")
    checked_cells = []
    matched_cells = []

    for merged_cell in sheet.merged_cells.ranges:
        workflow.add_debug_info(f"遍历到合并单元格区域: {merged_cell}")
        min_col, min_row, max_col, max_row = merged_cell.bounds
        cell_value = sheet.cell(row=min_row, column=min_col).value
        normalized_value = normalize_text(cell_value)

        cell_info = {
            "row_range": f"{min_row}-{max_row}",
            "col_range": f"{min_col}-{max_col}",
            "value": cell_value,
            "normalized_value": normalized_value
        }
        checked_cells.append(cell_info)

        workflow.add_debug_info(
            f"检查合并单元格: 行{min_row}-{max_row}, 列{min_col}-{max_col}, "
            f"原始值: '{cell_value}', 标准化后: '{normalized_value}'"
        )

        if normalized_value == target:
            matched_cells.append(cell_info)
            workflow.add_debug_info(
                f"找到完全匹配的合并表头: '{header_name}'，位置: 行{min_row}-{max_row}, 列{min_col}-{max_col}"
            )
            return {'min_row': min_row, 'max_row': max_row, 'min_col': min_col, 'max_col': max_col}

    workflow.add_debug_info(f"未找到完全匹配的合并表头，尝试模糊匹配...")
    for cell_info in checked_cells:
        if target in cell_info["normalized_value"]:
            min_row = int(cell_info["row_range"].split("-")[0])
            max_row = int(cell_info["row_range"].split("-")[1])
            min_col = int(cell_info["col_range"].split("-")[0])
            max_col = int(cell_info["col_range"].split("-")[1])
            matched_cells.append(cell_info)
            workflow.add_debug_info(
                f"找到模糊匹配的合并表头: '{cell_info['value']}'，位置: 行{min_row}-{max_row}, 列{min_col}-{max_col}, "
                f"包含目标关键词: '{header_name}'"
            )
            return {'min_row': min_row, 'max_row': max_row, 'min_col': min_col, 'max_col': max_col}

    workflow.add_debug_info(f"===== 未找到合并表头: '{header_name}' =====", is_error=True)
    workflow.add_debug_info(f"目标标准化值: '{target}'", is_error=True)
    workflow.add_debug_info(f"检查过的合并单元格总数: {len(checked_cells)}", is_error=True)

    if checked_cells:
        workflow.add_debug_info("检查过的合并单元格列表:", is_error=True)
        display_cells = checked_cells[:10]
        if len(checked_cells) > 20:
            display_cells.append("...")
            display_cells.extend(checked_cells[-10:])
        elif len(checked_cells) > 10:
            display_cells.extend(checked_cells[10:])

        for i, cell in enumerate(display_cells):
            if cell == "...":
                workflow.add_debug_info(" ... 中间有省略 ...", is_error=True)
                continue
            workflow.add_debug_info(
                f" [{i + 1}] 行{cell['row_range']}, 列{cell['col_range']}, "
                f"值: '{cell['value']}', 标准化: '{cell['normalized_value']}'", is_error=True
            )

    return None


def find_header_column_contains(sheet: Worksheet, keyword: str, workflow: TeleTableFillerWorkflow,
                                col_min: Optional[int] = None, col_max: Optional[int] = None) -> Optional[int]:
    """
    查找包含关键词的表头列，返回第一个匹配的列索引

    Args:
        sheet: 工作表
        keyword: 关键词
        workflow: 工作流实例
        col_min: 最小列
        col_max: 最大列

    Returns:
        Optional[int]: 列索引或None
    """
    target = normalize_text(keyword)
    range_str = f"，范围: 列{col_min}-{col_max}" if col_min and col_max else ""
    workflow.add_debug_info(f"正在查找包含关键词的表头: '{keyword}' (标准化: '{target}'){range_str}")

    search_min_col = col_min if col_min is not None else 1
    search_max_col = col_max if col_max is not None else sheet.max_column

    for row in range(1, 51):
        if row > sheet.max_row:
            break
        for col in range(search_min_col, search_max_col + 1):
            cell_value = sheet.cell(row=row, column=col).value
            normalized_value = normalize_text(cell_value)
            if target in normalized_value:
                workflow.add_debug_info(
                    f"找到表头 '{keyword}'，位置: 行{row}, 列{col}, "
                    f"值: '{cell_value}', 标准化: '{normalized_value}'"
                )
                return col

    workflow.add_debug_info(f"未找到包含关键词的表头: '{keyword}' 在指定范围", is_error=True)
    return None


def find_all_header_columns_contains(sheet: Worksheet, keyword: str, workflow: TeleTableFillerWorkflow,
                                     col_min: Optional[int] = None, col_max: Optional[int] = None) -> List[int]:
    """
    查找所有包含关键词的表头列，返回列索引列表

    Args:
        sheet: 工作表
        keyword: 关键词
        workflow: 工作流实例
        col_min: 最小列
        col_max: 最大列

    Returns:
        List[int]: 列索引列表
    """
    target = normalize_text(keyword)
    range_str = f"，范围: 列{col_min}-{col_max}" if col_min and col_max else ""
    workflow.add_debug_info(f"正在查找所有包含关键词的表头: '{keyword}' (标准化: '{target}'){range_str}")
    columns = []

    search_min_col = col_min if col_min is not None else 1
    search_max_col = col_max if col_max is not None else sheet.max_column

    for row in range(1, 51):
        if row > sheet.max_row:
            break
        for col in range(search_min_col, search_max_col + 1):
            cell_value = sheet.cell(row=row, column=col).value
            normalized_value = normalize_text(cell_value)
            if target in normalized_value and col not in columns:
                workflow.add_debug_info(
                    f"找到表头 '{keyword}'，位置: 行{row}, 列{col}, "
                    f"值: '{cell_value}', 标准化: '{normalized_value}'"
                )
                columns.append(col)

    columns.sort()
    workflow.add_debug_info(f"找到{len(columns)}个包含关键词'{keyword}'的列: {columns}")
    return columns


def get_sub_headers(sheet: Worksheet, main_header_range: Dict[str, int], workflow: TeleTableFillerWorkflow,
                    col_min: Optional[int] = None, col_max: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    获取主表头下的子表头

    Args:
        sheet: 工作表
        main_header_range: 主表头范围
        workflow: 工作流实例
        col_min: 最小列
        col_max: 最大列

    Returns:
        List[Dict[str, Any]]: 子表头列表
    """
    workflow.add_debug_info(
        f"正在获取主表头下的子表头，主表头位置: "
        f"行{main_header_range['min_row']}-{main_header_range['max_row']}, "
        f"列{main_header_range['min_col']}-{main_header_range['max_col']}"
    )
    sub_headers = []

    search_min_col = col_min if col_min is not None else main_header_range['min_col']
    search_max_col = col_max if col_max is not None else main_header_range['max_col']

    for row in range(main_header_range['max_row'] + 1, main_header_range['max_row'] + 21):
        if row > sheet.max_row:
            break
        row_headers = []
        for col in range(search_min_col, search_max_col + 1):
            cell_value = sheet.cell(row=row, column=col).value
            if cell_value and normalize_text(cell_value) != "":
                row_headers.append({
                    'name': cell_value,
                    'column': col,
                    'row': row
                })

        if row_headers:
            sub_headers = row_headers
            workflow.add_debug_info(
                f"在第{row}行找到{len(sub_headers)}个子表头: "
                f"{[h['name'] for h in sub_headers]}"
            )
            break

    if not sub_headers:
        workflow.add_debug_info("未找到子表头", is_error=True)

    return sub_headers


def get_data_start_row(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    确定数据起始行

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 数据起始行
    """
    workflow.add_debug_info("正在确定数据起始行")

    for row in range(1, sheet.max_row + 1):
        data_cells = 0
        for col in range(1, min(11, sheet.max_column + 1)):
            cell_value = sheet.cell(row=row, column=col).value
            if cell_value is not None:
                if isinstance(cell_value, (int, float)):
                    data_cells += 1
                elif str(cell_value).strip().isdigit():
                    data_cells += 1
        if data_cells >= 2:
            workflow.add_debug_info(f"数据起始行确定为: {row} (方法: 数字数据检测)")
            return row

    start_row = 6
    if start_row > sheet.max_row:
        start_row = 2
    workflow.add_debug_info(f"数据起始行确定为: {start_row} (方法: 默认值)")
    return start_row


def get_previous_value(sheet: Worksheet, row: int, col: int, col_name: str, workflow: TeleTableFillerWorkflow) -> Any:
    """
    向上查找最近的非空值

    Args:
        sheet: 工作表
        row: 当前行
        col: 列
        col_name: 列名
        workflow: 工作流实例

    Returns:
        Any: 值或None
    """
    workflow.add_debug_info(f"在行{row}, 列{col}({col_name})向上查找非空值")
    current_row = row - 1
    while current_row >= 1:
        value = sheet.cell(row=current_row, column=col).value
        if value is not None and str(value).strip() != '':
            workflow.add_debug_info(f"找到最近非空值: 行{current_row} = {value}")
            return value
        current_row -= 1

    workflow.add_debug_info(f"未找到{col_name}的非空值", is_error=True)
    return None


def find_expected_results_range(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> Optional[Dict[str, int]]:
    """
    查找 'Expected_results' 合并表头范围

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        Optional[Dict[str, int]]: 范围或None
    """
    header = find_merged_header(sheet, "Expected_results", workflow)
    if not header:
        header = find_merged_header(sheet, "Expected Results", workflow) or \
                 find_merged_header(sheet, "ExpectedResults", workflow) or \
                 find_merged_header(sheet, "预期结果", workflow)
    if not header:
        workflow.add_debug_info("未找到 'Expected_results' 合并表头", is_error=True)
        return None
    workflow.add_debug_info(
        f"找到 'Expected_results' 范围: 行{header['min_row']}-{header['max_row']}, 列{header['min_col']}-{header['max_col']}")
    return header


def fill_igr_igp(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> Optional[int]:
    """
    填充IGR/IGP列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        Optional[int]: 填充行数或None
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的IGR/IGP列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过IGR/IGP填充", is_error=True)
        return None

    main_header = find_merged_header(sheet, "Msg. Trans. Cond. (Scene)", workflow)

    sub_headers = get_sub_headers(sheet, main_header, workflow)
    if not sub_headers:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到子表头，跳过IGR/IGP填充", is_error=True)
        return None

    filtered_sub_headers = []
    for sh in sub_headers:
        normalized_name = normalize_text(sh['name'])
        if normalized_name in ['igr', 'igp']:
            filtered_sub_headers.append(sh)
            workflow.add_debug_info(f"保留IGR/IGP子表头: {sh['name']} (标准化: {normalized_name})")
        else:
            workflow.add_debug_info(f"忽略非IGR/IGP子表头: {sh['name']} (标准化: {normalized_name})")

    if not filtered_sub_headers:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到IGR/IGP子表头，跳过填充", is_error=True)
        return None

    igr_igp_col = find_header_column_contains(sheet, "IGR/IGP", workflow, expected_range['min_col'],
                                              expected_range['max_col'])
    if not igr_igp_col:
        workflow.add_debug_info("尝试查找IGR/IGP替代表头名称...")
        igr_igp_col = find_header_column_contains(sheet, "电源条件IGR/IGP", workflow, expected_range['min_col'],
                                                  expected_range['max_col'])

    if not igr_igp_col:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到IGR/IGP列，跳过IGR/IGP填充", is_error=True)
        return None

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    workflow.add_debug_info(f"开始填充数据: 行{start_row}至{end_row}，目标列: {igr_igp_col}")

    filled_rows = 0
    for row in range(start_row, end_row + 1):
        try:
            data = {}
            for sh in filtered_sub_headers:
                col = sh['column']
                current_value = sheet.cell(row=row, column=col).value
                str_current_value = str(current_value).strip().lower() if current_value is not None else ""

                if current_value is None or str_current_value == "":
                    workflow.add_debug_info(f"第{row}行{sh['name']}值为空，向上查找最近有效数据")
                    value = get_previous_value(sheet, row, col, sh['name'], workflow)
                    if value is not None:
                        workflow.add_debug_info(f"找到最近有效数据: {value}")
                        str_value = str(value).strip().lower()
                    else:
                        workflow.add_debug_info(f"未找到最近有效数据，使用默认值0")
                        str_value = ""
                else:
                    value = current_value
                    str_value = str_current_value

                if str_value in ['不适用', 'no', '0', 'n', 'none']:
                    data[sh['name']] = 0
                elif str_value in ['适用', 'yes', '1', 'y']:
                    data[sh['name']] = 1
                else:
                    data[sh['name']] = value if value is not None else 0

            json_str = json.dumps(data, ensure_ascii=False)
            sheet.cell(row=row, column=igr_igp_col, value=json_str)
            filled_rows += 1

            if row % 10 == 0 or row == end_row:
                workflow.add_debug_info(f"已填充{filled_rows}行数据 (当前行: {row})")

        except Exception as e:
            workflow.add_debug_info(f"填充第{row}行时出错: {str(e)}", is_error=True)
            continue

    workflow.add_debug_info(f"===== {sheet_name}工作表的IGR/IGP列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_event(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> Optional[int]:
    """
    填充Event列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        Optional[int]: 填充行数或None
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的Event列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过Event列填充", is_error=True)
        return None

    source_col = find_header_column_contains(sheet, "Event Cond.", workflow)
    if not source_col:
        workflow.add_debug_info("尝试查找Event Cond.替代表头...")
        source_col = find_header_column_contains(sheet, "EventCond.", workflow) or \
                     find_header_column_contains(sheet, "Event Cond", workflow) or \
                     find_header_column_contains(sheet, "EventCond", workflow) or \
                     find_header_column_contains(sheet, "event cond.", workflow) or \
                     find_header_column_contains(sheet, "Event\nCond.", workflow) or \
                     find_header_column_contains(sheet, "Event Cond. ", workflow)

    if not source_col:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Event Cond.列，跳过Event列填充", is_error=True)
        return None

    all_event_cols = find_all_header_columns_contains(sheet, "Event", workflow)
    all_event_cols = [col for col in all_event_cols if expected_range['min_col'] <= col <= expected_range['max_col']]
    if not all_event_cols:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Event列在Expected_results范围内，跳过Event列填充",
                                is_error=True)
        return None

    target_col = all_event_cols[-1]
    workflow.add_debug_info(f"Event源列(Event Cond.): 列{source_col}")
    workflow.add_debug_info(f"Event目标列(最后一个Event列): 列{target_col}")

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    workflow.add_debug_info(f"开始填充数据: 行{start_row}至{end_row}")

    filled_rows = 0
    for row in range(start_row, end_row + 1):
        try:
            source_value = sheet.cell(row=row, column=source_col).value
            if source_value is not None:
                sheet.cell(row=row, column=target_col, value=source_value)
                filled_rows += 1
                workflow.add_debug_info(f"第{row}行Event列填充成功: {source_value}")
            else:
                workflow.add_debug_info(f"第{row}行Event Cond.值为空，不填充", is_error=True)

            if row % 10 == 0 or row == end_row:
                workflow.add_debug_info(f"已填充{filled_rows}行数据 (当前行: {row})")

        except Exception as e:
            workflow.add_debug_info(f"填充第{row}行Event列时出错: {str(e)}", is_error=True)
            continue

    workflow.add_debug_info(f"===== {sheet_name}工作表的Event列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_special(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    填充Special列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 填充行数
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的Special列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过Special列填充", is_error=True)
        return 0

    all_special_cols = find_all_header_columns_contains(sheet, "Special", workflow)
    target_special_cols = [col for col in all_special_cols if
                           expected_range['min_col'] <= col <= expected_range['max_col']]
    if len(target_special_cols) < 1:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Special列在Expected_results范围内，跳过", is_error=True)
        return 0

    source_cols = all_special_cols[:-1] if len(all_special_cols) > 1 else []
    target_col = target_special_cols[-1]

    spec_col = find_header_column_contains(sheet, "引当元仕様書No", workflow)
    data_label_col = find_header_column_contains(sheet, "Data Label", workflow)
    if not spec_col or not data_label_col:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到规格书列或Data Label列，无法进行Special查询", is_error=True)
        return 0

    workflow.add_debug_info(f"找到{len(all_special_cols)}个Special列 (全表): {all_special_cols}")
    workflow.add_debug_info(f"源列列表(从后往前检查，全表): {source_cols}")
    workflow.add_debug_info(f"目标列 (范围内): {target_col}")

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    workflow.add_debug_info(f"开始填充数据: 行{start_row}至{end_row}")

    filled_rows = 0
    spec_groups = {}
    for row in range(start_row, end_row + 1):
        try:
            source_value = None
            source_col_index = -1

            for col in reversed(source_cols):
                current_value = sheet.cell(row=row, column=col).value
                if current_value is not None and str(current_value).strip() != '':
                    source_value = current_value
                    source_col_index = col
                    break

            if source_value is not None:
                workflow.add_debug_info(
                    f"第{row}行找到有效源值，来自列{source_col_index}: {source_value} (类型: {type(source_value)})")
                str_value = str(source_value).strip().lower()

                if str_value in ['0', 'no', 'n', 'false']:
                    sheet.cell(row=row, column=target_col, value="-")
                    filled_rows += 1
                elif str_value in ['1', 'yes', 'y', 'true']:
                    signal_name = sheet.cell(row=row, column=data_label_col).value
                    spec_file = sheet.cell(row=row, column=spec_col).value
                    if signal_name and spec_file:
                        signal_name = str(signal_name).strip()
                        spec_file = str(spec_file).strip()
                        if spec_file not in spec_groups:
                            spec_groups[spec_file] = []
                        spec_groups[spec_file].append({
                            "row": row,
                            "signal_name": signal_name
                        })
                else:
                    sheet.cell(row=row, column=target_col, value=source_value)
                    filled_rows += 1
            else:
                workflow.add_debug_info(f"第{row}行所有源列都无值，保持目标列不变")

        except Exception as e:
            workflow.add_debug_info(f"填充第{row}行Special列时出错: {str(e)}", is_error=True)
            continue

    for spec_file, rows in spec_groups.items():
        workflow.add_debug_info(f"处理Special=1的规格书: {spec_file}, 信号数量: {len(rows)}")

        rag = get_rag_instance(spec_file, workflow.source_dir, workflow.config, workflow.rag_instances)

        if rag == "FILE_NOT_FOUND":
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=target_col, value="式样书未找到")
                filled_rows += 1
            continue

        if rag == "FILE_ERROR":
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=target_col, value="文件错误")
                filled_rows += 1
            continue

        if not rag:
            continue

        for row_data in rows:
            row = row_data["row"]
            signal_name = row_data["signal_name"]

            try:
                special_info = asyncio.run(
                    query_rag_with_retry(rag, signal_name, "special", spec_file, workflow.config, workflow.query_cache))

                if special_info and special_info != "NOT_FOUND":
                    sheet.cell(row=row, column=target_col, value=special_info)
                    filled_rows += 1
                else:
                    sheet.cell(row=row, column=target_col, value="式样书未提及")
                    filled_rows += 1

            except Exception as e:
                workflow.add_debug_info(f"第{row}行Special查询出错: {str(e)}", is_error=True)
                sheet.cell(row=row, column=target_col, value="查询错误")
                filled_rows += 1

    workflow.add_debug_info(f"===== {sheet_name}工作表的Special列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_initial_value(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> Optional[int]:
    """
    填充Initial Value列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        Optional[int]: 填充行数或None
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的Initial Value列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过Initial Value填充", is_error=True)
        return None

    source_col = find_header_column_contains(sheet, "Trans. Initial Value", workflow)
    if not source_col:
        workflow.add_debug_info("尝试查找Trans. Initial Value替代表头...")
        source_col = find_header_column_contains(sheet, "Trans Initial Value", workflow) or \
                     find_header_column_contains(sheet, "TransInitialValue", workflow) or \
                     find_header_column_contains(sheet, "传输初始值", workflow)

    if not source_col:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Trans. Initial Value列，跳过Initial Value填充",
                                is_error=True)
        return None

    all_initial_cols = find_all_header_columns_contains(sheet, "Initial Value", workflow)
    all_initial_cols = [col for col in all_initial_cols if
                        expected_range['min_col'] <= col <= expected_range['max_col']]
    if not all_initial_cols:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Initial Value列在Expected_results范围内，跳过填充",
                                is_error=True)
        return None

    target_col = all_initial_cols[-1]
    workflow.add_debug_info(f"Initial Value源列(Trans. Initial Value): 列{source_col}")
    workflow.add_debug_info(f"Initial Value目标列(最后一个Initial Value列): 列{target_col}")

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    workflow.add_debug_info(f"开始填充数据: 行{start_row}至{end_row}")

    filled_rows = 0
    for row in range(start_row, end_row + 1):
        try:
            source_value = sheet.cell(row=row, column=source_col).value
            if source_value is not None:
                sheet.cell(row=row, column=target_col, value=source_value)
                filled_rows += 1
                workflow.add_debug_info(f"第{row}行Initial Value列填充成功: {source_value}")
            else:
                workflow.add_debug_info(f"第{row}行Trans. Initial Value值为空，不填充", is_error=True)

            if row % 10 == 0 or row == end_row:
                workflow.add_debug_info(f"已填充{filled_rows}行数据 (当前行: {row})")

        except Exception as e:
            workflow.add_debug_info(f"填充第{row}行Initial Value列时出错: {str(e)}", is_error=True)
            continue

    workflow.add_debug_info(f"===== {sheet_name}工作表的Initial Value列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_can_id_dlc_cycle(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> Optional[int]:
    """
    填充CAN ID/DLC/Cycle列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        Optional[int]: 填充行数或None
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的CAN ID/DLC/Cycle列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过CAN ID/DLC/Cycle填充",
                                is_error=True)
        return None

    target_col = find_header_column_contains(sheet, "CAN ID/DLC/Cycle", workflow, expected_range['min_col'],
                                             expected_range['max_col'])
    if not target_col:
        workflow.add_debug_info("尝试查找CAN ID/DLC/Cycle替代表头名称...")
        target_col = find_header_column_contains(sheet, "CANID/DLC/Cycle", workflow, expected_range['min_col'],
                                                 expected_range['max_col']) or \
                     find_header_column_contains(sheet, "CAN ID DLC Cycle", workflow, expected_range['min_col'],
                                                 expected_range['max_col']) or \
                     find_header_column_contains(sheet, "CAN ID/DLC/Cycle (Hex)", workflow, expected_range['min_col'],
                                                 expected_range['max_col'])

    if not target_col:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到CAN ID/DLC/Cycle列在Expected_results范围内，跳过填充",
                                is_error=True)
        return None

    can_id_col = find_header_column_contains(sheet, "CAN ID", workflow)
    dlc_col = find_header_column_contains(sheet, "DLC", workflow)
    cycle_col = find_header_column_contains(sheet, "Com.Trans.Cycle", workflow)

    if not can_id_col:
        workflow.add_debug_info("尝试查找CAN ID替代表头名称...")
        can_id_col = find_header_column_contains(sheet, "CANID", workflow) or \
                     find_header_column_contains(sheet, "Can ID", workflow)

    if not cycle_col:
        workflow.add_debug_info("尝试查找Com.Trans.Cycle替代表头名称...")
        cycle_col = find_header_column_contains(sheet, "传输周期", workflow) or \
                    find_header_column_contains(sheet, "Cycle", workflow) or \
                    find_header_column_contains(sheet, "通信周期", workflow)

    workflow.add_debug_info(
        f"找到的数据列 - CAN ID: {can_id_col}, DLC: {dlc_col}, "
        f"Com.Trans.Cycle: {cycle_col}"
    )

    if not all([can_id_col, dlc_col, cycle_col]):
        missing = []
        if not can_id_col: missing.append("CAN ID")
        if not dlc_col: missing.append("DLC")
        if not cycle_col: missing.append("Com.Trans.Cycle")
        workflow.add_debug_info(f"{sheet_name}工作表: 缺少必要的列: {', '.join(missing)}", is_error=True)
        return None

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    workflow.add_debug_info(f"开始填充数据: 行{start_row}至{end_row}，目标列: {target_col}")

    filled_rows = 0
    for row in range(start_row, end_row + 1):
        try:
            can_id = sheet.cell(row=row, column=can_id_col).value
            if not can_id or str(can_id).strip() == '':
                can_id = get_previous_value(sheet, row, can_id_col, "CAN ID", workflow)

            dlc = sheet.cell(row=row, column=dlc_col).value
            if not dlc or str(dlc).strip() == '':
                dlc = get_previous_value(sheet, row, dlc_col, "DLC", workflow)

            cycle = sheet.cell(row=row, column=cycle_col).value
            if not cycle or str(cycle).strip() == '':
                cycle = get_previous_value(sheet, row, cycle_col, "Com.Trans.Cycle", workflow)

            data = {
                "CAN ID": can_id,
                "DLC": dlc,
                "Cycle": cycle
            }

            json_str = json.dumps(data, ensure_ascii=False)
            sheet.cell(row=row, column=target_col, value=json_str)
            filled_rows += 1

            if row % 10 == 0 or row == end_row:
                workflow.add_debug_info(f"已填充{filled_rows}行数据 (当前行: {row})")

        except Exception as e:
            workflow.add_debug_info(f"填充第{row}行时出错: {str(e)}", is_error=True)
            continue

    workflow.add_debug_info(f"===== {sheet_name}工作表的CAN ID/DLC/Cycle列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_timeout(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    填充途絶タイムアウト列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 填充行数
    """
    workflow.add_debug_info("===== 开始填充途絶タイムアウト列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info("未找到Expected_results表头，跳过途絶タイムアウト填充", is_error=True)
        return 0

    data_label_col = find_header_column_contains(sheet, "Data Label", workflow)
    spec_col = find_header_column_contains(sheet, "引当元仕様書No", workflow)
    timeout_col = find_header_column_contains(sheet, "途絶タイムアウト", workflow, expected_range['min_col'],
                                              expected_range['max_col'])

    cycle_col = find_header_column_contains(sheet, "Com.Trans.Cycle", workflow)
    if not cycle_col:
        workflow.add_debug_info("尝试查找通信周期替代表头...")
        cycle_col = find_header_column_contains(sheet, "传输周期", workflow) or \
                    find_header_column_contains(sheet, "Cycle", workflow) or \
                    find_header_column_contains(sheet, "通信周期", workflow) or \
                    find_header_column_contains(sheet, "Transmission Cycle", workflow)

    if not all([data_label_col, spec_col, timeout_col, cycle_col]):
        missing = []
        if not data_label_col: missing.append("Data Label")
        if not spec_col: missing.append("引当元仕様書No")
        if not timeout_col: missing.append("途絶タイムアウト")
        if not cycle_col: missing.append("通信周期列")
        workflow.add_debug_info(f"缺少必要的列: {', '.join(missing)}", is_error=True)
        return 0

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    total_rows = end_row - start_row + 1
    filled_rows = 0

    spec_groups = {}

    for row in range(start_row, end_row + 1):
        signal_name = sheet.cell(row=row, column=data_label_col).value
        spec_file = sheet.cell(row=row, column=spec_col).value

        if signal_name and spec_file:
            signal_name = str(signal_name).strip()
            spec_file = str(spec_file).strip()

            if spec_file not in spec_groups:
                spec_groups[spec_file] = {
                    "signal_names": set(),
                    "rows": []
                }

            spec_groups[spec_file]["signal_names"].add(signal_name)
            spec_groups[spec_file]["rows"].append({
                "row": row,
                "signal_name": signal_name,
                "cycle_value": sheet.cell(row=row, column=cycle_col).value
            })

    for spec_file, group_data in spec_groups.items():
        workflow.add_debug_info(f"批量处理规格书: {spec_file}, 信号数量: {len(group_data['signal_names'])}")

        workflow.add_debug_info(f"{workflow.config.get('source_dir')}，“信息+++”，信号数量: {len(group_data['signal_names'])}")

        rag = get_rag_instance(spec_file, workflow.source_dir, workflow.config, workflow.rag_instances)

        if rag == "FILE_NOT_FOUND":
            workflow.add_debug_info(f"规格书文件不存在: {spec_file}，将对应行标记为'找不到文件'")
            for row_data in group_data["rows"]:
                row = row_data["row"]
                sheet.cell(row=row, column=timeout_col, value="找不到文件")
                filled_rows += 1
            continue

        if rag == "FILE_ERROR":
            workflow.add_debug_info(f"规格书文件错误: {spec_file}，将对应行标记为'文件错误'")
            for row_data in group_data["rows"]:
                row = row_data["row"]
                sheet.cell(row=row, column=timeout_col, value="文件错误")
                filled_rows += 1
            continue

        if not rag:
            continue

        for row_data in group_data["rows"]:
            row = row_data["row"]
            signal_name = row_data["signal_name"]
            cycle_value = row_data["cycle_value"]

            try:
                workflow.add_debug_info(f"\n处理第{row}行: 信号名='{signal_name}', 规格书='{spec_file}'")

                timeout_str = asyncio.run(
                    query_rag_with_retry(rag, signal_name, "timeout", spec_file, workflow.config, workflow.query_cache))

                timeout_value = None
                if timeout_str:
                    match = re.search(r'\d+', timeout_str)
                    if match:
                        timeout_value = int(match.group())
                        workflow.add_debug_info(f"提取到途绝时间: {timeout_value}秒")

                if timeout_value is None:
                    workflow.add_debug_info("未找到途绝时间，使用通信周期计算")
                    try:
                        cycle = float(cycle_value) if cycle_value else 0
                        calculated_value = cycle * 5
                        timeout_value = max(calculated_value, 3600)
                        workflow.add_debug_info(
                            f"计算得到途绝时间: max({cycle}*5={calculated_value}, 3600) = {timeout_value}")
                    except (ValueError, TypeError):
                        workflow.add_debug_info(f"无法计算途绝时间，通信周期值无效: {cycle_value}", is_error=True)
                        continue

                sheet.cell(row=row, column=timeout_col, value=timeout_value)
                filled_rows += 1

                processed = filled_rows
                progress = (processed / total_rows) * 100
                if processed % 10 == 0 or processed == total_rows:
                    workflow.add_debug_info(f"途絶タイムアウト列处理进度: {processed}/{total_rows} ({progress:.1f}%)")

            except Exception as e:
                workflow.add_debug_info(f"第{row}行处理出错: {str(e)}", is_error=True)
                continue

    workflow.add_debug_info(f"===== 途絶タイムアウト列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_data(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    填充data列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 填充行数
    """
    sheet_name = sheet.title
    workflow.add_debug_info(f"===== 开始填充{sheet_name}工作表的data列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info(f"{sheet_name}工作表: 未找到Expected_results表头，跳过data列填充", is_error=True)
        return 0

    data_label_col = find_header_column_contains(sheet, "Data Label", workflow)
    spec_col = find_header_column_contains(sheet, "引当元仕様書No", workflow)
    data_col = find_header_column_contains(sheet, "data", workflow, expected_range['min_col'],
                                           expected_range['max_col'])

    if not all([data_label_col, spec_col, data_col]):
        missing = []
        if not data_label_col: missing.append("Data Label")
        if not spec_col: missing.append("引当元仕様書No")
        if not data_col: missing.append("data")
        workflow.add_debug_info(f"缺少必要的列: {', '.join(missing)}", is_error=True)
        return 0

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    total_rows = end_row - start_row + 1
    filled_rows = 0

    spec_groups = {}
    for row in range(start_row, end_row + 1):
        signal_name = sheet.cell(row=row, column=data_label_col).value
        if not signal_name or str(signal_name).strip() == '':
            signal_name = get_previous_value(sheet, row, data_label_col, "Data Label", workflow)

        spec_file = sheet.cell(row=row, column=spec_col).value
        if not spec_file or str(spec_file).strip() == '':
            spec_file = get_previous_value(sheet, row, spec_col, "引当元仕様書No", workflow)

        if signal_name and spec_file:
            signal_name = str(signal_name).strip()
            spec_file = str(spec_file).strip()

            if spec_file not in spec_groups:
                spec_groups[spec_file] = []

            spec_groups[spec_file].append({
                "row": row,
                "signal_name": signal_name
            })

    for spec_file, rows in spec_groups.items():
        workflow.add_debug_info(f"处理规格书: {spec_file}, 信号数量: {len(rows)}")

        rag = get_rag_instance(spec_file, workflow.source_dir, workflow.config, workflow.rag_instances)

        if rag == "FILE_NOT_FOUND":
            workflow.add_debug_info(f"规格书文件不存在: {spec_file}，将对应行标记为'式样书未找到'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=data_col, value="式样书未找到")
                filled_rows += 1
            continue

        if rag == "FILE_ERROR":
            workflow.add_debug_info(f"规格书文件错误: {spec_file}，将对应行标记为'文件错误'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=data_col, value="文件错误")
                filled_rows += 1
            continue

        if not rag:
            continue

        for row_data in rows:
            row = row_data["row"]
            signal_name = row_data["signal_name"]

            try:
                workflow.add_debug_info(f"处理第{row}行: 信号名='{signal_name}'")

                data_info = asyncio.run(
                    query_rag_with_retry(rag, signal_name, "data", spec_file, workflow.config, workflow.query_cache))

                if data_info and data_info.strip() and data_info != "NOT_FOUND":
                    sheet.cell(row=row, column=data_col, value=data_info)
                    filled_rows += 1
                    workflow.add_debug_info(f"填充data: {data_info[:50]}...")
                else:
                    workflow.add_debug_info("未找到送信说明信息", is_error=True)
                    sheet.cell(row=row, column=data_col, value="式样书中未提及")
                    filled_rows += 1

                processed = filled_rows
                progress = (processed / total_rows) * 100
                if processed % 10 == 0 or processed == total_rows:
                    workflow.add_debug_info(f"data列处理进度: {processed}/{total_rows} ({progress:.1f}%)")

            except Exception as e:
                workflow.add_debug_info(f"第{row}行处理出错: {str(e)}", is_error=True)
                sheet.cell(row=row, column=data_col, value="查询错误")
                filled_rows += 1
                continue

    workflow.add_debug_info(f"===== {sheet_name}工作表的data列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_recovery(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    填充途绝后的动作列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 填充行数
    """
    workflow.add_debug_info("===== 开始填充途绝后的动作列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info("未找到Expected_results表头，跳过途绝后的动作填充", is_error=True)
        return 0

    data_label_col = find_header_column_contains(sheet, "Data Label", workflow)
    spec_col = find_header_column_contains(sheet, "引当元仕様書No", workflow)
    recovery_col = find_header_column_contains(sheet, "途绝后的动作", workflow, expected_range['min_col'],
                                               expected_range['max_col'])

    if not all([data_label_col, spec_col, recovery_col]):
        missing = []
        if not data_label_col: missing.append("Data Label")
        if not spec_col: missing.append("引当元仕様書No")
        if not recovery_col: missing.append("途绝后的动作")
        workflow.add_debug_info(f"缺少必要的列: {', '.join(missing)}", is_error=True)
        return 0

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    total_rows = end_row - start_row + 1
    filled_rows = 0

    spec_groups = {}
    for row in range(start_row, end_row + 1):
        signal_name = sheet.cell(row=row, column=data_label_col).value
        spec_file = sheet.cell(row=row, column=spec_col).value

        if signal_name and spec_file:
            signal_name = str(signal_name).strip()
            spec_file = str(spec_file).strip()

            if spec_file not in spec_groups:
                spec_groups[spec_file] = []

            spec_groups[spec_file].append({
                "row": row,
                "signal_name": signal_name
            })

    for spec_file, rows in spec_groups.items():
        workflow.add_debug_info(f"处理规格书: {spec_file}, 信号数量: {len(rows)}")

        rag = get_rag_instance(spec_file, workflow.source_dir, workflow.config, workflow.rag_instances)

        if rag == "FILE_NOT_FOUND":
            workflow.add_debug_info(f"规格书文件不存在: {spec_file}，将对应行标记为'找不到文件'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=recovery_col, value="找不到文件")
                filled_rows += 1
            continue

        if rag == "FILE_ERROR":
            workflow.add_debug_info(f"规格书文件错误: {spec_file}，将对应行标记为'文件错误'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=recovery_col, value="文件错误")
                filled_rows += 1
            continue

        if not rag:
            continue

        for row_data in rows:
            row = row_data["row"]
            signal_name = row_data["signal_name"]

            try:
                workflow.add_debug_info(f"处理第{row}行: 信号名='{signal_name}'")

                recovery_info = asyncio.run(
                    query_rag_with_retry(rag, signal_name, "recovery", spec_file, workflow.config,
                                         workflow.query_cache))

                if recovery_info and recovery_info.strip():
                    recovery_info = ' '.join(recovery_info.strip().split())
                    sheet.cell(row=row, column=recovery_col, value=recovery_info)
                    filled_rows += 1
                    workflow.add_debug_info(f"填充途绝后的动作: {recovery_info}")
                else:
                    workflow.add_debug_info("未找到途绝后的动作信息", is_error=True)
                    sheet.cell(row=row, column=recovery_col, value="(IGR-ON:未提及 IGR-OFF:未提及)")
                    filled_rows += 1

                processed = filled_rows
                progress = (processed / total_rows) * 100
                if processed % 10 == 0 or processed == total_rows:
                    workflow.add_debug_info(f"途绝后的动作列处理进度: {processed}/{total_rows} ({progress:.1f}%)")

            except Exception as e:
                workflow.add_debug_info(f"第{row}行处理出错: {str(e)}", is_error=True)
                continue

    workflow.add_debug_info(f"===== 途绝后的动作列填充完成，共填充{filled_rows}行 =====")
    return filled_rows


def fill_invalid_values(sheet: Worksheet, workflow: TeleTableFillerWorkflow) -> int:
    """
    填充无效值列

    Args:
        sheet: 工作表
        workflow: 工作流实例

    Returns:
        int: 填充行数
    """
    workflow.add_debug_info("===== 开始填充无效值列 =====")

    expected_range = find_expected_results_range(sheet, workflow)
    if not expected_range:
        workflow.add_debug_info("未找到Expected_results表头，跳过无效值填充", is_error=True)
        return 0

    data_label_col = find_header_column_contains(sheet, "Data Label", workflow)
    spec_col = find_header_column_contains(sheet, "引当元仕様書No", workflow)
    invalid_col = find_header_column_contains(sheet, "无效值", workflow, expected_range['min_col'],
                                              expected_range['max_col'])

    if not all([data_label_col, spec_col, invalid_col]):
        missing = []
        if not data_label_col: missing.append("Data Label")
        if not spec_col: missing.append("引当元仕様書No")
        if not invalid_col: missing.append("无效值")
        workflow.add_debug_info(f"缺少必要的列: {', '.join(missing)}", is_error=True)
        return 0

    start_row = get_data_start_row(sheet, workflow)
    end_row = sheet.max_row
    total_rows = end_row - start_row + 1
    filled_rows = 0

    spec_groups = {}
    for row in range(start_row, end_row + 1):
        signal_name = sheet.cell(row=row, column=data_label_col).value
        spec_file = sheet.cell(row=row, column=spec_col).value

        if signal_name and spec_file:
            signal_name = str(signal_name).strip()
            spec_file = str(spec_file).strip()

            if spec_file not in spec_groups:
                spec_groups[spec_file] = []

            spec_groups[spec_file].append({
                "row": row,
                "signal_name": signal_name
            })

    for spec_file, rows in spec_groups.items():
        workflow.add_debug_info(f"处理规格书: {spec_file}, 信号数量: {len(rows)}")

        rag = get_rag_instance(spec_file, workflow.source_dir, workflow.config, workflow.rag_instances)

        if rag == "FILE_NOT_FOUND":
            workflow.add_debug_info(f"规格书文件不存在: {spec_file}，将对应行标记为'找不到文件'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=invalid_col, value="找不到文件")
                filled_rows += 1
            continue

        if rag == "FILE_ERROR":
            workflow.add_debug_info(f"规格书文件错误: {spec_file}，将对应行标记为'文件错误'")
            for row_data in rows:
                row = row_data["row"]
                sheet.cell(row=row, column=invalid_col, value="文件错误")
                filled_rows += 1
            continue

        if not rag:
            continue

        for row_data in rows:
            row = row_data["row"]
            signal_name = row_data["signal_name"]

            try:
                workflow.add_debug_info(f"处理第{row}行: 信号名='{signal_name}'")

                invalid_values_str = asyncio.run(
                    query_rag_with_retry(rag, signal_name, "invalid", spec_file, workflow.config, workflow.query_cache))

                if invalid_values_str and invalid_values_str.strip():
                    invalid_values = re.split(r'[,\s，；;]+', invalid_values_str.strip())
                    invalid_values = [v.strip() for v in invalid_values if v.strip()]

                    if invalid_values:
                        result = ", ".join(invalid_values)
                        sheet.cell(row=row, column=invalid_col, value=result)
                        filled_rows += 1
                        workflow.add_debug_info(f"填充无效值: {result}")
                    else:
                        workflow.add_debug_info("未提取到有效无效值列表", is_error=True)
                        sheet.cell(row=row, column=invalid_col, value="无")
                        filled_rows += 1
                else:
                    workflow.add_debug_info("未找到无效值信息", is_error=True)
                    sheet.cell(row=row, column=invalid_col, value="未找到")
                    filled_rows += 1

                processed = filled_rows
                progress = (processed / total_rows) * 100
                if processed % 10 == 0 or processed == total_rows:
                    workflow.add_debug_info(f"无效值列处理进度: {processed}/{total_rows} ({progress:.1f}%)")

            except Exception as e:
                workflow.add_debug_info(f"第{row}行处理出错: {str(e)}", is_error=True)
                continue

    workflow.add_debug_info(f"===== 无效值列填充完成，共填充{filled_rows}行 =====")
    return filled_rows