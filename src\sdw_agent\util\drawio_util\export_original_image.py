#!/usr/bin/env python3
"""
导出原始 Draw.io 图像到 Excel
保持原始图表的完整视觉效果
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from openpyxl.drawing.image import Image as OpenpyxlImage

from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def find_drawio_executable() -> Optional[str]:
    """查找 draw.io 可执行文件"""
    possible_paths = [
        "drawio",  # 如果在PATH中
        "draw.io",
        r"C:\Program Files\draw.io\draw.io.exe",
        r"C:\Program Files (x86)\draw.io\draw.io.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\draw.io\draw.io.exe".format(os.getenv('USERNAME', '')),
        "/Applications/draw.io.app/Contents/MacOS/draw.io",  # macOS
        "/usr/bin/drawio",  # Linux
        "/usr/local/bin/drawio"
    ]
    
    for path in possible_paths:
        if check_executable(path):
            logger.info(f"找到 draw.io 可执行文件: {path}")
            return path
            
    logger.warning("未找到 draw.io 可执行文件")
    return None


def check_executable(path: str) -> bool:
    """检查可执行文件是否存在且可用"""
    try:
        if not os.path.exists(path):
            return False
        result = subprocess.run([path, "--version"], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
        return False


def export_drawio_to_png(drawio_file: str, 
                        output_png: str,
                        scale: float = 2.0,
                        page_index: int = 0) -> bool:
    """
    将 Draw.io 文件导出为 PNG 图像
    
    Args:
        drawio_file: Draw.io 文件路径
        output_png: 输出 PNG 文件路径
        scale: 缩放比例（建议2.0以获得高清图像）
        page_index: 页面索引（从0开始）
        
    Returns:
        是否成功导出
    """
    drawio_exe = find_drawio_executable()
    if not drawio_exe:
        logger.error("未找到 draw.io 可执行文件，请先安装 draw.io desktop")
        return False
        
    if not Path(drawio_file).exists():
        logger.error(f"Draw.io 文件不存在: {drawio_file}")
        return False
    
    # 构建命令行参数
    cmd = [
        drawio_exe,
        "--export",
        "--format", "png",
        "--page-index", str(page_index),
        "--scale", str(scale),
        "--crop",  # 裁剪空白区域
        "--output", output_png,
        drawio_file
    ]
    
    try:
        logger.info(f"正在导出 Draw.io 图像: {drawio_file} -> {output_png}")
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, 
                              capture_output=True, 
                              text=True, 
                              timeout=60)
        
        if result.returncode == 0:
            if Path(output_png).exists():
                logger.success(f"Draw.io 图像导出成功: {output_png}")
                return True
            else:
                logger.error("导出命令执行成功但未找到输出文件")
                return False
        else:
            logger.error(f"Draw.io 导出失败: {result.stderr}")
            logger.error(f"返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("Draw.io 导出超时")
        return False
    except Exception as e:
        logger.error(f"Draw.io 导出异常: {str(e)}")
        return False


def insert_original_drawio_to_excel(drawio_file: str,
                                  excel_file: str,
                                  sheet_name: str = "架构图",
                                  title: Optional[str] = None,
                                  scale: float = 2.0,
                                  max_width: int = 1200,
                                  max_height: int = 800) -> Dict[str, Any]:
    """
    将原始 Draw.io 图表插入到 Excel 中
    
    Args:
        drawio_file: Draw.io 文件路径
        excel_file: Excel 文件路径
        sheet_name: 工作表名称
        title: 图表标题
        scale: 导出缩放比例
        max_width: 图像最大宽度
        max_height: 图像最大高度
        
    Returns:
        操作结果
    """
    try:
        # 1. 导出 Draw.io 为高清 PNG
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_png = os.path.join(temp_dir, "drawio_export.png")
            
            if not export_drawio_to_png(drawio_file, temp_png, scale):
                return {
                    "success": False,
                    "message": "Draw.io 图像导出失败，请确保已安装 draw.io desktop",
                    "excel_file": excel_file
                }
            
            # 2. 插入图像到 Excel
            with ExcelUtil(excel_file, auto_create=True) as excel:
                # 确保工作表存在
                if sheet_name not in excel.get_sheet_names():
                    excel.create_sheet(sheet_name)
                
                current_row = 1
                
                # 添加标题（如果提供）
                if title:
                    excel.write_cell(sheet_name, current_row, 1, title)
                    title_style = CellStyle(
                        font_size=16,
                        font_bold=True,
                        alignment_horizontal="center"
                    )
                    excel.set_cell_style(sheet_name, current_row, 1, title_style)
                    current_row += 2  # 留出空行
                
                # 插入原始图像
                success = insert_image_to_excel_sheet(
                    excel=excel,
                    sheet_name=sheet_name,
                    image_path=temp_png,
                    row=current_row,
                    col=1,
                    max_width=max_width,
                    max_height=max_height
                )
                
                if success:
                    excel.save()
                    logger.success(f"原始 Draw.io 图表已成功插入到 Excel: {excel_file}")
                    return {
                        "success": True,
                        "message": "原始 Draw.io 图表插入成功",
                        "excel_file": excel_file,
                        "sheet_name": sheet_name,
                        "image_position": f"行{current_row}, 列1"
                    }
                else:
                    return {
                        "success": False,
                        "message": "图像插入 Excel 失败",
                        "excel_file": excel_file
                    }
                    
    except Exception as e:
        error_msg = f"插入原始 Draw.io 图表到 Excel 时发生异常: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "message": error_msg,
            "excel_file": excel_file
        }


def insert_image_to_excel_sheet(excel: ExcelUtil,
                               sheet_name: str,
                               image_path: str,
                               row: int,
                               col: int,
                               max_width: Optional[int] = None,
                               max_height: Optional[int] = None) -> bool:
    """
    将图像插入到 Excel 工作表中
    """
    try:
        # 使用 openpyxl 的方式插入图像
        if hasattr(excel, '_get_worksheet'):
            ws = excel._get_worksheet(sheet_name)
            if ws:
                img = OpenpyxlImage(image_path)
                
                # 调整图像大小
                if max_width or max_height:
                    original_width = img.width
                    original_height = img.height
                    
                    if max_width and original_width > max_width:
                        scale_factor = max_width / original_width
                        img.width = max_width
                        img.height = int(original_height * scale_factor)
                    
                    if max_height and img.height > max_height:
                        scale_factor = max_height / img.height
                        img.height = max_height
                        img.width = int(img.width * scale_factor)
                
                # 设置图像位置
                target_cell = ws.cell(row=row, column=col)
                img.anchor = target_cell.coordinate
                
                # 添加图像
                ws.add_image(img)
                
                # 调整行高以适应图像
                ws.row_dimensions[row].height = max(ws.row_dimensions[row].height or 15, 
                                                   img.height * 0.75)  # 转换为点
                
                logger.info(f"图像已插入到 {sheet_name} 工作表，位置: {target_cell.coordinate}")
                logger.info(f"图像尺寸: {img.width} x {img.height}")
                return True
        
        logger.error("无法获取工作表对象")
        return False
        
    except Exception as e:
        logger.error(f"插入图像失败: {str(e)}")
        return False


def demo_export_original_image():
    """演示导出原始 Draw.io 图像"""
    print("🎯 导出原始 Draw.io 图像演示")
    print("=" * 50)
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用 Draw.io 文件: {drawio_file}")
    
    # 2. 检查 draw.io 可执行文件
    drawio_exe = find_drawio_executable()
    if not drawio_exe:
        print("❌ 未找到 draw.io 可执行文件")
        print("💡 请安装 draw.io desktop:")
        print("   - Windows: https://github.com/jgraph/drawio-desktop/releases")
        print("   - macOS: brew install --cask drawio")
        print("   - Linux: 下载 AppImage 或使用包管理器安装")
        return
    
    print(f"✅ 找到 draw.io: {drawio_exe}")
    
    # 3. 导出到 Excel
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    excel_file = output_dir / "original_architecture_diagram.xlsx"
    
    result = insert_original_drawio_to_excel(
        drawio_file=drawio_file,
        excel_file=str(excel_file),
        sheet_name="原始架构图",
        title="系统架构图（原始版本）",
        scale=2.0,  # 高清导出
        max_width=1400,
        max_height=1000
    )
    
    # 4. 显示结果
    print(f"\n📊 导出结果:")
    print(f"   成功: {result['success']}")
    print(f"   消息: {result['message']}")
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   图像位置: {result['image_position']}")
        print(f"\n🎉 请打开 Excel 文件查看原始图表！")
    else:
        print(f"   错误详情: {result['message']}")


if __name__ == "__main__":
    demo_export_original_image()
