# file: regulation_function_confirm_router.py

from fastapi import APIRouter
from pydantic import BaseModel
from typing import Dict, Any
from loguru import logger

from sdw_agent.service.regulation_function_confirm.regulaion_confirm_workflow import RegulationFunctionWorkflow, RegulationImpact
from sdw_agent.service import WorkflowStatus, WorkflowResult
router = APIRouter(prefix="/api/sdw", tags=["法規確認"])

class RegulationConfirmRequest(BaseModel):
    input_file_path: str
    kb_file_path: str = None


class RegulationConfirmResponse(BaseModel):
    code: int
    message: str
    data: Dict[str, Any] = {}

@router.post("/regulation_function_confirm",
             summary="法規確認",
             description="对法规功能变更内容进行法规评估分类并写回结果。",
             response_model=RegulationConfirmResponse)
async def regulation_function_confirm(request: RegulationConfirmRequest):
    try:
        # 初始化工作流实例
        workflow = RegulationFunctionWorkflow()

        # 直接调用execute方法
        result = workflow.execute(
            origin_file_path=request.input_file_path,
            kb_file_path=request.kb_file_path
        )

        # 处理执行结果
        if result.status == WorkflowStatus.SUCCESS:
            result_data = result.data.get("results", [])

            return RegulationConfirmResponse(
                code=0,
                message="法规功能变更处理成功",
                data={
                    "modified_file": request.input_file_path,
                    "results": result_data,
                    "total_count": len(result_data),
                    "changed_count": sum(1 for r in result_data
                                         if r.get("is_changed") == RegulationImpact.YES.value)
                }
            )
        else:
            # 工作流执行失败
            logger.error(f"工作流执行失败: {result.message}")
            return RegulationConfirmResponse(
                code=500,
                message=f"法规功能变更处理失败: {result.message or '未知错误'}",
                data={}
            )

    except Exception as e:
        logger.error(f"法规评估处理失败: {str(e)}")
        return RegulationConfirmResponse(
            code=500,
            message=f"法规功能变更处理失败: {str(e)}",
            data={}
        )

