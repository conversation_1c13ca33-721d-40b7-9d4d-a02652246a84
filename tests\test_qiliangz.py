import httpx
import json

from sdw_agent.service.base_car_select.utils.car_info_resolver import CarInfoResolver


# def test_get_functionbook():
#     """
#     测试Ram设计书作成接口
#     发送真实的 HTTP POST 请求到指定的 FastAPI 路由。
#     """
#     # 定义接口地址
#     BASE_URL = "http://127.0.0.1:8001/api/sdw/function_book/getFunctionbook"
#
#     # 定义请求数据
#     payload = {
#         "commit_id": "7d5e5e8a9910c95fc97ec51da47ff5b8fd6db7a6",
#         "git_code_path": r"D:\dnkt_19v3\gfx_agl_met",
#         "component_book_path": r"D:\tdd_input\684D_Component_List.xlsx",
#         "git_branch": r"dnkt_develop"
#     }
#
#     # 发送 POST 请求
#     try:
#         response = httpx.post(BASE_URL, json=payload, timeout=600)
#
#         # 打印请求结果
#         print("Status Code:", response.status_code)
#         print("Response Body:", response.text)
#
#         # 简单验证响应
#         if response.status_code == 200:
#             print("POST 请求成功")
#         else:
#             print("POST 请求失败，HTTP 状态码:", response.status_code)
#
#     except Exception as e:
#         print("请求过程中出现错误:", str(e))


def test_resolve_car_info():
    car_info_resolver = CarInfoResolver(db_path=r"D:\tdd_input\面開発管理用_標準仕様DB_231110_v0093.xlsm")

    res = car_info_resolver._identify_excel_type()
    print(res)


# 调用测试函数
if __name__ == "__main__":
    # test_get_functionbook()

    test_resolve_car_info()
