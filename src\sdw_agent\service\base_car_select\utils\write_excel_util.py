"""
@File    : write_excel_util.py
@Time    : 2025/7/30 15:33
<AUTHOR> qiliang.zhou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 base 车辆选定
@Desc    : $ 成果物写入
"""
from typing import Union, List

from sdw_agent.service.base_car_select.model import CarDocInfoModel
from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange


class WriteExcelUtil(ExcelUtil):
    """
    自定义Excel工具类

    继承自ExcelUtil，提供额外的Excel操作功能，包括：
    1. 获取工作表的最大行索引和最大列索引
    2. 其他自定义功能
    """

    def __init__(self, file_path: str, logger, engine: str = "openpyxl",
                 auto_create: bool = True):
        """
        初始化自定义Excel工具

        Args:
            file_path: Excel文件路径
            engine: 操作引擎类型（"win32com", "openpyxl" 或 "pandas"）
            auto_create: 如果文件不存在是否自动创建
        """
        super().__init__(file_path, engine, auto_create)
        self.logger = logger

    def _ensure_sheet_exists(self, sheet_name: str) -> None:
        """
        确保指定的工作表存在，如果不存在则创建

        Args:
            sheet_name: 工作表名称
        """
        try:
            # 获取现有工作表列表
            existing_sheets = self.get_sheet_names()

            if sheet_name not in existing_sheets:
                self.logger.info(f"工作表 '{sheet_name}' 不存在，正在创建...")

                # 使用openpyxl引擎创建新工作表
                if hasattr(self.engine, 'workbook'):
                    # 创建新工作表
                    self.engine.workbook.create_sheet(title=sheet_name)
                    self.logger.info(f"已创建工作表 '{sheet_name}'")
                else:
                    self.logger.warning(f"无法创建工作表 '{sheet_name}'，引擎不支持")
            else:
                self.logger.debug(f"工作表 '{sheet_name}' 已存在")

        except Exception as e:
            self.logger.error(f"确保工作表存在时出错: {str(e)}")
            # 不抛出异常，让后续流程继续

    def save_recommend_info(self, new_car_doc_info:CarDocInfoModel, recommend_base_info):

        if new_car_doc_info is None:
            raise ValueError("新型车辆的式样信息解析失败")

        if not recommend_base_info:
            raise ValueError("未找到推荐Base车辆信息")

        new_car_name = new_car_doc_info.version +'\n'+new_car_doc_info.car_name
        new_car_docs = new_car_doc_info.doc_no

        self._ensure_sheet_exists("目次差分结果")

        for base_car in recommend_base_info:
            write_sheet_name = base_car['version'] +'_'+base_car['car_name']
            base_car_name = base_car['version'] +'\n'+base_car['car_name']
            doc_pairs = base_car['same_doc_pairs']+base_car['similar_doc_pairs']+base_car['only_new_docs']+base_car['only_base_docs']
            processed_doc_pairs = self._compare_pair(doc_pairs)

            self._ensure_sheet_exists(write_sheet_name)
            # 写入表头数据
            self.write_range(write_sheet_name, 2,2, [[new_car_name, base_car_name, "对比结果"]])
            self._set_header_style(write_sheet_name, 2, 4)
            # 写入数据
            self.write_range(write_sheet_name, 3, 2, processed_doc_pairs)
            cell_range = CellRange(start_row=3, start_col=2, end_row=len(processed_doc_pairs)+2, end_col=4)
            self._set_data_style(write_sheet_name, cell_range)

            self._auto_fit_columns_with_limits(write_sheet_name, 2, 3)

        if 'Sheet' in [work_sheet.title for work_sheet in self.engine.workbook.worksheets]:
            self.engine.workbook.remove(self.engine.workbook['Sheet'])
        self.save()

        return self.file_path

    def _set_header_style(self, sheet_name, start_row, end_col):
        """
        设置表头样式
        Args:
            sheet_name:
            start_row:
            end_col:

        Returns:

        """
        header_style = CellStyle(
            font_name="Meiryo UI",
            font_size=11,
            font_bold=True,
            font_color="000000",  # 黑色字体
            bg_color="00B0F0",
            alignment_horizontal="center",
            alignment_vertical="center",
            border_style="thin"
        )
        # 设置表头样式，列宽自适应
        # 应用表头样式
        for col_idx in range(2, end_col+1):
            self.set_cell_style(sheet_name, start_row, col_idx, header_style)

    def _set_data_style(self, sheet_name: str, cell_range: Union[CellRange, str]):
        """
        按照范围设置单元格样式
        Args:
            sheet_name:
            cell_range:
        """
        # 设置数据行样式
        data_style = CellStyle(
            font_name="Meiryo UI",
            font_size=10,
            font_color="000000",
            border_style="thin"
        )

        self.set_range_style(sheet_name, cell_range, data_style)

    @staticmethod
    def _compare_pair(doc_pairs:List[List[str]]):
        """
        对比New 和 Base 车辆的式样信息
        Args:
            doc_pairs:

        Returns:

        """
        for pair in doc_pairs:
            if pair[0] == pair[1]:
                pair.append("変更無し")
            elif (pair[0] == "" or pair[0] is None) and pair[1]:
                pair.append("削除")
            elif (pair[1] == "" or pair[1] is None) and pair[0]:
                pair.append("追加")
            elif pair[0] and pair[1] and pair[0] != pair[1]:
                pair.append("変更あり")
        return doc_pairs

    def _auto_fit_columns_with_limits(self, sheet_name: str, start_col: int, col_count: int) -> None:
        """
        自动调整列宽，设置最小宽度和最大宽度限制（专为openpyxl引擎优化）

        Args:
            sheet_name: 工作表名称
            start_col: 起始列号
            col_count: 列数
        """
        try:
            from openpyxl.utils import get_column_letter

            # 获取工作表
            worksheet = self.engine.workbook[sheet_name]

            # 遍历每一列进行宽度调整
            for i in range(col_count):
                col_num = start_col + i
                col_letter = get_column_letter(col_num)

                # 计算列的最大内容长度
                max_length = 0
                for row in range(1, worksheet.max_row + 1):
                    cell = worksheet.cell(row=row, column=col_num)
                    if cell.value is not None:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length

                # 设置列宽：最小12，最大50，内容长度+2的缓冲（与现有逻辑保持一致）
                adjusted_width = max(min(max_length + 2, 50), 12)
                worksheet.column_dimensions[col_letter].width = adjusted_width

            self.logger.info(f"已自动调整列宽，列数: {col_count}")

        except Exception as e:
            self.logger.warning(f"自动调整列宽失败: {str(e)}")

