"""
文件名: func_file_parser_util.py

功能:
该文件包含一系列用于解析代码文件的工具函数和类，主要用于分析函数调用关系、查找函数定义和调用位置，以及处理多线程和多进程的并行分析任务。

主要功能:
1. 判断函数是否在头文件中声明。
2. 查找指定函数的调用位置及上下文。
3. 递归分析目录中的代码文件，提取函数调用信息。
4. 支持多线程和多进程并行处理，提高分析效率。
5. 解析外部函数调用，查找其定义并返回详细信息。

主要模块:
- `is_function_in_header`: 判断函数是否在头文件中声明。
- `FastFunctionCallAnalyzer`: 类，用于快速分析函数调用关系。
- `analyze_directory`: 在目录中递归查找函数调用信息（多线程实现）。
- `analyze_dir`: 在目录中递归查找函数调用信息（多进程实现）。
- `parse_external_functions`: 解析函数中调用的外部函数。
"""
import concurrent
from multiprocessing import Pool, cpu_count
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, namedtuple
from typing import List, Dict, Tuple, NamedTuple, Optional

import mmap
from pathlib import Path
from loguru import logger

import chardet

from sdw_agent.util.function_util import extract_called_functions, process_function, find_macro_definition

# 定义结果数据结构
FunctionCallSite = namedtuple('FunctionCallSite', [
    'file_path',
    'caller_function',
    'line_number',
    'context'
])

FunctionAnalysisResult = namedtuple('FunctionAnalysisResult', [
    'target_function',
    'call_sites'
])


def is_function_in_header(directory: str, function_name: str) -> bool:
    """
    高效判断函数是否在头文件中声明

    :param directory: 代码文件目录
    :param function_name: 函数名
    :return: 如果函数在头文件中声明则返回 True
    """
    # 编译正则表达式模式
    # 匹配函数声明模式：返回类型 + 函数名 + 参数列表
    pattern = re.compile(
        rb'\b' + re.escape(function_name.encode()) + rb'\s*\([^)]*\)\s*;',
        re.MULTILINE
    )

    # 遍历目录中的所有 .h 文件
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.h'):
                file_path = Path(root) / file

                try:
                    # 使用内存映射高效读取大文件
                    with open(file_path, 'rb') as f:
                        # 使用内存映射文件
                        with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                            # 搜索函数声明
                            if pattern.search(mm):
                                return True

                except Exception:
                    # 如果内存映射失败，使用常规方法
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                            if pattern.search(content):
                                return True
                    except Exception:
                        continue

    return False


class FastFunctionCallAnalyzer:
    def __init__(self, repo_path: Path, target_function: str):
        self.repo_path = repo_path
        self.target_function = target_function
        self.extensions = {'.c', '.h', '.cpp', '.hpp', '.cc', '.cxx', '.hh', '.hxx', '.py', '.java', '.js', '.ts'}
        self.exclude_dirs = {'build', 'dist', 'node_modules', 'venv', '__pycache__', '.git', '.svn'}
        self.function_def_pattern = re.compile(
            r'^\s*(?:(?:static|inline|const)\s+)?'  # 修饰符
            r'\b\w+\s+'  # 返回类型
            r'(\w+)\s*'  # 函数名 (捕获组1)
            r'\([^)]*\)\s*'  # 参数列表
            r'\{'  # 函数体开始
        )

    def find_all_files(self) -> List[Path]:
        """快速查找所有源文件"""
        files = []
        for dirpath, _, filenames in os.walk(self.repo_path):
            # 过滤排除目录
            if any(exclude in dirpath for exclude in self.exclude_dirs):
                continue

            for filename in filenames:
                file_path = Path(dirpath) / filename
                if file_path.suffix in self.extensions:
                    files.append(file_path)
        return files

    def find_function_calls_in_file(self, file_path: Path) -> List[FunctionCallSite]:
        """在单个文件中查找函数调用位置"""
        try:
            # 使用内存映射高效读取大文件
            with file_path.open('rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                    content = mm.read().decode('utf-8', errors='ignore')
                    return self._find_calls_in_content(file_path, content)
        except Exception as e:
            logger.error(f"Error reading {file_path}: {str(e)}")
            return []

    def _find_calls_in_content(self, file_path: Path, content: str) -> List[FunctionCallSite]:
        """在文件内容中查找函数调用"""
        # 构建调用模式
        call_pattern = re.compile(rf'\b{re.escape(self.target_function)}\s*\(')

        # 获取所有行号以便快速定位
        line_starts = [0]
        pos = 0
        while pos < len(content):
            pos = content.find('\n', pos)
            if pos == -1:
                break
            pos += 1
            line_starts.append(pos)

        # 查找所有函数定义
        function_defs = {}
        for match in self.function_def_pattern.finditer(content):
            start_line = content.count('\n', 0, match.start()) + 1
            function_name = match.group(1)
            function_defs[start_line] = function_name

        # 查找调用位置
        results = []
        for match in call_pattern.finditer(content):
            # 查找行号
            match_end = match.end()
            line_num = next(i for i, start in enumerate(line_starts) if start > match_end)

            # 查找最近的函数定义
            caller_func = "Global Scope"
            for def_line in sorted(function_defs.keys(), reverse=True):
                if def_line <= line_num:
                    caller_func = function_defs[def_line]
                    break

            # 获取调用上下文
            context_start = max(0, line_num - 3)  # 前3行
            context_end = min(line_num + 2, len(line_starts) - 1)  # 后2行

            context = ""
            if context_start < len(line_starts) and context_end < len(line_starts):
                start_pos = line_starts[context_start]
                end_pos = line_starts[context_end + 1] if context_end + 1 < len(line_starts) else len(content)
                context = content[start_pos:end_pos]

            results.append(FunctionCallSite(
                file_path=file_path,
                caller_function=caller_func,
                line_number=line_num,
                context=context
            ))

        return results

    def analyze(self) -> FunctionAnalysisResult:
        """执行完整分析"""
        start_time = time.time()

        # 查找所有文件
        files = self.find_all_files()
        logger.debug(f"Found {len(files)} source files to analyze")

        # 并行处理文件
        call_sites = []
        with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
            futures = {executor.submit(self.find_function_calls_in_file, f): f for f in files}

            for future in as_completed(futures):
                file_path = futures[future]
                try:
                    results = future.result()
                    if results:
                        call_sites.extend(results)
                        logger.info(f"Found {len(results)} calls in {file_path.relative_to(self.repo_path)}")
                except Exception as e:
                    logger.error(f"Error processing {file_path}: {str(e)}")

        # 结果统计
        elapsed = time.time() - start_time
        logger.info(f"\nAnalysis complete in {elapsed:.2f} seconds")
        logger.info(f"Found {len(call_sites)} call sites for '{self.target_function}'")

        return FunctionAnalysisResult(
            target_function=self.target_function,
            call_sites=call_sites
        )


def format_results(result: FunctionAnalysisResult) -> str:
    """格式化分析结果"""
    output = []
    output.append(f"Analysis of calls to '{result.target_function}'")
    output.append("=" * 80)

    # 按文件分组
    files = defaultdict(list)
    for site in result.call_sites:
        files[site.file_path].append(site)

    # 按文件名排序
    for file_path, sites in sorted(files.items(), key=lambda x: str(x[0])):
        rel_path = file_path.relative_to(result.call_sites[0].file_path.parent)
        output.append(f"\nFile: {rel_path}")
        output.append("-" * 80)

        # 按调用者分组
        callers = defaultdict(list)
        for site in sites:
            callers[site.caller_function].append(site)

        # 按调用者排序
        for caller, caller_sites in sorted(callers.items()):
            output.append(f"\n  Called by: {caller}")
            for site in caller_sites:
                output.append(f"    Line {site.line_number}:")
                # 格式化上下文
                context_lines = site.context.split('\n')
                for i, line in enumerate(context_lines):
                    prefix = ">>" if site.line_number - len(context_lines) + i + 1 == site.line_number else "  "
                    output.append(f"{prefix} {line.strip()}")
                output.append("")

    return "\n".join(output)


def save_results_to_file(result: FunctionAnalysisResult, output_path: Path):
    """保存结果到文件"""
    formatted = format_results(result)
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(formatted)
    logger.success(f"Results saved to: {output_path}")


def analy_func_called_info(target_function, repo_path):
    """
    分析目标函数的调用信息。

    功能说明:
    - 使用 `FastFunctionCallAnalyzer` 类对代码库进行分析，查找目标函数的调用位置及上下文信息。
    - 返回分析结果，包括目标函数的调用位置列表。

    参数:
        target_function (str): 要分析的目标函数名称。
        repo_path (str): 代码库的路径。

    返回:
        FunctionAnalysisResult: 分析结果，包含目标函数名称和调用位置列表。

    异常:
        如果分析过程中发生错误，将抛出异常。
    """
    # 执行分析
    analyzer = FastFunctionCallAnalyzer(repo_path, target_function)
    result = analyzer.analyze()
    # 保存结果
    logger.success(f"Found {len(result.call_sites)} call sites to '{target_function}'")
    return result


import os
import re
from pathlib import Path


def read_file_with_encoding(file_path):
    """尝试多种编码读取文件，解决编码错误问题"""
    # 常见编码列表（优先尝试Windows相关编码，因为C文件在Windows上常使用这些编码）
    encodings = ['utf-8', 'latin-1', 'Windows-1252', 'gbk', 'gb2312']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            continue

    # 最后尝试二进制读取并替换无法解码的字符（兜底策略）
    try:
        with open(file_path, 'rb') as f:
            return f.read().decode('utf-8', errors='replace')  # 无法解码的字符用�替换
    except Exception as e:
        logger.error(f"二进制读取文件 {file_path} 失败: {e}")
        return None


def analyze_file(file_path, target_function):
    """
    在指定C/C++文件(.c/.h)中查找调用目标函数的父函数及其函数体
    优化编码处理，解决非UTF-8编码问题
    """
    result = {}
    try:
        # 读取文件（使用增强的编码处理）
        content = read_file_with_encoding(file_path)
        if content is None:
            logger.error(f"无法读取文件: {file_path}")
            return result

        # C/C++ 函数模式（适配.h和.c文件）
        func_pattern = re.compile(r'''
            ^\s*                  # 行首可能的空格
            ([\w\*\s]+?)\s+       # 返回类型（支持指针、const等修饰符）
            (\w+)\s*             # 函数名
            \([^)]*\)\s*         # 参数列表
            \{                   # 函数开始（.h中可能是声明，但此处只处理有实现的函数）
        ''', re.VERBOSE | re.MULTILINE)

        # 目标函数调用模式（支持带参数、分号结尾等情况）
        call_pattern = re.compile(
            r'\b' + re.escape(target_function) + r'\s*\([^)]*\);?'
        )

        # 查找所有函数
        for match in func_pattern.finditer(content):
            func_name = match.group(2)  # C函数名在第二个捕获组
            func_start = match.start()

            # 查找函数结束位置（匹配花括号，处理嵌套）
            brace_count = 1
            func_end = func_start + 1
            while func_end < len(content):
                if content[func_end] == '{':
                    brace_count += 1
                elif content[func_end] == '}':
                    brace_count -= 1
                if brace_count == 0:
                    break
                func_end += 1

            # 提取函数体
            func_body = content[func_start:func_end + 1]

            # 检查是否调用目标函数
            if call_pattern.search(func_body):
                # 计算行号
                line_number = content.count('\n', 0, func_start) + 1

                if func_name not in result:
                    result[func_name] = []
                result[func_name].append({
                    "line": line_number,
                    "function_body": func_body
                })

    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {e}")

    return result


def analyze_file(file_path, func_name):
    """
    在单个文件中查找函数 func_name 的调用信息。

    Args:
        file_path (str): 文件路径。
        func_name (str): 要查找的函数名。

    Returns:
        list[dict]: 包含调用信息的字典列表，每个字典包含：
            - "parent_function": 调用的父函数名。
            - "call_lines": 调用的行号列表。
            - "call_body": 调用的完整函数体。
            - "file_name": 调用文件名。
    """
    # results = []
    results = {}

    try:
        # 自动检测文件编码
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            encoding = chardet.detect(raw_data)['encoding']

        with open(file_path, 'r', encoding=encoding) as f:
            lines = f.readlines()

        # 读取文件内容
        content = "".join(lines)

        # 使用正则表达式匹配函数定义和调用
        function_pattern = re.compile(r"\b(\w+)\s*\([^)]*\)\s*{")  # 匹配函数定义
        call_pattern = re.compile(rf"\b{func_name}\s*\([^)]*\);")  # 匹配函数调用

        # 查找所有函数定义
        function_starts = [(m.start(), m.group(1)) for m in function_pattern.finditer(content)]

        # 遍历每个函数定义，查找其内部是否调用了目标函数
        for i, (start, parent_function) in enumerate(function_starts):
            # 确定函数体范围
            end = function_starts[i + 1][0] if i + 1 < len(function_starts) else len(content)
            function_body = content[start:end]

            # 查找目标函数调用
            call_matches = list(call_pattern.finditer(function_body))

            if call_matches:
                call_lines = [content[:m.start()].count("\n") + 1 for m in call_matches]
                results[parent_function] = {
                    "line": call_lines,
                    "function_body": function_body
                }

    except Exception as e:
        logger.error(f"Error analyzing file {file_path}: {e}")

    return results


#
def analyze_directory(directory_path: str, target_function: str,
                      supported_extensions: Optional[List[str]] = None,
                      max_workers: Optional[int] = 8) -> Dict[str, List[Dict]]:
    """
    在指定目录中递归查找调用目标函数的父函数及其函数体（多线程并行版）

    参数:
        directory_path: 目录路径
        target_function: 目标函数名
        supported_extensions: 支持的文件扩展名列表，默认为 ['.cpp', '.c']
        max_workers: 最大线程数，默认为 CPU 核心数 * 2

    返回:
        字典，格式为:
        {
            "父函数名": [
                {
                    "file": "文件完整路径",
                    "line": 行号,
                    "function_body": "函数体内容"
                },
                ...
            ]
        }
    """
    # 默认支持的文件扩展名
    if supported_extensions is None:
        supported_extensions = ['.cpp', '.c', '.h', '.hpp', '.cc', '.cxx']

    # 设置最大线程数
    if max_workers is None:
        max_workers = os.cpu_count() * 2

    # 结果字典
    result = {}

    # 收集所有需要分析的文件路径
    file_paths = []
    for root, _, files in os.walk(directory_path):
        for file in files:
            file_ext = Path(file).suffix.lower()
            if file_ext in supported_extensions:
                file_paths.append(os.path.join(root, file))

    logger.info(f"开始扫描 {len(file_paths)} 个文件，查找调用 '{target_function}' 的函数...")
    start_time = time.time()

    # 使用线程池并行处理文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有文件分析任务
        future_to_path = {
            executor.submit(analyze_file, file_path, target_function): file_path
            for file_path in file_paths
        }

        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_path):
            file_path = future_to_path[future]
            try:
                file_result = future.result()

                # 将文件结果合并到总结果中
                for func_name, calls in file_result.items():
                    if func_name not in result:
                        result[func_name] = []

                    for call in calls:
                        result[func_name].append({
                            "file": file_path,
                            "line": call["line"],
                            "function_body": call["function_body"]
                        })

            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")

    duration = time.time() - start_time
    logger.info(
        f"扫描完成！找到 {len(result)} 个调用了 '{target_function}' 的父函数{str(result.keys())}，耗时 {duration:.2f} 秒")

    return result


def process_file(args):
    """
    包装函数，用于多进程调用 analyze_file。
    返回结果以文件名为键。
    """
    file_path, func_name = args
    try:
        result = analyze_file(file_path, func_name)
        return {file_path: result}
    except Exception as e:
        return {file_path: []}


def analyze_dir(dir_path, func_name):
    """
    在整个目录中查找指定函数的调用信息（多进程实现）。
    返回以文件名为键的结果字典。
    """
    file_list = []
    start_time = time.time()
    # 遍历目录，收集所有 .c 和 .h 文件
    for root, _, files in os.walk(dir_path):
        for file in files:
            if file.endswith((".c", ".h")):
                file_path = os.path.join(root, file)
                file_list.append(file_path)

    # 使用多进程池并行处理文件
    results = []
    with Pool(processes=8) as pool:  # 限制并发进程数为 6
        try:
            results = pool.map(process_file, [(file, func_name) for file in file_list])
        except Exception as e:
            logger.error(f"Error processing files: {e}")

    # 整合结果为字典
    final_results = {}
    for result in results:
        if len(list(result.values())[0]) > 0:
            for result_item in result.values():
                for key, call in result_item.items():
                    final_results[key] = {
                        "file": list(result.keys())[0],
                        "line": call["line"],
                        "function_body": call["function_body"]
                    }
                    logger.info("外部调用函数:", final_results)

    duration = time.time() - start_time
    logger.info(
        f"扫描完成！找到 {list(final_results.keys())} 个调用了 '{func_name}' 的父函数{list(final_results.keys())}，耗时 {duration:.2f} 秒")

    return final_results


def check_last_part_upper(text):
    """
    检查字符串按下划线切分后，最后一个部分是否全是大写字母。

    参数:
        text (str): 输入的字符串。

    返回:
        bool: 如果最后一个部分全是大写字母，返回 True；否则返回 False。
    """
    # 按下划线切分字符串
    parts = text.split('_')
    # 获取最后一个部分
    last_part = parts[-1]
    # 判断最后一个部分是否全是大写
    return last_part.isupper()


def parse_external_functions(function_body, folder_path, variables):
    """
    解析函数中调用的外部函数，查找它们的定义并返回字典。
    参数：
        - function_body: 主函数体（字符串）。
        - folder_path: 文件夹路径。
        - variables: 全局和局部变量列表。
    返回：
        - 字典 {函数名: {"file_path": 文件路径, "function_body": 函数体}}。
    """
    # 提取主函数中调用的外部函数
    called_functions = extract_called_functions(function_body, variables)

    new_func_list = []
    old_func_list = []
    for called_func in called_functions:
        is_replaced = check_last_part_upper(called_func)
        if is_replaced:
            real_name = find_macro_definition(folder_path, called_func)
            if real_name:
                new_func_list.append(real_name[0])
                old_func_list.append(called_func)
    called_functions = [item for item in called_functions if item not in old_func_list]
    called_functions.extend(new_func_list)

    logger.info(f"调用的外部函数: {called_functions}")
    func_name_folder_pairs = [(func, folder_path) for func in called_functions]

    # 使用多进程池处理
    with Pool(processes=8) as pool:
        results = pool.map(process_function, func_name_folder_pairs)

    # 整理结果
    result = {func_name: details for func_name, details in results if details is not None}
    return result
