"""
@File    : tele_table_filler_api.py
<AUTHOR> <PERSON><PERSON>
@Date    : 2025-07-22
@Desc    : 通信表格填充API路由器，用于导入并填充通信式样书
"""

import logging
import os
from typing import Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field, field_validator

from sdw_agent.service import WorkflowStatus
from sdw_agent.service.tele_table_filler.models import InputModel, OutputModel  # 导入 models.py 中的 InputModel 和 OutputModel
from sdw_agent.service.tele_table_filler.tele_table_filler import TeleTableFillerWorkflow  # 导入工作流类

# 配置日志记录器
logger = logging.getLogger(__name__)

# 创建API路由器，设置前缀和标签
router = APIRouter(prefix="/api/sdw/tele_table_filler", tags=["4.4 システム","64. 通信チェック実施＆結果検証"])

class ResultResponse(BaseModel):
    """API响应模型"""
    code: int = Field(0, description="状态码，0表示成功，非0表示失败")
    msg: str = Field("", description="状态消息，成功时为空，失败时包含错误信息")
    data: Optional[OutputModel] = Field(None, description="结果文件信息，仅在成功时返回")


@router.post(
    "/upload_tele",
    summary="导入并填充通信式样书",
    description="根据输入的Excel文件路径和数据源目录，进行表格填充并生成结果文件",
    response_model=ResultResponse,
    responses={
        200: {"description": "成功返回填充结果文件路径"},
        400: {"description": "请求参数错误或文件验证失败"},
        404: {"description": "指定的文件不存在"},
        422: {"description": "请求数据格式错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def upload_tele_handler(request: InputModel) -> ResultResponse:
    """
    通信表格填充处理接口

    根据提供的Excel文件路径和RAG源目录，进行表格填充，生成对应的填充文件。

    Args:
        request: 包含Excel文件路径和RAG源目录的请求对象

    Returns:
        ResultResponse: 包含处理结果和生成文件路径的响应对象

    Raises:
        HTTPException: 当请求参数无效、文件不存在或处理过程中发生错误时抛出
    """
    try:
        # 记录请求信息
        logger.info(f"开始处理通信表格填充请求")
        logger.info(f"Excel文件路径: {request.file_path}")
        logger.info(f"RAG源目录: {request.source_dir}")

        # 验证输入文件信息
        if not request.file_path:
            logger.error("Excel文件路径为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Excel文件路径不能为空"
            )

        if not request.source_dir:
            logger.error("RAG源目录为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="RAG源目录不能为空"
            )

        # 标准化文件路径（将反斜杠替换为正斜杠）
        file_path = request.file_path.replace('\\', '/')
        source_dir = request.source_dir.replace('\\', '/')

        # 验证文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"Excel文件不存在: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Excel文件不存在: {file_path}"
            )

        if not os.path.exists(source_dir):
            logger.error(f"RAG源目录不存在: {source_dir}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"RAG源目录不存在: {source_dir}"
            )

        # 计算根目录下的 config_path（假设根目录是项目根，根据实际情况调整）
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))  # 向上三级到根目录
        config_path = os.path.join(root_dir, 'service','tele_table_filler', 'config.yaml')

        # 实例化工作流并执行
        logger.info(f"使用config路径: {config_path}")
        workflow = TeleTableFillerWorkflow(config_path=config_path)

        input_data = {
            "file_path": file_path,
            "source_dir": source_dir,
            "config": request.config  # 可选自定义配置
        }

        result = workflow.execute(input_data)

        # 根据工作流结果构建响应
        if result.status == WorkflowStatus.SUCCESS:
            output_data = OutputModel(
                filled_path=result.data.get("filled_path", ""),
                report_path=result.data.get("report_path", ""),
                filled_rows=result.data.get("filled_rows", 0),
                status="成功"
            )
            logger.info(f"通信表格填充处理成功，结果文件: {output_data.filled_path}")
            return ResultResponse(
                code=0,
                msg="通信填充成功",
                data=output_data
            )
        else:
            error_msg = result.message or result.error or "未知错误"
            logger.error(f"通信表格填充处理失败: {error_msg}")
            return ResultResponse(
                code=500,
                msg=f"通信填充失败: {error_msg}",
                data=None
            )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as ve:
        # 处理数据验证错误
        logger.error(f"数据验证错误: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"数据验证失败: {str(ve)}"
        )
    except Exception as e:
        # 处理其他未预期的错误
        logger.error(f"通信表格填充处理过程中发生未预期错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误，请稍后重试"
        )