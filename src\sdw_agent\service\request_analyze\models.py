"""
要件分析工作流数据模型

V字对应：
1.3 要件分析
2. 要求仕様読み合わせ（客先、部内、チーム内）

定义工作流中使用的输入输出数据结构和配置模型。
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator


class TaskInfo(BaseModel):
    """任务信息请求模型"""
    row_idx: int = Field(..., description="行索引")
    ar_no: str = Field(..., description="AR票号")
    ar_title: str = Field(..., description="AR标题")
    ar_link: str = Field(..., description="AR链接")
    epic_name: str = Field(..., description="Epic名称")
    req_change_content: str = Field(..., description="需求变更内容")
    p_no: str = Field(..., description="要件票号")
    guideline_key: str = Field('CSTM', description="Guideline键")

    @field_validator('req_change_content')
    @classmethod
    def validate_req_content(cls, v):
        if not v or not v.strip():
            raise ValueError("需求变更内容不能为空")
        if len(v) > 1000:  # 最大摘要长度
            raise ValueError(f"需求变更内容长度不能超过1000字符")
        return v.strip()

    @field_validator('ar_no')
    @classmethod
    def validate_ar_no(cls, v):
        if not v or not v.strip():
            raise ValueError("AR票号不能为空")
        return v.strip()


class SourceInfo(BaseModel):
    """源信息模型"""
    type: str = Field(..., description="源类型: local, svn, git")
    uri: str = Field(..., description="源URI")

    @field_validator('uri')
    @classmethod
    def validate_uri(cls, v):
        if not v or not v.strip():
            raise ValueError("URI不能为空")
        if len(v) > 260:  # 最大路径长度
            raise ValueError(f"URI长度不能超过260字符")
        return v.strip()


class WriteEpicRequest(BaseModel):
    """回写要件一览表请求模型"""
    keySource: SourceInfo = Field(..., description="源信息")
    change_summary: str = Field("", description="变更摘要")
    classify_name: str = Field("", description="分类名称")
    guideline_key: str = Field("", description="Guideline键")

    @field_validator('change_summary')
    @classmethod
    def validate_change_summary(cls, v):
        if not v or not v.strip():
            raise ValueError("变更摘要不能为空")
        if len(v) > 1000:  # 最大摘要长度
            raise ValueError(f"变更摘要长度不能超过1000字符")
        return v.strip()

    @field_validator('classify_name')
    @classmethod
    def validate_classify_name(cls, v):
        if not v or not v.strip():
            raise ValueError("分类名称不能为空")
        return v.strip()


class ClassifyScoreResult(BaseModel):
    """分类打分结果"""
    name: str = Field(..., description="分类名称")
    score: float = Field(..., description="分类得分，0-1")
    reason: str = Field(..., description="打分理由")

    @field_validator('score')
    @classmethod
    def validate_score(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("分数必须在0-1之间")
        return v


class ClassifyOutputData(BaseModel):
    """分类输出数据模型"""
    guideline_key: str = Field(..., description="使用的Guideline键")
    category: List[ClassifyScoreResult] = Field(..., description="分类结果列表")
    block_diagram_path: Optional[str] = Field(None, description="生成的架构图文件路径，失败时为None")


class OptimizeOutputData(BaseModel):
    """优化输出数据模型"""
    epic_file: str = Field(..., description="要件一览表路径")
    design_police_file: str = Field(..., description="设计评价方针路径")
    change_summary: str = Field(..., description="变更摘要")
    classify_name: str = Field(..., description="分类名称")
    guideline_key: str = Field(..., description="Guideline键")


class PolishedContent(BaseModel):
    """润色内容模型"""
    comprehend: str = Field(..., description="变更点理解")
    influence: str = Field(..., description="影响分析")
    design_police: str = Field(..., description="设计方针")
    failure_mode: str = Field(..., description="故障模式")
    concerns: str = Field(..., description="担心点")
    design_way: str = Field(..., description="设计方法")
    evaluation_way: str = Field(..., description="评价方法")


class EpicWriteData(BaseModel):
    """要件一览表写入数据模型"""
    epic_path: str = Field(..., description="要件一览表路径")
    guideline_key: str = Field(..., description='guideline标识')
    change_summary: str = Field(..., description="变更概要")
    need_notify: bool = Field(True, description='对应要否')
    reason: str = Field('', description='对应否的原因')
    comprehend: str = Field('-', description="变更点理解")
    influence: str = Field('-', description="影响分析")

    @field_validator('epic_path')
    @classmethod
    def validate_epic_path(cls, v):
        if not v or not v.strip():
            raise ValueError("要件一览表路径不能为空")
        return v.strip()


class DesignPolicyData(BaseModel):
    """设计评价方针数据模型"""
    change_summary: str = Field(..., description="变更概要")
    design_police: str = Field(..., description="设计方针")
    failure_mode: str = Field(..., description="故障模式")
    concerns: str = Field(..., description="担心点")
    design_way: str = Field(..., description="设计方法")
    evaluation_way: str = Field(..., description="评价方法")

    @field_validator('change_summary')
    @classmethod
    def validate_change_summary(cls, v):
        if not v or not v.strip():
            raise ValueError("变更概要不能为空")
        return v.strip()


class RequestAnalyzeConfigModel(BaseModel):
    """要件分析工作流配置模型"""
    name: str = Field(default="要件分析工作流", description="工作流名称")
    description: str = Field(default="基于guideline对变更需求进行分析和优化", description="工作流描述")
    version: str = Field(default="1.0.0", description="版本号")
    author: str = Field(default="SDW Agent", description="作者")
    
    # 模块特定配置
    default_guideline_key: str = Field(default="CSTM", description="默认Guideline键")
    min_score_threshold: float = Field(default=0.5, description="最低分数阈值")
    max_category_results: int = Field(default=100, description="最大分类结果数")
    timeout: int = Field(default=300, description="超时时间（秒）")

    @field_validator('min_score_threshold')
    @classmethod
    def validate_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("分数阈值必须在0-1之间")
        return v

    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError("超时时间必须大于0")
        return v
