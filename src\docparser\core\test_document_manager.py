# -*- coding: utf-8 -*-
"""
@File    : test_document_manager.py
<AUTHOR> zhenp
@Date    : 2025-06-03 10:32
@Desc    : Description of the file
"""
import os

# AI generation start
import pytest
from docparser.core.document_manager import DocumentManager
from docparser.models.document import DocumentObject
from docparser.utils.error_handler import DocParserError

def test_parse_document_file_not_found():
    dm = DocumentManager()
    invalid_file_path = "nonexistent_file.docx"

    # Assert that DocParserError is raised for invalid file paths
    with pytest.raises(DocParserError, match=f"File not found: {invalid_file_path}"):
        dm.parse_document(invalid_file_path)

def test_parse_document_unsupported_file_type():
    dm = DocumentManager()
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../testdata/test.unknownformat")
    file_path = os.path.normpath(file_path)

    # Assert that <PERSON>ParserError is raised for unsupported file types
    with pytest.raises(DocParserError, match="No parser registered for extension"):
        dm.parse_document(file_path)

def test_parse_document_success():
    dm = DocumentManager()
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../testdata/1220演示数据变更前.xlsx")
    file_path = os.path.normpath(file_path)

    res = dm.parse_document_object(file_path)

    assert len(res.document) == 7

def test_parse_document_with_plugin():
    dm = DocumentManager()

    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../testdata/1220演示数据变更前.xlsx")
    file_path = os.path.normpath(file_path)

    plugin_dir = os.path.join(current_dir,f"../plugins")
    plugin_dir = os.path.normpath(plugin_dir)
    dm.plugin_manager.discover_plugins(plugin_dir)
    res = dm.parse_document_object(file_path)

    assert len(res.document) == 7

# AI generation end