import requests
import json


class QwenVLClient:
    def __init__(self, server_url="http://192.168.53.118:5000/qwen2.5-vl-inference"):
        self.server_url = server_url
        # 基础prompt定义
        self.en_prompt_describe = """{} Please describe the image content in detail according to the following dimensions:​
Basic Information: Image dimensions (width * height), resolution, format, overall color tone, and color saturation.​
Main Content: Number of main subjects, their positions, shapes, sizes, and colors. If the subjects are people, describe their actions; if they are objects, explain their uses.​
Background Environment: Background color, patterns, scene, and the relationship between the background and the main subjects.​
Detailed Elements: Positions, content, fonts, sizes, colors, and functions of details such as text and symbols.​
Layout and Composition: Composition method, distribution of various elements, and the resulting visual effects."""

    def _construct_messages(self, base64_image):
        """构建请求所需的messages结构"""
        return [
            {
                "role": "user",
                "content": [
                    # {"type": "image", "image": base64_image},
                    {"type": "text", "text": self.en_prompt_describe.format(base64_image)},
                ],
            }
        ]

    def send_inference_request(self, base64_image):
        """
        发送推理请求到Qwen2.5-VL模型

        参数:
            base64_image (str): Base64编码的图片数据

        返回:
            str: 模型返回的结果或错误信息
        """
        try:
            # 构建请求消息
            messages = self._construct_messages(base64_image)

            # 准备请求数据
            payload = {
                "messages": messages
            }

            # 发送POST请求到服务端
            response = requests.post(self.server_url, json=payload)

            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    return result["result"]
                else:
                    return f"Error: {result.get('error', 'Unknown error')}"
            else:
                return f"Request failed with status code {response.status_code}"

        except Exception as e:
            return f"Exception occurred: {str(e)}"


# 使用示例
if __name__ == "__main__":
    # 假设这是一个Base64编码的图片
    sample_base64_image = "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwQAADsEBuJFr7QAAABh0RVh0U29mdHdhcmUAcGFpbnQubmV0IDQuMC41ZYUyZQAAAG1JREFUOE+lj9ENwCAIRB2IFdyhTQp52d8Y8wQa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+3Qa9O72q+8C4/g/wG9L17nq7YdFQAAAABJRU5ErkJggg=="

    # 创建客户端实例
    client = QwenVLClient()

    # 发送请求
    result = client.send_inference_request(sample_base64_image)
    print("Response from server:")
    print(result)