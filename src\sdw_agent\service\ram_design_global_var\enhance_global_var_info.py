"""
@File    : enhance_global_var_info.py
@Time    : 2025/7/15 19:20
<AUTHOR> qiliang.zhou
@Contact : <EMAIL>
@Desc    : 1. 解析684D_Component_List.xlsx，
           2. 得到全局变量的大項目（组件）, コンポーネント（成分）信息
           3. 将全局变量写入模板文件
"""
import os
import re
import pathlib
import warnings

import pandas as pd
from typing import Dict, Any
from langchain_core.messages import AIMessage
from openpyxl import load_workbook
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.ram_design_global_var.search_resolve_global_var import find_constant_definitions
from sdw_agent.util.excel_util import check_file_exists


# 类型映射表，支持常见嵌入式、C99、C/C++标准类型和常用别名
# 格式: 类型名: (位数, 是否有符号)
DATA_TYPE_MAP = {
    'U1':  (8, False),
    'U2':  (16, False),
    'U4':  (32, False),
    'U8':  (64, False),
    'S1':  (8, True),
    'S2':  (16, True),
    'S4':  (32, True),
    'S8':  (64, True),
    'uint8_t': (8, False),
    'uint16_t': (16, False),
    'uint32_t': (32, False),
    'uint64_t': (64, False),
    'int8_t': (8, True),
    'int16_t': (16, True),
    'int32_t': (32, True),
    'int64_t': (64, True),
    'BYTE': (8, False),
    'WORD': (16, False),
    'DWORD': (32, False),
    'QWORD': (64, False),
    # C/C++标准类型
    'unsigned char': (8, False),
    'unsigned short': (16, False),
    'unsigned int': (32, False),
    'unsigned long': (32, False),  # 64位平台可能是64位
    'unsigned long long': (64, False),
    'signed char': (8, True),
    'short': (16, True),
    'int': (32, True),
    'long': (32, True),  # 64位平台可能是64位
    'long long': (64, True),
}


class ComponentObj:
    def __init__(self, **row):
        self.large_prj = row['大項目']
        self.mid_prj = row['中項目']
        self.component_id = row['コンポーネントID']
        self.component_name = row['コンポーネント']
        self.file_path = row['文件路径']
        self.func_desc = row['機能']

class RamGlobalVarFormat(BaseModel):
    """
    LLM响应格式定义
    """
    min: str = Field(description="全局变量的最小值, 16进制表示")
    max: str = Field(description="全局变量的最大值, 16进制表示")
    desc: str = Field(description="全局变量的描述, 50字以内")

class RamStructVarFormat(BaseModel):
    """
    LLM响应格式定义
    """
    min: list[str] = Field(description="全局变量中各个子属性的最小值, 16进制表示")
    max: list[str] = Field(description="全局变量中各个子属性的最大值, 16进制表示")
    attr_name: list[str] = Field(description="全局变量中各个子属性的名称")
    attr_type: list[str] = Field(description="全局变量中各个子属性的类型")
    attr_bit_length: list[str] = Field(description="全局变量中各个子属性对应类型的字节长度")
    desc: str = Field(description="全局变量的描述, 50字以内")


def resolve_component_list(file_path:str)->list[ComponentObj]:
    """
    解析684D_Component_List.xlsx文件，并将每行记录创建成ComponentObj对象
    Args:
        file_path: 684D_Component_List.xlsx 文件路径

    Returns:

    """
    # 检查文件是否存在
    check_file_exists(file_path)
    # 忽略读取excel时数据验证警告
    warnings.filterwarnings('ignore', category=UserWarning,
                            message='Data Validation extension is not supported')

    df = pd.read_excel( file_path, sheet_name='SW-C List_R-Car', header=2, engine='openpyxl',engine_kwargs={'data_only': True}) # noqa

    cols = df.columns.tolist()
    
    # 假设 ComponentObj 构造函数支持关键字参数
    component_list = []
    for idx, row in df.iterrows():
        row_dict = {col: "" if pd.isna(row[col]) else row[col] for col in cols}
        component = ComponentObj(**row_dict)
        component_list.append(component)
    return component_list

def enhance_global_var_info(repo_path, file_path, changed_vars, code_diff):

    def is_code_file_in_component_dict(code_file_path, component_dict):
        for key in component_dict:
            if key in code_file_path:
                return True, key
        return False, None

    # 分析全局变量的值域范围
    resolve_const_in_diff(repo_path, changed_vars, code_diff)

    # 调用大模型判断变更全局变量的类型，值域范围
    llm_analyze_global_var(changed_vars)

    component_list = resolve_component_list(file_path)
    # 创建dict 增加匹配速度
    component_dict = {component.file_path.replace("\\", "/"): component for component in component_list if component.file_path}
    for code_file_path, changed_var in changed_vars.items():
        matched, matched_key = is_code_file_in_component_dict(code_file_path, component_dict)
        if matched:
            for var in changed_var:
                var['domain'] = component_dict[matched_key].large_prj
                var['component'] = component_dict[matched_key].component_name

    return changed_vars


def resolve_const_in_diff(repo_path, changed_vars: Dict[str, Any], code_diff)->Dict[str, Any]:
    """
    解析并将将变更代码中的宏定义替换成宏值
    Args:
        repo_path:
        changed_vars:
        code_diff:

    Returns:

    """
    # 从full_code 中提取出与变更的全局变量相关的代码
    changed_vars = extract_const_in_full_code(repo_path, changed_vars, code_diff)

    for file_path, changed_var in changed_vars.items():
        for var in changed_var:
            resolved_const = find_constant_definitions(str(pathlib.Path(repo_path)),
                                                       str(pathlib.Path(file_path)),
                                                       var['applied_consts'])
            if resolved_const:
                var['resolved_applied_const'] = resolved_const

    # 将变更代码中的宏定义替换成宏值
    replace_const_with_value(changed_vars)
    return changed_vars

def extract_const_in_full_code(repo_path, changed_vars, code_diff)->list[Dict[str, Any]]:
    """
    从变更代码中抽取出使用的全局变量的名称
    Args:
        changed_vars: 变更的全局变量，里面包含变更全局变量所在的变更行代码
    Returns: 全局变量列表
    """
    for file_path, changed_var in changed_vars.items():
        full_code = []
        for item in code_diff:
            if item['file_path'] == file_path:
                full_code = item['full_code']
                break
        for var_info in changed_var:
            matched_lines = []
            i = 0
            n = len(full_code)
            while i < n:
                line = full_code[i]
                if var_info['name'] in line:
                    # 检查是否为多行初始化
                    if re.match(r'^\s*static\b.*\b' + re.escape(var_info['name']) + r'\b.*=\s*\{', line):
                        block = [line]
                        i += 1
                        # 收集到 '};' 结束
                        while i < n:
                            block.append(full_code[i])
                            if re.match(r'^\s*};\s*$', full_code[i]):
                                break
                            i += 1
                        matched_lines.extend(block)
                    else:
                        matched_lines.append(line)
                i += 1
            var_info['matched_lines'] = matched_lines
            var_info['applied_consts'] = []

            found_consts = re.findall(r'\b[A-Z][A-Z0-9]*(?:_[A-Z][A-Z0-9]*)*\b', ' '.join(matched_lines))
            var_info['applied_consts'] += [const for const in found_consts if const not in DATA_TYPE_MAP and const!=var_info['type']]
            if var_info['type'] not in DATA_TYPE_MAP:
                # 寻找struct or union 类型的定义
                data_type, resolved_const =identify_struct_union(repo_path, file_path, list([var_info['type']]))
                if data_type:
                    var_info['struct_or_union'] = data_type
                if resolved_const:
                    var_info['struct_or_union_def'] = resolved_const

    return changed_vars


def identify_struct_union(repo_path, file_path, struct_union_name):
    """
    得到struct or union的定义
    Args:
        repo_path:
        file_path:
        struct_union_name:

    Returns:

    """
    if struct_union_name is None or not struct_union_name:
        return None, None

    resolved_const = find_struct_union_definitions(str(pathlib.Path(repo_path)),
                                               str(pathlib.Path(file_path)),
                                               struct_union_name)
    if resolved_const:
        # 根据定义判断类别 是 struct 还是 union
        data_type = get_type_struct_or_union(resolved_const, struct_union_name[0])
        return data_type, resolved_const

    return None, None

def replace_const_with_value(changed_vars:Dict[str, Any]):
    '''
    将涉及全局变量使用的代码中的宏定义替换成宏值
    '''
    for file_path, changed_var_list in changed_vars.items():
        for var in changed_var_list:
            if 'matched_lines' in var and 'resolved_applied_const' in var:
                resolved = var['resolved_applied_const']
                new_lines = []
                for line in var['matched_lines']:
                    # 替换所有宏名为宏值
                    for macro, macro_info in resolved.items():
                        if 'val' in macro_info:
                            # 只替换完整单词，防止误替换
                            line = re.sub(rf'\b{re.escape(macro)}\b', macro_info['val'], line)
                    new_lines.append(line)
                var['matched_lines'] = new_lines
    return changed_vars


def get_type_min_max(type_str: str):
    """
    根据类型字符串计算全局变量的16进制min和max
    支持类型: U1/U2/U4/S1/S2/S4/U8/S8 及其数组形式
    :param type_str: 变量类型字符串，如 'U1', 'U1[10]', 'S4', 'U2[8]' 等
    :return: (min_hex, max_hex) 16进制字符串
    """
    # 去除数组部分
    base_type = type_str.strip().split('[')[0].strip()
    if base_type not in DATA_TYPE_MAP:
        return (None, None)
    bits, signed = DATA_TYPE_MAP[base_type]
    if not signed:
        min_val = 0
        max_val = 2**bits - 1
    else:
        min_val = -(2**(bits-1))
        max_val = 2**(bits-1) - 1
    # 转为16进制字符串
    min_hex = hex(min_val & (2**bits-1)) if not signed else hex(min_val)
    max_hex = hex(max_val)
    return (min_hex, max_hex)


def llm_analyze_global_var(changed_vars):
    """
    调用大模型分析全局变量的最小值，最大值，用途说明
    """
    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt.ram_design.ram_global_var_prompts),
        ],
        template_format="mustache",
    )

    template_struct = ChatPromptTemplate(
        [
            ("user", ENV.prompt.ram_design.ram_struct_prompts),
        ],
        template_format="mustache",
    )
    for file_path, changed_var in changed_vars.items():
        for var in changed_var:
            global_var_name = var.get("name","")
            global_var_type = var.get("type","")
            global_var_comment = var.get("comment","")
            code_lines = '\n'.join(var.get("matched_full_lines", []))

            if not global_var_name or not global_var_type:
                continue

            logger.info(f"使用LLM分析全局变量，{global_var_type} {global_var_name} /*{global_var_comment}*/")

            if var.get("struct_or_union"):
                global_var_def = '\n'.join(var.get('struct_or_union_def'))
                invoke_data = {
                    "global_var_name": global_var_name,
                    "global_var_type": global_var_type,
                    "global_var_comment": global_var_comment,
                    "global_var_def": global_var_def,
                    "code_lines": code_lines,
                    "lang": 'zh-CN'
                }
                resp: AIMessage = get_ai_message_with_structured_output(
                    template_struct,
                    invoke_data,
                    RamStructVarFormat,
                    llm_model=None
                )
                var['min'] = resp.min
                var['max'] = resp.max
                var['attr_name'] = resp.attr_name
                var['attr_type'] = resp.attr_type
                var['attr_bit_length'] = resp.attr_bit_length
                var['comment'] = resp.desc
                logger.info(f"attr_name:{resp.attr_name},attr_type:{resp.attr_type},attr_bit_length:{resp.attr_bit_length}")
                logger.info(f"min:{resp.min}, max:{resp.max}, dec:{resp.desc}")
                continue

            invoke_data = {
                "global_var_name": global_var_name,
                "global_var_type": global_var_type,
                "global_var_comment": global_var_comment,
                "code_lines": code_lines,
                "lang": 'zh-CN'
            }

            resp: AIMessage = get_ai_message_with_structured_output(
                template,
                invoke_data,
                RamGlobalVarFormat,
                llm_model=None
            )

            var['min'] = resp.min
            var['max'] = resp.max
            var['attr_name'] = []
            var['attr_type'] = []
            var['attr_bit_length'] = []
            var['comment'] = resp.desc

            logger.info(f"min:{resp.min}, max:{resp.max}, dec:{resp.desc}")

    return changed_vars

def find_struct_union_definitions(repo_path: str, file_path: str, type_names: list) -> dict:
    """
    递归查找C文件及其include链中的指定 struct/union 类型的定义代码片段。
    只返回目标类型的定义。
    """
    found_defs = []
    searched_files = set()

    def _search_file(abs_path, remaining_types):
        if not os.path.isfile(abs_path) or abs_path in searched_files or not remaining_types:
            return
        searched_files.add(abs_path)
        try:
            with open(abs_path, encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception:
            return
        n = len(lines)
        for type_name in list(remaining_types):
            i = 0
            while i < n:
                line = lines[i]
                # typedef struct/union { ... } TypeName;
                typedef_match = re.match(r'^\s*typedef\s+(struct|union)\s*\{', line)
                if typedef_match:
                    block = [line.rstrip('\n')]
                    brace_count = line.count('{') - line.count('}')
                    i += 1
                    while i < n and brace_count > 0:
                        block.append(lines[i].rstrip('\n'))
                        brace_count += lines[i].count('{') - lines[i].count('}')
                        i += 1
                    # 检查 block 最后一行是否以 }TypeName; 结尾
                    if block and re.search(rf'\}}\s*{re.escape(type_name)}\s*;', block[-1]):
                        # found_defs[type_name] = {'def': block}
                        found_defs.extend(block)
                        remaining_types.remove(type_name)
                        break
                    # 兼容 } 单独一行，下一行是 TypeName;
                    elif i < n and re.match(rf'^\s*{re.escape(type_name)}\s*;\s*$', lines[i].strip()):
                        block.append(lines[i].rstrip('\n'))
                        # found_defs[type_name] = {'def': block}
                        found_defs.extend(block)
                        remaining_types.remove(type_name)
                        break
                # struct/union TypeName { ... };
                normal_match = re.match(rf'^\s*(struct|union)\s+{re.escape(type_name)}\s*\{{', line)
                if normal_match:
                    block = [line.rstrip('\n')]
                    brace_count = line.count('{') - line.count('}')
                    i += 1
                    while i < n and brace_count > 0:
                        block.append(lines[i].rstrip('\n'))
                        brace_count += lines[i].count('{') - lines[i].count('}')
                        i += 1
                    # 检查结尾
                    if i < n and re.match(r'^\s*;\s*$', lines[i].strip()):
                        block.append(lines[i].rstrip('\n'))
                        # found_defs[type_name] = {'def': block}
                        found_defs.extend(block)
                        remaining_types.remove(type_name)
                        break
                i += 1
        # 递归 include
        if remaining_types:
            content = ''.join(lines)
            include_pattern = re.compile(r'^\s*#include\s+[<"]([^">]+)[">]', re.MULTILINE)
            for m in include_pattern.finditer(content):
                inc_file = m.group(1)
                if inc_file.endswith('.h'):
                    inc_path = os.path.join(repo_path, inc_file)
                    if os.path.isfile(inc_path):
                        _search_file(inc_path, remaining_types)
                    else:
                        for root, dirs, files in os.walk(repo_path):
                            if inc_file in files:
                                _search_file(os.path.join(root, inc_file), remaining_types)
                                break

    abs_file_path = os.path.join(repo_path, file_path)
    _search_file(abs_file_path, set(type_names))
    return found_defs

def get_type_struct_or_union(resolved_const, type_name):
    """
    判断类型是 struct 还是 union
    :param resolved_const: find_struct_union_definitions 的返回结果
    :param type_name: 类型名
    :return: 'struct' 或 'union' 或 None
    """
    if not resolved_const:
        return None
    def_lines = resolved_const
    if not def_lines:
        return None
    first_line = def_lines[0].strip()
    if first_line.startswith('typedef struct') or first_line.startswith('struct'):
        return 'struct'
    if first_line.startswith('typedef union') or first_line.startswith('union'):
        return 'union'
    return None

if __name__ == '__main__':
    file_path = r"D:\tdd_input\684D_Component_List.xlsx"
    resolve_component_list(file_path)