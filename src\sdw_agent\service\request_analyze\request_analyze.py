"""
要件分析工作流

V字对应：
1.3 要件分析
2. 要求仕様読み合わせ（客先、部内、チーム内）

基于guideline对变更需求进行分类打分和内容优化的工作流。

主要功能：
1. 基于guideline对变更需求进行分类打分
2. 基于guideline分类对应的内容结合变更需求的描述进行润色
3. 将优化后的内容写入要件一览表
4. 生成设计评价方针文件
"""

import os
import shutil
from datetime import datetime
from typing import Optional, Dict, Any, List

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.service.guideline_manager import GuidelineManager
from sdw_agent.service.template_manager import template_manager
from sdw_agent.util.import_doc_util import ImportDocUtil
from sdw_agent.util.drawio_util import highlight_leaf_modules
from sdw_agent.config.env import ENV
from sdw_agent.service.request_analyze.models import (
    TaskInfo, WriteEpicRequest, ClassifyOutputData, OptimizeOutputData,
    ClassifyScoreResult, PolishedContent, EpicWriteData, DesignPolicyData
)
# 移除常量导入，直接使用self.config
from sdw_agent.service.request_analyze.utils import EpicWriter, DesignPolicyWriter, RequirementAnalyzer


@register_workflow("request_analyze")
class RequestAnalyzeWorkflow(BaseWorkflow):
    """
    要件分析工作流
    
    负责处理变更需求的分类打分和内容优化，包含以下能力：
    - 基于guideline对变更需求进行分类打分
    - 基于guideline分类对应的内容进行润色优化
    - 将结果写入要件一览表
    - 生成设计评价方针文件
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化要件分析工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        self.guideline_manager = GuidelineManager()
        self.import_doc_util = ImportDocUtil()
        self.epic_writer = EpicWriter()
        self.design_policy_writer = DesignPolicyWriter()
        self.requirement_analyzer = RequirementAnalyzer()

    def validate_input(self, *args, **kwargs) -> bool:
        """
        验证输入参数

        Args:
            根据不同的执行方法验证不同的参数

        Returns:
            bool: 验证是否通过
        """
        # 基础验证在Pydantic模型中已经完成
        return True

    def execute(self, operation: str, data: Any) -> WorkflowResult:
        """
        执行工作流核心逻辑

        Args:
            operation: 操作类型，'classify' 或 'optimize'
            data: 输入数据，TaskInfo 或 WriteEpicRequest

        Returns:
            WorkflowResult: 工作流执行结果
        """
        if operation == "classify":
            return self.execute_classify(data)
        elif operation == "optimize":
            return self.execute_optimize(data)
        else:
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"不支持的操作类型: {operation}",
                error=f"不支持的操作类型: {operation}"
            )

    def execute_classify(self, task_info: TaskInfo) -> WorkflowResult:
        """
        执行分类打分工作流
        
        Args:
            task_info: 任务信息
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.logger.info(f"开始执行分类打分，AR票号: {task_info.ar_no}")

            # 处理要件票号
            p_no = self.requirement_analyzer.process_p_no(task_info.p_no)

            # 生成变更摘要
            req_info = self.import_doc_util.make_jira_change_summary(
                task_info.ar_no,
                task_info.req_change_content,
                p_no
            )

            # 确定guideline_key
            guideline_key = task_info.guideline_key
            if not guideline_key:
                # 智能选择guideline
                result = self.guideline_manager.match_rule_by_change_request(req_info, 1)
                if not result:
                    error_msg = self.config.get('message_constants', {}).get('error_no_matching_guideline',
                                                                             '当前没有匹配的guideline')
                    raise Exception(error_msg)
                guideline_key = result[0].rule_key

            # 进行分类打分
            max_results = self.config.get('module_specific', {}).get('max_category_results', 100)
            result, categories = self.guideline_manager.match_category_by_change_request(
                guideline_key,
                req_info,
                max_results
            )

            # 处理分类结果
            category_results = sorted([
                {"name": i.category_name, "score": i.confidence, "reason": i.reasoning}
                for i in result
            ], key=lambda x: x['score'], reverse=True)

            # 添加"其它"分类和生成block图
            block_diagram_path = None  # 初始化block图路径
            min_threshold = self.config.get('module_specific', {}).get('min_score_threshold', 0.5)
            if category_results and category_results[0]['score'] < min_threshold:
                category_results.insert(0, self.requirement_analyzer.create_other_category_result(True))
                category_results.append(self.requirement_analyzer.create_other_category_result(False))
            else:
                category_results.append(self.requirement_analyzer.create_other_category_result(False))
                # 没有匹配到其他, 生成block图
                max_similar_category_name = category_results[0]['name']
                for item in categories:
                    if item.category_name == max_similar_category_name:
                        domain_info = categories[0].additional_fields.get('关联组件')
                        if domain_info is not None:
                            target_path_list = domain_info.strip().split("\n")
                            # 根据提供的路径，生成block图
                            block_diagram_path = self._generate_block_diagram(target_path_list, task_info.ar_no)
                            if block_diagram_path:
                                self.logger.info(f"block图生成成功: {block_diagram_path}")
                            break

            output_data = ClassifyOutputData(
                guideline_key=guideline_key,
                category=category_results,
                block_diagram_path=block_diagram_path
            )

            self.logger.info(f"成功完成分类打分，AR票号: {task_info.ar_no}")

            success_msg = self.config.get('message_constants', {}).get('success_classify', '成功对AR票进行分类打分')
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message=success_msg,
                data=output_data.model_dump()
            )

        except Exception as e:
            self.logger.error(f"分类打分失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"分类打分失败: {str(e)}",
                error=str(e)
            )

    def execute_optimize(self, request: WriteEpicRequest) -> WorkflowResult:
        """
        执行内容优化工作流
        
        Args:
            request: 优化请求
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.logger.info(f"开始执行内容优化，分类: {request.classify_name}")

            epic_path = request.keySource.uri
            change_summary = request.change_summary
            classify_name = request.classify_name
            guideline_key = request.guideline_key

            # 判断是否需要通知
            other_category_name = self.config.get('workflow_constants', {}).get('other_category_name', '其它')
            need_notify = classify_name != other_category_name
            design_police_file = ''

            if not need_notify:
                # 不需要对应的情况
                not_applicable_reason = self.config.get('workflow_constants', {}).get('not_applicable_reason',
                                                                                      '当前ブランド内グレード不适用')
                default_dash = self.config.get('workflow_constants', {}).get('default_dash_value', '-')
                epic_data = EpicWriteData(
                    epic_path=epic_path,
                    guideline_key=guideline_key,
                    change_summary=change_summary,
                    need_notify=False,
                    reason=not_applicable_reason,
                    comprehend=default_dash,
                    influence=default_dash
                )
            else:
                # 需要对应的情况，进行内容润色
                result = self.guideline_manager.polish_category_content(
                    guideline_key,
                    classify_name,
                    change_summary
                )
                result_map = {i.field_name: i.polished_content for i in result}

                polished_content = PolishedContent(
                    comprehend=result_map['变更点理解'],
                    influence=result_map['影响分析'],
                    design_police=result_map['设计方针'],
                    failure_mode=result_map['故障模式'],
                    concerns=result_map['担心点'],
                    design_way=result_map['设计方法'],
                    evaluation_way=result_map['评价方法']
                )

                # 生成设计评价方针
                design_policy_data = DesignPolicyData(
                    change_summary=change_summary,
                    design_police=polished_content.design_police,
                    failure_mode=polished_content.failure_mode,
                    concerns=polished_content.concerns,
                    design_way=polished_content.design_way,
                    evaluation_way=polished_content.evaluation_way
                )
                design_police_file = self.design_policy_writer.write_design_policy(design_policy_data)

                default_dash = self.config.get('workflow_constants', {}).get('default_dash_value', '-')
                epic_data = EpicWriteData(
                    epic_path=epic_path,
                    guideline_key=guideline_key,
                    change_summary=change_summary,
                    need_notify=True,
                    reason=default_dash,
                    comprehend=polished_content.comprehend,
                    influence=polished_content.influence
                )

            # 写入要件一览表
            self.epic_writer.write_epic(epic_data)

            output_data = OptimizeOutputData(
                epic_file=epic_path,
                design_police_file=design_police_file,
                change_summary=change_summary,
                classify_name=classify_name,
                guideline_key=guideline_key
            )

            self.logger.info(f"成功完成内容优化，分类: {classify_name}")

            success_msg = self.config.get('message_constants', {}).get('success_optimize', '成功生成优化后的分析结果')
            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message=success_msg,
                data=output_data.model_dump()
            )

        except Exception as e:
            self.logger.error(f"内容优化失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"内容优化失败: {str(e)}",
                error=str(e)
            )

    def _generate_block_diagram(self, target_path_list: List[str], ar_no: str) -> Optional[str]:
        """
        生成block图，高亮指定路径的叶子模块

        Args:
            target_path_list: 要高亮的路径列表
            ar_no: AR票号，用于生成文件名

        Returns:
            Optional[str]: 生成的block图文件路径，失败时返回None
        """
        try:
            self.logger.info(f"开始生成block图，AR票号: {ar_no}")
            self.logger.info(f"目标路径数量: {len(target_path_list)}")

            # 过滤空路径
            valid_paths = [path.strip() for path in target_path_list if path.strip()]
            if not valid_paths:
                self.logger.warning("没有有效的路径，跳过block图生成")
                return None

            # 1. 使用模板管理器获取block图模板文件路径
            template_path = template_manager.get_template_path("block_diagram_file")

            if not template_path:
                raise FileNotFoundError("无法获取block图模板文件")

            # 2. 创建输出目录
            output_base_dir = os.path.join(ENV.config.output_data_path, "request_analyze_block").replace("\\", "/")
            os.makedirs(output_base_dir, exist_ok=True)

            # 3. 生成带时间戳的输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_name = os.path.splitext(os.path.basename(template_path))[0]
            output_filename = f"{template_name}_{ar_no}_{timestamp}.drawio"
            output_file_path = os.path.join(output_base_dir, output_filename).replace("\\", "/")

            # 4. 复制模板文件到输出目录
            shutil.copy2(template_path, output_file_path)
            self.logger.info(f"模板文件已复制到: {output_file_path}")

            # 5. 调用高亮叶子模块工具
            result = highlight_leaf_modules(
                target_paths=valid_paths,
                drawio_file_path=output_file_path,  # 使用复制后的文件
                output_file_path=output_file_path,  # 直接覆盖原文件
                highlight_color="red",
                verbose=True,
                use_template_manager=False  # 不使用模板管理器，直接使用指定文件
            )

            if result["success"]:
                self.logger.info(f"block图高亮完成: {result['output_file']}")
                self.logger.info(f"高亮模块数: {result['highlighted_count']}, 失败数: {result['failed_count']}")
                return result["output_file"]
            else:
                self.logger.error(f"block图高亮失败: {result['message']}")
                # 高亮失败时删除已复制的文件
                if os.path.exists(output_file_path):
                    try:
                        os.remove(output_file_path)
                        self.logger.info(f"已删除失败的文件: {output_file_path}")
                    except Exception as delete_error:
                        self.logger.warning(f"删除失败文件时出错: {str(delete_error)}")
                return None

        except Exception as e:
            self.logger.error(f"生成block图时发生异常: {str(e)}")
            # 异常时删除已复制的文件（如果存在）
            try:
                if 'output_file_path' in locals() and os.path.exists(output_file_path):
                    os.remove(output_file_path)
                    self.logger.info(f"已删除异常时的文件: {output_file_path}")
            except Exception as delete_error:
                self.logger.warning(f"删除异常文件时出错: {str(delete_error)}")
            return None
