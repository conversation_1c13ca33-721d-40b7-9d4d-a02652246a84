#!/usr/bin/env python3
"""
测试使用 draw.io API 导出图像
最简单的纯 Python 方法，无需安装额外软件
"""

import os
import urllib.parse
import requests
from pathlib import Path
from loguru import logger

from sdw_agent.service.template_manager import template_manager


def test_drawio_api_export():
    """测试 draw.io API 导出功能"""
    print("🎯 测试 Draw.io API 导出")
    print("=" * 40)
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用文件: {drawio_file}")
    
    # 2. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        print(f"📄 文件大小: {len(xml_content)} 字符")
        print(f"📄 文件开头: {xml_content[:100]}...")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 3. 准备 API 请求
    export_url = "https://app.diagrams.net/export"
    
    params = {
        'format': 'png',
        'scale': '2',
        'border': '10',
        'bg': 'white',
        'from': 'data'
    }
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # URL 编码 XML 内容
    encoded_xml = urllib.parse.quote(xml_content)
    data = f"xml={encoded_xml}"
    
    print(f"🌐 API URL: {export_url}")
    print(f"📊 参数: {params}")
    print(f"📝 数据大小: {len(data)} 字符")
    
    # 4. 发送请求
    try:
        print("🚀 正在发送 API 请求...")
        
        response = requests.post(
            export_url,
            params=params,
            data=data,
            headers=headers,
            timeout=30
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📡 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 5. 保存图像
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            output_png = output_dir / "api_export_test.png"
            
            with open(output_png, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ API 导出成功!")
            print(f"📁 输出文件: {output_png}")
            print(f"📊 文件大小: {len(response.content)} 字节")
            
            # 6. 验证文件
            if output_png.exists() and output_png.stat().st_size > 0:
                print("✅ 文件验证通过")
                
                # 7. 尝试插入到 Excel
                try:
                    from sdw_agent.util.excel.core import ExcelUtil, CellStyle
                    from openpyxl.drawing.image import Image as OpenpyxlImage
                    
                    excel_file = output_dir / "api_export_test.xlsx"
                    
                    with ExcelUtil(str(excel_file), auto_create=True) as excel:
                        sheet_name = "API导出测试"
                        
                        if sheet_name not in excel.get_sheet_names():
                            excel.create_sheet(sheet_name)
                        
                        # 添加标题
                        excel.write_cell(sheet_name, 1, 1, "Draw.io API 导出测试")
                        title_style = CellStyle(
                            font_size=16,
                            font_bold=True,
                            alignment_horizontal="center"
                        )
                        excel.set_cell_style(sheet_name, 1, 1, title_style)
                        
                        # 插入图像
                        if hasattr(excel, '_get_worksheet'):
                            ws = excel._get_worksheet(sheet_name)
                            if ws:
                                img = OpenpyxlImage(str(output_png))
                                
                                # 调整大小
                                max_width = 1200
                                if img.width > max_width:
                                    scale_factor = max_width / img.width
                                    img.width = max_width
                                    img.height = int(img.height * scale_factor)
                                
                                # 设置位置
                                target_cell = ws.cell(row=3, column=1)
                                img.anchor = target_cell.coordinate
                                ws.add_image(img)
                                
                                print(f"✅ 图像已插入 Excel: {excel_file}")
                        
                        excel.save()
                
                except Exception as e:
                    print(f"⚠️  Excel 插入失败: {e}")
                
            else:
                print("❌ 文件验证失败")
                
        else:
            print(f"❌ API 请求失败")
            print(f"📄 响应内容: {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_simple_xml():
    """测试简单的 XML 内容"""
    print("\n🧪 测试简单 XML 内容")
    print("-" * 30)
    
    # 创建一个简单的 Draw.io XML
    simple_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="22.1.16" etag="test">
  <diagram name="Page-1" id="test">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Hello World" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="280" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>'''
    
    # 测试 API
    export_url = "https://app.diagrams.net/export"
    
    params = {
        'format': 'png',
        'scale': '2',
        'border': '10',
        'bg': 'white'
    }
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    encoded_xml = urllib.parse.quote(simple_xml)
    data = f"xml={encoded_xml}"
    
    try:
        print("🚀 测试简单 XML...")
        
        response = requests.post(
            export_url,
            params=params,
            data=data,
            headers=headers,
            timeout=15
        )
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            output_png = output_dir / "simple_test.png"
            
            with open(output_png, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 简单测试成功: {output_png}")
            print(f"📊 文件大小: {len(response.content)} 字节")
        else:
            print(f"❌ 简单测试失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 简单测试异常: {e}")


if __name__ == "__main__":
    test_drawio_api_export()
    test_simple_xml()
