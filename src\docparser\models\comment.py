"""
<AUTHOR> 王定雄
@Date    : 2024-11-11
@Desc    : 批注对象的数据结构的定义
"""
from docparser.models.base_object import BaseObject
from docparser.models.style import StyleObject
from docparser.models.coordinate import CoordinateObject


class CommentObject(BaseObject):
    """评论对象"""
    def __init__(self):
        self._text = ''  # 评论内容
        self._style = StyleObject()  # 样式
        self._coordinate = CoordinateObject()  # 坐标

    def to_dict(self):
        """
        将 CommentObject 对象转换为字典
        """
        return {
            "text": self._text,
            "style": self._style.to_dict(),  # 调用 StyleObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 CommentObject 实例
        """
        obj = cls()
        obj._text = data.get("text", '')
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 恢复 StyleObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        return obj

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, new_value):
        assert type(new_value) == str
        self._text = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value
