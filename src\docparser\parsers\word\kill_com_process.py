
import logging
import re
import time
import psutil
cur_ts = time.time()
for proc in psutil.process_iter():
    if re.findall(r"(?:RuntimeBroker|wps|wpscloudsvr|WINWORD.EXE)", proc.name()) and \
            proc.create_time() < cur_ts - 5:
        try:
            proc.terminate()
            print("old office processes has been killed.")
        except psutil.AccessDenied:
            raise psutil.AccessDenied(msg=f"permission to kill process is denied, please run with manage account")
        except psutil.NoSuchProcess:
            continue


