"""
Draw.io 架构图结构化读取工具
用于解析分层架构图，支持模块路径追踪和标红功能
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional
from dataclasses import dataclass
import re


@dataclass
class ModuleInfo:
    """模块信息"""
    id: str
    name: str
    x: float
    y: float
    width: float
    height: float
    parent_id: Optional[str] = None
    children: List[str] = None
    level: int = 0
    path: str = ""
    is_small_textbox: bool = False
    is_unnamed_container: bool = False

    def __post_init__(self):
        if self.children is None:
            self.children = []


class DrawioStructureReader:
    """Draw.io架构图结构化读取器"""
    
    def __init__(self, drawio_file_path: str):
        self.file_path = drawio_file_path
        self.modules: Dict[str, ModuleInfo] = {}
        self.root_modules: List[str] = []
        self.hierarchy: Dict[str, List[str]] = {}
        self.paths: Dict[str, str] = {}  # module_id -> full_path
        
    def parse(self) -> bool:
        """解析Draw.io文件"""
        try:
            tree = ET.parse(self.file_path)
            root = tree.getroot()

            # 找到mxGraphModel
            graph_model = root.find('.//mxGraphModel')
            if graph_model is None:
                print("❌ 未找到mxGraphModel元素")
                return False

            # 解析所有mxCell元素
            cells = graph_model.findall('.//mxCell')
            print(f"📊 找到 {len(cells)} 个元素")

            # 第一步：提取所有模块信息
            self._extract_modules(cells)

            # 第二步：转换相对坐标为绝对坐标
            self._convert_relative_coordinates()

            # 第三步：建立层级关系（基于空间包含关系，不依赖XML parent）
            self._build_hierarchy_by_spatial_containment()

            # 第四步：计算路径
            self._calculate_paths()

            print(f"✅ 解析完成: {len(self.modules)} 个模块, {len(self.root_modules)} 个根模块")
            return True

        except Exception as e:
            print(f"❌ 解析失败: {e}")
            return False
    
    def _extract_modules(self, cells: List[ET.Element]):
        """提取模块信息"""
        for cell in cells:
            # 跳过根元素和连接线
            if (cell.get('id') in ['0', '1'] or 
                cell.get('edge') == '1' or 
                cell.get('vertex') != '1'):
                continue
            
            # 获取几何信息
            geometry = cell.find('mxGeometry')
            if geometry is None:
                continue
            
            try:
                module_id = cell.get('id')
                name = self._extract_text_content(cell.get('value', ''))

                # 先获取几何信息
                x = float(geometry.get('x', 0))
                y = float(geometry.get('y', 0))
                width = float(geometry.get('width', 0))
                height = float(geometry.get('height', 0))
                parent_id = cell.get('parent', '1')

                # 处理空名称的模块，但保留可能是容器的模块
                if not name or not name.strip():
                    # 如果是容器模块（可能包含其他模块），给它一个默认名称
                    if width > 50 and height > 50:  # 可能是容器
                        name = f"container_{module_id[-3:]}"  # 使用ID后3位作为名称
                    else:
                        # 即使是小的无名元素，也要保留，因为可能是坐标转换链条中的一环
                        name = f"unnamed_{module_id[-3:]}"

                # 标记无名容器，用于后续路径优化
                is_unnamed_container = (not self._extract_text_content(cell.get('value', '')).strip())

                # 存储原始坐标用于后续转换
                original_x, original_y = x, y

                # 检查是否是小文本框（120×20左右的尺寸）
                is_small_textbox = (100 <= width <= 150 and 15 <= height <= 30)
                
                # 创建模块信息
                module = ModuleInfo(
                    id=module_id,
                    name=name,
                    x=x,
                    y=y,
                    width=width,
                    height=height,
                    parent_id=parent_id if parent_id != '1' else None
                )

                # 标记小文本框和无名容器
                module.is_small_textbox = is_small_textbox
                module.is_unnamed_container = is_unnamed_container
                
                self.modules[module_id] = module
                
            except (ValueError, TypeError) as e:
                print(f"⚠️  解析模块失败: {e}")
                continue
    
    def _extract_text_content(self, value: str) -> str:
        """从HTML格式的value中提取纯文本"""
        if not value:
            return ""
        
        # 解码HTML实体
        value = value.replace('&lt;', '<').replace('&gt;', '>').replace('&quot;', '"').replace('&amp;', '&')
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', value)
        
        # 清理空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text

    def _convert_relative_coordinates(self):
        """转换相对坐标为绝对坐标（基于XML中的parent关系）"""
        print("🔄 转换相对坐标为绝对坐标...")

        converted_count = 0
        max_iterations = len(self.modules) + 10  # 防止无限循环
        iteration = 0

        # 标记所有根模块（parent_id为None或'1'的模块）为已转换
        for module in self.modules.values():
            if module.parent_id is None or module.parent_id == '1':
                module._coordinate_converted = True

        # 使用迭代方式，确保所有模块都被转换
        while iteration < max_iterations:
            changed = False
            iteration += 1

            for module in self.modules.values():
                # 跳过已转换的模块
                if hasattr(module, '_coordinate_converted'):
                    continue

                # 跳过没有父模块的模块
                if not module.parent_id or module.parent_id not in self.modules:
                    module._coordinate_converted = True
                    continue

                parent_module = self.modules[module.parent_id]

                # 只有当父模块已经转换完成时才转换当前模块
                if hasattr(parent_module, '_coordinate_converted'):
                    # 将相对坐标转换为绝对坐标
                    old_x, old_y = module.x, module.y
                    module.x += parent_module.x
                    module.y += parent_module.y
                    module._coordinate_converted = True
                    converted_count += 1
                    changed = True

                    if converted_count <= 10:  # 只显示前10个转换
                        print(f"  转换 {module.name}: ({old_x}, {old_y}) + 父模块({parent_module.name})({parent_module.x}, {parent_module.y}) = ({module.x}, {module.y})")

            if not changed:
                break

        # 检查是否还有未转换的模块
        unconverted = [m for m in self.modules.values() if not hasattr(m, '_coordinate_converted')]
        if unconverted:
            print(f"⚠️  发现 {len(unconverted)} 个未转换的模块:")
            for module in unconverted[:5]:  # 只显示前5个
                print(f"    {module.name} (parent: {module.parent_id})")
            # 强制标记为已转换，避免后续问题
            for module in unconverted:
                module._coordinate_converted = True

        print(f"✅ 坐标转换完成，转换了 {converted_count} 个模块的坐标")

    def _build_hierarchy_by_spatial_containment(self):
        """基于空间包含关系建立层级关系（不依赖XML parent）"""
        print("🔄 基于空间包含关系建立层级...")

        # 清空现有的层级关系
        for module in self.modules.values():
            module.parent_id = None
            module.children = []
        self.root_modules = []

        # 为所有模块找到潜在的父模块
        for module_id, module in self.modules.items():
            potential_parents = []

            for other_id, other_module in self.modules.items():
                if other_id == module_id:
                    continue

                # 检查other_module是否包含module
                if self._is_contained(module, other_module):
                    potential_parents.append((other_id, other_module))

            # 如果有潜在父模块，选择最合适的
            if potential_parents:
                # 对于小文本框，优先选择非小文本框的父模块
                if module.is_small_textbox:
                    # 过滤出非小文本框的父模块
                    non_textbox_parents = [(pid, pm) for pid, pm in potential_parents if not pm.is_small_textbox]
                    if non_textbox_parents:
                        # 从非小文本框中选择最小的
                        direct_parent_id, _ = min(non_textbox_parents,
                                                key=lambda x: x[1].width * x[1].height)
                    else:
                        # 如果没有非小文本框父模块，选择最小的
                        direct_parent_id, _ = min(potential_parents,
                                                key=lambda x: x[1].width * x[1].height)
                else:
                    # 对于普通模块，选择面积最小的父模块（最直接的父模块）
                    direct_parent_id, _ = min(potential_parents,
                                            key=lambda x: x[1].width * x[1].height)

                # 设置父子关系
                module.parent_id = direct_parent_id
                self.modules[direct_parent_id].children.append(module_id)
            else:
                # 没有父模块，是根模块
                self.root_modules.append(module_id)

        # 检测并修复循环引用
        self._detect_and_fix_cycles()

        # 计算层级
        self._calculate_levels()

        print(f"✅ 空间层级关系建立完成")

    def _fix_coordinates_by_spatial_hierarchy(self):
        """基于空间层级关系修正坐标"""
        print("🔧 基于空间层级关系修正坐标...")

        fixed_count = 0

        # 特殊处理：查找可能需要坐标修正的模块
        # 1. 查找 msg 模块和 dsp_mainscrlcore 模块
        msg_modules = self.find_modules_by_name('msg')
        core_modules = self.find_modules_by_name('dsp_mainscrlcore')

        if msg_modules and core_modules:
            msg = msg_modules[0]
            core = core_modules[0]

            print(f"🔍 检查 msg 模块坐标...")
            print(f"  msg: ({msg.x}, {msg.y}), 尺寸: {msg.width}x{msg.height}")
            print(f"  dsp_mainscrlcore: ({core.x}, {core.y}), 尺寸: {core.width}x{core.height}")

            # 检查 msg 是否应该在 dsp_mainscrlcore 内
            if not self._is_contained(msg, core):
                print(f"  msg 不在 dsp_mainscrlcore 内，尝试修正...")

                # 查找 msg 的XML parent，获取其坐标
                msg_xml_parent_id = None
                for module_id, module in self.modules.items():
                    if hasattr(module, '_original_parent_id'):
                        if module.id == msg.id:
                            msg_xml_parent_id = module._original_parent_id
                            break

                # 如果没有找到，从原始数据中查找
                if not msg_xml_parent_id:
                    # 这里需要从原始XML中获取parent信息
                    # 根据诊断结果，msg的parent是 1-0bCCSwIkjvpjtFy3q8-465
                    # 其坐标是 (-610, 865)

                    # 尝试计算正确的绝对坐标
                    # msg 相对坐标: (153, 30)
                    # msg parent 坐标: (-610, 865) (从诊断结果得出)

                    old_x, old_y = msg.x, msg.y

                    # 如果 msg 的坐标看起来是相对坐标
                    if msg.x > 0 and msg.y > 0 and msg.x < 1000 and msg.y < 1000:
                        # 尝试加上可能的父容器偏移
                        # 根据诊断结果，msg的XML parent坐标是 (-610, 865)
                        potential_x = msg.x + (-610)
                        potential_y = msg.y + 865

                        # 检查这个坐标是否在 dsp_mainscrlcore 内
                        temp_x, temp_y = msg.x, msg.y
                        msg.x, msg.y = potential_x, potential_y

                        if self._is_contained(msg, core):
                            print(f"  ✅ 修正成功: msg ({old_x}, {old_y}) -> ({msg.x}, {msg.y})")
                            fixed_count += 1

                            # 更新 msg 的父子关系
                            msg.parent_id = core.id
                            core.children.append(msg.id)

                            # 从根模块列表中移除 msg（如果存在）
                            if msg.id in self.root_modules:
                                self.root_modules.remove(msg.id)
                        else:
                            # 回滚
                            msg.x, msg.y = temp_x, temp_y
                            print(f"  ❌ 修正失败: msg 仍不在 dsp_mainscrlcore 内")

        # 遍历其他模块，检查是否需要坐标修正
        for module_id, module in self.modules.items():
            if module.parent_id and module.parent_id in self.modules:
                parent = self.modules[module.parent_id]

                # 检查模块是否真的在父模块内
                if not self._is_contained(module, parent):
                    # 跳过已经处理过的 msg 模块
                    if module.name == 'msg':
                        continue

                    print(f"⚠️  模块 {module.name} 不在父模块 {parent.name} 内，尝试修正...")

                    # 简单策略：如果模块明显应该在父模块内
                    if self._should_be_contained(module, parent):
                        old_x, old_y = module.x, module.y

                        # 如果模块坐标看起来是相对坐标
                        if (module.x < parent.x + parent.width and
                            module.y < parent.y + parent.height and
                            module.x >= 0 and module.y >= 0):

                            # 转换为绝对坐标
                            module.x = parent.x + old_x
                            module.y = parent.y + old_y

                            # 再次检查是否在父模块内
                            if self._is_contained(module, parent):
                                print(f"  ✅ 修正成功: {module.name} ({old_x}, {old_y}) -> ({module.x}, {module.y})")
                                fixed_count += 1
                            else:
                                # 回滚
                                module.x, module.y = old_x, old_y
                                print(f"  ❌ 修正失败: {module.name}")

        print(f"✅ 坐标修正完成，修正了 {fixed_count} 个模块")

    def _should_be_contained(self, child: ModuleInfo, parent: ModuleInfo) -> bool:
        """判断子模块是否应该被父模块包含（基于语义分析）"""
        # 简单的启发式规则
        # 1. 如果子模块名称包含在父模块路径中
        # 2. 如果子模块是小文本框且在父模块附近

        if child.is_small_textbox:
            # 小文本框更可能是标签，应该在大容器内
            return True

        # 可以根据具体业务逻辑添加更多规则
        return False

    def _build_hierarchy(self):
        """建立层级关系（优化版，正确处理小文本框）"""
        # 第一步：为所有模块找到潜在的父模块
        for module_id, module in self.modules.items():
            potential_parents = []

            for other_id, other_module in self.modules.items():
                if other_id == module_id:
                    continue

                # 检查other_module是否包含module
                if self._is_contained(module, other_module):
                    potential_parents.append((other_id, other_module))

            # 如果有潜在父模块，选择最合适的
            if potential_parents:
                # 对于小文本框，优先选择非小文本框的父模块
                if module.is_small_textbox:
                    # 过滤出非小文本框的父模块
                    non_textbox_parents = [(pid, pm) for pid, pm in potential_parents if not pm.is_small_textbox]
                    if non_textbox_parents:
                        # 从非小文本框中选择最小的
                        non_textbox_parents.sort(key=lambda x: x[1].width * x[1].height)
                        direct_parent_id = non_textbox_parents[0][0]
                    else:
                        # 如果只有小文本框父模块，选择最小的
                        potential_parents.sort(key=lambda x: x[1].width * x[1].height)
                        direct_parent_id = potential_parents[0][0]
                else:
                    # 对于普通模块，选择最小的包含模块
                    potential_parents.sort(key=lambda x: x[1].width * x[1].height)
                    direct_parent_id = potential_parents[0][0]

                # 设置父子关系
                module.parent_id = direct_parent_id
                self.modules[direct_parent_id].children.append(module_id)
            else:
                # 没有父模块，是根模块
                self.root_modules.append(module_id)

        # 检测并修复循环引用
        self._detect_and_fix_cycles()
        
        # 计算层级
        self._calculate_levels()
    
    def _is_contained(self, child: ModuleInfo, parent: ModuleInfo) -> bool:
        """检查child是否被parent包含"""
        return (child.x >= parent.x and 
                child.y >= parent.y and
                child.x + child.width <= parent.x + parent.width and
                child.y + child.height <= parent.y + parent.height)
    
    def _detect_and_fix_cycles(self):
        """检测并修复循环引用"""
        cycles_fixed = 0
        
        for module_id, module in self.modules.items():
            if module.parent_id is None:
                continue
                
            # 检查是否存在循环引用
            visited = set()
            current = module_id
            
            while current and current in self.modules:
                if current in visited:
                    # 发现循环，断开当前模块的父子关系
                    print(f"⚠️  检测到循环引用，断开模块 {module.name} (ID: {module_id}) 的父子关系")
                    
                    # 从父模块的子模块列表中移除
                    if module.parent_id and module.parent_id in self.modules:
                        parent_children = self.modules[module.parent_id].children
                        if module_id in parent_children:
                            parent_children.remove(module_id)
                    
                    # 清除父模块引用，将其设为根模块
                    module.parent_id = None
                    if module_id not in self.root_modules:
                        self.root_modules.append(module_id)
                    
                    cycles_fixed += 1
                    break
                
                visited.add(current)
                current = self.modules[current].parent_id if current in self.modules else None
        
        if cycles_fixed > 0:
            print(f"✅ 修复了 {cycles_fixed} 个循环引用")
    
    def _calculate_levels(self):
        """计算模块层级（非递归版本）"""
        # 使用队列进行广度优先遍历
        from collections import deque

        queue = deque()

        # 初始化根模块
        for root_id in self.root_modules:
            if root_id in self.modules:
                self.modules[root_id].level = 0
                queue.append((root_id, 0))

        # 广度优先遍历设置层级
        while queue:
            module_id, level = queue.popleft()

            if module_id in self.modules:
                # 处理所有子模块
                for child_id in self.modules[module_id].children:
                    if child_id in self.modules:
                        self.modules[child_id].level = level + 1
                        queue.append((child_id, level + 1))

    def _calculate_paths(self):
        """计算模块路径（非递归版本，跳过无名容器）"""
        # 使用迭代方式避免递归深度问题
        processed = set()

        # 先处理根模块
        for root_id in self.root_modules:
            if root_id in self.modules:
                module = self.modules[root_id]
                # 如果根模块是无名容器，使用空路径
                if module.is_unnamed_container:
                    self.paths[root_id] = ""
                    module.path = ""
                else:
                    self.paths[root_id] = module.name
                    module.path = module.name
                processed.add(root_id)

        # 按层级处理其他模块，增加最大迭代次数防止死循环
        max_iterations = len(self.modules) + 10
        iteration = 0

        while iteration < max_iterations:
            changed = False
            iteration += 1

            for module_id, module in self.modules.items():
                if module_id in processed:
                    continue

                # 如果父模块已处理，则处理当前模块
                if module.parent_id is None or module.parent_id in processed:
                    if module.parent_id is None:
                        # 没有父模块，应该是根模块
                        if module.is_unnamed_container:
                            path = ""
                        else:
                            path = module.name
                        # 如果不在根模块列表中，添加进去
                        if module_id not in self.root_modules:
                            self.root_modules.append(module_id)
                    else:
                        # 有父模块，构建完整路径
                        if module.parent_id in self.paths:
                            parent_path = self.paths[module.parent_id]

                            # 如果当前模块是无名容器，不添加到路径中
                            if module.is_unnamed_container:
                                path = parent_path
                            else:
                                # 如果父路径为空（父模块是无名容器），直接使用当前模块名
                                if parent_path == "":
                                    path = module.name
                                else:
                                    path = f"{parent_path}/{module.name}"
                        else:
                            # 父模块路径还没计算出来，跳过这次
                            continue

                    self.paths[module_id] = path
                    module.path = path
                    processed.add(module_id)
                    changed = True

            # 如果没有变化，说明所有能处理的都处理完了
            if not changed:
                break

        # 处理剩余的孤儿模块（可能是循环引用或其他问题）
        unprocessed = set(self.modules.keys()) - processed
        if unprocessed:
            print(f"⚠️  发现 {len(unprocessed)} 个未处理的模块，将其设为根模块")
            for module_id in unprocessed:
                module = self.modules[module_id]
                if module.is_unnamed_container:
                    path = ""
                else:
                    path = module.name
                self.paths[module_id] = path
                module.path = path
                self.root_modules.append(module_id)
                processed.add(module_id)

    def get_module_by_path(self, path: str) -> Optional[ModuleInfo]:
        """根据路径获取模块"""
        for module in self.modules.values():
            if module.path == path:
                return module
        return None

    def find_modules_by_name(self, name: str) -> List[ModuleInfo]:
        """根据名称查找模块"""
        return [module for module in self.modules.values() if module.name == name]

    def get_children(self, module_id: str) -> List[ModuleInfo]:
        """获取子模块"""
        if module_id not in self.modules:
            return []

        return [self.modules[child_id] for child_id in self.modules[module_id].children]

    def get_all_descendants(self, module_id: str) -> List[ModuleInfo]:
        """获取所有后代模块（非递归版本，防循环引用）"""
        if module_id not in self.modules:
            return []

        descendants = []
        visited = set()  # 防止循环引用
        from collections import deque
        queue = deque([module_id])
        visited.add(module_id)  # 将起始模块加入已访问集合

        while queue:
            current_id = queue.popleft()
            if current_id in self.modules:
                for child_id in self.modules[current_id].children:
                    if child_id in self.modules and child_id not in visited:
                        descendants.append(self.modules[child_id])
                        queue.append(child_id)
                        visited.add(child_id)  # 标记为已访问

        return descendants

    def print_hierarchy(self, module_id: str = None, indent: int = 0):
        """打印层级结构（非递归版本）"""
        if module_id is None:
            print("📋 架构层级结构:")
            # 使用栈来避免递归
            from collections import deque
            stack = deque()

            # 将根模块加入栈（逆序以保持正确的打印顺序）
            for root_id in reversed(self.root_modules):
                if root_id in self.modules:
                    stack.append((root_id, 0))

            while stack:
                current_id, current_indent = stack.pop()
                if current_id not in self.modules:
                    continue

                module = self.modules[current_id]
                prefix = "  " * current_indent + ("├─ " if current_indent > 0 else "")
                print(f"{prefix}{module.name} (Level {module.level}) - {module.path}")

                # 将子模块加入栈（逆序以保持正确的打印顺序）
                for child_id in reversed(module.children):
                    if child_id in self.modules:
                        stack.append((child_id, current_indent + 1))
            return

        # 单个模块的情况保持简单
        if module_id not in self.modules:
            return

        module = self.modules[module_id]
        prefix = "  " * indent + ("├─ " if indent > 0 else "")
        print(f"{prefix}{module.name} (Level {module.level}) - {module.path}")

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        levels = {}
        small_textboxes = 0
        regular_modules = 0

        for module in self.modules.values():
            level = module.level
            if level not in levels:
                levels[level] = 0
            levels[level] += 1

            if module.is_small_textbox:
                small_textboxes += 1
            else:
                regular_modules += 1

        return {
            "total_modules": len(self.modules),
            "root_modules": len(self.root_modules),
            "max_level": max(levels.keys()) if levels else 0,
            "modules_per_level": levels,
            "small_textboxes": small_textboxes,
            "regular_modules": regular_modules
        }
