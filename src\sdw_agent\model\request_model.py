from pydantic import BaseModel, model_validator
from typing import Optional


class SourceInfo(BaseModel):
    type: str  # local, svn, git
    uri: str
    uuid: Optional[str] = ''


class RamRepoInfo(BaseModel):
    repo_path: str
    commit_id: str
    compared_commit_id: str


class DesignPeerReviewRequest(BaseModel):
    folderPath: str

class RepoInfo(BaseModel):
    repo_path: str
    commit_id: str
    compared_commit_id: Optional[str] = None

class SPIInfo(BaseModel):
    can_path: SourceInfo
    ctrl_path: SourceInfo

class CloneRepoRequest(BaseModel):
    repo_url: str
    username: str = ""
    password: str = ""
    branch: str
    repo_local_path: str = ""


class ChangePointImportRequest(BaseModel):
    file: str

class TeleTableFiller(BaseModel):
    uploaded_file_path: str
    rag_source_dir: str

class TestViewFileParserRequest(BaseModel):
    std_testcase_file_path: str
    test_rule_file_path: str
    knowledge_repo_path: str


class TestViewGenerateRequest(BaseModel):
    change_points: dict


class TestViewSuppleRequest(BaseModel):
    message: int


class RamGlobalVarRequest(BaseModel):
    keySource: SourceInfo
    repo: RamRepoInfo


class ChecklistReviewRequest(BaseModel):
    changepoint_file_path: str
    testcase_file_path: str
    checklist_file_path: str


class DnCodecheckRequest(BaseModel):
    commit_id: str


class OSSExaminationRequest(BaseModel):
    file_path: str

class ChecksheetRequest(BaseModel):
    checksheet_path: str
    target_filename: list[str]

class DscsCodeCheckRequest(BaseModel):
    repo: RamRepoInfo