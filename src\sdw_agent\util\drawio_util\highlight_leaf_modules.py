#!/usr/bin/env python3
"""
高亮叶子模块示例
高亮指定路径中的每个叶子模块（最后一个子模块）
"""

from pathlib import Path
from typing import List, Dict, Optional

from loguru import logger

from sdw_agent.service.template_manager import template_manager
from sdw_agent.util.drawio_util.architecture_analyzer import ArchitectureAnalyzer


def highlight_leaf_modules(
        target_paths: List[str],
        drawio_file_path: Optional[str] = None,
        output_file_path: Optional[str] = None,
        highlight_color: str = "red",
        verbose: bool = False,
        use_template_manager: bool = True
) -> Dict[str, any]:
    """
    高亮指定路径中的叶子模块（最后一个子模块）

    Args:
        target_paths: 要高亮的路径列表
        drawio_file_path: Draw.io文件路径，如果为None且use_template_manager为True，则使用模板管理器获取
        output_file_path: 输出文件路径，如果为None则自动生成
        highlight_color: 高亮颜色，默认为红色
        verbose: 是否输出详细日志
        use_template_manager: 是否使用模板管理器获取Draw.io文件，默认为True

    Returns:
        Dict包含以下信息:
        - success: bool - 是否成功
        - output_file: str - 输出文件路径
        - highlighted_count: int - 成功高亮的模块数量
        - failed_count: int - 失败的模块数量
        - details: List[Dict] - 每个路径的详细结果
        - message: str - 结果消息
        - template_source: str - 模板来源（"template_manager", "custom_path", "builtin"）
    """
    # 确定Draw.io文件路径
    template_source = "custom_path"
    actual_drawio_path = drawio_file_path

    if use_template_manager and drawio_file_path is None:
        # 使用模板管理器获取架构图模板
        template_path = template_manager.get_template_path("block_diagram_file")
        if template_path:
            actual_drawio_path = template_path
            template_source = "template_manager"
            logger.info(f"使用模板管理器获取架构图: {actual_drawio_path}")
        else:
            error_msg = "无法从模板管理器获取架构图模板"
            logger.error(error_msg)
            return {
                "success": False,
                "output_file": None,
                "highlighted_count": 0,
                "failed_count": len(target_paths),
                "details": [],
                "message": error_msg,
                "template_source": "none"
            }
    elif drawio_file_path is None:
        # 使用内置的默认文件
        current_dir = Path(__file__).parent
        actual_drawio_path = str(current_dir / "block_diagram.drawio")
        template_source = "builtin"
        logger.info(f"使用内置架构图: {actual_drawio_path}")

    logger.info(f"开始高亮叶子模块: {actual_drawio_path}")

    # 检查输入文件是否存在
    if not Path(actual_drawio_path).exists():
        error_msg = f"Draw.io文件不存在: {actual_drawio_path}"
        logger.error(error_msg)
        return {
            "success": False,
            "output_file": None,
            "highlighted_count": 0,
            "failed_count": len(target_paths),
            "details": [],
            "message": error_msg,
            "template_source": template_source
        }

    # 生成输出文件路径
    if output_file_path is None:
        input_path = Path(actual_drawio_path)
        output_file_path = str(input_path.parent / f"{input_path.stem}_highlighted{input_path.suffix}")

    try:
        # 1. 创建并加载分析器
        analyzer = ArchitectureAnalyzer(actual_drawio_path)
        if not analyzer.load():
            error_msg = f"加载Draw.io文件失败: {actual_drawio_path}"
            logger.error(error_msg)
            return {
                "success": False,
                "output_file": None,
                "highlighted_count": 0,
                "failed_count": len(target_paths),
                "details": [],
                "message": error_msg,
                "template_source": template_source
            }

        if verbose:
            logger.info("分析器加载成功")

        # 2. 逐个高亮每个路径的叶子模块
        success_count = 0
        details = []

        for i, path in enumerate(target_paths):
            # 获取路径的最后一个模块名（叶子模块）
            leaf_module_name = path.split('/')[-1] if '/' in path else path

            # 尝试高亮这个路径
            success = analyzer.highlighter.highlight_by_path(path, highlight_color)

            detail = {
                "index": i + 1,
                "path": path,
                "leaf_module_name": leaf_module_name,
                "success": success
            }

            if success:
                success_count += 1
                detail["message"] = "高亮成功"
                if verbose:
                    logger.info(f"✅ {leaf_module_name} - 路径: {path}")
            else:
                detail["message"] = "路径不存在或高亮失败"
                if verbose:
                    logger.warning(f"❌ {leaf_module_name} - 路径不存在: {path}")

            details.append(detail)

        # 3. 保存结果
        failed_count = len(target_paths) - success_count

        if success_count > 0:
            saved = analyzer.highlighter.save(output_file_path)
            if saved:
                success_msg = f"成功高亮 {success_count}/{len(target_paths)} 个模块，结果已保存到: {output_file_path}"
                logger.info(success_msg)
                return {
                    "success": True,
                    "output_file": output_file_path,
                    "highlighted_count": success_count,
                    "failed_count": failed_count,
                    "details": details,
                    "message": success_msg,
                    "template_source": template_source
                }
            else:
                error_msg = f"高亮成功但保存失败: {output_file_path}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "output_file": None,
                    "highlighted_count": success_count,
                    "failed_count": failed_count,
                    "details": details,
                    "message": error_msg,
                    "template_source": template_source
                }
        else:
            error_msg = f"没有成功高亮任何模块，共 {len(target_paths)} 个路径失败"
            logger.warning(error_msg)
            return {
                "success": False,
                "output_file": None,
                "highlighted_count": 0,
                "failed_count": failed_count,
                "details": details,
                "message": error_msg,
                "template_source": template_source
            }

    except Exception as e:
        error_msg = f"高亮叶子模块时发生异常: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "output_file": None,
            "highlighted_count": 0,
            "failed_count": len(target_paths),
            "details": [],
            "message": error_msg,
            "template_source": template_source
        }


def highlight_leaf_modules_demo():
    """高亮指定路径中的叶子模块（最后一个子模块）- 演示函数"""
    print("🎯 高亮叶子模块演示")
    print("=" * 50)

    # 指定的路径列表（使用实际存在的路径）
    target_paths = [
        "Disp/Dsp_app/Mainscrl/dsp_mainscrlcore/msg",
        "Disp/Dsp_app/Wrndtct/dsp_wrndtctcore/wrndtcfg",
        "Disp/Dsp_app/Wrnstsctl/dspwrn_cfg",
        "Disp/Dsp_app/Wrnstsctl/dwstoutgntr",
        "Disp/Dsp_build/Cnttdata/wrn",
        "Graphic (A53)/App/AppObj/tftwarning"
    ]

    # 调用工具方法 - 使用模板管理器
    result = highlight_leaf_modules(
        target_paths=target_paths,
        output_file_path="leaf_modules_highlighted.drawio",
        highlight_color="red",
        verbose=True,
        use_template_manager=True  # 使用模板管理器
    )

    # 显示结果
    print(f"\n📊 高亮结果:")
    print(f"   成功: {result['success']}")
    print(f"   高亮模块数: {result['highlighted_count']}")
    print(f"   失败模块数: {result['failed_count']}")
    print(f"   输出文件: {result['output_file']}")
    print(f"   模板来源: {result['template_source']}")
    print(f"   消息: {result['message']}")

    if result['details']:
        print(f"\n📋 详细结果:")
        for detail in result['details']:
            status = "✅" if detail['success'] else "❌"
            print(f"   {detail['index']}. {status} {detail['leaf_module_name']}")
            print(f"      路径: {detail['path']}")
            print(f"      状态: {detail['message']}")


if __name__ == "__main__":
    highlight_leaf_modules_demo()
