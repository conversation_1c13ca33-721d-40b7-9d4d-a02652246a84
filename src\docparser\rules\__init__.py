"""
Rules package for document parsing.
"""

# Import rule classes for easier access
from docparser.rules.dynamic_rule import (
    DynamicRule,
    DynamicTextRule,
    DynamicTableRule,
    DynamicPictureRule,
    DynamicGraphicRule,
    DynamicDocumentRule
)

# Import example rules
try:
    from docparser.rules.example_rules import (
        HeaderTextRule,
        TableCaptionRule,
        ImageMetadataRule,
        GraphicClassifierRule,
        DocumentMetadataRule
    )
except ImportError:
    # Example rules may not be available
    pass
