import unittest
from fastapi.testclient import TestClient
from sdw_agent.api.routers.integration import router
from fastapi import FastAPI

app = FastAPI()
app.include_router(router)

client = TestClient(app)


class TestIfIntegration(unittest.TestCase):

    def test_if_integration_success(self):
        """
        测试 I/F 整合性确认接口的正常情况
        """
        test_request_data = {
            "ifStylebook":{
                "uri": "http://example.com/if_stylebook.xlsx",
                "type": "aaa"},
            "gerritInfo":{
                "uri": "http://gerrit.example.com/repo.git",
                "username": "user",
                "password": "pass",
                "branch": "main",
                "type": "12"
            }
        }

        expected_response = {
            "code": 0,
            "msg": "",
            "data": {
                "if_integration_uri": "/path/to/report.xlsx"
            }
        }

        # 模拟 do_if_integration 返回值（可以使用 unittest.mock.patch）
        from unittest.mock import patch
        with patch('sdw_agent.api.routers.integration.do_if_integration') as mock_do:
            mock_do.return_value = "/path/to/report.xlsx"

            response = client.post("/api/sdw/integration/if_integration", json=test_request_data)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), expected_response)

    def test_if_integration_missing_fields(self):
        """
        测试请求数据缺失字段时接口的行为
        """
        invalid_request_data = {
            # 缺少 ifStylebook 和 gerritInfo 字段
        }

        response = client.post("/api/sdw/integration/if_integration", json=invalid_request_data)
        self.assertEqual(response.status_code, 422)  # FastAPI 的验证失败会返回 422 Unprocessable Entity


if __name__ == '__main__':
    unittest.main()
