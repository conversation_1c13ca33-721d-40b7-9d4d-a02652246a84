# -*- coding: utf-8 -*-
"""
@File    : base.py
<AUTHOR> 董根虎
@Date    : 2024-07-08 09:18
@Desc    : 为文档解析查分提供基础工具包

"""
import json
import logging
import os
import re
import time
from base64 import b64encode
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from io import BytesIO
from pathlib import Path
from threading import Lock
from typing import List, Union, Dict, Any

import imagehash
import openpyxl
import psutil
import pythoncom  # 导入 pythoncom 库
import win32com.client
import win32com.client as win32
from PIL import Image, ImageGrab
from openpyxl.cell.rich_text import TextBlock, CellRichText
from openpyxl.styles.colors import BLACK
from docparser.models.style import StyleObject

# from langchain_core.prompts import PromptTemplate
# import config
# from models import cur_chat

WIN32_LOCK = Lock()

# Default Color Index as per 18.8.27 of ECMA Part 4
COLOR_INDEX = (
    '00000000', '00FFFFFF', '00FF0000', '0000FF00', '000000FF', #0-4
    '00FFFF00', '00FF00FF', '0000FFFF', '00000000', '00FFFFFF', #5-9
    '00FF0000', '0000FF00', '000000FF', '00FFFF00', '00FF00FF', #10-14
    '0000FFFF', '00800000', '00008000', '00000080', '00808000', #15-19
    '00800080', '00008080', '00C0C0C0', '00808080', '009999FF', #20-24
    '00993366', '00FFFFCC', '00CCFFFF', '00660066', '00FF8080', #25-29
    '000066CC', '00CCCCFF', '00000080', '00FF00FF', '00FFFF00', #30-34
    '0000FFFF', '00800080', '00800000', '00008080', '000000FF', #35-39
    '0000CCFF', '00CCFFFF', '00CCFFCC', '00FFFF99', '0099CCFF', #40-44
    '00FF99CC', '00CC99FF', '00FFCC99', '003366FF', '0033CCCC', #45-49
    '0099CC00', '00FFCC00', '00FF9900', '00FF6600', '00666699', #50-54
    '00969696', '00003366', '00339966', '00003300', '00333300', #55-59
    '00993300', '00993366', '00333399', '00333333',  #60-63
)

def convert_xls_to_xlsx(file_path) -> str:
    # 初始化 COM 库
    pythoncom.CoInitialize()

    path = Path(file_path)
    # excel = win32.gencache.EnsureDispatch('Excel.Application')
    excel = win32com.client.Dispatch("Excel.Application")
    try:
        excel.Visible = False
        excel.DisplayAlerts = False

        # 构建目标文件路径
        target_path = path.absolute().with_suffix(".xlsx")

        # 如果目标文件存在，先删除它
        if target_path.exists():
            os.remove(target_path)

        wb = excel.Workbooks.Open(path.absolute())

        # FileFormat=51 is for .xlsx extension
        target_path = str(target_path)
        wb.SaveAs(target_path, FileFormat=51)
        # 关闭工作簿，不保存更改
        wb.Close(False)

    finally:
        # 退出 Excel 应用并取消 COM 库初始化
        # excel.Application.Quit()
        pythoncom.CoUninitialize()
    return target_path


def cell_in_range(row_, col_, list_):
    """
    判断单元格是否在指定范围内
    :param row_:
    :param col_:
    :param list_:
    :return:
    """
    if not list_:
        return False
    for h in list_:
        if isinstance(h, dict):
            for r in h["range"]:
                if r[0] <= row_ <= r[2] and r[1] <= col_ <= r[3]:
                    return True
        elif isinstance(h, (tuple, list)):
            if h[0][0] <= row_ <= h[1][0] and h[0][1] <= col_ <= h[1][1]:
                return True
    return False


def get_no_border_cells(cell, sheet, search_directions=None, search_depth=5, cell_range=None):
    if cell_range is None:
        cell_range = []
    if search_directions is None:
        search_directions = ['top', 'left', 'right', 'bottom']

    if search_depth == -1:
        search_count = float('inf')
    else:
        search_count = len(search_directions) * search_depth

    max_row = sheet.max_row
    max_column = sheet.max_column

    # 检查单元格是否有边框
    def has_border(cell, direction):
        if not (cell and cell.border):
            return False
        if direction == 'left':
            left_cell = sheet.cell(row=cell.row, column=cell.column - 1) if cell.column > 1 else None
            if left_cell and left_cell.border:
                return cell.border.left.style is not None or (
                        left_cell is not None and left_cell.border.right.style is not None)
        elif direction == 'right':
            right_cell = sheet.cell(row=cell.row, column=cell.column + 1)
            if right_cell and right_cell.border:
                return cell.border.right.style is not None or right_cell.border.left.style is not None
        elif direction == 'top':
            top_cell = sheet.cell(row=cell.row - 1, column=cell.column) if cell.row > 1 else None
            if top_cell and top_cell.border:
                return cell.border.top.style is not None or (
                        top_cell is not None and top_cell.border.bottom.style is not None)
        elif direction == 'bottom':
            bottom_cell = sheet.cell(row=cell.row + 1, column=cell.column)
            if bottom_cell and bottom_cell.border:
                return cell.border.bottom.style is not None or bottom_cell.border.top.style is not None
        return False

    visited = []
    queue = [(cell.row, cell.column)]

    while queue and search_count > 0:
        row, col = queue.pop(0)
        if (row, col) in visited:
            continue
        visited.append((row, col))
        cell = sheet.cell(row=row, column=col)
        if cell_range and not cell_in_range(cell.row, cell.column, cell_range):
            continue
        # 检查上边的单元格
        if 'top' in search_directions:
            if row > 1 and not has_border(cell, 'top'):
                # logging.info(f'{cell.coordinate} no top border')
                queue.append((row - 1, col))

        # 检查左边的单元格
        if 'left' in search_directions:
            if col > 1 and not has_border(cell, 'left'):
                # logging.info(f'{cell.coordinate} no left border')
                queue.append((row, col - 1))

        # 检查右边的单元格
        if 'right' in search_directions:
            if col <= max_column and not has_border(cell, 'right'):
                # logging.info(f'{cell.coordinate} no right border')
                queue.append((row, col + 1))

        # 检查下边的单元格
        if 'bottom' in search_directions:
            if row <= max_row and not has_border(cell, 'bottom'):
                # logging.info(f'{cell.coordinate} no bottom border')
                queue.append((row + 1, col))

        search_count -= 1

    return visited


def get_sheet_free_cells(sheet):
    """ get sheet free cells """
    logging.info("start to get sheet free cells.")
    max_row = sheet.max_row
    max_column = sheet.max_column
    free_cells = set()
    left_top_cell = sheet.cell(1, 1)
    if left_top_cell.border.left.style is None or left_top_cell.border.top.style is None:
        free_cells = free_cells | set(get_no_border_cells(left_top_cell, sheet, search_depth=-1))
    right_bottom_cell = sheet.cell(max_row, max_column)
    if (max_row, max_column) not in free_cells and (
            right_bottom_cell.border.right.style is None or right_bottom_cell.border.bottom.style is None):
        free_cells = free_cells | set(get_no_border_cells(right_bottom_cell, sheet, search_depth=-1))
    return free_cells


def get_sheet_ranges(sheet, free_cells):
    # 获取表格的最大行数和最大列数
    sheet_max_row = sheet.max_row
    sheet_max_column = sheet.max_column

    # 将 free_cells 转换为集合，便于快速查找
    free_cells_set = set(free_cells)
    for col in range(1, sheet_max_column + 1):
        free_cells_set.add((sheet_max_row + 1, col))
    for row in range(1, sheet_max_column + 1):
        free_cells_set.add((row, sheet_max_column + 1))
    # 初始化访问标记
    visited = set()

    def dfs(row, col):
        stack = [(row, col)]
        min_row, max_row = row, row
        min_col, max_col = col, col

        while stack:
            r, c = stack.pop()
            if (r, c) not in visited:
                visited.add((r, c))
                min_row = min(min_row, r)
                max_row = max(max_row, r)
                min_col = min(min_col, c)
                max_col = max(max_col, c)

                # 检查相邻的单元格
                for dr, dc in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nr, nc = r + dr, c + dc
                    if 1 <= nr <= sheet_max_row and 1 <= nc <= sheet_max_column:
                        if (nr, nc) not in free_cells_set and (nr, nc) not in visited:
                            stack.append((nr, nc))

        return (min_row, min_col), (max_row, max_col)

    ranges = []

    # 遍历所有可能的单元格
    for row in range(1, sheet_max_row + 1):
        for col in range(1, sheet_max_column + 1):
            if (row, col) not in free_cells_set and (row, col) not in visited:
                range_ = dfs(row, col)
                ranges.append(range_)

    return ranges


def range_cross_ranges(range_start_, range_end_, range_list_):
    # 将输入的 range_start_ 和 range_end_ 转换为 (min_row, min_col, max_row, max_col) 形式
    min_row, min_col = range_start_
    max_row, max_col = range_end_

    # 遍历 range_list_ 中的每一个 range
    for r in range_list_:
        # 将 range_list_ 中的每一个 range 转换为 (min_row, min_col, max_row, max_col) 形式
        (r_min_row, r_min_col), (r_max_row, r_max_col) = r

        # 判断输入的 range 是否与当前 range 有重合
        if not (max_row < r_min_row or min_row > r_max_row or
                max_col < r_min_col or min_col > r_max_col):
            return True

    # 如果没有找到与输入 range 重合的 range，返回 False
    return False


def expand_tables(sheet, tables):
    # 遍历每个区域
    _new_tables = []
    for i, ((min_row, min_col), (max_row, max_col)) in enumerate(tables):
        if range_cross_ranges((min_row, min_col), (max_row, max_col), _new_tables):
            continue
        # 检查上边
        while min_row > 1:
            changed = False
            for col in range(min_col, max_col + 1):
                cell = sheet.cell(row=min_row, column=col)
                neighbor_cell = sheet.cell(row=min_row - 1, column=col)
                if (cell.border.left.style is not None and cell.border.left.style == neighbor_cell.border.left.style) or \
                        (
                                cell.border.right.style is not None and cell.border.right.style == neighbor_cell.border.right.style):
                    min_row -= 1
                    changed = True
                    break
            if not changed:
                break

        # 检查下边
        while max_row < sheet.max_row:
            changed = False
            for col in range(min_col, max_col + 1):
                cell = sheet.cell(row=max_row, column=col)
                neighbor_cell = sheet.cell(row=max_row + 1, column=col)
                if (cell.border.left.style is not None and cell.border.left.style == neighbor_cell.border.left.style) or \
                        (
                                cell.border.right.style is not None and cell.border.right.style == neighbor_cell.border.right.style):
                    max_row += 1
                    changed = True
                    break
            if not changed:
                break

        # 检查左边
        while min_col > 1:
            changed = False
            for row in range(min_row, max_row + 1):
                cell = sheet.cell(row=row, column=min_col)
                neighbor_cell = sheet.cell(row=row, column=min_col - 1)
                if (cell.border.top.style is not None and cell.border.top.style == neighbor_cell.border.top.style) or \
                        (
                                cell.border.bottom.style is not None and cell.border.bottom.style == neighbor_cell.border.bottom.style):
                    min_col -= 1
                    changed = True
                    break
            if not changed:
                break

        # 检查右边
        while max_col < sheet.max_column:
            changed = False
            for row in range(min_row, max_row + 1):
                cell = sheet.cell(row=row, column=max_col)
                neighbor_cell = sheet.cell(row=row, column=max_col + 1)
                if (cell.border.top.style is not None and cell.border.top.style == neighbor_cell.border.top.style) or \
                        (
                                cell.border.bottom.style is not None and cell.border.bottom.style == neighbor_cell.border.bottom.style):
                    max_col += 1
                    changed = True
                    break
            if not changed:
                break

        # 更新区域
        tables[i] = ((min_row, min_col), (max_row, max_col))
        _new_tables.append(((min_row, min_col), (max_row, max_col)))

    return _new_tables


def get_start_end_of_table(sheet, config_ranges=None):
    """
    自动计算sheet中所有的表格的范围和表格外的内容的范围
    :param sheet:
    :param config_ranges:
    :return: (表格的范围, 表格外的内容的范围)
    """
    if config_ranges is None:
        config_ranges = []
    t0 = time.time()
    free_cells = get_sheet_free_cells(sheet)
    t1 = time.time()
    logging.info(f'free_cells cost: {t1 - t0}')
    tables = get_sheet_ranges(sheet, free_cells)
    t2 = time.time()
    logging.info(f'get tables cost: {t2 - t1}')
    tables = expand_tables(sheet, tables)
    t3 = time.time()
    logging.info(f'expand tables cost: {t3 - t2}')
    tables = merge_ranges2_to_ranges1(tables, config_ranges)
    t4 = time.time()
    logging.info(f'merge config to tables cost: {t4 - t3}')
    tables = expand_tables(sheet, tables)
    t5 = time.time()
    logging.info(f'expand tables cost: {t5 - t4}')
    logging.info(f'{sheet.title} tables {tables}')
    return tables, free_cells


def get_range_cross_ranges(range_start_, range_end_, range_list_):
    # 将输入的 range_start_ 和 range_end_ 转换为 (min_row, min_col, max_row, max_col) 形式
    min_row, min_col = range_start_
    max_row, max_col = range_end_

    # 遍历 range_list_ 中的每一个 range
    for r in range_list_:
        # 将 range_list_ 中的每一个 range 转换为 (min_row, min_col, max_row, max_col) 形式
        (r_min_row, r_min_col), (r_max_row, r_max_col) = r

        # 判断输入的 range 是否与当前 range 有重合
        if not (max_row < r_min_row or min_row > r_max_row or
                max_col < r_min_col or min_col > r_max_col):
            return r

    # 如果没有找到与输入 range 重合的 range，返回 False
    return []


def merge_ranges2_to_ranges1(range_list_1, range_list_2):
    merged_range_list_1 = []
    for range1 in range_list_1:
        cross_range = get_range_cross_ranges(*range1, range_list_2)
        if cross_range:
            range1 = ((min(range1[0][0], cross_range[0][0]), min(range1[0][1], cross_range[0][1])),
                      (max(range1[1][0], cross_range[1][0]), max(range1[1][1], cross_range[1][1])))
        merged_range_list_1.append(range1)

    return merge_ranges_to_self(merged_range_list_1)


def pop_items_by_indices(original_list, indices):
    # 将索引列表按降序排序，以便从后往前移除项目
    indices.sort(reverse=True)
    pop_list = []
    # 遍历索引列表，并从原始列表中移除相应的项目
    for index in indices:
        if 0 <= index < len(original_list):
            pop_list.insert(0, original_list.pop(index))

    return original_list, pop_list


def merge_ranges_to_self(range_list):
    _merged_ranges = []
    for range1 in range_list:
        _mix_indexes = get_range_cross_ranges_indexes(*range1, _merged_ranges)
        if _mix_indexes:
            _merged_ranges, need_merge_ranges = pop_items_by_indices(_merged_ranges, _mix_indexes)
            need_merge_min_rows = [i[0][0] for i in need_merge_ranges]
            need_merge_min_cols = [i[0][1] for i in need_merge_ranges]
            need_merge_max_rows = [i[1][0] for i in need_merge_ranges]
            need_merge_max_cols = [i[1][1] for i in need_merge_ranges]
            _merged_ranges.append(
                ((min(range1[0][0], *need_merge_min_rows), min(range1[0][1], *need_merge_min_cols)),
                 (max(range1[1][0], *need_merge_max_rows), max(range1[1][1], *need_merge_max_cols)))
            )
        else:
            _merged_ranges.append(range1)
    return _merged_ranges


def get_range_cross_ranges_indexes(range_start_, range_end_, range_list_):
    # 将输入的 range_start_ 和 range_end_ 转换为 (min_row, min_col, max_row, max_col) 形式
    min_row, min_col = range_start_
    max_row, max_col = range_end_
    result = []
    # 遍历 range_list_ 中的每一个 range
    for idx, r in enumerate(range_list_):
        # 将 range_list_ 中的每一个 range 转换为 (min_row, min_col, max_row, max_col) 形式
        (r_min_row, r_min_col), (r_max_row, r_max_col) = r

        # 判断输入的 range 是否与当前 range 有重合
        if not (max_row < r_min_row or min_row > r_max_row or
                max_col < r_min_col or min_col > r_max_col):
            result.append(idx)

    # 如果没有找到与输入 range 重合的 range，返回 False
    return result


def load_excel_workbook(file_path, data_only=False, rich_text=False):
    """
    使用openpyxl加载excel文档,当excel文档被损坏无法加载,则先通过win32com复制一份文档
    :param file_path:
    :param data_only:
    :return:
    """

    def read_excel_with_pywin32(file_path, data_only=False, rich_text=False):
        try:
            pythoncom.CoInitialize()  # 初始化 COM 库
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False  # 设置为 True 可以在 Excel 中看到打开的文件
            excel.DisplayAlerts = False  # 禁用提示框
            output_dir = "./output"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            template_file = os.path.join(output_dir, os.path.basename(file_path))
            template_file = os.path.abspath(template_file)
            # 打开源 Excel 文件
            try:
                # 打开Excel文件
                workbook = excel.Workbooks.Open(file_path)
                if workbook.ReadOnly:
                    logging.info(f"The file {file_path} is in use and can currently only be opened in read-only mode!")
                else:
                    logging.info(f"The file {file_path} can be normally opened for reading and writing")
            except pythoncom.com_error as e:
                logging.error(f"The file {file_path}  cannot be opened normally, it may be in use. Error message: {e}")
            # 保存为新的文件
            workbook.SaveAs(template_file)
            # 关闭工作簿
            workbook.Close(SaveChanges=False)
            # 退出 Excel 应用程序
            excel.Quit()
            # 释放 COM 对象
            del excel
            workbook = openpyxl.load_workbook(template_file, data_only=data_only, rich_text=rich_text)
            try:
                os.remove(template_file)
            except OSError as e:
                logging.error(f"Error: {e.filename} - {e.strerror}.")
            return workbook
        finally:
            pythoncom.CoUninitialize()  # 清理 COM 库

    # 加载工作簿时不设置 data_only 获取完整的单元格对象
    try:
        workbook = openpyxl.load_workbook(file_path, data_only=data_only, rich_text=rich_text)
    except Exception as e:
        # 打印异常的详细信息
        logging.info(f"异常信息: {e}")
        workbook = read_excel_with_pywin32(file_path, data_only, rich_text)
    return workbook


def _get_table_cell_value(cell, sheet, cell_range=None):
    if cell_range is None:
        cell_range = []
    v = get_cell_value(cell, sheet)
    if not v and not has_all_border(cell, sheet):
        # 目标单元格的位置
        t0 = time.time()
        no_border_start_cell = get_no_border_start_cell(cell, sheet, cell_range=cell_range)
        t1 = time.time()
        # logging.info(cell, t1 - t0)
        v = get_cell_value(no_border_start_cell, sheet)
    return v


def get_cell_info(cell, sheet, in_table=False, cell_range=None, with_strike=True):
    """ 获取单元格的内容、坐标、注释等信息 """
    if cell_range is None:
        cell_range = []
    if in_table:
        v = _get_table_cell_value(cell, sheet, cell_range)
    else:
        v = get_cell_value(cell, sheet)
    cell_info = {
        "content": f"{v}" if v is not None else "",  # 单元格内容, 处理空单元格
        "index": cell.coordinate  # 单元格坐标
    }
    # 保存备注信息
    if cell.comment:
        cell_info["comment"] = cell.comment.text

    # 保存删除线信息
    if with_strike:
        if not cell.value:  # 单元格无内容
            if is_merged(cell, sheet):
                cell = get_merged_start_cell(cell.row, cell.column, sheet.merged_cells)
            elif not has_all_border(cell, sheet) or in_table:
                cell = get_no_border_start_cell(cell, sheet, cell_range)
        strike_ranges = get_strike_ranges(cell)
        if strike_ranges:
            cell_info["strike_ranges"] = strike_ranges
    return cell_info


def get_strike_ranges(cell):
    """
    :param cell: openpyxl通过sheet.cell(row, col)选择的单元格
    :return: strike_ranges: [(3, 6), (9, 12)]
    """
    strike_ranges = []
    start, end = 0, 0
    try:
        for idx, part in enumerate(cell.value):
            if isinstance(part, TextBlock):
                content_size = len(part.text)
                is_strike = part.font.strike
            else:
                content_size = len(part)
                is_strike = cell.font.strike

            if is_strike:
                end = start + content_size
                strike_ranges.append((start, end))
            start += content_size
    except:
        pass
    return strike_ranges


def get_no_border_start_cell(cell, sheet, search_directions=None, search_depth=5, cell_range=None):
    if cell_range is None:
        cell_range = []
    if search_directions is None:
        search_directions = ['top', 'left', 'right', 'bottom']
    start_cell = cell
    search_count = len(search_directions) * search_depth

    # 检查单元格是否有边框
    def has_border(cell, direction):
        if direction == 'left':
            left_cell = sheet.cell(row=cell.row, column=cell.column - 1) if cell.column > 1 else None
            return cell.border.left.style is not None or (
                    left_cell is not None and left_cell.border.right.style is not None)
        elif direction == 'right':
            right_cell = sheet.cell(row=cell.row, column=cell.column + 1)
            return cell.border.right.style is not None or right_cell.border.left.style is not None
        elif direction == 'top':
            top_cell = sheet.cell(row=cell.row - 1, column=cell.column) if cell.row > 1 else None
            return cell.border.top.style is not None or (
                    top_cell is not None and top_cell.border.bottom.style is not None)
        elif direction == 'bottom':
            bottom_cell = sheet.cell(row=cell.row + 1, column=cell.column)
            return cell.border.bottom.style is not None or bottom_cell.border.top.style is not None
        return False

    visited = set()
    queue = [(cell.row, cell.column)]

    while queue and search_count > 0:
        row, col = queue.pop(0)
        if (row, col) in visited:
            continue
        visited.add((row, col))
        cell = sheet.cell(row=row, column=col)
        if get_cell_value(cell, sheet):
            return cell
        if cell_range and not cell_in_range(cell.row, cell.column, cell_range):
            continue
        # 检查上边的单元格
        if 'top' in search_directions:
            if row > 1 and not has_border(cell, 'top'):
                queue.append((row - 1, col))

        # 检查左边的单元格
        if 'left' in search_directions:
            if col > 1 and not has_border(cell, 'left'):
                queue.append((row, col - 1))

        # 检查右边的单元格
        if 'right' in search_directions:
            if col < sheet.max_column and not has_border(cell, 'right'):
                queue.append((row, col + 1))

        # 检查下边的单元格
        if 'bottom' in search_directions:
            if row < sheet.max_row and not has_border(cell, 'bottom'):
                queue.append((row + 1, col))

        search_count -= 1

    return start_cell


def has_all_border(cell, sheet):
    left = sheet.cell(row=cell.row, column=cell.column - 1) if cell.column > 1 else None
    top = sheet.cell(row=cell.row - 1, column=cell.column) if cell.row > 1 else None
    right = sheet.cell(row=cell.row, column=cell.column + 1)
    bottom = sheet.cell(row=cell.row + 1, column=cell.column)
    return all([cell.border.left.style is not None or (not left or left.border.right.style is not None),
                cell.border.right.style is not None or right.border.left.style is not None,
                cell.border.top.style is not None or (not top or top.border.bottom.style is not None),
                cell.border.bottom.style is not None or bottom.border.top.style is not None])


def has_any_border(cell, sheet):
    left = sheet.cell(row=cell.row, column=cell.column - 1) if cell.column > 1 else None
    top = sheet.cell(row=cell.row - 1, column=cell.column) if cell.row > 1 else None
    right = sheet.cell(row=cell.row, column=cell.column + 1)
    bottom = sheet.cell(row=cell.row + 1, column=cell.column)
    return any([cell.border.left.style is not None or (not left or left.border.right.style is not None),
                cell.border.right.style is not None or right.border.left.style is not None,
                cell.border.top.style is not None or (not top or top.border.bottom.style is not None),
                cell.border.bottom.style is not None or bottom.border.top.style is not None])


def get_filename(file_path):
    # 获取文件名（包括扩展名）
    base_name = os.path.basename(file_path)
    # 去除扩展名
    name = os.path.splitext(base_name)[0]
    return name


def load_json_file(path):
    with open(path, 'r', encoding='utf-8') as f:
        data = f.read()
        # 解析 JSON 数据
        json_data = json.loads(data)
        return json_data


def is_shape_in_cell(shape):
    """
    判断图形是否完全位于单元格内（矩形且不考虑旋转）。

    :param shape: Excel Shape 对象
    :return: bool 表示图形是否完全位于单元格内
    """
    try:
        # 获取图形的左上角单元格
        top_left_cell = shape.TopLeftCell

        # 获取图形的右下角单元格
        bottom_right_cell = shape.BottomRightCell

        # 判断左上角和右下角单元格是否是同一个
        if (top_left_cell.Row == bottom_right_cell.Row and
                top_left_cell.Column == bottom_right_cell.Column):
            return True  # 图形完全位于单元格内
        else:
            return False  # 图形跨越了多个单元格
    except Exception as e:
        # 默认返回 True
        logging.error(f"Error while checking shape position: {e}")
        return True


def get_excel_shapes_win32com(excel_file, exclude_type_list=[4]):
    """
    把所有的图形转换为图片保存在本地
    图片命名规范： {sheet.Name.replace(' ', '_')}_shape_{shape.ID}.png
    exclude_type_list: 排除的类型列表
    """
    results = []
    try:
        # 初始化 COM 库
        pythoncom.CoInitialize()
        # 启动Excel应用程序
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        # 禁用只读提示
        excel.DisplayAlerts = False

        # 打开Excel文件
        try:
            # 打开Excel文件
            wb = excel.Workbooks.Open(excel_file)
            if wb.ReadOnly:
                logging.info(f"The file {excel_file} is in use and can currently only be opened in read-only mode!")
            else:
                logging.info(f"The file {excel_file} can be normally opened for reading and writing")
        except pythoncom.com_error as e:
            logging.error(f"The file {excel_file} cannot be opened normally, it may be in use. Error message: {e}")

        # 遍历所有工作表
        for sheet in wb.Sheets:
            # Sheet 隐藏或者特别隐藏
            if sheet.Visible in [0, 2]:
                continue

            # 遍历工作表中的所有形状
            for idx, shape in enumerate(sheet.Shapes):
                # type 4: 点; 1: 形状; 17:文本内容; 6: 形状的分组 13: 图片 ....
                if shape.Type in exclude_type_list:
                    logging.info(f"img save continue, shape.Name: {shape.Name}")
                    continue

                # 判断图形图像是否在单元格内
                shape_in_cell = is_shape_in_cell(shape)
                # logging.info(f"shape_info {sheet.Name} {shape.ID} {shape.Type} {shape.Width} {shape.Height} "
                #              f" {shape.Top} {shape.Left}  "
                #              f" {shape.TopLeftCell.Address} {shape.TopLeftCell.Column} {shape.TopLeftCell.Row} "
                #              f" {shape.TopLeftCell.RowHeight} {shape.TopLeftCell.ColumnWidth}"
                #              f" {shape.Text if 'Text' in shape else ''} {shape.Name} {idx} ")

                result = {"type": "shape", "sheet_name": sheet.Name,
                          "id": shape.ID, "shape_type": shape.Type, "name": shape.Name,
                          "width": round_half_up(shape.Width), "height": round_half_up(shape.Height),
                          "from_row": int(shape.TopLeftCell.Row), "from_col": int(shape.TopLeftCell.Column),
                          "top": int(shape.Top), "left": int(shape.Left),
                          "from_row_off": 0, "from_col_off": 0,
                          "to_col": 0, "to_col_off": 0, "to_row": 0, "to_row_off": 0,
                          "index": shape.TopLeftCell.Address.replace("$", ""),
                          "in_cell": shape_in_cell
                          }
                # 获取文本内容
                if shape.Type in [1, 17]:
                    try:
                        tt = shape.TextFrame2.TextRange.Text
                        result["content"] = shape.TextFrame2.TextRange.Text.strip('\r')
                        # parse_textbox_properties(shape)
                        # result["content_style"] =
                    except Exception as ex:
                        logging.info(f"not found com shape.TextFrame2 sheet_name: {result['sheet_name']}"
                                     f"id: {result['id']} name: {result['name']} "
                                     f"type: {result['shape_type']}")
                # # 将形状导出为图片
                # output_path = os.path.join(output_folder, get_shapes_image_name(sheet.Name, shape.ID))
                try:
                    if float(shape.Height) <= 0 or float(shape.Width) <= 0:
                        img = None
                    else:
                        shape.CopyPicture(Appearance=1, Format=2)  # 复制形状为图片
                        time.sleep(0.2)
                        # 从剪贴板获取图像
                        img = ImageGrab.grabclipboard()

                    msg = f"id:{result['id']} name:{result['name']} shape_type:{result['shape_type']}"
                    # 如果剪贴板中有图像，保存它
                    if isinstance(img, Image.Image):
                        # img.save(output_path)
                        # result["path"] = os.path.abspath(output_path)

                        # 计算图片的base64
                        # 假设你已经有一个 PIL.Image.Image 对象
                        # image = Image.open(output_path)
                        # 创建一个字节流对象
                        buffered = BytesIO()
                        # 将图像保存到字节流中
                        img.save(buffered, format="PNG")
                        # 获取字节流的字节数据
                        result["data"] = b64encode(buffered.getvalue()).decode()

                        # 计算图片的hash
                        img_hash = get_img_hash(img)
                        result["hash"] = img_hash[0]
                        # time.sleep(0.1)
                        # logging.info(f"Saved image: {msg}")
                    else:
                        logging.info(f"not get image from CopyPicture {msg}")
                except Exception as ex:
                    logging.error(f"img save error {ex}")
                results.append(result)
        # time.sleep(0.6)

    finally:
        # 关闭Excel文件
        wb.Close(SaveChanges=False)
        excel.Quit()
        # time.sleep(1)
        try:
            if pythoncom:
                pythoncom.CoUninitialize()
        except Exception as e:
            logging.error(e)
    return results


def round_half_up(n):
    """ 数值舍去小数部分 """
    # return int(Decimal(n).quantize(Decimal("1"), rounding=ROUND_HALF_UP))
    return int(n)


def get_img_hash(img):
    """
    计算图片的hash值
    :param img:
    :return:
    """
    # 使用 aHash 计算哈希值
    hash1 = imagehash.average_hash(img)
    return str(hash1), hash1.hash.tolist()


def format_cell_value_with_openpyxl(cell):
    """
    格式化单元格的百分比的值
    :param cell: openpyxl.cell.cell.Cell对象
    :return: str
    """
    if cell.number_format.endswith("%") and isinstance(cell.value, (float, int)):
        # 百分比数值
        decimal_places = cell.number_format.count("0") - 1
        format_value = round(cell.value * 100, 2)
        return f"%.{decimal_places}f%%" % format_value
    return cell.value


def get_cell_value(cell, sheet, from_merged_cells=True):
    """
    解析excel单元格的内容
    :param cell:  Cell对象
    :param sheet: Worksheet对象
    :param from_merged_cells: 在合并单元格中， 是否从合并单元格中第一个单元格中取值
    :return:
    """
    v = format_cell_value_with_openpyxl(cell)
    if isinstance(v, datetime):
        # v = v.isoformat()  # 将 datetime 转换为 ISO 格式字符串
        v = v.strftime('%Y/%m/%d')
    elif isinstance(v, TextBlock):
        v = v.text
    elif isinstance(v, CellRichText):
        v = str(v)
    elif v is None:
        v = ''
    if v == '' and from_merged_cells:
        v = find_in_merged_cells(cell.row, cell.column, sheet.merged_cells)
    return v


def find_in_merged_cells(row_, col_, merged_cells_):
    """
    从合并单元格中查找所属内容
    :param row_:
    :param col_:
    :param merged_cells_:
    :return:
    """
    if not merged_cells_:
        return ""
    for r in merged_cells_.ranges:
        if r.min_row <= row_ <= r.max_row and r.min_col <= col_ <= r.max_col:
            return r.start_cell.value
    return ""


def is_merged(cell, sheet):
    merged_cells_ranges = sheet.merged_cells.ranges
    return any(cell.coordinate in merged_range for merged_range in merged_cells_ranges)


def get_merged_start_cell(row_, col_, merged_cells_):
    """
    从合并单元格中查找所属内容
    :param row_:
    :param col_:
    :param merged_cells_:
    :return:
    """
    if not merged_cells_:
        return None
    for r in merged_cells_.ranges:
        if r.min_row <= row_ <= r.max_row and r.min_col <= col_ <= r.max_col:
            return r.start_cell
    return None


def format_table(table):
    """
    AI获取表格标题输入的数据
    :param table:
    :return:
    """
    result = ""
    if table.rows:
        rows = table.rows[:10] if len(table.rows) > 10 else table.rows
        for row in rows:
            cols = row.cells[:10] if len(row.cells) > 10 else row.cells
            for col in cols:
                result = result + "\"" + col.text.replace("\n", "\\n").replace("\"", "\\\"") + "\","
            result = result + "\n"
    return result


def format_ai_result(content):
    # 包含 JSON 字符串的字符串
    # 去除多余的 ```json 和 ``` 标记
    json_str = content.strip('```json').strip('```').strip()
    # 解析 JSON 数据
    data = json.loads(json_str)
    # 提取 head_index_list 数组
    return tuple(data['head_index_list'])


#
# def get_table_head_by_ai(content):
#     ai_judge_reason = PromptTemplate.from_template(
#         config.get_config('req_diff', 'find_table_head_prompt')).format(content=content)
#     # 调用大模型进行AI判定
#     try:
#         get_config_limit = config.get_config('req_diff', 'find_table_head_limit')
#         if get_config_limit <= 1:
#             res = [cur_chat.invoke(ai_judge_reason)]
#         else:
#             with ThreadPoolExecutor() as executor:
#                 futures = [executor.submit(cur_chat.invoke, ai_judge_reason) for n in range(get_config_limit)]
#                 res = [future.result() for future in futures if future.result()]
#
#         contents = [r.content for r in res]
#         logging.info(f"get_table_head_by_ai thread: {threading.current_thread().name} "
#                      f"table: {content}, results: {contents}")
#         index_list = [format_ai_result(r) for r in contents]
#         # 使用 Counter 统计每个元素出现的次数
#         count = Counter(index_list)
#
#         # 找出出现次数最多的元素
#         most_common_element, most_common_count = count.most_common(1)[0]
#         return list(most_common_element)
#     except Exception as ex:
#         logging.info(f"get_table_head_by_ai error {ex}")
#     return []


def get_table_head_by_ai(param):
    pass


def parse_table_head(table, rule=1):
    """
    自动识别表格的表头
    通过表格背景色,单元格边框信息自动识别表头
    :param table: TableObject对象
    :param rule: 0: AI识别表头;  1: 背景颜色; 2: 下边框的样式
    """
    if not (table and table.rows):
        return

    _BLACK = "#" + BLACK[2:]
    _HEAD_BORDER_STYLE = ['medium', 'double']  # 粗线,双横线

    def check_horizontal_head(condition, only_condition=True):
        """
        水平表格的表头提取
        :param condition:
        :param only_condition: True, 只计算满足条件的数据作为表头; False, 自动识别到最大值之前所有的都算做表头
        :return:
        """
        for i in range(len(table.rows)):
            cells_ = table.rows[i].cells
            if all(condition(cells_[j]) for j in range(len(cells_))):
                table.head_list.append(i)
        if table.head_list and not only_condition:
            # 自动识别到最大值之前所有的数据都算做表头
            last_index = table.head_list[-1]
            table.head_list = [x for x in range(last_index + 1)]
        # 修正处理: 如果所有行都被标记为表头, 那么相当于没能正常识别出表头
        if len(table.head_list) == len(table.rows):
            logging.info("check_table_head All rows are marked as headers, is invalid")
            table.head_list = []
        if table.head_list and table.head_list[-1] == len(table.rows) - 1:
            table.head_list.pop()
        if table.head_list and table.head_list[0] != 0:
            table.head_list = []
        if table.head_list:
            for i in range(len(table.head_list)):
                if i > 0 and (table.head_list[i - 1] + 1) != table.head_list[i]:
                    table.head_list = table.head_list[:i]
                    break
            table.head_type = "horizontal"
            return True
        return False

    def check_vertical_head(condition, only_condition=True):
        """
        垂直表格的表头提取
        :param condition:
        :param only_condition: True, 只计算满足条件的数据作为表头; False, 自动识别到最大值之前所有的都算做表头
        :return:
        """
        col_len = len(table.rows[0].cells)
        for i in range(col_len):
            # 当前列中所有行都有背景色，那么该列识别为表头
            if all(condition(row.cells[i]) for row in table.rows):
                table.head_list.append(i)
        if table.head_list and not only_condition:
            # 自动识别到最大值之前所有的数据都算做表头
            last_index = table.head_list[-1]
            table.head_list = [x for x in range(last_index + 1)]
        # 修正处理: 如果所有列都被标记为表头, 那么相当于没能正常识别出表头
        if len(table.head_list) == col_len:
            logging.info("check_table_head, All columns are marked as headers, is invalid")
            table.head_list = []
        if table.head_list and table.head_list[-1] == col_len - 1:
            table.head_list.pop()
        if table.head_list and table.head_list[0] != 0:
            table.head_list = []
        if table.head_list:
            for i in range(len(table.head_list)):
                if i > 0 and (table.head_list[i - 1] + 1) != table.head_list[i]:
                    table.head_list = table.head_list[:i]
                    break
            table.head_type = "vertical"
            return True
        return False

    # 0. 使用AI识别表头
    if rule == 0:
        index_list = get_table_head_by_ai(format_table(table))
        table.head_type = "horizontal"
        table.head_list = index_list
        return
    elif type == 1 and check_horizontal_head(lambda c: c.style.background_color not in [_BLACK, '']):
        return
    elif type == 2 and check_horizontal_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE, True):
        return

    # # 1. 通过背景颜色识别水平表的表头
    # if check_horizontal_head(lambda c: c.style.background_color not in [_BLACK, '']):
    #     return
    # # 2. 通过表格的下边框加粗或者双横线识别水平表格的表头
    # if check_horizontal_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE, True):
    #     return
    # # 3. 通过背景颜色识别垂直表的表头
    # if check_vertical_head(lambda c: c.style.background_color not in [_BLACK, '']):
    #     return
    # # 4. 通过表格的右边框加粗或者双横线识别垂直表格的表头
    # if check_vertical_head(lambda c: c.border.border_bottom.border_style in _HEAD_BORDER_STYLE, True):
    #     return


def save_json_to_file(json_data, output_file):
    # Ensure the log directory exists
    log_dir = os.path.dirname(output_file)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=1, ensure_ascii=False)
    logging.info(f"save_json_to_file output_file: {output_file}")
    return os.path.abspath(output_file)


settings_file_path = "d:/work/kotei_omp/output/settings.json"


def write_head_mark_list_to_settings(value: List):
    """
    保存表头的列名称
    :param value:
    :return:
    """
    data = red_settings_json()
    old_ = data.get("head_mark_list", [])
    # 检查 array 中是否已经存在相同的元素
    if value not in old_:
        old_.append(value)
        data["head_mark_list"] = old_
        save_json_to_file(data, settings_file_path)


def write_chapter_mark_to_settings(value: str):
    """ 将word文本的章节号匹配规则写入文件 """
    data = red_settings_json()
    old_ = data.get("chapter_mark_list", [])
    # 检查 array 中是否已经存在相同的元素
    if value not in old_:
        old_.append(value)
        data["chapter_mark_list"] = old_
        save_json_to_file(data, settings_file_path)


def red_settings_json():
    if not os.path.exists(settings_file_path):
        return {}
    with open(settings_file_path, 'r', encoding='utf-8') as f:
        data_dict = json.load(f)
        return data_dict


def clean_tgt_process():
    """ 清除旧的office进程 """
    tgt_process = [
        "RuntimeBroker.exe",
        "wps.exe",
        "wpscloudsvr.exe"
    ]
    # 遍历所有的进程
    cur_ts = time.time()
    for proc in psutil.process_iter():
        try:
            if re.findall(r"(?:RuntimeBroker|wps|wpscloudsvr)\.exe", proc.name()) and \
                    proc.create_time() < cur_ts - 5:
                proc.terminate()
                logging.info("old office processes has been killed.")
        except psutil.NoSuchProcess:
            continue

def shape_to_object(shape, obj):
    """
    shape json对象转换为对象模型
    :param shape:
    :param obj:
    :return:
    """
    obj._id = shape["id"]
    obj._name = shape["name"]
    obj._width = shape["width"]
    obj._height = shape["height"]
    obj.px_width = obj._width
    obj.px_height = obj._height
    obj._in_cell = shape.get("in_cell", True)
    obj._data = shape.get("data", "")
    obj._digest = shape.get("hash", "")
    obj.coordinate.desc = shape["index"]
    obj.coordinate.top = str(shape["top"])  # 坐标:距离顶边距离
    obj.coordinate.left = str(shape["left"])  # 坐标:距离左边距离