"""
新变表更新工作流
"""
from fastapi import APIRouter
from loguru import logger
from sdw_agent.model.response_model import NewChangeListUpdateRequest, NewChangeListUpdateResponse
from sdw_agent.service.newchangelist_service.workflow_ncl_update import do_ncl_update


# 路由配置
router = APIRouter(prefix="/api/sdw/newchangelist", tags=["新变表工作流"])


@router.post("/update",
             summary="新变表更新",
             description="基于新变表更新工作流生成差分成果物AI版，开发确认需求与代码照合",
             response_description="",
          response_model=NewChangeListUpdateResponse)
async def parse_guideline_file(request: NewChangeListUpdateRequest):
    try:
        file_path = request.input_path
        if not file_path or not file_path.strip():
            return NewChangeListUpdateResponse(
                code=1,
                msg="失败：文件路径不能为空",
                data=""
            )
        
        output_path = do_ncl_update(file_path)
        return NewChangeListUpdateResponse(
            code=0,
            msg="新变表更新成功",
            data=output_path
        )
    except Exception as e:
        logger.error(f"代码差分生成失败: {str(e)}")
        return NewChangeListUpdateResponse(
            code=1,
            msg=f"新变表更新失败: {str(e)}",
            data=""
        )
