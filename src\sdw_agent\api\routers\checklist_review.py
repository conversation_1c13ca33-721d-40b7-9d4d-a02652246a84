#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : bug_commit.py
@Time    : 2025/7/15 20:28
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : 根据变更点筛选检查项，匹配测试用例，填写表单。
"""

from fastapi import APIRouter
from loguru import logger
from sdw_agent.service.checklist.checklist_review import ChecklistReviewWorkflow
from sdw_agent.model.request_model import ChecklistReviewRequest
from sdw_agent.model.response_model import ChecklistReviewResponse

router = APIRouter(prefix="/api/sdw", tags=["Test-CheckList评审"])

@router.post("/checklist_review",
             summary="CheckList评审",
             description="根据变更点筛选检查项，匹配测试用例，填写表单",
             response_description="",
             response_model=ChecklistReviewResponse)
async def checklist_review(request: ChecklistReviewRequest):
    changepoint_file_path, testcase_file_path, checklist_file_path = request.changepoint_file_path, request.testcase_file_path, request.checklist_file_path
    reviewer = ChecklistReviewWorkflow()
    try:
        save_path = await reviewer.execute(changepoint_file_path=changepoint_file_path, testcase_file_path=testcase_file_path, checklist_file_path=checklist_file_path)
        return {'code': 200, 'message': '生成checklist文件成功！', 'data': save_path}
    except Exception as e:
        logger.error(e)
        return {'code': 500, 'message': '生成checklist文件失败！', 'data': ""}
