"""
通信故障安全CS API

V字对应：
2.1 基本設計 通信故障安全 CS

模块简介和主要功能说明：
提供通信故障安全CS工作流的REST API接口，支持从变更代码中匹配CAN信号和控制信号。
使用重构后的工作流实现，提供更好的性能和可维护性。

主要功能：
1. 从变更代码中匹配CAN信号
2. 支持BitAssign和SPI JSON两种数据源
3. 支持本地仓库和Gerrit两种代码获取方式
4. 提供仓库克隆功能
"""

import traceback
from typing import Optional, Dict, Any

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field, field_validator
from loguru import logger

from sdw_agent.model.request_model import CloneRepoRequest, SourceInfo, RepoInfo, SPIInfo
from sdw_agent.service.communication_fault_cs import (
    CommunicationFaultCSWorkflow,
    InputDataModel,
    SourceType
)
from sdw_agent.service import WorkflowStatus
from sdw_agent.util.git_util import clone_repo_of_branch


class CommunicateCSRequest(BaseModel):
    """通信故障安全CS请求模型"""
    keySource: Optional[SourceInfo] = Field(None, description="BitAssign文件信息")
    spi: Optional[SPIInfo] = Field(None, description="SPI JSON文件信息")
    repo: RepoInfo = Field(..., description="仓库信息")
    case_sensitive: bool = Field(True, description="是否区分大小写")

    @field_validator('keySource', 'spi', mode='before')
    @classmethod
    def validate_source(cls, v, info):
        """验证至少有一个数据源"""
        values = info.data
        if 'keySource' in values and values['keySource'] is None and 'spi' in values and values['spi'] is None:
            raise ValueError('BitAssign 和 SpiJson 至少有一个不能为空')
        return v

class CommunicateCSResponse(BaseModel):
    """通信故障安全CS响应模型"""
    code: int = Field(0, description="状态码，0表示成功，其他表示失败")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")

class CommunicateCSResult(BaseModel):
    """通信故障安全CS结果模型"""
    output_file_path: str = Field(None, description="输出文件路径")
    total_files_processed: int = Field(..., description="处理的文件总数")
    total_matches_found: int = Field(..., description="找到的匹配总数")
    can_matches_count: int = Field(..., description="CAN信号匹配数量")
    ctrl_matches_count: Optional[int] = Field(None, description="控制信号匹配数量")


router = APIRouter(prefix="/api/sdw/communication", tags=["通信故障安全CS工作流"])

@router.post("/match_can",
             summary="从变更代码中匹配CAN信号",
             description="使用重构后的通信故障安全CS工作流，从变更代码中匹配CAN信号和控制信号",
             response_description="匹配结果，包含输出文件路径和匹配统计信息",
             response_model=CommunicateCSResponse)
async def match_can_from_code(request: CommunicateCSRequest):
    """
    从变更代码中匹配CAN信号和控制信号

    使用重构后的通信故障安全CS工作流，支持BitAssign和SPI JSON两种数据源。
    可以从本地仓库或Gerrit获取代码变更信息，并在变更中搜索匹配的信号。

    Args:
        request: 请求参数，包含数据源信息和仓库信息

    Returns:
        匹配结果，包含输出文件路径和匹配统计信息
    """
    logger.info(f"接收到通信故障安全CS匹配请求: {request}")

    try:


        # 确定数据源类型和路径
        source_type = None
        bit_assign_path = None
        can_json_path = None
        ctrl_json_path = None

        if request.spi:  # 优先使用SPI JSON
            source_type = SourceType.SPI_JSON
            can_json_path = request.spi.can_path.uri
            ctrl_json_path = request.spi.ctrl_path.uri
            logger.info(f"使用SPI JSON数据源: CAN={can_json_path}, CTRL={ctrl_json_path}")
        elif request.keySource:
            source_type = SourceType.BIT_ASSIGN
            bit_assign_path = request.keySource.uri
            logger.info(f"使用BitAssign数据源: {bit_assign_path}")
        else:
            raise ValueError("BitAssign和SPI JSON至少需要提供一种数据源")

        # 准备输入数据
        input_data = InputDataModel(
            source_type=source_type,
            commit_id=request.repo.commit_id,
            compared_commit_id=request.repo.compared_commit_id if hasattr(request.repo, "compared_commit_id") and request.repo.compared_commit_id != "" else None,
            repository_path=request.repo.repo_path,
            bit_assign_path=bit_assign_path,
            can_json_path=can_json_path,
            ctrl_json_path=ctrl_json_path,
            case_sensitive=request.case_sensitive
        )

        # 执行工作流
        logger.info("开始执行通信故障安全CS工作流")
        # 创建工作流实例
        workflow = CommunicationFaultCSWorkflow(input_data.source_type)
        result = workflow.run(input_data)

        # 处理结果
        if result.status == WorkflowStatus.SUCCESS:
            output_data = result.data

            # 构建响应数据
            response_data = CommunicateCSResult(
                output_file_path=output_data.get("output_file_path", ""),
                total_files_processed=output_data.get("total_files_processed", 0),
                total_matches_found=output_data.get("total_matches_found", 0),
                can_matches_count=len(output_data.get("can_matches", [])),
                ctrl_matches_count=len(output_data.get("ctrl_matches", [])) if output_data.get("ctrl_matches") else None
            )

            logger.success("通信故障安全CS工作流执行成功")

            return {
                "code": 0,
                "msg": "CAN信号匹配成功",
                "data": {
                    "keyList": output_data.get("output_file_path", ""),
                    "result": response_data.model_dump()
                }
            }
        else:
            logger.error(f"通信故障安全CS工作流执行失败: {result.error}")
            return {
                "code": 500,
                "msg": f"{result.message}",
                "data": {}
            }
    except Exception as e:
        logger.exception(f"通信故障安全CS匹配过程中发生异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/clone_repo",
             summary="克隆远程仓库到本地",
             description="",
             response_description="",
             response_model=CommunicateCSResponse)
async def clone_repo(request: CloneRepoRequest):
    """获取要件一览表"""
    try:
        repo_url = request.repo_url
        username = request.username
        password = request.password
        branch = request.branch
        repo_local_path = request.repo_local_path
        clone_repo_of_branch(repo_url, branch, repo_local_path, username, password)

        return {
            "code": 0,
            "msg": "仓库克隆成功",
            "data": {
                "keyList": 'OK'
            }
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))