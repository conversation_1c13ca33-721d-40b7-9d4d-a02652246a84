"""
IF整合性确认工作流数据模型

定义工作流使用的数据模型和验证模式
"""

from typing import List, Dict, Any, Optional
from enum import Enum
from pathlib import Path

from pydantic import BaseModel, Field, validator


class IFIntegrationConfigModel(BaseModel):
    """IF整合性确认配置模型"""

    # 基本配置
    name: str = Field(default="IF整合性确认")
    description: str = Field(default="检查代码中接口使用与IF设计书的一致性")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")

    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)

    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)

    # 安全配置
    security: Dict[str, Any] = Field(default_factory=dict)

    # LLM配置
    llm: Dict[str, Any] = Field(default_factory=dict)

    # 日志配置
    logging: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        extra = "allow"


class IntegrationStatus(str, Enum):
    """接口集成状态枚举"""
    OK = "OK"
    NG = "NG"
    PENDING = "Pending"


class IFStylebookInfo(BaseModel):
    """IF设计书信息模型"""
    uri: str
    type: str = "file"  # 默认为文件类型

    @validator('uri')
    def validate_uri(cls, v):
        path = Path(v)
        if not path.exists():
            raise ValueError(f"IF设计书文件不存在: {v}")
        return v


class CloneRepoInfo(BaseModel):
    """克隆代码仓库信息模型"""
    repo_url: str
    branch: str
    username: Optional[str] = None
    password: Optional[str] = None

    @validator('repo_url')
    def validate_repo_url(cls, v):
        if not v.startswith(('http://', 'https://', 'git://', 'ssh://')):
            raise ValueError(f"不支持的仓库URL格式: {v}")
        return v


class IfIntegrationRequest(BaseModel):
    ifStylebook: IFStylebookInfo
    cloneRepoInfo: CloneRepoInfo


class IFMatch(BaseModel):
    """接口匹配信息模型"""
    matched_pattern: str
    line_num: int
    line: str
    file_path: str


class IFStylebookEntry(BaseModel):
    """IF设计书条目模型"""
    name: str
    return_type: Optional[str] = None
    parameters: List[Dict[str, str]] = Field(default_factory=list)
    constraints: Optional[str] = None
    description: Optional[str] = None


class ConfirmResult(BaseModel):
    """LLM确认结果模型"""
    confirm_result: str = Field(description="确认结果")

    @validator('confirm_result')
    def validate_confirm_result(cls, v, values, **kwargs):
        valid_results = ["OK", "NG", "Pending"]
        if v not in valid_results:
            return "Pending"  # 默认返回Pending
        return v


class IntegrationResult(BaseModel):
    """集成结果模型"""
    index: int
    if_name: str
    code_location: str
    code_content: str
    confirm_result: IntegrationStatus
    if_stylebook_name: str
    if_version: str = "-"
    confirm_date: str = "-"
    confirmed_by: str = "DEV AGENT"
    remarks: str = ""
    status: str = "-"


class ProcessingResult(BaseModel):
    """处理结果模型"""
    integration_data: Dict[str, List[List[str]]]
    output_file: str
