import pathlib
import traceback
from typing import Dict, Any, List
import pandas as pd

from loguru import logger

from sdw_agent.util.excel.core import ExcelUtil, CellStyle


class CommunicationFaultExcelUtil(ExcelUtil):
    """
    通信故障CS专用Excel工具类，继承自ExcelUtil

    提供与现有逻辑相同的表头样式设置和列宽自适应功能：
    - 表头：Meiryo UI字体、11号、加粗、左对齐、浅橙色背景
    - 数据：Meiryo UI字体、10号
    - 自动调整列宽
    """

    def __init__(self, config, file_path: str, engine: str = "openpyxl", auto_create: bool = True):
        """
        初始化通信故障CS Excel工具

        Args:
            file_path: Excel文件路径
            engine: 操作引擎类型，默认使用openpyxl
            auto_create: 如果文件不存在是否自动创建
        """
        super().__init__(file_path, engine, auto_create)
        self.config = config
        logger.info(f"初始化通信故障CS Excel工具 - 文件: {file_path}")

    def _ensure_sheet_exists(self, sheet_name: str) -> None:
        """
        确保指定的工作表存在，如果不存在则创建

        Args:
            sheet_name: 工作表名称
        """
        try:
            # 获取现有工作表列表
            existing_sheets = self.get_sheet_names()

            if sheet_name not in existing_sheets:
                logger.info(f"工作表 '{sheet_name}' 不存在，正在创建...")

                # 使用openpyxl引擎创建新工作表
                if hasattr(self.engine, 'workbook'):
                    # 创建新工作表
                    self.engine.workbook.create_sheet(title=sheet_name)
                    logger.info(f"已创建工作表 '{sheet_name}'")
                else:
                    logger.warning(f"无法创建工作表 '{sheet_name}'，引擎不支持")
            else:
                logger.debug(f"工作表 '{sheet_name}' 已存在")

        except Exception as e:
            logger.error(f"确保工作表存在时出错: {str(e)}")
            # 不抛出异常，让后续流程继续

    def _remove_default_empty_sheets(self) -> None:
        """
        移除默认的空工作表（如'Sheet', 'Sheet1'等）
        """
        try:
            if not hasattr(self.engine, 'workbook'):
                return

            workbook = self.engine.workbook
            sheets_to_remove = []

            # 检查所有工作表
            for sheet in workbook.worksheets:
                sheet_name = sheet.title

                # 检查是否为默认的空工作表
                if self._is_default_empty_sheet(sheet, sheet_name):
                    sheets_to_remove.append(sheet_name)

            # 移除空工作表（但至少保留一个工作表）
            for sheet_name in sheets_to_remove:
                if len(workbook.worksheets) > 1:  # 确保至少保留一个工作表
                    workbook.remove(workbook[sheet_name])
                    logger.info(f"已移除默认空工作表: '{sheet_name}'")
                else:
                    logger.debug(f"保留工作表 '{sheet_name}' 以确保文件至少有一个工作表")

        except Exception as e:
            logger.warning(f"移除默认空工作表时出错: {str(e)}")

    def _is_default_empty_sheet(self, sheet, sheet_name: str) -> bool:
        """
        判断是否为默认的空工作表

        Args:
            sheet: openpyxl工作表对象
            sheet_name: 工作表名称

        Returns:
            bool: 是否为默认空工作表
        """
        try:
            # 检查工作表名称是否为默认名称
            default_names = ['Sheet', 'Sheet1', 'Worksheet', 'Worksheet1']
            if sheet_name not in default_names:
                return False

            # 检查工作表是否为空（没有数据）
            if sheet.max_row == 1 and sheet.max_column == 1:
                # 检查唯一的单元格是否为空
                cell_value = sheet.cell(row=1, column=1).value
                if cell_value is None or str(cell_value).strip() == '':
                    return True

            return False

        except Exception as e:
            logger.warning(f"检查空工作表时出错: {str(e)}")
            return False

    def _filter_excluded_fields(self, df: pd.DataFrame, excluded_fields: List[str] = None) -> pd.DataFrame:
        """
        过滤掉DataFrame中的排除字段

        Args:
            df: 原始DataFrame
            excluded_fields: 要排除的字段列表

        Returns:
            pd.DataFrame: 过滤后的DataFrame
        """
        if excluded_fields is None:
            excluded_fields = []

        try:
            # 获取需要保留的列
            columns_to_keep = [col for col in df.columns if col not in excluded_fields]

            if not columns_to_keep:
                logger.warning("所有列都被排除，返回空DataFrame")
                return pd.DataFrame()

            # 过滤DataFrame
            filtered_df = df[columns_to_keep].copy()

            if excluded_fields:
                logger.info(f"已排除字段: {excluded_fields}")
                logger.info(f"保留字段: {columns_to_keep}")

            return filtered_df

        except Exception as e:
            logger.error(f"过滤排除字段时出错: {str(e)}")
            return df  # 出错时返回原始DataFrame

    def save_dataframe_with_style(self, df: pd.DataFrame, excluded_fields, sheet_name: str = "Sheet1",
                                  start_row: int = 1, start_col: int = 1,
                                  include_header: bool = True) -> None:
        """
        保存DataFrame到Excel，应用与现有逻辑相同的样式

        Args:
            df: 要保存的DataFrame
            excluded_fields: 不需要展示在成果物中的列
            sheet_name: 工作表名称
            start_row: 起始行号（从1开始）
            start_col: 起始列号（从1开始）
            include_header: 是否包含表头
        """
        try:
            logger.info(f"开始保存DataFrame到工作表 '{sheet_name}'，数据行数: {len(df)}")

            # 确保工作表存在
            self._ensure_sheet_exists(sheet_name)

            # 处理排除字段
            filtered_df = self._filter_excluded_fields(df, excluded_fields)

            # 写入数据
            self.write_dataframe(sheet_name, filtered_df.iloc[:, :-1], start_row, start_col, include_header)

            if include_header:
                # 设置表头样式 - 与现有逻辑保持一致
                header_style = CellStyle(
                    font_name="Meiryo UI",
                    font_size=11,
                    font_bold=True,
                    font_color="000000",  # 黑色字体
                    bg_color="F8CBAD",    # 浅橙色背景 (FFF8CBAD去掉FF前缀)
                    alignment_horizontal="left",
                    alignment_vertical="center"
                )

                # 应用表头样式
                for col_idx in range(len(filtered_df.columns)-1):
                    self.set_cell_style(sheet_name, start_row, start_col + col_idx, header_style)

                logger.info(f"已设置表头样式，列数: {len(df.columns)}")

            # 设置数据行样式
            data_style = CellStyle(
                font_name="Meiryo UI",
                font_size=10,
                font_color="000000"
            )

            # 设置超链接样式
            hyper_style = CellStyle(
                font_name="Meiryo UI",
                font_size=10,
                font_color="0000FF",  # 蓝色
                font_italic=True, # 斜体
                font_underline='single'   # 添加下划线
            )

            columns = filtered_df.columns.tolist()
            line_num_idx = columns.index("line_number") if "line_number" in columns else -1
            code_url_idx = columns.index("text_file_link") if "text_file_link" in columns else -1

            data_start_row = start_row + (1 if include_header else 0)
            for row_idx in range(len(filtered_df)):
                for col_idx in range(len(filtered_df.columns)):
                    # 为行号设置超链接，实现点击跳转到代码行
                    if col_idx == code_url_idx and line_num_idx != -1 and code_url_idx != -1:
                        # 从原始DataFrame获取超链接数据
                        original_row = df.iloc[row_idx]
                        self.write_hyperlink(
                            sheet_name,
                            data_start_row + row_idx,
                            start_col + line_num_idx,
                            str(original_row["text_file_link"]),
                            str(original_row["line_number"]))
                        # 设置超链接样式
                        self.set_cell_style(
                            sheet_name,
                            data_start_row + row_idx,
                            start_col + line_num_idx,
                            hyper_style
                        )
                    else:
                        self.set_cell_style(
                            sheet_name,
                            data_start_row + row_idx,
                            start_col + col_idx,
                            data_style
                        )

            logger.info(f"已设置数据样式，数据行数: {len(filtered_df)}")

            # 自动调整列宽 - 与现有逻辑保持一致
            self._auto_fit_columns_with_limits(sheet_name, start_col, len(filtered_df.columns))

            # 移除默认的空工作表
            self._remove_default_empty_sheets()

            logger.info(f"DataFrame保存完成，工作表: {sheet_name}")

        except Exception as e:
            traceback.print_exc()
            logger.error(f"保存DataFrame失败: {str(e)}")
            raise

    def _auto_fit_columns_with_limits(self, sheet_name: str, start_col: int, col_count: int) -> None:
        """
        自动调整列宽，设置最小宽度和最大宽度限制（专为openpyxl引擎优化）

        Args:
            sheet_name: 工作表名称
            start_col: 起始列号
            col_count: 列数
        """
        try:
            from openpyxl.utils import get_column_letter

            # 获取工作表
            worksheet = self.engine.workbook[sheet_name]

            # 遍历每一列进行宽度调整
            for i in range(col_count):
                col_num = start_col + i
                col_letter = get_column_letter(col_num)

                # 计算列的最大内容长度
                max_length = 0
                for row in range(1, worksheet.max_row + 1):
                    cell = worksheet.cell(row=row, column=col_num)
                    if cell.value is not None:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length

                # 设置列宽：最小12，最大50，内容长度+2的缓冲（与现有逻辑保持一致）
                adjusted_width = max(min(max_length + 2, 50), 12)
                worksheet.column_dimensions[col_letter].width = adjusted_width

            logger.info(f"已自动调整列宽，列数: {col_count}")

        except Exception as e:
            logger.warning(f"自动调整列宽失败: {str(e)}")

    def save_multiple_dataframes(self, dataframes: Dict[str, pd.DataFrame], source_type) -> None:
        """
        保存多个DataFrame到不同的工作表

        Args:
            dataframes: 字典，键为工作表名称，值为DataFrame

        注意：
        - 每个工作表会自动创建（如果不存在）
        - 空的DataFrame会被跳过
        - 默认的空工作表会被移除
        """
        try:
            logger.info(f"开始保存多个DataFrame，工作表数量: {len(dataframes)}")

            # 过滤掉空的DataFrame
            valid_dataframes = {
                sheet_name: df for sheet_name, df in dataframes.items()
                if df is not None and not df.empty
            }

            if not valid_dataframes:
                logger.warning("没有有效的DataFrame需要保存")
                return

            # 保存每个DataFrame到对应的工作表
            for sheet_name, df in valid_dataframes.items():
                logger.info(f"保存工作表 '{sheet_name}'，数据行数: {len(df)}")
                excluded_fields = []
                if source_type=='bit_assign':
                    excluded_fields = self.config.get('bit_assign', {}).get('excluded_files',[])
                elif sheet_name=='spi_json':
                    excluded_fields = self.config.get('spi_json', {}).get('excluded_files',[])

                # 保存DataFrame（会自动创建工作表）
                self.save_dataframe_with_style(df, excluded_fields, sheet_name)
                logger.info(f"已保存工作表 '{sheet_name}'，数据行数: {len(df)}")

            # 移除默认的空工作表
            self._remove_default_empty_sheets()

            logger.info("所有DataFrame保存完成")

        except Exception as e:
            logger.error(f"保存多个DataFrame失败: {str(e)}")
            raise


def get_column_index(headers: List[str], column_name: str) -> int:
        """
        获取列索引

        Args:
            headers: 表头列表
            column_name: 列名

        Returns:
            int: 列索引

        Raises:
            ValueError: 当列不存在时
        """
        try:
            return headers.index(column_name)
        except ValueError:
            raise ValueError(f"BitAssign文件中没有找到列: {column_name}")


def extract_added_lines_from_gerrit(file_diffs: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    从Gerrit差异中提取新增的代码行信息

    Args:
        file_diffs: Gerrit文件差异信息

    Returns:
        List[Dict[str, Any]]: 新增代码行信息列表
    """
    if file_diffs is None:
        raise ValueError("变更内容为空")

    added_lines_info = []
    for file_path, diff_info in file_diffs.items():
        added_line_nums = diff_info.get("changed_lines", {}).get("added", [])
        diff = diff_info.get("diff", {})
        added_lines = get_added_lines_from_diff(diff, added_line_nums)

        added_lines_info.append({
            "file_name": file_path.split('/')[-1],
            "file_path": file_path,
            "change_id": diff_info.get("change_id"),
            "commit_id": diff_info.get("commit_id"),
            "added_line_num": added_line_nums,
            "added_lines": added_lines
        })

    return added_lines_info

def extract_added_lines_from_local(repo_path:str, file_diffs: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    从本地Git差异中提取新增的代码行信息

    Args:
        file_diffs: 本地文件差异信息
        repo_path: 仓库路径
    Returns:
        List[Dict[str, Any]]: 新增代码行信息列表
    """
    if file_diffs is None:
        raise ValueError("变更内容为空")

    added_lines_info = []
    for file_path, diff_info in file_diffs.items():
        added_line_nums = diff_info.get("changed_lines", {}).get("added", [])

        # 提取新增的代码内容
        added_lines = []
        for item in diff_info.get("diff", []):
            if item['type'] == 'b':
                added_lines.extend(item['content'])

        added_lines_info.append({
            "file_name": file_path.split('/')[-1],
            "file_path": pathlib.Path(repo_path).as_posix()+'/'+file_path,
            "change_id": '',
            "commit_id": diff_info.get("commit_id"),
            "added_line_num": added_line_nums,
            "added_lines": added_lines
        })

    return added_lines_info

def get_added_lines_from_diff(diff: Dict[str, Any], added_line_nums: List[int]) -> List[str]:
    """
    从差异中提取新增的代码行内容

    Args:
        diff: 差异信息
        added_line_nums: 新增行号列表

    Returns:
        List[str]: 新增代码行内容列表
    """
    added_lines = []
    new_line = 0
    added_line_set = set(added_line_nums)

    for chunk in diff.get("content", []):
        if "ab" in chunk:
            new_line += len(chunk["ab"])
        elif "b" in chunk:
            lines = chunk["b"]
            for line in lines:
                new_line += 1
                if new_line in added_line_set:
                    added_lines.append(line)

    return added_lines