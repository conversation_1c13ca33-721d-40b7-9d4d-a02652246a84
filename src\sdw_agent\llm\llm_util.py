import time
from typing import Optional, Any, Dict

from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel

from sdw_agent.config.env import ENV
from sdw_agent.llm import gpt4o_api, dnkt_model, llama_model
from loguru import logger

# 定义默认模型常量
DEFAULT_MODEL = gpt4o_api

# 模型名称到模型实例的映射（可维护性提升）
MODEL_MAP = {
    'gpt-4o': gpt4o_api,
    'qwen': dnkt_model,  # 注意：此处保留原实现，但建议确认是否应为 qwen_model
    'llama': llama_model,
}


def get_model() -> ChatOpenAI:
    """
    根据环境变量中的模型名称获取对应的模型实例。

    Returns:
        model: 返回根据环境变量中配置的模型名称对应的模型实例。
               如果模型名称不在已知的模型中，默认返回gpt4o_api模型实例。
    """
    # 获取环境变量中配置的模型名称，设置默认值防止属性错误
    model_name = getattr(ENV, 'config', None)
    if model_name is not None:
        model_name = getattr(model_name, 'llm_model', None)

    # 使用字典查找模型，未找到则使用默认模型
    llm_model = MODEL_MAP.get(model_name, DEFAULT_MODEL)
    logger.info(f"使用模型: {llm_model.model_name}")
    return llm_model


def get_ai_message(template: ChatPromptTemplate, invoke_data: dict,
                   llm_model: Optional[ChatOpenAI] = None, auto_parse=False) -> dict | None | Any:
    """
    根据给定的模板和数据生成AI消息。

    :param template: 用于构建聊天提示的模板。
    :param invoke_data: 包含要填充到模板中的数据的字典。
    :param llm_model: 可选参数，用于生成消息的语言模型。如果未提供，则调用get_model()获取默认模型。
    :return: 由语言模型生成的BaseMessage对象。
    :raises RuntimeError: 如果模型初始化失败或链式调用失败。
    """
    try:
        # 初始化语言模型，如果未提供llm_model，则调用get_model()获取默认模型
        llm = llm_model or get_model()
        if not isinstance(template, ChatPromptTemplate):
            raise TypeError("template 必须是 ChatPromptTemplate 类型")
        if not isinstance(invoke_data, dict):
            raise TypeError("invoke_data 必须是 dict 类型")

        # 将模板与语言模型结合，形成一个处理链
        chain = template | llm
        if auto_parse:
            chain = chain | StrOutputParser()
        # 使用处理链和调用数据生成最终的AI消息
        for attempt in range(5):
            try:
                return chain.invoke(invoke_data)
            except Exception as e:
                if attempt == 4:  # 最后一次尝试
                    raise RuntimeError(f"生成AI消息失败: {e}") from e
                logger.warning(f"第{attempt + 1}次尝试失败，准备重试: {str(e)}")
                time.sleep(2 ** attempt)  # 指数退避
    except Exception as e:
        logger.error(e)
        # 可根据实际情况替换为更具体的异常处理
        raise RuntimeError(f"生成AI消息失败: {e}") from e


def get_ai_message_with_structured_output(
        template: ChatPromptTemplate,
        invoke_data: Dict[str, Any],
        base_model,
        llm_model: Optional[ChatOpenAI] = None
) -> dict | None | Any:
    """
    根据给定的模板和数据生成AI消息。

    :param template: 用于构建聊天提示的模板。
    :param invoke_data: 包含要填充到模板中的数据的字典。
    :param base_model: 用于结构化输出的Pydantic模型类。
    :param llm_model: 可选参数，用于生成消息的语言模型。如果未提供，则调用get_model()获取默认模型。
    :return: 由语言模型生成的BaseMessage对象。
    :raises RuntimeError: 如果模型初始化失败或链式调用失败。
    :raises TypeError: 如果参数类型不正确。
    """
    try:
        # 初始化语言模型，如果未提供llm_model，则调用get_model()获取默认模型
        llm = llm_model or get_model()

        # 参数类型检查
        if not isinstance(template, ChatPromptTemplate):
            raise TypeError("template 必须是 ChatPromptTemplate 类型")
        if not isinstance(invoke_data, dict):
            raise TypeError("invoke_data 必须是 dict 类型")
        if not (isinstance(base_model, type) and issubclass(base_model, BaseModel)):
            raise TypeError("base_model 必须是 pydantic.BaseModel 的子类")

        # 将模板与语言模型结合，形成一个处理链
        chain = template | llm.with_structured_output(base_model)
        # 使用处理链和调用数据生成最终的AI消息
        for attempt in range(5):
            try:
                return chain.invoke(invoke_data)
            except Exception as e:
                if attempt == 4:  # 最后一次尝试
                    raise RuntimeError(f"生成AI消息失败: {e}") from e
                logger.warning(f"第{attempt + 1}次尝试失败，准备重试: {str(e)}")
                time.sleep(2 ** attempt)  # 指数退避
    except (RuntimeError, ValueError, TypeError) as e:
        logger.error(e)
        # 仅捕获预期范围内可能发生的异常
        raise RuntimeError(f"生成AI消息失败: {e}") from e
