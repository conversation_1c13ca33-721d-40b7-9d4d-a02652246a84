"""
需求CS确认Service

V字对应：
1.3 要件分析
3. RequirementCS確認

该模块提供需求分析和CS确认相关的API接口，用于根据要件一览表填写CheckListSheet。
主要功能包括读取需求列表文件和CS文件，进行需求分析和确认。

主要功能：
1. 读取要件一览表Excel文件，解析ARチケットNO和相关信息
2. 查找对应的SCL文件并解析变更内容
3. 读取CS确认文件进行需求确认
4. 提供完整的需求分析和确认流程
"""
import pathlib
from typing import List, Dict, Any, Optional

import pandas as pd
from loguru import logger

from sdw_agent.util import select_util


class RequirementCSService:
    """
    需求CS确认服务类

    负责处理需求分析和CS确认的核心业务逻辑，包括：
    - 解析要件一览表文件
    - 查找和解析SCL文件
    - 读取CS确认文件
    - 进行需求分析和确认
    """

    def __init__(self, req_list_file: str, cs_file: str):
        """
        初始化需求CS确认服务

        Args:
            req_list_file (str): 要件一览表文件路径
            cs_file (str): CS确认文件路径
        """
        logger.info(f"初始化RequirementCSService - 要件文件: {req_list_file}, CS文件: {cs_file}")

        # 验证文件路径
        req_path = pathlib.Path(req_list_file)
        cs_path = pathlib.Path(cs_file)

        if not req_path.exists():
            logger.error(f"要件一览表文件不存在: {req_list_file}")
            raise FileNotFoundError(f"要件一览表文件不存在: {req_list_file}")

        if not cs_path.exists():
            logger.error(f"CS确认文件不存在: {cs_file}")
            raise FileNotFoundError(f"CS确认文件不存在: {cs_file}")

        self.req_list_file = req_list_file
        self.req_dir = req_path.parent
        self.cs_file = cs_file

        logger.info(f"服务初始化完成 - 要件目录: {self.req_dir}")
        logger.debug(f"要件文件路径: {self.req_list_file}")
        logger.debug(f"CS文件路径: {self.cs_file}")

    def read_req_file(self) -> Dict[str, Any]:
        """
        读取要件一览表文件并解析需求信息

        Returns:
            Dict[str, Any]: 包含ARチケットNO和对应需求信息的字典
        """
        logger.info("开始读取要件一览表文件")

        try:
            # 获取Excel文件的所有sheet名称
            excel_file = pd.ExcelFile(self.req_list_file)
            sheet_names = excel_file.sheet_names
            logger.debug(f"发现的sheet名称: {sheet_names}")

            # 使用智能选择工具选择要件一览sheet
            sheet_name = select_util.select_one('要件一览', sheet_names)
            logger.info(f"选择的sheet: {sheet_name}")

            # 读取Excel数据，从第6行开始（header=5）
            df = pd.read_excel(self.req_list_file, sheet_name, header=5)
            logger.info(f"成功读取Excel数据，共{len(df)}行")
            logger.debug(f"数据列名: {df.columns.tolist()}")

            # 检查必要的列是否存在
            required_columns = ['ARチケットNO', 'Unnamed: 26']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                raise ValueError(f"Excel文件缺少必要的列: {missing_columns}")

            # 按ARチケットNO分组，聚合相关信息
            logger.info("开始按ARチケットNO分组处理数据")
            info: Dict[str, str] = df[['ARチケットNO', 'Unnamed: 26']].groupby("ARチケットNO")["Unnamed: 26"].agg(
                lambda a: '\n'.join(set(str(x) for x in a if pd.notna(x)))
            ).to_dict()

            logger.info(f"成功分组，共找到{len(info)}个ARチケットNO")

            # 处理每个ARチケットNO
            processed_results = {}
            for ticket_no, requirement_info in info.items():
                logger.debug(f"处理ARチケットNO: {ticket_no}")

                # 查找对应的SCL文件
                scl_files = self.find_scl_file(ticket_no)
                if not scl_files:
                    logger.warning(f"未找到ARチケットNO {ticket_no} 对应的SCL文件")
                    continue

                logger.info(f"为ARチケットNO {ticket_no} 找到{len(scl_files)}个SCL文件")

                # 解析所有SCL文件
                scl_results = []
                for scl_file in scl_files:
                    logger.debug(f"解析SCL文件: {scl_file}")
                    try:
                        scl_result = self.parse_scl_file(scl_file)
                        scl_results.append({
                            'file_path': str(scl_file),
                            'data': scl_result
                        })
                        logger.debug(f"成功解析SCL文件: {scl_file}")
                    except Exception as e:
                        logger.error(f"解析SCL文件失败 {scl_file}: {str(e)}")
                        continue

                # 组合结果
                processed_results[ticket_no] = {
                    'requirement_info': requirement_info,
                    'scl_results': scl_results
                }

                logger.info(f"ARチケットNO {ticket_no} 处理完成，包含{len(scl_results)}个SCL结果")

                # TODO: results结合requirement_info组成完整信息，让大模型针对固定的CheckList做判断题

            logger.info(f"要件一览表文件读取完成，共处理{len(processed_results)}个有效ARチケットNO")
            return processed_results

        except Exception as e:
            logger.error(f"读取要件一览表文件失败: {str(e)}")
            raise

    def find_scl_file(self, ticket_no: str) -> List[pathlib.Path]:
        """
        根据ARチケットNO查找对应的SCL文件

        Args:
            ticket_no (str): ARチケットNO

        Returns:
            List[pathlib.Path]: 找到的SCL文件路径列表
        """
        logger.debug(f"查找ARチケットNO {ticket_no} 对应的SCL文件")

        # 构建SCL文件目录路径
        scl_dir = self.req_dir / ticket_no
        logger.debug(f"SCL目录路径: {scl_dir}")

        # 检查目录是否存在
        if not scl_dir.exists():
            logger.warning(f"SCL目录不存在: {scl_dir}")
            return []

        if not scl_dir.is_dir():
            logger.warning(f"路径不是目录: {scl_dir}")
            return []

        # 查找所有以SCL_开头的文件
        try:
            scl_files = list(scl_dir.glob('SCL_*'))
            logger.info(f"在目录 {scl_dir} 中找到{len(scl_files)}个SCL文件")

            # 过滤出Excel文件
            excel_files = [f for f in scl_files if f.suffix.lower() in ['.xlsx', '.xls']]
            if len(excel_files) != len(scl_files):
                logger.debug(f"过滤后剩余{len(excel_files)}个Excel文件")

            # 记录找到的文件
            for file_path in excel_files:
                logger.debug(f"找到SCL文件: {file_path.name}")

            return excel_files

        except Exception as e:
            logger.error(f"查找SCL文件时发生错误: {str(e)}")
            return []

    def parse_scl_file(self, scl_file: pathlib.Path) -> List[Dict[str, Any]]:
        """
        解析SCL文件，提取变更信息

        Args:
            scl_file (pathlib.Path): SCL文件路径

        Returns:
            List[Dict[str, Any]]: 解析后的变更信息列表
        """
        logger.info(f"开始解析SCL文件: {scl_file.name}")

        # 定义需要查找的关键列
        target_keys = ['変更前', '変更後', '差分種別', '変更内容']
        logger.debug(f"目标列名: {target_keys}")

        try:
            # 获取Excel文件的所有sheet
            excel_file = pd.ExcelFile(scl_file)
            sheet_names = excel_file.sheet_names
            logger.info(f"SCL文件包含{len(sheet_names)}个sheet: {sheet_names}")

            results = []

            for sheet_name in sheet_names:
                logger.debug(f"处理sheet: {sheet_name}")

                try:
                    # 从第4行开始读取数据（header=3）
                    df = pd.read_excel(scl_file, sheet_name=sheet_name, header=3)
                    logger.debug(f"Sheet {sheet_name} 包含{len(df)}行数据")

                    # 获取所有列名
                    columns = df.columns.tolist()
                    logger.debug(f"Sheet {sheet_name} 的列名: {columns}")

                    # 使用智能选择工具匹配目标列
                    matched_keys = select_util.select_many(
                        '\n'.join(target_keys),
                        columns,
                        count=4,
                        detailed=True
                    )

                    logger.debug(f"匹配到的列: {matched_keys}")

                    # 检查是否找到足够的列
                    if len(matched_keys) < 4:
                        logger.warning(f"Sheet {sheet_name} 中未找到足够的目标列（需要4列，找到{len(matched_keys)}列）")
                        continue

                    # 提取实际的列名
                    actual_column_names = [item[0] for item in matched_keys]
                    logger.info(f"Sheet {sheet_name} 使用的列名: {actual_column_names}")

                    # 提取对应列的数据
                    sheet_data = df[actual_column_names].copy()

                    # 清理数据：移除全为空的行
                    sheet_data = sheet_data.dropna(how='all')

                    # 转换为字典格式，便于后续处理
                    sheet_result = {
                        'sheet_name': sheet_name,
                        'column_mapping': dict(zip(target_keys, actual_column_names)),
                        'data': sheet_data.to_dict('records'),
                        'row_count': len(sheet_data)
                    }

                    results.append(sheet_result)
                    logger.info(f"Sheet {sheet_name} 解析完成，有效数据{len(sheet_data)}行")

                except Exception as e:
                    logger.error(f"解析sheet {sheet_name} 时发生错误: {str(e)}")
                    continue

            logger.info(f"SCL文件 {scl_file.name} 解析完成，成功处理{len(results)}个sheet")
            return results

        except Exception as e:
            logger.error(f"解析SCL文件 {scl_file} 失败: {str(e)}")
            raise

    def read_cs_file(self) -> Optional[Dict[str, Any]]:
        """
        读取CS确认文件

        Returns:
            Optional[Dict[str, Any]]: CS确认文件的内容，如果读取失败返回None
        """
        logger.info("开始读取CS确认文件")

        try:
            # 获取Excel文件的所有sheet
            excel_file = pd.ExcelFile(self.cs_file)
            sheet_names = excel_file.sheet_names
            logger.info(f"CS文件包含{len(sheet_names)}个sheet: {sheet_names}")

            cs_data = {}

            for sheet_name in sheet_names:
                logger.debug(f"读取CS文件sheet: {sheet_name}")

                try:
                    # 读取sheet数据
                    df = pd.read_excel(self.cs_file, sheet_name=sheet_name)
                    logger.debug(f"CS文件sheet {sheet_name} 包含{len(df)}行数据")

                    # 存储sheet数据
                    cs_data[sheet_name] = {
                        'data': df.to_dict('records'),
                        'columns': df.columns.tolist(),
                        'row_count': len(df)
                    }

                    logger.info(f"CS文件sheet {sheet_name} 读取完成")

                except Exception as e:
                    logger.error(f"读取CS文件sheet {sheet_name} 时发生错误: {str(e)}")
                    continue

            logger.info(f"CS确认文件读取完成，成功读取{len(cs_data)}个sheet")
            return cs_data

        except Exception as e:
            logger.error(f"读取CS确认文件失败: {str(e)}")
            return None

    def process_requirement_analysis(self) -> Dict[str, Any]:
        """
        执行完整的需求分析流程

        Returns:
            Dict[str, Any]: 分析结果
        """
        logger.info("开始执行需求分析流程")

        try:
            # 读取要件一览表
            req_data = self.read_req_file()

            # 读取CS确认文件
            cs_data = self.read_cs_file()

            # 组合分析结果
            analysis_result = {
                'requirement_data': req_data,
                'cs_data': cs_data,
                'analysis_summary': {
                    'total_tickets': len(req_data) if req_data else 0,
                    'cs_sheets': len(cs_data) if cs_data else 0,
                    'timestamp': pd.Timestamp.now().isoformat()
                }
            }

            logger.info("需求分析流程执行完成")
            return analysis_result

        except Exception as e:
            logger.error(f"需求分析流程执行失败: {str(e)}")
            raise


def main():
    """
    主函数 - 用于测试和演示
    """
    logger.info("启动需求CS确认服务测试")

    try:
        # 创建服务实例
        service = RequirementCSService(
            req_list_file=r"C:\Users\<USER>\Desktop\workflows\R4小\R4小_epic v035~v041_要件分析.xlsx",
            cs_file=r"C:\Users\<USER>\Desktop\workflows\R4小\【DNKT】式样读合CheckList_v3.0.xlsx"
        )

        # 执行完整的分析流程
        result = service.process_requirement_analysis()

        # 输出分析摘要
        summary = result.get('analysis_summary', {})
        logger.info(f"分析完成 - 处理了{summary.get('total_tickets', 0)}个ARチケット，"
                   f"读取了{summary.get('cs_sheets', 0)}个CS sheet")

        logger.info("需求CS确认服务测试完成")

    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}")
        raise


if __name__ == '__main__':
    main()
