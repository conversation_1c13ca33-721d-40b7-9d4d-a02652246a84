# -*- coding: utf-8 -*-
"""
@File    : testcase_template_api.py
<AUTHOR> hk
@Date    : 2025-04-08 10:48
@Desc    : Description of the file
"""
from fastapi import APIRouter

from sdw_agent.model.request_model import (ChangePointImportRequest, TestViewFileParserRequest,
                                           TestViewGenerateRequest, TestViewSuppleRequest)
from sdw_agent.model.response_model import (ChangePointImportResponse, TestViewFileParserResponse,
                                            TestViewGenerateResponse, TestViewSuppleResponse)
from sdw_agent.service.test_view_generate.changepoint import Changepoint
from sdw_agent.service.test_view_generate.file_parse import FileParser
from sdw_agent.service.test_view_generate.rule_test_view import NewTestView
from sdw_agent.service.test_view_generate.testcase import TestCase

router = APIRouter(prefix="/api/sdw/test_view", tags=["变更一览表提交"])

@router.post("/changepoint/import",
             summary="导入并解析要件一览",
             description="选择要件一览, 导入并解析得到变更点",
             response_description="",
             response_model=ChangePointImportResponse)
async def import_changepoint(request: ChangePointImportRequest):
    file_path = request.file
    res, data = Changepoint.import_changepoint(file_path)
    if res:
        return {
            'code': 0,
            'message': "解析要件一览表成功",
            'data': data
        }
    return {
            'code': 500,
            'message': "解析要件一览表失败",
            'data': []
        }

@router.post("/file_parser/confirm",
             summary="确认解析excel",
             description="确认解析excel, 并将解析得到的数据储存为json",
             response_description="",
             response_model=TestViewFileParserResponse)
async def confirm_parse_file(request: TestViewFileParserRequest):
    properties = request.model_dump()
    res, msg = FileParser.parse_main(properties)
    if res:
        return {
            'code': 0,
            'message': "解析测试观点依赖文件成功",
            'data': msg
        }
    return {
            'code': 500,
            'message': "解析测试观点依赖文件失败",
            'data': msg
        }


@router.post("/generate",
             summary="根据变更点生成测试观点列表",
             description="根据变更点生成测试观点列表，并对中间结果文件的保存",
             response_description="",
             response_model=TestViewGenerateResponse)
async def generate(request: TestViewGenerateRequest):
    res, data = NewTestView.generate(request.change_points)
    if res:
        return {
            'code': 0,
            'message': "变更点生成测试观点成功",
            'data': data
        }
    return {
        'code': 500,
        'message': "变更点生成测试观点失败",
        'data': {}
    }


@router.post("/test_case/supplemental_generate",
             summary="将基于变更点生成的测试观点所对应测试用例回写到excel",
             description="基于变更点生成的测试观点所对应测试用例，回写对应excel",
             response_description="",
             response_model=TestViewSuppleResponse)
async def supplemental_generate(request: TestViewSuppleRequest):
    res = TestCase.supplemental_generate()
    if res:
        return {
            'code': 0,
            'message': "测试用例及测试观点回写成功",
            'data': res
        }
    return {
        'code': 500,
        'message': "测试用例及测试观点回写失败",
        'data': {}
    }


if __name__ == '__main__':
    input_data = {
        "change_points": {
                "ARチケットNO": "MET19PFV3-21517",
                "ARチケットNOリンク": "MET19PFV3-21517",
                "ARチケットタイトル": "【顧客要求_変更】MET-G_CSTMLST-CSTD-A0-04-A-C0",
                "A核": "〇",
                "R核": "〇",
                "SCL填写要": "×",
                "changeNo": "MET19PFV3-21517",
                "id": 2,
                "requirementMd": "MET_CSTM_警報音量の階層修正機能.md",
                "summary": "警報音量の階層修正",
                "エピック名": "MET-G_CSTMLST-CSTD_SoC R_警報音量の階層修正",
                "变更内容所在的章节信息": "5.1 画面階層情報",
                "変更内容": "第二階層の警報音量の詳細画面は第二階層から第一階層の警報音量項目の下に移動する",
                "対応イベント": "R2大",
                "差分種別": "选项位置变更",
                "概要": "警報音量の階層修正",
                "要件チケットNO": "-",
                "要件チケットNOリンク": "-",
                "要件需求文档名称": "MET_CSTM_警報音量の階層修正機能.md"
            }
    }
    _, res = TestView.generate(input_data['change_points'])
    print(res)
