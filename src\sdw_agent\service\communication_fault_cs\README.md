# 通信故障安全CS工作流

## 概述

通信故障安全CS工作流用于分析代码变更中的CAN信号和控制信号匹配。该工作流支持从BitAssign文件和SPI JSON文件中解析信号定义，并在代码变更中搜索匹配的信号。

## V字对应

- **V字阶段**: 2.1 基本設計
- **V字项目**: 通信故障安全 CS

## 主要功能

1. **信号定义解析**
   - 支持BitAssign Excel文件解析
   - 支持SPI JSON文件解析
   - 提取CAN信号和控制信号定义

2. **代码变更分析**
   - 从Gerrit获取代码变更信息
   - 从本地Git仓库获取代码变更信息
   - 支持指定提交ID和对比提交ID

3. **信号匹配搜索**
   - 多线程并行搜索
   - 支持多种匹配模式（精确匹配、赋值语句、指针访问等）
   - 可配置大小写敏感性

4. **结果输出**
   - 生成Excel格式的匹配结果报告
   - 包含详细的匹配信息和统计数据
   - 支持分别输出CAN信号和控制信号匹配结果

## 使用方法

### 基本用法

```python
from sdw_agent.service.communication_fault_cs import CommunicationFaultCSWorkflow, InputDataModel, SourceType

# 创建工作流实例
workflow = CommunicationFaultCSWorkflow()

# 准备输入数据 - BitAssign方式
input_data = InputDataModel(
    source_type=SourceType.BIT_ASSIGN,
    commit_id="your_commit_id",
    bit_assign_path="/path/to/bitassign.xls",
    case_sensitive=True
)

# 执行工作流
result = workflow.run(input_data)

if result.status == WorkflowStatus.SUCCESS:
    print(f"执行成功: {result.message}")
    print(f"输出文件: {result.data['output_file_path']}")
else:
    print(f"执行失败: {result.error}")
```

### SPI JSON方式

```python
# 准备输入数据 - SPI JSON方式
input_data = InputDataModel(
    source_type=SourceType.SPI_JSON,
    commit_id="your_commit_id",
    can_json_path="/path/to/can_signals.json",
    ctrl_json_path="/path/to/ctrl_signals.json",
    repository_path="/path/to/local/repo"  # 可选，使用本地仓库
)

# 执行工作流
result = workflow.run(input_data)
```

## 配置说明

工作流配置文件 `config.yaml` 包含以下主要配置项：

### 基本配置
- `name`: 工作流名称
- `description`: 工作流描述
- `version`: 版本号
- `author`: 作者

### 处理配置
- `max_workers`: 最大并发线程数（默认10）
- `default_case_sensitive`: 默认是否区分大小写（默认true）
- `timeout_seconds`: 处理超时时间（默认300秒）

### 输出配置
- `output_format`: 输出文件格式（默认xlsx）
- `include_summary`: 是否包含摘要信息（默认true）

### 信号匹配配置
- `patterns`: 搜索模式配置
- `min_line_length`: 最小行长度（默认3）
- `exclude_comments`: 是否排除注释行（默认true）

## 输入参数

### InputDataModel

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| source_type | SourceType | 是 | 数据源类型（bit_assign或spi_json） |
| commit_id | str | 是 | 提交ID |
| compared_commit_id | str | 否 | 对比的提交ID |
| repository_path | str | 否 | 本地仓库路径 |
| bit_assign_path | str | 条件 | BitAssign文件路径（source_type为bit_assign时必填） |
| can_json_path | str | 条件 | CAN信号JSON文件路径（source_type为spi_json时必填） |
| ctrl_json_path | str | 条件 | 控制信号JSON文件路径（source_type为spi_json时必填） |
| case_sensitive | bool | 否 | 是否区分大小写（默认true） |

## 输出结果

### OutputDataModel

| 字段名 | 类型 | 说明 |
|--------|------|------|
| output_file_path | str | 输出文件路径 |
| total_files_processed | int | 处理的文件总数 |
| total_matches_found | int | 找到的匹配总数 |
| can_matches | List[MatchedSignalModel] | CAN信号匹配结果 |
| ctrl_matches | List[MatchedSignalModel] | 控制信号匹配结果 |
| processing_time | float | 处理时间（秒） |
| summary | str | 处理摘要 |

### MatchedSignalModel

| 字段名 | 类型 | 说明 |
|--------|------|------|
| commit_id | str | 提交ID |
| file_path | str | 文件路径 |
| file_name | str | 文件名 |
| line_number | int | 行号 |
| line_content | str | 行内容 |
| frame_id | str | 帧ID |
| matched_signal | str | 匹配的信号名称 |
| signal_name_en | str | 信号英文名称（BitAssign） |
| signal_name_ja | str | 信号日文名称（BitAssign） |
| signal_name | str | 信号名称（SPI JSON） |
| init_value | Union[str, int, float] | 初始值 |
| max_value | Union[int, float] | 最大值（SPI JSON） |
| min_value | Union[int, float] | 最小值（SPI JSON） |
| notes | str | 备注（SPI JSON） |

## 错误处理

工作流包含完善的错误处理机制：

1. **输入验证**: 验证所有输入参数的有效性
2. **文件检查**: 检查输入文件是否存在和可读
3. **异常捕获**: 捕获并记录所有异常信息
4. **优雅降级**: 在部分处理失败时继续处理其他文件

## 性能优化

1. **多线程处理**: 使用线程池并行处理多个文件
2. **模式预编译**: 预编译正则表达式模式提高匹配效率
3. **内存管理**: 分批处理大数据集避免内存溢出
4. **缓存机制**: 缓存重复计算结果

## 注意事项

1. 确保输入文件格式正确且可读
2. 提交ID必须是有效的Git提交哈希
3. 使用本地仓库时确保路径正确且有读取权限
4. 大文件处理时注意内存使用情况
5. 网络环境不稳定时建议使用本地仓库模式

## 版本历史

- **v1.0.0**: 初始重构版本，支持BitAssign和SPI JSON两种数据源
