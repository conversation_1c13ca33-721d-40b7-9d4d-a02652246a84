"""
统一日志配置模块 - 集成uvicorn和loguru
"""
import logging
import sys
from pathlib import Path
from loguru import logger


class InterceptHandler(logging.Handler):
    """
    拦截标准库logging的处理器，将日志重定向到loguru
    
    这个处理器拦截所有标准库logging的日志记录，并将它们转发给loguru处理
    """
    
    def emit(self, record):
        # 获取对应的loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
            
        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
            
        # 将日志转发给loguru
        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging(
    console_level="INFO",
    file_level="DEBUG",
    log_file=None,
    json_format=False,
    intercept_uvicorn=True
):
    """
    设置统一的日志配置
    
    Args:
        console_level: 控制台日志级别
        file_level: 文件日志级别
        log_file: 日志文件路径，如果为None则使用默认路径
        json_format: 是否使用JSON格式输出
        intercept_uvicorn: 是否拦截uvicorn日志
    """
    # 移除所有现有的处理器
    logger.remove()
    
    # 设置默认日志文件路径
    if log_file is None:
        home_dir = Path.home()
        log_dir = home_dir / ".sdw" / "log"
        log_dir.mkdir(parents=True, exist_ok=True)
        log_file = log_dir / "SDW_Agent.log"
    
    # 控制台格式
    if json_format:
        console_format = '{{"time":"{time:YYYY-MM-DD HH:mm:ss}", "level":"{level}", "message":"{message}", "name":"{name}", "function":"{function}", "line":"{line}"}}'
    else:
        console_format = "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    
    # 文件格式
    if json_format:
        file_format = '{{"time":"{time:YYYY-MM-DD HH:mm:ss}", "level":"{level}", "message":"{message}", "name":"{name}", "function":"{function}", "line":"{line}"}}'
    else:
        file_format = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format=console_format,
        level=console_level,
        colorize=not json_format,
    )
    
    # 添加文件输出
    logger.add(
        log_file,
        format=file_format,
        level=file_level,
        rotation="1 day",
        retention="10 days",
        encoding="utf-8",
        enqueue=True
    )
    
    # 拦截uvicorn日志
    if intercept_uvicorn:
        # 配置uvicorn和FastAPI使用的标准库logging
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
        
        # 拦截所有第三方库的日志
        for _log in ["uvicorn", "uvicorn.error", "uvicorn.access", "fastapi"]:
            _logger = logging.getLogger(_log)
            _logger.handlers = [InterceptHandler()]
            _logger.propagate = False
    
    logger.info("日志系统初始化完成")
    return logger


def get_logger():
    """获取已配置的logger实例"""
    return logger
