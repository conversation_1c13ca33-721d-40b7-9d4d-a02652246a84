import json
import math
import os
import pathlib
import re
import sys
import threading
from typing import Dict, Union, List

import pandas as pd
import win32con
import win32gui
import xlwings as xw
import yaml
from openpyxl import load_workbook
from openpyxl.cell import MergedCell
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from loguru import logger
import time

from sdw_agent.config.config import ROOT_DIR

src_path = str(pathlib.Path(__file__).parent.parent.parent)
if src_path not in sys.path:
    sys.path.insert(0, src_path)


def read_excel(file_path: str, sheet_name: str | int = 0, header: int = 0):
    """
        根据指定路径读取excel文件
        参数:
            file_path (str): 文件路径
            sheet_name (str | int): sheet name
            header (int): 表头行索引
            add_uuid (bool): 是否添加UUID列，默认为False
    """
    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)
    # ['対応イベント', 'ARチケットNO', 'ARチケットNOリンク', 'ARチケットタイトル', 'エピック名', '概要', '要件チケットNO', '要件チケットNOリンク', 'R核', 'A核', '要件需求文档名称', '变更内容所在的章节信息']
    column_names = df.columns.tolist()

    # 加载 Excel 文件
    workbook = load_workbook(file_path)

    # 获取指定工作表
    if isinstance(sheet_name, int):
        sheet = workbook[workbook.sheetnames[sheet_name]]
    else:
        sheet = workbook[sheet_name]
    hyperlinks = []
    pattern = r'HYPERLINK\("(.*?)"'
    # 读取hyperlink 信息
    for idx, row in enumerate(sheet.iter_rows(min_row=(header + 1))):
        if idx == 0:
            continue
        hyperdict = {}
        for col_idx, col in enumerate(column_names):
            if not isinstance(row[col_idx], MergedCell) and isinstance(row[col_idx].internal_value, str):
                match = re.search(pattern, row[col_idx].internal_value)
                if match:
                    hyperlink_url = match.group(1)
                    hyperdict[col] = hyperlink_url
                else:
                    hyperlink_url = ""
                    hyperdict[col] = hyperlink_url

        hyperlinks.append(hyperdict)
    # 返回超链接信息，dataframe
    return hyperlinks, df


def check_file_exists(file_path: str) -> None:
    """
    通用文件存在性检查函数
    参数:
        file_path (str): 待检查的文件路径
    抛出:
        FileNotFoundError: 当文件路径不存在时
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"{_('文件路径不存在:')} {file_path}")  # noqa


def save_df_to_excel(df: pd.DataFrame, file_path: str, sheet_name: str = "Sheet1", mode: str = "w"):
    """
    保存DataFrame到Excel，表头Meiryo UI字体、11号、加粗、左对齐，宽度自适应。
    """
    def _set_excel_header_style(worksheet, df):
        # 设置表头样式
        header_font = Font(name="Meiryo UI", size=11, bold=True)
        header_alignment = Alignment(horizontal="left", vertical="center")
        header_fill = PatternFill(start_color='FFF8CBAD', end_color='FFF8CBAD', fill_type='solid')
        for col_idx, col in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_idx)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # 设置除表头外单元格样式
        body_font = Font(name="Meiryo UI", size=10)
        for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row, min_col=1, max_col=worksheet.max_column):
            for cell in row:
                cell.font = body_font

        # 列宽自适应
        for col_idx, col in enumerate(df.columns, 1):
            max_length = max(
                [len(str(cell.value)) for cell in worksheet[get_column_letter(col_idx)]]
            )
            worksheet.column_dimensions[get_column_letter(col_idx)].width = max(max_length + 2, 12)
        pass


    # 用pandas写入Excel
    if mode == 'a':
        with pd.ExcelWriter(file_path, engine="openpyxl", mode='a', if_sheet_exists='replace') as writer:
            df.to_excel(writer, index=False, sheet_name=sheet_name)
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]
            # 设置表头样式，列宽自适应
            _set_excel_header_style(worksheet, df)

    else:
        with pd.ExcelWriter(file_path, engine="openpyxl", mode='w') as writer:
            df.to_excel(writer, index=False, sheet_name=sheet_name)
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]
            # 设置表头样式，列宽自适应
            _set_excel_header_style(worksheet, df)


class ReadExcel:
    """用来解析test agent的变更一览表、checklist、rule、测试用例文件"""

    @staticmethod
    def import_excel(file_path, sheet_name, header_index, path_type):
        # 异步读取文件内容
        if not os.path.isfile(file_path):
            return None
            # 加载工作簿
        read_data = ReadExcel.read_sheet(file_path, sheet_name, header_index)
        res = []
        i = 1
        for data in read_data:
            var = {key: value for key, value in data.items() if value is not None}
            if path_type == "testcases":
                var["sheet_name"] = sheet_name
            if path_type == "testcases" and "示例" not in var:
                continue
            if path_type == "testcases" and var["示例"] in ["说明", "Please Insert above this line"]:
                continue
            res.append(var)
            i = i + 1
        return res

    @staticmethod
    def read_sheet(file_path, sheet_name, header_index):
        # 加载 Excel 文件
        workbook = load_workbook(file_path, data_only=True)

        # 获取指定的工作表
        # 判断有无指定sheet
        if sheet_name not in workbook.sheetnames:
            return []
        sheet = workbook[sheet_name]

        # 遍历合并单元格，填充合并区域的值
        merged_ranges = list(sheet.merged_cells.ranges)  # 将集合转换为静态列表
        for merged_cell in merged_ranges:
            # 获取合并单元格左上角单元格的值
            top_left_cell_value = sheet.cell(row=merged_cell.min_row, column=merged_cell.min_col).value

            # 取消合并单元格
            sheet.unmerge_cells(str(merged_cell))

            # 遍历合并区域的所有单元格并填充值
            for row in range(merged_cell.min_row, merged_cell.max_row + 1):
                for col in range(merged_cell.min_col, merged_cell.max_col + 1):
                    sheet.cell(row=row, column=col).value = top_left_cell_value

        # 读取表头
        headers = []
        for cell in sheet[header_index]:  # 假设第一行是表头
            headers.append(cell.value if cell.value is not None else f"未命名列{len(headers) + 1}")

        # 获取数据行
        data = []
        for row in sheet.iter_rows(min_row=header_index + 1, values_only=True):  # 从第二行开始读取数据
            if any(row):  # 检查行是否完全为空
                record = {}
                for i in range(len(headers)):
                    if row[i] is not None:
                        if headers[i] in record:
                            if str(record[headers[i]]) != str(row[i]):
                                record[headers[i]] = str(record[headers[i]]) + "-" + str(row[i])
                        else:
                            record[headers[i]] = str(row[i])
                data.append(record)

        ## 去除掉所有的未命名列
        # 过滤掉包含未命名列的记录,并确保每条记录都是json格式
        for record in data:
            keys_to_delete = [key for key in record.keys() if key.startswith("未命名")]
            for key in keys_to_delete:
                del record[key]

        return data

    @staticmethod
    def data_save(data, path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    @staticmethod
    def read_multiple_sheets(file_path, sheet_names, header_index, path_type):
        res = []
        for sheet_name in sheet_names:
            data = ReadExcel.import_excel(file_path, sheet_name, header_index, path_type)
            res.extend(data)
        return res

    @staticmethod
    def parse_excel(path_type, excel_path):
        data_dir = os.path.join(ROOT_DIR, "data")
        data_dir = os.path.abspath(data_dir)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        config = yaml.load(open(os.path.join(ROOT_DIR, "config/config.yaml"), "r", encoding="utf-8"),
                           Loader=yaml.FullLoader)
        test_agent_input_table = config["test_agent_input_table"][path_type]
        sheet_names = test_agent_input_table["sheet_name"]
        header_index = test_agent_input_table["header_index"]
        file_path = test_agent_input_table["name"]
        res = ReadExcel.read_multiple_sheets(excel_path, sheet_names, header_index, path_type)
        save_path = os.path.join(ROOT_DIR, file_path)
        ReadExcel.data_save(res, save_path)
        return save_path, res


class ExcelTemplateWriter:
    """用来将生成的测试观点回写到模板中"""

    def __init__(self, template_path: str):
        """
        升级版Excel模板写入工具（支持合并单元格）

        :param template_path: 模板文件路径
        """
        template_path = get_template_path(template_path)
        self.wb = load_workbook(template_path)
        self.merged_ranges = {}  # 缓存合并区域 {sheet_name: List[合并范围元组]}

        # 初始化时缓存所有合并区域
        for sheet_name in self.wb.sheetnames:
            sheet = self.wb[sheet_name]
            self.merged_ranges[sheet_name] = [
                (r.min_row, r.min_col, r.max_row, r.max_col)
                for r in sheet.merged_cells.ranges
            ]

    def _get_column_letter(self, index: int) -> str:
        """将数字索引转换为Excel列字母"""
        return get_column_letter(index + 1)  # openpyxl从1开始计数

    def _copy_style(self, source_cell, target_cell):
        """复制单元格样式"""
        if source_cell.has_style:
            target_cell.font = source_cell.font.copy()
            target_cell.border = source_cell.border.copy()
            target_cell.fill = source_cell.fill.copy()
            target_cell.number_format = source_cell.number_format
            target_cell.protection = source_cell.protection.copy()
            target_cell.alignment = source_cell.alignment.copy()

    def write_data(
            self,
            sheet_name: str,
            data: List[Union[Dict, object]],
            start_row: int = 2,
            style_sample_row: int = 2
    ) -> None:
        """
        通用数据写入方法

        :param sheet_name: 工作表名称
        :param data: 数据列表，支持字典列表或数据类实例
        :param start_row: 数据起始行
        :param style_sample_row: 样式参考行
        """
        if not data:
            return

        ws = self.wb[sheet_name]
        fields = ["id", "category", "checkpoint", "mse_category", "mse_content", "procedure", "source"]
        # 缓存样式样本
        sample_row = []
        for col_idx, _ in enumerate(fields):
            col_letter = self._get_column_letter(col_idx)
            sample_cell = ws[f"{col_letter}{style_sample_row}"]
            sample_row.append(sample_cell)

        # 写入数据
        for row_idx, item in enumerate(data, start=start_row):
            for col_idx, field in enumerate(fields):
                col_letter = self._get_column_letter(col_idx)
                cell = ws[f"{col_letter}{row_idx}"]

                # 设置值
                if isinstance(item, dict):
                    cell.value = item.get(field)
                else:
                    cell.value = getattr(item, field, None)

                # 复制样式
                self._copy_style(sample_row[col_idx], cell)

    def save(self, output_path: str) -> None:
        """保存文件时保留原始合并结构"""
        # 恢复原始合并区域
        for sheet_name in self.wb.sheetnames:
            sheet = self.wb[sheet_name]
            sheet.merged_cells.ranges = []
            for bounds in self.merged_ranges[sheet_name]:
                sheet.merge_cells(
                    start_row=bounds[0],
                    start_column=bounds[1],
                    end_row=bounds[2],
                    end_column=bounds[3]
                )

        self.wb.save(output_path)
        self.wb.close()


def get_template_path(file_name: str) -> str:
    """主要用来将测试用例写入测试用例模板时需要，获取资源目录下的模板文件绝对路径"""
    # 获取当前脚本的绝对路径（假设此函数写在 src/utils 目录中的一个文件里）
    current_script_path = pathlib.Path(__file__).resolve()

    # 回退到项目根目录（假设脚本在 src 子目录中）
    project_root = current_script_path.parent.parent  # 根据实际层级调整

    # 拼接资源路径
    template_path = project_root / "resources" / "templates" / file_name

    # 路径存在性检查
    if not template_path.exists():
        raise FileNotFoundError(f"模板文件不存在: {template_path}")

    return str(template_path)


def replace_nan_with_empty_string(d):
    for key, value in d.items():
        if isinstance(value, dict):  # 如果值是嵌套字典，递归处理
            replace_nan_with_empty_string(value)
        elif isinstance(value, float) and math.isnan(value):  # 检查是否为 NaN
            d[key] = ""  # 替换为空字符串


def click_buttons_in_wb(wb, macro_names):
    """
    使用 xlwings 调用已打开的工作簿中的多个宏（模拟点击一连串按钮）。

    Args:
        wb (xlwings.Book): 已打开的工作簿对象。
        macro_names (list): 包含多个宏名称的列表（如 ['Sheet1.CommandButton1_Click', 'Sheet1.CommandButton2_Click']）。
    """
    try:
        # 依次调用宏
        for macro_name in macro_names:
            logger.debug(f"Calling macro: {macro_name}")
            wb.macro(macro_name)()  # 调用宏并执行

        # 保存工作簿
        # wb.save()
        logger.info(f"All macros {macro_names} executed successfully.")

    except Exception as e:
        print(f"Error: {e}")


def compare_excel_sheets(file1, file2):
    # 读取两个 Excel 文件
    excel1 = pd.ExcelFile(file1)
    excel2 = pd.ExcelFile(file2)

    # 获取所有 sheet 名称
    sheets1 = set(excel1.sheet_names)
    sheets2 = set(excel2.sheet_names)

    # 结果字典
    result = {}

    # 遍历两个文件中共有的 sheet
    for sheet in sheets1.intersection(sheets2):
        # 读取 sheet 内容
        df1 = excel1.parse(sheet)
        df2 = excel2.parse(sheet)
        df1 = df1.fillna("")
        df2 = df2.fillna("")
        # 将 DataFrame 转为字符串行列表，方便比较
        rows1 = df1.astype(str).apply(lambda x: ','.join(x), axis=1).tolist()
        rows2 = df2.astype(str).apply(lambda x: ','.join(x), axis=1).tolist()

        # 找出新增的行和删除的行
        added_rows = [row for row in rows2 if row not in rows1]
        deleted_rows = [row for row in rows1 if row not in rows2]

        # 如果有新增或删除的行，记录到结果中
        if added_rows or deleted_rows:
            result[sheet] = {
                "新增": added_rows,
                "删除": deleted_rows
            }
    return result

def close_msgbox():
    for i in range(2):
        time.sleep(6)
        # 查找 MsgBox 弹窗
        hwnd = win32gui.FindWindow(None, "Microsoft Excel")  # 弹窗标题
        if hwnd:
            print("MsgBox detected, closing...")
            # 点击 "确定" 按钮
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)

def click_buttons(file_path, macro_names):
    """
    使用 xlwings 调用 .xlsm 文件中的多个宏（模拟点击一连串按钮）。

    Args:
        file_path (str): .xlsm 文件的路径。
        macro_names (list): 包含多个宏名称的列表（如 ['Sheet1.CommandButton1_Click', 'Sheet1.CommandButton2_Click']）。
    """
    try:
        # 打开 Excel 应用程序
        app = xw.App(visible=False)  # 设置为 False 不显示 Excel 窗口
        app.display_alerts = False  # 禁用警告弹窗

        # 打开工作簿
        wb = app.books.open(file_path)
        # 禁用Excel警告和弹窗
        app.api.DisplayAlerts = False
        # 启动关闭弹窗的线程
        thread = threading.Thread(target=close_msgbox)
        thread.start()

        # 依次调用宏
        for macro_name in macro_names:
            logger.debug(f"Calling macro: {macro_name}")
            wb.macro(macro_name)()  # 调用宏并执行

        thread.join()
        # 保存并关闭工作簿
        # target_path = os.path.join(os.path.normpath(r"C:\Users\<USER>\Documents"), os.path.basename(file_path))

        # wb.save(target_path)
        wb.save()
        wb.close()

        # 退出 Excel 应用程序
        app.quit()
        logger.info(f"All macros {macro_names} executed successfully.")
        # return target_path

    except Exception as e:
        print(f"Error: {e}")
        if 'wb' in locals():
            wb.close()
        app.quit()


def click_buttons_in_wb(wb, macro_names):
    """
    使用 xlwings 调用已打开的工作簿中的多个宏（模拟点击一连串按钮）。

    Args:
        wb (xlwings.Book): 已打开的工作簿对象。
        macro_names (list): 包含多个宏名称的列表（如 ['Sheet1.CommandButton1_Click', 'Sheet1.CommandButton2_Click']）。
    """
    try:
        # 依次调用宏
        for macro_name in macro_names:
            logger.info(f"Calling macro: {macro_name}")
            wb.macro(macro_name)()  # 调用宏并执行

        # 保存工作簿
        wb.save()
        logger.info("All macros executed successfully.")

    except Exception as e:
        logger.error(f"Error: {e}")
        raise e


if __name__ == '__main__':
    file_path = r"C:\Users\<USER>\Downloads\19PFv3_TestCase_Auto_MET-G_CSTMLST-CSTD-A0-06-A-C0 (全集).xlsx"
    ReadExcel.parse_excel("testcases", file_path)
