# 测试用例生成器使用说明

## 概述

这个测试用例生成器可以根据模块参数的取值范围，自动生成笛卡尔积测试用例。支持多种参数范围格式，并能为每个模块生成完整的测试用例集合。

## 功能特性

- **🤖 大模型智能解析**：使用AI大模型智能解析参数范围，支持复杂格式和自然语言描述
- **📋 多种范围格式支持**：支持范围格式（如 `0~8`）、逗号分隔格式（如 `0, 1`）、中文描述等
- **🎯 智能值选择**：对于大范围自动选择代表性的值（起始值、中间值、结束值等）
- **🔄 双模式解析**：大模型解析模式（推荐）+ 规则解析模式（备用）
- **⚡ 笛卡尔积生成**：为每个模块的输入参数生成完整的笛卡尔积组合
- **📊 Excel输出**：将生成的测试用例保存为Excel文件，每个模块一个sheet
- **📈 详细统计**：提供测试用例数量统计和示例展示
- **📝 专业日志管理**：使用logger进行日志输出，支持不同级别的日志记录

## 输入格式

### Excel参数模板格式

Excel文件应包含以下列：

| 功能模块 | 参数类型 | 参数名称 | 参数取值范围 |
|---------|---------|---------|-------------|
| ModuleA | 入参    | ARG_1   | 0~3         |
| ModuleA | 入参    | ARG_2   | 0~3         |
| ModuleA | 入参    | ARG_3   | 0~100       |
| ModuleA | 入参    | ARG_4   | 0, 1        |
| ModuleA | 出参    | ARG_5   | 5, 6, 7, 8  |
| ModuleB | 入参    | ARG_1   | 0~8         |
| ModuleB | 入参    | ARG_2   | 0~100       |
| ModuleB | 出参    | ARG_3   | 0, 1        |

### 支持的参数范围格式

#### 🤖 大模型解析模式（推荐）

大模型能够智能理解各种复杂的参数范围格式：

1. **数值范围格式**:
   - `0~8` → `[0, 1, 2, 3, 4, 5, 6, 7, 8]`（小范围全覆盖）
   - `0~100` → `[0, 1, 50, 99, 100]`（大范围代表性值）
   - `-40~85` → `[-40, -39, 23, 84, 85]`（负数范围）

2. **逗号分隔格式**:
   - `0, 1` → `[0, 1]`
   - `5, 6, 7, 8` → `[5, 6, 7, 8]`

3. **中文枚举**:
   - `低速, 中速, 高速` → `['低速', '中速', '高速']`
   - `开启, 关闭, 待机` → `['开启', '关闭', '待机']`

4. **英文枚举**:
   - `AUTO, MANUAL, TEST` → `['AUTO', 'MANUAL', 'TEST']`
   - `OK, ERROR, WARNING` → `['OK', 'ERROR', 'WARNING']`

5. **特殊情况**:
   - `100~0` → `[100, 99, 75, 25, 0]`（逆序范围）
   - `A~Z` → `['A', 'B', 'M', 'Y', 'Z']`（字母范围）
   - `0.5~2.5` → `[0, 1, 2]`（浮点范围转整数）

#### 📋 规则解析模式（备用）

1. **范围格式**: `0~8`
   - 小范围（≤10）：返回所有值 `[0, 1, 2, 3, 4, 5, 6, 7, 8]`
   - 大范围（>10）：返回代表性值 `[0, 1, 50, 99, 100]`（对于 `0~100`）

2. **逗号分隔格式**: `0, 1` 或 `5, 6, 7, 8`
   - 返回所有指定的值

3. **单个值**: `10`
   - 返回该单个值

## 使用方法

### 基本使用

```python
from src.sdw_agent.service.module_domain_test import TestCaseGenerator

# 初始化生成器
generator = TestCaseGenerator(
    arg_file="参数模板.xlsx",
    result_file="生成的测试用例.xlsx"
)

# 🤖 使用大模型解析（推荐）
all_test_cases = generator.generate_all_test_cases(use_llm=True)

# 📋 使用规则解析（备用）
# all_test_cases = generator.generate_all_test_cases(use_llm=False)

# 保存到Excel文件
generator.save_test_cases_to_excel(all_test_cases)

# 打印摘要
generator.print_test_cases_summary(all_test_cases)
```

### 高级使用

```python
# 🤖 大模型解析单个参数范围
values_llm = generator.parse_range_value("低速, 中速, 高速", use_llm=True)
print(values_llm)  # ['低速', '中速', '高速']

# 📋 规则解析单个参数范围
values_rule = generator.parse_range_value("0~100", use_llm=False)
print(values_rule)  # [0, 1, 50, 99, 100]

# 读取并解析模块数据
df = generator.read_arg_file()
modules_data = generator.parse_modules_data(df, use_llm=True)

# 为单个模块生成测试用例
module_test_cases = generator.generate_test_cases_for_module(
    "ModuleA",
    modules_data["ModuleA"]
)

# 容错处理：大模型失败时自动回退到规则解析
try:
    all_test_cases = generator.generate_all_test_cases(use_llm=True)
except Exception as e:
    print(f"大模型解析失败，回退到规则解析: {e}")
    all_test_cases = generator.generate_all_test_cases(use_llm=False)
```

## 输出格式

### 生成的测试用例结构

每个测试用例包含以下信息：

```python
{
    "case_id": 1,
    "module": "ModuleA",
    "inputs": {
        "ARG_1": 0,
        "ARG_2": 0,
        "ARG_3": 0,
        "ARG_4": 0
    },
    "expected_outputs": {
        "ARG_5": [5, 6, 7, 8]
    }
}
```

### Excel输出格式

生成的Excel文件包含：
- 每个模块一个sheet
- 列包括：模块名称、用例ID、输入参数列、期望输出参数列

示例：

| 模块名称 | 用例ID | 输入_ARG_1 | 输入_ARG_2 | 输入_ARG_3 | 输入_ARG_4 | 期望输出_ARG_5 |
|---------|--------|-----------|-----------|-----------|-----------|---------------|
| ModuleA | 1      | 0         | 0         | 0         | 0         | [5, 6, 7, 8]  |
| ModuleA | 2      | 0         | 0         | 0         | 1         | [5, 6, 7, 8]  |
| ...     | ...    | ...       | ...       | ...       | ...       | ...           |

## 示例

### 运行演示

```bash
poetry run python test_case_generator_demo.py
```

这将：
1. 创建示例参数模板
2. 生成测试用例
3. 显示详细结果
4. 保存到Excel文件

### 预期输出

```
=== 测试用例生成摘要 ===
模块 ModuleA: 160 个测试用例
  示例用例:
    用例1: (ARG_1=0, ARG_2=0, ARG_3=0, ARG_4=0)
    用例2: (ARG_1=0, ARG_2=0, ARG_3=0, ARG_4=1)
    用例3: (ARG_1=0, ARG_2=0, ARG_3=1, ARG_4=0)
    ... 还有 157 个用例
模块 ModuleB: 45 个测试用例
  示例用例:
    用例1: (ARG_1=0, ARG_2=0)
    用例2: (ARG_1=0, ARG_2=1)
    用例3: (ARG_1=0, ARG_2=50)
    ... 还有 42 个用例

总计: 205 个测试用例
```

## 计算说明

### 笛卡尔积计算

对于ModuleA的示例：
- ARG_1: [0, 1, 2, 3] (4个值)
- ARG_2: [0, 1, 2, 3] (4个值)  
- ARG_3: [0, 1, 50, 99, 100] (5个值)
- ARG_4: [0, 1] (2个值)

总测试用例数 = 4 × 4 × 5 × 2 = 160个

### 代表性值选择策略

对于大范围（如 `0~100`），选择策略：
- 起始值：0
- 起始值+1：1  
- 中间值：50
- 结束值-1：99
- 结束值：100

这样可以覆盖边界值和中间值，确保测试的全面性。

## 日志管理

### 日志级别

系统使用专业的日志管理，支持以下级别：

- **INFO**: 主要流程信息（文件读取、模块处理、测试用例生成等）
- **DEBUG**: 详细调试信息（参数解析结果、示例用例等）
- **WARNING**: 警告信息（大模型解析失败回退等）
- **ERROR**: 错误信息（文件读取失败、解析异常等）
- **SUCCESS**: 成功完成信息

### 日志示例

```
2025-07-16 19:35:04 | INFO     | module_domain_test:read_arg_file:49 - 成功读取文件，共3行数据
2025-07-16 19:35:04 | DEBUG    | module_domain_test:read_arg_file:50 - 列名: ['功能模块', '参数类型', '参数名称', '参数取值范围']
2025-07-16 19:35:06 | INFO     | module_domain_test:_parse_range_value_with_llm:139 - 大模型解析 '0~5' -> [0, 1, 2, 3, 4, 5], 理由: 因为这个范围的值总数（6个）少于10个，因此可以直接包含所有的具体值以确保全面覆盖。
2025-07-16 19:35:11 | INFO     | module_domain_test:generate_all_test_cases:299 - 处理模块: TestModule
2025-07-16 19:35:11 | INFO     | module_domain_test:generate_all_test_cases:306 - 生成测试用例数量: 12
```

## 注意事项

1. **内存使用**：大量参数组合可能产生大量测试用例，注意内存使用
2. **Excel限制**：Excel单个sheet最多支持1,048,576行
3. **文件格式**：确保输入Excel文件格式正确，包含必要的列
4. **参数范围**：确保参数范围格式正确，避免解析错误
5. **日志输出**：所有输出信息通过logger管理，可根据需要调整日志级别

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多参数范围格式
- 添加测试用例过滤功能
- 支持优先级测试用例生成
- 添加测试用例执行框架集成
