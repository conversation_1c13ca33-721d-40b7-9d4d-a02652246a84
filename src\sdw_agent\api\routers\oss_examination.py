#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : oss_examination.py
@Time    : 2025/7/17 14:48
<AUTHOR> <PERSON><PERSON> Ke
@Version : 1.0
@Desc    : 判断使用开源协议的代码片段是否需要继续开源并给出理由。
"""

from fastapi import APIRouter
from loguru import logger
from sdw_agent.service.oss_examination.oss_examination import OSSExaminationWorkflow
from sdw_agent.model.request_model import OSSExaminationRequest
from sdw_agent.model.response_model import OSSExaminationResponse

router = APIRouter(prefix="/api/sdw", tags=["Dev-OSS误混入审查"])

@router.post("/oss_examination",
             summary="OSS误混入审查",
             description="判断使用开源协议的代码片段是否需要继续开源并给出理由",
             response_description="返回一个填充后的excel文件",
             response_model=OSSExaminationResponse)
async def oss_examination(request: OSSExaminationRequest):
    file_path = request.file_path
    examiner = OSSExaminationWorkflow()
    try:
        save_path = await examiner.execute(file_path=file_path)
        return {'code': 200, 'message': '生成oss审查文件成功！', 'data': save_path}
    except Exception as e:
        logger.error(e)
        return {'code': 500, 'message': '生成oss审查文件失败！', 'data': ""}