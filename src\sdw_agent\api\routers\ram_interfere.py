"""
RAM干涉工作流

V字对应：
3.3.46. RAM干渉チェック実施＆結果検証

该模块提供RAM干涉检查功能，分析代码中的全局变量变更和RAM干涉情况。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量位置检索
3. RAM干涉确认
4. 生成RAM干涉检查报告
"""

from fastapi import APIRouter

from sdw_agent.service.ram_interfere_service import do_ram_interfere, RAMInterfereRequest

router = APIRouter(prefix="/api/sdw/ram_interfere", tags=["3.3.46. RAM干渉チェック実施＆結果検証"])


@router.post("/ram_interfere",
             summary="3.3.46. RAM干渉チェック実施＆結果検証",
             description="",
             response_description="")
async def ram_interfere(request: RAMInterfereRequest):
    """3.3.46. RAM干渉チェック実施＆結果検証"""
    repo_info = request.repoInfo

    ram_interfere_uri = do_ram_interfere(repo_info)
    return {
        "code": 0,
        "msg": "",
        "data": {
            "ram_interfere_uri": ram_interfere_uri
        }
    }
