# DEV Agent

DEV Agent 是一个开发助手工具，旨在提高开发效率并简化开发流程。它提供了一系列功能，包括API管理、配置管理和Shell工具等。 

## 项目结构

```
├── src
│   └── dev_agent
│       ├── api
│       │   ├── static - 静态资源文件（Swagger UI, Redoc）
│       │   ├── __init__.py - API模块初始化
│       │   └── server.py - 主要的API服务端代码
│       ├── config
│       │   ├── __init__.py - 配置模块初始化
│       │   ├── config.yaml - 主配置文件
│       │   ├── env.py - 环境配置
│       │   └── prompt.yaml - 提示信息配置
│       ├── shell
│       │   ├── __init__.py - Shell模块初始化
│       │   └── tt.py - 命令行工具文件
│       ├── streamlit
│       │   └── dashboard.py - Streamlit前端页面
│       ├── unit
│       │   └── __init__.py - 单元测试模块初始化
│       └── __init__.py - 项目根初始化文件
├── tests
│   └── __init__.py - 测试套件初始化
├── README.md - 项目说明文档
├── babel.cfg - 国际化配置文件
└── pyproject.toml - Python项目配置文件
```

## 功能特点

### API管理
- 提供RESTful API接口
- 支持Swagger和Redoc文档界面
- 可扩展的API服务架构

### 配置管理
- YAML格式配置文件
- 灵活的环境配置支持
- 提示信息可配置化

### Shell工具
- 提供命令行工具支持
- 易于扩展的Shell功能

### 前端界面
- 使用Streamlit构建的可视化仪表板
- 响应式布局设计
- 交互式功能支持

## 开发规范

### 运行环境
- Python 3.11
- Streamlit
- Flask
- PyYAML

### 安装依赖
```bash
pip install poetry
poetry update
```

### 启动应用
```bash
# 启动API服务
cd src/sdw_agent
python -m api.server

# 或者启动Streamlit前端
cd src/sdw_agent/streamlit
streamlit run dashboard.py
```

```shell
# 很稳的git专用
# 初始化
git config set --global user.name xuqiao
git config set --global user.email <EMAIL>
git clone ssh://root@*************/root/kotei-project/DEV_Agent.git
cd DEV_Agent
curl -Lo .git/hooks/commit-msg http://************:8080/tools/hooks/commit-msg
git checkout develop
git branch -u origin/develop
git branch base

# 开发过程循环
# start_loop
# todo 本地开发产生了一些提交...
# 将本地提交推送到远程仓库（如果存在冲突，可以在PyCharm上面尝试解决）
git fetch --all && git rebase --onto origin/develop base develop 
# todo 可能要解决冲突
# 推送提交
git push
# 重置base分支，为接下来基于新的base进行开发做准备
git branch -D base && git branch base
# end_loop

# 推送评审（管理员专用）
# 初始化（只需要执行一次）
git remote add gerrit ssh://qiao_xu@************:29418/technical-department/roc/roc_code/sdw-agent
# 推送评审
git push gerrit develop:refs/for/develop730
```

```shell
# gerrit 专用
# gerrit 初始化
# 1. 注册gerrit
# 2. 访问http://************:8080/settings/#EmailAddresses
# 3. 设置邮箱地址，发送验证链接激活
# 4. 访问http://************:8080/settings/#SSHKeys
# 5. 设置公钥，保证公钥后面的邮箱地址和前面一致

# 初始化
# 访问http://************:8080/admin/repos/technical-department/roc/roc_code/sdw-agent,general
git clone "ssh://qiao_xu@************:29418/technical-department/roc/roc_code/sdw-agent" && (cd "sdw-agent" && mkdir -p `git rev-parse --git-dir`/hooks/ && curl -Lo `git rev-parse --git-dir`/hooks/commit-msg http://************:8080/tools/hooks/commit-msg && chmod +x `git rev-parse --git-dir`/hooks/commit-msg)
cd sdw-agent
git checkout develop730
git branch -u origin/develop730
git branch base

# 开发过程循环
# start_loop
# todo 本地开发产生了一些提交...
# 将本地提交推送到远程仓库（如果存在冲突，可以在PyCharm上面尝试解决）
git fetch --all && git rebase --onto origin/develop730 base develop730 
# todo 可能要解决冲突
# 推送提交并评审
git push develop730:refs/for/develop730
# todo 等待评审通过
# 重置base分支，为接下来基于新的base进行开发做准备
git branch -D base && git branch base
# end_loop

```
