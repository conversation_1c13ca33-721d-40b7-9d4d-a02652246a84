"""
Dynamic rule implementation for document parsing.
"""

import json
import re
from typing import Any, Dict, Callable, Optional, List, Union

from docparser.interfaces.rule_interface import Rule, TextRule, TableRule, PictureRule, GraphicRule, DocumentRule

class DynamicRule(Rule):
    """
    Dynamic rule implementation.
    Allows rules to be defined dynamically from JSON configuration.
    """
    
    def __init__(self, rule_id: str, name: str, description: str, priority: int = 0, enabled: bool = True):
        """
        Initialize dynamic rule.
        
        Args:
            rule_id: Rule ID
            name: Rule name
            description: Rule description
            priority: Rule priority (higher priority rules are applied first)
            enabled: Whether the rule is enabled
        """
        self._rule_id = rule_id
        self._name = name
        self._description = description
        self._priority = priority
        self._enabled = enabled
        self._condition_func = lambda obj: True  # Default condition: always apply
        self._transform_func = lambda obj: obj   # Default transform: no change
    
    def get_rule_id(self) -> str:
        """
        Get rule ID.
        
        Returns:
            Rule ID
        """
        return self._rule_id
    
    def get_rule_name(self) -> str:
        """
        Get rule name.
        
        Returns:
            Rule name
        """
        return self._name
    
    def get_rule_description(self) -> str:
        """
        Get rule description.
        
        Returns:
            Rule description
        """
        return self._description
    
    def get_rule_priority(self) -> int:
        """
        Get rule priority.
        Higher priority rules are applied first.
        
        Returns:
            Rule priority
        """
        return self._priority
    
    def get_rule_condition(self) -> Callable[[Dict[str, Any]], bool]:
        """
        Get rule condition function.
        
        Returns:
            Function that takes an object and returns True if the rule should be applied
        """
        return self._condition_func
    
    def set_condition(self, condition_func: Callable[[Dict[str, Any]], bool]) -> None:
        """
        Set rule condition function.
        
        Args:
            condition_func: Function that takes an object and returns True if the rule should be applied
        """
        self._condition_func = condition_func
    
    def apply_rule(self, target_object: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply rule to target object.
        
        Args:
            target_object: Object to apply rule to
            
        Returns:
            Modified object
        """
        return self._transform_func(target_object)
    
    def set_transform(self, transform_func: Callable[[Dict[str, Any]], Dict[str, Any]]) -> None:
        """
        Set rule transform function.
        
        Args:
            transform_func: Function that takes an object and returns a modified object
        """
        self._transform_func = transform_func
    
    def is_enabled(self) -> bool:
        """
        Check if rule is enabled.
        
        Returns:
            True if rule is enabled, False otherwise
        """
        return self._enabled
    
    def enable(self) -> None:
        """Enable rule."""
        self._enabled = True
    
    def disable(self) -> None:
        """Disable rule."""
        self._enabled = False
    
    def to_json(self) -> Dict[str, Any]:
        """
        Convert rule to JSON.
        
        Returns:
            JSON representation of rule
        """
        return {
            'id': self._rule_id,
            'name': self._name,
            'description': self._description,
            'priority': self._priority,
            'target': self.get_rule_target(),
            'enabled': self._enabled
        }
    
    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> 'DynamicRule':
        """
        Create rule from JSON.
        
        Args:
            data: JSON data
            
        Returns:
            Rule instance
        """
        # Create rule instance
        rule = cls(
            rule_id=data.get('id', 'unknown'),
            name=data.get('name', 'Unnamed Rule'),
            description=data.get('description', ''),
            priority=data.get('priority', 0),
            enabled=data.get('enabled', True)
        )
        
        # Set condition function
        condition = data.get('condition')
        if condition:
            rule.set_condition(cls._create_condition_function(condition))
        
        # Set transform function
        transform = data.get('transform')
        if transform:
            rule.set_transform(cls._create_transform_function(transform))
        
        return rule
    
    @staticmethod
    def _create_condition_function(condition: Dict[str, Any]) -> Callable[[Dict[str, Any]], bool]:
        """
        Create condition function from JSON.
        
        Args:
            condition: Condition specification
            
        Returns:
            Condition function
        """
        condition_type = condition.get('type', 'always')
        
        if condition_type == 'always':
            return lambda obj: True
        
        elif condition_type == 'never':
            return lambda obj: False
        
        elif condition_type == 'field_exists':
            field = condition.get('field', '')
            return lambda obj: field in obj
        
        elif condition_type == 'field_equals':
            field = condition.get('field', '')
            value = condition.get('value')
            return lambda obj: field in obj and obj[field] == value
        
        elif condition_type == 'field_contains':
            field = condition.get('field', '')
            value = condition.get('value', '')
            return lambda obj: field in obj and isinstance(obj[field], str) and value in obj[field]
        
        elif condition_type == 'field_matches':
            field = condition.get('field', '')
            pattern = condition.get('pattern', '')
            regex = re.compile(pattern)
            return lambda obj: field in obj and isinstance(obj[field], str) and regex.search(obj[field]) is not None
        
        elif condition_type == 'and':
            subconditions = condition.get('conditions', [])
            subfuncs = [DynamicRule._create_condition_function(c) for c in subconditions]
            return lambda obj: all(f(obj) for f in subfuncs)
        
        elif condition_type == 'or':
            subconditions = condition.get('conditions', [])
            subfuncs = [DynamicRule._create_condition_function(c) for c in subconditions]
            return lambda obj: any(f(obj) for f in subfuncs)
        
        elif condition_type == 'not':
            subcondition = condition.get('condition', {})
            subfunc = DynamicRule._create_condition_function(subcondition)
            return lambda obj: not subfunc(obj)
        
        else:
            # Default to always apply
            return lambda obj: True
    
    @staticmethod
    def _create_transform_function(transform: Dict[str, Any]) -> Callable[[Dict[str, Any]], Dict[str, Any]]:
        """
        Create transform function from JSON.
        
        Args:
            transform: Transform specification
            
        Returns:
            Transform function
        """
        transform_type = transform.get('type', 'identity')
        
        if transform_type == 'identity':
            return lambda obj: obj
        
        elif transform_type == 'set_field':
            field = transform.get('field', '')
            value = transform.get('value')
            return lambda obj: {**obj, field: value}
        
        elif transform_type == 'remove_field':
            field = transform.get('field', '')
            
            def remove_field(obj):
                result = obj.copy()
                if field in result:
                    del result[field]
                return result
            
            return remove_field
        
        elif transform_type == 'rename_field':
            old_field = transform.get('old_field', '')
            new_field = transform.get('new_field', '')
            
            def rename_field(obj):
                result = obj.copy()
                if old_field in result:
                    result[new_field] = result[old_field]
                    del result[old_field]
                return result
            
            return rename_field
        
        elif transform_type == 'replace_text':
            field = transform.get('field', '')
            pattern = transform.get('pattern', '')
            replacement = transform.get('replacement', '')
            regex = re.compile(pattern)
            
            def replace_text(obj):
                result = obj.copy()
                if field in result and isinstance(result[field], str):
                    result[field] = regex.sub(replacement, result[field])
                return result
            
            return replace_text
        
        elif transform_type == 'chain':
            subtransforms = transform.get('transforms', [])
            subfuncs = [DynamicRule._create_transform_function(t) for t in subtransforms]
            
            def chain_transforms(obj):
                result = obj
                for func in subfuncs:
                    result = func(result)
                return result
            
            return chain_transforms
        
        else:
            # Default to identity transform
            return lambda obj: obj

class DynamicTextRule(DynamicRule, TextRule):
    """Dynamic rule for text objects."""
    pass

class DynamicTableRule(DynamicRule, TableRule):
    """Dynamic rule for table objects."""
    pass

class DynamicPictureRule(DynamicRule, PictureRule):
    """Dynamic rule for picture objects."""
    pass

class DynamicGraphicRule(DynamicRule, GraphicRule):
    """Dynamic rule for graphic objects."""
    pass

class DynamicDocumentRule(DynamicRule, DocumentRule):
    """Dynamic rule for document objects."""
    pass
