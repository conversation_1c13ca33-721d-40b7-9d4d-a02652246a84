"""
函数分析工作流数据模型
"""
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field


@dataclass
class FunctioncalledData:
    """函数调用数据"""
    DomainName: str = ""
    file_name: str = ""
    func_name: str = ""
    func_desc: str = ""


@dataclass
class FunctionAnalyzeBookConfig:
    """函数分析书配置模型"""
    code_path: str
    commit_id_after: str
    commit_id_before: str = ""
    component_book_url: str = ""
    gerrit_url: str = ""
    username: str = ""
    password: str = ""
    git_branch: str = "develop"

    def __post_init__(self):
        """配置验证"""
        if not self.code_path:
            raise ValueError("代码路径不能为空")
        if not self.commit_id_after:
            raise ValueError("目标提交ID不能为空")


@dataclass
class FunctionAnalyzeBookResult:
    """函数分析书结果模型"""
    func_book_url: str = ""
    func_change_info_list: List[Any] = field(default_factory=list)
    success: bool = False
    message: str = ""
    error_details: Optional[str] = None


@dataclass
class GitDiffInfo:
    """Git差异信息"""
    file_path: str
    added_lines: List[str] = field(default_factory=list)
    full_code: List[str] = field(default_factory=list)
    line_numbers: List[int] = field(default_factory=list)


@dataclass
class FunctionChangeInfo:
    """函数变更信息"""
    function_name: str
    function_sign: str
    function_start: int
    function_end: int
    file_path: str = ""


@dataclass
class ComponentData:
    """组件数据"""
    file_path: str
    component_name: str


class FunctionAnalyzeBookRequest(BaseModel):
    """API请求模型"""
    code_path: str
    commit_id_after: str
    commit_id_before: Optional[str] = ""
    component_book_url: str = ""
    gerrit_url: Optional[str] = ""
    git_branch: Optional[str] = "develop"


class FunctionAnalyzeBookResponse(BaseModel):
    """API响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any] = {}


@dataclass
class FuncDescResult(BaseModel):
    """
    变更类型结果
    """
    design_desc: str = Field(description="对于函数的功能描述")


@dataclass
class FuncReturnResult(BaseModel):
    """
    变更类型结果
    """
    return_desc: str = Field(description="对于函数的返回值功能描述")


@dataclass
class FuncVarReturnResult(BaseModel):
    """
    变更类型结果
    """
    return_desc: str = Field(description="对于函数的变量功能描述")


@dataclass
class FuncVarReturnResult(BaseModel):
    """
    变更类型结果
    """
    return_desc: str = Field(description="对于函数的变量功能描述")


@dataclass
class FuncProcessReturnResult(BaseModel):
    """
    变更类型结果
    """
    func_proc_desc: str = Field(description="对于函数的流程描述")
    func_proc_UML: str = Field(description="对于函数的流程的plantUML描述结果")
    func_proc_COT: str = Field(description="plantUML描述结果拆解的过程")


@dataclass
class FuncParamResult(BaseModel):
    """
    变更类型结果
    """
    param_desc: str = Field(description="对于函数的入参功能描述")


@dataclass
class FuncCalledResult(BaseModel):
    """
    变更类型结果
    """
    called_desc: str = Field(description="对于父类函数的调用意图的描述")


@dataclass
class FunctionParameter:
    """表示函数参数的元数据信息

    Attributes:
        type: 参数数据类型
        name: 参数名称
        direction: 参数方向（输入/输出/双向）
        desc: 参数描述（默认空字符串）
    """
    type: str
    name: str
    direction: str  # in/out/inout
    desc: str = ""


@dataclass
class FunctionDeclaration:
    """表示函数声明的元数据信息

    Attributes:
        name: 函数名称
        return_type: 返回值类型
        parameters: 参数列表（FunctionParameter对象的列表）
    """
    name: str
    return_type: str
    parameters: List[FunctionParameter]


@dataclass
class FunctionDetailData:
    """表示函数详细信息的元数据容器

    Attributes:
        return_type: 返回值类型（默认空字符串）
        return_var: 返回值变量名（默认空字符串）
        return_desc: 返回值描述（默认空字符串）
        func_name: 函数名称（默认空字符串）
        func_sign: 函数签名（默认空字符串）
        func_body: 函数体代码（默认空字符串）
        func_desc: 函数描述（默认空字符串）
        func_process: 函数处理逻辑描述（默认空字符串）
        func_file_name: 函数所在文件名（默认空字符串）
        functionType: 函数类型（默认空字符串）
        parameters: 参数列表（FunctionParameter对象的列表，默认None）
        allVariables: 所有变量的字典映射（默认None）
        func_parents: 函数父节点的字典映射（默认None）
    """
    return_type: str = ""
    return_var: str = ""
    return_desc: str = ""
    func_name: str = ""
    func_sign: str = ""
    func_body: str = ""
    func_desc: str = ""
    func_process: str = ""
    func_file_name: str = ""
    functionType: str = ""  # 默认值
    parameters: List[FunctionParameter] = None
    allVariables: Dict[str, List] = None
    func_parents: Dict[str, List] = None
# ... existing code ...
