"""
RAM确认工作流

V字对应：
4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証

该模块提供RAM确认功能，根据Gerrit Diff文档获取变更的全局变量并确认初始化时机。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量初始化位置检索，并确认初始化时机
3. 生成RAM确认报告
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from loguru import logger

from sdw_agent.service.ram_confirm_service import do_ram_confirm, RAMConfirmRequest

router = APIRouter(prefix="/api/sdw/ram_confirm", tags=["4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証"])


@router.post("/ram_confirm",
             summary="4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証",
             description="根据Gerrit Diff文档，获取变更的全局变量并确认初始化时机",
             response_description="RAM确认报告文件路径")
async def ram_confirm(request: RAMConfirmRequest) -> Dict[str, Any]:
    """
    4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証
    
    Args:
        request: RAM确认请求参数
        
    Returns:
        Dict: 包含报告文件路径的响应
    """

    try:
        logger.info(f"开始执行RAM确认: {request.repoInfo.repo_path}")

        # 使用兼容函数
        ram_confirm_uri = do_ram_confirm(request.repoInfo)

        return {
            "code": 0,
            "msg": "RAM确认执行成功",
            "data": {
                "ram_confirm_uri": ram_confirm_uri
            }
        }

    except Exception as e:
        logger.exception(f"RAM确认执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
