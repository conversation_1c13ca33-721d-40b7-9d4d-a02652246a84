# IF整合性确认工作流配置

# 基本配置
name: "新变表更新"
description: "提供新变表更新功能，检查代码中新变表的更新内容"
version: "1.0.0"
author: "SDW-Team"

#用户配置
user_config:
  ncl_gerrit_url: "http://************:8080"
  ncl_gerrit_username: "cr_robot"
  ncl_gerrit_password: "ocsa@2024!"

# 处理参数
ncl_update:
  excel:
    start_row: 8
    columns:
      aw: 49  # AV列
      p: 16
    sheet_name: "機能一覧と新規・変更内容"
    default_values:
      display_size: "12.3"
      initial_status: "要help"
      vehicle_code: "-"
      sqa_status: "-"
      change_scope: "要help"
      pf_change: "無"
    field_mapping:
      Display_size: 16
      ソフトの変更内容: 17
      変更対象コンポーネント: 18
      新変定移植: 19
      車両コード: 20
      SQA完了状況: 21
      行数: 22
      変更範囲: 23
      PF同時変更: 25
      PF同時変更2: 26
# LLM配置
codereviewpoint:
  1: >
    todo
  2: >
    todo
  3: >
    todo
  4: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在对以下数据类型的访问的变更:
    2字节以上数据中的部分字节/位字段/结构体/联合体的元素或整体
    如果不存在请回复：不存在
    如果存在，需要考虑所用微控制器的字节顺序（大端模式/小端模式）和位顺序（高位优先/低位优先），确认是否能够正确访问预期的地址和位位置。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  5: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在需按固定数据大小(1字节、2字节、4字节单位)写入的区域,且其中包含未定义的位字段。
    如果不存在请回复：不存在
    如果存在，需确认该未定义位字段中写入的值对微控制器/IC的运行/设置的影响。若影响不明确，需向制造商确认。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  6: >
    todo
  7: >
    todo
  8: >
    todo
  9: >
    todo
  10: >
    todo
  11: >
    todo
  12: >
    todo
  13: >
    todo
  14: >
    todo
  15: >
    todo
  16: >
    todo
  17: >
    todo
  18: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在输入→输出特性曲线的变更？特性曲线是指的类似于输入电压与输出电压的对应关系（在车载仪表中，通常是亮度曲线,蜂鸣曲线等）。
    如果不存在请回复：不存在
    如果存在，确认除法余数的处理方式（截断、四舍五入、进位），同时需考虑映射转换时的量化误差。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  19: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在输入→输出特性曲线的变更？特性曲线是指的类似于输入电压与输出电压的对应关系（在车载仪表中，通常是亮度曲线,蜂鸣曲线等）。
    如果不存在请回复：不存在
    如果存在，特性值、要求规格与设计结果需通过图表对照并确保一致性。关于要求规格MAP,需将要求规格→最终输出的特性进行图表化,实施公差及误差确认。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  20: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在外部函数调用的变更？
    如果不存在请回复：不存在
    如果调用了外部函数，需确认该函数是否是需要成对使用的函数。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  21: >
    todo
  22: >
    todo
  23: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在通过运算求取指针地址的变更？
    如果不存在请回复：不存在
    如果存在通过运算求取指针地址,需确认计算结果是否正确，地址计算会因数据长度不同而变化。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  24: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在加权平均运算的变更？
    如果不存在请回复：不存在
    如果存在加权平均计算,需要设计为储存累计值，若存储平均值，可能会因位数丢失导致无法达到所需精度。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  25: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在数值运算的变更？
    如果不存在请回复：不存在
    如果存在数值运算,在进行数值计算时,需确认运算精度是否存在异常,需在RAM定义书中明确数据的LSB,并确认不会因位数丢失导致运算精度不足
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  26: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在数值运算的变更？
    如果不存在请回复：不存在
    如果存在数值运算,在进行数值计算时,需确认数据范围是否存在异常,需在RAM定义书中明确数据范围,并确认计算过程中不会发生overflow/underflow。
    若有意使用overflow/underflow,需在设计书中记录相关动作
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  27: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在数值运算的变更？
    如果不存在请回复：不存在
    如果存在数值运算,在进行数值运算时,需确认各项数据的单位及LSB是否存在矛盾,并确认计算式及各项数据和常数的定义。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  28: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在有符号的变量以及有符号变量的运算的变更？
    如果不存在请回复：不存在
    如果存在有符号变量的运算,需验证各变量及计算结果为0、±1的情况。
    注意,变量通过S1,S2,S4定义的变量为有符号变量。
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  29: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    是否存在运算和输入数据的变更？
    如果不存在请回复：不存在
    如果存在运算,是否对运算实施了必要的保护处理？
    如果存在输入数据,必须假定范围外数据的输入,对范围外的数据实施保护处理,是否进行了这样的处理？
    
    ## 输出规范
    请按照以下结构化格式用中文回复：
    ### 结论
    [存在/不存在]
    ### 分析依据
    详细说明得出该结论的理由
  30: >
    ## 输入数据
    ### 变更代码片段
    {{code_after}}

    ### 完整代码上下文
    {{code_full}}

    ## 分析要求
    请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
    代码在文件中是否新增RAM或变更RAM?
    如果不存在,请回复：不存在
    如果存在,给出所有新增RAM或变更RAM的变更位置,并对于RAM异常情况的防护处理进行确认。
    
    ## 输出规范
    请给出审查结果，判断是否存在(status)和原因(reason):