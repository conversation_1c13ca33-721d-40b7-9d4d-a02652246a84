import re
import logging
from typing import Any, Dict, List, Optional, Union

from docparser.interfaces.plugin_interface import PluginInterface
from docparser.models.document import DocumentBlockObject, DocumentObject
from docparser.models.table import TableObject

# Configure logging
logger = logging.getLogger('docparser.plugins.merge_col')

class MergeColPlugin(PluginInterface):
    """
    Plugin for analyzing text objects in documents.
    Adds metadata about text content such as word count, character count, etc.
    """

    def get_plugin_name(self) -> str:
        """Get plugin name"""
        return "MergeCol"

    def get_plugin_version(self) -> str:
        """Get plugin version"""
        return "1.0.0"

    def get_supported_document_types(self) -> List[str]:
        """Get supported document types"""
        return ["xlsx", "xls", "xlsm"]  # Support excel

    def process_document(self, document: DocumentObject) -> DocumentObject:
        """
        Process document data.

        Args:
            document: Dictionary containing parsed document data

        Returns:
            Processed document data
        """
        logger.info("Processing document with TextAnalyzer plugin")
        for doc in document.document:
            doc.tables = self.process_table_objects(doc.tables)

        return document

    def process_table_objects(self, table_objects: List[TableObject]) ->  List[TableObject]:
        """
        Process text objects.

        Args:
            table_objects: List of text objects

        Returns:
            Processed text objects
        """
        res = []
        for table in table_objects:
            self._do_merge_col(table)
            res.append(table)
        return res

    def _is_ignore_merge(self, table: TableObject):
        if not table.rows:
            return True
        if table.rows[0].cells:
            first_cell = table.rows[0].cells[0]
            block = first_cell
            while not isinstance(block, DocumentBlockObject) and block and hasattr(block, 'layout'):
                block = block.layout.parent_ref
            if block and block.name == 'Coversheet':
                return True
        return False

    def _do_merge_col(self, table: TableObject) -> TableObject:
        if self._is_ignore_merge(table):
            return table
        merge_ranges = table.get_merged_ranges()
        if not merge_ranges or len(merge_ranges) < len(table.rows):
            return table
        merge_col = self._get_merge_col(merge_ranges, len(table.rows))
        if not merge_col:
            return table
        # 统计需要去除的列索引
        del_col = []
        for item in merge_col:
            del_col.extend([i for i in range(item[0] + 1, item[1] + 1)])
        temp_head_list = []
        for row_index, row in enumerate(table.rows):
            col_index = 0
            # 组装新的cells
            new_cells = []
            for cell in row.cells:
                if cell.col_index not in del_col:
                    # 如果是竖表头且识别出表头，需要更新表头的索引
                    if row_index == 0 and table.head_type == 'vertical' and cell.col_index in table.head_list:
                        temp_head_list.append(col_index)
                    new_cells.append(cell)
                    cell.col_index = col_index
                    col_index += 1
            row.cells = new_cells
        if table.head_type == 'vertical' and set(table.head_list) & set(del_col):
            table.head_list = temp_head_list
        return table

    def _get_merge_col(self, merge_ranges, row_num):
        result = []
        # 对每一列的合并范围进行统计
        merged_cols = {}
        for item_range in merge_ranges:
            # 检查每一个单元格的行范围是否只有一行
            if item_range[2] - item_range[0] == 0:
                # 获取合并范围
                merged_range = tuple(item_range[1:4:2])
                if merged_range not in merged_cols:
                    merged_cols[merged_range] = []
                merged_cols[merged_range].append(1)

        # 检查哪些列的合并范围覆盖了整个表格的行数
        for merged_range, rows in merged_cols.items():
            if len(rows) == row_num:
                result.append((merged_range[0], merged_range[1]))
        return result