#!/usr/bin/env python3
"""
手动截图后插入 Excel
适用于无法自动导出的情况
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from openpyxl.drawing.image import Image as OpenpyxlImage

from sdw_agent.util.excel.core import ExcelUtil, CellStyle


def insert_manual_image_to_excel(image_file: str,
                                excel_file: str,
                                sheet_name: str = "架构图",
                                title: Optional[str] = None,
                                max_width: int = 1200,
                                max_height: int = 800) -> Dict[str, Any]:
    """
    将手动截图的图像插入到 Excel 中
    
    Args:
        image_file: 图像文件路径（PNG, JPG等）
        excel_file: Excel 文件路径
        sheet_name: 工作表名称
        title: 图表标题
        max_width: 图像最大宽度
        max_height: 图像最大高度
        
    Returns:
        操作结果
    """
    try:
        if not Path(image_file).exists():
            return {
                "success": False,
                "message": f"图像文件不存在: {image_file}",
                "excel_file": excel_file
            }
        
        with ExcelUtil(excel_file, auto_create=True) as excel:
            # 确保工作表存在
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            current_row = 1
            
            # 添加标题（如果提供）
            if title:
                excel.write_cell(sheet_name, current_row, 1, title)
                title_style = CellStyle(
                    font_size=16,
                    font_bold=True,
                    alignment_horizontal="center"
                )
                excel.set_cell_style(sheet_name, current_row, 1, title_style)
                current_row += 2  # 留出空行
            
            # 插入图像
            from sdw_agent.util.drawio_util.export_original_image import insert_image_to_excel_sheet
            success = insert_image_to_excel_sheet(
                excel=excel,
                sheet_name=sheet_name,
                image_path=image_file,
                row=current_row,
                col=1,
                max_width=max_width,
                max_height=max_height
            )
            
            if success:
                excel.save()
                logger.success(f"图像已成功插入到 Excel: {excel_file}")
                return {
                    "success": True,
                    "message": "图像插入成功",
                    "excel_file": excel_file,
                    "sheet_name": sheet_name,
                    "image_position": f"行{current_row}, 列1"
                }
            else:
                return {
                    "success": False,
                    "message": "图像插入 Excel 失败",
                    "excel_file": excel_file
                }
                
    except Exception as e:
        error_msg = f"插入图像到 Excel 时发生异常: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "message": error_msg,
            "excel_file": excel_file
        }


def demo_manual_insert():
    """演示手动插入图像"""
    print("🎯 手动插入图像演示")
    print("=" * 50)
    print("📋 使用步骤:")
    print("1. 在 draw.io 中打开你的文件")
    print("2. 导出为 PNG 图像（File -> Export as -> PNG）")
    print("3. 将图像保存到 output/manual_screenshot.png")
    print("4. 运行此脚本")
    print()
    
    # 检查是否有手动截图
    screenshot_path = Path("output/manual_screenshot.png")
    if not screenshot_path.exists():
        print(f"❌ 未找到截图文件: {screenshot_path}")
        print("💡 请按照上述步骤先创建截图文件")
        return
    
    print(f"✅ 找到截图文件: {screenshot_path}")
    
    # 插入到 Excel
    excel_file = Path("output/manual_architecture_diagram.xlsx")
    
    result = insert_manual_image_to_excel(
        image_file=str(screenshot_path),
        excel_file=str(excel_file),
        sheet_name="手动截图架构图",
        title="系统架构图（手动截图版本）",
        max_width=1400,
        max_height=1000
    )
    
    # 显示结果
    print(f"\n📊 插入结果:")
    print(f"   成功: {result['success']}")
    print(f"   消息: {result['message']}")
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   图像位置: {result['image_position']}")
        print(f"\n🎉 请打开 Excel 文件查看图表！")
    else:
        print(f"   错误详情: {result['message']}")


if __name__ == "__main__":
    demo_manual_insert()
