[project]
name = "sdw-agent"
version = "0.1.6"
description = "sdw-agent is a tool for developers"
authors = [
    { name = "x<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11,<3.12"
dependencies = [
    "loguru (>=0.7.3,<0.8.0)",
    "dynaconf (>=3.2.11,<4.0.0)",
    "fastapi-babel (>=1.0.0,<2.0.0)",
    "fastapi[standard] (>=0.115.14,<0.116.0)",
    "pandas (>=2.3.0,<3.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "langchain (>=0.3.26,<0.4.0)",
    "langchain-openai (==0.3.27)",
    "langgraph (==0.3.17)",
    "langserve[all] (>=0.3.1,<0.4.0)",
    "networkx (>=3.5,<4.0)",
    "xlrd (>=2.0.2,<3.0.0)",
    "pygerrit2 (>=0.0.0)",
    "jira (>=3.8.0,<4.0.0)",
    "cryptography (>=45.0.5,<46.0.0)",
    "xlwings (>=0.33.15,<0.34.0)",
    "tabulate (>=0.9.0,<0.10.0)",
    "flasgger (==*******)",
    "flask-babel (==4.0.0)",
    "flask-session (==0.8.0)",
    "websockets (==12.0)",
    "unidic-lite (==1.0.8)",
    "mecab-python3 (==1.0.10)",
    "anytree (==2.13.0)",
    "grandalf (==0.8)",
    "pywin32 (>=310,<311)",
    "chardet (>=5.2.0,<6.0.0)",
    "lightrag-hku[api] (>=1.4.2,<2.0.0)",
    "nest-asyncio (>=1.6.0,<2.0.0)",
    "scipy (>=1.16.1,<2.0.0)",
    "pyperclip (>=1.9.0,<2.0.0)",
    "paramiko (>=3.5.1,<4.0.0)",
    "bs4 (>=0.0.2,<0.0.3)",
    "playwright (>=1.54.0,<2.0.0)",

]


[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pandas-stubs = "^2.3.0.250703"



[tool.poetry.group.docparser.dependencies]
colorama = "^0.4.6"
contourpy = "^1.3.2"
coverage = "^7.9.2"
cycler = "^0.12.1"
et-xmlfile = "^2.0.0"
fonttools = "^4.58.5"
imagehash = "^4.3.2"
iniconfig = "^2.1.0"
kiwisolver = "^1.4.8"
lxml = "^6.0.0"
matplotlib = "^3.10.3"
more-itertools = "^10.7.0"
numpy = "^2.3.1"
pandas = "^2.3.0"
pillow = "^11.3.0"
pluggy = "^1.6.0"
psutil = "^7.0.0"
pyparsing = "^3.2.3"

[tool.poetry]
packages = [
    { include = "sdw_agent", from = "src" },
    { include = "docparser", from = "src" },
]

[tool.poetry.scripts]
# dev_server即将弃用，使用sdw_server代替
dev_server = "sdw_agent.api.server:main"
sdw_server = "sdw_agent.api.server:main"

[[tool.poetry.source]]
name = "mirrors"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
