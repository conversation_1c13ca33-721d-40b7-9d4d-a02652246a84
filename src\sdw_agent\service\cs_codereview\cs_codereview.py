"""
模块名称 selfchceck and code review

V字对应：【FID_DEV_0028】基于代码审核观点的自动化代码检查

主要功能：
基于Git差分代码和预定义的代码自检审核观点，通过AI模型自动生成审核观点检查报告。

"""

import sys
import datetime
import openpyxl
import shutil

from pathlib import Path
# project_root = Path(__file__).parent.parent.parent.parent
# sys.path.append(str(project_root))
from loguru import logger
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from typing import Optional  # Dict, List, Any, Optional
from sdw_agent.config.env import ENV  # noqa
from sdw_agent.service.DN_codecheck.dn_codecheck import CodeCheckerWorkflow
from openpyxl.styles import Font, colors


@register_workflow("cs_codereview")
class CodeReviewWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.REVISION_ID = "current"
        self.prompt = ""

    def validate_input(self, change_id) -> bool:
        """
        验证输入参数

        Args:
            input_data: 输入数据模型

        Returns:
            bool: 验证是否通过
        """
        try:
            self.logger.info("输入参数验证通过")
            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, change_id):
        """
        执行代码检查并返回结果文件路径

        Args:
            change_id (str): Gerrit 变更ID

        Returns:
            str: 分析结果文件路径
        """
        current_dir = Path(__file__).parent
        config_path = current_dir / "config.yaml"

        checker = CodeCheckerWorkflow(str(config_path))
        # checker = CodeCheckerWorkflow("./config.yaml")
        diff_info = checker.get_gerrit_diff_code(change_id)
        save_file_path = copy_template_sheet(change_id)
        for file_name, info in diff_info.items():
            if file_name == 'COMMIT_MSG':
                continue

            file_content = ''.join([line + '\n' for line in info['code_all']])
            file_content_after = ''.join([line + '\n' for line in info['code_after']])

            results = checker.send_to_llm(file_content_after, file_content)
            # for i, result in enumerate(results):
            #     result_array.append(f"{file_name}:{result}")
            Write2OutputExcel(results, file_name, save_file_path)
        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message="代码自我检查成功",
            data={"file_path": str(save_file_path)}
            )


def copy_template_sheet(change_id: str):
    # 获取当前文件所在目录
    current_dir = Path(__file__).parent
    # 构建模板文件路径
    excel_template = current_dir / 'doc_temp' / '【新模板】セルフチェック＆コードレビュー観点CS.xlsx'

    # 使用 ENV 中的配置路径
    output_dir = Path(ENV.config.output_data_path) / "cs_codereview"
    output_dir.mkdir(parents=True, exist_ok=True)  # 确保目录存在

    # 生成带 change_id 和时间戳的文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    save_file_name = f"【新模板】セルフチェック＆コードレビュー観点CS_{change_id}_{timestamp}.xlsx"
    save_file_path = output_dir / save_file_name

    # 检查模板文件是否存在
    if not excel_template.exists():
        raise FileNotFoundError(f"模板文件不存在: {excel_template}")

    # 复制模板文件
    shutil.copy2(excel_template, save_file_path)

    return str(save_file_path)


# def Write2OutputExcel(content: list[str], file_name: str, output_file_path: str):
#     # 初始化 ExcelUtil（建议使用 win32com 引擎，适合写入和样式操作）
#     excel = ExcelUtil(output_file_path, engine="openpyxl")

#     template_sheet_name = "コードレビュー観点CS"

#     # 构造新 Sheet 名称（最多 31 字符）
#     sheet_name = f"コードレビュー観点CS_{file_name}"
#     sheet_name = sheet_name[:31]
#     if sheet_name in excel.get_sheet_names():
#         sheet_name = f"{sheet_name}_copy"  # 避免重复

#     # 复制模板 Sheet
#     excel.copy_sheet(template_sheet_name, sheet_name)

#     # 创建蓝色字体样式
#     # blue_style = CellStyle(font_color="0000FF")  # 蓝色字体

#     # 写入数据
#     for i in range(len(content)):
#         if 'None' not in content[i]:
#             # 判断是否包含“不存在”，设置“否”或“要”
#             value = '否' if '不存在' in content[i] else '要'
#             # excel.set_cell_style(sheet_name, i + 9, 8, blue_style)  # 设置 H 列字体为蓝色
#             excel.write_cell(sheet_name, i + 9, 8, value)  # 写入“要”或“否”
#             excel.write_cell(sheet_name, i + 9, 9, content[i])  # 写入内容到 I 列

#     # 保存文件
#     excel.save()


def Write2OutputExcel(content: list[str], file_name: str, output_file_path: str):
    # 加载目标文件
    workbook = openpyxl.load_workbook(output_file_path)
    template_sheet = workbook["コードレビュー観点CS"]

    # 构造新 Sheet 名称
    sheet_name = f"コードレビュー観点CS_{file_name}"
    sheet_name = sheet_name[:31]  # Excel Sheet 名称最多 31 字符
    if sheet_name in workbook.sheetnames:
        sheet_name = f"{sheet_name}_copy"  # 避免重复

    # 复制模板 Sheet
    new_sheet = workbook.copy_worksheet(template_sheet)
    new_sheet.title = sheet_name

    # 移动到最前面
    workbook.move_sheet(new_sheet, offset=-3)

    # 创建蓝色字体样式
    blue_font = Font(color=colors.BLUE)

    # 写入数据
    for i in range(0, len(content)):
        if 'None' not in content[i]:
            if ('不存在' in content[i]):
                new_sheet.cell(row=i+9, column=8).value = '否'
            else:
                new_sheet.cell(row=i+9, column=8).value = '要'
            new_sheet.cell(row=i+9, column=8).font = blue_font
            new_sheet.cell(row=i+9, column=9).value = content[i]

    # 保存文件
    workbook.save(output_file_path)
    workbook.close()


if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python script.py <change_id>")
        sys.exit(1)
    change_id = sys.argv[1]
    workflow = CodeReviewWorkflow()
    result_path = workflow.run(change_id)
    print(f"Report saved to: {result_path}")