# -*- coding: utf-8 -*-
"""
@File    : new_parser.py
<AUTHOR> zhenp
@Date    : 2025-05-30 10:29
@Desc    : Description of the file
"""
import logging
import os
from datetime import datetime
from typing import Dict, <PERSON><PERSON>, List

import openpyxl
from openpyxl.cell.rich_text import Text<PERSON><PERSON>, CellRichText
from openpyxl.cell.text import InlineFont
from openpyxl.styles.colors import WHIT<PERSON>, BLACK
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.cell import Cell

from build.lib.docparser.models import TextObject
from build.lib.docparser.models.document_objects import PictureObject
from docparser import DocumentObject, RunObject
from docparser.models import DocumentBlockObject
from docparser.models.layout import LayoutObject
from docparser.common.base import load_excel_workbook, format_cell_value_with_openpyxl, COLOR_INDEX, WIN32_LOCK, \
    get_excel_shapes_win32com, shape_to_object
from docparser.common.theme_color import ThemeColor
from docparser.models.border import CellBorderObject, BorderObject
from docparser.models.style import FontStyleObject, StyleObject
from docparser.models.table import RowObject, CellObject, Position, TableObject
from docparser.parsers.excel.utils import int_color_to_hex
from docparser.parsers.excel_new.base import get_no_border_cells_and_table_cells, get_table_range_from_boundary_set
import pythoncom  # 导入 pythoncom 库
import win32com.client

from docparser.utils.logging_utils import get_logger

logger = get_logger("excel_parser")

def get_position(sheet: Worksheet) -> Dict[Tuple[int, int], Position]:
    total_width = .0
    width_dict = {}
    total_height = .0
    height_dict = {}
    cell_position_dict: Dict[Tuple[int, int], Position] = {}
    for col_idx in range(1, sheet.max_column + 1):
        # Convert column index to column letter
        column_letter = sheet.cell(row=1, column=col_idx).column_letter
        column_dimension = sheet.column_dimensions.get(column_letter, None)
        column_width = column_dimension.width if column_dimension else 8.38  # Default width = 8.43
        total_width += column_width
        width_dict[col_idx] = column_width

    for row_idx in range(1, sheet.max_row + 1):
        row_dimension = sheet.row_dimensions.get(row_idx, None)
        row_height = row_dimension.height if row_dimension else 13.5  # Default height = 15
        total_height += row_height
        height_dict[row_idx] = row_height

    accu_width = 0
    for col_idx in range(1, sheet.max_column + 1):
        accu_height = 0
        current_width = width_dict[col_idx]
        for row_idx in range(1, sheet.max_row + 1):
            current_height = height_dict[row_idx]
            position = Position()
            position.x = accu_width / total_width
            position.y = accu_height / total_height
            position.width = current_width / total_width
            position.height = current_height / total_height
            cell_position_dict[(row_idx, col_idx)] = position

            accu_height += current_height
        accu_width += current_width

    for merged_range in sheet.merged_cells.ranges:

        min_row = merged_range.min_row
        max_row = merged_range.max_row
        min_col = merged_range.min_col
        max_col = merged_range.max_col

        start_tuple = (min_row, min_col)
        start_position = cell_position_dict[start_tuple]

        for row in range(min_row+1, max_row + 1):  # +1 to include the end_row
            current_position = cell_position_dict[(row, min_col)]
            start_position.height = start_position.height + current_position.height
            current_position.height = 0
        for col in range(min_col+1, max_col + 1):  # +1 to include the end_col
            current_position = cell_position_dict[(min_row, col)]
            start_position.width = start_position.width + current_position.width
            current_position.width = 0
        for row in range(min_row + 1, max_row + 1):
            for col in range(min_col + 1, max_col + 1):
                cell_position_dict.get((row, col)).width = 0
                cell_position_dict.get((row, col)).height = 0

    return cell_position_dict


# 尝试重构用的代码，这个类暂时没有用到
class NewParser:
    def __init__(self, file_path: str):
        # 加载工作簿时不设置 data_only 获取完整的单元格对象
        # 加载excel的主题颜色
        self._file_path = file_path
        self._WHITE = "#" + WHITE[2:]
        self._BLACK = "#" + BLACK[2:]
        self._cells: Dict[Tuple[int,int], CellObject] = {}
        self._shapes = []
        self._THEME_COLORS = None
        self._openpyxl_wb = None
        self._win32com_wb = None
        self._excel_app = None

    def parse(self):
        self._init_wb(self._file_path)
        res = DocumentObject()

        self._shapes = self._parse_shapes(self._file_path)

        for sheet_name in self._openpyxl_wb.sheetnames:
            sheet = self._openpyxl_wb[sheet_name]
            doc = self._parse_sheet(sheet)
            res.document.append(doc)
        self._release_resources()
        return res

    def _parse_shapes(self, file_path, exclude_type_list=[4]):
        """
        使用win32com解析图形+图片
        :param file_path:
        :return:
        """
        with WIN32_LOCK:
            shapes_json = get_excel_shapes_win32com(file_path, exclude_type_list)
        return shapes_json

    def _parse_sheet(self, sheet: Worksheet)->DocumentBlockObject:
        res = DocumentBlockObject()
        self._cells = self._parse_cells(sheet)
        table_ranges, free_cells = self._get_start_end_of_table(sheet.max_row, sheet.max_column)

        tables = self._parse_table(table_ranges, res)
        res.tables = tables
        return res

    def _parse_cells(self, sheet: Worksheet) -> Dict[Tuple[int,int], CellObject]:
        res = {}
        cell_position = get_position(sheet)
        sheet_name = sheet.title
        available_sheets = [s.Name for s in self._win32com_wb.Sheets]
        # ws_with_pywin32 = self._win32com_wb.Sheets[sheet.title]
        if sheet_name not in available_sheets:
            raise ValueError(f"目标工作表 '{sheet_name}' 不存在！可用的工作表: {available_sheets}")
        for row_index, row in enumerate(sheet.iter_rows(), start=1):  # `start=1` for 1-based row indexing
            for col_index, cell in enumerate(row, start=1):  # `start=1` for 1-based column indexing
                # Extract cell information using the helper method
                cell_obj = self._get_cell_info(cell, ws_with_pywin32)
                cell_obj.row_index = row_index
                cell_obj.col_index = col_index
                cell_pos = cell_position.get((row_index, col_index), Position())
                cell_obj.position = cell_pos
                # Add parsed cell object to the result dictionary
                res[(row_index, col_index)] = cell_obj
        # set picture if it's in cell
        for shape in self._shapes:
            if shape["sheet_name"] == sheet.title and shape["in_cell"]:
                row = shape["from_row"]
                col = shape["from_col"]
                cell_obj = self._cells.get((row, col))
                cell_obj._has_picture = True
                obj = PictureObject()
                shape_to_object(shape, obj)
                cell_obj._picture = obj
        return res

    def _parse_table(self, table_ranges, document_block: DocumentBlockObject) -> List[TableObject]:
        tables: List[TableObject] = []
        for index, table_range in enumerate(table_ranges):
            table = TableObject()
            table.data_id = index + 1
            table.layout = LayoutObject.new_with_parent_ref(document_block)
            (min_row, min_col), (max_row, max_col) = table_range
            for row_index in range(min_row, max_row + 1):
                row_obj = RowObject()
                row_obj.data_id = row_index
                row_obj.row_index = row_index - min_row
                cells: List[CellObject] = []
                for col_index in range(min_col, max_col + 1):
                    cell_obj = self._cells.get((row_index, col_index))
                    cell_obj.layout = LayoutObject.new_with_parent_ref(row_obj)
                    cell_obj.data_id = 10 * cell_obj.row_index + cell_obj.col_index
                    # 将 row 和 column 改为当前table的索引
                    cell_obj.row_index = cell_obj.row_index - min_row
                    cell_obj.col_index = cell_obj.col_index - min_col
                    cells.append(cell_obj)
                row_obj.layout = LayoutObject.new_with_parent_ref(table)
                row_obj.cells = cells
                table.rows.append(row_obj)
                if table.rows:
                    table.coordinate.desc = table.rows[0].cells[0].coordinate.desc
                    #last_row = table.rows[len(table.rows) - 1]
                    #table._last_coordinate = last_row.cells[len(last_row.cells) - 1].coordinate.desc
            tables.append(table)
        return tables

    def _get_cell_info(self, cell: Cell, ws_with_pywin32) -> CellObject:
        v = self._get_cell_value(cell)
        cell_info = {
            "content": f"{v}" if v is not None else "",  # 单元格内容, 处理空单元格
            "index": cell.coordinate  # 单元格坐标
        }
        if cell.comment:
            cell_info["comment"] = cell.comment.text

        border = CellBorderObject()
        borders = self._get_borders(cell)
        border.border_left = borders["left"]
        border.border_right = borders["right"]
        border.border_top = borders["top"]
        border.border_bottom = borders["bottom"]

        cell_obj = CellObject()

        cell_obj.text = cell_info.get("content", "")
        cell_obj.comment.text = cell_info.get("comment", "")
        cell_obj._border = border
        cell_obj.coordinate.desc = cell.coordinate

        cell_obj.style = self._get_text_style(cell.font)

        bg_color = self._get_cell_bg_color(cell, ws_with_pywin32)

        if bg_color:
            cell_obj.style.background_color = bg_color
        if cell.fill.patternType:
            cell_obj.style.background_style = cell.fill.patternType
        else:
            cell_obj.style.background_style = 'solid'

        current_text = TextObject()
        run_objs = self._get_cell_text(cell)
        current_text._text = cell_obj.text
        current_text._style = run_objs[0].style if run_objs else None
        current_text._runs = run_objs
        current_text.coordinate.desc = cell.coordinate
        cell_obj.content.append(current_text)
        return cell_obj

    def _get_cell_value(self, cell: Cell):
        v = format_cell_value_with_openpyxl(cell)
        if isinstance(v, datetime):
            # v = v.isoformat()  # 将 datetime 转换为 ISO 格式字符串
            v = v.strftime('%Y/%m/%d')
        elif isinstance(v, TextBlock):
            v = v.text
        elif isinstance(v, CellRichText):
            v = str(v)
        elif v is None:
            v = ''
        return  v
    # 和原版逻辑有一点区别，只获取当前单元格的边框
    def _get_borders(self, cell: Cell) -> dict[str, BorderObject]:
        res = {}
        cell_border = cell.border

        # Left Border
        left_border = BorderObject()
        left_border.border_style = cell_border.left.style
        left_border.border_color = self._get_rgb_color(cell_border.left.color)
        res["left"] = left_border

        # Right Border
        right_border = BorderObject()
        right_border.border_style = cell_border.right.style
        right_border.border_color = self._get_rgb_color(cell_border.right.color)
        res["right"] = right_border

        # Top Border
        top_border = BorderObject()
        top_border.border_style = cell_border.top.style
        top_border.border_color = self._get_rgb_color(cell_border.top.color)
        res["top"] = top_border

        # Bottom Border
        bottom_border = BorderObject()
        bottom_border.border_style = cell_border.bottom.style
        bottom_border.border_color = self._get_rgb_color(cell_border.bottom.color)
        res["bottom"] = bottom_border
        return res

    def _get_rgb_color(self, color):
        """
        openpyxl的颜色对象解析对于的rgb颜色值
        :param color:
        :return:
        """
        if not color:
            return ""
        type_ = color.type
        value_ = color.value
        if type_ == 'rgb':
            return "#" + value_[2:] if len(value_) == 8 else "#" + value_
        elif type_ == 'indexed':
            # 索引颜色
            return self._get_color_by_index(value_)
        elif type_ == 'theme':
            # 主题颜色
            if color.theme >= len(self._THEME_COLORS):
                return ""
            return "#" + ThemeColor().theme_and_tint_to_rgb(self._THEME_COLORS, color.theme, color.tint)
        else:
            return ""

    def _get_color_by_index(self, value):
        """
        拼接颜色属性
        :param value:
        :return:
        """
        return self._BLACK if value >= len(COLOR_INDEX) else "#" + COLOR_INDEX[value][2:]

    def _get_text_style(self, font):
        """
        解析文本样式
        :param font:
        :return:
        """
        style_obj = StyleObject()
        style_obj._font_size = font.size
        if font.color:
            style_obj._font_color = self._get_rgb_color(font.color)
        elif font.color is None:
            style_obj._font_color = "#FFFFFF"
        style_obj._background_color = ''
        # 字体样式，如粗体、斜体、下划线、删除线
        font_style_ = FontStyleObject()
        if font.b:
            font_style_.bold = True  # 粗体
        if font.i:
            font_style_.italic = True  # 斜体
        if font.u:
            font_style_.underline = True  # 下划线
        if font.strike:
            font_style_.strikeout = True  # 删除线
        style_obj.font_style = font_style_

        # excel中font行内文本样式: InlineFont
        if isinstance(font, InlineFont):
            style_obj._font_family = font.rFont
        else:
            style_obj._font_family = font.name
        return style_obj

    def _get_start_end_of_table(self, max_row, max_column):
        res = get_no_border_cells_and_table_cells(self._cells,max_row, max_column)
        table_ranges = get_table_range_from_boundary_set(res.table)
        return table_ranges, res.free

    def _get_cell_text(self, cell: Cell) -> List[RunObject]:
        run_objs = []
        font = cell.font
        value = cell.value
        if cell.fill.fgColor and cell.fill.fgColor.rgb:
            bg_color = "#" + cell.fill.fgColor.rgb[2:]
        else:
            bg_color = "#FFFFFF"
        if isinstance(value, TextBlock):
            style_obj = self._get_text_style(cell.value.font)
            style_obj.background_color = bg_color
            r = RunObject()
            r.coordinate.desc = cell.coordinate
            if cell.value.text:
                r._text = str(cell.value.text)
            r._style = style_obj
            run_objs.append(r)
        elif isinstance(value, CellRichText):
            # CellRichText 文本片段循环处理
            for item in value:
                item_font = font if isinstance(item, str) else item.font
                style_obj = self._get_text_style(item_font)
                style_obj.background_color = bg_color
                r = RunObject()
                r.coordinate.desc = cell.coordinate
                if item and isinstance(item, str):
                    r._text = item
                elif item.text:
                    r._text = str(item.text)
                r._style = style_obj
                run_objs.append(r)
        else:
            style_obj = self._get_text_style(font)
            style_obj.background_color = bg_color
            r = RunObject()
            if value:
                r._text = str(value)
            r.coordinate.desc = cell.coordinate
            r._style = style_obj
            run_objs.append(r)
        return run_objs

    def _get_cell_bg_color(self, cell_: Cell, ws_with_pywin32):
        """
        获取单元格背景色
        https://openpyxl.readthedocs.io/en/latest/_modules/openpyxl/styles/colors.html
        :param cell_: openpyxl.cell.cell.Cell对象
        :return: 十六进制的颜色值
        """
        cell = ws_with_pywin32.Range(cell_.coordinate)
        return int_color_to_hex(int(cell.Interior.Color))
    def _init_wb(self, file_path):
        """初始化 openpyxl 和 win32com 的 Workbook 对象."""
        try:
            # 初始化 openpyxl 的 Workbook
            self._openpyxl_wb = openpyxl.load_workbook(file_path, rich_text=True)
            self._THEME_COLORS = ThemeColor().get_theme_colors(self._openpyxl_wb)
            # 初始化 COM 环境和 Workbook
            pythoncom.CoInitialize()  # 初始化 COM 库
            self._excel_app = win32com.client.Dispatch("Excel.Application")
            self._excel_app.Visible = False  # 隐藏 Excel GUI
            self._excel_app.DisplayAlerts = False  # 禁用提示框

            # 打开 Excel 文件作为 COM 对象
            self._win32com_wb = self._excel_app.Workbooks.Open(file_path)
        except pythoncom.com_error as com_error:
            logger.error(f"错误代码: {com_error.hresult}")  # HRESULT 错误代码
            logger.error(f"错误来源: {com_error.source}")  # 错误来源（通常是 COM 组件）
            logger.error(f"错误描述: {com_error.excepinfo[2]}")  # COM 返回的错误描述

            # 如果 COM 初始化失败，释放资源
            self._release_resources()
            raise
        except Exception as e:
            print(f"发生未知错误: {e}")
            # 如果任何异常发生，释放资源
            self._release_resources()
            raise

    def _release_resources(self):
        """释放资源，确保 openpyxl 和 COM 对象正确关闭."""
        try:
            # 关闭 openpyxl 的 Workbook 对象
            if self._openpyxl_wb:
                self._openpyxl_wb.close()

            # 关闭 win32com 的 Workbook 和 Excel.Application
            if self._win32com_wb:
                self._win32com_wb.Close(SaveChanges=False)
            if self._excel_app:
                self._excel_app.Quit()

        finally:
            # 解除对 COM 对象的引用
            self._openpyxl_wb = None
            self._win32com_wb = None
            self._excel_app = None

            # 解除 COM 库的初始化
            pythoncom.CoUninitialize()

if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    file_path = os.path.join(current_dir, f"../../testdata/test_case.xlsx")
    file_path = os.path.normpath(file_path)
    parse = NewParser(file_path)
    parse.parse()