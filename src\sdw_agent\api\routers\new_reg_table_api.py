"""
新规变化表作成工作流路由
"""
import os
import shutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter
from pydantic import BaseModel
from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.model.request_model import SourceInfo
from sdw_agent.service import WorkflowStatus
from sdw_agent.service.new_reg_table_workflow import NewRegTableWorkflow, NewRegTableInputModel
from sdw_agent.service.template_manager import template_manager

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/new_reg_table", tags=["新规变化表作成工作流"])


# 定义请求和响应模型
class NewRegTableRequest(BaseModel):
    """新规变化表作成请求模型"""
    RequirementSource: SourceInfo  # 要件一览表的路径
    NewRegTemplateSource: SourceInfo  # 新规变化表模板路径


class NewRegTableWorkflowRequest(BaseModel):
    """新规变化表作成工作流请求模型（只需要要件一览表）"""
    RequirementSource: SourceInfo  # 要件一览表的路径


class NewRegTableResponse(BaseModel):
    """新规变化表作成响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


@router.post("/create",
             summary="新规变化表作成工作流",
             description="使用工作流模式根据要件一览表和内置模板创建新规变化表",
             response_model=NewRegTableResponse)
async def create_new_regulation_table_workflow(request: NewRegTableWorkflowRequest):
    """
    新规变化表作成工作流处理函数

    Args:
        request: 请求参数，只包含要件一览表

    Returns:
        处理结果，包含生成的文件路径
    """
    try:
        logger.info(f"开始执行新规变化表作成工作流")
        logger.info(f"要件一览表: {request.RequirementSource.uri}")

        # 1. 使用模板管理器获取模板文件路径
        template_path = template_manager.get_template_path("new_reg_table_file")

        if not template_path:
            raise FileNotFoundError("无法获取新规变化表工作流的模板文件")

        # 2. 创建输出目录
        output_base_dir = os.path.join(ENV.config.output_data_path, "new_reg_table").replace("\\", "/")
        os.makedirs(output_base_dir, exist_ok=True)

        # 3. 生成带时间戳的输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_name = os.path.splitext(os.path.basename(template_path))[0]
        output_filename = f"{template_name}_{timestamp}.xlsx"
        output_file_path = os.path.join(output_base_dir, output_filename).replace("\\", "/")

        # 4. 复制模板文件到输出目录
        shutil.copy2(template_path, output_file_path)
        logger.info(f"模板文件已复制到: {output_file_path}")

        # 5. 创建工作流实例
        workflow = NewRegTableWorkflow()

        # 6. 准备输入数据（使用复制后的模板文件）
        template_source = SourceInfo(type="local", uri=output_file_path)
        input_data = NewRegTableInputModel(
            requirement_source=request.RequirementSource,
            template_source=template_source
        )

        # 7. 执行工作流
        result = workflow.run(input_data)

        if result.status == WorkflowStatus.SUCCESS:
            logger.info(f"新规变化表作成工作流执行成功，输出文件: {output_file_path}")
            # 返回成功结果，包含生成的文件路径
            return {
                "code": 0,
                "msg": "新规变化表作成成功",
                "data": {
                    "success": True,
                    "output_file_path": output_file_path
                }
            }
        else:
            logger.error(f"新规变化表作成工作流执行失败: {result.message}")
            # 如果工作流失败，删除已复制的模板文件
            try:
                if os.path.exists(output_file_path):
                    os.remove(output_file_path)
                    logger.info(f"已清理失败的输出文件: {output_file_path}")
            except Exception as cleanup_error:
                logger.warning(f"清理失败文件时出错: {cleanup_error}")

            return {
                "code": 1,
                "msg": f"新规变化表作成失败: {result.message}",
                "data": {
                    "success": False,
                    "error_details": result.error or result.message
                }
            }

    except Exception as e:
        logger.exception(f"新规变化表作成工作流执行异常: {str(e)}")

        # 如果异常发生，尝试清理可能已创建的输出文件
        try:
            if 'output_file_path' in locals() and os.path.exists(output_file_path):
                os.remove(output_file_path)
                logger.info(f"已清理异常时的输出文件: {output_file_path}")
        except Exception as cleanup_error:
            logger.warning(f"清理异常文件时出错: {cleanup_error}")

        return {
            "code": 1,
            "msg": f"新规变化表作成失败: {str(e)}",
            "data": {
                "success": False,
                "error_details": str(e)
            }
        }