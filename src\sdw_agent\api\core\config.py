"""
应用配置和中间件设置
"""
import os
import pathlib
from fastapi import FastAPI, Request
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi_babel import BabelConfigs, BabelMiddleware
from fastapi_babel.properties import RootConfigs
from loguru import logger
from starlette.middleware.cors import CORSMiddleware
from starlette.staticfiles import StaticFiles


def setup_app_middleware(app: FastAPI):
    """设置应用中间件和静态文件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
    )
    
    # 静态文件配置 - 静态文件现在在core目录下
    base_path = pathlib.Path(os.path.abspath(os.path.dirname(__file__)))
    static_dir = base_path / "static"
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # 请求日志中间件
    @app.middleware("http")
    @logger.catch(reraise=True)
    async def log_requests(request: Request, call_next):
        # 记录请求参数
        if request.method == "POST":
            request_data = await request.json()
            logger.debug(f"Request data: {request_data}")
        elif request.method == "GET":
            query_params = request.query_params
            logger.debug(f"Query params: {query_params}")

        response = await call_next(request)
        return response
    
    # Babel国际化配置 - 翻译文件现在在core目录下
    babel_configs: RootConfigs = BabelConfigs(
        ROOT_DIR=__file__,
        BABEL_DEFAULT_LOCALE=None,
        BABEL_TRANSLATION_DIRECTORY="translations"
    )
    
    def locale_selector(request: Request):
        return request.headers.get('Lang', 'zh')[:2]
    
    app.add_middleware(BabelMiddleware, babel_configs=babel_configs, locale_selector=locale_selector)


def setup_docs_routes(app: FastAPI):
    """设置文档路由"""
    
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=app.title + " - Swagger UI",
            swagger_js_url="/static/swagger/swagger-ui-bundle.js",
            swagger_css_url="/static/swagger/swagger-ui.css",
        )

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html():
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=app.title + " - ReDoc",
            redoc_js_url="/static/redoc/redoc.standalone.js",
        )
