"""提示词模板管理模块"""
from typing import Dict, Any, List, Optional, Union
import os
import json
import logging
import re
from pathlib import Path
from jinja2 import Template, Environment, FileSystemLoader, select_autoescape


class PromptTemplate:
    """提示词模板"""
    
    def __init__(self, template_str: str, template_id: str = None):
        """初始化提示词模板"""
        self.template_str = template_str
        self.template_id = template_id
        self.template = Template(template_str)
        
    def render(self, **kwargs) -> str:
        """渲染模板"""
        return self.template.render(**kwargs)
    
    @classmethod
    def from_file(cls, file_path: str, template_id: Optional[str] = None) -> 'PromptTemplate':
        """从文件加载模板"""
        with open(file_path, 'r', encoding='utf-8') as f:
            template_str = f.read()
        
        # 如果没有提供模板ID，使用文件名
        if template_id is None:
            template_id = Path(file_path).stem
            
        return cls(template_str, template_id)


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, templates_dir: Optional[str] = None):
        """初始化提示词管理器"""
        self.templates: Dict[str, PromptTemplate] = {}
        self.templates_dir = templates_dir
        self.logger = logging.getLogger(__name__)
        
        # 如果提供了模板目录，加载所有模板
        if templates_dir:
            self.load_templates_from_directory(templates_dir)
            
        # 设置Jinja2环境
        self.env = Environment(
            loader=FileSystemLoader(templates_dir) if templates_dir else None,
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    def register_template(self, template_id: str, template: Union[str, PromptTemplate]) -> None:
        """注册模板"""
        if isinstance(template, str):
            template = PromptTemplate(template, template_id)
        
        self.templates[template_id] = template
        self.logger.debug(f"注册模板: {template_id}")
        
    def load_templates_from_directory(self, directory: str) -> None:
        """从目录加载所有模板"""
        template_files = list(Path(directory).glob("*.txt")) + list(Path(directory).glob("*.j2"))
        
        for template_file in template_files:
            template_id = template_file.stem
            try:
                template = PromptTemplate.from_file(str(template_file), template_id)
                self.register_template(template_id, template)
            except Exception as e:
                self.logger.error(f"加载模板 {template_file} 失败: {str(e)}")
                
        self.logger.info(f"从目录 {directory} 加载了 {len(template_files)} 个模板")
    
    def get_template(self, template_id: str) -> PromptTemplate:
        """获取模板"""
        if template_id not in self.templates:
            raise ValueError(f"未找到模板: {template_id}")
            
        return self.templates[template_id]
    
    def render_template(self, template_id: str, **kwargs) -> str:
        """渲染模板"""
        template = self.get_template(template_id)
        return template.render(**kwargs)
    
    def render_template_from_file(self, file_path: str, **kwargs) -> str:
        """从文件渲染模板"""
        if not os.path.exists(file_path):
            # 尝试在模板目录中查找
            if self.templates_dir:
                file_path = os.path.join(self.templates_dir, file_path)
                
        template = PromptTemplate.from_file(file_path)
        return template.render(**kwargs)


class AgentPromptBuilder:
    """Agent提示词构建器"""
    
    def __init__(self, prompt_manager: PromptManager):
        """初始化提示词构建器"""
        self.prompt_manager = prompt_manager
        self.logger = logging.getLogger(__name__)
        
    def build_system_prompt(self, agent_type: str, **kwargs) -> str:
        """构建系统提示词"""
        template_id = f"system_{agent_type}"
        try:
            return self.prompt_manager.render_template(template_id, **kwargs)
        except ValueError:
            self.logger.warning(f"未找到模板 {template_id}，使用默认系统提示词")
            return self._get_default_system_prompt(agent_type)
    
    def build_user_prompt(self, agent_type: str, task_type: str, **kwargs) -> str:
        """构建用户提示词"""
        template_id = f"user_{agent_type}_{task_type}"
        try:
            return self.prompt_manager.render_template(template_id, **kwargs)
        except ValueError:
            # 尝试使用通用模板
            try:
                template_id = f"user_{agent_type}"
                return self.prompt_manager.render_template(template_id, task_type=task_type, **kwargs)
            except ValueError:
                self.logger.warning(f"未找到模板 {template_id}，使用默认用户提示词")
                return self._get_default_user_prompt(agent_type, task_type, **kwargs)
    
    def _get_default_system_prompt(self, agent_type: str) -> str:
        """获取默认系统提示词"""
        default_prompts = {
            "requirement": "你是一个专业的汽车软件需求分析专家，擅长分析和理解软件需求。",
            "design": "你是一个专业的汽车软件设计专家，擅长软件架构设计和详细设计。",
            "code_review": "你是一个专业的代码审查专家，擅长发现代码中的问题和改进点。",
            "test": "你是一个专业的软件测试专家，擅长设计测试用例和测试策略。",
            "default": "你是一个专业的汽车软件开发助手，擅长解决各种软件开发问题。"
        }
        return default_prompts.get(agent_type, default_prompts["default"])
    
    def _get_default_user_prompt(self, agent_type: str, task_type: str, **kwargs) -> str:
        """获取默认用户提示词"""
        task_description = f"请执行{task_type}任务"
        
        # 添加输入数据摘要
        input_summary = ""
        for key, value in kwargs.items():
            if isinstance(value, str) and len(value) > 100:
                value = value[:100] + "..."
            input_summary += f"\n- {key}: {value}"
            
        return f"{task_description}\n\n输入数据:{input_summary}\n\n请分析以上数据并给出结果。" 