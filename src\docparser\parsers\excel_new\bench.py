# -*- coding: utf-8 -*-
"""
@File    : bench.py
<AUTHOR> zhenp
@Date    : 2025-06-19 13:47
@Desc    : Description of the file
"""
import cProfile
from datetime import datetime

import openpyxl
from openpyxl import Workbook
from openpyxl.cell import Cell

from docparser.common.base import COLOR_INDEX, format_cell_value_with_openpyxl
from docparser.common.theme_color import ThemeColor
from docparser.models.border import BorderObject
from docparser.models.table import CellBorderObject
from openpyxl.cell.rich_text import TextBlock, CellRichText


def get_cell_border(cell: Cell, themes) -> CellBorderObject:
    if cell.value is None:
        return CellBorderObject()
    cell_border = CellBorderObject()
    border_style = getattr(cell.border, "top").style
    border_color = get_rgb_color(themes,getattr(cell.border, "top").color)
    cell_border.border_top = BorderObject(border_color, border_style)
    border_style = getattr(cell.border, "left").style
    border_color = get_rgb_color(themes, getattr(cell.border, "left").color)
    cell_border.border_left = BorderObject(border_color, border_style)
    border_style = getattr(cell.border, "bottom").style
    border_color = get_rgb_color(themes, getattr(cell.border, "bottom").color)
    cell_border.border_bottom = BorderObject(border_color, border_style)
    border_style = getattr(cell.border, "right").style
    border_color = get_rgb_color(themes, getattr(cell.border, "right").color)
    cell_border.border_right = BorderObject(border_color, border_style)
    return cell_border

def get_rgb_color(themes, color):
    """
    openpyxl的颜色对象解析对于的rgb颜色值
    :param color:
    :return:
    """
    if not color:
        return ""
    type_ = color.type
    value_ = color.value
    if type_ == 'rgb':
        return "#" + value_[2:] if len(value_) == 8 else "#" + value_
    elif type_ == 'indexed':
        # 索引颜色
        return get_color_by_index(value_)
    elif type_ == 'theme':
        # 主题颜色
        if color.theme >= len(themes):
            return ""
        return "#" + ThemeColor().theme_and_tint_to_rgb(themes, color.theme, color.tint)
    else:
        return ""

def get_theme_colors(wb: Workbook):
    """Gets theme colors from the workbook"""
    # see: https://groups.google.com/forum/#!topic/openpyxl-users/I0k3TfqNLrc
    from openpyxl.xml.functions import QName, fromstring
    xlmns = 'http://schemas.openxmlformats.org/drawingml/2006/main'
    root = fromstring(wb.loaded_theme)
    themeEl = root.find(QName(xlmns, 'themeElements').text)
    colorSchemes = themeEl.findall(QName(xlmns, 'clrScheme').text)
    firstColorScheme = colorSchemes[0]

    colors = []

    for c in ['lt1', 'dk1', 'lt2', 'dk2', 'accent1', 'accent2', 'accent3', 'accent4', 'accent5', 'accent6']:
        accent = firstColorScheme.find(QName(xlmns, c).text)

        if 'window' in accent.getchildren()[0].attrib['val']:
            colors.append(accent.getchildren()[0].attrib['lastClr'])
        else:
            colors.append(accent.getchildren()[0].attrib['val'])

    return colors

def get_color_by_index(value):
    """
    拼接颜色属性
    :param value:
    :return:
    """
    return "#000000" if value >= len(COLOR_INDEX) else "#" + COLOR_INDEX[value][2:]

def get_cell_info(cell):
    if cell.value is None:
        return
    """ 获取单元格的内容、坐标、注释等信息 """
    v = get_cell_value(cell)
    cell_info = {
        "content": f"{v}" if v is not None else "",  # 单元格内容, 处理空单元格
        "index": cell.coordinate  # 单元格坐标
    }
    # 保存备注信息
    if cell.comment:
        cell_info["comment"] = cell.comment.text

    return cell_info

def get_cell_value(cell):
    """
    解析excel单元格的内容
    :param cell:  Cell对象
    :param sheet: Worksheet对象
    :param from_merged_cells: 在合并单元格中， 是否从合并单元格中第一个单元格中取值
    :return:
    """
    v = format_cell_value_with_openpyxl(cell)
    if isinstance(v, datetime):
        # v = v.isoformat()  # 将 datetime 转换为 ISO 格式字符串
        v = v.strftime('%Y/%m/%d')
    elif isinstance(v, TextBlock):
        v = v.text
    elif isinstance(v, CellRichText):
        v = str(v)
    elif v is None:
        v = ''
    return v

if __name__ == "__main__":
    file_path = r"D:\work\RRM\REQ_Compare_Document\01REQ\0102Report\00 业务数据\fengtian\line5\line5变更前.xlsx"
    profiler = cProfile.Profile()
    profiler.enable()
    workbook = openpyxl.load_workbook(file_path, read_only=False, rich_text=True)
    theme_colors = get_theme_colors(workbook)
    for name in workbook.sheetnames:
        sheet = workbook[name]
        for row in sheet.rows:
            for cell in row:
                get_cell_border(cell, theme_colors)
                get_cell_info(cell)
    profiler.disable()
    profiler.dump_stats(r"C:\Users\<USER>\prof\line5-new.prof")

