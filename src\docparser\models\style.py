import re

from .base_object import BaseObject
import re
class FontStyleObject(BaseObject):
    """字体样式"""
    def __init__(self):
        self._normal = True  # 正常
        self._bold = False  # 粗体
        self._italic = False  # 斜体
        self._underline = False  # 下划线
        self._strikeout = False  # 删除线

    def to_dict(self):
        """
        将 FontStyleObject 对象转换为字典
        """
        return {
            "normal": self._normal,
            "bold": self._bold,
            "italic": self._italic,
            "underline": self._underline,
            "strikeout": self._strikeout,
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 FontStyleObject 实例
        """
        obj = cls()
        obj._normal = data.get("normal", True)
        obj._bold = data.get("bold", False)
        obj._italic = data.get("italic", False)
        obj._underline = data.get("underline", False)
        obj._strikeout = data.get("strikeout", False)
        return obj

    @property
    def normal(self):
        return self._normal

    @normal.setter
    def normal(self, new_value):
        assert type(new_value) is bool
        self._normal = new_value
        if new_value:
            self.bold = False
            self.italic = False
            self.underline = False
            self.strikeout = False

    @property
    def bold(self):
        return self._bold

    @bold.setter
    def bold(self, new_value):
        assert type(new_value) is bool
        self._bold = new_value
        if new_value:
            self._normal = False

    @property
    def italic(self):
        return self._italic

    @italic.setter
    def italic(self, new_value):
        assert type(new_value) is bool
        self._italic = new_value
        if new_value:
            self._normal = False

    @property
    def underline(self):
        return self._underline

    @underline.setter
    def underline(self, new_value):
        assert type(new_value) is bool
        self._underline = new_value
        if new_value:
            self._normal = False

    @property
    def strikeout(self):
        return self._strikeout

    @strikeout.setter
    def strikeout(self, new_value):
        assert type(new_value) is bool
        self._strikeout = new_value
        if new_value:
            self._normal = False

    def __eq__(self, other):
        if self.normal != other.normal:
            return False
        if self.bold != other.bold:
            return False
        if self.italic != other.italic:
            return False
        if self.underline != other.underline:
            return False
        if self.strikeout != other.strikeout:
            return False
        return True


class StyleObject(BaseObject):
    """样式对象"""
    def __init__(self):
        self._font_family = ''  # 字体
        self._font_size = ''  # 字号,以pt为单位
        self._font_color = ''  # 字体颜色, #00ff00
        self._font_style = FontStyleObject()  # 字体样式
        self._background_color = ''  # 背景颜色
        self._background_style = ''  # 背景样式

    def to_dict(self):
        """
        将 StyleObject 对象转换为字典
        """
        return {
            "font_family": self._font_family,
            "font_size": self._font_size,
            "font_color": self._font_color,
            "font_style": self._font_style.to_dict(),  # 调用 FontStyleObject 的 to_dict 方法
            "background_color": self._background_color,
            "background_style": self._background_style,
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 StyleObject 实例
        """
        obj = cls()
        obj._font_family = data.get("font_family", "")
        obj._font_size = data.get("font_size", "")
        obj._font_color = data.get("font_color", "")
        obj._background_color = data.get("background_color", "")
        obj._background_style = data.get("background_style", "")

        # 从字典填充 FontStyleObject，嵌套调用 FontStyleObject.from_dict
        font_style_data = data.get("font_style", {})
        obj._font_style = FontStyleObject.from_dict(font_style_data)

        return obj

    @property
    def font_family(self):
        return self._font_family

    @font_family.setter
    def font_family(self, new_value):
        assert type(new_value) == str
        self._font_family = new_value

    @property
    def font_size(self):
        return self._font_size

    @font_size.setter
    def font_size(self, new_value):
        assert type(new_value) == str
        # 字体单位pt
        assert new_value.endswith('pt')
        self._font_size = new_value

    @property
    def font_color(self):
        return self._font_color

    @font_color.setter
    def font_color(self, new_value):
        if new_value not in ['auto', "#auto"] and not re.match(r'^#[0-9A-Fa-f]{6}$', new_value):
            raise ValueError(f"期望 font_color 为有效的颜色值，如#000000, 却获取到了：{new_value}")
        self._font_color = new_value

    @property
    def font_style(self):
        return self._font_style

    @font_style.setter
    def font_style(self, new_value):
        assert isinstance(new_value, FontStyleObject)
        self._font_style = new_value

    @property
    def background_color(self):
        return self._background_color

    @background_color.setter
    def background_color(self, new_value):
        if new_value not in ['auto', "#auto"] and not re.match(r'^#[0-9A-Fa-f]{6}$', new_value):
            raise ValueError(f"期望 background_color 为有效的颜色值，如#000000, 却获取到了：{new_value}")
        self._background_color = new_value

    @property
    def background_style(self):
        return self._background_style

    @background_style.setter
    def background_style(self, new_value):
        self._background_style = new_value

    def __eq__(self, other):
        if self.font_size != other.font_size:
            return False
        if self.font_family != other.font_family:
            return False
        if self.font_color != other.font_color:
            return False
        if self.background_color != other.background_color:
            return False
        if self.background_style != other.background_style:
            return False
        if self.font_style != other.font_style:
            return False
        return True