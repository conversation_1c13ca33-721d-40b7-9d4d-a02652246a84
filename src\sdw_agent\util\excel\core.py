"""
通用Excel读写工具类

本模块提供了一个通用的Excel操作工具类，支持：
1. 读取Excel文件的指定区域
2. 写入单元格数据
3. 设置单元格样式
4. 创建和管理工作表
5. 支持多种Excel格式(.xlsx, .xlsm, .xls)

设计原则：
- 基类提供通用功能，便于继承扩展
- 支持多种操作引擎（openpyxl, xlwings, pandas）
- 提供丰富的样式设置选项
- 异常处理和资源管理
"""

import os
import re
from abc import ABC, abstractmethod
from copy import copy
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from loguru import logger
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter, column_index_from_string
from openpyxl.worksheet.worksheet import Worksheet

from sdw_agent.util.select_util import select_one


class MultiLevelHeaderInfo:
    """多级表头信息类"""

    def __init__(self, raw_data: List[List], merged_data: List[List],
                 hierarchical: List[Dict], flat_columns: List[str],
                 visible_rows: List[int], column_range: Tuple[int, int]):
        """
        初始化多级表头信息

        Args:
            raw_data: 原始表头数据
            merged_data: 处理合并单元格后的数据
            hierarchical: 层次结构数据
            flat_columns: 平铺的列名列表
            visible_rows: 可见行列表
            column_range: 列范围元组(start_col, end_col)
        """
        self.raw_data = raw_data
        self.merged_data = merged_data
        self.hierarchical = hierarchical
        self.flat_columns = flat_columns
        self.visible_rows = visible_rows
        self.column_range = column_range

        logger.debug(f"创建多级表头信息 - 可见行: {visible_rows}, 列范围: {column_range}, 总列数: {len(flat_columns)}")

    def get_column_mapping(self) -> Dict[str, int]:
        """
        获取列名到列索引的映射

        Returns:
            列名到列索引的映射字典
        """
        mapping = {}
        for i, col_name in enumerate(self.flat_columns):
            mapping[col_name] = i
        logger.debug(f"生成列名映射，共{len(mapping)}个列")
        return mapping

    def print_analysis(self):
        """打印表头分析结果"""
        logger.info("=== 多级表头分析结果 ===")
        logger.info(f"可见行: {self.visible_rows}")
        logger.info(f"列范围: {self.column_range}")
        logger.info(f"总列数: {len(self.flat_columns)}")

        logger.info("=== 层次结构示例（前10列）===")
        for i, col_info in enumerate(self.hierarchical[:10]):
            logger.info(f"列{col_info['column_index'] + 1}: {col_info['hierarchy']}")

        logger.info("=== 平铺列名示例（前10列）===")
        for i, col_name in enumerate(self.flat_columns[:10]):
            logger.info(f"列{i + 1}: {col_name}")


class CellStyle:
    """单元格样式配置类"""

    def __init__(
            self,
            font_name: str = "Calibri",
            font_size: int = 11,
            font_bold: bool = False,
            font_italic: bool = False,
            font_color: str = "000000",
            bg_color: Optional[str] = None,
            alignment_horizontal: str = "left",
            alignment_vertical: str = "center",
            border_style: Optional[str] = None,
            border_color: str = "000000",
            font_underline: Optional[str] = None
    ):
        """
        初始化单元格样式

        Args:
            font_name: 字体名称
            font_size: 字体大小
            font_bold: 是否加粗
            font_italic: 是否斜体
            font_color: 字体颜色（十六进制，不含#）
            bg_color: 背景颜色（十六进制，不含#）
            alignment_horizontal: 水平对齐方式（left, center, right）
            alignment_vertical: 垂直对齐方式（top, center, bottom）
            border_style: 边框样式（thin, medium, thick等）
            border_color: 边框颜色（十六进制，不含#）
            font_underline: 下划线（'single', 'double'）
        """
        self.font_name = font_name
        self.font_size = font_size
        self.font_bold = font_bold
        self.font_italic = font_italic
        self.font_color = font_color
        self.font_underline = font_underline
        self.bg_color = bg_color
        self.alignment_horizontal = alignment_horizontal
        self.alignment_vertical = alignment_vertical
        self.border_style = border_style
        self.border_color = border_color

    def to_openpyxl_style(self) -> Dict[str, Any]:
        """转换为openpyxl样式对象"""
        style_dict = {}

        # 字体样式
        style_dict['font'] = Font(
            name=self.font_name,
            size=self.font_size,
            bold=self.font_bold,
            italic=self.font_italic,
            color=self.font_color,
            underline=self.font_underline
        )

        # 对齐样式
        style_dict['alignment'] = Alignment(
            horizontal=self.alignment_horizontal,
            vertical=self.alignment_vertical
        )

        # 背景填充
        if self.bg_color:
            style_dict['fill'] = PatternFill(
                start_color=self.bg_color,
                end_color=self.bg_color,
                fill_type='solid'
            )

        # 边框样式
        if self.border_style:
            side = Side(style=self.border_style, color=self.border_color)
            style_dict['border'] = Border(
                left=side, right=side, top=side, bottom=side
            )

        return style_dict


class CellRange:
    """单元格区域类"""

    def __init__(self, start_row: int, start_col: Union[int, str],
                 end_row: int = None, end_col: Union[int, str] = None):
        """
        初始化单元格区域

        Args:
            start_row: 起始行号（从1开始）
            start_col: 起始列号（数字从1开始或字母如'A'）
            end_row: 结束行号（如果为None，则为单行）
            end_col: 结束列号（如果为None，则为单列）
        """
        self.start_row = start_row
        self.start_col = self._normalize_column(start_col)
        self.end_row = end_row if end_row is not None else start_row
        self.end_col = self._normalize_column(end_col) if end_col is not None else self.start_col

    def _normalize_column(self, col: Union[int, str]) -> int:
        """将列标识符标准化为数字"""
        if isinstance(col, str):
            return column_index_from_string(col)
        return col

    def to_excel_range(self) -> str:
        """转换为Excel区域字符串格式，如'A1:C3'"""
        start_col_letter = get_column_letter(self.start_col)
        end_col_letter = get_column_letter(self.end_col)

        if self.start_row == self.end_row and self.start_col == self.end_col:
            return f"{start_col_letter}{self.start_row}"
        else:
            return f"{start_col_letter}{self.start_row}:{end_col_letter}{self.end_row}"

    def get_cell_coordinates(self) -> List[Tuple[int, int]]:
        """获取区域内所有单元格的坐标列表"""
        coordinates = []
        for row in range(self.start_row, self.end_row + 1):
            for col in range(self.start_col, self.end_col + 1):
                coordinates.append((row, col))
        return coordinates


class BaseExcelEngine(ABC):
    """Excel操作引擎基类"""

    @abstractmethod
    def read_range(self, sheet_name: str, cell_range: CellRange) -> List[List[Any]]:
        """读取指定区域的数据"""
        pass

    @abstractmethod
    def write_cell(self, sheet_name: str, row: int, col: Union[int, str], value: Any):
        """写入单元格数据"""
        pass

    @abstractmethod
    def write_hyperlink(self, sheet_name: str, row: int, col: Union[int, str], url: str,
                        display_text: str = None) -> None:
        """写入超链接到指定单元格"""
        pass

    @abstractmethod
    def set_cell_style(self, sheet_name: str, row: int, col: Union[int, str], style: CellStyle):
        """设置单元格样式"""
        pass

    @abstractmethod
    def create_sheet(self, sheet_name: str) -> bool:
        """创建新工作表"""
        pass

    @abstractmethod
    def delete_sheet(self, sheet_name: str) -> bool:
        """删除工作表"""
        pass

    @abstractmethod
    def get_sheet_names(self) -> List[str]:
        """获取所有工作表名称"""
        pass

    @abstractmethod
    def save(self, file_path: Optional[str] = None):
        """保存文件"""
        pass

    @abstractmethod
    def close(self):
        """关闭文件"""
        pass


class OpenpyxlEngine(BaseExcelEngine):
    """基于openpyxl的Excel操作引擎"""

    def __init__(self, file_path: str):
        """
        初始化openpyxl引擎

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.workbook = None
        logger.info(f"初始化OpenpyxlEngine，文件路径: {file_path}")
        self._load_workbook()

    def _load_workbook(self):
        """加载工作簿"""
        try:
            if os.path.exists(self.file_path) and os.path.getsize(self.file_path) > 0:
                # 文件存在且不为空，尝试加载
                logger.info(f"加载现有Excel文件: {self.file_path}")
                self.workbook = load_workbook(self.file_path)
                logger.info(f"成功加载Excel文件，工作表数量: {len(self.workbook.sheetnames)}")
            else:
                # 文件不存在或为空，创建新工作簿
                logger.info(f"创建新的Excel工作簿: {self.file_path}")
                self.workbook = Workbook()
                logger.info("成功创建新的Excel工作簿")
        except Exception as e:
            # 如果加载失败，创建新工作簿
            logger.warning(f"加载Excel文件失败: {e}，尝试创建新工作簿")
            try:
                self.workbook = Workbook()
                logger.info("成功创建新的Excel工作簿")
            except Exception as e2:
                logger.error(f"无法创建Excel工作簿: {e2}")
                raise Exception(f"无法创建Excel工作簿: {e2}")

    def _get_worksheet(self, sheet_name: str) -> Worksheet:
        """获取工作表对象"""
        if sheet_name not in self.workbook.sheetnames:
            raise ValueError(f"工作表 '{sheet_name}' 不存在")
        return self.workbook[sheet_name]

    def _normalize_column(self, col: Union[int, str]) -> int:
        """将列标识符标准化为数字"""
        if isinstance(col, str):
            return column_index_from_string(col)
        return col

    def read_range(self, sheet_name: str, cell_range: CellRange) -> List[List[Any]]:
        """读取指定区域的数据"""
        logger.debug(f"OpenpyxlEngine读取区域数据: {sheet_name}, {cell_range.to_excel_range()}")
        worksheet = self._get_worksheet(sheet_name)
        data = []

        for row in range(cell_range.start_row, cell_range.end_row + 1):
            row_data = []
            for col in range(cell_range.start_col, cell_range.end_col + 1):
                cell = worksheet.cell(row=row, column=col)
                row_data.append(cell.value)
            data.append(row_data)

        logger.debug(f"成功读取数据，行数: {len(data)}, 列数: {len(data[0]) if data else 0}")
        return data

    def write_cell(self, sheet_name: str, row: int, col: Union[int, str], value: Any):
        """写入单元格数据"""
        logger.debug(f"OpenpyxlEngine写入单元格: {sheet_name}, 行{row}, 列{col}, 值: {value}")
        worksheet = self._get_worksheet(sheet_name)
        col_num = self._normalize_column(col)
        cell = worksheet.cell(row=row, column=col_num)
        cell.value = value

    def write_hyperlink(self, sheet_name: str, row: int, col: Union[int, str], url: str,
                        display_text: str = None) -> None:
        """写入超链接到指定单元格"""
        logger.debug(f"OpenpyxlEngine写入超链接: {sheet_name}, 行{row}, 列{col}, URL: {url}")
        worksheet = self._get_worksheet(sheet_name)
        col_num = self._normalize_column(col)

        if display_text is None:
            display_text = url

        cell = worksheet.cell(row=row, column=col_num)
        cell.value = display_text
        cell.hyperlink = url

    def set_cell_style(self, sheet_name: str, row: int, col: Union[int, str], style: CellStyle):
        """设置单元格样式"""
        worksheet = self._get_worksheet(sheet_name)
        col_num = self._normalize_column(col)
        cell = worksheet.cell(row=row, column=col_num)

        style_dict = style.to_openpyxl_style()
        for attr_name, attr_value in style_dict.items():
            setattr(cell, attr_name, attr_value)

    def create_sheet(self, sheet_name: str) -> bool:
        """创建新工作表"""
        try:
            if sheet_name in self.workbook.sheetnames:
                return False  # 工作表已存在
            self.workbook.create_sheet(sheet_name)
            return True
        except Exception:
            return False

    def delete_sheet(self, sheet_name: str) -> bool:
        """删除工作表"""
        try:
            if sheet_name not in self.workbook.sheetnames:
                return False  # 工作表不存在
            self.workbook.remove(self.workbook[sheet_name])
            return True
        except Exception:
            return False

    def get_sheet_names(self) -> List[str]:
        """获取所有工作表名称"""
        return self.workbook.sheetnames

    def save(self, file_path: Optional[str] = None):
        """保存文件"""
        save_path = file_path or self.file_path
        self.workbook.save(save_path)

    def close(self):
        """关闭文件"""
        if self.workbook:
            self.workbook.close()
            self.workbook = None


class PandasEngine(BaseExcelEngine):
    """基于pandas的Excel操作引擎（主要用于数据读取和分析）"""

    def __init__(self, file_path: str):
        """
        初始化pandas引擎

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.excel_file = None
        self.data_cache = {}
        self._load_excel()

    def _load_excel(self):
        """加载Excel文件"""
        try:
            if os.path.exists(self.file_path):
                self.excel_file = pd.ExcelFile(self.file_path)
                # 缓存所有sheet的数据
                for sheet_name in self.excel_file.sheet_names:
                    self.data_cache[sheet_name] = pd.read_excel(
                        self.excel_file, sheet_name=sheet_name
                    )
        except Exception as e:
            raise Exception(f"无法加载Excel文件 {self.file_path}: {e}")

    def read_range(self, sheet_name: str, cell_range: CellRange) -> List[List[Any]]:
        """读取指定区域的数据"""
        if sheet_name not in self.data_cache:
            raise ValueError(f"工作表 '{sheet_name}' 不存在")

        df = self.data_cache[sheet_name]

        # 转换为0基索引
        start_row = cell_range.start_row - 1
        end_row = cell_range.end_row - 1
        start_col = cell_range.start_col - 1
        end_col = cell_range.end_col - 1

        # 提取数据
        try:
            subset = df.iloc[start_row:end_row + 1, start_col:end_col + 1]
            return subset.values.tolist()
        except IndexError as e:
            raise ValueError(f"指定的区域超出了数据范围: {e}")

    def write_cell(self, sheet_name: str, row: int, col: Union[int, str], value: Any):
        """写入单元格数据（pandas引擎不支持直接写入）"""
        raise NotImplementedError("Pandas引擎不支持直接写入单元格，请使用OpenpyxlEngine")

    def write_hyperlink(self, sheet_name: str, row: int, col: Union[int, str], url: str,
                        display_text: str = None) -> None:
        """写入单元格超链接"""
        raise NotImplementedError("Pandas引擎不支持直接写入超链接，请使用OpenpyxlEngine")

    def set_cell_style(self, sheet_name: str, row: int, col: Union[int, str], style: CellStyle):
        """设置单元格样式（pandas引擎不支持）"""
        raise NotImplementedError("Pandas引擎不支持设置单元格样式，请使用OpenpyxlEngine")

    def create_sheet(self, sheet_name: str) -> bool:
        """创建新工作表（pandas引擎不支持）"""
        raise NotImplementedError("Pandas引擎不支持创建工作表，请使用OpenpyxlEngine")

    def delete_sheet(self, sheet_name: str) -> bool:
        """删除工作表（pandas引擎不支持）"""
        raise NotImplementedError("Pandas引擎不支持删除工作表，请使用OpenpyxlEngine")

    def get_sheet_names(self) -> List[str]:
        """获取所有工作表名称"""
        return list(self.data_cache.keys()) if self.data_cache else []

    def save(self, file_path: Optional[str] = None):
        """保存文件（pandas引擎不支持直接保存）"""
        raise NotImplementedError("Pandas引擎不支持直接保存，请使用OpenpyxlEngine")

    def close(self):
        """关闭文件"""
        if self.excel_file:
            self.excel_file.close()
            self.excel_file = None
        self.data_cache.clear()


class Win32comEngine(BaseExcelEngine):
    """基于win32com的Excel操作引擎（保护宏和图片）"""

    def __init__(self, file_path: str):
        """
        初始化win32com引擎

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.excel_app = None
        self.workbook = None
        logger.info(f"初始化Win32comEngine，文件路径: {file_path}")
        self._init_excel_app()

    def _init_excel_app(self):
        """初始化Excel应用程序"""
        try:
            import win32com.client
            import pythoncom

            logger.info("开始初始化Excel应用程序")

            # 初始化COM
            pythoncom.CoInitialize()
            logger.debug("COM初始化完成")

            # 尝试连接到现有的Excel实例或创建新实例
            self.excel_app = self._get_or_create_excel_app(win32com.client)
            logger.info("Excel应用程序对象创建/连接成功")

            # 配置Excel应用程序
            self._configure_excel_app()

            # 打开或创建工作簿
            self._load_workbook()

        except ImportError:
            logger.error("win32com模块未安装")
            raise ImportError("win32com模块未安装，请使用 pip install pywin32 安装")
        except Exception as e:
            logger.error(f"初始化Excel应用程序失败: {e}")
            raise Exception(f"初始化Excel应用程序失败: {e}")

    def _get_or_create_excel_app(self, win32com_client):
        """获取或创建Excel应用程序实例"""
        try:
            # 首先尝试连接到现有的Excel实例
            try:
                logger.debug("连接现有的Excel实例")
                excel_app = win32com_client.GetActiveObject("Excel.Application")
                logger.debug("连接到现有的Excel实例")

                # 检查目标文件是否已在现有实例中打开
                abs_file_path = os.path.abspath(self.file_path)
                for wb in excel_app.Workbooks:
                    if hasattr(wb, 'FullName') and wb.FullName and wb.FullName.lower() == abs_file_path.lower():
                        logger.debug(f"文件已在现有Excel实例中打开: {self.file_path}")
                        self.workbook = wb
                        self._existing_file_opened = True  # 标记文件已打开
                        return excel_app

                logger.debug("文件未在现有实例中打开，将使用现有实例")
                self._existing_file_opened = False
                return win32com_client.DispatchEx("Excel.Application")

            except Exception as e1:
                # 没有现有实例，创建新的
                logger.warning(f"未找到现有Excel实例，创建新实例: {e1}")
                self._existing_file_opened = False
                return win32com_client.DispatchEx("Excel.Application")

        except Exception as e2:
            logger.warning(f"获取Excel实例时出错: {e2}，创建新实例")
            self._existing_file_opened = False
            return win32com_client.DispatchEx("Excel.Application")

    def _configure_excel_app(self):
        """配置Excel应用程序"""
        try:
            # 检查是否有现有工作簿已打开（说明连接到了现有实例）
            has_existing_workbook = hasattr(self, 'workbook') and self.workbook is not None

            if not has_existing_workbook:
                # 新实例或没有打开目标文件，使用静默模式
                self.excel_app.Visible = False
                self.excel_app.DisplayAlerts = False
                self.excel_app.EnableEvents = False  # 禁用事件，防止VBA脚本执行
                self.excel_app.ScreenUpdating = False  # 禁用屏幕更新，提高性能
                logger.debug("配置为静默模式")
            else:
                # 连接到现有实例且文件已打开，保持可见性和更新
                # 不修改Visible状态，保持用户当前的设置
                self.excel_app.Visible = True
                self.excel_app.DisplayAlerts = False  # 仍然禁用警告
                self.excel_app.ScreenUpdating = True  # 启用屏幕更新，确保实时显示
                logger.debug("配置为实时更新模式（文件已打开）")

            # 尝试设置计算模式
            try:
                self.excel_app.Calculation = -4105  # xlCalculationManual - 手动计算模式
            except Exception:
                pass  # 某些Excel版本不允许修改计算模式

        except Exception as e:
            logger.warning(f"配置Excel应用程序失败: {e}")

    def _load_workbook(self):
        """加载或创建工作簿"""
        try:
            # 如果工作簿已经在_get_or_create_excel_app中设置了，直接返回
            if hasattr(self, 'workbook') and self.workbook is not None:
                logger.info(f"使用已打开的工作簿: {self.workbook.Name}")
                return

            # 检查文件是否存在
            file_exists = os.path.exists(self.file_path) and os.path.getsize(self.file_path) > 0

            if file_exists:
                # 文件存在，尝试打开
                logger.info(f"打开现有Excel文件: {self.file_path}")
                self.workbook = self.excel_app.Workbooks.Open(
                    os.path.abspath(self.file_path),
                    UpdateLinks=0,  # 不更新链接
                    ReadOnly=False,  # 非只读模式
                    Format=None,  # 自动检测格式
                    Password="",  # 空密码
                    WriteResPassword="",  # 空写入密码
                    IgnoreReadOnlyRecommended=True,  # 忽略只读建议
                    AddToMru=False,  # 不添加到最近使用
                    Local=False,  # 不使用本地设置
                    CorruptLoad=0  # 正常加载
                )
                logger.info(f"成功打开Excel文件，工作表数量: {self.workbook.Sheets.Count}")
            else:
                # 文件不存在，创建新工作簿
                logger.info(f"创建新的Excel工作簿: {self.file_path}")
                self.workbook = self.excel_app.Workbooks.Add()
                logger.info("成功创建新的Excel工作簿")

        except Exception as e:
            logger.error(f"无法加载Excel文件 {self.file_path}: {e}")
            raise Exception(f"无法加载Excel文件 {self.file_path}: {e}")

    def _get_worksheet(self, sheet_name: str):
        """获取工作表对象"""
        try:
            return self.workbook.Sheets[sheet_name]
        except:
            raise ValueError(f"工作表 '{sheet_name}' 不存在")

    def _normalize_column(self, col: Union[int, str]) -> str:
        """将列标识符标准化为字母"""
        if isinstance(col, str):
            return col.upper()
        else:
            return get_column_letter(col)

    def read_range(self, sheet_name: str, cell_range: CellRange) -> List[List[Any]]:
        """读取指定区域的数据"""
        range_str = f"{get_column_letter(cell_range.start_col)}{cell_range.start_row}:{get_column_letter(cell_range.end_col)}{cell_range.end_row}"
        logger.debug(f"Win32comEngine读取区域数据: {sheet_name}, {range_str}")

        worksheet = self._get_worksheet(sheet_name)

        # 构建Excel区域字符串
        start_col_letter = get_column_letter(cell_range.start_col)
        end_col_letter = get_column_letter(cell_range.end_col)
        range_str = f"{start_col_letter}{cell_range.start_row}:{end_col_letter}{cell_range.end_row}"

        # 读取区域数据
        range_obj = worksheet.Range(range_str)

        if cell_range.start_row == cell_range.end_row and cell_range.start_col == cell_range.end_col:
            # 单个单元格
            result = [[range_obj.Value]]
        else:
            # 多个单元格
            values = range_obj.Value
            if isinstance(values, tuple):
                # 多行数据
                result = [list(row) if isinstance(row, tuple) else [row] for row in values]
            else:
                # 单行数据
                result = [list(values) if isinstance(values, tuple) else [values]]

        logger.debug(f"成功读取数据，行数: {len(result)}, 列数: {len(result[0]) if result else 0}")
        return result

    def write_cell(self, sheet_name: str, row: int, col: Union[int, str], value: Any):
        """写入单元格数据"""
        logger.debug(f"Win32comEngine写入单元格: {sheet_name}, 行{row}, 列{col}, 值: {value}")
        worksheet = self._get_worksheet(sheet_name)
        col_letter = self._normalize_column(col)
        cell_address = f"{col_letter}{row}"

        cell = worksheet.Range(cell_address)
        cell.Value = value

        # 如果Excel可见，强制刷新显示
        if self.excel_app.Visible and self.excel_app.ScreenUpdating:
            self.excel_app.Calculate()  # 触发重新计算和显示更新

    def write_hyperlink(self, sheet_name: str, row: int, col: Union[int, str], url: str,
                        display_text: str = None) -> None:
        """写入超链接到指定单元格"""
        logger.debug(f"Win32comEngine写入超链接: {sheet_name}, 行{row}, 列{col}, URL: {url}")
        worksheet = self._get_worksheet(sheet_name)
        col_letter = self._normalize_column(col)
        cell_address = f"{col_letter}{row}"

        if display_text is None:
            display_text = url

        # 使用win32com的Hyperlinks.Add方法
        cell_range = worksheet.Range(cell_address)
        worksheet.Hyperlinks.Add(
            Anchor=cell_range,
            Address=url,
            TextToDisplay=display_text
        )

    def set_cell_style(self, sheet_name: str, row: int, col: Union[int, str], style: CellStyle):
        """设置单元格样式"""
        worksheet = self._get_worksheet(sheet_name)
        col_letter = self._normalize_column(col)
        cell_address = f"{col_letter}{row}"

        cell = worksheet.Range(cell_address)

        # 设置字体
        cell.Font.Name = style.font_name
        cell.Font.Size = style.font_size
        cell.Font.Bold = style.font_bold
        cell.Font.Italic = style.font_italic

        # 设置字体颜色（win32com使用RGB整数值）
        if style.font_color:
            rgb_color = int(style.font_color, 16)
            # 转换为BGR格式（win32com使用BGR）
            r = (rgb_color >> 16) & 0xFF
            g = (rgb_color >> 8) & 0xFF
            b = rgb_color & 0xFF
            cell.Font.Color = (b << 16) | (g << 8) | r

        # 设置背景颜色
        if style.bg_color:
            rgb_color = int(style.bg_color, 16)
            r = (rgb_color >> 16) & 0xFF
            g = (rgb_color >> 8) & 0xFF
            b = rgb_color & 0xFF
            cell.Interior.Color = (b << 16) | (g << 8) | r

        # 设置对齐方式
        alignment_map = {
            'left': -4131,  # xlLeft
            'center': -4108,  # xlCenter
            'right': -4152,  # xlRight
        }
        if style.alignment_horizontal in alignment_map:
            cell.HorizontalAlignment = alignment_map[style.alignment_horizontal]

        vertical_alignment_map = {
            'top': -4160,  # xlTop
            'center': -4108,  # xlCenter
            'bottom': -4107,  # xlBottom
        }
        if style.alignment_vertical in vertical_alignment_map:
            cell.VerticalAlignment = vertical_alignment_map[style.alignment_vertical]

        # 设置边框（简化处理）
        if style.border_style:
            border_weight_map = {
                'thin': 2,  # xlThin
                'medium': -4138,  # xlMedium
                'thick': 4,  # xlThick
            }
            if style.border_style in border_weight_map:
                weight = border_weight_map[style.border_style]
                cell.Borders.Weight = weight

    def create_sheet(self, sheet_name: str) -> bool:
        """创建新工作表"""
        try:
            # 检查工作表是否已存在
            try:
                self.workbook.Sheets[sheet_name]
                return False  # 工作表已存在
            except:
                pass  # 工作表不存在，继续创建

            # 创建新工作表
            self.workbook.Sheets.Add().Name = sheet_name
            return True
        except Exception:
            return False

    def delete_sheet(self, sheet_name: str) -> bool:
        """删除工作表"""
        try:
            worksheet = self.workbook.Sheets[sheet_name]
            worksheet.Delete()
            return True
        except Exception:
            return False

    def get_sheet_names(self) -> List[str]:
        """获取所有工作表名称"""
        try:
            return [sheet.Name for sheet in self.workbook.Sheets]
        except Exception:
            return []

    def save(self, file_path: Optional[str] = None):
        """保存文件"""
        try:
            if file_path:
                # 指定了新的文件路径，另存为
                self.workbook.SaveAs(os.path.abspath(file_path))
                self.file_path = file_path
            else:
                # 检查是否是已打开的现有文件
                if (hasattr(self, '_existing_file_opened') and self._existing_file_opened and
                        hasattr(self.workbook, 'FullName') and self.workbook.FullName):
                    # 已打开的现有文件，直接保存
                    self.workbook.Save()
                elif hasattr(self.workbook, 'FullName') and self.workbook.FullName:
                    # 有文件路径的工作簿，直接保存
                    self.workbook.Save()
                else:
                    # 新创建的工作簿，另存为指定路径
                    self.workbook.SaveAs(os.path.abspath(self.file_path))
        except Exception as e:
            raise Exception(f"保存文件失败: {e}")

    def copy_sheet(self, source_sheet: str, target_sheet: str) -> bool:
        """
        复制工作表（win32com专用方法）

        Args:
            source_sheet: 源工作表名称
            target_sheet: 目标工作表名称

        Returns:
            是否复制成功
        """
        try:
            logger.debug(f"Win32com复制工作表: {source_sheet} -> {target_sheet}")

            # 获取源工作表
            source_ws = self._get_worksheet(source_sheet)

            # 检查目标工作表是否已存在
            try:
                self.workbook.Sheets[target_sheet]
                logger.warning(f"目标工作表 '{target_sheet}' 已存在")
                return False
            except:
                pass  # 工作表不存在，继续创建

            # 临时启用屏幕更新以确保实时显示
            original_screen_updating = self.excel_app.ScreenUpdating
            self.excel_app.ScreenUpdating = True

            try:
                # 使用Excel的Copy方法复制工作表
                # 这会复制所有内容：数据、格式、图片、图表、宏等
                source_ws.Copy(After=self.workbook.Sheets(self.workbook.Sheets.Count))

                # 重命名新复制的工作表
                new_sheet = self.workbook.Sheets(self.workbook.Sheets.Count)
                new_sheet.Name = target_sheet

                # 强制刷新显示
                self.excel_app.Calculate()  # 重新计算
                if self.excel_app.Visible:
                    # 如果Excel可见，激活新工作表以确保用户能看到
                    new_sheet.Activate()

                logger.info(f"成功复制工作表: {source_sheet} -> {target_sheet}")
                return True

            finally:
                # 恢复原始的屏幕更新设置
                self.excel_app.ScreenUpdating = original_screen_updating

        except Exception as e:
            logger.error(f"复制工作表失败: {e}")
            return False

    def refresh_display(self):
        """强制刷新Excel显示"""
        try:
            if self.excel_app:
                # 临时启用屏幕更新
                original_screen_updating = self.excel_app.ScreenUpdating
                self.excel_app.ScreenUpdating = True

                # 强制重新计算和刷新
                self.excel_app.Calculate()

                # 恢复原始设置
                self.excel_app.ScreenUpdating = original_screen_updating

                logger.debug("Excel显示已刷新")
        except Exception as e:
            logger.warning(f"刷新Excel显示失败: {e}")

    def close(self):
        """关闭文件"""
        try:
            """关闭当前工作簿，并在没有其他打开的工作簿时关闭Excel应用程序"""
            # 关闭当前工作簿
            if self.workbook is not None:
                try:
                    self.workbook.Close(SaveChanges=False)
                    logger.debug(f"已关闭工作簿: {self.file_path}")
                except Exception as e:
                    logger.error(f"关闭工作簿时出错: {e}")
                finally:
                    self.workbook = None

            # 检查是否还有其他打开的工作簿
            if self.excel_app is not None:
                try:
                    # 如果没有其他工作簿打开，则关闭Excel应用程序
                    if self.excel_app.Workbooks.Count == 0:
                        self.excel_app.Quit()
                        logger.debug("已关闭Excel应用程序（没有其他打开的工作簿）")
                    else:
                        logger.debug(f"保留Excel应用程序打开（还有 {self.excel_app.Workbooks.Count} 个工作簿打开）")
                except Exception as e:
                    logger.error(f"检查工作簿数量或关闭Excel应用程序时出错: {e}")
                finally:
                    # 释放COM对象引用
                    self.excel_app = None
            # 清理COM
            try:
                import pythoncom
                pythoncom.CoUninitialize()
            except:
                pass

        except Exception as e:
            print(f"关闭Excel时出错: {e}")


class WorksheetContext:
    """
    工作表上下文管理器

    提供对特定工作表的便捷操作，包括：
    1. 插入/删除行和列
    2. 读写单元格数据
    3. 设置样式
    4. 自动保存和刷新

    使用示例:
        with excel.worksheet("Sheet1") as ws:
            # 插入行
            ws.insert_rows(3, 2)  # 在第3行前插入2行

            # 删除列
            ws.delete_columns("C", 1)  # 删除C列

            # 写入数据
            ws.write_cell(1, 1, "标题")

            # 设置样式
            ws.set_cell_style(1, 1, CellStyle(font_bold=True))
    """

    def __init__(self, excel_util, sheet_name: str):
        """
        初始化工作表上下文

        Args:
            excel_util: ExcelUtil实例
            sheet_name: 工作表名称
        """
        self.excel_util = excel_util
        self.sheet_name = sheet_name
        self._original_sheet_name = sheet_name
        self._changes_made = False

    def __enter__(self):
        """进入上下文管理器"""
        # 确保工作表存在
        self.sheet_name = self.excel_util.get_sheet_name_fuzzy(self.sheet_name)
        logger.debug(f"进入工作表上下文: {self.sheet_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        try:
            if self._changes_made:
                # 如果有更改，刷新显示
                self.excel_util.refresh_display()
                logger.debug(f"工作表上下文退出，已刷新显示: {self.sheet_name}")
            else:
                logger.debug(f"工作表上下文退出，无更改: {self.sheet_name}")
        except Exception as e:
            logger.warning(f"退出工作表上下文时出错: {e}")

    def insert_rows(self, start_row: int, count: int = 1):
        """
        插入多行

        Args:
            start_row: 起始行号（从1开始），新行将插入在此行之前
            count: 插入的行数
        """
        try:
            logger.info(f"在工作表 '{self.sheet_name}' 第{start_row}行前插入{count}行")

            if isinstance(self.excel_util.engine, Win32comEngine):
                worksheet = self.excel_util.engine._get_worksheet(self.sheet_name)

                # 选择要插入的行范围
                range_str = f"{start_row}:{start_row + count - 1}"
                rows_range = worksheet.Rows(range_str)

                # 插入行
                rows_range.Insert(Shift=-4121)  # xlShiftDown

            elif isinstance(self.excel_util.engine, OpenpyxlEngine):
                worksheet = self.excel_util.engine.workbook[self.sheet_name]
                worksheet.insert_rows(start_row, count)

            else:
                raise NotImplementedError("当前引擎不支持插入行操作")

            self._changes_made = True
            logger.info(f"成功插入{count}行")

        except Exception as e:
            logger.error(f"插入行失败: {e}")
            raise

    def insert_rows_with_format_copy(self, start_row: int, count: int = 1, copy_from_row: Optional[int] = None):
        """
        插入多行，带格式复制
        
        Args:
            start_row: 起始行号（从1开始），新行将插入在此行之前
            count: 插入的行数
            copy_from_row: 复制格式的源行号，如果为None则复制start_row-1行的格式
        """
        try:
            logger.info(f"在工作表 '{self.sheet_name}' 第{start_row}行前插入{count}行（带格式复制）")

            # 确定格式源行
            if copy_from_row is None:
                copy_from_row = max(1, start_row - 1)  # 默认复制前一行格式

            if isinstance(self.excel_util.engine, Win32comEngine):
                worksheet = self.excel_util.engine._get_worksheet(self.sheet_name)

                # 先复制源行的格式
                source_row = worksheet.Rows(copy_from_row)
                source_row.Copy()

                # 插入新行
                range_str = f"{start_row}:{start_row + count - 1}"
                rows_range = worksheet.Rows(range_str)
                rows_range.Insert(Shift=-4121)  # xlShiftDown

                # 将格式粘贴到新插入的行
                for i in range(count):
                    target_row = start_row + i
                    target_range = worksheet.Rows(target_row)
                    target_range.PasteSpecial(Paste=-4122)  # xlPasteFormats

                # 清除剪贴板
                self.excel_util.engine.excel_app.CutCopyMode = False

            elif isinstance(self.excel_util.engine, OpenpyxlEngine):
                worksheet = self.excel_util.engine.workbook[self.sheet_name]

                # 获取源行的样式信息
                source_row_styles = []
                max_col = worksheet.max_column

                for col in range(1, max_col + 1):
                    source_cell = worksheet.cell(row=copy_from_row, column=col)
                    source_row_styles.append({
                        'font': copy(source_cell.font),
                        'fill': copy(source_cell.fill),
                        'border': copy(source_cell.border),
                        'alignment': copy(source_cell.alignment),
                        'number_format': source_cell.number_format
                    })

                # 插入行
                worksheet.insert_rows(start_row, count)

                # 应用样式到新插入的行
                for row_offset in range(count):
                    target_row = start_row + row_offset
                    for col in range(1, len(source_row_styles) + 1):
                        target_cell = worksheet.cell(row=target_row, column=col)
                        style = source_row_styles[col - 1]

                        target_cell.font = style['font']
                        target_cell.fill = style['fill']
                        target_cell.border = style['border']
                        target_cell.alignment = style['alignment']
                        target_cell.number_format = style['number_format']

            else:
                raise NotImplementedError("当前引擎不支持带格式插入行操作")

            self._changes_made = True
            logger.info(f"成功插入{count}行（已复制格式）")

        except Exception as e:
            logger.error(f"带格式插入行失败: {e}")
            raise

    def delete_rows(self, start_row: int, count: int = 1):
        """
        删除多行

        Args:
            start_row: 起始行号（从1开始）
            count: 删除的行数
        """
        try:
            logger.info(f"在工作表 '{self.sheet_name}' 删除第{start_row}行开始的{count}行")

            if isinstance(self.excel_util.engine, Win32comEngine):
                worksheet = self.excel_util.engine._get_worksheet(self.sheet_name)

                # 选择要删除的行范围
                range_str = f"{start_row}:{start_row + count - 1}"
                rows_range = worksheet.Rows(range_str)

                # 删除行
                rows_range.Delete(Shift=-4162)  # xlShiftUp

            elif isinstance(self.excel_util.engine, OpenpyxlEngine):
                worksheet = self.excel_util.engine.workbook[self.sheet_name]
                worksheet.delete_rows(start_row, count)

            else:
                raise NotImplementedError("当前引擎不支持删除行操作")

            self._changes_made = True
            logger.info(f"成功删除{count}行")

        except Exception as e:
            logger.error(f"删除行失败: {e}")
            raise

    def insert_columns(self, start_col: Union[int, str], count: int = 1):
        """
        插入多列

        Args:
            start_col: 起始列号（数字从1开始或字母如'A'），新列将插入在此列之前
            count: 插入的列数
        """
        try:
            # 标准化列号
            if isinstance(start_col, str):
                start_col_num = column_index_from_string(start_col)
                start_col_letter = start_col.upper()
            else:
                start_col_num = start_col
                start_col_letter = get_column_letter(start_col)

            logger.info(f"在工作表 '{self.sheet_name}' 第{start_col_letter}列前插入{count}列")

            if isinstance(self.excel_util.engine, Win32comEngine):
                worksheet = self.excel_util.engine._get_worksheet(self.sheet_name)

                # 选择要插入的列范围
                end_col_letter = get_column_letter(start_col_num + count - 1)
                range_str = f"{start_col_letter}:{end_col_letter}"
                cols_range = worksheet.Columns(range_str)

                # 插入列
                cols_range.Insert(Shift=-4161)  # xlShiftToRight

            elif isinstance(self.excel_util.engine, OpenpyxlEngine):
                worksheet = self.excel_util.engine.workbook[self.sheet_name]
                worksheet.insert_cols(start_col_num, count)

            else:
                raise NotImplementedError("当前引擎不支持插入列操作")

            self._changes_made = True
            logger.info(f"成功插入{count}列")

        except Exception as e:
            logger.error(f"插入列失败: {e}")
            raise

    def delete_columns(self, start_col: Union[int, str], count: int = 1):
        """
        删除多列

        Args:
            start_col: 起始列号（数字从1开始或字母如'A'）
            count: 删除的列数
        """
        try:
            # 标准化列号
            if isinstance(start_col, str):
                start_col_num = column_index_from_string(start_col)
                start_col_letter = start_col.upper()
            else:
                start_col_num = start_col
                start_col_letter = get_column_letter(start_col)

            logger.info(f"在工作表 '{self.sheet_name}' 删除第{start_col_letter}列开始的{count}列")

            if isinstance(self.excel_util.engine, Win32comEngine):
                worksheet = self.excel_util.engine._get_worksheet(self.sheet_name)

                # 选择要删除的列范围
                end_col_letter = get_column_letter(start_col_num + count - 1)
                range_str = f"{start_col_letter}:{end_col_letter}"
                cols_range = worksheet.Columns(range_str)

                # 删除列
                cols_range.Delete(Shift=-4159)  # xlShiftToLeft

            elif isinstance(self.excel_util.engine, OpenpyxlEngine):
                worksheet = self.excel_util.engine.workbook[self.sheet_name]
                worksheet.delete_cols(start_col_num, count)

            else:
                raise NotImplementedError("当前引擎不支持删除列操作")

            self._changes_made = True
            logger.info(f"成功删除{count}列")

        except Exception as e:
            logger.error(f"删除列失败: {e}")
            raise

    def write_cell(self, row: int, col: Union[int, str], value: Any):
        """
        写入单元格数据

        Args:
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）
            value: 要写入的值
        """
        self.excel_util.write_cell(self.sheet_name, row, col, value)
        self._changes_made = True

    def read_cell(self, row: int, col: Union[int, str]) -> Any:
        """
        读取单元格数据

        Args:
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）

        Returns:
            单元格的值
        """
        return self.excel_util.read_cell(self.sheet_name, row, col)

    def write_range(self, start_row: int, start_col: Union[int, str], data: List[List[Any]]):
        """
        写入区域数据

        Args:
            start_row: 起始行号（从1开始）
            start_col: 起始列号（数字从1开始或字母如'A'）
            data: 二维列表数据
        """
        self.excel_util.write_range(self.sheet_name, start_row, start_col, data)
        self._changes_made = True

    def read_range(self, cell_range: Union[CellRange, str]) -> List[List[Any]]:
        """
        读取指定区域的数据

        Args:
            cell_range: 单元格区域对象或Excel格式字符串（如'A1:C3'）

        Returns:
            二维列表，包含区域内的所有数据
        """
        return self.excel_util.read_range(self.sheet_name, cell_range)

    def set_cell_style(self, row: int, col: Union[int, str], style: CellStyle):
        """
        设置单元格样式

        Args:
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）
            style: 单元格样式对象
        """
        self.excel_util.set_cell_style(self.sheet_name, row, col, style)
        self._changes_made = True

    def set_range_style(self, cell_range: Union[CellRange, str], style: CellStyle):
        """
        设置区域样式

        Args:
            cell_range: 单元格区域对象或Excel格式字符串
            style: 单元格样式对象
        """
        self.excel_util.set_range_style(self.sheet_name, cell_range, style)
        self._changes_made = True

    def auto_fit_columns(self, columns: Optional[List[Union[int, str]]] = None):
        """
        自动调整列宽

        Args:
            columns: 要调整的列列表，如果为None则调整所有列
        """
        self.excel_util.auto_fit_columns(self.sheet_name, columns)
        self._changes_made = True

    def merge_cells(self, cell_range: Union[CellRange, str]):
        """
        合并单元格

        Args:
            cell_range: 要合并的单元格区域
        """
        self.excel_util.merge_cells(self.sheet_name, cell_range)
        self._changes_made = True

    def unmerge_cells(self, cell_range: Union[CellRange, str]):
        """
        取消合并单元格

        Args:
            cell_range: 要取消合并的单元格区域
        """
        self.excel_util.unmerge_cells(self.sheet_name, cell_range)
        self._changes_made = True


class ExcelUtil:
    """
    通用Excel读写工具类

    这是一个通用的Excel操作工具类，提供了丰富的Excel操作功能：
    1. 支持多种操作引擎（openpyxl, pandas）
    2. 提供便捷的单元格读写操作
    3. 支持样式设置和格式化
    4. 支持工作表管理
    5. 便于继承扩展

    使用示例:
        # 创建Excel工具实例
        excel = ExcelUtil("test.xlsx")

        # 读取数据
        data = excel.read_range("Sheet1", CellRange(1, 1, 5, 3))

        # 写入数据
        excel.write_cell("Sheet1", 1, 1, "Hello")

        # 设置样式
        style = CellStyle(font_bold=True, bg_color="FFFF00")
        excel.set_cell_style("Sheet1", 1, 1, style)

        # 保存文件
        excel.save()
    """

    def __init__(self, file_path: str, engine: str = "win32com", auto_create: bool = True):
        """
        初始化Excel工具

        Args:
            file_path: Excel文件路径
            engine: 操作引擎类型（"win32com", "openpyxl" 或 "pandas"）
                   - "win32com": 默认引擎，保护宏和图片，支持完整功能
                   - "openpyxl": 纯Python实现，不依赖Excel程序
                   - "pandas": 高效读取，不支持写入和样式
            auto_create: 如果文件不存在是否自动创建
        """
        self.file_path = file_path
        self.engine_type = engine
        self.auto_create = auto_create
        self.engine = None

        logger.info(f"初始化ExcelUtil - 文件: {file_path}, 引擎: {engine}, 自动创建: {auto_create}")

        # 检查文件是否存在
        file_exists = os.path.exists(file_path) and os.path.getsize(file_path) > 0

        if not file_exists:
            if auto_create:
                logger.info(f"文件不存在，将自动创建: {file_path}")
                # 确保目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                # 创建空的Excel文件
                self._create_empty_excel_file()
            else:
                logger.error(f"Excel文件不存在且未启用自动创建: {file_path}")
                raise FileNotFoundError(f"Excel文件不存在: {file_path}")

        # 初始化引擎
        self._init_engine()

    def _create_empty_excel_file(self):
        """创建空的Excel文件"""
        try:
            if self.engine_type.lower() == "win32com":
                # 使用win32com创建空文件
                try:
                    import win32com.client as win32
                    excel_app = win32.DispatchEx("Excel.Application")
                    excel_app.Visible = False
                    excel_app.DisplayAlerts = False

                    workbook = excel_app.Workbooks.Add()
                    workbook.SaveAs(os.path.abspath(self.file_path))
                    workbook.Close()
                    excel_app.Quit()
                    logger.info(f"使用win32com成功创建Excel文件: {self.file_path}")
                except ImportError:
                    logger.warning("win32com不可用，使用openpyxl创建文件")
                    self._create_with_openpyxl()
                except Exception as e:
                    logger.warning(f"win32com创建文件失败: {e}，使用openpyxl创建文件")
                    self._create_with_openpyxl()
            else:
                # 使用openpyxl创建空文件
                self._create_with_openpyxl()

        except Exception as e:
            logger.error(f"创建Excel文件失败: {e}")
            raise

    def _create_with_openpyxl(self):
        """使用openpyxl创建空Excel文件"""
        try:
            from openpyxl import Workbook
            workbook = Workbook()
            workbook.save(self.file_path)
            workbook.close()
            logger.info(f"使用openpyxl成功创建Excel文件: {self.file_path}")
        except Exception as e:
            logger.error(f"使用openpyxl创建文件失败: {e}")
            raise

    def _init_engine(self):
        """初始化操作引擎"""
        logger.info(f"初始化{self.engine_type}引擎")

        try:
            if self.engine_type.lower() == "win32com":
                self.engine = Win32comEngine(self.file_path)
            elif self.engine_type.lower() == "openpyxl":
                self.engine = OpenpyxlEngine(self.file_path)
            elif self.engine_type.lower() == "pandas":
                self.engine = PandasEngine(self.file_path)

            else:
                logger.error(f"不支持的引擎类型: {self.engine_type}")
                raise ValueError(f"不支持的引擎类型: {self.engine_type}。支持的引擎: win32com, openpyxl, pandas")

            logger.info(f"{self.engine_type}引擎初始化成功")

        except Exception as e:
            logger.error(f"{self.engine_type}引擎初始化失败: {e}")
            raise

    def get_sheet_names(self) -> List[str]:
        """
        获取所有工作表名称

        Returns:
            工作表名称列表
        """
        return self.engine.get_sheet_names()

    def get_sheet_name_fuzzy(self, sheet_name: str) -> str:
        """
        模糊匹配工作表名称

        Args:
            sheet_name: 要匹配的工作表名称

        Returns:
            匹配到的工作表名称
        """
        return select_one(sheet_name, self.get_sheet_names())

    def read_cell(self, sheet_name: str, row: int, col: Union[int, str]) -> Any:
        """
        读取单个单元格的值

        Args:
            sheet_name: 工作表名称
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）

        Returns:
            单元格的值
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)
        cell_range = CellRange(row, col, row, col)
        data = self.engine.read_range(sheet_name, cell_range)
        return data[0][0] if data and data[0] else None

    def read_range(self, sheet_name: str, cell_range: Union[CellRange, str]) -> List[List[Any]]:
        """
        读取指定区域的数据

        Args:
            sheet_name: 工作表名称
            cell_range: 单元格区域对象或Excel格式字符串（如'A1:C3'）

        Returns:
            二维列表，包含区域内的所有数据
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(cell_range, str):
            cell_range = self._parse_excel_range(cell_range)

        return self.engine.read_range(sheet_name, cell_range)

    def read_range_as_dataframe(self, sheet_name: str, cell_range: Union[CellRange, str],
                                has_header: bool = True) -> pd.DataFrame:
        """
        读取指定区域的数据并返回DataFrame

        Args:
            sheet_name: 工作表名称
            cell_range: 单元格区域
            has_header: 第一行是否为表头

        Returns:
            包含数据的DataFrame
        """
        data = self.read_range(sheet_name, cell_range)

        if not data:
            return pd.DataFrame()

        if has_header and len(data) > 1:
            return pd.DataFrame(data[1:], columns=data[0])
        else:
            return pd.DataFrame(data)

    @staticmethod
    def safe_cell_value(value):
        if value is None:
            value = ""
        elif isinstance(value, str):
            if value.startswith(('=', '+', '-')):  # 公式保护
                value = "'" + value
            elif len(value) > 32767:  # 长文本处理
                value = value[:32767]
        return value

    def write_cell(self, sheet_name: str, row: int, col: Union[int, str], value: Any, value_must_be_safe=False, write_fuzzy = True):
        """
        写入单个单元格的值

        Args:
            sheet_name: 工作表名称
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）
            value: 要写入的值
            value_must_be_safe: value值确保能写入excel，如果需要保持公式等特殊格式的value，请保持False
        """
        original_sheet_name = sheet_name
        if write_fuzzy:
            sheet_name = self.get_sheet_name_fuzzy(sheet_name)
        logger.debug(f"写入单元格: {original_sheet_name}({sheet_name}), 行{row}, 列{col}, 值: {value}")
        if value_must_be_safe:
            value = self.safe_cell_value(value)
        self.engine.write_cell(sheet_name, row, col, value)

    def write_range(self, sheet_name: str, start_row: int, start_col: Union[int, str],
                    data: List[List[Any]]):
        """
        写入区域数据

        Args:
            sheet_name: 工作表名称
            start_row: 起始行号（从1开始）
            start_col: 起始列号（数字从1开始或字母如'A'）
            data: 二维列表数据
        """
        original_sheet_name = sheet_name
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        rows = len(data)
        cols = len(data[0]) if data else 0
        logger.info(
            f"写入区域数据: {original_sheet_name}({sheet_name}), 起始位置({start_row},{start_col}), 大小({rows}x{cols})")

        # 标准化列号
        if isinstance(start_col, str):
            start_col = column_index_from_string(start_col)

        for row_idx, row_data in enumerate(data):
            for col_idx, value in enumerate(row_data):
                self.engine.write_cell(
                    sheet_name,
                    start_row + row_idx,
                    start_col + col_idx,
                    value
                )

        logger.info(f"成功写入{rows}行{cols}列数据")

    def write_dataframe(self, sheet_name: str, df: pd.DataFrame, start_row: int = 1,
                        start_col: Union[int, str] = 1, include_header: bool = True):
        """
        写入DataFrame数据

        Args:
            sheet_name: 工作表名称
            df: 要写入的DataFrame
            start_row: 起始行号（从1开始）
            start_col: 起始列号（数字从1开始或字母如'A'）
            include_header: 是否包含表头
        """
        data = []

        if include_header:
            data.append(df.columns.tolist())

        data.extend(df.values.tolist())

        self.write_range(sheet_name, start_row, start_col, data)

    def set_cell_style(self, sheet_name: str, row: int, col: Union[int, str], style: CellStyle):
        """
        设置单个单元格的样式

        Args:
            sheet_name: 工作表名称
            row: 行号（从1开始）
            col: 列号（数字从1开始或字母如'A'）
            style: 单元格样式对象
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)
        self.engine.set_cell_style(sheet_name, row, col, style)

    def set_range_style(self, sheet_name: str, cell_range: Union[CellRange, str], style: CellStyle):
        """
        设置区域样式

        Args:
            sheet_name: 工作表名称
            cell_range: 单元格区域对象或Excel格式字符串
            style: 单元格样式对象
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(cell_range, str):
            cell_range = self._parse_excel_range(cell_range)

        coordinates = cell_range.get_cell_coordinates()
        for row, col in coordinates:
            self.engine.set_cell_style(sheet_name, row, col, style)

    def create_sheet(self, sheet_name: str) -> bool:
        """
        创建新工作表

        Args:
            sheet_name: 工作表名称

        Returns:
            是否创建成功
        """
        return self.engine.create_sheet(sheet_name)

    def delete_sheet(self, sheet_name: str) -> bool:
        """
        删除工作表

        Args:
            sheet_name: 工作表名称

        Returns:
            是否删除成功
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)
        return self.engine.delete_sheet(sheet_name)

    def copy_sheet(self, source_sheet: str, target_sheet: str) -> bool:
        """
        复制工作表

        Args:
            source_sheet: 源工作表名称
            target_sheet: 目标工作表名称

        Returns:
            是否复制成功
        """
        try:
            source_sheet = self.get_sheet_name_fuzzy(source_sheet)

            if isinstance(self.engine, Win32comEngine):
                # 使用win32com引擎的专用复制方法
                # 这会复制所有内容：数据、格式、图片、图表、宏等
                return self.engine.copy_sheet(source_sheet, target_sheet)

            elif isinstance(self.engine, OpenpyxlEngine):
                # 创建新工作表
                if not self.create_sheet(target_sheet):
                    return False

                source_ws = self.engine.workbook[source_sheet]
                target_ws = self.engine.workbook[target_sheet]

                # 复制数据和基本样式
                for row in source_ws.iter_rows():
                    for cell in row:
                        target_cell = target_ws.cell(row=cell.row, column=cell.column)
                        target_cell.value = cell.value
                        if cell.has_style:
                            target_cell.font = cell.font.copy()
                            target_cell.border = cell.border.copy()
                            target_cell.fill = cell.fill.copy()
                            target_cell.number_format = cell.number_format
                            target_cell.protection = cell.protection.copy()
                            target_cell.alignment = cell.alignment.copy()

            return True
        except Exception as e:
            logger.error(f"复制工作表失败: {e}")
            return False

    def auto_fit_columns(self, sheet_name: str, columns: Optional[List[Union[int, str]]] = None):
        """
        自动调整列宽

        Args:
            sheet_name: 工作表名称
            columns: 要调整的列列表，如果为None则调整所有列
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(self.engine, Win32comEngine):
            # win32com引擎的自动调整列宽
            worksheet = self.engine._get_worksheet(sheet_name)

            if columns is None:
                # 调整所有列
                worksheet.Columns.AutoFit()
            else:
                # 调整指定列
                for col in columns:
                    if isinstance(col, str):
                        col_letter = col
                    else:
                        col_letter = get_column_letter(col)
                    worksheet.Columns(col_letter).AutoFit()

        elif isinstance(self.engine, OpenpyxlEngine):
            # openpyxl引擎的自动调整列宽
            worksheet = self.engine.workbook[sheet_name]

            if columns is None:
                # 调整所有列
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = get_column_letter(column[0].column)

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            else:
                # 调整指定列
                for col in columns:
                    if isinstance(col, str):
                        col_letter = col
                        col_num = column_index_from_string(col)
                    else:
                        col_letter = get_column_letter(col)
                        col_num = col

                    max_length = 0
                    for row in range(1, worksheet.max_row + 1):
                        cell = worksheet.cell(row=row, column=col_num)
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[col_letter].width = adjusted_width
        else:
            raise NotImplementedError("自动调整列宽功能仅支持win32com和openpyxl引擎")

    def merge_cells(self, sheet_name: str, cell_range: Union[CellRange, str]):
        """
        合并单元格

        Args:
            sheet_name: 工作表名称
            cell_range: 要合并的单元格区域
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(self.engine, Win32comEngine):
            # win32com引擎的合并单元格
            worksheet = self.engine._get_worksheet(sheet_name)

            if isinstance(cell_range, str):
                range_str = cell_range
            else:
                range_str = cell_range.to_excel_range()

            worksheet.Range(range_str).Merge()

        elif isinstance(self.engine, OpenpyxlEngine):
            # openpyxl引擎的合并单元格
            worksheet = self.engine.workbook[sheet_name]

            if isinstance(cell_range, str):
                worksheet.merge_cells(cell_range)
            else:
                worksheet.merge_cells(cell_range.to_excel_range())
        else:
            raise NotImplementedError("合并单元格功能仅支持win32com和openpyxl引擎")

    def unmerge_cells(self, sheet_name: str, cell_range: Union[CellRange, str]):
        """
        取消合并单元格

        Args:
            sheet_name: 工作表名称
            cell_range: 要取消合并的单元格区域
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(self.engine, Win32comEngine):
            # win32com引擎的取消合并单元格
            worksheet = self.engine._get_worksheet(sheet_name)

            if isinstance(cell_range, str):
                range_str = cell_range
            else:
                range_str = cell_range.to_excel_range()

            worksheet.Range(range_str).UnMerge()

        elif isinstance(self.engine, OpenpyxlEngine):
            # openpyxl引擎的取消合并单元格
            worksheet = self.engine.workbook[sheet_name]

            if isinstance(cell_range, str):
                worksheet.unmerge_cells(cell_range)
            else:
                worksheet.unmerge_cells(cell_range.to_excel_range())
        else:
            raise NotImplementedError("取消合并单元格功能仅支持win32com和openpyxl引擎")

    def refresh_display(self):
        """
        强制刷新Excel显示

        当使用win32com引擎且Excel文件已打开时，调用此方法可以强制刷新显示，
        确保用户能够实时看到通过程序所做的更改。
        """
        if isinstance(self.engine, Win32comEngine):
            self.engine.refresh_display()
        else:
            logger.debug("当前引擎不支持刷新显示功能")

    def worksheet(self, sheet_name: str) -> WorksheetContext:
        """
        获取工作表上下文管理器

        Args:
            sheet_name: 工作表名称

        Returns:
            WorksheetContext对象，可用作上下文管理器

        Example:
            with excel.worksheet("Sheet1") as ws:
                # 插入2行
                ws.insert_rows(3, 2)

                # 删除1列
                ws.delete_columns("C", 1)

                # 写入数据
                ws.write_cell(1, 1, "标题")

                # 设置样式
                ws.set_cell_style(1, 1, CellStyle(font_bold=True))
        """
        return WorksheetContext(self, sheet_name)

    def save(self, file_path: Optional[str] = None):
        """
        保存Excel文件

        Args:
            file_path: 保存路径，如果为None则保存到原文件
        """
        save_path = file_path or self.file_path
        logger.info(f"保存Excel文件: {save_path}")
        try:
            self.engine.save(file_path)
            logger.info("Excel文件保存成功")
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            raise

    def close(self):
        """关闭Excel文件，释放资源"""
        logger.info("关闭Excel文件，释放资源")
        if self.engine:
            try:
                self.engine.close()
                logger.info("Excel引擎资源释放成功")
            except Exception as e:
                logger.warning(f"释放Excel引擎资源时出现警告: {e}")
        else:
            logger.debug("Excel引擎未初始化，无需释放资源")

    def _parse_excel_range(self, range_str: str) -> CellRange:
        """
        解析Excel格式的区域字符串

        Args:
            range_str: Excel格式的区域字符串，如'A1:C3'或'A1'

        Returns:
            CellRange对象
        """
        range_str = range_str.strip().upper()

        if ':' in range_str:
            # 区域格式，如'A1:C3'
            start_cell, end_cell = range_str.split(':')
            start_match = re.match(r'([A-Z]+)(\d+)', start_cell)
            end_match = re.match(r'([A-Z]+)(\d+)', end_cell)

            if not start_match or not end_match:
                raise ValueError(f"无效的区域格式: {range_str}")

            start_col = column_index_from_string(start_match.group(1))
            start_row = int(start_match.group(2))
            end_col = column_index_from_string(end_match.group(1))
            end_row = int(end_match.group(2))

            return CellRange(start_row, start_col, end_row, end_col)
        else:
            # 单个单元格格式，如'A1'
            match = re.match(r'([A-Z]+)(\d+)', range_str)
            if not match:
                raise ValueError(f"无效的单元格格式: {range_str}")

            col = column_index_from_string(match.group(1))
            row = int(match.group(2))

            return CellRange(row, col)

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

    # ==================== 多级表头提取功能 ====================

    def extract_multi_level_headers(self,
                                    sheet_name: str,
                                    start_row: int,
                                    end_row: int,
                                    start_col: int = 1,
                                    end_col: Optional[int] = None,
                                    exclude_hidden_rows: bool = True,
                                    separator: str = ' | ') -> MultiLevelHeaderInfo:
        """
        提取多级表头信息

        这个方法专门用来解决Excel中复杂的多级表头结构，特别是包含合并单元格的情况。
        它能够正确处理合并单元格，并生成合理的层次结构和平铺列名。

        Args:
            sheet_name: 工作表名称
            start_row: 表头开始行（1-based）
            end_row: 表头结束行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            separator: 多级表头连接符

        Returns:
            MultiLevelHeaderInfo对象，包含完整的表头信息

        Example:
            # 提取第1-4行的多级表头
            header_info = excel.extract_multi_level_headers(
                sheet_name="Sheet1",
                start_row=1,
                end_row=4,
                separator=" - "
            )
            print(header_info.flat_columns)  # 查看生成的列名
        """
        logger.info(f"开始提取多级表头 - 工作表: {sheet_name}, 行范围: {start_row}-{end_row}")

        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        # 只有openpyxl和win32com引擎支持合并单元格检测
        if not isinstance(self.engine, (OpenpyxlEngine, Win32comEngine)):
            logger.warning("当前引擎不支持合并单元格检测，将使用简化处理")
            return self._extract_headers_simple(sheet_name, start_row, end_row, start_col, end_col, separator)

        # 自动检测结束列
        if end_col is None:
            end_col = self._detect_max_column(sheet_name, start_row, end_row)
            logger.debug(f"自动检测到结束列: {end_col}")

        # 检查隐藏行
        visible_rows = self._get_visible_rows(sheet_name, start_row, end_row, exclude_hidden_rows)
        logger.debug(f"可见行: {visible_rows}")

        # 提取原始表头数据
        raw_data = self._extract_raw_header_data(sheet_name, visible_rows, start_col, end_col)
        logger.debug(f"提取原始数据完成，行数: {len(raw_data)}")

        # 处理合并单元格
        merged_data = self._process_merged_cells(sheet_name, raw_data, visible_rows, start_col, end_col)
        logger.debug("合并单元格处理完成")

        # 构建层次结构
        hierarchical = self._build_hierarchical_structure(merged_data)
        logger.debug(f"构建层次结构完成，列数: {len(hierarchical)}")

        # 生成平铺列名
        flat_columns = self._generate_flat_columns(hierarchical, separator)
        logger.debug(f"生成平铺列名完成，列数: {len(flat_columns)}")

        header_info = MultiLevelHeaderInfo(
            raw_data=raw_data,
            merged_data=merged_data,
            hierarchical=hierarchical,
            flat_columns=flat_columns,
            visible_rows=visible_rows,
            column_range=(start_col, end_col)
        )

        return header_info

    def read_excel_with_multi_level_headers(self,
                                            sheet_name: str,
                                            header_start_row: int,
                                            header_end_row: int,
                                            data_start_row: int,
                                            start_col: int = 1,
                                            end_col: Optional[int] = None,
                                            exclude_hidden_rows: bool = True,
                                            max_empty_rows: int = 3,
                                            separator: str = ' | ') -> pd.DataFrame:
        """
        使用多级表头提取器读取Excel数据，替代简单的header参数方式

        这个方法专门用来解决当使用header=4等参数时，遇到第三行和第四行合并单元格
        导致列名找不到的问题。它会正确处理合并单元格，生成合理的列名。

        Args:
            sheet_name: 工作表名称
            header_start_row: 表头开始行（1-based）
            header_end_row: 表头结束行（1-based）
            data_start_row: 数据开始行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            max_empty_rows: 连续空行数量阈值，超过此数量认为数据结束
            separator: 多级表头连接符

        Returns:
            处理好的DataFrame，列名为合并后的多级表头

        Example:
            # 替代 pd.read_excel(file_path, header=4)
            df = excel.read_excel_with_multi_level_headers(
                sheet_name="Sheet1",
                header_start_row=1,  # 从第1行开始的表头
                header_end_row=4,    # 到第4行结束的表头
                data_start_row=5,    # 从第5行开始的数据
                separator=" - "      # 自定义分隔符
            )
        """
        logger.info(f"开始读取多级表头Excel数据 - 工作表: {sheet_name}")
        logger.info(f"表头范围: {header_start_row}-{header_end_row}, 数据起始行: {data_start_row}")

        # 提取表头信息
        header_info = self.extract_multi_level_headers(
            sheet_name=sheet_name,
            start_row=header_start_row,
            end_row=header_end_row,
            start_col=start_col,
            end_col=end_col,
            exclude_hidden_rows=exclude_hidden_rows,
            separator=separator
        )

        # 确定实际的列范围
        actual_end_col = header_info.column_range[1]

        # 提取数据部分
        data_end_row = self._find_data_end_row(sheet_name, data_start_row, actual_end_col, max_empty_rows)
        logger.debug(f"数据结束行: {data_end_row}")

        if data_end_row < data_start_row:
            logger.warning("未找到有效数据行")
            return pd.DataFrame(columns=header_info.flat_columns)

        # 读取数据区域
        data_range = CellRange(data_start_row, start_col, data_end_row, actual_end_col)
        data_values = self.read_range(sheet_name, data_range)

        # 创建DataFrame
        df = pd.DataFrame(data_values, columns=header_info.flat_columns)

        logger.info(f"成功读取数据 - 行数: {len(df)}, 列数: {len(df.columns)}")
        return df

    def find_header_range_by_keyword(self,
                                     sheet_name: str,
                                     keyword: str,
                                     max_search_rows: int = 20) -> Tuple[Optional[int], Optional[int]]:
        """
        通过关键词自动查找表头范围

        Args:
            sheet_name: 工作表名称
            keyword: 要搜索的关键词
            max_search_rows: 最大搜索行数

        Returns:
            (表头开始行, 表头结束行) 的元组，如果未找到则返回 (None, None)
        """
        logger.info(f"开始搜索表头关键词: '{keyword}' 在工作表: {sheet_name}")

        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        for row in range(1, max_search_rows + 1):
            for col in range(1, min(20, 50)):  # 搜索前20列
                try:
                    cell_value = self.read_cell(sheet_name, row, col)
                    if cell_value and keyword in str(cell_value):
                        logger.debug(f"在位置({row}, {col})找到关键词")

                        # 找到关键词，尝试确定表头范围
                        header_start = row

                        # 向下搜索，找到表头结束行
                        header_end = row
                        for check_row in range(row + 1, min(row + 5, max_search_rows + 1)):
                            has_content = False
                            for check_col in range(1, min(20, 50)):
                                check_value = self.read_cell(sheet_name, check_row, check_col)
                                if check_value and str(check_value).strip():
                                    has_content = True
                                    break

                            if has_content:
                                header_end = check_row
                            else:
                                break

                        logger.info(f"找到表头范围: {header_start}-{header_end}")
                        return header_start, header_end
                except Exception as e:
                    logger.debug(f"搜索位置({row}, {col})时出错: {e}")
                    continue

        logger.warning(f"未找到包含关键词'{keyword}'的表头")
        return None, None

    # ==================== 多级表头提取辅助方法 ====================

    def _detect_max_column(self, sheet_name: str, start_row: int, end_row: int) -> int:
        """自动检测最大列数"""
        logger.debug(f"自动检测最大列数 - 行范围: {start_row}-{end_row}")

        max_col = 1
        for row in range(start_row, end_row + 1):
            for col in range(1, 100):  # 最多检测100列
                try:
                    value = self.read_cell(sheet_name, row, col)
                    if value and str(value).strip():
                        max_col = max(max_col, col)
                    elif col > max_col + 10:  # 连续10列为空则停止
                        break
                except:
                    break

        logger.debug(f"检测到最大列数: {max_col}")
        return max_col

    def _get_visible_rows(self, sheet_name: str, start_row: int, end_row: int,
                          exclude_hidden: bool) -> List[int]:
        """获取可见行列表"""
        if not exclude_hidden:
            return list(range(start_row, end_row + 1))

        visible_rows = []
        for row in range(start_row, end_row + 1):
            if not self._is_row_hidden(sheet_name, row):
                visible_rows.append(row)

        logger.debug(f"可见行筛选完成，从{end_row - start_row + 1}行中筛选出{len(visible_rows)}行")
        return visible_rows

    def _is_row_hidden(self, sheet_name: str, row_num: int) -> bool:
        """检查行是否隐藏"""
        try:
            if isinstance(self.engine, OpenpyxlEngine):
                worksheet = self.engine.workbook[sheet_name]
                row_dimension = worksheet.row_dimensions.get(row_num)
                return row_dimension and row_dimension.hidden
            elif isinstance(self.engine, Win32comEngine):
                worksheet = self.engine._get_worksheet(sheet_name)
                return worksheet.Rows(row_num).Hidden
            else:
                return False
        except Exception as e:
            logger.debug(f"检查行{row_num}是否隐藏时出错: {e}")
            return False

    def _extract_raw_header_data(self, sheet_name: str, rows: List[int],
                                 start_col: int, end_col: int) -> List[List]:
        """提取原始表头数据"""
        logger.debug(f"提取原始表头数据 - 行: {rows}, 列: {start_col}-{end_col}")

        raw_data = []
        for row in rows:
            row_data = []
            for col in range(start_col, end_col + 1):
                try:
                    value = self.read_cell(sheet_name, row, col)
                    # 清理数据
                    if value is not None:
                        if isinstance(value, str):
                            value = value.replace('\n', ' ').strip()
                        row_data.append(value if value else "")
                    else:
                        row_data.append("")
                except Exception as e:
                    logger.debug(f"读取单元格({row}, {col})时出错: {e}")
                    row_data.append("")
            raw_data.append(row_data)

        logger.debug(f"原始数据提取完成，行数: {len(raw_data)}")
        return raw_data

    def _process_merged_cells(self, sheet_name: str, header_data: List[List],
                              rows: List[int], start_col: int, end_col: int) -> List[List]:
        """处理合并单元格"""
        logger.debug("开始处理合并单元格")

        merged_data = [row[:] for row in header_data]  # 深拷贝

        if isinstance(self.engine, OpenpyxlEngine):
            merged_data = self._process_merged_cells_openpyxl(
                sheet_name, merged_data, rows, start_col, end_col
            )
        elif isinstance(self.engine, Win32comEngine):
            merged_data = self._process_merged_cells_win32com(
                sheet_name, merged_data, rows, start_col, end_col
            )

        logger.debug("合并单元格处理完成")
        return merged_data

    def _process_merged_cells_openpyxl(self, sheet_name: str, merged_data: List[List],
                                       rows: List[int], start_col: int, end_col: int) -> List[List]:
        """使用openpyxl处理合并单元格"""
        worksheet = self.engine.workbook[sheet_name]

        # 获取所有合并单元格范围
        merged_ranges = []
        for merged_range in worksheet.merged_cells.ranges:
            # 检查合并范围是否在我们的表头区域内
            if (merged_range.min_row >= min(rows) and
                    merged_range.max_row <= max(rows) and
                    merged_range.min_col >= start_col and
                    merged_range.max_col <= end_col):
                merged_ranges.append(merged_range)
                logger.debug(f"找到合并单元格: {merged_range}")

        # 处理每个合并单元格
        for merged_range in merged_ranges:
            # 获取合并单元格的值
            top_left_cell = worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
            merged_value = top_left_cell.value if top_left_cell.value is not None else ""
            if isinstance(merged_value, str):
                merged_value = merged_value.replace('\n', ' ').strip()

            # 将值填充到合并范围内的所有单元格
            for row in range(merged_range.min_row, merged_range.max_row + 1):
                if row in rows:
                    row_idx = rows.index(row)
                    for col in range(merged_range.min_col, merged_range.max_col + 1):
                        if start_col <= col <= end_col:
                            col_idx = col - start_col
                            merged_data[row_idx][col_idx] = merged_value

        return merged_data

    def _process_merged_cells_win32com(self, sheet_name: str, merged_data: List[List],
                                       rows: List[int], start_col: int, end_col: int) -> List[List]:
        """使用win32com处理合并单元格"""
        try:
            worksheet = self.engine._get_worksheet(sheet_name)

            # Win32com处理合并单元格比较复杂，这里使用简化方法
            # 检查每个单元格是否是合并单元格的一部分
            for row_idx, row in enumerate(rows):
                for col in range(start_col, end_col + 1):
                    col_idx = col - start_col
                    try:
                        cell = worksheet.Cells(row, col)
                        # 如果单元格是合并单元格的一部分，获取合并区域的值
                        if hasattr(cell, 'MergeArea') and cell.MergeArea.Count > 1:
                            merge_area = cell.MergeArea
                            # 获取合并区域左上角的值
                            merged_value = merge_area.Cells(1, 1).Value
                            if merged_value:
                                if isinstance(merged_value, str):
                                    merged_value = merged_value.replace('\n', ' ').strip()
                                merged_data[row_idx][col_idx] = merged_value
                    except Exception as e:
                        logger.debug(f"处理合并单元格({row}, {col})时出错: {e}")
                        continue

        except Exception as e:
            logger.warning(f"Win32com处理合并单元格时出错: {e}")

        return merged_data

    def _build_hierarchical_structure(self, merged_data: List[List]) -> List[Dict]:
        """构建层次结构"""
        logger.debug("开始构建层次结构")

        if not merged_data:
            return []

        num_cols = len(merged_data[0])
        hierarchical = []

        for col_idx in range(num_cols):
            column_hierarchy = []
            for row_data in merged_data:
                if col_idx < len(row_data):
                    value = row_data[col_idx]
                    if value and str(value).strip():  # 只添加非空值
                        column_hierarchy.append(str(value).strip())

            hierarchical.append({
                'column_index': col_idx,
                'hierarchy': column_hierarchy,
                'levels': len(column_hierarchy)
            })

        logger.debug(f"层次结构构建完成，列数: {len(hierarchical)}")
        return hierarchical

    def _generate_flat_columns(self, hierarchical_headers: List[Dict], separator: str = ' | ') -> List[str]:
        """生成平铺的列名"""
        logger.debug(f"开始生成平铺列名，分隔符: '{separator}'")

        flat_columns = []

        for col_info in hierarchical_headers:
            hierarchy = col_info['hierarchy']
            if hierarchy:
                # 将层次结构连接成单一列名
                # 过滤掉重复的值和空值
                unique_parts = []
                for part in hierarchy:
                    if part and str(part).strip() and (
                            not unique_parts or str(part).strip() != str(unique_parts[-1]).strip()
                    ):
                        unique_parts.append(str(part).strip())

                if unique_parts:
                    column_name = separator.join(unique_parts)
                else:
                    column_name = f"Column_{col_info['column_index'] + 1}"
            else:
                column_name = f"Column_{col_info['column_index'] + 1}"

            flat_columns.append(column_name)

        logger.debug(f"平铺列名生成完成，列数: {len(flat_columns)}")
        return flat_columns

    def _find_data_end_row(self, sheet_name: str, start_row: int, end_col: int,
                           max_empty_rows: int) -> int:
        """查找数据结束行"""
        logger.debug(f"查找数据结束行，起始行: {start_row}, 最大空行数: {max_empty_rows}")

        empty_row_count = 0
        last_data_row = start_row - 1

        # 最多检查1000行
        for row in range(start_row, start_row + 1000):
            has_data = False

            # 检查这一行是否有数据
            for col in range(1, end_col + 1):
                try:
                    value = self.read_cell(sheet_name, row, col)
                    if value and str(value).strip():
                        has_data = True
                        break
                except:
                    continue

            if has_data:
                last_data_row = row
                empty_row_count = 0
            else:
                empty_row_count += 1
                if empty_row_count >= max_empty_rows:
                    break

        logger.debug(f"数据结束行: {last_data_row}")
        return last_data_row

    def _find_data_index_in_row(self, find_value, sheet_name: str, start_row: int, end_col: int,
                                max_empty_rows: int) -> int:
        """
        查找指定值是否存在于某一行的所有列中，并返回该行的索引。

        功能说明:
        - 从指定的起始行开始，逐行检查数据是否包含指定值。
        - 如果连续的空行超过指定的最大空行数，则停止查找。
        - 返回包含指定值的行索引。

        Args:
            find_value: 要查找的值。
            sheet_name (str): 工作表名称。
            start_row (int): 起始行号（从1开始）。
            end_col (int): 检查的列范围的结束列号。
            max_empty_rows (int): 最大允许的连续空行数。

        Returns:
            int: 包含指定值的行索引。如果未找到，则返回 -1。
        """
        logger.debug(f"查找值 '{find_value}' 所在的行，起始行: {start_row}, 最大空行数: {max_empty_rows}")

        empty_row_count = 0  # 连续空行计数

        # 从起始行开始逐行检查
        for row in range(start_row, start_row + 6000):  # 限制最多检查1000行
            try:
                # 获取当前行的所有列数据
                row_data = [self.read_cell(sheet_name, row, col) for col in range(1, end_col + 1) if
                            self.read_cell(sheet_name, row, col) is not None]

                # 检查当前行是否包含指定值
                if str(find_value) in "".join(row_data):
                    logger.debug(f"在第 {row} 行找到值 '{find_value}'")
                    return row  # 返回找到值的行索引

                # 如果当前行没有数据，增加空行计数
                if all(cell is None or str(cell).strip() == "" for cell in row_data):
                    empty_row_count += 1
                    if empty_row_count >= max_empty_rows:
                        logger.debug(f"连续空行超过 {max_empty_rows} 行，停止查找")
                        break
                else:
                    empty_row_count = 0  # 重置空行计数

            except Exception as e:
                logger.debug(f"读取第 {row} 行时出错: {e}")
                continue

        logger.warning(f"未找到值 '{find_value}'")
        return -1  # 如果未找到值，则返回 -1

    def _find_data_index_in_col(self, find_value, sheet_name: str, start_col: int, end_row: int,
                                max_empty_col: int) -> int:
        """
        查找指定值是否存在于某一列的所有行中，并返回该列的索引。

        功能说明:
        - 从指定的起始列开始，逐列检查数据是否包含指定值。
        - 如果连续的空列超过指定的最大空列数，则停止查找。
        - 返回包含指定值的列索引。

        Args:
            find_value: 要查找的值。
            sheet_name (str): 工作表名称。
            start_col (int): 起始列号（从1开始）。
            end_row (int): 检查的行范围的结束行号。
            max_empty_col (int): 最大允许的连续空列数。

        Returns:
            int: 包含指定值的列索引。如果未找到，则返回 -1。
        """
        logger.debug(
            f"查找值 '{find_value}' 所在的列，起始列: {start_col}, 结束行: {end_row}, 最大空列数: {max_empty_col}")

        empty_col_count = 0  # 连续空列计数

        # 从起始列开始逐列检查，最多检查100列
        for col in range(start_col, start_col + 100):
            try:
                # 获取当前列的所有行数据（从第1行到end_row）
                col_data = []
                for row in range(1, end_row + 1):
                    try:
                        cell_value = self.read_cell(sheet_name, row, col)
                        if cell_value is not None:
                            col_data.append(str(cell_value))
                        else:
                            col_data.append("")
                    except:
                        col_data.append("")  # 读取失败时添加空字符串

                # 检查当前列是否包含指定值
                if str(find_value) in "".join(col_data):
                    logger.debug(f"在第 {col} 列找到值 '{find_value}'")
                    return col  # 返回找到值的列索引

                # 如果当前列没有数据，增加空列计数
                if all(cell.strip() == "" for cell in col_data):
                    empty_col_count += 1
                    if empty_col_count >= max_empty_col:
                        logger.debug(f"连续空列超过 {max_empty_col} 列，停止查找")
                        break
                else:
                    empty_col_count = 0  # 重置空列计数

            except Exception as e:
                logger.debug(f"读取第 {col} 列时出错: {e}")
                continue

        logger.warning(f"列搜索未找到值 '{find_value}'")
        return 1  # 如果未找到值，则返回 1

    def _extract_headers_simple(self, sheet_name: str, start_row: int, end_row: int,
                                start_col: int, end_col: Optional[int], separator: str) -> MultiLevelHeaderInfo:
        """简化的表头提取（用于不支持合并单元格检测的引擎）"""
        logger.warning("使用简化的表头提取方法")

        if end_col is None:
            end_col = self._detect_max_column(sheet_name, start_row, end_row)

        rows = list(range(start_row, end_row + 1))
        raw_data = self._extract_raw_header_data(sheet_name, rows, start_col, end_col)

        # 简化处理，不处理合并单元格
        merged_data = raw_data
        hierarchical = self._build_hierarchical_structure(merged_data)
        flat_columns = self._generate_flat_columns(hierarchical, separator)

        return MultiLevelHeaderInfo(
            raw_data=raw_data,
            merged_data=merged_data,
            hierarchical=hierarchical,
            flat_columns=flat_columns,
            visible_rows=rows,
            column_range=(start_col, end_col)
        )

    # ==================== 静态便捷方法 ====================

    @staticmethod
    def read_excel_smart(file_path: str,
                         sheet_name: str = None,
                         header_start_row: int = 1,
                         header_end_row: int = 1,
                         data_start_row: int = 2,
                         start_col: int = 1,
                         end_col: Optional[int] = None,
                         exclude_hidden_rows: bool = True,
                         separator: str = ' | ',
                         engine: str = "win32com") -> pd.DataFrame:
        """
        智能读取Excel文件的静态方法，支持多级表头

        这是一个便捷的静态方法，无需创建ExcelUtil实例即可使用。
        专门用来替代简单的 pd.read_excel(header=4) 调用。

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，如果为None则使用第一个工作表
            header_start_row: 表头开始行（1-based）
            header_end_row: 表头结束行（1-based）
            data_start_row: 数据开始行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            separator: 多级表头连接符
            engine: 使用的引擎类型

        Returns:
            处理好的DataFrame，列名为合并后的多级表头

        Example:
            # 替代 pd.read_excel(file_path, header=4)
            df = ExcelUtil.read_excel_smart(
                file_path="data.xlsx",
                sheet_name="要件一覧",
                header_start_row=1,
                header_end_row=4,
                data_start_row=5,
                separator=" - "
            )
        """
        logger.info(f"智能读取Excel文件: {file_path}")

        with ExcelUtil(file_path, engine=engine, auto_create=False) as excel:
            # 如果没有指定工作表名称，使用第一个工作表
            if sheet_name is None:
                sheet_names = excel.get_sheet_names()
                if not sheet_names:
                    raise ValueError("Excel文件中没有工作表")
                sheet_name = sheet_names[0]
                logger.info(f"使用第一个工作表: {sheet_name}")

            return excel.read_excel_with_multi_level_headers(
                sheet_name=sheet_name,
                header_start_row=header_start_row,
                header_end_row=header_end_row,
                data_start_row=data_start_row,
                start_col=start_col,
                end_col=end_col,
                exclude_hidden_rows=exclude_hidden_rows,
                separator=separator
            )

    @staticmethod
    def extract_headers_smart(file_path: str,
                              sheet_name: str = None,
                              header_start_row: int = 1,
                              header_end_row: int = 1,
                              start_col: int = 1,
                              end_col: Optional[int] = None,
                              exclude_hidden_rows: bool = True,
                              separator: str = ' | ',
                              engine: str = "win32com") -> MultiLevelHeaderInfo:
        """
        智能提取表头信息的静态方法

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，如果为None则使用第一个工作表
            header_start_row: 表头开始行（1-based）
            header_end_row: 表头结束行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            separator: 多级表头连接符
            engine: 使用的引擎类型

        Returns:
            MultiLevelHeaderInfo对象
        """
        logger.info(f"智能提取表头信息: {file_path}")

        with ExcelUtil(file_path, engine=engine, auto_create=False) as excel:
            # 如果没有指定工作表名称，使用第一个工作表
            if sheet_name is None:
                sheet_names = excel.get_sheet_names()
                if not sheet_names:
                    raise ValueError("Excel文件中没有工作表")
                sheet_name = sheet_names[0]
                logger.info(f"使用第一个工作表: {sheet_name}")

            return excel.extract_multi_level_headers(
                sheet_name=sheet_name,
                start_row=header_start_row,
                end_row=header_end_row,
                start_col=start_col,
                end_col=end_col,
                exclude_hidden_rows=exclude_hidden_rows,
                separator=separator
            )

    def set_column_width(self, sheet_name: str, column: Union[int, str], width: float):
        """
        设置单个列的宽度
        
        Args:
            sheet_name: 工作表名称
            column: 列号（数字从1开始或字母如'A'）
            width: 列宽度
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(self.engine, Win32comEngine):
            worksheet = self.engine._get_worksheet(sheet_name)
            if isinstance(column, int):
                from openpyxl.utils import get_column_letter
                col_letter = get_column_letter(column)
            else:
                col_letter = column
            worksheet.Columns(col_letter).ColumnWidth = width

        elif isinstance(self.engine, OpenpyxlEngine):
            worksheet = self.engine.workbook[sheet_name]
            if isinstance(column, int):
                from openpyxl.utils import get_column_letter
                col_letter = get_column_letter(column)
            else:
                col_letter = column
            worksheet.column_dimensions[col_letter].width = width

        else:
            logger.warning(f"当前引擎 {type(self.engine)} 不支持设置列宽")

    def set_column_widths(self, sheet_name: str, column_widths: Dict[Union[int, str], float]):
        """
        批量设置列宽
        
        Args:
            sheet_name: 工作表名称
            column_widths: 列宽字典，键为列号或列字母，值为宽度
        """
        sheet_name = self.get_sheet_name_fuzzy(sheet_name)

        if isinstance(self.engine, Win32comEngine):
            worksheet = self.engine._get_worksheet(sheet_name)
            for column, width in column_widths.items():
                if isinstance(column, int):
                    from openpyxl.utils import get_column_letter
                    col_letter = get_column_letter(column)
                else:
                    col_letter = column
                worksheet.Columns(col_letter).ColumnWidth = width

        elif isinstance(self.engine, OpenpyxlEngine):
            worksheet = self.engine.workbook[sheet_name]
            for column, width in column_widths.items():
                if isinstance(column, int):
                    from openpyxl.utils import get_column_letter
                    col_letter = get_column_letter(column)
                else:
                    col_letter = column
                worksheet.column_dimensions[col_letter].width = width

        else:
            logger.warning(f"当前引擎 {type(self.engine)} 不支持设置列宽")

    def write_hyperlink(self, sheet_name: str, row: int, col: int, url: str, display_text: str = None) -> None:
        """
        写入超链接到指定单元格
        
        Args:
            sheet_name: 工作表名称
            row: 行号
            col: 列号
            url: 链接地址
            display_text: 显示文本，如果为None则使用url
        """
        if display_text is None:
            display_text = url

        self.engine.write_hyperlink(sheet_name, row, col, url, display_text)


# 预定义的常用样式
class CommonStyles:
    """常用样式预定义"""

    # 表头样式
    HEADER = CellStyle(
        font_name="Calibri",
        font_size=12,
        font_bold=True,
        bg_color="D9E1F2",
        alignment_horizontal="center",
        border_style="thin"
    )

    # 数据样式
    DATA = CellStyle(
        font_name="Calibri",
        font_size=11,
        alignment_horizontal="left",
        border_style="thin"
    )

    # 数字样式
    NUMBER = CellStyle(
        font_name="Calibri",
        font_size=11,
        alignment_horizontal="right",
        border_style="thin"
    )

    # 警告样式
    WARNING = CellStyle(
        font_name="Calibri",
        font_size=11,
        font_bold=True,
        font_color="FF0000",
        bg_color="FFCCCC",
        border_style="thin"
    )

    # 成功样式
    SUCCESS = CellStyle(
        font_name="Calibri",
        font_size=11,
        font_bold=True,
        font_color="006600",
        bg_color="CCFFCC",
        border_style="thin"
    )


# 工具函数
def create_excel_util(file_path: str, engine: str = "win32com", auto_create: bool = True) -> ExcelUtil:
    """
    创建ExcelUtil实例的工厂函数

    Args:
        file_path: Excel文件路径
        engine: 操作引擎类型（默认win32com，保护宏和图片）
        auto_create: 如果文件不存在是否自动创建

    Returns:
        ExcelUtil实例
    """
    logger.info(f"创建ExcelUtil实例 - 文件: {file_path}, 引擎: {engine}")
    return ExcelUtil(file_path, engine, auto_create)


# 导出所有公共API
__all__ = [
    'ExcelUtil', 'CellStyle', 'CellRange', 'CommonStyles',
    'MultiLevelHeaderInfo', 'create_excel_util', 'batch_update_cells',
    'read_excel_with_multi_level_headers_simple', 'analyze_excel_structure'
]


def batch_update_cells(excel_util: ExcelUtil, sheet_name: str, updates: Dict[str, Any]):
    """
    批量更新单元格

    Args:
        excel_util: ExcelUtil实例
        sheet_name: 工作表名称
        updates: 更新字典，键为单元格地址（如'A1'），值为要设置的值
    """
    logger.info(f"批量更新单元格，共{len(updates)}个单元格")
    for cell_address, value in updates.items():
        cell_range = excel_util._parse_excel_range(cell_address)
        excel_util.write_cell(sheet_name, cell_range.start_row, cell_range.start_col, value)
    logger.info("批量更新完成")


def read_excel_with_multi_level_headers_simple(file_path: str,
                                               sheet_name: str = None,
                                               header_start_row: int = 1,
                                               header_end_row: int = 4,
                                               data_start_row: int = 5,
                                               separator: str = ' | ',
                                               engine: str = "win32com") -> pd.DataFrame:
    """
    简化的多级表头Excel读取函数，完全兼容原有接口

    这个函数提供了最简单的调用方式，用来替代 pd.read_excel(header=4) 等调用。

    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称，如果为None则使用第一个工作表
        header_start_row: 表头开始行（1-based）
        header_end_row: 表头结束行（1-based）
        data_start_row: 数据开始行（1-based）
        separator: 多级表头连接符
        engine: 使用的引擎类型

    Returns:
        处理好的DataFrame

    Example:
        # 替代 pd.read_excel(file_path, header=4)
        df = read_excel_with_multi_level_headers_simple(
            file_path="data.xlsx",
            sheet_name="要件一覧"
        )

        # 自定义表头范围
        df = read_excel_with_multi_level_headers_simple(
            file_path="data.xlsx",
            sheet_name="要件一覧",
            header_start_row=3,
            header_end_row=5,
            data_start_row=6,
            separator=" - "
        )
    """
    logger.info(f"简化读取多级表头Excel: {file_path}")
    return ExcelUtil.read_excel_smart(
        file_path=file_path,
        sheet_name=sheet_name,
        header_start_row=header_start_row,
        header_end_row=header_end_row,
        data_start_row=data_start_row,
        separator=separator,
        engine=engine
    )


def analyze_excel_structure(file_path: str,
                            sheet_name: str = None,
                            max_rows: int = 20,
                            max_cols: int = 20,
                            engine: str = "win32com") -> Dict[str, Any]:
    """
    分析Excel文件结构，帮助确定表头范围

    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称，如果为None则使用第一个工作表
        max_rows: 最大分析行数
        max_cols: 最大分析列数
        engine: 使用的引擎类型

    Returns:
        包含结构分析结果的字典
    """
    logger.info(f"分析Excel文件结构: {file_path}")

    with ExcelUtil(file_path, engine=engine, auto_create=False) as excel:
        # 如果没有指定工作表名称，使用第一个工作表
        if sheet_name is None:
            sheet_names = excel.get_sheet_names()
            if not sheet_names:
                raise ValueError("Excel文件中没有工作表")
            sheet_name = sheet_names[0]

        # 读取前几行数据进行分析
        analysis_range = CellRange(1, 1, max_rows, max_cols)
        data = excel.read_range(sheet_name, analysis_range)

        # 分析每行的数据密度
        row_analysis = []
        for i, row in enumerate(data, 1):
            non_empty_count = sum(1 for cell in row if cell and str(cell).strip())
            density = non_empty_count / len(row) if row else 0
            row_analysis.append({
                'row': i,
                'non_empty_cells': non_empty_count,
                'total_cells': len(row),
                'density': density,
                'sample_data': [str(cell)[:20] if cell else "" for cell in row[:5]]
            })

        # 寻找可能的表头结束位置
        possible_header_end = 1
        for i, analysis in enumerate(row_analysis):
            if analysis['density'] > 0.3:  # 如果数据密度大于30%
                possible_header_end = i + 1
            elif i > 0 and analysis['density'] < 0.1 and row_analysis[i - 1]['density'] > 0.3:
                # 如果当前行密度很低，但前一行密度较高，可能是表头结束
                break

        result = {
            'file_path': file_path,
            'sheet_name': sheet_name,
            'total_sheets': len(excel.get_sheet_names()),
            'sheet_names': excel.get_sheet_names(),
            'analyzed_rows': len(row_analysis),
            'row_analysis': row_analysis,
            'suggested_header_end': possible_header_end,
            'suggested_data_start': possible_header_end + 1
        }

        logger.info(f"结构分析完成，建议表头结束行: {possible_header_end}")
        return result


if __name__ == '__main__':
    # 使用示例
    try:
        # 创建Excel工具实例（默认使用win32com引擎）
        with ExcelUtil("test.xlsx", auto_create=True) as excel:
            # 写入数据
            excel.write_cell("Sheet1", 1, 1, "姓名")
            excel.write_cell("Sheet1", 1, 2, "年龄")
            excel.write_cell("Sheet1", 2, 1, "张三")
            excel.write_cell("Sheet1", 2, 2, 25)

            # 设置表头样式
            excel.set_cell_style("Sheet1", 1, 1, CommonStyles.HEADER)
            excel.set_cell_style("Sheet1", 1, 2, CommonStyles.HEADER)

            # 设置数据样式
            excel.set_cell_style("Sheet1", 2, 1, CommonStyles.DATA)
            excel.set_cell_style("Sheet1", 2, 2, CommonStyles.NUMBER)

            # 自动调整列宽
            excel.auto_fit_columns("Sheet1")

            # 保存文件
            excel.save()

        print("Excel文件操作完成（使用win32com引擎，保护宏和图片）")

    except Exception as e:
        print(f"操作失败: {e}")
        print("如果win32com不可用，可以尝试使用openpyxl引擎:")
        print('ExcelUtil("test.xlsx", engine="openpyxl")')
