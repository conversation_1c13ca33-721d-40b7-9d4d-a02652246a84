"""
Warning Code Generation Utilities
警告代码生成工具类
"""
import traceback
from typing import Dict, List, Optional
from loguru import logger
from openpyxl.utils import column_index_from_string, get_column_letter

from sdw_agent.service.warning_code_gen_check.util.excel_operation_util import ExcelOperationUtils


class WarningCodeGenUtils:
    """警告代码生成工具类"""

    @staticmethod
    def parse_tools_column(wb_code_gen_tools_wb, sheet_name: Optional[str] = None) -> None:
        """
        解析工具列，将 Excel 文件中的工作表的 A 列内容逐个单元格复制到 Z 列
        
        Args:
            wb_code_gen_tools_wb: 代码生成工具工作簿
            sheet_name: 要操作的工作表名称
        """
        try:
            wb = wb_code_gen_tools_wb
            ws = wb.sheets[sheet_name] if sheet_name else wb.sheets[0]

            # 复制A列到Z列
            WarningCodeGenUtils._copy_column_a_to_z(ws)

            # 设置公式
            WarningCodeGenUtils._set_formulas_in_sheet(wb, ws)

            logger.info("手工处理tools_column完成")

        except Exception as e:
            logger.error(f"解析工具列失败: {str(e)}")
            traceback.print_exc()
            raise e

    @staticmethod
    def _copy_column_a_to_z(ws) -> None:
        """将A列内容复制到Z列"""
        try:
            # 获取 A 列的最后一个非空单元格的行号
            last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row
            logger.info(f"A 列的最后一行: {last_row}")

            # 逐个单元格复制 A 列到 Z 列
            # for row in range(2, last_row + 1):
            #     a_value = ws.range(f"A{row}").value
            #     ws.range(f"Z{row}").value = a_value
            #     logger.info(f"复制 A{row} 的值到 Z{row}: {a_value}")

            # 读取 A 列数据（从第 2 行开始）
            a_column_data = ws.range(f"A2:A{last_row + 1}").value

            # 确保数据是列表
            if not isinstance(a_column_data, list):
                a_column_data = [a_column_data]

            # 将嵌套列表转换为一维列表（如果有嵌套）
            a_column_data = [item[0] if isinstance(item, list) else item for item in a_column_data]

            # 转换为二维数组以便写入
            a_column_data = [[item] for item in a_column_data]

            # 写入到 Z 列（从第 2 行开始）
            ws.range(f"Z2:Z{last_row + 1}").value = a_column_data

            logger.info(f"批量复制 A2:A{last_row} 到 Z2:Z{last_row}")

        except Exception as e:
            logger.error(f"复制A列到Z列失败: {str(e)}")
            raise

    @staticmethod
    def _set_formulas_in_sheet(wb, ws) -> None:
        """在工作表中设置公式"""
        try:
            # 获取 Z 列的最后一个非空单元格的行号
            last_row = wb.sheets["CONTDISP（MET）"].range('A' + str(ws.cells.last_cell.row)).end('up').row
            logger.info(f"Z 列的最后一行: {last_row}")

            ws = wb.sheets["設定シート①"]

            # 遍历每一行，设置公式
            for row in range(2, last_row + 1):
                formula = f"=IF((ROW(I{row + 4})-4)<$K$1,'CONTDISP（MET）'!Z{row},\"\")"
                ws.range(f"Z{row + 4}").formula = formula
                logger.debug(f"设置公式到 Z{row}: {formula}")

        except Exception as e:
            logger.info(f"设置公式失败: {str(e)}")
            raise

    @staticmethod
    def _process_warning_properties(wb, change_row_result: Dict) -> None:
        """处理警告属性"""
        try:
            # 提取不同类型的警告键
            warning_properties = WarningCodeGenUtils._extract_warning_properties(change_row_result)

            # 获取现有的警告属性
            existing_properties = WarningCodeGenUtils._get_existing_warning_properties(wb)

            # 计算需要添加的新属性
            new_properties = WarningCodeGenUtils._calculate_new_properties(
                warning_properties, existing_properties
            )

            # 添加新属性到工作表
            WarningCodeGenUtils._add_properties_to_sheet(wb, new_properties, change_row_result)

        except Exception as e:
            logger.error(f"处理警告属性失败: {str(e)}")
            raise

    @staticmethod
    def _extract_warning_properties(change_row_result: Dict) -> Dict[str, List]:
        """从变更结果中提取警告属性"""
        dual_channel_keys = [
            key for key, value in change_row_result.items()
            if value.get("dual_channel") == "Y"
        ]

        adas_keys = [
            key for key, value in change_row_result.items()
            if value.get("adas") == "Y"
        ]

        new_rule_keys = [
            key for key, value in change_row_result.items()
            if value.get("ソフトSWを伴う割込みメッセージ\n※1") == "○" and int(key) != 606
        ]

        big_intr_keys = [
            key for key, value in change_row_result.items()
            if value.get("big_intr") == "Y"
        ]

        return {
            "dual_channel_Y": dual_channel_keys,
            "adas_Y": adas_keys,
            "new_rule_keys": new_rule_keys,
            "big_intr_keys": big_intr_keys,
        }

    @staticmethod
    def _get_existing_warning_properties(wb) -> Dict[str, List]:
        """获取现有的警告属性"""
        sheet = wb.sheets["警告属性"]

        # 获取第一行的 key
        keys = sheet.range((1, 1), (1, sheet.used_range.last_cell.column)).value

        # 初始化结果字典
        result = {key: [] for key in keys}

        # 遍历每列，将第二行及以下的值添加到对应的 key 列表中
        for col_index, key in enumerate(keys, start=1):
            column_values = sheet.range((2, col_index), (sheet.used_range.last_cell.row, col_index)).value
            result[key] = column_values if column_values else []

        return result

    @staticmethod
    def _calculate_new_properties(warning_properties: Dict, existing_properties: Dict) -> Dict[str, List]:
        """计算需要添加的新属性"""
        new_properties = {
            "dual_channel_add": [
                item for item in warning_properties["dual_channel_Y"]
                if item not in existing_properties.get("双通道警告配置", [])
            ],
            "adas_add": [
                item for item in warning_properties["adas_Y"]
                if item not in existing_properties.get("ADAS", [])
            ],
            "new_rule_keys_add": [
                item for item in warning_properties["new_rule_keys"]
                if item not in existing_properties.get("常新规表示", [])
            ],
            "big_intr_keys_add": [
                item for item in warning_properties["big_intr_keys"]
                if item not in existing_properties.get("全屏中断", [])
            ],
        }

        # 检查 dual_channel_add 的值，并动态更新其他属性
        for item in new_properties["dual_channel_add"]:
            # 如果 item 在 dual_channel_add 中，将 item + 0.01 添加到其他属性中
            if item + 0.01 not in new_properties["adas_add"] and item in new_properties["adas_add"]:
                new_properties["adas_add"].append(item + 0.01)
            if item + 0.01 not in new_properties["new_rule_keys_add"] and item in new_properties["new_rule_keys_add"]:
                new_properties["new_rule_keys_add"].append(item + 0.01)
            if item + 0.01 not in new_properties["big_intr_keys_add"] and item in new_properties["big_intr_keys_add"]:
                new_properties["big_intr_keys_add"].append(item + 0.01)

        return new_properties

    @staticmethod
    def _add_properties_to_sheet(wb, new_properties: Dict[str, List], change_row_result) -> None:
        """将新属性添加到工作表"""
        sheet = wb.sheets["警告属性"]

        # 定义列映射
        column_mapping = {
            "dual_channel_add": "A",
            "adas_add": "B",
            "big_intr_keys_add": "C",
            "new_rule_keys_add": "F"
        }

        for prop_type, values in new_properties.items():
            if values and prop_type in column_mapping:
                column = column_mapping[prop_type]
                WarningCodeGenUtils._add_values_to_column(sheet, column, values, prop_type, change_row_result)

    @staticmethod
    def _add_values_to_column(sheet, column: str, values: List, prop_type, change_row_result) -> None:
        """将值添加到指定列"""
        # 找到列的最后一个非空单元格
        last_row = sheet.range(f"{column}{sheet.cells.last_cell.row}").end('up').row

        # 将值依次写入列的最后
        for i, value in enumerate(values):
            sheet.range(f"{column}{last_row + i + 1}").value = value
            ExcelOperationUtils.set_cell_background_color(sheet, last_row + i + 1, column)
            if prop_type == "big_intr_keys_add":
                if change_row_result[int(value)]["dms"] == "Y":
                    sheet.range(f"{get_column_letter(column_index_from_string(column) + 1)}{last_row + i + 1}").value = "DMS"
                else:
                    sheet.range(f"{get_column_letter(column_index_from_string(column) + 1)}{last_row + i + 1}").value = "OTHER"
                ExcelOperationUtils.set_cell_background_color(sheet, last_row + i + 1, get_column_letter(column_index_from_string(column) + 1))

    @staticmethod
    def copy_column_a_to_z(ws) -> None:
        """
        将工作表的 A 列内容复制到 Z 列，动态检测 A 列的行数
        
        Args:
            ws: xlwings 的工作表对象
        """
        try:
            # 获取 A 列的最后一个非空单元格的行号
            last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row
            logger.debug(f"A 列的最后一行: {last_row}")

            # 获取 A 列的内容
            a_column_range = f"A2:A{last_row}"
            a_column_data = ws.range(a_column_range).value
            logger.debug(f"A 列数据范围: {a_column_range}")

            # 将 A 列的内容复制到 Z 列
            z_column_range = f"Z2:Z{last_row}"
            logger.debug(f"Z 列数据范围: {z_column_range}")
            ws.range(z_column_range).value = a_column_data

            logger.info("A 列内容已成功复制到 Z 列。")

        except Exception as e:
            logger.error(f"复制A列到Z列时发生错误: {e}")
            raise

    @staticmethod
    def update_warning_for_value(wb, sheet_name: str, target_value: int = 1276,
                                 target_column: str = "Q", update_value: str = "×") -> None:
        """
        在指定工作簿的工作表中查找 A 列中值为 target_value 的行，并将对应行的 target_column 修改为 update_value
        
        Args:
            wb: xlwings.Book 已打开的工作簿对象
            sheet_name: 工作表名称
            target_value: 要查找的目标值，默认为 1276
            target_column: 要更新的列，默认为 "Q" 列
            update_value: 更新的值，默认为 "×"
        """
        try:
            # 获取工作表
            ws = wb.sheets[sheet_name]

            # 获取 A 列的最后一个非空单元格的行号
            last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row

            # 遍历 A 列，查找目标值
            for row in range(1, last_row + 1):
                cell_value = ws.range(f"A{row}").value
                try:
                    if int(cell_value) == int(target_value):
                        # 更新对应行的目标列值
                        ws.range(f"{target_column}{row}").value = update_value
                        logger.debug(f"更新第 {row} 行的 {target_column} 列为 {update_value}")
                except (ValueError, TypeError):
                    continue

            logger.info("更新完成！")

        except Exception as e:
            logger.error(f"更新警告值时发生错误: {e}")
            raise
