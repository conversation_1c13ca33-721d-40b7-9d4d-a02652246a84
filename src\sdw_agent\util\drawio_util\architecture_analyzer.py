"""
架构图分析器 - 主要使用接口
提供简单易用的API来分析和操作Draw.io架构图
"""

from sdw_agent.util.drawio_util.drawio_structure_reader import DrawioStructureReader
from sdw_agent.util.drawio_util.drawio_highlighter import DrawioHighlighter
from typing import List, Dict
import json


class ArchitectureAnalyzer:
    """架构图分析器 - 主要使用接口"""
    
    def __init__(self, drawio_file_path: str):
        self.file_path = drawio_file_path
        self.reader = DrawioStructureReader(drawio_file_path)
        self.highlighter = None  # 延迟初始化，避免重复解析
        self.loaded = False
    
    def load(self) -> bool:
        """加载架构图"""
        # 首先解析结构
        if not self.reader.parse():
            print(f"❌ 架构图解析失败: {self.file_path}")
            return False

        # 然后初始化高亮器，传递已解析的reader
        self.highlighter = DrawioHighlighter(self.file_path, self.reader)

        # 加载高亮器（跳过重复解析）
        if not self.highlighter.load(skip_parse=True):
            print(f"❌ 高亮器加载失败: {self.file_path}")
            return False

        self.loaded = True
        print(f"✅ 架构图加载成功: {self.file_path}")
        return True
    
    def get_children_paths(self, parent_path: str) -> List[str]:
        """获取子模块路径列表"""
        if not self.loaded:
            return []

        parent = self.reader.get_module_by_path(parent_path)
        if not parent:
            return []

        children = self.reader.get_children(parent.id)
        return [child.path for child in children]
    
    def get_all_paths(self) -> List[str]:
        """获取所有模块路径（过滤空路径）"""
        if not self.loaded:
            return []
        return [module.path for module in self.reader.modules.values()
                if module.path and module.path.strip()]
    
    def search_paths(self, keyword: str) -> List[str]:
        """搜索包含关键词的路径"""
        if not self.loaded:
            return []

        all_paths = self.get_all_paths()
        return [path for path in all_paths if keyword.lower() in path.lower()]

    def get_path_statistics(self) -> Dict[str, int]:
        """获取路径统计信息"""
        if not self.loaded:
            return {}

        all_modules = list(self.reader.modules.values())
        valid_paths = self.get_all_paths()

        return {
            "total_modules": len(all_modules),
            "valid_paths": len(valid_paths),
            "empty_paths": len(all_modules) - len(valid_paths),
            "max_depth": max([path.count('/') for path in valid_paths] + [0]),
            "avg_depth": sum([path.count('/') for path in valid_paths]) / len(valid_paths) if valid_paths else 0
        }
    
    def export_structure(self, output_path: str = None) -> bool:
        """导出架构结构为JSON"""
        if not self.loaded:
            return False
        
        if output_path is None:
            output_path = self.file_path.replace('.drawio', '_structure.json')
        
        try:
            structure = {
                "metadata": {
                    "source_file": self.file_path,
                    "total_modules": len(self.reader.modules),
                    "root_modules": len(self.reader.root_modules),
                    "statistics": self.reader.get_statistics()
                },
                "modules": {},
                "hierarchy": {}
            }
            
            # 导出模块信息
            for module_id, module in self.reader.modules.items():
                structure["modules"][module_id] = {
                    "name": module.name,
                    "path": module.path,
                    "level": module.level,
                    "position": {"x": module.x, "y": module.y},
                    "size": {"width": module.width, "height": module.height},
                    "parent_id": module.parent_id,
                    "children_ids": module.children
                }
            
            # 导出层级结构
            def build_hierarchy(module_id: str) -> Dict:
                module = self.reader.modules[module_id]
                node = {
                    "id": module_id,
                    "name": module.name,
                    "path": module.path,
                    "children": []
                }
                
                for child_id in module.children:
                    node["children"].append(build_hierarchy(child_id))
                
                return node
            
            structure["hierarchy"] = [
                build_hierarchy(root_id) for root_id in self.reader.root_modules
            ]
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(structure, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 结构已导出: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False
    
    def print_summary(self):
        """打印架构摘要"""
        if not self.loaded:
            print("❌ 架构图未加载")
            return
        
        stats = self.reader.get_statistics()
        
        print(f"\n📊 架构图摘要:")
        print(f"   文件: {self.file_path}")
        print(f"   总模块数: {stats['total_modules']}")
        print(f"   根模块数: {stats['root_modules']}")
        print(f"   最大层级: {stats['max_level']}")
        print(f"   各层级分布: {stats['modules_per_level']}")
        
        print(f"\n📋 根模块列表:")
        for root_id in self.reader.root_modules:
            root_module = self.reader.modules[root_id]
            children_count = len(self.reader.get_all_descendants(root_id))
            print(f"   - {root_module.name} ({children_count} 个子模块)")
