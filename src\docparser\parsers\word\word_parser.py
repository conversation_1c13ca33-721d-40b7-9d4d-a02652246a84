from docx.document import Document as DocxDocument
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import win32com.client

from docparser.models.position import Position
import json
import logging
import os
import re
import time
from base64 import b64encode
from collections import defaultdict
from typing import List, Union
from lxml.etree import _Element
from functools import cached_property
from copy import deepcopy
import webcolors
import shutil
import psutil
from docx import Document
from docx import types as t
from docx.table import Table
from docx.table import _Cell
from docx.image.exceptions import UnrecognizedImageError
from PIL import Image, ImageGrab
from io import BytesIO
import pythoncom
from win32com.client import Dispatch, GetActiveObject
from win32com.client.gencache import GetGeneratePath
from docx.text.paragraph import Paragraph
from docx.text.run import Run
from docx.oxml.text.paragraph import CT_P
from docx.oxml.text.run import CT_R
from docx.oxml.table import CT_Tbl
from docx.oxml.drawing import CT_Drawing
from docx.oxml.ns import qn
from docparser.models.document import DocumentObject, DocumentBlockObject
from docparser.models.text import TextObject, RunObject
from docparser.models.style import StyleObject
from docparser.models.layout import LayoutObject
from docparser.models.table import TableObject, RowObject, CellObject
from docparser.models.comment import CommentObject
from docparser.models.border import BorderObject
from docparser.models.picture import PictureObject
from docparser.models.graphic import GraphicObject
from docparser.common.base import get_img_hash

import pywintypes
import traceback
from more_itertools import peekable

from docparser.core.base_parser import BaseParser

# Configure logging
logger = logging.getLogger('docparser')

PICTURE_SAVE_COUNTER = defaultdict(int)
PICTURE_READ_COUNTER = defaultdict(int)
DPI = 96
WORD_OFFICE_TYPE = ""


def get_cell_specifed_border(cell: _Cell, side: str) -> Union[str, None]:
    """ 获取一个单元格对应side的边框 """
    ct_tc = getattr(cell, "_element", None)
    borders_list = ct_tc.xpath(".//w:tcBorders")
    if borders_list:
        borders = borders_list[0]
        specify_border = borders.find(qn(f"w:{side}"))
        if specify_border is not None:
            specify_border_style = specify_border.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val")
            specify_border_color = specify_border.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}color")
            return str(specify_border_style) + ":" + str(specify_border_color)
        return None
    return None


def get_adjacent_cell_border(table: Table, rid: int, cid: int, side: str) -> Union[str, None]:
    """ 根据side参数选择对应的单元格，并获取其对应的边框
    :param table: Table表格对象
    :param rid: 行索引，从0开始
    :param cid: 列索引，从0开始
    :param side: 单元格边框的方向，如"left"/"top"/"right"/"bottom"
    :return: str or None
    """
    min_row = 0
    max_row = len(table.rows) - 1
    min_col = 0
    max_col = len(table.columns) - 1
    directions = {
        "left": "right",
        "top": "bottom",
        "right": "left",
        "bottom": "top"
    }
    try:
        if side == "left":
            # 向左找一个单元格
            tgt_row = rid
            tgt_col = cid - 1
            if tgt_col < min_col:
                return None
            left_cell = table.cell(tgt_row, tgt_col)
            return get_cell_specifed_border(left_cell, directions.get(side))
        elif side == "top":
            # 向上找一个单元格
            tgt_row = rid - 1
            tgt_col = cid
            if tgt_row < min_row:
                return None
            top_cell = table.cell(tgt_row, tgt_col)
            return get_cell_specifed_border(top_cell, directions.get(side))
        elif side == "right":
            # 向右找一个单元格
            tgt_row = rid
            tgt_col = cid + 1
            if tgt_col > max_col:
                return None
            right_cell = table.cell(tgt_row, tgt_col)
            return get_cell_specifed_border(right_cell, directions.get(side))
        else:
            # 向下找一个单元格
            tgt_row = rid + 1
            tgt_col = cid
            if tgt_row > max_row:
                return None
            bottom_cell = table.cell(tgt_row, tgt_col)
            return get_cell_specifed_border(bottom_cell, directions.get(side))
    except IndexError:
        return None


def get_word_instance():
    # 创建 Word 应用程序对象
    try:
        word_app = GetActiveObject("Word.Application")
    except (pythoncom.com_error, pywintypes.com_error):
        pythoncom.CoInitialize()
        word_app = Dispatch("Word.Application")  # Word.Application
    word_app.Visible = False
    word_app.DisplayAlerts = False

    app_name = word_app.Application.Name
    global WORD_OFFICE_TYPE
    # 判定office的类型
    if "WPS" in app_name or "WPS" in word_app.Path:
        logging.info("current connected WPS Office")
        WORD_OFFICE_TYPE = "wps"
    elif "Microsoft" in app_name:
        logging.info("current connected Microsoft Office")
        WORD_OFFICE_TYPE = "microsoft"
    return word_app


def close_word_instance():
    try:
        word = GetActiveObject("Word.Application")
        word.Quit()
        pythoncom.CoUninitialize()
    except (pythoncom.com_error, pywintypes.com_error) as e:
        logging.error(f"没有活跃的word进程: {e}")
    except AttributeError:
        logging.error(f"关闭应用程序Word.Quit failed.")


class WordLayoutObject(LayoutObject):
    """ WordLayoutObject 子类 """

    def __init__(self):
        super(WordLayoutObject, self).__init__()
        self.is_chapter_title = 0  # 该文本是否为章节标题 - 0 不是; 1 手动章节标题; 2 自动章节标题
        self.chapter_id = ''  # 是章节时，保存章节号


def clean_tgt_process():
    """ 清除旧的office进程 """
    tgt_process = [
        "RuntimeBroker.exe",
        "wps.exe",
        "wpscloudsvr.exe",
        "WINWORD.EXE",
    ]
    # 遍历所有的进程
    cur_ts = time.time()
    for proc in psutil.process_iter():
        if re.findall(r"(?:RuntimeBroker|wps|wpscloudsvr|WINWORD.EXE)", proc.name()) and \
                proc.create_time() < cur_ts - 5:
            try:
                proc.terminate()
                logging.info("old office processes has been killed.")
            except psutil.AccessDenied:
                raise psutil.AccessDenied(msg=f"permission to kill process is denied, please run with manage account")
            except psutil.NoSuchProcess:
                continue


class WordParser(BaseParser):
    """
    Parser for Microsoft Word documents (.doc, .docx).
    """
    clean_flag = 0

    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.docx', '.doc']
        self._word_app = None
        self._page_numbers = {}

        self.first_page_id = 1
        # 按顺序保存所有数据对象，顺序队列
        self._elements_by_order = []
        self._file_path = None
        # 段落页码信息
        self._para_info = {}  # 格式如{ 1: {"content": "段落文本", "page_id": "1"}}
        # 图片页码信息
        self._picture_info = []  # 对应docx inline_shapes 顺序存储图片的页码
        self._picture_index = -1  # 对应docx inline_shapes 顺序存储图片的索引
        self._picture_index_switch = False
        # 图形页码信息
        self._graphic_info = {}  # 存储图形的信息，如{"Rectangle 2": {"data": b"xxx", "page_id": "1"}}
        self._graphic_index = -1
        self.visio_data = []
        self._mapping_gname = {
            "矩形": "Rectangle",
            "右箭头": "Right Arrow",
            "左箭头": "Left Arrow",
            "上箭头": "Upper Arrow",
            "下箭头": "Down Arrow",
            "剪去单角的矩形": "Snip Single Corner Rectangle",
            "直接连接符": "Straight Connector",
            "曲线连接符": "Curved Connector",
            "直接箭头连接符": "Straight Arrow Connector",
            "圆角矩形": "Rounded Rectangle",
            "肘形连接符": "Elbow Connector",
            "矩形标注": "Rectangular Callout",
            "圆角矩形标注": "Rounded Rectangular Callout",
            "椭圆": "Oval",
            "文本框": "TextBox"
        }  # 图形名称映射，从python-docx的中文名映射到pywin32的英文名
        # 页眉图形
        self._header_graphic = {}
        # 页脚图形
        self._footer_graphic = {}
        # 文本框页码
        self._text_frame = []
        self._text_frame_index = -1
        # 解析header、footer路由数据
        self.router = {}
        # 表格页码信息
        self._table_info = {}
        # 标记位
        self.is_need_merge = False
        self.is_blank_para = False
        # 文档块对象
        self._block_obj = None
        # driver目录
        self._driver_dir = os.path.dirname(os.path.abspath(__file__))
        # word文档的总页码
        self._total_pages = 0

    def _extract_metadata(self, doc: DocxDocument) -> None:
        """
        Extract metadata from Word document.
        
        Args:
            doc: Word document
        """
        core_props = doc.core_properties
        self.document.metadata = {
            'title': core_props.title or '',
            'author': core_props.author or '',
            'created': str(core_props.created) if core_props.created else '',
            'modified': str(core_props.modified) if core_props.modified else '',
            'last_modified_by': core_props.last_modified_by or '',
            'revision': core_props.revision or '',
            'category': core_props.category or '',
            'comments': core_props.comments or '',
            'content_status': core_props.content_status or '',
            'subject': core_props.subject or '',
            'keywords': core_props.keywords or '',
            'language': core_props.language or '',
            'version': core_props.version or '',
        }

    def _extract_page_info(self, file_path: str) -> None:
        """
        Extract page count and page numbers using Word COM object.
        
        Args:
            file_path: Path to the Word document
        """
        try:
            # Initialize COM
            pythoncom.CoInitialize()

            # Create Word application
            word_app = win32com.client.Dispatch('Word.Application')
            word_app.Visible = False

            # Open document
            doc = word_app.Documents.Open(os.path.abspath(file_path))

            # Get page count
            self.document.page_count = doc.ComputeStatistics(2)  # wdStatisticPages

            # Get page numbers for paragraphs
            for i, para in enumerate(doc.Paragraphs):
                key = i  # Use paragraph index as key
                self._page_numbers[key] = para.Range.Information(3)  # wdActiveEndPageNumber

            # Close document
            doc.Close(False)
            word_app.Quit()

        except Exception as e:
            logger.error(f"Error extracting page info: {e}")
            self.document.page_count = 1  # Default to 1 page
        finally:
            pythoncom.CoUninitialize()

    def _parse_paragraphs(self, doc: DocxDocument) -> None:
        """
        Parse paragraphs from Word document.
        
        Args:
            doc: Word document
        """
        for i, para in enumerate(doc.paragraphs):
            if not para.text.strip():
                continue  # Skip empty paragraphs

            # Get page number
            page_number = self._page_numbers.get(i, 1)

            # Create text object
            text_obj = self.add_text_object(
                text=para.text,
                page_number=page_number,
                name=f"Paragraph_{i}"
            )

            # Extract style information
            self._extract_paragraph_style(para, text_obj)

            # Extract runs (text with different formatting)
            self._extract_runs(para, text_obj)

    def _extract_paragraph_style(self, para: Paragraph, text_obj: TextObject) -> None:
        """
        Extract style information from paragraph.
        
        Args:
            para: Word paragraph
            text_obj: Text object to update
        """
        # Get paragraph alignment
        if para.alignment == WD_PARAGRAPH_ALIGNMENT.LEFT:
            text_obj.alignment = "left"
        elif para.alignment == WD_PARAGRAPH_ALIGNMENT.CENTER:
            text_obj.alignment = "center"
        elif para.alignment == WD_PARAGRAPH_ALIGNMENT.RIGHT:
            text_obj.alignment = "right"
        elif para.alignment == WD_PARAGRAPH_ALIGNMENT.JUSTIFY:
            text_obj.alignment = "justify"

        # Get paragraph style name
        if para.style:
            text_obj.metadata['style_name'] = para.style.name

    def _extract_runs(self, para: Paragraph, text_obj: TextObject) -> None:
        """
        Extract runs (text with different formatting) from paragraph.
        
        Args:
            para: Word paragraph
            text_obj: Text object to update
        """
        for run in para.runs:
            run_data = {
                'text': run.text,
                'bold': run.bold,
                'italic': run.italic,
                'underline': bool(run.underline),
                'font_name': run.font.name if run.font.name else '',
                'font_size': run.font.size.pt if run.font.size else None,
                'font_color': self._rgb_to_hex(run.font.color.rgb) if run.font.color.rgb else '',
            }
            text_obj.runs.append(run_data)

            # Update text object style based on first run
            if not text_obj.runs:
                text_obj.font_name = run_data['font_name']
                text_obj.font_size = run_data['font_size'] or 0
                text_obj.font_color = run_data['font_color']
                text_obj.bold = run_data['bold']
                text_obj.italic = run_data['italic']
                text_obj.underline = run_data['underline']

    def _parse_tables(self, doc: DocxDocument) -> None:
        """
        Parse tables from Word document.
        
        Args:
            doc: Word document
        """
        table_index = 0
        for table in doc.tables:
            # Create table object
            rows = len(table.rows)
            cols = len(table.columns)

            table_obj = self.add_table_object(
                rows=rows,
                columns=cols,
                page_number=1,  # Default, will try to determine actual page
                name=f"Table_{table_index}"
            )
            table_index += 1

            # Extract cells
            self._extract_table_cells(table, table_obj)

            # Extract table borders
            self._extract_table_borders(table, table_obj)

    def _extract_table_cells(self, table: Table, table_obj: TableObject) -> None:
        """
        Extract cells from table.
        
        Args:
            table: Word table
            table_obj: Table object to update
        """
        for i, row in enumerate(table.rows):
            for j, cell in enumerate(row.cells):
                cell_data = {
                    'row': i,
                    'column': j,
                    'text': cell.text,
                    'content': []
                }

                # Extract paragraphs in cell
                for para in cell.paragraphs:
                    if not para.text.strip():
                        continue

                    para_data = {
                        'text': para.text,
                        'runs': []
                    }

                    # Extract runs in paragraph
                    for run in para.runs:
                        run_data = {
                            'text': run.text,
                            'bold': run.bold,
                            'italic': run.italic,
                            'underline': bool(run.underline),
                            'font_name': run.font.name if run.font.name else '',
                            'font_size': run.font.size.pt if run.font.size else None,
                            'font_color': self._rgb_to_hex(run.font.color.rgb) if run.font.color.rgb else '',
                        }
                        para_data['runs'].append(run_data)

                    cell_data['content'].append(para_data)

                table_obj.cells.append(cell_data)

    def _extract_table_borders(self, table: Table, table_obj: TableObject) -> None:
        """
        Extract border information from table.
        
        Args:
            table: Word table
            table_obj: Table object to update
        """
        # This is a simplified implementation
        # A complete implementation would extract detailed border information
        table_obj.borders = {
            'has_borders': True,
            'border_details': 'See cell-specific border information'
        }

    def _convert_to_docx(self, file_path: str) -> str:
        """
        Convert .doc file to .docx format.
        
        Args:
            file_path: Path to the .doc file
            
        Returns:
            Path to the converted .docx file
        """
        try:
            # Initialize COM
            pythoncom.CoInitialize()

            # Create Word application
            word_app = win32com.client.Dispatch('Word.Application')
            word_app.Visible = False

            # Get absolute path
            abs_path = os.path.abspath(file_path)
            docx_path = os.path.splitext(abs_path)[0] + '.docx'

            # Check if converted file already exists
            if os.path.exists(docx_path):
                return docx_path

            # Open and convert document
            doc = word_app.Documents.Open(abs_path)
            doc.SaveAs2(docx_path, 16)  # 16 = wdFormatDocumentDefault (.docx)
            doc.Close()
            word_app.Quit()

            logger.info(f"Converted {file_path} to {docx_path}")
            return docx_path

        except Exception as e:
            logger.error(f"Error converting .doc to .docx: {e}")
            raise
        finally:
            pythoncom.CoUninitialize()

    def _rgb_to_hex(self, rgb) -> str:
        """
        Convert RGB value to hex color code.
        
        Args:
            rgb: RGB value
            
        Returns:
            Hex color code
        """
        if not rgb:
            return ""

        try:
            # RGB is stored as a tuple (r, g, b)
            r, g, b = rgb
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return ""

    def get_file_path(self):
        """ get file path """
        return self._file_path

    def set_file_path(self, file_path: str) -> None:
        """ set file path """
        self._file_path = file_path
        # 清空当前对象的临时数据
        self._elements_by_order.clear()
        self._para_info.clear()
        self._picture_info.clear()
        self._picture_index = -1
        self._graphic_info.clear()
        PICTURE_SAVE_COUNTER.clear()
        self._graphic_index = -1
        self._table_info.clear()
        self.is_need_merge = False
        self.is_blank_para = False
        self._text_frame.clear()
        self._text_frame_index = -1
        # 页眉图形
        self._header_graphic.clear()
        # 页脚图形
        self._footer_graphic.clear()
        # 路由数据
        self.router.clear()
        self._total_pages = 0

    def _parse_header_graphic(self, doc, word):
        """ 解析页眉中的图形数据 """
        for section in doc.Sections:
            for header in section.Headers:
                for shape in header.Shapes:
                    logging.info(f"read shape bytes for shape name: {shape.Name}")
                    try:
                        shape_text = shape.TextFrame.TextRange.Text.replace("\u3000", " ").strip('\r')
                    except Exception as e:
                        logging.info(f"Shape text can not read")
                        shape_text = ''
                    try:
                        shape_type = shape.Type
                    except Exception as e:
                        logging.info(f"Shape type can not read")
                        shape_type = ''
                    try:
                        graphic_info = {
                            "page_id": str(shape.Anchor.Information(1)),
                            "px_width": str(shape.Width * DPI / 72),
                            "px_height": str(shape.Height * DPI / 72),
                            "text": shape_text,
                            "shape_type": shape_type
                        }
                        if float(shape.Height) <= 0 or float(shape.Width) <= 0:
                            img = None
                        else:
                            shape.Select()
                            word.Selection.CopyAsPicture()
                            time.sleep(0.2)
                            img = ImageGrab.grabclipboard()
                        if img is not None:
                            b_io = BytesIO()
                            img.save(b_io, format="png")
                            graphic_info["data"] = b_io.getvalue()
                            graphic_info["px_width"] = img.width
                            graphic_info["px_height"] = img.height
                        elif WORD_OFFICE_TYPE == "wps":
                            logging.info(f"read shape bytes is none, current WORD_OFFICE_TYPE wps, try save as picture.")
                            raise Exception
                    except (pywintypes.com_error, pythoncom.com_error, Exception) as e:
                        logging.error(e.args)
                        logging.info(f"read shape bytes error, start to try save as picture.")
                        # 临时保存在用户家目录下
                        shape_images = os.path.expanduser("~\\shape_images")
                        # 清空目录
                        if WordParser.can_clean() and os.path.exists(shape_images):
                            shutil.rmtree(shape_images)
                        os.makedirs(shape_images, exist_ok=True)
                        try:
                            cur_image_path = os.path.join(shape_images, f"{shape.Name}.png")
                            shape.SaveAsPicture(cur_image_path)
                            image = Image.open(cur_image_path)
                            b_io = BytesIO()
                            image.save(b_io, format="png")
                            graphic_info["data"] = b_io.getvalue()
                            graphic_info["px_width"] = img.width
                            graphic_info["px_height"] = img.height
                            graphic_info["shape_type"] = shape.Type
                        except Exception as e:
                            # import traceback
                            # print(traceback.format_exc())
                            logging.error(e.args)
                    self._header_graphic[shape.Name] = graphic_info

    def _parse_footer_graphic(self, doc, word):
        """ 解析页脚中的图形数据 """
        for section in doc.Sections:
            for footer in section.Footers:
                for shape in footer.Shapes:
                    logging.info(f"read shape bytes for shape name: {shape.Name}")
                    try:
                        shape_text = shape.TextFrame.TextRange.Text.replace("\u3000", " ").strip('\r')
                    except Exception as e:
                        logging.info(f"Shape text can not read")
                        shape_text = ''
                    try:
                        shape_type = shape.Type
                    except Exception as e:
                        logging.info(f"Shape type can not read")
                        shape_type = ''
                    try:
                        graphic_info = {
                            "page_id": str(shape.Anchor.Information(1)),
                            "px_width": str(shape.Width * DPI / 72),
                            "px_height": str(shape.Height * DPI / 72),
                            "text": shape_text,
                            "shape_type": shape_type
                        }
                        if float(shape.Height) <= 0 or float(shape.Width) <= 0:
                            img = None
                        else:
                            shape.Select()
                            word.Selection.CopyAsPicture()
                            time.sleep(0.2)
                            img = ImageGrab.grabclipboard()
                        if img is not None:
                            b_io = BytesIO()
                            img.save(b_io, format="png")
                            graphic_info["data"] = b_io.getvalue()
                            graphic_info["px_width"] = img.width
                            graphic_info["px_height"] = img.height
                            graphic_info["shape_type"] = shape.Type
                        elif WORD_OFFICE_TYPE == "wps":
                            logging.info(f"read shape bytes is none, current WORD_OFFICE_TYPE wps, try save as picture.")
                            raise Exception
                    except (pywintypes.com_error, pythoncom.com_error, Exception) as e:
                        logging.error(e.args)
                        logging.info(f"read shape bytes error, start to try save as picture.")
                        # 临时保存在用户家目录下
                        shape_images = os.path.expanduser("~\\shape_images")
                        # 清空目录
                        if WordParser.can_clean() and os.path.exists(shape_images):
                            shutil.rmtree(shape_images)
                        os.makedirs(shape_images, exist_ok=True)
                        try:
                            cur_image_path = os.path.join(shape_images, f"{shape.Name}.png")
                            shape.SaveAsPicture(cur_image_path)
                            image = Image.open(cur_image_path)
                            b_io = BytesIO()
                            image.save(b_io, format="png")
                            graphic_info["data"] = b_io.getvalue()
                            graphic_info["px_width"] = img.width
                            graphic_info["px_height"] = img.height
                            graphic_info["shape_type"] = shape.Type
                        except Exception as e:
                            # import traceback
                            # print(traceback.format_exc())
                            logging.error(e.args)
                    self._footer_graphic[shape.Name] = graphic_info

    @staticmethod
    def convert_to_docx(file_path: str) -> str:
        """ 转换doc 为docx 格式 """
        output_file_path = os.path.splitext(file_path)[0] + ".docx"
        try:
            word_app = get_word_instance()
            # 打开 Word 文档
            try:
                doc = word_app.Documents.Open(file_path)
                if doc.ReadOnly:
                    logging.info(f"The file {file_path} is in use and can currently only be opened in read-only mode!")
                else:
                    logging.info(f"The file {file_path} can be normally opened for reading and writing")
            except pythoncom.com_error as e:
                logging.error(f"The file {file_path} cannot be opened normally, it may be in use. Error message: {e}")
            # 保存为 .docx 格式
            doc.SaveAs(output_file_path, FileFormat=12)  # 12 表示 .docx 格式
            # 检查文档是否处于兼容模式
            if doc.CompatibilityMode != 0:  # 0 表示非兼容模式
                logging.info(f"The file {file_path} is in compatibility mode. Attempting to convert...")
                # 将文档保存为非兼容模式的 .docx 格式
                doc.SetCompatibilityMode(0)
                doc.SaveAs(output_file_path, FileFormat=12)  # 12 表示 .docx 格式

            # 关闭文档
            doc.Close(False)
            # 退出 Word 应用程序
            close_word_instance()
            time.sleep(2)
        except Exception as e:
            logging.error(e)
        return output_file_path

    def _read_document(self, file_path: str) -> None:
        """读取文档，将文件数据加载到内存
        :param file_path: 文档的路径
        :return: None
        """
        if not os.path.isfile(file_path):
            raise FileNotFoundError(f"文件{file_path}不存在.")
        elif not file_path.endswith("docx"):
            # 转换doc 到docx
            file_path = self.convert_to_docx(file_path)
        self.set_file_path(file_path)
        try:
            self._parse_para_page_num(save_para_info=False)
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logging.error(e.args)
        # logging.info(f'_graphic_info: {self._graphic_info} _header_graphic: {self._header_graphic} _footer_graphic: {self._footer_graphic} ')
        self._origin_object = Document(file_path)

    def _save_document(self, file_path):
        """写入文档，将文件数据写入到指定路径中
        :param file_path: 文档的路径
        :return: 写入是否成功
        """
        pass

    @classmethod
    def can_clean(cls):
        """ 实现每次程序运行时，仅执行一次的清空操作 """
        if cls.clean_flag == 0:
            cls.clean_flag += 1
            return True
        else:
            return False

    @staticmethod
    def read_bytes(idx, shape, word):
        """ 读取图形的字节数据 """
        logging.info(f"read shape bytes for shape idx: {idx}; name: {shape.Name}; type: {shape.Type}")
        data = [b"", "0", "0"]
        try:
            shape.Select()
            time.sleep(0.1)
            word.Selection.CopyAsPicture()
            time.sleep(0.3)
            image = ImageGrab.grabclipboard()
            if image:
                # image.show()
                b_io = BytesIO()
                image.save(b_io, format='png')
                data[0] = b_io.getvalue()
                data[1] = image.width
                data[2] = image.height
            elif WORD_OFFICE_TYPE == "wps":
                logging.info(f"read shape bytes is none, current WORD_OFFICE_TYPE wps, try save as picture.")
                raise Exception
        except (pywintypes.com_error, pythoncom.com_error, Exception) as e:
            logging.error(e.args)
            logging.info(f"read shape bytes error, start to try save as picture.")
            # 临时保存在用户家目录下
            shape_images = os.path.expanduser("~\\shape_images")
            # 清空目录
            if WordParser.can_clean() and os.path.exists(shape_images):
                shutil.rmtree(shape_images)
            os.makedirs(shape_images, exist_ok=True)
            try:
                cur_image_path = os.path.join(shape_images, f"shape_{idx}_{shape.Name}.png")
                # shape.Select()
                # time.sleep(0.3)
                # word.Selection.SaveAsPicture(cur_image_path)
                shape.SaveAsPicture(cur_image_path)
                if not data[0]:
                    image = Image.open(cur_image_path)
                    b_io = BytesIO()
                    image.save(b_io, format="png")
                    data[0] = b_io.getvalue()
                    data[1] = image.width
                    data[2] = image.height
            except (pywintypes.com_error, pythoncom.com_error):
                logging.error(f"read shape bytes error for {idx}-{shape.Name}")
        except Exception as e:
            # import traceback
            # print(traceback.format_exc())
            logging.error(e.args)
        return data

    @staticmethod
    def is_empty_table(table: TableObject) -> bool:
        """ 判断表格对象是否为空表格 """
        # 表格没有行数据
        if not table.rows:
            return True

        # 检查表格行数据，单元格是否为空
        row_data = []
        for row in table.rows:
            cell_data = []
            for cell_obj in row.cells:
                if cell_obj.text or cell_obj.content:
                    cell_data.append(True)
                else:
                    cell_data.append(False)
            if any(cell_data):
                row_data.append(cell_data)
        if row_data:
            return False
        return True

    def _parse_tbl_page_num(self, doc) -> None:
        """ 基于pywin32 解析表格的页码、单元格页码
        :param doc: pywin32打开的word文档对象
        """
        pre_tbl_idx = -1
        cur_tbl_idx = -1
        for table in doc.Tables:
            table.AllowAutoFit = False
            # 检查是否为空表格
            position_obj = Position()
            try:
                position_obj.x = table.Range.Information(5)
                position_obj.y = table.Range.Information(6)
                position_obj.width = sum(column.Width for column in table.Columns)
                position_obj.height = sum(row.Height for row in table.Rows)
            except Exception as e:
                logging.error(f"Error parsing table position: {e}")
            for pp in table.Range.Paragraphs:
                cond_list = [
                    pp.Range.Text.replace("\r", "").replace("\x07", "") != "",
                    pp.Range.InlineShapes.Count > 0,
                    pp.Range.Tables.Count > 1,
                    pp.Range.ShapeRange.Count > 0
                ]
                if any(cond_list):
                    cur_tbl_idx += 1
                    break
            # 空表则跳过
            if cur_tbl_idx == pre_tbl_idx:
                continue
            # 起始、结束段落
            start_para = table.Range.Paragraphs.First
            end_para = table.Range.Paragraphs.Last
            # 表格的虚标行数、列数
            row_count, column_count = table.Range.Rows.Count, table.Range.Columns.Count
            true_row_count = 0
            all_cells_page_num = []
            tbl_matrix = []
            all_cell_String_num = []
            cell_positions = []
            back_up_position_obj_height = 0
            # 遍历表格内的单元格, 计算页码
            for r_idx in range(row_count):
                # 存储当前行的内容
                cur_row_cnt = []
                temp_page_num = []
                cell_ListString_num = []
                row_height = 0
                for c_idx in range(column_count):
                    cell_position_obj = None
                    list_number = None  # 初始化自动编号为 None
                    try:
                        # pywin32读取的表格单元格从1,1开始
                        cur_cell = table.Cell(r_idx + 1, c_idx + 1)
                        try:
                            cell_position_obj = self.create_position_obj(cur_cell.Range.Information(5),
                                                                    cur_cell.Range.Information(6),
                                                                    cur_cell.Width, cur_cell.Height)
                            cell_positions.append(cell_position_obj)
                            if not position_obj.height and not row_height:
                                row_height = cur_cell.Height
                                back_up_position_obj_height += cur_cell.Height
                        except Exception as e:
                            logging.error(f"Error parsing cell position: {e}")
                        # 获取单元格内首个段落
                        cur_cell_start_para = cur_cell.Range.Paragraphs.First
                        para_text = cur_cell_start_para.Range.Text.replace('\r', '').replace('\x07', '')
                        # 检查是否有自动编号
                        if cur_cell_start_para.Range.ListFormat.ListType != 0:  # ListType != 0 表示有编号
                            list_number = cur_cell_start_para.Range.ListFormat.ListString  # 提取编号字符串
                            # para_text = f"{list_number}"  # 将编号和内容结合

                        if para_text:
                            # 文本段落
                            cur_row_cnt.append(para_text)
                        elif cur_cell_start_para.Range.Tables.Count > 1:
                            cur_row_cnt.append("首个段落内为表格，非文本")
                        elif cur_cell_start_para.Range.InlineShapes.Count:
                            cur_row_cnt.append("首个段落内为图片，非文本")
                        elif cur_cell_start_para.Range.ShapeRange.Count:
                            cur_row_cnt.append("首个段落内为图形，非文本")
                        else:
                            cur_row_cnt.append("")
                            # cur_cell_page_num = str(cur_cell_start_para.Range.Information(1)) + para_text
                        cur_cell_page_num = str(cur_cell_start_para.Range.Information(1))
                    except pythoncom.com_error as e:
                        logging.error(f"Error parsing cell page: {e}")
                        cell_positions.append(cell_position_obj)
                        # 合并的单元格的页码，取前一个cell的页码
                        if all_cells_page_num:
                            cur_cell_page_num = all_cells_page_num[-1]
                        else:
                            cur_cell_page_num = None
                        cur_row_cnt.append("")
                    # 当前单元格的页码加入临时列表中
                    temp_page_num.append(cur_cell_page_num)
                    # 将当前单元格的自动编号信息添加到 cell_list_numbers 中
                    cell_ListString_num.append(list_number)

                # 当前为非空行时，追加到表格数据矩阵中
                # if any(cur_row_cnt):
                true_row_count += 1
                tbl_matrix.append(cur_row_cnt)
                all_cells_page_num.extend(temp_page_num)
                all_cell_String_num.extend(cell_ListString_num)
            if not position_obj.height:
                position_obj.height = back_up_position_obj_height
            # 存储当前表格信息
            self._table_info[cur_tbl_idx] = {
                "rows": true_row_count,
                "cols": column_count,
                "start_page_num": str(start_para.Range.Information(1)),
                "end_page_num": str(end_para.Range.Information(1)),
                "cell_page_num": all_cells_page_num,
                "data": tbl_matrix,
                "cell_String_num": all_cell_String_num,
                "position": position_obj,
                "cell_positions": cell_positions
            }
            # 索引同步
            pre_tbl_idx = cur_tbl_idx

    def clean_text(self, doc, para):
        """
        清理文本中内联图片的占位符/
        :param doc: 基于pywin32打开的文档 com组件对象
        :param para: com组件段落对象
        :return: str
        """
        start = para.Range.Start
        sub_texts = []
        for inline_shape in para.Range.InlineShapes:
            sub_text = doc.Range(start, inline_shape.Range.Start).Text
            sub_texts.append(sub_text)
            start = inline_shape.Range.Start
        sub_text = doc.Range(start + 1, para.Range.End).Text.strip("\r")
        sub_texts.append(sub_text)
        return "".join(sub_texts)

    def extract_images_from_word_html(self, cur_doc):

        shape_images = os.path.expanduser("~\\shape_images")
        # 清空目录
        if WordParser.can_clean() and os.path.exists(shape_images):
            shutil.rmtree(shape_images)
        os.makedirs(shape_images, exist_ok=True)
        cur_image_path = os.path.join(shape_images, f"output.html")
        cur_doc.SaveAs(cur_image_path, FileFormat=10)
        # 1. 定位图片目录
        img_dir = shape_images + "\output.files"

        if not os.path.exists(img_dir):
            logging.info(f"图片目录不存在: {img_dir}")
            return []

        # 2. 遍历目录并过滤图片文件
        image_files = []
        valid_exts = {'.png', '.jpg', '.jpeg', '.gif', '.bmp'}

        for filename in os.listdir(img_dir):
            filepath = os.path.join(img_dir, filename)
            ext = os.path.splitext(filename)[1].lower()

            if ext in valid_exts and os.path.isfile(filepath):
                image_files.append(filepath)

        # 3. 读取图片数据
        images_data = []
        for img_path in image_files:
            try:
                with Image.open(img_path) as img:
                    images_data.append({
                        "path": img_path,
                        "data": img.copy(),  # 保留原始数据
                        "format": img.format,
                        "size": img.size
                    })
            except Exception as e:
                print(f"跳过损坏图片 {img_path}: {e}")

        return images_data

    def _parse_para_page_num(self, save_para_info=False):
        """ 解析段落的页码 """
        logging.info("start to parse paragraph info.")
        global WORD_OFFICE_TYPE
        word = get_word_instance()
        flag = False
        # 打开word文档
        if word.Documents.Count:
            for d_idx in range(word.Documents.Count):
                # 兼容WPS and Microsoft
                cur_idx = d_idx + 1 if WORD_OFFICE_TYPE == "microsoft" else d_idx
                d_doc = word.Documents.Item(cur_idx)  # Microsoft 从1开始, WPS 从0开始
                if self._file_path == d_doc.FullName:
                    cur_doc = d_doc
                    flag = True
                    break
            else:
                cur_doc = word.Documents.Open(self._file_path, ReadOnly=False)
        else:
            cur_doc = word.Documents.Open(self._file_path, ReadOnly=False)

        shape_images = os.path.expanduser("~\\shape_images")
        # 清空目录
        if WordParser.can_clean() and os.path.exists(shape_images):
            shutil.rmtree(shape_images)
        os.makedirs(shape_images, exist_ok=True)
        cur_image_path = os.path.join(shape_images, f"output.html")
        cur_doc.SaveAs(cur_image_path, FileFormat=10)
        images_data = self.extract_images_from_word_html(cur_doc)
        count_idx = 0
        try:
            # 切换word文档的审阅模式 -> 最终状态
            view = word.ActiveWindow.View
            view.ShowRevisionsAndComments = False
            view.ShowFormatChanges = False
        except Exception as e:
            logging.error(traceback.format_exc())
            logging.error(e)
        cur_doc = word.Documents.Open(self._file_path, ReadOnly=False)
        cur_doc.ActiveWindow.View.Type = 3  # wdPrintView（打印视图）
        cur_doc.ActiveWindow.View.Zoom.Percentage = 100  # 100% 缩放
        # 统计当前word文档的总页码
        self._total_pages = cur_doc.Content.Information(4)
        self.document.page_count = self._total_pages
        self.first_page_id = cur_doc.Paragraphs[0].Range.Information(1)
        try:
            for sec in cur_doc.Sections:
                # 检查是否存在页脚（避免报错）
                if sec.Footers.Count > 0:
                    # 重置页码从1开始
                    sec.Footers[0].PageNumbers.StartingNumber = 1
                    self.first_page_id = 1
        except Exception as e:
            logging.error(traceback.format_exc())
            logging.error(e)
        # 遍历body所有段落，并获取页码
        for cur_para in cur_doc.Paragraphs:
            if cur_para.Range.Tables.Count:
                continue
            elif cur_para.Range.InlineShapes.Count:
                # 图片的 int 页码
                page_id = cur_para.Range.Information(1)
                shape_count = cur_para.Range.InlineShapes.Count
                self._picture_info.extend([str(page_id)] * shape_count)
            # 处理文本段落
            text_content = cur_para.Range.Text.replace("\r", "")
            if cur_para.Range.InlineShapes.Count:
                text_content = self.clean_text(cur_doc, cur_para)
            try:
                x = cur_para.Range.Information(5)
                y = cur_para.Range.Information(6)
                position_obj = self.create_position_obj(x, y)
            except Exception as e:
                logging.error(f"Error parsing cur_para {text_content} position: {e}")
                position_obj = None
            if text_content:
                # 段落文本内容
                para_text = self.full_to_half(text_content)
                # .replace("　", " ")\
                # .replace("．", ".")\
                para_text = para_text.replace("\u000b", "").strip(" ").strip("\t").strip("\f")
                if para_text and "\f" not in para_text:
                    count_idx += 1
                    # 获取段落页码
                    page_id = cur_para.Range.Information(1)
                    self._para_info[count_idx] = {
                        "content": para_text.strip(" "),
                        "page_id": str(page_id),
                        "auto_number": cur_para.Range.ListFormat.ListString if cur_para.Range.ListFormat.ListType != 0 else "",
                        "auto_header": cur_para.Range.ListFormat.ListString if cur_para.Range.Style.NameLocal.startswith(
                            "Heading") else "",
                        "left_indent": cur_para.LeftIndent,
                        'position': position_obj
                    }
                elif para_text and "\f" in para_text:
                    pre_id = count_idx + 1
                    cur_id = count_idx + 2
                    count_idx += 2
                    pre_page_id = cur_para.Range.Information(1) - 1
                    cur_page_id = pre_page_id + 1
                    pre_content, cur_content = para_text.strip(" ").split("\f")
                    self._para_info[pre_id] = {
                        "content": self.full_to_half(pre_content),
                        "page_id": str(pre_page_id),
                        'position': position_obj
                    }
                    self._para_info[cur_id] = {
                        "content": self.full_to_half(cur_content),
                        "page_id": str(cur_page_id),
                        'position': position_obj
                    }

        # 解析body表格的页码
        self._parse_tbl_page_num(cur_doc)

        # 遍历文档中的所有内嵌对象
        for shape in cur_doc.InlineShapes:
            data = [b"", "0", "0", "0", None]
            # 导出为图片
            try:
                x = shape.Range.Information(5)
                y = shape.Range.Information(6)
                position_obj = self.create_position_obj(x, y)
            except Exception as e:
                logging.error(f"Error parsing InlineShape position: {e}")
                position_obj = None
            shape.Select()
            word.Selection.CopyAsPicture()
            time.sleep(0.3)
            image = ImageGrab.grabclipboard()
            if image:
                b_io = BytesIO()
                image.save(b_io, format="png")
                data[0] = b_io.getvalue()
                data[1] = image.width
                data[2] = image.height
                data[3] = shape.Range.Information(1)
                data[4] = position_obj
                self.visio_data.append(data)
                print(f"内置对象已保存为图片: ")

        # 解析body图形的页码、数据（包含文本框）
        shapes = list(cur_doc.Shapes)
        for idx, shape in enumerate(shapes):  # 遍历顺序为先读取图形，再读取文本框
            try:
                shape_type = shape.Type
                logging.info(f'Parse shape: {shape.Name} {shape.Id} [{shape_type}]')
                page_id = shape.Anchor.Information(1)
                # 处理文本框
                # if shape.Type == 17:  # 17 表示文本框
                #     self._text_frame.append({
                #         "page_id": str(page_id)
                #     })
                # elif shape.Type == 13:  # 在 python-docx 中为图片
                #     continue
                # else:
                # 处理图形
                shape_name = shape.Name
                position_obj = self.create_position_obj(shape.left, shape.top, shape.width, shape.height)
                try:
                    shape_text = shape.TextFrame.TextRange.Text.replace("\u3000", " ").strip('\r')
                except pythoncom.com_error as e:
                    logging.info(f"Error parsing {shape.Name} shape_text: {e}")
                    shape_text = ''
                if float(shape.Height) <= 0 or float(shape.Width) <= 0:
                    bdata, width, height = b"", "0", "0"
                else:
                    bdata, width, height = self.read_bytes(idx, shape, word)
                    data = self.get_most_sim_data(images_data, shape)
                    if not bdata and data:
                        b_io = BytesIO()
                        data['data'].save(b_io, format='png')
                        bdata = b_io.getvalue()
                        width = data['data'].width
                        height = data['data'].height
                graphic_info = {
                    "data": bdata,
                    "page_id": str(page_id),
                    "px_width": width if width != "0" else str(shape.Width * DPI / 72),
                    "px_height": height if height != "0" else str(shape.Height * DPI / 72),
                    "shape_type": shape_type,
                    "text": shape_text,
                    "position": position_obj,
                }

                PICTURE_SAVE_COUNTER[shape_name] += 1
                self._graphic_info[f'{shape_name}_{PICTURE_SAVE_COUNTER[shape_name]}'] = graphic_info
                if PICTURE_SAVE_COUNTER[shape_name] > 1:
                    if PICTURE_SAVE_COUNTER[shape_name] == 2:
                        self._graphic_info[shape_name] = []
                        self._graphic_info[shape_name].append(self._graphic_info[f'{shape_name}_1'])
                    self._graphic_info[shape_name].append(graphic_info)
            except Exception as e:
                logging.error(traceback.format_exc())
                logging.error(f"Error parsing shape: {e}")

        # 解析页眉 中的图形
        self._parse_header_graphic(cur_doc, word)
        # 解析页脚 中的图形
        self._parse_footer_graphic(cur_doc, word)

        # 关闭文档
        cur_doc.Close(False)
        if not flag:
            try:
                close_word_instance()
                time.sleep(0.5)
            except pythoncom.com_error as e:
                pass

        # 保存段落信息
        if save_para_info:
            with open(self._file_path.rsplit(".", maxsplit=1)[0] + "_pywin32_para_info.json", "w",
                      encoding="utf-8") as f:
                f.write(json.dumps(self._para_info, indent=4, ensure_ascii=False))

    def create_position_obj(self, x=None, y=None, width=None, height=None):
        position = Position()
        if x:
            position.x = x
        if y:
            position.y = y
        if width:
            position.width = width
        if height:
            position.height = height
        return position

    def similar_judge(self, a, b, mini_distance):
        if a > b:
            simi_close = b / a
        else:
            simi_close = a / b
        if simi_close > mini_distance:
            return simi_close
        else:
            return False

    def get_most_sim_data(self, images_data, shape):
        similars = []
        max_i = None
        max_sim_value = 1.9
        for i in range(len(images_data)):
            cur_data = images_data[i]['data']
            width_sim = self.similar_judge(cur_data.width, shape.Width * DPI / 72, 0.95)
            height_sim = self.similar_judge(cur_data.height, shape.Height * DPI / 72, 0.95)
            if width_sim and height_sim:
                if width_sim + height_sim == max_sim_value:
                    similars = []
                    break
                if (width_sim + height_sim) > max_sim_value:
                    max_i = i
                    max_sim_value = width_sim + height_sim
                similars.append(images_data[i])
        if not similars:
            data = None
        elif len(similars) == 1:
            data = similars[0]
        else:
            data = images_data[max_i]
        if data:
            images_data.remove(data)
        return data

    def _parse_auto_chapter(self):
        """ 解析自动章节内容，包括列表项编号等 """
        pass

    def _get_object_info(self, rid):
        """从doc的资源中提取图片
        :param rid: 资源ID
        :return: dict
        """
        doc = self._origin_object
        if not doc or not hasattr(doc, 'inline_shapes'):
            return {}

        # 解析图片数据
        inline_shapes = doc.inline_shapes
        if not hasattr(inline_shapes, 'part'):
            return {}
        part = inline_shapes.part
        if not hasattr(part, 'rels'):
            return {}
        rels = part.rels
        if not rels:
            return {}

        obj = rels.get(rid)
        if not obj:
            return {}
        image = None
        try:
            image = obj.target_part.image
        except AttributeError as e:
            return {}
        except UnrecognizedImageError:
            return {
                "data": obj.target_part.blob
            }
        if not image:
            return {}

        data = {
            "data": image.blob,
            "sha1": image.sha1,
            "width": image.px_width,
            "height": image.px_height
        }
        return data

    def parse_picture_parent(self, picture_obj: PictureObject):
        """ 解析图片的父章节 """
        tgt_data = self.router.get(picture_obj.label, None)
        if tgt_data is None:
            return

        for idx, text_obj in enumerate(reversed(tgt_data)):
            if idx == 0:
                picture_obj.layout.prev_ref = text_obj
                text_obj.layout.next_ref = picture_obj

            if isinstance(text_obj, TextObject) and text_obj.layout.is_chapter_title:
                picture_obj.layout.parent_ref = text_obj
                picture_obj.layout.parent_content = text_obj.text
                break

    def _parse_picture(self, drawing: CT_Drawing, label: str = "body") -> PictureObject:
        """ 解析docx的CT_Drawing对象，返回PictureObject对象 """
        picture_object = PictureObject()
        picture_object.label = label

        # 解析pic id
        pic_a = drawing.xpath(".//pic:blipFill/a:blip")
        if pic_a:
            pic_id = pic_a[0].embed
        else:
            pic_id = ""

        # 遍历CT_Drawing 对象
        for item_d in drawing:
            tag_name = item_d.tag.split("}")[-1]
            # wp:inline 标签
            if tag_name == "inline":
                for item_wp in item_d:
                    tag_name = item_wp.tag.split("}")[-1]

                    # 解析a:graphic标签
                    if tag_name == "graphic":
                        graphic_data = item_wp.graphicData
                        pic = graphic_data.pic
                        pic_name = pic.nvPicPr.cNvPr.name
                        # pic_id = pic.blipFill.blip.embed
                        pic_width = pic.spPr.xfrm.ext.cx
                        pic_height = pic.spPr.xfrm.ext.cy
                        off_x, off_y = pic.spPr.xfrm.off.x, pic.spPr.xfrm.off.y
                        picture_object.id = pic_id
                        picture_object.name = pic_name
                        picture_object.width = str(pic_width)
                        picture_object.height = str(pic_height)
                        # 解析坐标
                        picture_object.top = off_y
                        picture_object.left = off_x
                        # 获取图片的二进制数据
                        d = self._get_object_info(pic_id)
                        if "data" in d:
                            byte_data = d.get("data")
                            picture_object.data = b64encode(byte_data).decode()
                            picture_object.px_width = d.get("width", "0")
                            picture_object.px_height = d.get("height", "0")
                            # 创建image对象
                            b_io = BytesIO(byte_data)
                            img_obj = Image.open(b_io)
                            digest, hash_array = get_img_hash(img_obj)
                            # picture_object._digest = digest
                            # picture_object._hash_array = hash_array
                        # 解析图片的章节
                        self.parse_picture_parent(picture_object)
                        # 解析图片的页码
                        try:
                            if self._picture_index_switch:
                                self._picture_index += 1  # 从self._picture_info列表中取页码
                                picture_object.layout.page_id = self._picture_info[self._picture_index]
                        except IndexError:
                            pass
                    elif tag_name == "effectExtent":
                        picture_object.position._x = item_wp.get("l", None)
                        picture_object.position._y = item_wp.get("t", None)
                        picture_object.position._width = item_wp.get("r", None)
                        picture_object.position._height = item_wp.get("b", None)
            elif tag_name == "anchor":
                pic_name_e = item_d.xpath(".//wp:docPr")
                if pic_name_e:
                    pic_name = pic_name_e[0].name
                else:
                    pic_name = ""
                # 解析宽高
                pic_width_height_e = item_d.xpath(".//wp:extent")
                if pic_width_height_e:
                    pic_width = str(pic_width_height_e[0].cx)
                    pic_height = str(pic_width_height_e[0].cy)
                else:
                    pic_width = ""
                    pic_height = ""
                # 解析偏移量
                h_offset = item_d.xpath(".//wp:positionH/wp:posOffset")
                v_offset = item_d.xpath(".//wp:positionV/wp:posOffset")
                if h_offset:
                    off_x = h_offset[0].text
                else:
                    off_x = ""
                if v_offset:
                    off_y = v_offset[0].text
                else:
                    off_y = ""

                picture_object.id = pic_id
                picture_object.name = pic_name
                picture_object.width = pic_width
                picture_object.height = pic_height
                # 解析坐标
                picture_object.coordinate.top = off_y
                picture_object.coordinate.left = off_x
                # 获取图片的二进制数据
                d = self._get_object_info(pic_id)
                if "data" in d:
                    byte_data = d.get("data")
                    picture_object.data = b64encode(byte_data).decode()
                self.parse_picture_parent(picture_object)
                # 解析图片的页码
                try:
                    PICTURE_READ_COUNTER[pic_name] += 1
                    picture_object.layout.page_id = self._graphic_info.get(
                        f'{pic_name}_{PICTURE_READ_COUNTER[pic_name]}').get("page_id")
                except AttributeError:
                    pass
        return picture_object

    @staticmethod
    def _parse_run_style(run: CT_R) -> StyleObject:
        """
            解析docx CT_R对象的样式，返回StyleObject
        """
        pr = run.rPr
        # 没有属性，返回None
        if pr is None:
            style_obj = StyleObject()
            run_text = getattr(run, 'text', None)
            if run_text and run_text.strip():
                style_obj.font_color = "#000000"
            return style_obj

        # 样式集成到style对象
        style_object = StyleObject()
        # 解析样式
        font_name = pr.rFonts.ascii if pr.rFonts is not None else None
        if font_name:
            # 'MS Gothic' 'MS PGothic' 'ＭＳ Ｐゴシック' 'ＭＳ ゴシック'
            # 将全角字体名替换为半角
            font_name = font_name.replace("ＭＳ", "MS").replace("Ｐ", "P").replace("ゴシック", "Gothic")
            style_object.font_family = font_name
        font_size = float(pr.sz.attrib.values()[0]) / 2 if pr.sz is not None else None
        if font_size:
            font_size = str(font_size) + "pt"
            style_object.font_size = font_size
        # 字体颜色
        color = pr.color.val if pr.color is not None else None
        run_text = getattr(run, 'text', None)
        if run_text and run_text.strip():
            if color is not None:
                if color == "auto":
                    color = "000000"
                color = "#" + str(color)
                style_object.font_color = color
            else:
                color = "#000000"
                style_object.font_color = color
        # 加粗
        bold = pr.b.val if pr.b is not None else None
        if bold is not None:
            style_object.font_style.bold = bold
        italic = pr.i.val if pr.i is not None else None
        if italic is not None:
            style_object.font_style.italic = italic
        underline = pr.u.val if pr.u is not None else None
        if underline:
            underline = True
        else:
            underline = False
        style_object.font_style.underline = underline
        # 删除线
        if pr.strike is not None and pr.strike.val:
            style_object.font_style.strikeout = True
        # 双删除线
        if pr.dstrike is not None and pr.dstrike.val:
            style_object.font_style.double_strikeout = True
        # 背景色
        bg_color = pr.highlight.values() if pr.highlight is not None else None
        if bg_color:
            bg_color = bg_color[0]
        if bg_color != 'none' and bg_color is not None:
            try:
                hex_bg_color = webcolors.name_to_hex(bg_color)
                style_object.background_color = hex_bg_color
            except ValueError:
                pass
        elif bg_color is None:
            shd_bg_color = pr.xpath(".//w:shd")
            if shd_bg_color:
                bg_color = shd_bg_color[0].get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}fill")
                if bg_color is not None and re.match(r'^[0-9A-Fa-f]{6}$', bg_color):
                    style_object.background_color = "#" + bg_color if "#" not in bg_color else bg_color
                elif bg_color is not None and bg_color.isalpha():
                    try:
                        hex_bg_color = webcolors.name_to_hex(bg_color)
                        style_object.background_color = hex_bg_color
                    except ValueError:
                        pass
        # 返回style对象
        return style_object

    # def _parse_object(self, item_r, label: str = "body") -> PictureObject:
    #     """ 将w:object 解析为图片 """
    #     picture_object = PictureObject()
    #     picture_object.label = label
    #     try:
    #         v_shape_type = item_r[0]
    #         v_shape = item_r[1]
    #         v_image_data = v_shape[0]
    #     except IndexError:
    #         return picture_object
    #     # 解析rid
    #     pic_id = v_image_data.get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}id", "")
    #
    #     # 解析偏移量
    #     shape_coordsize = v_shape_type.get("coordsize")
    #     if shape_coordsize:
    #         off_x, off_y = shape_coordsize.split(",")
    #     else:
    #         off_x, off_y = "", ""
    #     # 解析图片宽高
    #     shape_style = v_shape.get("style")
    #     if shape_style:
    #         pic_width, pic_height = shape_style.split(";")
    #         pic_width = pic_width.split(":")[-1]
    #         pic_height = pic_height.split(":")[-1]
    #     else:
    #         pic_width, pic_height = "", ""
    #     # 对象赋值
    #     picture_object.id = pic_id
    #     picture_object.width = pic_width
    #     picture_object.height = pic_height
    #     # 解析坐标
    #     picture_object.coordinate.top = off_y
    #     picture_object.coordinate.left = off_x
    #     # 获取图片的二进制数据
    #     d = self._get_object_info(pic_id)
    #     if "data" in d:
    #         byte_data = d.get("data")
    #         picture_object.data = b64encode(byte_data).decode()
    #     # 解析图片的章节
    #     self.parse_picture_parent(picture_object)
    #     # 解析图片的页码
    #     try:
    #         picture_object.layout.page_id = self._picture_info[self._picture_index]
    #     except IndexError:
    #         pass
    #     return picture_object

    def _parse_run(self, run: CT_R, label: str = "body") -> Union[RunObject, PictureObject, GraphicObject]:
        """
            解析docx CT_R对象:
            若为纯文本节段，则返回 RunObject对象
            若为内联的图片节段，则返回PictureObject对象
            若为内联的图形节段，则返回GraphicObject对象
        """
        # 节段内部是否有图片/图形，用列表存储
        drawing_list = []
        # 节段内为文本，用于RunObject存储
        run_object = RunObject()
        run_object.label = label
        docx_run = Run(run, "")

        # 查看run内部是否为br 分页
        for item_in_run in run:
            tag_name = item_in_run.tag.split("}")[-1]
            if tag_name == "br" and not docx_run.text:
                run_object.text = "br"
                cur_type = item_in_run.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}type")
                if cur_type is not None:
                    run_object.type = cur_type
                return run_object

        # run内非br，则正常解析
        if docx_run.text:  # 仅为文本节段
            run_object.text = docx_run.text
            # 解析run节段的属性
            style_object = self._parse_run_style(run)
            run_object.style = style_object
        else:
            # run内联图片
            for item_r in run:
                tag_name = item_r.tag.split("}")[-1]
                if tag_name == 'drawing':
                    # CT_Drawing 图片解析
                    picture_object = self._parse_picture(item_r, label=label)
                    drawing_list.append(picture_object)
                elif tag_name == "AlternateContent":
                    # _Element对象解析
                    graphic_object = self._parse_graphic(item_r, label=label)
                    # 文本框 作为普通文本
                    if isinstance(graphic_object, TextObject):
                        return graphic_object
                    drawing_list.append(graphic_object)
                elif tag_name == "object":
                    # w:object 对象解析为图片
                    # self._picture_index += 1
                    # picture_object = self._parse_object(item_r, label=label)
                    # drawing_list.append(picture_object)
                    pass
                elif tag_name == "pict":
                    # parse shape with new tag pict
                    graphic_object = self._parse_pict(item_r, label=label)
                    drawing_list.append(graphic_object)

        return drawing_list[0] if drawing_list else run_object

    def _parse_chapter_id(self, text_content: str) -> Union[str, None]:
        """ 解析一个文本段落中的章节编号
            如 1.1 第一章，属于章节标题，可以解析出章节编号1.1
            如 第一章主要讲解通信原理部分，不属于章节标题， 无法解析出章节编号
        """
        # 替换全角为半角
        text_content = text_content.replace("\u3000", " ").replace("．", ".").replace('-', '.')
        # 第一类章节号
        pattern1 = r"^(?: |\u3000)*(\d+\.?\d*\.?\d*\.?(?:\d+ |\.\d*| ))(?: |\u3000)*(?:\w+)"
        match_result = re.findall(pattern1, text_content, re.I)
        if match_result:
            chapter_id = match_result[0]
            # 动态非法关键词列表（如 inch/imch 等）
            invalid_keywords = ["inch:", "imch:"]
            # 构造正则表达式：匹配任意非法关键词变体
            invalid_pattern = re.escape(chapter_id.strip()) + r"(" + "|".join(
                map(re.escape, invalid_keywords)) + r")\s*"
            if re.search(invalid_pattern, text_content, re.I):
                return None
            if "、" in chapter_id:
                chapter_id = chapter_id.replace("、", ".")
            return chapter_id.strip().strip(".")
        else:
            return None

    @staticmethod
    def full_to_half(text):
        half = ''
        for char in text:
            if 65281 <= ord(char) <= 65374:
                half += chr(ord(char) - 65248)
            else:
                half += char
        return half.replace("　", " ")

    def _parse_parent_node(self, text_obj: TextObject, label: str = "body") -> None:
        """ 解析当前文本段落所属的父章节，并直接更新当前TextObject对象 """
        tgt_data = self.router.get(label)

        if tgt_data:
            chapter_id = self._parse_chapter_id(text_obj.text)
            if chapter_id is None:
                # 没有章节号的普通文本
                for idx, ele in enumerate(reversed(tgt_data)):
                    if idx == 0:
                        text_obj.layout.prev_ref = ele
                        ele.layout.next_ref = text_obj
                    if not isinstance(ele, TextObject):
                        continue
                    ele_chapter_id = self._parse_chapter_id(ele.text)
                    if ele_chapter_id:
                        # 普通文本找到父章节
                        text_obj.layout.parent_ref = ele
                        text_obj.layout.parent_content = ele.text
                        break
            else:
                # 有章节号的标题，根据自己的章节号查找父章节
                text_obj.layout.is_chapter_title = 1
                text_obj.layout.chapter_id = chapter_id
                for idx, ele in enumerate(reversed(tgt_data)):
                    if idx == 0:
                        text_obj.layout.prev_ref = ele
                        ele.layout.next_ref = text_obj
                    if not isinstance(ele, TextObject):
                        continue
                    ele_chapter_id = self._parse_chapter_id(ele.text)
                    if ele_chapter_id and self.full_to_half(chapter_id.rsplit(".", maxsplit=1)[0]) == self.full_to_half(
                            ele_chapter_id):
                        # 找到父章节，更新当前TextObject对象
                        text_obj.layout.parent_ref = ele
                        text_obj.layout.parent_content = ele.text
                        break
        else:
            chapter_id = self._parse_chapter_id(text_obj.text)
            if chapter_id:
                text_obj.layout.is_chapter_title = 1
                text_obj.layout.chapter_id = chapter_id
        if not text_obj.layout.parent_ref:
            text_obj.layout.parent_ref = self._block_obj

    def _get_page_id(self, count_idx: int, cur_text_obj: TextObject) -> list:
        """ 获取文本的页码 """
        cur_text_obj_text = self.full_to_half(cur_text_obj.text)
        para_info_by_idx = self._para_info.get(count_idx, None)
        # 信息列表
        info_list = []
        if para_info_by_idx:
            # 根据段落索引找到pywin32解析的段落信息后，开始对比并获取页码
            if cur_text_obj_text.replace('\n', '') == para_info_by_idx.get("content").replace('\n', ''):
                page_id = para_info_by_idx.get("page_id")
                auto_number = para_info_by_idx.get("auto_number")
                auto_header = para_info_by_idx.get("auto_header")
                left_indent = para_info_by_idx.get("left_indent")
                if para_info_by_idx.get('position'):
                    cur_text_obj.position = para_info_by_idx.get('position')
            else:
                # 段落错位，微调进行上下文查找
                for i in range(max(count_idx - 10, 0), count_idx + 10, 1):
                    para_info_by_idx = self._para_info.get(i, None)
                    if para_info_by_idx:
                        # 根据段落索引找到pywin32解析的段落信息后，开始对比并获取页码
                        if cur_text_obj_text == para_info_by_idx.get("content"):
                            page_id = para_info_by_idx.get("page_id")
                            auto_number = para_info_by_idx.get("auto_number")
                            auto_header = para_info_by_idx.get("auto_header")
                            left_indent = para_info_by_idx.get("left_indent")
                            if para_info_by_idx.get('position'):
                                cur_text_obj.position = para_info_by_idx.get('position')
                            break
                else:
                    page_id = ""
                    auto_number = ""
                    auto_header = ""
                    left_indent = 0
        else:
            # 段落错位，微调进行上下文查找
            for i in range(max(count_idx - 10, 0), count_idx + 10, 1):
                para_info_by_idx = self._para_info.get(i, None)
                if para_info_by_idx:
                    # 根据段落索引找到pywin32解析的段落信息后，开始对比并获取页码
                    if cur_text_obj_text == para_info_by_idx.get("content"):
                        page_id = para_info_by_idx.get("page_id")
                        auto_number = para_info_by_idx.get("auto_number")
                        auto_header = para_info_by_idx.get("auto_header")
                        left_indent = para_info_by_idx.get("left_indent")
                        if para_info_by_idx.get('position'):
                            cur_text_obj.position = para_info_by_idx.get('position')
                        break
            else:
                page_id = ""
                auto_number = ""
                auto_header = ""
                left_indent = 0
        return [page_id, auto_number, auto_header, left_indent]

    def _parse_text(self, para: CT_P, label: str = "body") -> tuple:
        """
        解析docx CT_P对象，可以分别解析出文本、图片、图形
        并组织成通用结构的TextObject/PictureObject/GraphicObject对象
        :param para: docx CT_P类型对象，该段落对象中可能是文本、图片、图形
        :return: 返回元组(TextObject, PictureObject/GraphicObject列表)
        """
        # 文本对象列表
        text_obj_list = []
        text_obj = TextObject()
        text_obj.layout = WordLayoutObject()
        text_obj.label = label
        shape_list = []  # 存储一个段落中的若干图片、图形

        # 解析段落的节段
        for item_p in para:
            tag_name = item_p.tag.split("}")[-1]
            if tag_name in ['del', 'ins']:
                # del和ins层
                if tag_name == 'del':
                    # del为批注删除的内容，忽略
                    continue
                if tag_name == 'ins':
                    for item_ins in item_p:
                        # 解析当前容器中的所有的run节段
                        tag_name = item_ins.tag.split("}")[-1]
                        if tag_name == 'r':
                            # 解析Run对象
                            run_object = self._parse_run(item_ins, label=label)
                            if isinstance(run_object, TextObject):  # 文本框 直接返回一个TextObject
                                run_object.label = label
                                text_obj_list.append(run_object)
                            elif isinstance(run_object, RunObject) and run_object.text not in ["br", ""]:
                                # run 阶段内部是文本
                                text_obj.runs.append(run_object)
                                text_obj.text += run_object.text
                            elif isinstance(run_object, RunObject) and run_object.text == "br" \
                                    and run_object.type == "page":
                                # run 内部有br 分页
                                if text_obj.text:
                                    # 去除文本两边的空格等字符
                                    text_obj.text = text_obj.text.replace("　", " ").replace("\u3000", " ") \
                                        .replace("．", ".").strip(" ").strip("\t").strip("\r")
                                    text_obj_list.append(deepcopy(text_obj))
                                    text_obj = TextObject()
                                    text_obj.layout = WordLayoutObject()
                                    text_obj.label = label
                            else:
                                # 解析到图片 或者 图形
                                run_object.label = label
                                shape_list.append(run_object)

            elif tag_name == 'r':
                # 解析Run对象
                run_object = self._parse_run(item_p, label=label)
                if isinstance(run_object, TextObject):
                    run_object.label = label
                    text_obj_list.append(run_object)
                elif isinstance(run_object, RunObject) and run_object.text not in ["br", ""]:
                    # run 节段内部是文本
                    text_obj.runs.append(run_object)
                    text_obj.text += run_object.text
                elif isinstance(run_object, RunObject) and run_object.text == "br" and run_object.type == "page":
                    # run 内部有br 分页
                    if text_obj.text:
                        # 去除文本两边的空格等字符
                        text_obj._text = text_obj.text.replace("　", " ").replace("\u3000", " ").replace("．", ".") \
                            .strip(" ").strip("\t").strip("\r")
                        text_obj_list.append(deepcopy(text_obj))
                        text_obj = TextObject()
                        text_obj.layout = WordLayoutObject()
                        text_obj.label = label
                else:
                    # 解析到图片或者图形
                    run_object.label = label
                    shape_list.append(run_object)

            # 其它的tag，如w:moveTo 的解析
            elif tag_name == "moveTo":
                for w_r in item_p:
                    if isinstance(w_r, CT_R):
                        # 解析Run对象
                        run_object = self._parse_run(w_r, label=label)
                        if isinstance(run_object, RunObject) and run_object.text not in ["br", ""]:
                            # run 节段内部是文本
                            text_obj.runs.append(run_object)
                            text_obj.text += run_object.text
            elif tag_name == "hyperlink":
                return self._parse_text(item_p, label=label)

        # 解析出多个文本对象
        if text_obj_list:
            if text_obj.text:  # 追加最后一个文本对象
                text_obj_list.append(text_obj)
            for per_text_obj in text_obj_list:
                # 解析当前 文本段落 的父节点
                self._parse_parent_node(per_text_obj, label=label)

        # 解析出一个文本对象
        elif text_obj.text:
            # 去除文本两边的空格等字符
            text_obj.text = text_obj.text.replace("　", " ").replace("\u3000", " ").replace("．", ".") \
                .strip(" ").strip("\t").strip("\r")

            # 解析当前 文本节点的 父节点
            self._parse_parent_node(text_obj, label=label)
        else:
            # 空文本段落
            self.is_blank_para = True
        self.remove_use_less_run_obj(text_obj)

        # 返回结果
        if text_obj_list:  # 多个文本 和 图片、图形
            return [t for t in text_obj_list if t.text], shape_list
        elif text_obj.text:  # 段落中有一个文本对象、有图片（图形）
            return text_obj, shape_list
        else:
            return None, shape_list

    @staticmethod
    def remove_use_less_run_obj(text_obj):
        if text_obj.runs:
            if len(text_obj.runs) < 2:
                check_run_object_list = text_obj.runs
            else:
                first_run_obj = text_obj.runs[0]
                last_run_obj = text_obj.runs[-1]
                check_run_object_list = [first_run_obj, last_run_obj]
            for run_object in check_run_object_list:
                if run_object.text in ['　']:
                    text_obj.runs.remove(run_object)

    @staticmethod
    def parse_tbl_borders(tbl: CT_Tbl, table: TableObject) -> None:
        """ 解析表格的边框 """
        tbl_borders = tbl.tblPr.xpath(".//w:tblBorders")
        if tbl_borders:
            tbl_borders = tbl_borders[0]
            border_side = ["left", "top", "right", "bottom"]
            for bside in border_side:
                border_by_side = tbl_borders.find(qn(f"w:{bside}"))
                if border_by_side is not None:
                    b_val = border_by_side.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val")
                    border_obj = BorderObject()
                    border_obj.border_style = b_val
                    setattr(table.border, f"border_{bside}", border_obj)

    def parse_tbl_layout(self, table: TableObject) -> None:
        """ 解析表格的布局信息 """
        tgt_data = self.router.get(table.label, None)
        if tgt_data is None:
            return
        for idx, e in enumerate(reversed(tgt_data)):
            if idx == 0:
                table.layout.prev_ref = e
                e.layout.next_ref = table

            if isinstance(e, TextObject) and e.layout.is_chapter_title:
                table.layout.parent_content = e.text
                table.layout.parent_ref = e
                break

    @staticmethod
    def parse_tbl_style(tbl: Table, table: TableObject) -> None:
        """ 解析表格的样式 """
        # 样式 docx.styles.style import _TableStyle
        t_style = tbl.style
        tbl_font = t_style.font
        size = tbl_font.size
        name = tbl_font.name
        if name is not None:
            table.style.font_family = name
        if size is not None:
            table.style.font_size = str(size) + "pt"

    @cached_property
    def get_comments_content(self) -> dict:
        """ 获取word文档中的批注信息 """
        import zipfile
        from lxml import etree
        # Extract comments and their IDs
        comments = {}
        try:
            # 打开压缩文件，读取comments.xml
            with zipfile.ZipFile(self._file_path, 'r') as zf:
                # Read the comments XML file
                comments_xml = zf.read('word/comments.xml')
                # Parse the XML
                comments_tree = etree.fromstring(comments_xml)

                for comment in comments_tree.findall('.//w:comment', namespaces=comments_tree.nsmap):
                    comment_id = comment.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}id')
                    comment_text = ''.join(comment.itertext())
                    comments[comment_id] = comment_text
        except KeyError:
            pass
        return comments

    def parse_cell_comment(self, row_idx: int, col_idx: int, cell: _Cell) -> Union[CommentObject, None]:
        """ 解析表格单元格的批注信息 """
        comment_obj = CommentObject()
        comment_obj.coordinate.relative = {}  # 赋值cell对象 有序列化循环引用问题
        comment_obj.coordinate.desc = f"cell({row_idx + 1}, {col_idx + 1})"
        # 批注内容
        ct_tc = cell._element
        ct_tc_comment = ct_tc.xpath(".//w:commentReference")
        if ct_tc_comment:
            ct_tc_comment = ct_tc_comment[0]
            comment_id = ct_tc_comment.attrib.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}id")
            cur_comment_text = self.get_comments_content.get(comment_id)
            if cur_comment_text is not None:
                comment_obj.text = cur_comment_text
            return comment_obj
        else:
            # 没有批注
            return None

    @staticmethod
    def parse_cell_style(cell: _Cell, cell_obj: CellObject) -> None:
        """ 解析单元格的样式 """
        # 单元格背景色
        ct_tc = cell._element
        cell_bg_color = ct_tc.xpath(".//w:shd")
        if cell_bg_color:
            cell_bg_color = cell_bg_color[0]
            bg_color = cell_bg_color.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}fill")
            bg_style = cell_bg_color.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val")
            if bg_color is not None and re.match(r'#?[0-9A-Fa-f]{6}$', bg_color):
                cell_obj.style.background_color = "#" + bg_color if "#" not in bg_color else bg_color
            if not cell_obj.style.background_color and bg_color == 'auto':
                # 当 w:fill="auto" 时，Word 会尝试使用 w:color 的值作为背景颜色
                style_color = cell_bg_color.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}color")
                cell_obj.style.background_color = "#" + style_color if "#" not in style_color else style_color
            if bg_style is not None:
                # 单元格的底纹图案
                cell_obj.style._background_style = bg_style
        if cell_obj.style._background_style == '':
            # 取默认值
            cell_obj.style._background_style = 'clear'
        if cell_obj.style._background_color == '':
            cell_obj.style._background_color = '#auto'

    @staticmethod
    def parse_cell_borders(row_idx: int, col_idx: int, cell: _Cell, proxy_table: Table, cell_obj: CellObject) -> None:
        """ 基于python-docx解析单元格的边框
        :param row_idx: 单元格的行索引，从0开始
        :param col_idx: 单元格的列索引，从0开始
        :param cell: 单元格对象，即_Cell代理对象
        :param proxy_table: 单元格所在的代理表格对象，即Table对象
        :param cell_obj: 通用CellObject单元格对象
        """
        # 获取单元格的边框
        ct_tc = cell._element
        borders_list = ct_tc.xpath('.//w:tcBorders')
        if borders_list:
            borders = borders_list[0]
            for border_side in ['top', 'left', 'bottom', 'right']:
                # 逐一查找各个边框
                border = borders.find(qn(f'w:{border_side}'))
                # 边框存在，则存入cell_obj对象
                if border is not None:
                    b_val = border.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val")
                    if b_val is not None:
                        border_obj = BorderObject()
                        border_obj.border_style = b_val
                        border_obj.border_color = border.get(
                            "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}color")
                        setattr(cell_obj.border, f"border_{border_side}", border_obj)
                else:  # 当前单元格的边框为None，即没有显性设置，则看相邻的单元格的边框
                    adjacent_border = get_adjacent_cell_border(proxy_table, row_idx, col_idx, border_side)
                    if adjacent_border is not None:
                        border_obj = BorderObject()
                        b_val, b_color = adjacent_border.split(":")
                        border_obj.border_style = b_val
                        border_obj.border_color = b_color
                        setattr(cell_obj.border, f"border_{border_side}", border_obj)

    def _parse_cell(self, row_idx: int, col_idx: int, cell: _Cell, tbl: Table, label: str = "body") -> CellObject:
        """ 解析_Cell对象， 返回CellObject对象
        :param row_idx: 单元格的行索引，从0开始
        :param col_idx: 单元格的列索引，从0开始
        :param cell: _Cell代理对象
        :param tbl: 单元格所在的Table表格（代理对象）
        :param label: 表示页眉、页脚、body主体的标签
        :return: CellObject对象
        """
        # 单元格对象
        cell_obj = CellObject()
        cell_obj.row_index = row_idx
        cell_obj.col_index = col_idx
        cell_obj.box_graphics = []
        content_iter = peekable(cnt for cnt in cell.iter_inner_content() if isinstance(cnt, Paragraph))
        # for cnt in cell.iter_inner_content():
        for cnt in content_iter:
            if isinstance(cnt, Paragraph):
                # 从段落中可以解析为文本、图片、图形
                para_text, para_pic_list = self._parse_text(cnt._element, label=label)
                if isinstance(para_text, TextObject) and para_text.text:
                    # cell_obj.text += para_text.text + "\n"
                    cell_obj.text += para_text.text
                    cell_obj.content.append(para_text)
                    # 检查是否还有下一个元素（不消耗它）
                    try:
                        content_iter.peek()  # 尝试查看下一个元素（不移动指针）
                        cell_obj.text += "\n"
                        cell_obj.content.append(self.get_line_text_obj())  # 如果不是最后一个，就加换行
                    except StopIteration:  # 没有下一个元素了
                        pass  # 最后一个元素，不加换行
                elif isinstance(para_text, list):
                    pre_text, latter_text = para_text
                    cell_obj.text += pre_text.text + "\n" + latter_text.text
                    cell_obj.content.extend(para_text)
                # 段落中解析出图片、图形
                if para_pic_list:
                    for item in para_pic_list:
                        # # 处理悬浮在表格上的特殊文本框
                        # if hasattr(item,"shape_type") and item.shape_type == 17:
                        #     cell_obj.box_graphics.append(item)
                        # else:
                        cell_obj.content.append(item)
                        self.add_shape_to_block(item, self._block_obj, is_in_table=True)

            elif isinstance(cnt, Table):
                cell_obj.content.append(self._parse_table(cnt._element, self._origin_object, label=label))
        # 单元格文本内容去除两边\n
        cell_obj.text = cell_obj.text.strip("\n")

        # 解析单元格的批注
        comment = self.parse_cell_comment(row_idx, col_idx, cell)
        if comment is not None:
            cell_obj.comment = comment

        # 解析单元格的边框
        self.parse_cell_borders(row_idx, col_idx, cell, tbl, cell_obj)
        # 解析单元格的样式
        self.parse_cell_style(cell, cell_obj)
        # 合并一个单元格里的多个图形、图片
        # merge_cell_pics(cell_obj)
        return cell_obj

    @staticmethod
    def get_line_text_obj():
        line_text_obj = TextObject()
        line_run_obj = RunObject()
        line_run_obj.text = "\n"
        line_text_obj.runs.append(line_run_obj)
        line_text_obj.text = "\n"
        return line_text_obj

    @staticmethod
    def is_true_row(ct_tr: CT_R):
        """ 判定当前的xml表格行是否为真实的行数据（不存在w:del标签） """
        tr_pr = ct_tr.trPr
        if tr_pr is None:
            return True
        if tr_pr.xpath(".//w:del"):
            return False
        return True

    @staticmethod
    def parse_merge_region(table: Table, tbl_obj: TableObject, ct_tbl: CT_Tbl) -> None:
        """ 解析当前表格中单元格的合并区域范围 """
        total_rows = tbl_obj.rows
        if not total_rows:
            return
        row_num = len(total_rows)
        col_num = len(total_rows[0].cells)
        # 处理单元格的横向合并
        i = j = 0
        while i < row_num:  # 行索引
            while j < col_num:
                # 获取通用cell模型对象
                cell_obj = total_rows[i].cells[j]

                # 处理横向的合并
                # 0 0 [None, 0, None, 2]
                # 0 1 [None, 1, None, 3]
                # 0 2 [None, 2, None, 4]
                # 0 3 [None, None, None, None]
                if cell_obj.merged_ranges_2[1] is not None and cell_obj.merged_ranges_2[3] is not None:
                    # 当前单元格后面的若干单元格 的合并范围重新赋值
                    for jj in range(cell_obj.merged_ranges_2[1] + 1, cell_obj.merged_ranges_2[3] + 1):
                        try:
                            total_rows[i].cells[jj].merged_ranges_2[1] = cell_obj.merged_ranges_2[1]
                            total_rows[i].cells[jj].merged_ranges_2[3] = cell_obj.merged_ranges_2[3]
                        except IndexError:
                            pass
                    # 下一个起始单元格
                    j += cell_obj.merged_ranges_2[3] - cell_obj.merged_ranges_2[1] + 1
                else:
                    j += 1
            i += 1
            j = 0

        # 处理纵向的合并
        i = j = 0
        ct_tr_list = ct_tbl.tr_lst  # CT_Row列表
        # 去除ct_tr_list中的空行
        ct_tr_list = list(filter(WordParser.is_true_row, ct_tr_list))
        while i < row_num:  # 基于当前解析出的TableObject
            # 控制tc的列索引
            grid_span = 0
            grid_span_flag = False
            lxml_tc_idx = -1
            while j < col_num:
                # 通用CellObject
                cur_cell_obj = tbl_obj.rows[i].cells[j]
                merged_range2 = getattr(cur_cell_obj, "merged_ranges_2", [])
                # 有横向的合并
                if (merged_range2[3] is not None and merged_range2[1] is not None
                        and grid_span < 2 and not grid_span_flag):
                    grid_span = merged_range2[3] - merged_range2[1] + 1
                if grid_span < 2 and not grid_span_flag:  # 单元格没有横向的合并
                    lxml_tc_idx += 1

                # 单元格有横向的合并
                elif grid_span >= 1 and not grid_span_flag:
                    lxml_tc_idx += 1
                    grid_span_flag = True
                    grid_span -= 1
                elif grid_span >= 1 and grid_span_flag:
                    grid_span -= 1
                    if grid_span == 0:
                        grid_span_flag = False
                # 获取底层的xml tc对象
                try:
                    tr_obj = ct_tr_list[i]
                    # cell_xml = tr_obj.tc_lst[j]
                    cell_xml = tr_obj.tc_lst[lxml_tc_idx]
                except IndexError:
                    j += 1
                    continue
                # 按行合并
                vmerge = cell_xml.xpath('.//w:vMerge')
                # 通用cell对象
                cell_obj = total_rows[i].cells[j]

                # 存在行合并
                if vmerge and vmerge[0].val == "restart" \
                        and cell_obj.merged_ranges_2[0] is None and cell_obj.merged_ranges_2[2] is None:
                    cell_obj.merged_ranges_2[0] = i
                    new_i = i + 1
                    while new_i < row_num:
                        # 获取下一个行，当前列的一个单元格
                        try:
                            tr_obj = ct_tr_list[new_i]
                            cell_xml = tr_obj.tc_lst[lxml_tc_idx]
                        except IndexError:
                            break
                        # 列合并
                        vmerge = cell_xml.xpath('.//w:vMerge')
                        if vmerge and vmerge[0].val != "restart":
                            new_i += 1
                            continue
                        else:
                            cell_obj.merged_ranges_2[2] = new_i - 1
                            break
                    # 行索引超出时，
                    if cell_obj.merged_ranges_2[2] is None:
                        cell_obj.merged_ranges_2[2] = new_i - 1

                    # 更新（当前纵向合并范围的）其他cell对象纵向合并
                    for k in range(i + 1, new_i):
                        try:
                            total_rows[k].cells[j].merged_ranges_2[0] = cell_obj.merged_ranges_2[0]
                            total_rows[k].cells[j].merged_ranges_2[2] = cell_obj.merged_ranges_2[2]
                        except IndexError:
                            continue
                j += 1

            # 仅支持不同的列中 合并行数相同的情况
            i += 1  # 处理下一行
            j = 0  # 重置列索引

        # 处理为None的值
        for i in range(row_num):
            for j in range(col_num):
                cell_obj = total_rows[i].cells[j]
                # 跳过 没有横向合并的也没有纵向合并的
                if cell_obj.merged_ranges_2[0] is None and cell_obj.merged_ranges_2[1] is None \
                        and cell_obj.merged_ranges_2[2] is None and cell_obj.merged_ranges_2[3] is None:
                    continue
                elif cell_obj.merged_ranges_2[1] is None and cell_obj.merged_ranges_2[3] is None:
                    cell_obj.merged_ranges_2[1] = cell_obj.merged_ranges_2[3] = j

                elif cell_obj.merged_ranges_2[0] is None and cell_obj.merged_ranges_2[2] is None:
                    cell_obj.merged_ranges_2[0] = cell_obj.merged_ranges_2[2] = i

        # 处理 横向+纵向 的合并
        i = 0
        while i < row_num:
            j = 0
            while j < col_num:
                cell = total_rows[i].cells[j]
                # 复合合并区域
                if None not in cell.merged_ranges_2 and i == cell.merged_ranges_2[0] and j == cell.merged_ranges_2[1]:
                    # 计算行、列的跨度
                    row_dist = cell.merged_ranges_2[2] - cell.merged_ranges_2[0] + 1
                    col_dist = cell.merged_ranges_2[3] - cell.merged_ranges_2[1] + 1
                    if row_dist > 1 and col_dist > 1:
                        start_row, start_col, end_row, end_col = cell.merged_ranges_2
                        for ii in range(i, row_dist):
                            for jj in range(j, col_dist):
                                if ii == i and jj == j:
                                    continue
                                else:
                                    temp_cell = total_rows[ii].cells[jj]
                                    temp_cell.merged_ranges_2 = [start_row, start_col, end_row, end_col]

                        j += col_dist
                        continue
                j += 1
            i += 1

        # 为cell对象的合并范围重新赋值
        for i in range(row_num):
            for j in range(col_num):
                cell_obj = total_rows[i].cells[j]
                if any(getattr(cell_obj, "merged_ranges_2", [])):
                    cell_obj.merged_ranges = cell_obj.merged_ranges_2
                if hasattr(cell_obj, "merged_ranges_2"):
                    delattr(cell_obj, "merged_ranges_2")

        # 查看横向、纵向的合并
        # i = 0
        # while i < len(total_rows):
        #     j = 0
        #     while j < col_num:
        #         cell = total_rows[i].cells[j]
        #         print("查看結果:", i, j, cell.merged_ranges)
        #         j += 1
        #     i += 1

    @staticmethod
    def table_row_align(tbl_obj):
        """ 表格行、列对齐 """
        logging.info("start to deal table row align.")
        max_cell_num = -1
        for row in tbl_obj.rows:
            max_cell_num = max(max_cell_num, len(row.cells))

        for idx, row in enumerate(tbl_obj.rows):
            if len(row.cells) < max_cell_num:
                logging.info(f"do table row-{idx} align.")
                left_num = max_cell_num - len(row.cells)
                temp_cells = [CellObject()] * left_num
                for temp_cell in temp_cells:
                    temp_cell.merged_ranges_2 = [None, None, None, None]
                row.cells.extend(temp_cells)

    def _parse_table(self, tbl: CT_Tbl, parent: t.ProvidesStoryPart, tbl_idx: int = None,
                     label: str = "body") -> TableObject:
        """
        解析docx中的CT_Tbl表格对象，组织成通用结构的表格对象TableObject
        :param tbl: CT_Tbl对象
        :param parent: 表格对象的父容器
        :param tbl_idx: 表格在文档顺序中的索引（只计表格）
        :return: TableObject对象
        """
        # 表格页码信息
        if tbl_idx is not None:
            tbl_page_info = self._table_info.get(tbl_idx)
        else:
            tbl_page_info = None
        # 基于代理Table对象 解析数据
        proxy_table = Table(tbl, parent)
        # 通用表格对象
        tbl_obj = TableObject()
        tbl_obj.label = label  # 标识页眉、页脚、主体
        tbl_obj.box_graphics = []
        if tbl_page_info and tbl_page_info.get('position'):
            tbl_obj.position = tbl_page_info.get('position')
        # 真正的行数索引
        true_row_idx = -1
        # 解析表格数据
        for r_idx, row in enumerate(proxy_table.rows):
            # 当前行列表
            cur_row_cnt = []
            r_cells = list(row.cells)
            for c_idx, cell in enumerate(r_cells):
                # 解析单元格
                cell_obj = self._parse_cell(r_idx, c_idx, cell, proxy_table, label=label)
                # 当前单元格临时存入行列表
                # if cell_obj.content:
                cur_row_cnt.append(cell_obj)
                # 表格之上的文本框
                if hasattr(cell_obj, "box_graphics") and cell_obj.box_graphics:
                    if (c_idx > 1 and cell.grid_span > 1 and r_cells[c_idx - 1].grid_span > 1
                            and cur_row_cnt and hasattr(cur_row_cnt[-2], "box_graphics") and cur_row_cnt[
                                -2].box_graphics
                            and cell_obj.box_graphics[0].data == cur_row_cnt[-2].box_graphics[0].data):
                        cell_obj.box_graphics = []
                    tbl_obj.box_graphics.extend(cell_obj.box_graphics)
                if tbl_page_info:
                    index = r_idx * tbl_page_info.get("cols") + c_idx
                    if len(tbl_page_info.get('cell_positions')) > index and tbl_page_info.get('cell_positions')[index]:
                        cell_obj.position = tbl_page_info.get('cell_positions')[index]
                # 初始化单元格的合并范围
                cell_obj.merged_ranges_2 = [None, None, None, None]
                # 处理单元格的横向合并
                col_span = cell.grid_span
                if col_span > 1:  # 有列合并
                    cell_obj.merged_ranges_2[1] = c_idx
                    cell_obj.merged_ranges_2[3] = c_idx + col_span - 1

            # 创建行对象
            row_obj = RowObject()
            # cur_row_cnt_bool = [True if c_obj.content else False for c_obj in cur_row_cnt]
            # if any(cur_row_cnt_bool):
            true_row_idx += 1
            row_obj.row_index = true_row_idx
            row_obj.label = label
            # 获取行对象的页码
            if tbl_page_info is not None:
                tbl_columns_with_pywin32 = tbl_page_info.get("cols")
                tgt_index = true_row_idx * tbl_columns_with_pywin32
                try:
                    for i in range(tgt_index, tgt_index + tbl_columns_with_pywin32):
                        cur_row_page_id = str(tbl_page_info.get("cell_page_num")[i])
                        if cur_row_page_id != 'None':
                            break
                    # cur_autostring_id = str(tbl_page_info.get("cell_String_num")[tgt_index])
                except IndexError as e:
                    # logging.error(f"parse table_{tbl_idx} cell({r_idx}, {c_idx}) page num failed.")
                    cur_row_page_id = ""
                row_obj.layout.page_id = cur_row_page_id
            # row_obj.layout.autostring_id = cur_autostring_id
            row_obj.cells.extend(cur_row_cnt)
            for c_obj in cur_row_cnt:
                c_obj.layout.parent_ref = row_obj
                # 单元格的页码取当前行的页码
                c_obj.layout.page_id = row_obj.layout.page_id
                c_obj.row_index = row_obj.row_index
            if tbl_page_info is not None:
                cur_autostring_id = tbl_page_info.get("cell_String_num")
                # 单元格对象追加到行
                for c_obj in cur_row_cnt:
                    if cur_autostring_id and any(x is not None for x in cur_autostring_id):
                        cell_tbl_columns_with_pywin32 = tbl_page_info.get("cols")
                        # 检查 row_obj.row_index, cell_tbl_columns_with_pywin32 和 c_obj.col_index 是否为空
                        # if not row_obj.row_index or not cell_tbl_columns_with_pywin32 or not c_obj.col_index:
                        #     continue
                        cell_tgt_index = int(row_obj.row_index) * int(cell_tbl_columns_with_pywin32) + int(
                            c_obj.col_index)
                        if len(cur_autostring_id) <= cell_tgt_index:
                            continue
                        if cur_autostring_id[cell_tgt_index] and not c_obj.text:
                            c_obj.text = str(cur_autostring_id[cell_tgt_index])
                            text_obj = TextObject()
                            text_obj.text = str(cur_autostring_id[cell_tgt_index])
                            run_obj = RunObject()
                            run_obj.text = str(cur_autostring_id[cell_tgt_index])
                            text_obj.runs.append(run_obj)
                            c_obj.content.append(text_obj)
                            c_obj.auto_number = True

            # 过滤掉空行对象
            # if self.is_empty_row(row_obj):
            #     continue
            # 行数据追加到表格
            row_obj.layout.parent_ref = tbl_obj
            tbl_obj.rows.append(row_obj)

        # 表格行对齐
        self.table_row_align(tbl_obj)
        # 解析单元格的合并区域
        self.parse_merge_region(proxy_table, tbl_obj, tbl)
        # 解析表格的边框
        self.parse_tbl_borders(tbl, tbl_obj)
        # 解析表格的layout
        self.parse_tbl_layout(tbl_obj)
        # 解析表格的样式 style
        self.parse_tbl_style(proxy_table, tbl_obj)
        return tbl_obj

    def parse_txbx_paragraph(self, text_obj: TextObject, txt_box: _Element) -> None:
        """ 将文本框中的所有段落文本解析为一个段落 """
        for tb_para in txt_box:
            txt_obj, _ = self._parse_text(tb_para)
            text_obj.text = text_obj.text + "\n" + txt_obj.text
            text_obj.runs.extend(txt_obj.runs)
        text_obj.text = text_obj.text.strip("\n")

    def parse_graphic_parent(self, graphic_obj: GraphicObject):
        """ 解析图形的父章节 """
        tgt_data = self.router.get(graphic_obj.label, None)
        if tgt_data is None:
            return

        for idx, obj in enumerate(reversed(tgt_data)):
            if idx == 0:
                graphic_obj.layout.prev_ref = obj
                obj.layout.next_ref = graphic_obj

            if isinstance(obj, TextObject) and obj.layout.is_chapter_title:
                graphic_obj.layout.parent_ref = obj
                graphic_obj.layout.parent_content = obj.text
                break

    def _parse_pict(self, w_pict, label: str = "body"):
        """ parse shape with tag w_pict """
        # instantiate
        graphic_obj = GraphicObject()
        graphic_obj.label = label
        # inner shape
        shape = w_pict[0]  # 内部图形
        if len(w_pict) > 1:
            flag = False
            for pict_shape in w_pict:
                for shape_e in pict_shape:
                    shape_e_tag_name = shape_e.tag.split("}")[-1]
                    if shape_e_tag_name == "textbox":
                        shape = pict_shape
                        flag = True
                        break
                if flag:
                    break
        # 填充色
        # fill_color = shape.get("fillcolor").split()[0] if shape.get("fillcolor") else None
        # if fill_color is not None and re.match(r'^#[0-9A-Fa-f]{6}$', fill_color):
        #     graphic_obj.style.background_color = str(fill_color)
        # elif fill_color is not None:
        #     try:
        #         fill_color = webcolors.name_to_hex(fill_color)
        #         graphic_obj.style.background_color = str(fill_color)
        #     except:
        #         logging.error(f"webcolors name to hex error-{fill_color}")

        css_string = shape.get('style', '')
        # 使用正则表达式提取 height 和 width 的值
        height_match = re.search(r'height:([\d.]+)pt', css_string)
        width_match = re.search(r'width:([\d.]+)pt', css_string)
        # 提取值并转换为str
        graphic_obj.px_height = str(round(float(height_match.group(1)) * (DPI / 72))) if height_match else ''
        graphic_obj.px_width = str(round(float(width_match.group(1)) * (DPI / 72))) if width_match else ''
        if not height_match or not width_match:
            graphic_obj.height_width_ratio = ''
        else:
            graphic_obj.height_width_ratio = float(height_match.group(1)) / float(width_match.group(1)) if float(
                width_match.group(1)) else ''
        graphic_obj.height = str(round(float(height_match.group(1)) * 12700)) if height_match else ''
        graphic_obj.width = str(round(float(width_match.group(1)) * 12700)) if width_match else ''

        # graphic_obj.data = shape.get("{urn:schemas-microsoft-com:office:office}gfxdata")
        graphic_obj.id = shape.get("id", "")
        logging.info(f"parsed current graphic id {graphic_obj.id}.")
        # 边框颜色及粗细
        # graphic_obj.stroke_color = shape.get("strokecolor") if shape.get("strokecolor") else None
        # graphic_obj.stroke_weight = shape.get("strokeweight") if shape.get("strokeweight") else None
        # 解析图形内部文本
        logging.info("start to parse textframe inside current graphic.")
        for shape_e in shape:
            shape_e_tag_name = shape_e.tag.split("}")[-1]
            if shape_e_tag_name == "stroke":
                pass
            elif shape_e_tag_name == "textbox":
                if len(shape_e) > 0:
                    self._parse_graphic_text(graphic_obj, label, shape_e)
                    # for para in shape_e[0]:
                    #     for para_e in para:
                    #         para_e_tag = para_e.tag.split("}")[-1]
                    #         if para_e_tag == "r":
                    #             graphic_obj.text += para_e.text
                    #         elif para_e_tag == "ins":
                    #             for item in para_e:
                    #                 item_tag = item.tag.split("}")[-1]
                    #                 if item_tag == "r":
                    #                     graphic_obj.text += item.text
        if graphic_obj.text == '車両適合定数一覧を参照':
            print()
        similars = []
        graphic_info = {}
        if label == 'header':
            _graphic_info = self._header_graphic
        elif label == 'footer':
            _graphic_info = self._footer_graphic
        else:
            _graphic_info = self._graphic_info
        if graphic_obj.text:
            for name, graphic in _graphic_info.items():
                if isinstance(graphic, list):
                    continue
                if graphic_obj.text in graphic['text'].replace('\n', '').replace('\r', ''):
                    similars.append(graphic)
            if len(similars) == 1:
                graphic_info = similars[0]
            elif len(similars) > 1:
                graphic_info = self.find_closest_height_width_ratio(graphic_obj, similars)
        else:
            graphic_info = self.find_closest_height_width_ratio(graphic_obj, _graphic_info.values(),
                                                                mini_distance=0.97)
        self.get_data_from_graphic_info(graphic_info, graphic_obj)

        return graphic_obj

    def get_data_from_graphic_info(self, graphic_info, graphic_obj):
        if graphic_info:
            bytes_data = graphic_info.get("data")
            if bytes_data is not None:
                graphic_obj.data = b64encode(bytes_data).decode()
            graphic_obj.px_width = graphic_info.get("px_width")
            graphic_obj.px_height = graphic_info.get("px_height")
            graphic_obj.layout.page_id = graphic_info.get("page_id")
            # 处理悬浮在表格上面的特殊文本框
            graphic_obj.shape_type = graphic_info.get("shape_type")
            if graphic_info.get("position"):
                graphic_obj.position = graphic_info.get("position")

    def find_closest_height_width_ratio(self, graphic_obj, similars, mini_distance=0.7):
        graphic_info1 = {}
        if not graphic_obj.height_width_ratio:
            return graphic_info1
        for graphic in similars:
            if isinstance(graphic, list):
                continue
            if graphic.get("px_height") and graphic.get("px_width") and float(graphic.get("px_width")):
                ratio = float(graphic.get("px_height")) / float(graphic.get("px_width"))
                if ratio > graphic_obj.height_width_ratio:
                    simi_close = graphic_obj.height_width_ratio / ratio
                else:
                    simi_close = ratio / graphic_obj.height_width_ratio
                if simi_close > mini_distance:
                    graphic_info1 = graphic
                    mini_distance = simi_close
        return graphic_info1

    def _parse_graphic(self, ele: _Element, label: str = "body") -> Union[GraphicObject, TextObject]:
        """
        解析docx中_Element对象中的图形，组织成通用结构的对象结构
        :param ele: _Element对象
        :return: GraphicObject图形对象
        """
        graphic_obj = GraphicObject()
        graphic_obj.label = label
        try:
            # 解析图形属性
            choice = ele[0]
            anchor = choice[0][0]
            # 查询图形的名称
            # wp_doc_pr = anchor.xpath(".//wp:docPr")
            # if wp_doc_pr:
            #     wp_doc_pr = wp_doc_pr[0]
            #     if "文本框" in wp_doc_pr.name:
            #         txt_box = anchor.xpath(".//w:txbxContent")
            #         if txt_box:
            #             txt_box = txt_box[0]
            #             # 处理文本框内的段落
            #             text_obj = TextObject()
            #             text_obj.layout = WordLayoutObject()
            #             text_obj.is_text_frame = True
            #             self.parse_txbx_paragraph(text_obj, txt_box)
            #             return text_obj

            logging.info("start to parse graphic attr.")
            # 遍历anchor内的元素
            for e in list(anchor):
                tag_name = e.tag.split("}")[-1]
                if tag_name == "extent":
                    graphic_obj.width = str(e.cx)
                    graphic_obj.height = str(e.cy)
                elif tag_name == "effectExtent":
                    graphic_obj.coordinate.left = e.get("l", None)
                    graphic_obj.coordinate.top = e.get("t", None)
                    graphic_obj.coordinate.right = e.get("r", None)
                    graphic_obj.coordinate.bottom = e.get("b", None)
                elif tag_name == "docPr":
                    graphic_obj.name = e.name
                elif tag_name == "graphic":
                    wsp = e.graphicData[0]
                    for wsp_e in wsp:
                        tag_name = wsp_e.tag.split("}")[-1]
                        if tag_name == "spPr":
                            graphic_type = wsp_e[1].get("prst")
                            if graphic_type:
                                graphic_obj.graphic_type = graphic_type
            # 解析图形数据
            fallback = ele[1]
            pict = fallback[0]
            shape = pict[0]  # 内部图形
            if len(pict) > 1:
                flag = False
                for pict_shape in pict:
                    for shape_e in pict_shape:
                        shape_e_tag_name = shape_e.tag.split("}")[-1]
                        if shape_e_tag_name == "textbox":
                            shape = pict_shape
                            flag = True
                            break
                    if flag:
                        break

            # 填充色
            fill_color = shape.get("fillcolor").split()[0] if shape.get("fillcolor") else None
            if fill_color is not None and re.match(r'^#[0-9A-Fa-f]{6}$', fill_color):
                graphic_obj.style.background_color = str(fill_color)
            elif fill_color is not None:
                try:
                    fill_color = webcolors.name_to_hex(fill_color)
                    graphic_obj.style.background_color = str(fill_color)
                except:
                    logging.error(f"webcolors name to hex error-{fill_color}")

            # graphic_obj.data = shape.get("{urn:schemas-microsoft-com:office:office}gfxdata")
            graphic_obj.id = shape.get("id")
            logging.info(f"parsed current graphic id {graphic_obj.id}.")
            # 边框颜色及粗细
            graphic_obj.stroke_color = shape.get("strokecolor") if shape.get("strokecolor") else None
            graphic_obj.stroke_weight = shape.get("strokeweight") if shape.get("strokeweight") else None
            # 解析图形内部文本
            logging.info("start to parse textframe inside current graphic.")
            for shape_e in shape:
                shape_e_tag_name = shape_e.tag.split("}")[-1]
                if shape_e_tag_name == "stroke":
                    pass
                elif shape_e_tag_name == "textbox":
                    if len(shape_e) > 0:
                        self._parse_graphic_text(graphic_obj, label, shape_e)
            if graphic_obj.text == '車両適合定数一覧を参照':
                print()
            # 解析body中图形的父章节
            if label == "body":
                logging.info("start to parse parent chapter of current graphic.")
                self.parse_graphic_parent(graphic_obj)
                # 解析图形的页码
                logging.info("start to parse page id and data of current graphic.")
                # self._graphic_index += 1
                try:
                    # 根据python-docx图形的名称 转换pywin32中对应的图形名称，并从pywin32解析的图形中获取数据
                    gname, gidx = graphic_obj.name.rsplit(" ", maxsplit=1)
                    final_gname = self._mapping_gname.get(gname, gname) + " " + gidx
                    PICTURE_READ_COUNTER[final_gname] += 1
                    graphic_info = self._graphic_info.get(F'{final_gname}_{PICTURE_READ_COUNTER[final_gname]}', {})
                    # 处理一个picture对应多个解析数据
                    while not graphic_info and PICTURE_READ_COUNTER[final_gname] > 0:
                        PICTURE_READ_COUNTER[final_gname] -= 1
                        graphic_info = self._graphic_info.get(F'{final_gname}_{PICTURE_READ_COUNTER[final_gname]}', {})
                    # 处理多个picture对应1个解析数据
                    if self._graphic_info.get(final_gname):
                        graphic_obj.height_width_ratio = float(graphic_obj.height) / float(graphic_obj.width) if float(
                            graphic_obj.width) else ''
                        graphic_info_closest = self.find_closest_height_width_ratio(graphic_obj,
                                                                                    self._graphic_info.get(final_gname))
                        if graphic_info_closest:
                            graphic_info = graphic_info_closest
                    self.get_data_from_graphic_info(graphic_info, graphic_obj)
                except AttributeError:
                    logging.error("parse graphic page id and data failed.")
                except ValueError:
                    logging.error(f"split graphic name-{graphic_obj.name} failed.")
        except IndexError as e:
            logging.error("parse graphic data failed.")
        return graphic_obj

    def _parse_graphic_text(self, graphic_obj: GraphicObject, label: str, shape_e):
        text_obj_tmp = TextObject()
        # 遍历w:wxbxContent内的段落
        para_text = ""
        for para in shape_e[0]:
            for para_e in para:
                if para_e.tag.endswith("r"):
                    for item in para_e:
                        if item.text:
                            graphic_obj.text += item.text
                    run_object = self._parse_run(para_e, label=label)
                    if isinstance(run_object, RunObject) and run_object.text:
                        # run 节段内部是文本
                        text_obj_tmp.runs.append(run_object)
                        run_object.text = run_object.text.replace('\n', '')
                        text_obj_tmp.text += run_object.text
                elif para_e.tag.endswith("ins"):
                    for item in para_e:
                        if item.text:
                            graphic_obj.text += item.text
                            text_obj_tmp.text += item.text.replace('\n', '')
                # else:
                #     graphic_obj.text += para.text
            if para.text:
                para_text += para.text
        if not graphic_obj.text:
            graphic_obj.text = para_text
        if text_obj_tmp.text:
            graphic_obj.text_obj = text_obj_tmp

    def get_picture_data(self, rid: str, label="header") -> str:
        """ 获取图片的数据，返回base64编码的字符串 """
        from base64 import b64encode
        node = getattr(self._origin_object.sections[0], label, None)
        if node:
            try:
                image = node.part.rels.get(rid).target_part.image.blob
                data = b64encode(image).decode()
            except AttributeError:
                data = None
            except UnrecognizedImageError:
                data = None
        else:
            data = None
        return data

    def get_graphic_data(self, shape_name: str, label: str):
        """ 根据图形名称（id）获取页眉、页脚中的图形数据 """
        logging.info(f"start to get graphic data from {label}.")
        node_data = getattr(self, f"_{label}_graphic", None)
        data = None
        if node_data:
            try:
                gname, gidx = shape_name.rsplit(" ", maxsplit=1)
                final_gname = self._mapping_gname.get(gname, gname) + " " + gidx
            except ValueError:
                final_gname = shape_name
            shape_data = node_data.get(final_gname, None)
            if isinstance(shape_data, dict) and 'data' in shape_data:
                data = shape_data['data']
        return data

    def parse_visio_object(self, block_obj: DocumentBlockObject):
        if not self.visio_data:
            return
        for i in range(len(self.visio_data)):
            visio = self.visio_data[i]
            graphic_obj = GraphicObject()
            graphic_obj.id = str(float(visio[1] / visio[2]))
            graphic_obj.data = b64encode(visio[0]).decode()
            graphic_obj.width = str(visio[1])
            graphic_obj.height = str(visio[2])
            graphic_obj.layout.page_id = str(visio[3])
            graphic_obj.graphic_type = 'visio'
            if visio[4]:
                graphic_obj.position = visio[4]
            block_obj.graphics.append(graphic_obj)

    def parse_header_footer(self, block_obj: DocumentBlockObject) -> None:
        """ parse word header and footer """
        header = self._origin_object.sections[0].header
        footer = self._origin_object.sections[0].footer
        hf = ["header", "footer"]
        for label in hf:
            cur_list = getattr(block_obj, label, None)
            if cur_list is None:
                continue
            obj = eval(label)
            # 解析页眉、页脚
            temp_list = []
            for i in obj._element:
                type_name = i.__class__.__name__
                if type_name == 'CT_P':
                    # 解析段落文本
                    cur_text_obj, shape_list = self._parse_text(i, label=label)
                    if isinstance(cur_text_obj, list):
                        temp_list.extend(cur_text_obj)
                    elif isinstance(cur_text_obj, TextObject) and cur_text_obj.text:
                        temp_list.append(cur_text_obj)

                    # 判断是否解析出图片、图形
                    if shape_list:
                        for cur_shape in shape_list:
                            if isinstance(cur_shape, PictureObject):
                                # 图片
                                shape_rid = cur_shape.id
                                cur_shape.data = self.get_picture_data(shape_rid, label=label)
                            elif isinstance(cur_shape, GraphicObject):
                                # 图形
                                shape_name = cur_shape.name
                                if not cur_shape.data:
                                    shape_data = self.get_graphic_data(shape_name, label=label)
                                    if shape_data is not None:
                                        cur_shape.data = b64encode(shape_data).decode()

                            # if getattr(cur_shape, "data", ""):  # 存储非空图片
                            temp_list.append(cur_shape)

                elif type_name == 'CT_Tbl':
                    tbl_obj = self._parse_table(i, self._origin_object, label=label)
                    temp_list.append(tbl_obj)

            # 解析每页的页眉、页脚
            if temp_list:
                for pid in range(self.first_page_id, self.first_page_id + self._total_pages):
                    page_num = pid
                    # 设置页码，并追加到页眉、页脚的列表中
                    for obj in temp_list:
                        obj.layout.page_id = str(page_num)
                        if isinstance(obj, TableObject):
                            for row in obj.rows:
                                row.layout.page_id = str(page_num)
                                for cell in row.cells:
                                    cell.layout.page_id = str(page_num)
                                    cell.content = list(filter(lambda e: not isinstance(e, RunObject), cell.content))
                                    for cnt_obj in cell.content:
                                        if isinstance(cnt_obj, TextObject) and re.findall(r"\d+/\d+", self.full_to_half(
                                                cnt_obj.text)):
                                            cnt_obj.text = ""
                                            cnt_obj.runs = []
                                    if re.findall(r"\d+/\d+", self.full_to_half(cell.text)):
                                        cell.text = ""
                    cur_list.append(deepcopy(temp_list))

    @staticmethod
    def clean_gen_cache():
        """ 清除缓存的接口代码 """
        try:
            gen_cache_path = GetGeneratePath()
            shutil.rmtree(gen_cache_path)
        except (OSError, PermissionError):
            logging.error("clean gen cache failed")

    def _parse_document_content(self, file_path: str):
        """ parse document """
        # 清理office进程
        clean_tgt_process()
        self.clean_gen_cache()
        # 读取文档
        self._read_document(file_path)
        self._extract_metadata(self._origin_object)
        # 组织通用结构BlockObject对象列表
        self._block_obj = block_obj = DocumentBlockObject()
        block_obj.file_name = os.path.basename(file_path)
        self.router = {
            "body": self._elements_by_order,
            "header": self._block_obj.header,
            "footer": self._block_obj.footer
        }
        # 统计段落的索引（忽略表格、图片部分）
        count_idx = 0
        # 统计表格的索引
        tbl_idx = -1
        # text_data = {}
        try:
            for idx, item in enumerate(self._origin_object.element.body):
                # 解析段落
                if isinstance(item, CT_P):
                    self._picture_index_switch = True
                    # 解析段落
                    cur_text_obj, shape_list = self._parse_text(item)
                    # 从段落中解析出 文本对象列表
                    if isinstance(cur_text_obj, list):
                        for per_text_obj in cur_text_obj:
                            if not hasattr(per_text_obj, "is_text_frame"):
                                count_idx += 1
                                # text_data[count_idx] = {"content": per_text_obj.text, "page_id": ""}
                                # 获取当前文本对象的页码
                                page_id, auto_number, auto_header, left_indent = self._get_page_id(count_idx,
                                                                                                   per_text_obj)
                                per_text_obj.layout.page_id = str(page_id)
                                if auto_number:
                                    per_text_obj.text = auto_number + " " + per_text_obj.text
                                elif auto_header:
                                    per_text_obj.text = auto_header + " " + per_text_obj.text
                                # 自动编号匹配章节号
                                if (auto_number or auto_header) and left_indent > 5:
                                    chapter_id = self._parse_chapter_id(per_text_obj.text)
                                    if chapter_id:
                                        per_text_obj.layout.is_chapter_title = 2
                                        per_text_obj.layout.chapter_id = chapter_id
                                # 文本对象添加索引
                                per_text_obj.index = count_idx - 1
                            else:
                                # 处理文本框的页码
                                self._text_frame_index += 1
                                if self._text_frame_index < len(self._text_frame):
                                    per_text_obj.layout.page_id = str(
                                        self._text_frame[self._text_frame_index].get("page_id"))
                                else:
                                    per_text_obj.layout.page_id = ""
                            # 文本对象 加入顺序队列
                            self._elements_by_order.append(per_text_obj)
                            # 文本对象加入 块对象
                            block_obj.add_text(per_text_obj)
                    # 从段落中解析出非空文本对象
                    elif isinstance(cur_text_obj, TextObject) and cur_text_obj.text:
                        cur_text_obj.text = cur_text_obj.text.strip(" ").strip("\u3000")
                        if not hasattr(cur_text_obj, "is_text_frame"):
                            count_idx += 1
                            # text_data[count_idx] = {"content": per_text_obj.text, "page_id": ""}
                            # 获取当前文本对象的页码
                            page_id, auto_number, auto_header, left_indent = self._get_page_id(count_idx, cur_text_obj)
                            cur_text_obj.layout.page_id = str(page_id)
                            if auto_number:
                                cur_text_obj.text = auto_number + " " + cur_text_obj.text
                            elif auto_header:
                                cur_text_obj.text = auto_header + " " + cur_text_obj.text
                            # 自动编号匹配章节号
                            if (auto_number or auto_header) and left_indent > 5:
                                chapter_id = self._parse_chapter_id(cur_text_obj.text)
                                if chapter_id:
                                    cur_text_obj.layout.is_chapter_title = 2
                                    cur_text_obj.layout.chapter_id = chapter_id
                            # 文本对象添加索引
                            cur_text_obj.index = count_idx - 1
                        else:
                            # 处理文本框的页码（文本框作为文本对象）
                            self._text_frame_index += 1
                            if self._text_frame_index < len(self._text_frame):
                                cur_text_obj.layout.page_id = str(
                                    self._text_frame[self._text_frame_index].get("page_id"))
                            else:
                                cur_text_obj.layout.page_id = ""

                        # text_data[count_idx] = {"content": cur_text_obj.text, "page_id": ""}
                        # 文本对象 加入顺序队列
                        self._elements_by_order.append(cur_text_obj)
                        # 文本对象加入 块对象
                        block_obj.add_text(cur_text_obj)

                    # 判断是否解析出图片、图形
                    if shape_list:
                        for cur_shape in shape_list:
                            self.add_shape_to_block(cur_shape, block_obj)
                            if isinstance(cur_shape, (PictureObject, GraphicObject)):
                                self._elements_by_order.append(cur_shape)

                # 解析表格
                elif isinstance(item, CT_Tbl):
                    self._picture_index_switch = False
                    tbl_idx += 1
                    cur_table_object = self._parse_table(item, self._origin_object, tbl_idx)
                    cur_table_object.index = tbl_idx
                    # 判断是否为空表格
                    if self.is_empty_table(cur_table_object):
                        tbl_idx -= 1
                        continue
                    block_obj.add_table(cur_table_object)
                    # cell.content.graphic
                    if hasattr(cur_table_object, "box_graphics"):
                        block_obj.add_graphic(cur_table_object.box_graphics)
                    self._elements_by_order.append(cur_table_object)
                else:
                    pass
        except Exception as e:
            logging.error(traceback.format_exc())
            logging.error(f"Error parsing shape: {e}")

        # # 处理表格的表头
        # with ThreadPoolExecutor() as executor:
        #     futures = [executor.submit(parse_table_head, t) for t in block_obj.tables]
        #     concurrent.futures.wait(futures)
        # 解析表格的页码
        for tbl_idx, table_obj in enumerate(block_obj.tables):
            if isinstance(table_obj, TableObject):
                tbl_info = self._table_info.get(tbl_idx)
                if tbl_info is not None:
                    table_obj.layout.page_id = tbl_info.get("start_page_num")
        # 解析visio对象
        self.parse_visio_object(block_obj)

        # 解析页眉、页脚
        self.parse_header_footer(block_obj)
        self.document._document = [block_obj]

    @staticmethod
    def add_shape_to_block(shape: Union[PictureObject, GraphicObject], block: DocumentBlockObject,
                           is_in_table=False) -> None:
        """ 将图片、图形对象添加到 DocumentBlockObject 对象中 """
        if isinstance(shape, PictureObject):
            # 图片
            block.add_picture(shape, is_in_table=is_in_table)

        elif isinstance(shape, GraphicObject):
            # 图形
            block.add_graphic(shape, is_in_table=is_in_table)

    @staticmethod
    def is_empty_row(row_obj: RowObject) -> bool:
        """ 判断是否为空行 """
        row_cnt = []
        for cell in row_obj.cells:
            if cell.text or cell.content:
                row_cnt.append(True)
            else:
                row_cnt.append(False)
        if any(row_cnt):
            return False
        return True
