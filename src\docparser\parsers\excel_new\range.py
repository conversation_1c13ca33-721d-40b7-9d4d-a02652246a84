# -*- coding: utf-8 -*-
"""
@File    : range.py.py
<AUTHOR> zhenp
@Date    : 2025-06-12 11:17
@Desc    : Description of the file
"""
from typing import Dict, Tuple

from docparser import CellObject


def get_start_end_of_table(sheet, cells: Dict[Tuple[int, int], CellObject]):
    """
    自动计算sheet中所有的表格的范围和表格外的内容的范围
    :param sheet:
    :param config_ranges:
    :return: (表格的范围, 表格外的内容的范围)
    """
    free_cells = get_sheet_free_cells(sheet, cells)
    tables = get_sheet_ranges(sheet, free_cells)
    tables = expand_tables(sheet, tables)
    return tables, free_cells


def get_sheet_ranges(sheet, free_cells):
    # 获取表格的最大行数和最大列数
    sheet_max_row = sheet.max_row
    sheet_max_column = sheet.max_column

    # 将 free_cells 转换为集合，便于快速查找
    free_cells_set = set(free_cells)
    for col in range(1, sheet_max_column + 1):
        free_cells_set.add((sheet_max_row + 1, col))
    for row in range(1, sheet_max_column + 1):
        free_cells_set.add((row, sheet_max_column + 1))
    # 初始化访问标记
    visited = set()

    def dfs(row, col):
        stack = [(row, col)]
        min_row, max_row = row, row
        min_col, max_col = col, col

        while stack:
            r, c = stack.pop()
            if (r, c) not in visited:
                visited.add((r, c))
                min_row = min(min_row, r)
                max_row = max(max_row, r)
                min_col = min(min_col, c)
                max_col = max(max_col, c)

                # 检查相邻的单元格
                for dr, dc in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nr, nc = r + dr, c + dc
                    if 1 <= nr <= sheet_max_row and 1 <= nc <= sheet_max_column:
                        if (nr, nc) not in free_cells_set and (nr, nc) not in visited:
                            stack.append((nr, nc))

        return (min_row, min_col), (max_row, max_col)

    ranges = []

    # 遍历所有可能的单元格
    for row in range(1, sheet_max_row + 1):
        for col in range(1, sheet_max_column + 1):
            if (row, col) not in free_cells_set and (row, col) not in visited:
                range_ = dfs(row, col)
                ranges.append(range_)

    return ranges


def get_sheet_free_cells(sheet, cells: Dict[Tuple[int, int], CellObject]):
    """ get sheet free cells """
    max_row = sheet.max_row
    max_column = sheet.max_column
    free_cells = set()
    left_top_cell = cells[(1, 1)]
    if left_top_cell.border.border_left.border_style is None or left_top_cell.border.border_top.border_style is None:
        free_cells = free_cells | set(get_no_border_cells(left_top_cell, sheet, cells, search_depth=-1))
    right_bottom_cell = cells[(max_row, max_column)]
    if (max_row, max_column) not in free_cells and (
            right_bottom_cell.border.border_right.border_style is None or right_bottom_cell.border.border_bottom.border_style is None):
        free_cells = free_cells | set(get_no_border_cells(right_bottom_cell, sheet, cells, search_depth=-1))
    return free_cells


def get_range_cross_ranges(range_start_, range_end_, range_list_):
    # 将输入的 range_start_ 和 range_end_ 转换为 (min_row, min_col, max_row, max_col) 形式
    min_row, min_col = range_start_
    max_row, max_col = range_end_

    # 遍历 range_list_ 中的每一个 range
    for r in range_list_:
        # 将 range_list_ 中的每一个 range 转换为 (min_row, min_col, max_row, max_col) 形式
        (r_min_row, r_min_col), (r_max_row, r_max_col) = r

        # 判断输入的 range 是否与当前 range 有重合
        if not (max_row < r_min_row or min_row > r_max_row or
                max_col < r_min_col or min_col > r_max_col):
            return r

    # 如果没有找到与输入 range 重合的 range，返回 False
    return []


def get_no_border_cells(cell: CellObject, sheet, cells: Dict[Tuple[int, int], CellObject], search_directions=None,
                        search_depth=5, cell_range=None):
    if cell_range is None:
        cell_range = []
    if search_directions is None:
        search_directions = ['top', 'left', 'right', 'bottom']

    if search_depth == -1:
        search_count = float('inf')
    else:
        search_count = len(search_directions) * search_depth

    max_row = sheet.max_row
    max_column = sheet.max_column

    # 检查单元格是否有边框
    def has_border(cell: CellObject, direction):
        if not (cell and cell.border):
            return False
        if direction == 'left':
            left_cell = cells[(cell.row_index, cell.col_index-1)] if cell.col_index > 1 else None
            if left_cell and left_cell.border:
                return cell.border.border_left.border_style is not None or (
                        left_cell is not None and left_cell.border.border_right.border_style is not None)
        elif direction == 'right':
            right_cell = cells[(cell.row_index, cell.col_index+1)]
            if right_cell and right_cell.border:
                return cell.border.border_right.border_style is not None or right_cell.border.border_left.border_style is not None
        elif direction == 'top':
            top_cell = cells[(cell.row_index - 1, cell.col_index)] if cell.row_index > 1 else None
            if top_cell and top_cell.border:
                return cell.border.border_top.border_style is not None or (
                        top_cell is not None and top_cell.border.border_bottom.border_style is not None)
        elif direction == 'bottom':
            bottom_cell = cells[(cell.row_index + 1, cell.col_index)]
            if bottom_cell and bottom_cell.border:
                return cell.border.border_bottom.border_style is not None or bottom_cell.border.bottom_top.bottom_style is not None
        return False

    visited = []
    queue = [(cell.row_index, cell.col_index)]

    while queue and search_count > 0:
        row, col = queue.pop(0)
        if (row, col) in visited:
            continue
        visited.append((row, col))
        cell = cells[(row, col)]
        if cell_range and not cell_in_range(cell.row_index, cell.col_index, cell_range):
            continue
        # 检查上边的单元格
        if 'top' in search_directions:
            if row > 1 and not has_border(cell, 'top'):
                # logging.info(f'{cell.coordinate} no top border')
                queue.append((row - 1, col))

        # 检查左边的单元格
        if 'left' in search_directions:
            if col > 1 and not has_border(cell, 'left'):
                # logging.info(f'{cell.coordinate} no left border')
                queue.append((row, col - 1))

        # 检查右边的单元格
        if 'right' in search_directions:
            if col <= max_column and not has_border(cell, 'right'):
                # logging.info(f'{cell.coordinate} no right border')
                queue.append((row, col + 1))

        # 检查下边的单元格
        if 'bottom' in search_directions:
            if row <= max_row and not has_border(cell, 'bottom'):
                # logging.info(f'{cell.coordinate} no bottom border')
                queue.append((row + 1, col))

        search_count -= 1

    return visited


def expand_tables(sheet, tables):
    # 遍历每个区域
    _new_tables = []
    for i, ((min_row, min_col), (max_row, max_col)) in enumerate(tables):
        if range_cross_ranges((min_row, min_col), (max_row, max_col), _new_tables):
            continue
        # 检查上边
        while min_row > 1:
            changed = False
            for col in range(min_col, max_col + 1):
                cell = sheet.cell(row=min_row, column=col)
                neighbor_cell = sheet.cell(row=min_row - 1, column=col)
                if (cell.border.left.style is not None and cell.border.left.style == neighbor_cell.border.left.style) or \
                        (
                                cell.border.right.style is not None and cell.border.right.style == neighbor_cell.border.right.style):
                    min_row -= 1
                    changed = True
                    break
            if not changed:
                break

        # 检查下边
        while max_row < sheet.max_row:
            changed = False
            for col in range(min_col, max_col + 1):
                cell = sheet.cell(row=max_row, column=col)
                neighbor_cell = sheet.cell(row=max_row + 1, column=col)
                if (cell.border.left.style is not None and cell.border.left.style == neighbor_cell.border.left.style) or \
                        (
                                cell.border.right.style is not None and cell.border.right.style == neighbor_cell.border.right.style):
                    max_row += 1
                    changed = True
                    break
            if not changed:
                break

        # 检查左边
        while min_col > 1:
            changed = False
            for row in range(min_row, max_row + 1):
                cell = sheet.cell(row=row, column=min_col)
                neighbor_cell = sheet.cell(row=row, column=min_col - 1)
                if (cell.border.top.style is not None and cell.border.top.style == neighbor_cell.border.top.style) or \
                        (
                                cell.border.bottom.style is not None and cell.border.bottom.style == neighbor_cell.border.bottom.style):
                    min_col -= 1
                    changed = True
                    break
            if not changed:
                break

        # 检查右边
        while max_col < sheet.max_column:
            changed = False
            for row in range(min_row, max_row + 1):
                cell = sheet.cell(row=row, column=max_col)
                neighbor_cell = sheet.cell(row=row, column=max_col + 1)
                if (cell.border.top.style is not None and cell.border.top.style == neighbor_cell.border.top.style) or \
                        (
                                cell.border.bottom.style is not None and cell.border.bottom.style == neighbor_cell.border.bottom.style):
                    max_col += 1
                    changed = True
                    break
            if not changed:
                break

        # 更新区域
        tables[i] = ((min_row, min_col), (max_row, max_col))
        _new_tables.append(((min_row, min_col), (max_row, max_col)))

    return _new_tables


def range_cross_ranges(range_start_, range_end_, range_list_):
    # 将输入的 range_start_ 和 range_end_ 转换为 (min_row, min_col, max_row, max_col) 形式
    min_row, min_col = range_start_
    max_row, max_col = range_end_

    # 遍历 range_list_ 中的每一个 range
    for r in range_list_:
        # 将 range_list_ 中的每一个 range 转换为 (min_row, min_col, max_row, max_col) 形式
        (r_min_row, r_min_col), (r_max_row, r_max_col) = r

        # 判断输入的 range 是否与当前 range 有重合
        if not (max_row < r_min_row or min_row > r_max_row or
                max_col < r_min_col or min_col > r_max_col):
            return True

    # 如果没有找到与输入 range 重合的 range，返回 False
    return False


def cell_in_range(row_, col_, list_):
    """
    判断单元格是否在指定范围内
    :param row_:
    :param col_:
    :param list_:
    :return:
    """
    if not list_:
        return False
    for h in list_:
        if isinstance(h, dict):
            for r in h["range"]:
                if r[0] <= row_ <= r[2] and r[1] <= col_ <= r[3]:
                    return True
        elif isinstance(h, (tuple, list)):
            if h[0][0] <= row_ <= h[1][0] and h[0][1] <= col_ <= h[1][1]:
                return True
    return False
