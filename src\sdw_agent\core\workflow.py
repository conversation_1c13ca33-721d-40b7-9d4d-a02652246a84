"""工作流引擎核心组件"""
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from datetime import datetime
import networkx as nx
from .base import BaseStep, StepInput, StepOutput, StepResult, StepStatus

from langgraph.prebuilt import create_react_agent
@dataclass
class WorkflowDefinition:
    """工作流定义"""
    workflow_id: str
    name: str
    description: str
    steps: List[str]  # 步骤ID列表
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WorkflowExecutionPlan:
    """工作流执行计划"""
    workflow_id: str
    steps_to_execute: List[str]
    parallel_groups: List[List[str]]  # 可并行执行的步骤组
    dependencies_graph: nx.DiGraph
    
    
@dataclass
class WorkflowExecutionResult:
    """工作流执行结果"""
    workflow_id: str
    status: StepStatus
    step_results: Dict[str, StepResult] = field(default_factory=dict)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    error: Optional[str] = None
    
    def complete(self):
        """标记工作流完成"""
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        
        # 根据步骤结果判断整体状态
        if any(r.status == StepStatus.FAILED for r in self.step_results.values()):
            self.status = StepStatus.FAILED
        elif all(r.status == StepStatus.SUCCESS for r in self.step_results.values()):
            self.status = StepStatus.SUCCESS
        else:
            self.status = StepStatus.SKIPPED


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self, max_parallel_workers: int = 5):
        self.max_parallel_workers = max_parallel_workers
        self.logger = logging.getLogger(__name__)
        self.steps_registry: Dict[str, BaseStep] = {}
        self.workflows_registry: Dict[str, WorkflowDefinition] = {}
        
    def register_step(self, step: BaseStep):
        """注册步骤"""
        self.steps_registry[step.step_id] = step
        self.logger.info(f"注册步骤: {step.name} (ID: {step.step_id})")
        
    def register_workflow(self, workflow: WorkflowDefinition):
        """注册工作流"""
        self.workflows_registry[workflow.workflow_id] = workflow
        self.logger.info(f"注册工作流: {workflow.name} (ID: {workflow.workflow_id})")
        
    def create_execution_plan(self, 
                            workflow_id: str, 
                            selected_steps: Optional[List[str]] = None) -> WorkflowExecutionPlan:
        """创建执行计划"""
        workflow = self.workflows_registry.get(workflow_id)
        if not workflow:
            raise ValueError(f"工作流不存在: {workflow_id}")
            
        # 确定要执行的步骤
        steps_to_execute = selected_steps or workflow.steps
        
        # 构建依赖图
        graph = nx.DiGraph()
        for step_id in steps_to_execute:
            step = self.steps_registry.get(step_id)
            if not step:
                raise ValueError(f"步骤不存在: {step_id}")
                
            graph.add_node(step_id)
            for dep in step.dependencies:
                if dep in steps_to_execute:
                    graph.add_edge(dep, step_id)
        
        # 检查循环依赖
        if not nx.is_directed_acyclic_graph(graph):
            raise ValueError("检测到循环依赖")
            
        # 计算并行执行组
        parallel_groups = self._calculate_parallel_groups(graph)
        
        return WorkflowExecutionPlan(
            workflow_id=workflow_id,
            steps_to_execute=steps_to_execute,
            parallel_groups=parallel_groups,
            dependencies_graph=graph
        )
    
    def _calculate_parallel_groups(self, graph: nx.DiGraph) -> List[List[str]]:
        """计算可并行执行的步骤组"""
        if not graph.nodes():
            return []
            
        # 使用拓扑排序分层
        layers = []
        remaining = set(graph.nodes())
        
        while remaining:
            # 找出没有依赖或依赖已满足的节点
            layer = []
            for node in remaining:
                predecessors = set(graph.predecessors(node))
                if not predecessors or predecessors.isdisjoint(remaining):
                    layer.append(node)
            
            if not layer:
                raise ValueError("无法计算执行顺序，可能存在问题")
                
            layers.append(layer)
            remaining -= set(layer)
            
        return layers
    
    def execute_workflow(self,
                        plan: WorkflowExecutionPlan,
                        initial_data: Dict[str, Any] = None) -> WorkflowExecutionResult:
        """执行工作流"""
        result = WorkflowExecutionResult(
            workflow_id=plan.workflow_id,
            status=StepStatus.RUNNING
        )
        
        # 共享数据存储
        shared_data = initial_data or {}
        
        try:
            # 按层执行
            for layer_idx, layer_steps in enumerate(plan.parallel_groups):
                self.logger.info(f"执行第 {layer_idx + 1} 层，包含步骤: {layer_steps}")
                
                # 并行执行同一层的步骤
                layer_results = self._execute_parallel_steps(
                    layer_steps, 
                    shared_data
                )
                
                # 检查是否有失败
                for step_id, step_result in layer_results.items():
                    result.step_results[step_id] = step_result
                    
                    if step_result.status == StepStatus.FAILED:
                        raise RuntimeError(f"步骤 {step_id} 执行失败")
                    
                    # 更新共享数据
                    if step_result.output:
                        shared_data[step_id] = step_result.output.data
                        
        except Exception as e:
            self.logger.error(f"工作流执行失败: {str(e)}")
            result.error = str(e)
            result.status = StepStatus.FAILED
        else:
            result.status = StepStatus.SUCCESS
        finally:
            result.complete()
            
        return result
    
    def _execute_parallel_steps(self,
                               step_ids: List[str],
                               shared_data: Dict[str, Any]) -> Dict[str, StepResult]:
        """并行执行多个步骤"""
        results = {}
        
        if len(step_ids) == 1:
            # 单个步骤直接执行
            step_id = step_ids[0]
            step = self.steps_registry[step_id]
            input_data = self._prepare_step_input(step, shared_data)
            results[step_id] = step.execute(input_data)
        else:
            # 多个步骤并行执行
            with ThreadPoolExecutor(max_workers=min(len(step_ids), self.max_parallel_workers)) as executor:
                future_to_step = {}
                
                for step_id in step_ids:
                    step = self.steps_registry[step_id]
                    input_data = self._prepare_step_input(step, shared_data)
                    future = executor.submit(step.execute, input_data)
                    future_to_step[future] = step_id
                
                for future in as_completed(future_to_step):
                    step_id = future_to_step[future]
                    try:
                        results[step_id] = future.result()
                    except Exception as e:
                        self.logger.error(f"步骤 {step_id} 执行异常: {str(e)}")
                        results[step_id] = StepResult(
                            step_id=step_id,
                            step_name=self.steps_registry[step_id].name,
                            status=StepStatus.FAILED,
                            error=str(e)
                        )
                        
        return results
    
    def _prepare_step_input(self, step: BaseStep, shared_data: Dict[str, Any]) -> StepInput:
        """准备步骤输入数据"""
        # 收集依赖步骤的输出
        input_data = {}
        
        for dep_id in step.dependencies:
            if dep_id in shared_data:
                input_data[dep_id] = shared_data[dep_id]
                
        # 添加初始数据
        if "initial_data" in shared_data:
            input_data["initial_data"] = shared_data["initial_data"]
            
        return StepInput(data=input_data)


class WorkflowScheduler:
    """工作流调度器 - 支持条件执行和动态调度"""
    
    def __init__(self, engine: WorkflowEngine):
        self.engine = engine
        self.logger = logging.getLogger(__name__)
        
    def schedule_conditional(self,
                           workflow_id: str,
                           conditions: Dict[str, callable],
                           initial_data: Dict[str, Any] = None) -> WorkflowExecutionResult:
        """条件调度执行"""
        # 根据条件动态确定要执行的步骤
        workflow = self.engine.workflows_registry[workflow_id]
        steps_to_execute = []
        
        for step_id in workflow.steps:
            if step_id in conditions:
                condition_func = conditions[step_id]
                if condition_func(initial_data):
                    steps_to_execute.append(step_id)
            else:
                # 没有条件的步骤默认执行
                steps_to_execute.append(step_id)
                
        self.logger.info(f"条件调度确定执行步骤: {steps_to_execute}")
        
        # 创建执行计划
        plan = self.engine.create_execution_plan(workflow_id, steps_to_execute)
        
        # 执行工作流
        return self.engine.execute_workflow(plan, initial_data) 