"""
模块名称 实施代码文件比较

V字对应：【FID_DEV_0027】自动化生成FileCompare成果物

主要功能：
该功能是针对新旧两个版本的代码进行代码差分，生成FileCompare成果物，并结合两个版本之间每个Commit的Code 
Diff解析结果，更新FileCompare成果物中的代码差分确认结果。

"""

import os
import sys
import json

from pathlib import Path
# project_root = Path(__file__).parent.parent.parent.parent
# sys.path.append(str(project_root))
from loguru import logger
from sdw_agent.config.env import ENV  # noqa
from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from typing import Optional  # Dict, List, Any, Optional
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将 third_party/ 文件夹添加到 sys.path
third_party_path = os.path.join(current_dir, "autodiff")
sys.path.append(third_party_path)

import auto_diff


@register_workflow("FileCompareCodediff")
class FileCompareCodediff(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.logger = logger.bind(name="FileCompareCodediff")
        self.BcompPath = ENV.config.autodiff.BcompPath
        self.IgnorePattern = ENV.config.autodiff.IgnorePattern
        self.BinPattern = ENV.config.autodiff.BinPattern

    def validate_input(self, code_path: str, version1: str, version2: str = None) -> bool:
        try:
            # 1. 检查 code_path 和 version1 是否为空
            if not code_path or not version1:
                self.logger.error("code_path 和 version1 不能为空")
                return False

            # 2. 检查 code_path 是否为有效路径
            if not Path(code_path).exists():
                self.logger.error(f"code_path 不存在: {code_path}")
                return False

            # 3. 构造 config.json 路径（code_path 下的 autodiff/config/config.json）
            current_file_dir = Path(__file__).resolve().parent
            config_dir = current_file_dir / "autodiff" / "config"
            config_file = config_dir / "config.json"
            # print(config_file)
            self.logger.error(f"{config_file}")
            # 4. 确保目录存在
            config_dir.mkdir(parents=True, exist_ok=True)

            config = {}
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                except json.JSONDecodeError:
                    # 如果文件内容不是有效的JSON，使用空字典
                    config = {}
            config = {
                "BcompPath": self.BcompPath,
                "IgnorePattern": self.IgnorePattern,
                "BinPattern": self.BinPattern
            }
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)

            # 5. 验证 BcompPath 可执行文件
            if not self._check_bc_path(self.BcompPath):
                return False
            # 6. 验证 gitconfig 配置
            if not self._check_git_config():
                return False

            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def _check_bc_path(self, path: str) -> bool:
        """验证指定路径下的 BComp.exe 是否可执行"""
        bc_path = Path(path)
        if not bc_path.exists():
            self.logger.error(f"BcompPath 路径不存在: {path}")
            return False
        if not bc_path.is_file():
            self.logger.error(f"BcompPath 路径不是文件: {path}")
            return False
        if not bc_path.name.endswith(".exe"):
            self.logger.error(f"BcompPath 不是可执行文件: {path}")
            return False
        return True

    def _check_git_config(self) -> bool:
        """验证 gitconfig 是否配置了 diff 和 difftool 工具"""
        user_dir = Path.home()  # 获取当前用户的用户目录
        gitconfig_path = user_dir / ".gitconfig"
        if not gitconfig_path.exists():
            self.logger.warning("未找到 .gitconfig 文件，请创建并配置 diff 工具。")
            return False
        # 读取配置内容
        try:
            with open(gitconfig_path, "r", encoding="utf-8") as f:
                content = f.read()
        except Exception as e:
            self.logger.error(f"读取 .gitconfig 失败: {e}")
            return False
        # 检查是否有 [diff] 和 [difftool] 配置
        if "[diff]" not in content or "[difftool]" not in content:
            self.logger.warning("请配置 [diff] 和 [difftool] 部分")
            return False
        # 检查 diff.tool 配置
        if "tool = hook" not in content:
            self.logger.warning("请设置 diff.tool = hook")
            return False
        self.logger.info("gitconfig 配置已验证，可以正常使用 diff 工具。")

        return True

    def execute(self, code_path: str, version1: str, version2: str = None) -> WorkflowResult:
        try:
            original_dir = os.getcwd()
            os.chdir(code_path)
            relative_paths = generate_commit_diff_list(code_path, version1, version2)
            absolute_paths = [
                (Path(code_path) / path).resolve().as_posix()
                for path in relative_paths
            ]
        except Exception as e:
            self.logger.error(f"生成 commit diff 失败: {e}")
            return WorkflowResult(
                status=WorkflowStatus.FAILURE,
                message=f"生成 commit diff 失败: {e}",
                data={"file_path": []}
            )
        finally:
            os.chdir(original_dir)  # 恢复工作目录  
        print("✅ 最终输出：")
        print(absolute_paths)
        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message="FileCompare 执行成功",
            data={"file_path": absolute_paths}
            )


def generate_commit_diff_list(code_path, start_commit, end_commit=None):
    """
    根据提供的 code_path, start_commit, end_commit 提取提交差异并处理。
    参数:
        code_path: 项目代码的根目录路径。
        start_commit: 起始提交哈希。
        end_commit: 结束提交哈希。
        （如果 end_commit 为 None，则表示只获取 commit_list 的前两个提交）
    """
    try:
        # 切换到 code_path 目录（如 get_commit_list 需要）
        os.chdir(code_path)
    except Exception as e:
        print(f"无法切换到目录 {code_path}，错误: {e}")
        return
    excel_paths = []
    if end_commit:
        # 当有 end_commit 时，获取两个提交之间的所有差异
        commit_list = auto_diff.get_commit_list2(start_commit=start_commit, end_commit=end_commit)
        commit_size = len(commit_list)
        result_list = []
        index = 0
        while index < (commit_size - 1):
            # 获取相邻两个提交的差异
            result = auto_diff.get_commit_diff(commit_list[index + 1], commit_list[index])
            result_list.append(result)
            index += 1
        # 获取起始提交和最新的提交差异（可能需要根据情况调整逻辑）
        result = auto_diff.get_commit_diff(start_commit, commit_list[-1])
        result_list.append(result)
        # 创建差异列表
        auto_diff.d2e.create_list(result_list, start_commit, end_commit)
        excel_paths = [r.info.get('excel_path') for r in result_list if r and 'excel_path' in r.info]
    else:
        # 当只有 start_commit 时，获取前两个提交的差异
        commit_list = auto_diff.get_commit_list(start_commit)
        if len(commit_list) >= 2:
            result = auto_diff.get_commit_diff(commit_list[1], commit_list[0])
            # 获取 excel_path
            excel_paths.append(result.info['excel_path'])
            # filepath = auto_diff.d2e.create_list([result], start_commit, None)
        else:
            print("提交列表长度不足，无法计算差异。")
    return excel_paths


# 示例调用逻辑
if __name__ == "__main__":
    # 初始化工作流处理器
    file_compare = FileCompareCodediff()

    # 设置测试参数
    code_path = "D:/AI_SDW/gfx_agl_hud"  # 替换为你的实际项目路径
    version1 = "v2.2.4"  # 起始版本
    # version1 = "3f6b7f5ec09a7e9275b2c6256c2703d80f0f7" 
    version2 = None  # 结束版本（可选）
    version2 = "v2.2.5"  # 结束版本（可选）

    try:
        # 执行 FileCompare 流程
        bool = file_compare.validate_input(code_path, version1, version2)
        result = file_compare.execute(code_path, version1, version2)

        # 输出执行结果
        if result.status == WorkflowStatus.SUCCESS:
            print("✅ 执行成功！")
            print(f"结果文件路径: {result}")
            print(f"结果文件路径: {result.data['file_path']}")
            print("请检查目标目录中是否存在 .result 文件夹及其中的内容。")
        else:
            print("❌ 执行失败！")
            print(f"错误信息: {result.message}")

    except Exception as e:
        print("❌ 测试过程中发生异常！")
        print(f"异常详情: {str(e)}")