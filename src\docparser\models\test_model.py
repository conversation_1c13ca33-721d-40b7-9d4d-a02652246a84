import json
import os

import pytest

from docparser.models.document import DocumentBlockObject


def test_document_from_dict():
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Test file's directory
    dir = os.path.join(current_dir, f"../testdata/excel_results")
    dir = os.path.normpath(dir)

    for root, _, files in os.walk(dir):
        for file in files:
            if file.endswith(".json"):
                file_path = os.path.join(root, file)

                # Read JSON content
                with open(file_path, "r", encoding="utf-8") as json_file:
                    try:
                        original_data = json.load(json_file)
                        del original_data["_elements"]
                        original_data = remove_underscores(original_data)
                        # Convert JSON data into a DocumentBlockObject
                        document_block = DocumentBlockObject.from_dict(original_data)
                        # Convert the DocumentBlockObject back into a dictionary
                        generated_data = document_block.to_dict()
                        assert original_data == generated_data, f"Mismatch for file: {file_path}"
                    except json.JSONDecodeError as e:
                        pytest.fail(f"Error decoding JSON in file {file_path}: {e}")
def remove_underscores(data):
    """
    Recursively remove underscores from all keys in a nested dictionary.

    Args:
        data (dict | list): The input dictionary or list to process.

    Returns:
        dict | list: The processed dictionary or list with underscores removed from keys.
    """
    ignore_keys = ["px_height", "px_width", "shape_type", "cell_list", "in_cell", "position"]
    if isinstance(data, dict):
        return {
            key.lstrip('_'): remove_underscores(value)
            for key, value in data.items()
            if key.lstrip('_') not in ignore_keys  # Exclude 'cell_list' key
        }
    elif isinstance(data, list):
        return [remove_underscores(item) for item in data]
    else:
        return data  # Return value unchanged if it's neither a dict nor a list