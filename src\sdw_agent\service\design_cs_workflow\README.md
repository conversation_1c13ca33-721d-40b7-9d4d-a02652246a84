# 设计基准Check Sheet工作流

## 概述

设计基准Check Sheet工作流用于分析Excel文件中的设计基准检查表数据，提供数据提取、隐藏行处理、数据过滤等功能。

## V字对应

- **V字阶段**: V2.1 基本设计
- **V字项目**: 基本设计

## 主要功能

1. **Excel文件结构分析** - 自动分析Excel文件结构，识别工作表
2. **表头映射和数据提取** - 将日文表头映射为英文字段，提取结构化数据
3. **隐藏行和隐藏列检测** - 检测并处理Excel中的隐藏行和隐藏列
4. **数据过滤和导出** - 支持按条件过滤数据，导出结构化结果

## 使用方法

### 基本使用

```python
from sdw_agent.service.design_cs_workflow import DesignCSWorkflow, DesignCSInputModel

# 创建工作流实例
workflow = DesignCSWorkflow()

# 准备输入数据
input_data = DesignCSInputModel(
    file_path="C:/path/to/your/excel_file.xlsm",
    target_sheet="設計基準CS-基本設計",
    include_hidden_rows=False
)

# 执行工作流
result = workflow.run(input_data)

if result.status == WorkflowStatus.SUCCESS:
    print(f"成功处理，提取了 {len(result.data['data_list'])} 行数据")
    # 访问结果数据
    data_list = result.data['data_list']
    header_mapping = result.data['header_mapping']
else:
    print(f"处理失败: {result.message}")
```

### 高级使用

```python
# 包含隐藏行并应用过滤条件
input_data = DesignCSInputModel(
    file_path="C:/path/to/your/excel_file.xlsm",
    target_sheet="設計基準CS-基本設計",
    include_hidden_rows=True,
    filter_major_category="特定大項目",
    filter_middle_category="特定中項目"
)

result = workflow.run(input_data)
```

### 使用工厂函数

```python
from sdw_agent.service.design_cs_workflow import create_design_cs_workflow

# 使用自定义配置
workflow = create_design_cs_workflow(config_path="custom_config.yaml")
```

## 输入参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| file_path | str | 是 | - | Excel文件路径 |
| target_sheet | str | 否 | None | 目标工作表名称，None时自动查找 |
| include_hidden_rows | bool | 否 | False | 是否包含隐藏行 |
| header_row | int | 否 | 6 | 表头行号 |
| start_row | int | 否 | 7 | 数据开始行号 |
| filter_major_category | str | 否 | None | 过滤大項目 |
| filter_middle_category | str | 否 | None | 过滤中項目 |

## 输出结果

输出结果包含以下字段：

- `data_list`: 提取的数据列表，每行数据包含所有映射的字段
- `header_mapping`: 日文表头到英文字段的映射关系
- `columns_hidden_status`: 列隐藏状态信息
- `total_rows`: 总行数
- `visible_rows`: 可见行数
- `hidden_rows`: 隐藏行数
- `sheet_name`: 实际使用的工作表名称
- `file_path`: 文件路径

## 配置说明

工作流支持通过YAML配置文件进行配置，主要配置项包括：

### 基本配置
- `name`: 工作流名称
- `description`: 工作流描述
- `version`: 版本号
- `author`: 作者

### 模块特定配置
- `default_file_path`: 默认文件路径
- `default_target_sheet`: 默认目标工作表
- `max_rows`: 最大处理行数
- `timeout`: 超时时间

详细配置请参考 `config.yaml` 文件。

## 错误处理

工作流包含完善的错误处理机制：

1. **文件验证** - 检查文件是否存在、格式是否支持
2. **参数验证** - 验证输入参数的有效性
3. **数据验证** - 使用Pydantic模型验证提取的数据
4. **异常捕获** - 捕获并记录处理过程中的异常

## 日志记录

工作流使用loguru进行日志记录，支持以下日志级别：

- `DEBUG`: 详细的调试信息
- `INFO`: 普通信息，表示正常的程序执行
- `WARNING`: 警告信息
- `ERROR`: 错误信息

## 注意事项

1. 确保Excel文件路径正确且文件可访问
2. 支持的Excel格式：.xlsx, .xlsm, .xls
3. 工作流会自动查找相关工作表，但建议明确指定目标工作表
4. 处理大文件时注意内存使用情况
5. 隐藏行处理可能影响性能，根据需要选择是否包含

## 版本历史

- v1.0.0: 初始版本，支持基本的Excel数据提取和处理功能
