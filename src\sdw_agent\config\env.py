import os
import pathlib
import shutil
import sys

from dynaconf import Dynaconf
from loguru import logger

from sdw_agent.config.logging_config import setup_logging

__all__ = [
    'ENV',
]


# 全局配置入口，新增yaml时需要在此处处理
class Environment:
    def __init__(self):
        setup_logging(console_level="INFO", file_level="DEBUG", intercept_uvicorn=True)
        # 按照绝对路径找到配置文件的路径（兼容打包单文件执行和调试运行，可查看spec文件）
        base_path = pathlib.Path(os.path.abspath(os.path.dirname(__file__)))

        # 获取用户路径
        home_dir = pathlib.Path.home()
        self.data_dir = home_dir / ".sdw"
        self.rule_dir = self.data_dir / "rules"
        if not os.path.exists(self.rule_dir):
            self.rule_dir.mkdir(exist_ok=True)
        # 获取配置目录
        sdw_dir = home_dir / self.data_dir / "config"

        # 配置文件路径
        config_file = sdw_dir / "config.yaml"

        # 查询当前配置版本
        current_version = Dynaconf(settings_files=[base_path / "config.yaml"]).version

        # 如果外部配置文件不存在，使用内部默认配置
        if os.path.exists(config_file):
            # 读取主配置
            logger.info(f'Loading config.yaml from: {config_file}')
            self.config = Dynaconf(settings_files=[config_file])
            if not hasattr(self.config, 'version') or current_version > self.config.version:
                logger.info(f'version update to {current_version}')
                logger.info(f'copy new config to {config_file}')
                config_file.parent.mkdir(exist_ok=True)
                shutil.copy(base_path / "config.yaml", config_file)
                self.config.reload()
        else:
            logger.info(f'config.yaml not found')
            logger.info(f'copy default config to {config_file}')
            config_file.parent.mkdir(exist_ok=True)
            shutil.copy(base_path / "config.yaml", config_file)
            logger.info(f'Loading config.yaml from: {config_file}')
            self.config = Dynaconf(settings_files=[config_file])

        prompt_file = sdw_dir / "prompt.yaml"
        # 如果外部配置文件不存在，使用内部默认配置
        if not os.path.exists(prompt_file):
            logger.info(f'prompt.yaml not found')
            logger.info(f'copy default config to {prompt_file}')
            config_file.parent.mkdir(exist_ok=True)
            shutil.copy(base_path / "prompt.yaml", prompt_file)
        logger.info(f'Loading prompt.yaml from: {prompt_file}')
        self.prompt = Dynaconf(settings_files=[prompt_file])
        # 创建SDW成果物存放路径
        output_path = os.path.join(pathlib.Path(self.config.output_data_path))
        if not os.path.exists(output_path):
            os.mkdir(output_path)


# 仅使用已配置的logger
ENV = Environment()
