"""
函数分析书服务模块
包含函数分析书生成等工作流
"""

from sdw_agent.service.func_analyze_book.func_analyze_book import FuncAnalyzeBookWorkflow, \
    FuncAnalyzeBookService, gen_function_book_multi_commit_service, gen_function_book_service, \
    get_function_analyze_book_service, gen_change_info_service, gen_change_detail_service, gen_change_name_service, \
    gen_change_name_service_new, gen_change_parents_service, gen_calling_func_service, extract_component_data, \
    find_module_in_string, find_function_by_line, get_gerrit_diff_code, get_local_diff_code, get_function_open_type, \
    gen_func_return_varname
from sdw_agent.service.func_analyze_book.models import FunctionAnalyzeBookConfig, FunctionAnalyzeBookResult, \
    FunctioncalledData, FunctionAnalyzeBookRequest, FunctionAnalyzeBookResponse
from sdw_agent.service.func_analyze_book.util.func_analyze_util import FunctionAnalyzeUtils, GitDiffAnalyzer, ComponentUtils, \
    FunctionRelationshipAnalyzer, ExcelBookUtils


# 保持向后兼容的函数接口

__all__ = [
    # 新的工作流类
    'FuncAnalyzeBookWorkflow',
    'FuncAnalyzeBookService',

    # 数据模型
    'FunctionAnalyzeBookConfig',
    'FunctionAnalyzeBookResult',
    'FunctioncalledData',
    'FunctionAnalyzeBookRequest',
    'FunctionAnalyzeBookResponse',

    # 工具类
    'FunctionAnalyzeUtils',
    'GitDiffAnalyzer',
    'ComponentUtils',
    'FunctionRelationshipAnalyzer',
    'ExcelBookUtils',

    # 向后兼容的函数接口
    'gen_function_book_multi_commit_service',
    'gen_function_book_service',
    'get_function_analyze_book_service',
    'gen_change_info_service',
    'gen_change_detail_service',
    'gen_change_name_service',
    'gen_change_name_service_new',
    'gen_change_parents_service',
    'gen_calling_func_service',
    'extract_component_data',
    'find_module_in_string',
    'find_function_by_line',
    'get_gerrit_diff_code',
    'get_local_diff_code',
    'get_function_open_type',
    'gen_func_return_varname'
]

