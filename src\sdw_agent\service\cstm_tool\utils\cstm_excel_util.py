import json
import os
import re
import pathlib
import traceback

import win32com.client as win32
from datetime import datetime
from loguru import logger
from sdw_agent.service.cstm_tool.utils.config_manager import config_manager


logger.bind(name="CSTM_TOOL")


class CSTMExcelUtil:
    def __init__(self, file_path, sheet_name=None, keep_vba=True):
        """
        初始化 ExcelUtil 对象
        
        Args:
            file_path (str): 文件路径
            sheet_name (str, optional): sheet 名称
            keep_vba (bool, optional): 是否保留VBA宏代码
        """
        if not pathlib.Path(file_path).exists():
            raise FileNotFoundError(
                # f"{gettext('未找到Excel文件：')}{file_path}"
                f"未找到Excel文件：{file_path}"
            )
            
        self.file_path = file_path
        self.sheet_name = sheet_name
        self.fill_color = 0xF47920
        self.fill_color_index = 41
        self.updated_cells = set()
        
        # 初始化Excel应用程序
        try:
            self.excel = win32.Dispatch("Excel.Application")
            self.excel.Visible = True
        except Exception as e:
            traceback.print_exc()
            raise Exception(
                # f"{gettext('打开文件失败，请确保已经关闭该文件:')}{file_path}"
                f"打开文件失败，请确保已经关闭该文件: {file_path}"
            )
        
        # 打开工作簿
        self.workbook = self.excel.Workbooks.Open(self.file_path)
        
        # 获取工作表
        if sheet_name:
            self.sheet = self.workbook.Sheets(sheet_name)
        else:
            self.sheet = self.workbook.ActiveSheet

        # 重置将文件中历史更新单元格样式
        self._reset_fill_color()

    def click_custom_button(self):
        """
        触发Excel文件中的自定义按钮
        
        Args:
            button_name (str): 按钮名称，默认为'CustomButton1'
        """
        try:
            # Get all shapes in the sheet
            shapes = self.sheet.Shapes
            buttons = []
            
            # Check each shape to find buttons
            for i in range(1, shapes.Count + 1):
                shape = shapes.Item(i)
                if shape.Type == 12:  # msoFormControl = 9 indicates a form control
                    buttons.append(shape.Name)
            
            if not buttons:
                logger.error("No buttons found in the current sheet")
                return
                
            logger.info("Available buttons:", buttons)
            # Try different macro name formats since Excel can be inconsistent
            try:
                self.workbook.Application.Run("Sheet1.CommandButton1_Click")
            except Exception as e:
                logger.error(f"First attempt failed: {str(e)}")
                try:
                    self.workbook.Application.Run("'Sheet1'!CommandButton1_Click") 
                except:
                    try:
                        self.workbook.Application.Run("CommandButton1_Click")
                    except Exception as e:
                        logger.error(f"Failed to run macro with all name formats: {str(e)}")
                        return

        except Exception as e:
            logger.error(f"Failed to click button: {str(e)}")

    def insert_row(self, row_index: int, data: dict):
        """
        在指定位置插入新行
        
        Args:
            row_index (int): 插入位置
            data (dict): 插入数据 {列字母: 值}
        """
        self.sheet.Rows(row_index).Insert()
        inserted_cells = set()
        for col_letter, value in data.items():
            # 将列字母转换为列号
            col_index = ord(col_letter.upper()) - ord('A') + 1
            cell = self.sheet.Cells(row_index, col_index)
            cell.Value = value
            self._set_fill_color(row_index, col_index, cell) # 设置背景色为F47920
            inserted_cells.add((row_index, col_index))
        self.updated_cells.update(inserted_cells)

    def delete_row(self, row_index: int):
        """
        删除指定行
        
        Args:
            row_index (int): 删除行
        """
        self.sheet.Rows(row_index).Delete()

    def update_cell(self, row_index: int, data: dict):
        """
        更新指定位置的单元格值
        
        Args:
            row_index (int): 行索引
            data (dict): 更新数据 {列字母: 值}
        """
        updated_cells = set()   
        for col_letter, value in data.items():
            # 将列字母转换为列号
            col_index = ord(col_letter.upper()) - ord('A') + 1
            cell = self.sheet.Cells(row_index, col_index)
            cell.Value = value
            self._set_fill_color(row_index, col_index, cell) # 设置背景色为F47920
            updated_cells.add((row_index, col_index))
        self.updated_cells.update(updated_cells)


    def save(self) -> str:
        """
        保存更改到 excel 文件
        
        Returns:
            str: 新文件路径
            
        Raises:
            Exception: 如果保存失败
        """
        try:
            # 获取文件名和扩展名
            file_ext = pathlib.Path(self.file_path).suffix
            file_name = pathlib.Path(self.file_path).stem
            file_name = re.sub(r'(_\d{8}_\d{6})+', '', file_name)
            new_file_name = f"{file_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}"
            # 创建新文件路径
            new_path = str(pathlib.Path(config_manager.get('workflow_config.output.path')) / new_file_name)

            # 自动触发生成代码按钮 宏定义函数
            self.click_custom_button()

            if self.updated_cells:
                # 保存更新单元格位置
                self._save_updated_cells(new_file_name)

            # 另存为新文件
            self.workbook.SaveAs(new_path)
            
            # 关闭工作簿和Excel应用程序
            self.workbook.Close()
            # self.excel.Quit()
            
            # 设置Excel应用程序为不可见 不然会展示一个空的Excel文件
            self.excel.Visible = False
            
            
            logger.success(f"Excel文件保存成功: {new_path}")
            return new_path
            
        except Exception as e:
            if pathlib.Path(new_path).exists():
                pathlib.Path(new_path).unlink()
            traceback.print_exc()
            raise Exception(
                # f"{gettext('Excel文件保存失败:')}{str(e)}"
                f"Excel文件保存失败: {str(e)}"
            )

    def _reset_fill_color(self):
        """
        根据JSON记录重置特定单元格的背景颜色为无填充色
        """
        try:
            # 构建json文件路径
            json_path = os.path.join(pathlib.Path(config_manager.get('workflow_config.output.path')), 'temp', 'change_cell.json')
            
            # 如果json文件不存在，直接返回
            if not os.path.exists(json_path):
                return
                
            # 读取json数据
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 获取当前文件名（去掉_new后缀）和sheet名
            file_name = pathlib.Path(self.file_path).name
            # 检查文件名是否包含日期字符串尾缀
            contains_date_suffix = bool(re.search(r'(_\d{8}_\d{6})+', file_name))
            sheet_name = self.sheet.Name
            
            # 检查是否存在对应的记录
            if contains_date_suffix and file_name in data and sheet_name in data[file_name]:
                # 获取需要重置的单元格位置
                cells_to_reset = data[file_name][sheet_name]
                # 重置这些单元格的颜色
                for row_index, col_index in cells_to_reset:
                    try:
                        cell = self.sheet.Cells(row_index, col_index)
                        if cell.Interior.ColorIndex == self.fill_color_index:
                            self.sheet.Cells(row_index, col_index).Interior.ColorIndex = -4142  # 设置为无填充
                    except AttributeError:
                        continue
                
        except Exception as e:
            traceback.print_exc()
            raise Exception(
                # f"{gettext('重置单元格背景颜色失败:')}{str(e)}"
                f"重置单元格背景颜色失败: {str(e)}"
            )
        
    def _set_fill_color(self, row_index, col_index, cell):
        """
        设置单元格背景颜色
        """
        cell.Interior.Color = self.fill_color
        
    def _save_updated_cells(self, saved_file_name):
        """
        保存更新单元格位置
        """
        
        # 获取文件名（去掉_new后缀）和sheet名
        file_name = saved_file_name
        sheet_name = self.sheet.Name
        # 构建json文件路径
        json_path = os.path.join(pathlib.Path(config_manager.get('workflow_config.output.path')), 'temp', 'change_cell.json')
        # 确保temp目录存在
        os.makedirs(os.path.dirname(json_path), exist_ok=True)
        
        # 读取或创建json数据
        data = {}
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

        # 相同文件只保留最近十次的更新记录
        file_name_preffix = re.sub(r'(_\d{8}_\d{6})+', '', file_name)
        filtered_key= [key for key in data.keys() if key.startswith(file_name_preffix)]
        if len(filtered_key) >10 :
            remove_keys = filtered_key[:-10]
            for rem in remove_keys:
                data.pop(rem, None)

        # 更新数据
        if file_name not in data:
            data[file_name] = {}
        data[file_name][sheet_name] = list(self.updated_cells)

        # 写入json文件
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)



if __name__ == "__main__":
    # excel_util = ExcelUtil('C:/Users/<USER>/Desktop/副本tt.xlsm', sheet_name="Sheet1")
    # new_path = excel_util.save()

    excel_util = CSTMExcelUtil("D:/tdd_input/CSTM Tool配置文件-演示.xlsm", sheet_name="InputData")
    # path = str(pathlib.Path('Z:\input\CSTM Tool配置文件.xlsm'))
    # unblock_file(path)
    # excel_util = ExcelUtil(path, sheet_name="InputData")
    # excel_util.click_custom_button()
    new_path = excel_util.save()
