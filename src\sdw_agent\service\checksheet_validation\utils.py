"""
测试代理24工作流辅助工具

提供向量检索、文件处理等辅助功能
"""

import os
import pickle
import numpy as np
import asyncio
from collections import defaultdict
from typing import List, Dict, Tuple, Any, Optional, Set
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import gc

from loguru import logger
from openai import AzureOpenAI

from sdw_agent.service.checksheet_validation.models import StatusEnum, SheetMatchInfo, StatusDetail


async def embedding_func(texts: list[str], config: Dict[str, Any]) -> np.ndarray:
    """
    将文本转换为向量表示
    
    Args:
        texts: 文本列表
        config: Azure配置
        
    Returns:
        np.ndarray: 嵌入向量
    """
    # 获取配置
    azure_config = config.get("azure", {})
    api_key = azure_config.get("openai_api_key")
    api_version = azure_config.get("embedding_api_version")
    endpoint = azure_config.get("openai_endpoint")
    deployment = azure_config.get("embedding_deployment")
    batch_size = config.get("embedding", {}).get("batch_size", 5)
    
    if not api_key or not api_version or not endpoint or not deployment:
        raise ValueError("请确保所有Azure OpenAI相关配置已设置！")
    
    client = AzureOpenAI(
        api_key=api_key,
        api_version=api_version,
        azure_endpoint=endpoint,
    )
    
    all_embeddings = []
    for i in range(0, len(texts), batch_size):
        batch = texts[i:i+batch_size]
        embedding = client.embeddings.create(model=deployment, input=batch)
        all_embeddings.extend([item.embedding for item in embedding.data])
    
    return np.array(all_embeddings)


def get_valid_sheets(file_path: str) -> List[str]:
    """
    获取有效的工作表名称列表（B6单元格为'No.'的工作表）
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        List[str]: 有效工作表名称列表
    """
    wb = load_workbook(file_path, read_only=True)
    valid_sheets = []
    
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        cell_value = ws['B6'].value
        if cell_value == 'No.':
            valid_sheets.append(sheet_name)
            
    wb.close()
    return valid_sheets


def extract_sheet_rows_with_merged_cells(file_path: str, sheet_name: str) -> Tuple[List[str], List[str]]:
    """
    从第10行开始，提取工作表内容
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        
    Returns:
        Tuple[List[str], List[str]]: 
            - 全行内容（用空格连接）
            - B列序号 + D到P列内容（用空格连接）
    """
    wb = load_workbook(file_path, read_only=False)
    ws = wb[sheet_name]
    
    # 处理合并单元格
    merged_ranges = ws.merged_cells.ranges
    merged_map = {}
    for merged_range in merged_ranges:
        min_row, min_col, max_row, max_col = merged_range.min_row, merged_range.min_col, merged_range.max_row, merged_range.max_col
        for row in range(min_row, max_row + 1):
            for col in range(min_col, max_col + 1):
                merged_map[(row, col)] = (min_row, min_col, max_row, max_col)
    
    max_col = ws.max_column
    max_row = ws.max_row
    result_full = []
    result_d_to_p = []
    
    for row_idx in range(10, max_row + 1):
        row_values = []
        d_to_p_values = []
        b_col_value = str(row_idx - 9)
        
        for col in range(1, max_col + 1):
            cell = ws.cell(row=row_idx, column=col)
            pos = (cell.row, cell.column)
            value = cell.value
            
            if pos in merged_map:
                min_row, min_col, max_row_m, max_col_m = merged_map[pos]
                if col == min_col:
                    value = ws.cell(row=min_row, column=min_col).value
                else:
                    value = ''
                    
            value = str(value) if value is not None else ''
            value = value.replace('\n', '').replace('\r', '')  # 去除换行符
            
            # B列（第2列）直接替换为当前逻辑行号
            if col == 2:
                value = b_col_value
                
            row_values.append(value)
            
            if 4 <= col <= 16:
                d_to_p_values.append(value)
                
        result_full.append(' '.join(row_values))
        result_d_to_p.append(b_col_value + ' ' + ' '.join(d_to_p_values))
        
    wb.close()
    return result_full, result_d_to_p


async def embed_and_store_all_valid_sheets(
    file_path: str, 
    save_dir: str,
    config: Dict[str, Any]
) -> Set[str]:
    """
    提取、计算并保存所有有效工作表的嵌入向量
    
    Args:
        file_path: Excel文件路径
        save_dir: 保存目录
        config: 配置信息
        
    Returns:
        Set[str]: 有效工作表名称集合
    """
    sheets = get_valid_sheets(file_path)
    logger.info(f"找到合法sheet页: {sheets}")
    
    os.makedirs(save_dir, exist_ok=True)
    valid_sheet_names = set()
    
    for sheet in sheets:
        emb_path = os.path.join(save_dir, f"{sheet}_embeddings.npy")
        
        if os.path.exists(emb_path):
            logger.info(f"{emb_path} 已存在，跳过当前sheet: {sheet}")
            valid_sheet_names.add(sheet)
            continue
            
        logger.info(f"处理Sheet: {sheet}")
        sheet_rows, sheet_embeddings = extract_sheet_rows_with_merged_cells(file_path, sheet)
        
        # 过滤过长或空行
        sheet_embeddings = [str(s) for s in sheet_embeddings if s and str(s).strip() and len(str(s)) < 2000]
        
        if not sheet_embeddings:
            logger.warning(f"Sheet {sheet} 无有效内容，跳过。")
            continue
            
        emb_array = await embedding_func(sheet_embeddings, config)
        np.save(emb_path, emb_array)
        
        with open(os.path.join(save_dir, f"{sheet}_lines.pkl"), "wb") as f:
            pickle.dump(sheet_embeddings, f)
            
        logger.info(f"已保存: {sheet} 的embedding和文本")
        valid_sheet_names.add(sheet)
        
    return valid_sheet_names


def get_top_k_semantic_matches(
    embeddings: np.ndarray,
    query_emb: np.ndarray,
    lines: list,
    top_k: int = 10,
    score_threshold: float = 0.0
) -> List[Tuple[int, str, float]]:
    """
    获取语义上最匹配的前K个结果
    
    Args:
        embeddings: 嵌入向量数组
        query_emb: 查询向量
        lines: 文本行列表
        top_k: 返回结果数量
        score_threshold: 相似度阈值
        
    Returns:
        List[Tuple[int, str, float]]: (索引, 文本, 相似度)元组列表
    """
    query_norm = np.linalg.norm(query_emb)
    if query_norm == 0:
        return []
        
    scores = np.dot(embeddings, query_emb) / (np.linalg.norm(embeddings, axis=1) * query_norm + 1e-8)
    top_k_idx = np.argpartition(scores, -top_k)[-top_k:]
    top_k_idx = top_k_idx[np.argsort(-scores[top_k_idx])]
    
    results = []
    for idx in top_k_idx:
        if scores[idx] >= score_threshold:
            results.append((idx, lines[idx], scores[idx]))
            
    return results


def check_ok_ng(sheet_name: str, index_list: List[int], wb) -> StatusEnum:
    """
    检查给定行是否包含OK或NG
    
    Args:
        sheet_name: 工作表名称
        index_list: 行索引列表（相对于第10行的偏移）
        wb: 工作簿对象
        
    Returns:
        StatusEnum: 检查结果状态
    """
    if not index_list:
        return StatusEnum.NONE
        
    ws = wb[sheet_name]
    result = StatusEnum.OTHER
    
    for idx in index_list:
        row_num = idx + 10  # 直接用index+10作为Excel行号
        if row_num > ws.max_row:
            continue
            
        row_cells = [ws.cell(row=row_num, column=col).value for col in range(1, ws.max_column+1)]
        row_cells_str = [str(c) if c is not None else '' for c in row_cells]
        
        if any('NG' in c for c in row_cells_str):
            return StatusEnum.NG
            
        if any('OK' in c for c in row_cells_str):
            result = StatusEnum.OK
            
    return result


def group_sheet_indices_by_status(index_list: List[Tuple[str, int]], wb) -> Dict[str, Dict[str, List[str]]]:
    """
    按状态分组工作表索引
    
    Args:
        index_list: [(工作表名称, 索引), ...] 列表
        wb: 工作簿对象
        
    Returns:
        Dict[str, Dict[str, List[str]]]: 按工作表和状态分组的索引
    """
    sheet_to_status_indices = defaultdict(lambda: defaultdict(list))
    
    for sheet, idx in index_list:
        status = check_ok_ng(sheet, [idx], wb)
        sheet_to_status_indices[sheet][status].append(str(idx))
        
    return sheet_to_status_indices


def format_sheet_status_output(sheet_to_status_indices: Dict[str, Dict[str, List[str]]]) -> str:
    """
    格式化工作表状态输出
    
    Args:
        sheet_to_status_indices: 按工作表和状态分组的索引
        
    Returns:
        str: 格式化的输出文本
    """
    lines = []
    for sheet, status_dict in sheet_to_status_indices.items():
        lines.append(f"{sheet}:")
        for status in [StatusEnum.OTHER, StatusEnum.NG, StatusEnum.OK, StatusEnum.NONE]:
            if status in status_dict and status_dict[status]:
                idxs = ",".join(status_dict[status])
                lines.append(f"{status}:{idxs}")
                
    return "\n".join(lines).strip()


def merge_check_from_status_indices(sheet_to_status_indices: Dict[str, Dict[str, List[str]]]) -> StatusEnum:
    """
    从状态索引中合并检查结果
    
    Args:
        sheet_to_status_indices: 按工作表和状态分组的索引
        
    Returns:
        StatusEnum: 合并后的状态
    """
    has_O = any(StatusEnum.OTHER in status_dict and status_dict[StatusEnum.OTHER] 
                for status_dict in sheet_to_status_indices.values())
                
    has_NG = any(StatusEnum.NG in status_dict and status_dict[StatusEnum.NG] 
                 for status_dict in sheet_to_status_indices.values())
                 
    has_OK = any(StatusEnum.OK in status_dict and status_dict[StatusEnum.OK] 
                 for status_dict in sheet_to_status_indices.values())
    
    if has_O:
        return StatusEnum.OTHER
    elif has_NG:
        return StatusEnum.NG
    elif has_OK:
        return StatusEnum.OK
    else:
        return StatusEnum.NONE


def write_filenames_to_excel(ws, filenames: List[str], start_row: int = 8, start_col: int = 5):
    """
    将文件名写入Excel
    
    Args:
        ws: 工作表对象
        filenames: 文件名列表
        start_row: 开始行
        start_col: 开始列
    """
    for index, filename in enumerate(filenames):
        current_col = start_col + 2 * index
        ws.cell(row=start_row, column=current_col, value=filename)
        ws.cell(row=start_row, column=current_col + 1, value="对应NO") 