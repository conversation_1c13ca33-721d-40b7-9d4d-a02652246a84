req_design_verify:
  target_sheet: "機能一覧と新規・変更内容"
  header_start_row: 5
  header_end_row: 7
  data_start_row: 8
  start_col: 1
  end_col: 100
  exclude_hidden_rows: true
  req_content_column: "要件の内容"
  design_book_column: "パラメータ設計書"
  consecutive_empty_limit: 5
  min_design_book_length: 3
  output_subdir: "check_results"
  cleanup_temp_files: true
  temp_file_prefix: "temp_req_design_verify_"
  
  # 检查规则配置
  check_rules:
    skip_empty_req: true
    stop_on_consecutive_empty: true
    design_book_validation:
      min_length: 3
      invalid_values: ["", "-", "None", "nan"]
      
  # 输出配置
  output:
    include_summary: true
    include_statistics: true
    add_styling: true
    column_widths:
      row_number: 8
      req_content: 50
      design_book: 30
      check_result: 12
      
  # 样式配置
  styles:
    header_color: "D3D3D3"
    ok_color: "90EE90"
    ng_color: "FFB6C1"
    title_font_size: 16
    header_font_size: 12
