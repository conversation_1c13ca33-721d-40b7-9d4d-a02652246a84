from typing import Dict, List, Tuple
from pydantic import BaseModel, Field

class GitCommitChange(BaseModel):
    """表示单个提交的变更详情"""
    commit_id: str
    author: str
    changes: Dict[str, Dict]  # 文件路径 -> {
                              #   'diff': str,        # 变更差异
                              #   'code_before': str, # 变更前内容
                              #   'code_after': str,  # 变更后内容
                              # }

class DscsCodeCheckInput(BaseModel):
    """工作流输入模型"""
    repo_path: str = Field(description="代码仓库路径")
    commit_id_base: str = Field(description="要检查的commit id的基础版本")
    commit_id: str = Field(description="要检查的commit id")

class DscsCodeCheckResult(BaseModel):
    """代码检查结果项"""
    file_path: str
    issues: List[Dict]
    severity: str

class ConfirmFormatDSCSNew(BaseModel):
    """LLM确认结果模型"""
    confirm_result: str = Field(
        description="",
    )

class ConfirmFormatDSCSChange(BaseModel):
    """LLM确认结果模型"""
    status: str = Field(description="结论的状态，存在或不存在")
    reason: str = Field(description="检查结果的原因说明")