"""
设计基准Check Sheet工作流数据模型

定义工作流中使用的输入输出数据结构和配置模型。
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator
from sdw_agent.service.workflow_config import WorkflowConfigManager


def _get_design_cs_config():
    """获取设计基准工作流配置"""
    try:
        config_manager = WorkflowConfigManager(workflow_name="design_cs_workflow")
        return config_manager.get_config("module_specific")
    except Exception:
        # 如果配置加载失败，返回默认值
        return {
            "default_header_row": 6,
            "default_start_row": 7,
            "max_rows": 10000,
            "timeout": 300
        }


class AnalysisResultModel(BaseModel):
    """分析结果模型"""
    total_items: int = Field(description="总项目数")
    processed_items: int = Field(description="已处理项目数")
    hidden_items: int = Field(description="隐藏项目数")
    categories: Dict[str, int] = Field(description="分类统计")
    summary: str = Field(description="分析摘要")


class DesignCSInputModel(BaseModel):
    """设计基准Check Sheet工作流输入模型"""
    file_path: str = Field(description="Excel文件路径")
    target_sheet: Optional[str] = Field(default=None, description="目标工作表名称")
    include_hidden_rows: bool = Field(default=False, description="是否包含隐藏行")
    header_row: Optional[int] = Field(default=None, description="表头行号，不指定则使用配置默认值")
    start_row: Optional[int] = Field(default=None, description="数据开始行号，不指定则使用配置默认值")
    filter_major_category: Optional[str] = Field(default=None, description="过滤大項目")
    filter_middle_category: Optional[str] = Field(default=None, description="过滤中項目")

    # 新增：AI分析相关参数
    enable_ai_analysis: bool = Field(default=False, description="是否启用AI分析")
    ar_no: Optional[str] = Field(default=None, description="AR编号")
    ar_summary: Optional[str] = Field(default=None, description="AR摘要")
    p_no: Optional[str] = Field(default=None, description="P编号")

    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        if not v or not v.strip():
            raise ValueError("文件路径不能为空")
        return v.strip()

    @field_validator('header_row', 'start_row')
    @classmethod
    def validate_row_numbers(cls, v):
        if v is not None and v < 1:
            raise ValueError("行号必须大于0")
        return v

    def get_header_row(self) -> int:
        """获取表头行号，如果未指定则从配置中获取默认值"""
        if self.header_row is not None:
            return self.header_row
        config = _get_design_cs_config()
        return config.get("default_header_row", 6)

    def get_start_row(self) -> int:
        """获取数据开始行号，如果未指定则从配置中获取默认值"""
        if self.start_row is not None:
            return self.start_row
        config = _get_design_cs_config()
        return config.get("default_start_row", 7)


class DesignCSOutputModel(BaseModel):
    """设计基准Check Sheet工作流输出模型"""
    analysis_result: AnalysisResultModel = Field(description="分析结果")
    extracted_data: List[Dict[str, Any]] = Field(description="提取的原始数据")
    metadata: Dict[str, Any] = Field(description="元数据信息")
    processing_info: Dict[str, Any] = Field(description="处理信息")
    file_path: str = Field(description="文件路径")
    sheet_name: str = Field(description="工作表名称")
    success: bool = Field(description="处理是否成功")


class DesignCSConfigModel(BaseModel):
    """设计基准Check Sheet工作流配置模型"""
    name: str = Field(description="工作流名称")
    description: str = Field(description="工作流描述")
    version: str = Field(description="版本号")
    author: str = Field(description="作者")

    # 模块特定配置
    default_file_path: str = Field(description="默认文件路径")
    default_target_sheet: str = Field(description="默认目标工作表")
    default_header_row: int = Field(description="默认表头行号")
    default_start_row: int = Field(description="默认数据开始行号")
    max_rows: int = Field(description="最大处理行数")
    timeout: int = Field(description="超时时间（秒）")

    @field_validator('max_rows', 'timeout')
    @classmethod
    def validate_positive_numbers(cls, v):
        if v <= 0:
            raise ValueError("数值必须大于0")
        return v

    @classmethod
    def from_config(cls) -> "DesignCSConfigModel":
        """从配置文件创建配置模型实例"""
        try:
            config_manager = WorkflowConfigManager(workflow_name="design_cs_workflow")
            base_config = config_manager.get_config()
            module_config = config_manager.get_config("module_specific")

            return cls(
                name=base_config.get("name", "设计基准Check Sheet工作流"),
                description=base_config.get("description", "分析Excel文件中的设计基准检查表数据"),
                version=base_config.get("version", "1.0.0"),
                author=base_config.get("author", "SDW Agent"),
                default_file_path=module_config.get("default_file_path", "C:/tdd_input/ソフトウェア 設計基準CS.xlsm"),
                default_target_sheet=module_config.get("default_target_sheet", "設計基準CS-基本設計"),
                default_header_row=module_config.get("default_header_row", 6),
                default_start_row=module_config.get("default_start_row", 7),
                max_rows=module_config.get("max_rows", 10000),
                timeout=module_config.get("timeout", 300)
            )
        except Exception:
            # 如果配置加载失败，使用硬编码默认值
            return cls(
                name="设计基准Check Sheet工作流",
                description="分析Excel文件中的设计基准检查表数据",
                version="1.0.0",
                author="SDW Agent",
                default_file_path="C:/tdd_input/ソフトウェア 設計基準CS.xlsm",
                default_target_sheet="設計基準CS-基本設計",
                default_header_row=6,
                default_start_row=7,
                max_rows=10000,
                timeout=300
            )
