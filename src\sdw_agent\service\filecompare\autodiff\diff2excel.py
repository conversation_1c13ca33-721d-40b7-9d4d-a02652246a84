
import shutil
import os

import pyperclip
import xlwings as xw
from xlwings.constants import <PERSON>eType, LineStyle,BordersIndex,BorderWeight
from diffconfig import Config, CONFIG_PATH
import re
from jira_helper import JiraHelper

DIFF_TEMPLATE_PATH = os.path.join(CONFIG_PATH, 'file_diff_template.xlsx') 
LIST_TEMPLATE_PATH = os.path.join(CONFIG_PATH, 'diff_list_template.xlsx') 

def create_diff(config:Config):
    filename =  f"【19PFv3】コード差分_{Config.get_repository()}_{config.commitid}.xlsx"
    filepath =  os.path.join(config.basedir,filename)
        
    shutil.copyfile(DIFF_TEMPLATE_PATH, filepath)
    update_excel(config,filepath)
    
    config.set_excel_path(filepath)
        
    return config

def create_list(config_list:list[Config], start:str, end:str):
    result_path = Config.get_reslut_path(f'{start}_{end}')
    filename =  f"【19PFv3】コード差分_{Config.get_repository()}_{start}_{end}.xlsx"
    filepath =  os.path.join(result_path,filename)

    shutil.rmtree(result_path, True)
    os.makedirs(result_path)
    shutil.copyfile(LIST_TEMPLATE_PATH, filepath)
    
    book = xw.Book(filepath)
        
    list_sheet = book.sheets['List']
    list_sheet.range('D2').value = start
    list_sheet.range('D3').value = end
    
    update_list_excel(config_list, list_sheet, result_path)
    
    book.save()
    book.close()
    
        
    return filepath

def get_jirano(commitmsg:str):
    return re.findall(r'MET19PFV3-\d+', commitmsg)   

def update_cover(config:Config, sheet:xw.Sheet):
    sheet.range('B16').value = f'【{config.repository}】\n {config.commitid}'
    sheet.range('B21').value = config.commitmsg
        
def update_filesheet(filesheet:xw.Sheet, template:xw.Sheet, diffpath):
    with open(diffpath, encoding='utf-8',errors='ignore') as file:
        text = file.read()
        text = text.encode('utf-8',errors='ignore').decode('gbk',errors='ignore')
        filesheet.api.Application.CutCopyMode=1
        taget = filesheet.range('A1')
        error = True
        trycount = 0
        while error and trycount < 5 :            
            try:          
                pyperclip.copy(text)
                taget.api.PasteSpecial(Paste=PasteType.xlPasteAll)
            except Exception:
                print("Error:",Exception)
            else:
                error = False

            trycount += 1
        
        if error:
            raise  Exception(f'Can not copy data to excel! {trycount} times.')
        
        last_row = filesheet.used_range.last_cell.row
        
        template.range('A4:I6').copy(filesheet.range('A4:I6'))
        filesheet.range('F4').formula=f'=IF(COUNTIF($F$7:$F{last_row},"待确认")=0,"Y","N")'
        filesheet.range('G4').formula=f'=IF(COUNTIF($F$7:$F{last_row},"待确认")=0,"Y","N")'
        filesheet.range('H4').formula=f'=IF(COUNTIF($G$7:$G{last_row},"NG")=0,"Y","N")'
        filesheet.range('I4').formula=f'=IF(COUNTIF($H$7:$H{last_row},"NG")=0,"Y","N")'
                
        confirm_range = filesheet.range(f'F7:I{last_row}')        
        template.range('F7:I7').copy(confirm_range)
                
        # Borders(12) 内部水平边线。
        confirm_range.api.Borders(BordersIndex.xlInsideHorizontal).LineStyle = LineStyle.xlLineStyleNone

        # Borders(8) 顶部框。
        confirm_range.api.Borders(BordersIndex.xlEdgeTop).LineStyle = LineStyle.xlContinuous
        confirm_range.api.Borders(BordersIndex.xlEdgeTop).Weight = BorderWeight.xlMedium
        
        # Borders(9) 底部边框
        confirm_range.api.Borders(BordersIndex.xlEdgeBottom).LineStyle = LineStyle.xlContinuous
        confirm_range.api.Borders(BordersIndex.xlEdgeBottom).Weight = BorderWeight.xlMedium
        
        filesheet.range('F6:I6').api.AutoFilter(Field=1, Criteria1="=待确认") 
        
def update_excel(config:Config, file):
    book = xw.Book(file)
    update_cover(config,book.sheets['Cover'])
    template = book.sheets['template']
    filelist = book.sheets['FileList']
    row = 11
    
    filelist.range(f'C3').value = config.commitid_previous
    filelist.range(f'C6').value = config.commitid
    try:    
        for (filename, diffinfo) in config.info['diffs'].items():
            sheetname = os.path.basename(filename)
            name = sheetname[-30:]
            count = 0
            while True:        
                if name.lower() in (s.name.lower() for s in template.book.sheets):
                    count += 1
                    name = f'{sheetname[-30:]}{count}'
                else:
                    filesheet = template.copy(name=name)
                    break
                
            filelist.range(f'C{row}').value = config.repository
            
            difftype = diffinfo['type']
            diffpath = diffinfo['diff']
            match difftype:
                case Config.DIFF_DELETE:
                    filelist.range(f'D{row}').value = filename
                    filelist.range(f'E{row}').value = ''
                    filelist.range(f'F{row}').value = '削除'
                case Config.DIFF_NEW:
                    filelist.range(f'D{row}').value = ''
                    filelist.range(f'E{row}').value = filename
                    filelist.range(f'F{row}').value = '新規'                
                case _:
                    filelist.range(f'D{row}').value = filename
                    filelist.range(f'E{row}').value = filename
                    filelist.range(f'F{row}').value = '変更'
            
            if os.path.exists(diffpath):
                link_cell = filelist.range(f'G{row}')
                link_cell.value=filesheet.name
                link_cell.api.Hyperlinks.Add(Anchor=filelist.range(f'G{row}').api, Address="", SubAddress=f"'{filesheet.name}'!A1", TextToDisplay=filesheet.name)
                update_filesheet(filesheet, template, diffpath)
            else:
                filelist.range(f'G{row}').value = '自動差分実行なし、手操作の確認が必要'
                filelist.range(f'G{row}').font.color = '#ff0000'
            
            row += 1
        template.delete() 
        book.save() 
    except Exception as e:
        raise
    finally:    
        book.close() 
    
def update_list_excel(config_list, list_sheet, result_path):
    jira_helper = JiraHelper()
    row = 11
    for index in range(len(config_list)):
        config:Config = config_list[index]
        print ("Adding diff", config.commitid, '...')
        copy_name = f'No.{index + 1}_{os.path.basename(config.excel_path)}'
        copy_path = os.path.join(result_path, copy_name)
        shutil.copyfile(config.excel_path, copy_path)
        
        list_sheet.range(f'A{row}').value = index + 1
        list_sheet.range(f'B{row}').value = config.commitmsg
        list_sheet.range(f'G{row}').value = config.author
        list_sheet.range(f'H{row}').value = config.commitid
        list_sheet.range(f'I{row}').value = config.commitdate
        list_sheet.range(f'J{row}').formula=f'=XLOOKUP($G{row}, data!A:A,data!B:B)'
        list_sheet.range(f'M{row}').add_hyperlink(address=copy_name, text_to_display=copy_name)
        
        jirano_list = get_jirano(config.commitmsg)
        if len(jirano_list) > 0:
            epic_summary_list = []
            ar_no_list = []
            for jirano in jirano_list:
                epic = jira_helper.get_epic(jirano) 
                if epic:       
                    epic_summary_list.append(epic.fields.summary)
                    ar_list = jira_helper.get_ar(epic)
                    ar_no_list.append('/'.join([ar.key for ar in ar_list] if len(ar_list) > 0 else '-'))
                else:
                    epic_summary_list.append('-')
                    ar_no_list.append('-')
            list_sheet.range(f'D{row}').value = '\n'.join(ar_no_list) if len(ar_no_list) > 0 else '-'
            list_sheet.range(f'E{row}').value = '\n'.join(epic_summary_list) if len(epic_summary_list) > 0 else '-'
            list_sheet.range(f'F{row}').value = '\n'.join(jirano_list) if len(jirano_list) > 0 else '-'
        else:
            list_sheet.range(f'D{row}').value = '-'
            list_sheet.range(f'E{row}').value = '-'
            list_sheet.range(f'F{row}').value = '-'
        
        row += 1
        
        print ("Add diff", config.commitid, 'finished!')
    
