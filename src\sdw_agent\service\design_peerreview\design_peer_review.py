"""
設計レビュー開催工作流

V字对应：
設計レビュー開催

根据输入路径遍历文件夹下所有文档检查是否没有问题
以下观点审议可否转移下一工序"
为了防止后续工序中的“返工”，审议“2.1基本设计”中的讨论结果是否存在误解/考虑遗漏"作为设计工程的公式DR，审议项目是否运作得当"


"""

import glob
import os
import pathlib
from cgitb import reset

import pandas as pd
import xlwings as xw
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries, column_index_from_string 
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.styles import PatternFill, Border, Side, Alignment,Font
from pydantic import BaseModel, Field
import difflib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow

from sdw_agent.util.git_util import clone_repo_of_branch
from sdw_agent.config.env import ENV
from sdw_agent.service.design_peerreview.models import BasicDesignInput
from sdw_agent.service.design_peerreview import basic_design,coding_design,detail_design,design_peerreview_llm,common_cs
from sdw_agent.service.workflow_config import WorkflowConfigManager

@register_workflow("design_peer_review")
class Design_PeerreviewWorkflow(BaseWorkflow):
    def __init__(self, config_path: Optional[str] = None):
        """
                初始化IF整合性确认工作流

                Args:
                    config_path: 配置文件路径，如不提供则使用默认路径
                """
        # 调用父类构造方法，初始化通用属性
        super().__init__(config_path)

        # 注册配置模型，为后续的配置解析和验证做准备
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """
        注册配置模型用于验证

        本函数通过调用配置管理器，注册一个特定的配置模型，用于后续的配置验证
        """
        config_manager = WorkflowConfigManager(workflow_name="design_peer_review")

    def validate_input(self, input_data: BasicDesignInput) -> bool:
        """验证输入参数"""
        try:
            # 检查路径是否存在
            file_path = Path(input_data.dir_path)
            if not file_path.exists():
                self.logger.error(f"文件不存在: {input_data.dir_path}")
                return False
            return True
        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, input_data: BasicDesignInput) -> WorkflowResult:
        """执行工作流"""
        self.logger.info("开始执行review开崔工作流")

        try:
            # 获取文件的V字名称
            file_info = common_cs.check_v_process_name(input_data.dir_path, input_data.peer_review_type)
            review_results = []
            file_count = 0
            for file_key, file_value in file_info.items():
                v_process_name = file_value['所属V字项目']
                logger.info(f"总计{len(file_info)}个文件，当前正在检查第{file_count+1}个...")
                logger.info(f"正在检查文档{file_key}...")
                file_count = file_count+1
                result = None
                if v_process_name != '':
                    result = self.check_function[v_process_name](file_key)
                review_results.extend(self.generator_data(result, file_value[f"文件路径"], file_value[f"文件名"], file_value[f"所属V字项目"]))

            logger.info(f"检测完成，准备写入数据")
            # 创建DataFrame并写入Excel
            df = pd.DataFrame(review_results)
            review_file_name = [
                "開催.xlsx",
                "設計レビュー開催_基本.xlsx",
                "設計ピアレビュー開催_詳細.xlsx",
                "設計レビュー開催_基本_詳細.xlsx",
                "コードレビュー開催_コード.xlsx",
                "設計レビュー開催_基本_コード.xlsx",
                "設計レビュー開催_詳細_コード.xlsx",
                "設計レビュー開催_基本_詳細_コード.xlsx",
            ]
            # 生成输出文件路径
            output_file_name = review_file_name[input_data.peer_review_type&0x07]
            if os.path.isdir(input_data.dir_path):
                output_path = os.path.join(input_data.dir_path, output_file_name)
            else:
                output_path = os.path.join(os.path.dirname(input_data.dir_path), output_file_name)
            # 使用封装的Excel格式化函数
            excel_result = self._format_excel_with_styles(df, output_path)

            if excel_result['success']:
                logger.success(f"评审结果已写入: {output_path}")
                return WorkflowResult(
                    status=WorkflowStatus.SUCCESS,
                    message=f"更新工作流执行成功",
                    data={"output_message": output_path}
                )
            else:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message=f"更新工作流执行失败",
                    data={"output_message": review_results}
                )
        except Exception as e:
            self.logger.exception(f"更新工作流执行失败")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"更新工作流执行失败: {str(e)}",
                error=str(e)
            )

    @classmethod
    def generator_data(self,data_src,file_path,file_name,v_process_name):
        review_results = []
        if data_src is None :
            if "~" not in file_name:
                logger.info(f"{file_name} 找不到V字未检测")
                review_results.append({
                    f"文件路径": file_path,
                    f"文件名": file_name,
                    f"所属V字项目": "-",
                    f"Sheet名称": "-",
                    f"确认是否有空白项": "-",
                    f"确认没有NG项": "-",
                    f"确认签字盖章是否OK": "-",
                    f"确认内容填写是否异常": "-",
                    # '成果物': os.path.basename(file_key),
                    # '可否review': '可' if result['success'] else '否',
                    '理由': "-"
                })
            return review_results
        for sheet_name_key, check_result_value in data_src.items():
            llm_result = ""
            ng_result = "-"
            stamp_result = "-"
            blank_result = "-"
            content_result = "-"
            #for check_result_value in check_results:
            #for check_result_key,check_result_value in check_results.items():
            if f"确认是否有空白项" in check_result_value.keys():
                blank_result = "OK" if check_result_value[f"确认是否有空白项"] else "NG"
                llm_result = llm_result + str(check_result_value['blank_message'])
            if f"确认没有NG项" in check_result_value.keys():
                ng_result = "OK" if check_result_value[f"确认没有NG项"] else "NG"
                llm_result = llm_result + str(check_result_value['ng_message'])
            if f"确认签字盖章是否OK" in check_result_value.keys():
                stamp_result = "OK" if check_result_value[f"确认签字盖章是否OK"] else "NG"
                llm_result = llm_result + str(check_result_value['stamp_message'])
            if f"确认内容填写是否异常" in check_result_value.keys():
                content_result = "OK" if check_result_value[f"确认内容填写是否异常"] else "NG"
                llm_result = llm_result + str(check_result_value['content_message'])
            if "NG" in (ng_result+stamp_result+blank_result+content_result) :
                llm_result = design_peerreview_llm.llm_peer_review(llm_result)
            else:
                llm_result = "-"
            review_results.append({
                f"文件路径": file_path,
                f"文件名": file_name,
                f"所属V字项目": v_process_name,
                f"Sheet名称": sheet_name_key,
                f"确认是否有空白项": blank_result,
                f"确认没有NG项": ng_result,
                f"确认签字盖章是否OK": stamp_result,
                f"确认内容填写是否异常":content_result,
                # '成果物': os.path.basename(file_key),
                # '可否review': '可' if result['success'] else '否',
                '理由': llm_result
            })
        return review_results

    @classmethod
    def _format_excel_with_styles(self,df, output_path, sheet_name='评审结果', start_row=1, start_col=1):
        """
        将DataFrame写入Excel并设置样式

        Args:
            df: 要写入的DataFrame
            output_path: 输出文件路径
            sheet_name: 工作表名称
            start_row: 开始行（从1开始）
            start_col: 开始列（从1开始）

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 写入数据
                df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=start_row, startcol=start_col)

                # 获取工作表对象
                worksheet = writer.sheets[sheet_name]

                # 定义浅蓝色填充
                light_blue_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
                light_red_fill = PatternFill(start_color="FA0000", end_color="FA0000", fill_type="solid")
                light_green_fill = PatternFill(start_color="2AFA00", end_color="2AFA00", fill_type="solid")
                # 设置表头样式
                header_cells = ['B2', 'C2', 'D2',"E2","F2","G2","H2","I2","J2"]  # 成果物、可否review、理由
                for cell_ref in header_cells:
                    cell = worksheet[cell_ref]
                    cell.fill = light_blue_fill

                #sheet名添加超链接
                # 定义字体样式（红色，单下划线）
                font = Font(color="0000FF", underline="single")
                for row in range(3, len(df) + 3):  # 从第2行开始（包括表头和数据行）
                    col = 5
                    cell = worksheet.cell(row=row, column=col)# B、C、D、E列
                    file_path = worksheet.cell(row=row, column=col-3).value+r"/"+worksheet.cell(row=row, column=col-2).value
                    if cell.value =="-":
                        cell.hyperlink = r"file:///"+f"file:///{file_path}"
                    else:
                        cell.hyperlink = r"file:///"+f"file:///{file_path}#{cell.value}!A1"
                    cell.font = font


                #遍历所有单元格并设置背景色
                for row in worksheet.iter_rows():
                    for cell in row:
                        if str(cell.value) == f"OK":
                            cell.fill = light_green_fill
                        elif str(cell.value) == f"NG":
                            cell.fill = light_red_fill

                # 设置所有单元格的自动换行
                for row in range(2, len(df) + 3):  # 从第2行开始（包括表头和数据行）
                    for col in range(2, 6):  # B、C、D、E列
                        cell = worksheet.cell(row=row, column=col)
                        cell.alignment = Alignment(wrap_text=True)


                # 设置边框
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # 为所有数据区域设置边框
                for row in range(2, len(df) + 3):  # 从第2行到数据结束
                    for col in range(2, 11):  # B、C、D列
                        cell = worksheet.cell(row=row, column=col)
                        cell.border = thin_border

                # 调整列宽
                worksheet.column_dimensions['B'].width = 60  # 成果物
                worksheet.column_dimensions['C'].width = 35  # 成果物
                worksheet.column_dimensions['D'].width = 13  # 可否review
                worksheet.column_dimensions['E'].width = 12  # 理由
                worksheet.column_dimensions['F'].width = 18  # 理由
                worksheet.column_dimensions['G'].width = 13  # 理由
                worksheet.column_dimensions['H'].width = 18  # 理由
                worksheet.column_dimensions['I'].width = 18  # 理由
                worksheet.column_dimensions['J'].width = 95  # 理由

            logger.success(f"Excel文件已成功格式化并保存: {output_path}")
            return {
                'success': True,
                'message': f"Excel文件已成功格式化并保存: {output_path}",
                'output_path': output_path
            }

        except Exception as e:
            error_msg = f"格式化Excel文件时出现错误: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': True
            }

    def check_document(filepath):
        return None

    check_function = {
        'ベースソフトの選定': check_document,
        '使用予定OSSピックアップ 【OSS管理プロセス】 ': check_document,
        'DRBFMの実施': check_document,
        '人命と安全に関わるFTAの実施': check_document,
        '機能一覧と新規・変更内容 （DR・QA_MD）作成': check_document,
        'ソフトウェア構造図作成（DR・QA_MD構造、システム他）': check_document,
        '他車種、標準ソフト、マイコン問題点一覧': check_document,
        '機能別設計・評価方針＆実施結果(DR･QA_MD)': basic_design.check_func_design_evaluation,
        'ソフトウェア 設計基準CS(基本設計)': basic_design.check_design_cs_basic,
        '通信フェイルセーフCS': basic_design.check_signal_failsafe,
        'ソフトウェア設計書作成（I/F含む）': basic_design.check_domain_if_spec,
        '要件⇔ソフトウェア設計書照合実施': basic_design.check_software_design_book_function_specification_book_2_2,

        'I/F整合性確認': detail_design.check_if_cs,
        'CAN入出力一覧確認': detail_design.check_can_output,
        '関数仕様書作成': detail_design.check_func_book,
        'RAM設計書作成': detail_design.check_ram_book,
        'ソフトウェア設計書 ⇔ 関数仕様書照合実施': detail_design.check_software_design_book_function_specification_book_2_5,
        'ソフトウェア 設計基準CS（詳細設計）': detail_design.check_design_cs_basic,
        '最近の不具合横にらみ（設計）': detail_design.check_recent_bug_points,

        '機能一覧と新規・変更内容 （DR・QA_MD）更新': coding_design.check_drqamd,
        'コーディング基準確認＆結果検証': detail_design.check_design_cs_basic,
        'QAC解析実施＆結果検証': coding_design.check_qac,
        'CodeSonar解析実施&検証結果': coding_design.check_codesonar,
        'ファイル比較実施（比較結果と変更内容の確認）': coding_design.check_file_compare,
        'セルフチェック＆コードレビュー観点CS': coding_design.check_viewcs,
        '開発環境設定手順書確認（Softune等）': check_document,
        '変更点ICEテスト実施＆結果検証': check_document,
        'コンパイラ最適化検証(割り込み禁止区間ASM検証)': check_document,
        'RAM干渉チェック実施＆結果検証': coding_design.check_ram_interference,
        '設計文書　⇔　コード　照合実施＆結果検証': coding_design.check_design_book_code_match,

    }









if __name__ == "__main__":
    # 测试设计评审结果函数
    # test_path = "C:/DEV/730/dnktsvn730/Roc_Doc/01REQ/0103Spec/DEV_Agent/0730V字成果物/2.5详细设计"
    # test_path2= "C:/DEV/730/7.11/7.11test"
    # result = check_design_review_result(test_path2)
    # result = check_can_output("C:/DEV/test/【MM連携】検査_(CAN入出力消込)178D_MM-SUB-BUS_MM連携シグナル確認_7inch_178D840B893B_量確.xlsm")
    # result= check_func_book("C:/DEV/730/7.11/7.11test/面企画_1A_DrvInd_関数仕様書_v1.0.0.xlsm")
    # result = check_if_cs("C:/DEV/730/7.11/生成成果物/IF整合性CS兼結果報告書_20250711_155228.xlsx")
    # result = check_ram_book("C:/DEV/test/RAM設計書_Warning(MET19PFV3-34734) .xls")
    # result = check_design_cs("C:/DEV/test/ソフトウェア 設計基準CS.xlsm")
    # result = check_viewcs("C:/DEV/730/7.18/セルフチェック＆コードレビュー観点CS/【新模板】セルフチェック＆コードレビュー観点CS_32478.xlsx")
    # result = check_file_compare("C:/DEV/730/7.18/ファイル比較実施（比較結果と変更内容の確認）/No.3_【19PFv3】コード差分_cr7_app_global_f0228fe803f22da9fdb9f92c965cc16107374fc5.xlsx")
    # result = check_drqamd("C:/DEV/730/7.18/機能一覧と新規・変更内容 （DR・QA_MD）更新/機能一覧と新規・変更内容(new).xlsx")
    # result = check_qac("C:/DEV/730/7.18/QAC解析実施＆結果検証/DNKT_QACResult_QAC_result_detail_CR7_APP.xlsm")
    # result = check_codesonar("C:/DEV/730/7.18/CodeSonar解析実施&検証結果/DNKT_19PFv3_CR7_CS.xlsm")
    # result = check_design_review_result_3_3("C:/DEV/730/7.18/新建文件夹/test")
    # result = check_recent_bug_points("C:/DEV/730/7.11/7.11开催/最近の不具合点検ポイント-△20.xlsx")
    # result = check_software_design_book_function_specification_book("C:/DEV/730/7.11/7.11开催/ソフトウェア設計書⇔関数仕様書照合実施.xlsx")
    # result = check_ram_interference("C:/DEV/730/7.18/新建文件夹/RAM干渉チェック実施＆結果検証/RAM干涉.xlsx")
    # result = check_design_book_code_match("C:/DEV/730/7.18/新建文件夹/設計文書　⇔　コード　照合実施＆結果検証/設計文書　⇔　コード　照合実施＆結果検証.xlsx")
    workflow = Design_PeerreviewWorkflow()
    input_data = BasicDesignInput(dir_path=r"D:/caijunwei/code/git/AI/测试/QAC_codesonar/DNKT_CPPResult_MET.xlsm",peer_review_type = 0x00)
    result = workflow.run(input_data)
    print(result)
    pass