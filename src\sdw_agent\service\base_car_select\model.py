"""
@File    : model.py.py
@Time    : 2025/7/28 16:21
<AUTHOR> qiliang.zhou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 base 车辆选定
@Desc    : 数据模型定义
"""
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator


class ConfigModel(BaseModel):
    """工作流配置模型"""
    name: str = Field("通信故障安全CS工作流", description="工作流名称")
    description: str = Field("分析代码变更中的CAN信号匹配", description="工作流描述")
    version: str = Field("1.0.0", description="版本号")
    author: str = Field("qiliang_zhou", description="作者")


class InputDataModel(BaseModel):
    """工作流输入数据模型"""
    base_db: str = Field(None, description="base车辆的式样DB要件路径")
    new_db: str = Field(None, description="new车辆的式样DB要件路径")
    new_car_name: str = Field(..., description="new车辆名称")
    base_car_name: Optional[str] = Field(None, description="base车辆名称")


class CarDocInfoModel:
    def __init__(self):
        self.car_name: str = ""
        self.version: str = ""
        self.doc_name: List[str] = []
        self.doc_no: List[str] = []

class SelectedCarInfo:
    def __init__(self, input_data: InputDataModel):
        self.new_car_version = input_data.new_car_name.split('$')[0] if '$' in input_data.new_car_name else ""
        self.new_car_name = input_data.new_car_name.split('$')[1] if '$' in input_data.new_car_name else input_data.new_car_name
        if hasattr(input_data, 'base_car_name') and input_data.base_car_name:
            self.base_car_version = input_data.base_car_name.split('$')[0] if '$' in input_data.base_car_name else ""
            self.base_car_name = input_data.base_car_name.split('$')[1] if '$' in input_data.base_car_name else input_data.base_car_name

