import glob
import os

import pandas as pd
import xlwings as xw
from langchain_core.messages import AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries, column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.styles import PatternFill, Border, Side, Alignment
from pydantic import BaseModel, Field

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
import difflib
from pathlib import Path
import sdw_agent.service.design_peerreview.common_cs as common_cs
from sdw_agent.service.design_peerreview.models import CheckRangeIninfo

def check_software_design_book_function_specification_book_2_2(file_path):
    # 检查空白和NG内容
    ng_results = {}
    check_ng_sheets_info = {
        "設計ID一覧": {"range_address": ["D4:F4"],
                       "check_direction": 'down',
                       "check_direction_enable": True,
                       "range_not_included": []},
        "要件⇔ソフトウェア設計書照合": {"range_address": ["D6:J6"],
                                        "check_direction": 'down',
                                        "check_direction_enable": True,
                                        "range_not_included": []}
    }
    check_info = CheckRangeIninfo()
    for sheet_name, check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        check_info.range_not_included = check_info_value["range_not_included"]
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = common_cs.check_blank_ng_na_xls(file_path, sheet_name, check_info, full_word_matching=True)
            ng_results = common_cs.mergeDict(ng_results, ng_result)

    return ng_results


def check_design_cs_basic(file_path):
    return common_cs.check_cs_design_conditions(file_path)

def check_signal_failsafe(file_path):
    try:
        stamp_results = {}
        ng_results = {}
        stamp_sheets_name = {"通信フェイルセーフＣＳ": ["E7:G10"]}
        for sheet_name, stamp_ranges in stamp_sheets_name.items():
            for addr in stamp_ranges:
                stamp_result = common_cs.check_stamp(file_path, sheet_name,addr)
                stamp_results = common_cs.mergeDict(stamp_results, stamp_result)

        check_ng_sheets_info = {
            "通信フェイルセーフＣＳ": {"range_address": ["F22:G24","F28:G30","F34:G38","F42:G47","F51:G53","F57:G61","F65:G66"],
                                                    "check_direction": 'down',
                                                    "check_direction_enable": False,
                                                    "range_not_included": []}
        }
        check_info = CheckRangeIninfo()
        for sheet_name, check_info_value in check_ng_sheets_info.items():
            range_addresses = check_info_value["range_address"]
            check_info.check_direction = check_info_value["check_direction"]
            check_info.check_direction_enable = check_info_value["check_direction_enable"]
            check_info.range_not_included = check_info_value["range_not_included"]
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info, check_ng_flag=False)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
            error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
            logger.error(error_msg)


def check_domain_if_spec(file_path):
    try:
        stamp_results = {}
        ng_results = {}

        check_ng_sheets_info = {
            "IF一覧": {"range_address": ["C5:C5"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": []}
        }
        next_if_name = {
            "ドメイン外公開I/F":"ドメイン内公開I/F",
            "ドメイン内公開I/F":"参照I/F",
            "参照I/F":"ドメイン外公開I/F"
        }

        #檢測單元格填寫是否有空白或者NG
        check_info = CheckRangeIninfo()
        row_idx = 0
        for func_sheet_name, check_info_value in check_ng_sheets_info.items():
            range_addresses = check_info_value["range_address"]
            #获取 检测范围 F:AC
            if_names = common_cs.get_excel_columns_data(file_path, func_sheet_name, None, data_by_col_idx=5)
            #{表达式 for 变量 in 列表 if 条件}
            if_names_info = {if_idx:if_name for if_idx,if_name in enumerate(if_names) if if_name in ["ドメイン外公開I/F","ドメイン内公開I/F","参照I/F"]}
            for if_idx,if_name  in if_names_info.items():
                if if_name == "ドメイン外公開I/F":
                    check_addr = "D" + str(if_idx) + ":D" + str(if_idx)
                    range_addresses.append(check_addr)

                end_idx = 0
                if next_if_name[if_name] in if_names[if_idx:]:
                    end_idx = if_names.index(next_if_name[if_name],if_idx,len(if_names))
                else:
                    end_idx = len(if_names)
                check_addr = "F"+str(if_idx+1)+":AC"+str(end_idx)
                range_addresses.append(check_addr)
                check_info_value["range_not_included"].append(str(if_idx+1)+":"+str(if_idx+1))
            for range_addr in range_addresses:
                check_info.range_address = range_addr
                check_info.check_direction = check_info_value["check_direction"]
                check_info.range_not_included = check_info_value["range_not_included"]
                check_info.check_direction_enable = check_info_value["check_direction_enable"]
                ng_result = common_cs.check_blank_ng_na_xls(file_path, func_sheet_name, check_info,full_word_matching=True,check_direction=False)
                ng_results = common_cs.mergeDict(ng_results, ng_result)

            #檢測填写内容
            #todo

        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)


def check_func_design_evaluation(file_path):
    try:
        stamp_results = {}
        ng_results = {}
        check_ng_sheets_info = {
            "設計・評価方針": {"range_address": ["C5:N5"],
                     "check_direction": 'down',
                     "check_direction_enable": True,
                     "range_not_included": []}
        }

        #檢測單元格填寫是否有空白或者NG
        check_info = CheckRangeIninfo()
        sheet_names = common_cs.get_excel_sheet_names_data(file_path)
        row_idx = 0
        for func_sheet_name, check_info_value in check_ng_sheets_info.items():
            for sheet_name in sheet_names:
                if func_sheet_name in sheet_name:
                    range_addresses = check_info_value["range_address"]
                    for range_addr in range_addresses:
                        check_info.range_address = range_addr
                        check_info.check_direction = check_info_value["check_direction"]
                        check_info.range_not_included = check_info_value["range_not_included"]
                        check_info.check_direction_enable = check_info_value["check_direction_enable"]
                        ng_result = common_cs.check_blank_ng_na(file_path, sheet_name, check_info)
                        ng_results = common_cs.mergeDict(ng_results, ng_result)

            #檢測填写内容
            #todo

        return common_cs.mergeDict(ng_results,stamp_results)
    except Exception as e:
        error_msg = f"Sheet '{file_path}' 检查过程中出现错误: {e}"
        logger.error(error_msg)