#!/usr/bin/env python3
"""
导出原始 Draw.io 图像的统一入口
提供多种导出方式选择
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.service.template_manager import template_manager


def export_original_drawio_image(drawio_file: Optional[str] = None,
                                excel_file: str = "output/architecture_diagram.xlsx",
                                method: str = "auto",
                                title: Optional[str] = None,
                                scale: float = 2.0,
                                max_width: int = 1400,
                                max_height: int = 1000) -> Dict[str, Any]:
    """
    导出原始 Draw.io 图像到 Excel
    
    Args:
        drawio_file: Draw.io 文件路径，如果为None则使用模板管理器
        excel_file: Excel 输出文件路径
        method: 导出方法 ("auto", "desktop", "online", "manual")
        title: 图表标题
        scale: 缩放比例
        max_width: 图像最大宽度
        max_height: 图像最大高度
        
    Returns:
        操作结果
    """
    
    # 1. 确定 Draw.io 文件路径
    if drawio_file is None:
        drawio_file = template_manager.get_template_path("block_diagram_file")
        if not drawio_file:
            return {
                "success": False,
                "message": "未找到 Draw.io 模板文件",
                "method": method
            }
    
    if not Path(drawio_file).exists():
        return {
            "success": False,
            "message": f"Draw.io 文件不存在: {drawio_file}",
            "method": method
        }
    
    logger.info(f"使用 Draw.io 文件: {drawio_file}")
    
    # 2. 根据方法选择导出方式
    if method == "auto":
        # 自动选择最佳方法
        method = _choose_best_method()
        logger.info(f"自动选择导出方法: {method}")
    
    # 3. 执行导出
    if method == "desktop":
        return _export_via_desktop(drawio_file, excel_file, title, scale, max_width, max_height)
    elif method == "online":
        return _export_via_online(drawio_file, excel_file, title, scale, max_width, max_height)
    elif method == "manual":
        return _export_via_manual(excel_file, title, max_width, max_height)
    else:
        return {
            "success": False,
            "message": f"不支持的导出方法: {method}",
            "method": method
        }


def _choose_best_method() -> str:
    """自动选择最佳的导出方法"""

    # 1. 检查是否有 draw.io desktop
    from sdw_agent.util.drawio_util.export_original_image import find_drawio_executable
    if find_drawio_executable():
        return "desktop"

    # 2. 检查网络连接（简单测试）
    try:
        import requests
        response = requests.get("https://app.diagrams.net", timeout=5)
        if response.status_code == 200:
            return "online"
    except:
        pass

    # 3. 回退到手动方式
    return "manual"


def _export_via_desktop(drawio_file: str, excel_file: str, title: Optional[str],
                       scale: float, max_width: int, max_height: int) -> Dict[str, Any]:
    """使用 draw.io desktop 导出"""
    try:
        from sdw_agent.util.drawio_util.export_original_image import insert_original_drawio_to_excel

        result = insert_original_drawio_to_excel(
            drawio_file=drawio_file,
            excel_file=excel_file,
            sheet_name="架构图",
            title=title or "系统架构图",
            scale=scale,
            max_width=max_width,
            max_height=max_height
        )
        result["method"] = "desktop"
        return result

    except Exception as e:
        return {
            "success": False,
            "message": f"Desktop 导出失败: {str(e)}",
            "method": "desktop"
        }


def _export_via_online(drawio_file: str, excel_file: str, title: Optional[str],
                      scale: float, max_width: int, max_height: int) -> Dict[str, Any]:
    """使用在线服务导出"""
    try:
        from sdw_agent.util.drawio_util.online_export import insert_online_exported_drawio_to_excel

        result = insert_online_exported_drawio_to_excel(
            drawio_file=drawio_file,
            excel_file=excel_file,
            sheet_name="架构图",
            title=title or "系统架构图",
            export_method="online",
            scale=scale,
            max_width=max_width,
            max_height=max_height
        )
        result["method"] = "online"
        return result

    except Exception as e:
        return {
            "success": False,
            "message": f"在线导出失败: {str(e)}",
            "method": "online"
        }


def _export_via_manual(excel_file: str, title: Optional[str],
                      max_width: int, max_height: int) -> Dict[str, Any]:
    """使用手动截图方式"""
    try:
        # 检查是否有手动截图
        screenshot_path = Path("output/manual_screenshot.png")
        if screenshot_path.exists():
            from sdw_agent.util.drawio_util.manual_image_insert import insert_manual_image_to_excel

            result = insert_manual_image_to_excel(
                image_file=str(screenshot_path),
                excel_file=excel_file,
                sheet_name="架构图",
                title=title or "系统架构图",
                max_width=max_width,
                max_height=max_height
            )
            result["method"] = "manual"
            return result
        else:
            return {
                "success": False,
                "message": f"手动截图文件不存在: {screenshot_path}。请先创建截图文件。",
                "method": "manual",
                "instructions": [
                    "1. 在 draw.io 中打开你的文件",
                    "2. 导出为 PNG 图像（File -> Export as -> PNG）",
                    "3. 将图像保存到 output/manual_screenshot.png",
                    "4. 重新运行此脚本"
                ]
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"手动导出失败: {str(e)}",
            "method": "manual"
        }


def demo_all_methods():
    """演示所有导出方法"""
    print("🎯 Draw.io 原始图像导出演示")
    print("=" * 60)
    
    # 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用 Draw.io 文件: {drawio_file}")
    print()
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 测试所有方法
    methods = ["desktop", "online", "manual"]
    
    for method in methods:
        print(f"🔄 测试方法: {method}")
        print("-" * 40)
        
        excel_file = output_dir / f"architecture_diagram_{method}.xlsx"
        
        result = export_original_drawio_image(
            drawio_file=drawio_file,
            excel_file=str(excel_file),
            method=method,
            title=f"系统架构图 ({method.upper()} 方法)",
            scale=2.0,
            max_width=1400,
            max_height=1000
        )
        
        print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   消息: {result['message']}")
        
        if result['success']:
            print(f"   文件: {result['excel_file']}")
        elif 'instructions' in result:
            print("   操作步骤:")
            for instruction in result['instructions']:
                print(f"     {instruction}")
        
        print()
    
    print("🎉 测试完成！请查看 output 目录中的 Excel 文件。")


def quick_export():
    """快速导出（自动选择最佳方法）"""
    print("🚀 快速导出 Draw.io 原始图像")
    print("=" * 40)
    
    result = export_original_drawio_image(
        method="auto",
        title="系统架构图",
        excel_file="output/quick_architecture_diagram.xlsx"
    )
    
    print(f"📊 导出结果:")
    print(f"   方法: {result.get('method', 'unknown')}")
    print(f"   成功: {'✅' if result['success'] else '❌'}")
    print(f"   消息: {result['message']}")
    
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result.get('sheet_name', 'N/A')}")
        print(f"\n🎉 请打开 Excel 文件查看原始图表！")
    elif 'instructions' in result:
        print("\n📋 操作步骤:")
        for instruction in result['instructions']:
            print(f"   {instruction}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_export()
    else:
        demo_all_methods()
