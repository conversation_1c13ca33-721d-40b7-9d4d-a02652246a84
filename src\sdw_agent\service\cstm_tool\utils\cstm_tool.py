import json
import re
from copy import deepcopy

import openpyxl
import traceback
import copy
from typing import List, Dict, Any
from fastapi import HTTPException
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import AIMessage
from loguru import logger

from sdw_agent.service.cstm_tool.utils.config_manager import config_manager
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.cstm_tool.model import MacroDefFormat, ScreenHierarchyItem
from sdw_agent.service.cstm_tool.utils.cstm_excel_util import CSTMExcelUtil
from sdw_agent.service.cstm_tool.utils.cstm_extract_table import extract_get_set_cnt_table, if_need_special_handle
from sdw_agent.service.cstm_tool.utils.cstm_utils import CstmUtils

logger.bind(name="CSTM_TOOL")


def set_item_position(cstm_data: list) -> list:
    """
    重新排列cstm_data列表中的元素位置
    排序规则:
    1. is_cntt=True 且 type='append' 的元素优先级最高
    2. is_cntt=True 且 type='update' 的元素优先级次之
    3. 其他元素保持原有顺序

    :param cstm_data: 包含字典元素的列表
    :return: 重新排序后的列表
    """
    if not cstm_data or not isinstance(cstm_data, list):
        return cstm_data or []

    # 分类存储不同优先级的元素
    priority_1 = []  # is_cntt=True 且 type='append'
    priority_2 = []  # is_cntt=True 且 type='update'
    others = []      # 其他元素

    for item in cstm_data:
        if not isinstance(item, dict):
            others.append(item)
            continue

        is_cntt = item.get('is_cntt', False)
        item_type = item.get('type', '')

        if is_cntt and item_type == 'append':
            priority_1.append(item)
        elif is_cntt and item_type == 'update':
            priority_2.append(item)
        else:
            others.append(item)

    # 按优先级合并列表
    result = priority_1 + priority_2 + others
    return result


def sanitize_filename(text: str, max_length: int = 50) -> str:
    """
    将文本处理为适合作为文件名的格式

    :param text: 原始文本
    :param max_length: 最大长度，默认50个字符
    :return: 处理后的文件名
    """
    if not text or not isinstance(text, str):
        return "unnamed"

    # 定义不允许在文件名中使用的特殊字符
    invalid_chars = r'[<>:"/\\|?*\n\r\t\x00-\x1f]'

    # 去除或替换特殊字符
    sanitized = re.sub(invalid_chars, '', text)

    # 去除多余的空格并用下划线替换剩余空格
    sanitized = re.sub(r'\s+', '_', sanitized.strip())

    # 去除开头和结尾的点号和下划线（Windows文件名限制）
    sanitized = sanitized.strip('._')

    # 如果处理后为空，使用默认名称
    if not sanitized:
        return "unnamed"

    # 处理长度限制
    if len(sanitized) > max_length:
        # 计算前后保留的字符数
        prefix_length = (max_length - 3) // 2  # 减去3是为了...
        suffix_length = max_length - 3 - prefix_length

        # 确保在字符边界处截断（避免截断多字节字符）
        prefix = sanitized[:prefix_length]
        suffix = sanitized[-suffix_length:] if suffix_length > 0 else ""

        sanitized = f"{prefix}...{suffix}"

    return sanitized


# 方案一: 构造类来表示Excel表格中的数据
class CusOptInfo:
    # 表示选项信息
    def __init__(self):
        self.CusOptNum = 0  # H列: 每一个子选项id号， 从0开始
        self.OptLabel = ""  # I列: 选项标签，用于显示
        self.OptDef = ""  # J列: 选项定义，用于生成宏定义名称
        self.OptType = ""  # K列: 选项类型
        self.OptOrder = ""  # L列: 选项顺序，用于计算支持位值，值n对应2^(n-1)
        self.OptSupbit = ""  # M列: 支持位顺序，用于确定支持位的生成顺序

        # 下一个内容相关的字段
        self.NextCnttLabel = ""  # N列: 下一个内容的标签
        self.NextCnttDef = ""  # O列: 下一个内容的定义
        self.NextCnttSpbEnb = ""  # P列: 下一个内容的使能标志

        # 生成函数相关的字段
        self.CnttGetCon = ""  # S列: 获取内容的函数
        self.CnttSetCon = ""  # T列: 设置内容的函数
        self.CnttCancel = ""  # U列: 取消函数接口
        self.CnttGetConvert = ""  # V列: 获取设定值数量
        self.CnttSetConvert = ""  # W列: 发送设定值数量
        self.CnttTableGet = ""  # X列: 获取设定值表
        self.CnttTableSet = ""  # Y列: 发送设定值表

        # 自定义的字段
        self.row_index = -1  # 记录当前行的索引
        self.has_next_cntt = False  # 是否存在下一个内容

class CusOptCnttInfo:
    # 1-6阶层主数据结构，包含内容和选项的综合信息
    def __init__(self):
        self.Level = ""  # B列: 阶层
        self.Label = ""  # C列: 画面名称
        self.Definit = ""  # D列: 宏定义
        self.Main = ""  # E列: 上一阶层画面ID
        self.Secondary = ""  # F列: 上一阶层位置
        self.Position = ""  # G列: 第0阶层位置
        # self.parent_label = ""  # 添加父级标签属性，用于处理车辆支持位
        self.used_supbits = []  # 已使用的支持位集合

        self.link_type = ""  # Q列: 链接类型
        self.eco_wallet = ""  # R列: ECOWALLET

        # self.CusOptInfo = [CusOptInfo() for _ in range(50)]  # 预分配50个选项
        self.cus_opt_info_list = []  # 存储CusOptInfo的列表

        self.lines_num = 0  # 存储行数, 如果有add的这里会加1

    def get_next_available_supbit(self):
        """获取下一个可用的支持位"""
        # 过滤掉0值，从1开始计算
        opt_supbit_list = [x for x in self.used_supbits if x > 0]
        opt_supbit_list.sort()
        new_opt_supbit = 1
        if len(opt_supbit_list) != 0:
            for i in range(1, len(opt_supbit_list) + 1):
                if i not in opt_supbit_list:
                    new_opt_supbit = i
                    break
            if new_opt_supbit == 1:
                new_opt_supbit = opt_supbit_list[-1] + 1
        self.used_supbits.append(new_opt_supbit)
        return new_opt_supbit

    def release_supbit(self, supbit):
        """释放一个支持位"""
        try:
            supbit_value = int(supbit)
            if supbit_value in self.used_supbits:
                self.used_supbits.remove(supbit_value)
        except (ValueError, TypeError) as e:
            # traceback.print_exc()
            logger.error(f"释放支持位失败，支持位不是整数: {traceback.format_exc()}")
            raise ValueError(
                # f"{gettext('画面释放支持位失败:')}{self.Label}','{gettext('支持位不是整数:')}{supbit}"
                f"画面释放支持位失败:{self.Label}, 支持位不是整数:{supbit}"
            )


def get_few_shot(change_before_cstm: str, label_col_idx, definition_col_idx):
    '''
    用于生成宏定义函数名称的fewshot
    :param change_before_cstm:
    :param label_col_idx:
    :param definition_col_idx:
    :return:
    '''
    workbook = openpyxl.load_workbook(change_before_cstm, data_only=True)
    input_worksheet = workbook["InputData"]
    unique_pairs = []
    for row in range(4, input_worksheet.max_row + 1):
        i_col_value = input_worksheet.cell(row=row, column=label_col_idx).value  # I列
        j_col_value = input_worksheet.cell(row=row, column=definition_col_idx).value  # J列

        # 检查是否遇到结束标记
        if i_col_value == 'end' and j_col_value == 'end':
            break

        if i_col_value and j_col_value:  # 确保两个值都不为空
            pair = [str(i_col_value), str(j_col_value)]
            if pair not in unique_pairs:  # 检查是否已存在
                unique_pairs.append(pair)

    return unique_pairs

def generate_unique_name(result, exist_name):
    '''
    为了保证宏定义函数名称的唯一性，如果已经存在该函数名称，则添加版本号尾缀
    :param result:
    :param exist_name:
    :return:
    '''
    # 找到所有以 result 开头的名字
    matching_names = [name for name in exist_name if name == result or str(name).startswith(f"{result}_")]

    # 提取尾缀数字
    suffix_numbers = []
    for name in matching_names:
        match = re.match(f"{result}_(\d+)$", name)
        if match:
            suffix_numbers.append(int(match.group(1)))

    # 如果存在尾缀数字，从中找到最大的数字
    if suffix_numbers:
        max_suffix = max(suffix_numbers)
    elif len(matching_names)==1 and not suffix_numbers:
        max_suffix = 1
    else:
        max_suffix = 0

    # 生成新的名字
    if max_suffix==0:
        return f"{result}"
    else:
        return f"{result}_{max_suffix + 1}"

def generate_unique_func_name(label_name: str, change_before_cstm:str):
    '''
    调用大模型生成宏定义函数名称
    :param label_name: 变更模块的名字
    :param change_before_cstm 变更前cstm配置表路径
    :return:
    '''
    try:
        unique_pairs = get_few_shot(change_before_cstm, 9, 10)

        # 得到few_shot
        header = "| " + " | ".join(['模块名', '宏定义函数名']) + " |"
        separator = "| " + " | ".join(["---"] * 2) + " |"
        few_shot = []
        for pair in unique_pairs[:50]:
            few_shot.append("| " + " | ".join(pair) + " |")
        few_shot_prompt = "\n".join([header, separator] + few_shot)

        template = ChatPromptTemplate(
            [
                ("user", config_manager.get('prompt.generate_cstm_tool_prompts.generate_definition_prompt'))
            ],
            template_format="mustache",
        )

        resp: AIMessage = get_ai_message_with_structured_output(
            template,
            {"few_shot": few_shot_prompt, "label": label_name},
            MacroDefFormat,
            llm_model=None
        )

        result = resp.result
        # 确保生成的宏定义函数名称是在cstm配置工具中是唯一的
        exist_name = []
        workbook = openpyxl.load_workbook(change_before_cstm, data_only=True)
        input_worksheet = workbook["InputData"]
        for row in range(4, input_worksheet.max_row + 1):
            exist_name.append(input_worksheet.cell(row=row, column=10).value)

        result = generate_unique_name(result, exist_name)
        logger.info(f"生成的宏定义函数名为:{result}")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

    return result

def format_definition_name(input_string):
    """
    格式化字符串：第一个字母大写，其他字母小写，去掉下划线。

    :param input_string: 传入的字符串
    :return: 格式化后的字符串
    """
    # 去掉下划线
    cleaned_string = input_string.replace("_", "").replace(" ", "")
    # 第一个字母大写，其他字母小写
    formatted_string = cleaned_string.capitalize()
    return formatted_string

def convert_special_to_half_width(s):
    """
    将字符串中的特殊字符（如罗马数字）转换为对应的普通半角字符。
    """
    # 定义特殊字符的映射表
    roman_to_ascii = {
        "Ⅰ": "I",
        "Ⅱ": "II",
        "Ⅲ": "III",
        "Ⅳ": "IV",
        "Ⅴ": "V",
        "Ⅵ": "VI",
        "Ⅶ": "VII",
        "Ⅷ": "VIII",
        "Ⅸ": "IX",
        "Ⅹ": "X",
        "Ⅺ": "XI",
        "Ⅻ": "XII",
    }

    result = ""
    for char in s:
        if char in roman_to_ascii:
            result += roman_to_ascii[char]  # 替换为对应的普通字符
        else:
            result += char  # 非特殊字符保持不变
    return result

def fill_curent_item_info(cus_opt_info: CusOptInfo, change_before_cstm:str, item_dict: ScreenHierarchyItem, type:str = 'append'):
    '''
    填充当前模块的meta info
    :param cus_opt_info:
    :param item_dict:
    :return:
    '''
    if cus_opt_info.OptLabel.strip()=="" or cus_opt_info.OptLabel.strip() != item_dict.item_name.strip():
        # 当cus_opt_info.OptLabel为空 --对应新增场景
        # 如果cus_opt_info.OptLabel.strip() == item_dict.item_name.strip() 则表明已经存在不用调用大模型生成
        cus_opt_info.OptLabel = item_dict.item_name  # I 列
        if type != 'update':
            # 如果是选项文言变更，画面文言变更 则不需要调用大模型重新生成宏定义 保持原来的
            cus_opt_info.OptDef = generate_unique_func_name(item_dict.item_name, change_before_cstm)  # J 列

    # 模块类型映射关系
    item_type_dict = {
        "AI": "A",
        "AII": "A",
        "AI/AII": "A",
        "AIII-1": "A_0",
        "AIII-2": "A_0",
        "BI": "B",
        "B_2": "B_2",
        "CI": "C",
        "CII": "NON",
        "D": "D",
        "F": "D",
        "G": "G",
        "⇒": "NON",
        "-": "NON"
    }
    # 原始式样书中的切替Type可能包含特殊字符，将特殊字符进行转换
    transformed_str = convert_special_to_half_width(item_dict.switch_type.split('\n')[-1])
    if cus_opt_info.OptType=="" or cus_opt_info.OptType != item_type_dict.get(transformed_str):
        cus_opt_info.OptType = item_type_dict.get(transformed_str)  # K列
    return cus_opt_info


def fill_next_item_infp(cus_opt_info: CusOptInfo, item_dict: ScreenHierarchyItem, req_list: List[ScreenHierarchyItem]):
    '''
    填充下一层级模块的meta 信息
    :param cus_opt_info:
    :param item_dict:
    :param req_list:
    :return:
    '''
    # 判断当前模块是否有下一阶层信息
    next_items = []

    # 获取 当前item_name 所在行的索引
    target_index = 0
    for idx in range(len(req_list)):
        if item_dict.item_name==req_list[idx].item_name and item_dict.hierarchy_level==req_list[idx].hierarchy_level:
            target_index = idx


    if item_dict:  # 确保找到了目标行
        # 获取 当前item_name 所在行的 hierarchy_level 值
        target_hierarchy_level = item_dict.hierarchy_level

        # 筛选出 当前item_name 所在行后面的行
        filtered_list = req_list[target_index + 1:]

        # 进一步筛选 hierarchy_level 列不同于目标值的行
        next_items = [item for item in filtered_list if item.hierarchy_level!=target_hierarchy_level]

    if next_items:
        cus_opt_info.NextCnttLabel = next_items[0].title  # N列
        cus_opt_info.NextCnttDef = cus_opt_info.OptDef # O列
    else:
        cus_opt_info.NextCnttLabel = '-'  # N列
        cus_opt_info.NextCnttDef = 'ERROR'  # O列

    cus_opt_info.NextCnttSpbEnb = 'SPD_OPRT_FALSE' if item_dict.operable_while_driving == '否' else 'SPD_OPRT_TRUE'  # P列
    return cus_opt_info

def is_null_cell_condition(cus_opt_info: CusOptInfo, item_dict: ScreenHierarchyItem, get_tbl_data, is_special_handle: bool):

    # 定义常量
    # KEY_ON_OFF_DISPLAY = 'on_off_display'
    # KEY_ITEM_DESC = 'item_description'
    # STR_FUNC_ON_OFF = "機能 ON/OFF"
    # STR_KETEI = "決定"

    # 提取并设置默认值
    # on_off_display = item_dict.get(KEY_ON_OFF_DISPLAY, '')
    # item_desc = item_dict.get(KEY_ITEM_DESC, '')
    on_off_display = item_dict.on_off_display
    item_desc = item_dict.item_description


    is_proj_no_switch =  on_off_display is None or on_off_display.strip() == '' or on_off_display == '-'
    is_proj_add_no_switch = item_desc is None or item_desc.strip() in ['', '-']
    is_no_out_info = get_tbl_data is not None and len(get_tbl_data) == 0
    is_no_special_handle = not is_special_handle
    is_no_valid_trans_style = cus_opt_info.OptType == "NON"

    is_null = (is_proj_no_switch and is_proj_add_no_switch and is_no_out_info and is_no_special_handle
               and is_no_valid_trans_style)

    return is_null


def fill_get_func_content(cus_opt_info: CusOptInfo, item_dict: ScreenHierarchyItem, get_tbl_data, is_special_handle: bool):
    """
    填写受信函数单元格
    Func_GetContent  S列
    NumContent_GetConvert V列
    Table_GetContentConvert X列
    """
    # 定义常量
    KEY_ON_OFF_DISPLAY = 'on_off_display'
    KEY_ITEM_DESC = 'item_description'
    STR_FUNC_ON_OFF = "機能 ON/OFF"
    STR_KETEI = "決定"

    # 缓存格式化名称，避免重复计算
    opt_def_name = format_definition_name(cus_opt_info.OptDef)

    # 提取并设置默认值
    on_off_display = item_dict.on_off_display
    item_desc = item_dict.item_description

    highlight_str = f"&u1_g_Ss{opt_def_name}Highlight"
    null_ptr = "(void *)NULL"
    zero_u1 = "(U1)0"
    null_u1 = "(U1 *)NULL"

    # S列赋值
    is_null_cell = is_null_cell_condition(cus_opt_info, item_dict, get_tbl_data, is_special_handle)
    cntt_get_con = "(void *)NULL" if is_null_cell else f"&u1_g_Ss{format_definition_name(cus_opt_info.OptDef)}Highlight"
    cus_opt_info.CnttGetCon = cntt_get_con

    # V列赋值
    if cntt_get_con == null_ptr:
        cntt_get_convert = zero_u1
    else:
        if STR_FUNC_ON_OFF in item_desc:
            if get_tbl_data:
                cntt_get_convert = "(U1)NUMOPT_DMNCFGGNRLGETCNVT"
            else:
                cntt_get_convert = f"(U1)NUMOPT_DMNCFG{opt_def_name.upper()}GETCNVT"
        else:
            cntt_get_convert = f"(U1)NUMOPT_DMNCFG{opt_def_name.upper()}GETCNVT"

    cus_opt_info.CnttGetConvert = cntt_get_convert

    # X列赋值
    if cntt_get_con == null_ptr:
        cntt_tbl_get = null_u1
    else:
        if STR_FUNC_ON_OFF in item_desc:
            if get_tbl_data:
                cntt_tbl_get = "&u1_TBL_DMNCFGSOFTGNRLGETSTS[0]"
            else:
                cntt_tbl_get = f"&u1_TBL_DMNCFG{opt_def_name.upper()}GETSTS[0]"
        else:
            cntt_tbl_get = f"&u1_TBL_DMNCFG{opt_def_name.upper()}GETSTS[0]"

    cus_opt_info.CnttTableGet = cntt_tbl_get

    return cus_opt_info


def fill_set_func_content(cus_opt_info: CusOptInfo, item_dict: ScreenHierarchyItem, set_tbl_data, is_special_handle):
    """
    填写出信函数单元格
    Func_SetContent  T列
    Func_SetCancelContent U列
    NumContent_SetConvert W列
    Table_SetContentConvert Y列
    """
    # 提前获取并安全处理字段内容
    on_off_display = item_dict.on_off_display
    item_desc = item_dict.item_description
    opt_def_name = format_definition_name(cus_opt_info.OptDef)

    # T列
    cntt_set_con = ""
    has_on_off = "機能 ON/OFF" in item_desc
    has_jueding = "決定" in item_desc

    # if not on_off_display or on_off_display == '-':
    #     if has_on_off or has_jueding:
    #         cntt_set_con = f"&vd_g_Ss{opt_def_name}ReqEnter"
    #     else:
    #         if set_tbl_data:
    #             cntt_set_con = f"&vd_g_Ss{opt_def_name}ReqEnter"
    #         else:
    #             if is_special_handle:
    #                 cntt_set_con = f"&vd_g_Ss{opt_def_name}ReqEnter"
    #             else:
    #                 cntt_set_con = "(void *)NULL"
    # else:
    #     cntt_set_con = f"&vd_g_Ss{opt_def_name}ReqEnter"

    is_null_cell = is_null_cell_condition(cus_opt_info, item_dict, set_tbl_data, is_special_handle)
    cntt_set_con = "(void *)NULL" if is_null_cell else  f"&vd_g_Ss{opt_def_name}ReqEnter"
    cus_opt_info.CnttSetCon = cntt_set_con

    # U列
    use_set_func = cus_opt_info.CnttSetCon != "(void *)NULL"
    cntt_cancel = f"&vd_g_Ss{opt_def_name}LesEnter" if use_set_func else "(void *)NULL"
    cus_opt_info.CnttCancel = cntt_cancel

    # W列
    cntt_set_convert = "(U1)0"
    if use_set_func:
        if has_on_off:
            if set_tbl_data:
                cntt_set_convert = "(U1)NUMOPT_DMNCFGGNRLSETCNVT"
                if isinstance(set_tbl_data, (list, tuple)):
                    for row in set_tbl_data:
                        if row and ('not pushed' in row.lower() or 'pushed' in row.lower()):
                            cntt_set_convert = "(U1)NUMOPT_DMNCFGSOFTGNRLSETCNVT"
                            break
            else:
                cntt_set_convert = "(U1)NUMOPT_DMNCFGGNRLSETCNVT"
        else:
            cntt_set_convert = f"(U1)NUMOPT_DMNCFG{opt_def_name.upper()}SETCNVT"
    cus_opt_info.CnttSetConvert = cntt_set_convert

    # Y列
    cntt_tbl_set = "(U1 *)NULL"
    if use_set_func:
        if has_on_off:
            if set_tbl_data:
                cntt_tbl_set = "&u1_TBL_DMNCFGSOFTGNRLSETSTS[0]"
            else:
                cntt_tbl_set = f"&u1_TBL_DMNCFG{opt_def_name.upper()}SETSTS[0]"
        else:
            cntt_tbl_set = f"&u1_TBL_DMNCFG{opt_def_name.upper()}SETSTS[0]"
    cus_opt_info.CnttTableSet = cntt_tbl_set

    return cus_opt_info


def fill_ctsm_info_by_rule(req_list, item_name, item_hierachy_level, cus_opt_info: CusOptInfo, change_before_cstm:str, cur_dir:str) -> CusOptInfo:
    '''
    根据规则填充cstm配置信息
    :param req_list:
    :param item_name:
    :param cus_opt_info:
    :param change_before_cstm: 变更前cstm配置文件路径
    :return:
    '''
    try:
        # 获取配置中的映射字典
        item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

        # 从阶层变更table中获取到当前项所在行
        # filter_rows = [item for item in req_list if item.item_name == item_name]
        if req_list is None:
            raise ValueError(f"新增选项信息解析失败: {item_name}")

        req_list = req_list[1]
        filter_rows = []
        for item in req_list:
            if item.item_name in item_name_map:
                if item_name_map[item.item_name] == item_name and str(item_hierachy_level) == item.hierarchy_level:
                    filter_rows.append(item)
            else:
                if item.item_name == item_name and str(item_hierachy_level) == item.hierarchy_level:
                    filter_rows.append(item)

        item_dict = None
        if filter_rows:
            item_dict = filter_rows[0]

        if not item_dict:
            return cus_opt_info

        # 解析关联式样信息
        linked_spec_prefix = parse_spec_document(item_dict)
        change_name = item_dict.item_name

        # 解析变更后需求文档得到5.2 SW制御信息
        set_tbl_data, get_tbl_data, linked_spec_excel = extract_get_set_cnt_table(linked_spec_prefix, change_name, cur_dir)
        # 判断是否需要特殊处理
        is_special_handle = if_need_special_handle(change_name, linked_spec_excel)

        if item_dict:
            # 填充当前模块的meta info
            cus_opt_info = fill_curent_item_info(cus_opt_info, change_before_cstm, item_dict)
            # 填充下一层级模块的meta info
            cus_opt_info = fill_next_item_infp(cus_opt_info, item_dict, req_list)
            # 填充受信函数
            cus_opt_info = fill_get_func_content(cus_opt_info, item_dict, get_tbl_data, is_special_handle)
            # 填充送信函数
            cus_opt_info = fill_set_func_content(cus_opt_info, item_dict, set_tbl_data, is_special_handle)

        return cus_opt_info
    except Exception as e:
        # traceback.print_exc()
        logger.error(f"模块{item_name} 信息填充错误")
        raise HTTPException(status_code=500, detail=str(e))


def get_change_result(cstm_data_file, objects_pairs, cur_dir, cus_opt_cntt_info_list, change_before_cstm_path: str = "") -> List[Dict[str, Any]]:
    """
    根据json 分析文件得到需要操作的list
    :param cstm_data_file: json 分析文件路径
    :param objects_pairs: 变更前后需求文档的配对信息
    :param cur_dir: 变更后原始式样书所在文件夹路径
    :param cus_opt_cntt_info_list: 外部读取的文件列表内容（CusOptCnttInfo对象列表）
    :param change_before_cstm_path: base cstm 文件路径
    :return: 需要变更的操作信息列表
    """
    try:
        # 读取并解析cstm数据文件
        cstm_data = read_cstm_data(cstm_data_file)
        need_change_opt_info = []

        # 将画面新增，画面更新相关item 前置，避免后续操作画面选项的时候报画面不存在的错误
        cstm_data = set_item_position(cstm_data)

        # 遍历cstm_data中的每一项，根据变更类型进行处理
        for item in cstm_data:
            # 获取当前项名称和深度
            item_name = item.get('opt_after_path').split('👍')[-1]
            item_deep = len(item.get('opt_after_path').split('👍')) - 1

            # 根据变更类型选择对应的处理逻辑
            if item["type"] == "move":
                # 移动操作：先删除再添加
                need_change_opt_info.extend(process_move_item(item, cus_opt_cntt_info_list))
            elif item["type"] == "append":
                # 添加操作：递归方式添加新的模块
                need_change_opt_info.extend(process_append_item(item, cus_opt_cntt_info_list, objects_pairs, change_before_cstm_path, cur_dir))
            elif item["type"] == "update":
                # 更新操作：修改现有模块的信息
                need_change_opt_info.extend(process_update_item(item, objects_pairs, cur_dir, cus_opt_cntt_info_list, change_before_cstm_path))
            elif item["type"] == "delete":
                # 删除操作：从配置中移除指定模块
                need_change_opt_info.extend(process_delete_item(item, cus_opt_cntt_info_list))
            else:
                # 错误日志：未知的变更类型
                logger.error(f'未知的变更类型：{item["type"]}')

    except Exception as e:
        # 异常处理：记录错误信息并抛出异常
        logger.error(f"处理变更结果时发生异常：{str(e)}", exc_info=True)
        traceback.print_exc()
        raise ValueError(f"处理变更结果时发生异常,生成路径分析json文件失败")

    return need_change_opt_info

def process_move_item(item, cus_opt_cntt_info_list):
    """
    处理类型为'move'的变更项
    :param item: 当前处理的变更项
    :param cus_opt_cntt_info_list: CusOptCnttInfo对象列表
    :return: 需要执行的变更操作列表
    """
    opt_before_path = list(filter(None, item["opt_before_path"].split("👍")))
    opt_after_path = list(filter(None, item["opt_after_path"].split("👍")))
    opt_property = item["opt_property"]

    before_cus_opt_info, before_cus_opt_info_list, parent_cntt_info = get_change_opt_info(opt_before_path, cus_opt_cntt_info_list)
    result = []

    if before_cus_opt_info:
        # 先执行删除操作
        result.extend(modify_opt_info(before_cus_opt_info, before_cus_opt_info_list, parent_cntt_info, "delete"))

        _, after_cus_opt_info_list, parent_cntt_info = get_change_opt_info(opt_after_path, cus_opt_cntt_info_list)
        after_cus_opt_info = before_cus_opt_info

        if after_cus_opt_info and after_cus_opt_info_list:
            # 设置新的顺序和行索引，并执行添加操作
            after_cus_opt_info.OptOrder = opt_property.get("order")
            after_cus_opt_info.row_index = after_cus_opt_info_list[-1].row_index + 1
            result.extend(modify_opt_info(after_cus_opt_info, before_cus_opt_info_list, parent_cntt_info, "add"))

    return result

def process_append_item(item, cus_opt_cntt_info_list, objects_pairs, change_before_cstm_path, cur_dir):
    """
    处理类型为'append'的变更项
    :param item: 当前处理的变更项
    :param cus_opt_cntt_info_list: CusOptCnttInfo对象列表
    :param objects_pairs: 变更前后需求文档的配对信息
    :param change_before_cstm_path: 变更前cstm文件路径
    :param cur_dir: 变更后原始式样书所在文件夹路径
    :return: 需要执行的变更操作列表
    """
    return recursive_append_opt_info(item, cus_opt_cntt_info_list, [], objects_pairs, change_before_cstm_path, cur_dir)

def update_basic_properties(opt_info, opt_property):
    """更新对象updated的属性"""
    for key, value in opt_property.items():
        if hasattr(opt_info, key):
            setattr(opt_info, key, value)

def find_matching_item_dict(objects_pairs, item_name, item_deep):
    '''
    从变更后目录树objects_pairs[1]中查找与item_name,item_deep匹配的对象、
    :param objects_pairs:
    :param item_name: 从路径中解析出的item name
    :param item_deep: 在路径中的hyrachy level
    :return: 变更后目录树中对应的对象
    '''
    # 获取配置中的映射字典
    item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

    for obj in objects_pairs[1]:
        # item_deep - 1 是为了消除Menu的影响，Menu为虚拟层级
        name_matches = (item_name_map.get(obj.item_name) == item_name or
                       obj.item_name == item_name)
        level_matches = obj.hierarchy_level == str(item_deep - 1)

        if name_matches and level_matches:
            return obj
    return None


def parse_spec_document(item_dict):
    """解析关联式样文档信息"""
    spec_document = item_dict.spec_document.strip("[]").split(",")
    linked_spec_prefix = [item.strip("'\"") for item in spec_document]
    return linked_spec_prefix


def update_get_set_functions(opt_info, item_dict, linked_spec_prefix, change_name, cur_dir):
    """更新授信/送信函数"""
    set_tbl_data, get_tbl_data, linked_spec_excel = extract_get_set_cnt_table(
        linked_spec_prefix, change_name, cur_dir)
    is_special_handle = if_need_special_handle(change_name, linked_spec_excel)

    # 填充受信函数
    opt_info = fill_get_func_content(opt_info, item_dict, get_tbl_data, is_special_handle)
    # 填充送信函数
    opt_info = fill_set_func_content(opt_info, item_dict, set_tbl_data, is_special_handle)

    return opt_info

def update_cntt_placeholder(item, item_dict, opt_info, cus_opt_cntt_info_list, opt_before_path):
    """处理画面文言变更"""
    result = []

    # 更新画面占位行的内容
    container_map = {container.Label: idx for idx, container in enumerate(cus_opt_cntt_info_list)}
    bf_cntt_name = opt_before_path[-1]

    if bf_cntt_name not in container_map:
        raise ValueError(
            f"cstm 配置工具中未找到匹配画面, 画面信息更新失败:{item_dict.item_name}"
        )

    bf_container_idx = container_map[bf_cntt_name]
    # 更新cus_opt_cntt_info_list中的画面文言信息
    cus_opt_cntt_info_list[bf_container_idx].Label = item_dict.item_name
    cntt_row_idx = cus_opt_cntt_info_list[bf_container_idx].cus_opt_info_list[0].row_index

    result.append({
        "type": "update",
        "opt_info": cus_opt_cntt_info_list[bf_container_idx],
        "index": cntt_row_idx
    })

    return result


def process_update_item(item, objects_pairs, cur_dir, cus_opt_cntt_info_list, change_before_cstm_path):
    """
    处理类型为'update'的变更项
    :param item: 当前处理的变更项
    :param objects_pairs: 变更前后需求文档的配对信息
    :param cur_dir: 变更后原始式样书所在文件夹路径
    :param cus_opt_cntt_info_list: CusOptCnttInfo对象列表
    :param change_before_cstm_path: 变更前cstm文件路径
    :return: 需要执行的变更操作列表
    """
    result = []

    # 解析输入参数
    opt_before_path = list(filter(None, item["opt_before_path"].split("👍")))
    opt_property = item["opt_property"] #记录了对象变更的属性信息

    # 获取变更前的配置信息
    before_cus_opt_info, _, _ = get_change_opt_info(opt_before_path, cus_opt_cntt_info_list)
    if not before_cus_opt_info:
        return result

    # 更新对象变更的属性信息
    update_basic_properties(before_cus_opt_info, opt_property)

    # 提取变更项信息
    opt_after_path = item.get('opt_after_path', '')
    item_name = opt_after_path.split('👍')[-1]
    item_deep = len(opt_after_path.split('👍')) - 1

    # 从变更后目录树中查找匹配的item_dict
    item_dict = find_matching_item_dict(objects_pairs, item_name, item_deep)
    if not item_dict:
        result.append({
            "type": "update",
            "opt_info": before_cus_opt_info,
            "index": before_cus_opt_info.row_index
        })
        return result

    # 解析关联式样文档
    linked_spec_prefix = parse_spec_document(item_dict)
    change_name = item_dict.item_name

    # 更新当前模块的元信息
    before_cus_opt_info = fill_curent_item_info(
        before_cus_opt_info, change_before_cstm_path, item_dict, type='update')

    # 更新授信/送信函数（如果需要）
    change_types = item.get('change_types')
    if change_types and 'CSTM意匠变更' not in change_types:
        before_cus_opt_info = update_get_set_functions(
            before_cus_opt_info, item_dict, linked_spec_prefix, change_name, cur_dir)

    # 处理画面文言变更
    # 判断是否为画面文言变更
    if item.get('is_cntt') and item.get('change_types') == ['CSTM意匠变更']:
        # 更新下一层级模块的meta info
        before_cus_opt_info.NextCnttLabel = item_dict.item_name
        # 画面文言变更 需要更新画面占位行的信息
        cntt_results = update_cntt_placeholder(
            item, item_dict, before_cus_opt_info, cus_opt_cntt_info_list, opt_before_path)
        result.extend(cntt_results)

    # 添加主要的更新结果
    result.append({
        "type": "update",
        "opt_info": before_cus_opt_info,
        "index": before_cus_opt_info.row_index
    })

    return result

def process_delete_item(item, cus_opt_cntt_info_list):
    """
    处理类型为'delete'的变更项
    :param item: 当前处理的变更项
    :param cus_opt_cntt_info_list: CusOptCnttInfo对象列表
    :return: 需要执行的变更操作列表
    """
    opt_before_path = list(filter(None, item["opt_before_path"].split("👍")))
    before_cus_opt_info, before_cus_opt_info_list, parent_cntt_info = get_change_opt_info(opt_before_path, cus_opt_cntt_info_list)

    if before_cus_opt_info:
        return modify_opt_info(before_cus_opt_info, before_cus_opt_info_list, parent_cntt_info, "delete", cus_opt_cntt_info_list)

    return []


def read_excel_input_cst_data(worksheet, cus_opt_cntt_info_list):
    """从Excel中读取数据，对应VB中的ReadExcelInputCstData函数"""
    cus_opt_cntt_num = -1
    start_index = 4  # 从第4行开始读取数据
    index_row = start_index
    opt_array_index = 0
    current_label = ""

    # 辅助函数，用于获取单元格的值而不是公式
    def get_cell_value(cell):
        if cell is None:
            return ""
        # 如果是公式计算结果，获取其值而不是公式文本
        if hasattr(cell, 'Value'):
            return cell.Value if cell.Value is not None else ""
        return cell if cell is not None else ""

    # 读取I列的第4行数据作为起始点
    opt_label_str = get_cell_value(worksheet.Cells(start_index, 9))  # I列对应9

    while opt_label_str != "end":
        if opt_label_str != "-":
            # 处理子选项信息
            opt_array_index += 1

            cus_opt_info = CusOptInfo()
            cus_opt_info.row_index = index_row

            cus_opt_info.CusOptNum = int(get_cell_value(worksheet.Cells(index_row, 8)))  # H列
            cus_opt_info.OptLabel = get_cell_value(worksheet.Cells(index_row, 9))  # I列
            cus_opt_info.OptDef = get_cell_value(worksheet.Cells(index_row, 10))  # J列
            cus_opt_info.OptType = get_cell_value(worksheet.Cells(index_row, 11))  # K列
            cus_opt_info.OptOrder = int(get_cell_value(worksheet.Cells(index_row, 12)))  # L列
            cus_opt_info.OptSupbit = int(
                '0' if str(get_cell_value(worksheet.Cells(index_row, 13))).strip()=='' else get_cell_value(worksheet.Cells(index_row, 13))
            )  # M列

            # 处理下一个内容信息
            cus_opt_info.NextCnttLabel = get_cell_value(worksheet.Cells(index_row, 14))  # N列
            cus_opt_info.NextCnttDef = get_cell_value(worksheet.Cells(index_row, 15))  # O列
            cus_opt_info.NextCnttSpbEnb = get_cell_value(worksheet.Cells(index_row, 16))  # P列

            # 如果第N列不为- 则表示存在下一阶层信息
            if cus_opt_info.NextCnttLabel != "-":
                cus_opt_info.has_next_cntt = True

            # 处理生成函数相关的字段
            cus_opt_info.CnttGetCon = get_cell_value(worksheet.Cells(index_row, 19))  # S列
            cus_opt_info.CnttSetCon = get_cell_value(worksheet.Cells(index_row, 20))  # T列
            cus_opt_info.CnttCancel = get_cell_value(worksheet.Cells(index_row, 21))  # U列
            cus_opt_info.CnttGetConvert = get_cell_value(worksheet.Cells(index_row, 22))  # V列
            cus_opt_info.CnttSetConvert = get_cell_value(worksheet.Cells(index_row, 23))  # W列
            cus_opt_info.CnttTableGet = get_cell_value(worksheet.Cells(index_row, 24))  # X列
            cus_opt_info.CnttTableSet = get_cell_value(worksheet.Cells(index_row, 25))  # Y列
            global cus_opt_cntt_info
            cus_opt_cntt_info.cus_opt_info_list.append(cus_opt_info)

        else:
            # 处理新的内容部分, 新建的CusOptCnttInfo对象
            cus_opt_cntt_info = CusOptCnttInfo()
            cus_opt_cntt_info_list.append(cus_opt_cntt_info)

            start_opt = index_row
            opt_array_index = 0
            cus_opt_cntt_num += 1

            # 读取当前级别标题信息
            cus_opt_cntt_info.Level = get_cell_value(worksheet.Cells(start_opt, 2))  # B列
            cus_opt_cntt_info.Label = get_cell_value(worksheet.Cells(start_opt, 3))  # C列
            # 保存当前标签，用于子级内容
            current_label = cus_opt_cntt_info.Label

            # 为后续内容设置父标签
            cus_opt_cntt_info.parent_label = current_label

            cus_opt_cntt_info.Definit = get_cell_value(worksheet.Cells(start_opt, 4))  # D列
            cus_opt_cntt_info.Main = get_cell_value(worksheet.Cells(start_opt, 5))  # E列
            cus_opt_cntt_info.Secondary = get_cell_value(worksheet.Cells(start_opt, 6))  # F列
            cus_opt_cntt_info.Position = get_cell_value(worksheet.Cells(start_opt, 7))  # G列

            # 把-第0列加入到CusOptInfo中
            cus_opt_info = CusOptInfo()
            cus_opt_info.row_index = index_row

            cus_opt_info.CusOptNum = get_cell_value(worksheet.Cells(index_row, 8))  # H列
            cus_opt_info.OptLabel = get_cell_value(worksheet.Cells(index_row, 9))  # I列
            cus_opt_info.OptDef = get_cell_value(worksheet.Cells(index_row, 10))  # J列
            cus_opt_info.OptType = get_cell_value(worksheet.Cells(index_row, 11))  # K列
            cus_opt_info.OptOrder = get_cell_value(worksheet.Cells(index_row, 12))  # L列
            cus_opt_info.OptSupbit = get_cell_value(worksheet.Cells(index_row, 13))  # M列

            # 第0列下一个内容信息
            cus_opt_info.NextCnttLabel = get_cell_value(worksheet.Cells(index_row, 14))  # N列
            cus_opt_info.NextCnttDef = get_cell_value(worksheet.Cells(index_row, 15))  # O列
            cus_opt_info.NextCnttSpbEnb = get_cell_value(worksheet.Cells(index_row, 16))  # P列

            # 第0列生成函数相关的字段
            cus_opt_info.CnttGetCon = get_cell_value(worksheet.Cells(index_row, 19))  # S列
            cus_opt_info.CnttSetCon = get_cell_value(worksheet.Cells(index_row, 20))  # T列
            cus_opt_info.CnttCancel = get_cell_value(worksheet.Cells(index_row, 21))  # U列
            cus_opt_info.CnttGetConvert = get_cell_value(worksheet.Cells(index_row, 22))  # V列
            cus_opt_info.CnttSetConvert = get_cell_value(worksheet.Cells(index_row, 23))  # W列
            cus_opt_info.CnttTableGet = get_cell_value(worksheet.Cells(index_row, 24))  # X列
            cus_opt_info.CnttTableSet = get_cell_value(worksheet.Cells(index_row, 25))  # Y列

            # 读取链接类型和ECOWALLET信息
            cus_opt_cntt_info.link_type = get_cell_value(worksheet.Cells(start_opt, 17))  # Q列
            cus_opt_cntt_info.eco_wallet = get_cell_value(worksheet.Cells(start_opt, 18))  # R列

            cus_opt_cntt_info.cus_opt_info_list.append(cus_opt_info)

        index_row += 1
        opt_label_str = get_cell_value(worksheet.Cells(index_row, 9))

    for cus_opt_cntt_info in cus_opt_cntt_info_list:
        # cus_opt_cntt_info.lines_num表示画面中包含子项的个数，由于CusOptNum是从0开始所以需要加1
        cus_opt_nums = [int(opt_info.CusOptNum) for opt_info in cus_opt_cntt_info.cus_opt_info_list if opt_info.CusOptNum and str(int(opt_info.CusOptNum)).isdigit()]
        if cus_opt_nums:
            cus_opt_cntt_info.lines_num = max(cus_opt_nums)+1
        else:
            cus_opt_cntt_info.lines_num = 1

    logger.info("Input data read successfully")
    return cus_opt_cntt_num

def make_excel_update(need_change_opt_info_list, excel_util:CSTMExcelUtil, cstm_end_flag:bool):
    """
    修改Excel文件， 根据need_change_opt_info_list中的信息，修改Excel文件
    先根据need_change_opt_info_list中的index，找到需要修改的行，对行进行排序，因为需要从后面开始修改，否则会影响前面的行
    然后根据need_change_opt_info_list中的type，进行修改
    如果同一行同时有delete和update操作，则只执行delete操作

    # TODO 这里如果有多个update，对象地址一般是一样的，可以优化合并为只操作一次
    """
    def get_label(obj:CusOptInfo|CusOptCnttInfo):
        if isinstance(obj, CusOptInfo):
            return obj.OptLabel
        elif isinstance(obj, CusOptCnttInfo):
            return obj.Label
        else:
            return ""

    # lru_cache()
    # 根据index排序
    need_change_opt_info_list.sort(key=lambda x: x["index"])

    # 先预执行更改index, 只有delete和add的情况下，index需要对应减一和加1，update的情况下不需要
    for i, item in enumerate(need_change_opt_info_list):
        if item["type"] == "delete":
            for j in range(i + 1, len(need_change_opt_info_list)):
                if need_change_opt_info_list[j]["index"] > item["index"]:
                    need_change_opt_info_list[j]["index"] -= 1
        elif item["type"] == "add":
            for j in range(i + 1, len(need_change_opt_info_list)):
                if need_change_opt_info_list[j]["index"] == item["index"] \
                        and need_change_opt_info_list[j]["type"] == "update"\
                        and type(need_change_opt_info_list[j]["opt_info"]) is type(item["opt_info"]) \
                        and get_label(need_change_opt_info_list[j]["opt_info"]) == get_label(item["opt_info"]):
                    continue

                if need_change_opt_info_list[j]["index"] >= item["index"]:
                    need_change_opt_info_list[j]["index"] += 1

    # 记录需要删除的行索引
    deleted_rows = set()
    # 根据type修改
    for idx, item in enumerate(need_change_opt_info_list):
        if item["type"] == "delete":
            excel_util.delete_row(item["index"])
            deleted_rows.add(item["index"])
        elif item["type"] == "add":
            if isinstance(item['opt_info'], CusOptInfo):
                excel_util.insert_row(item["index"], {
                    'H': item["opt_info"].CusOptNum,
                    'I': item["opt_info"].OptLabel,
                    'J': item["opt_info"].OptDef,
                    'K': item["opt_info"].OptType,
                    'L': item["opt_info"].OptOrder,
                    'M': item["opt_info"].OptSupbit,
                    'N': item["opt_info"].NextCnttLabel,
                    'O': item["opt_info"].NextCnttDef,
                    'P': item["opt_info"].NextCnttSpbEnb,
                    'S': item["opt_info"].CnttGetCon,
                    'T': item["opt_info"].CnttSetCon,
                    'U': item["opt_info"].CnttCancel,
                    'V': item["opt_info"].CnttGetConvert,
                    'W': item["opt_info"].CnttSetConvert,
                    'X': item["opt_info"].CnttTableGet,
                    'Y': item["opt_info"].CnttTableSet
                })
            elif isinstance(item['opt_info'], CusOptCnttInfo):
                excel_util.insert_row(item["index"],{
                    'B': item["opt_info"].Level,
                    'C': item["opt_info"].Label,
                    'D': item["opt_info"].Definit,
                    'E': item["opt_info"].Main,
                    'F': item["opt_info"].Secondary,
                    'G': item["opt_info"].Position,
                    'Q': item["opt_info"].link_type,
                    'R': item["opt_info"].eco_wallet
                })
                # 父级目录占位行的选项信息
                parent_opt_info=item['opt_info'].cus_opt_info_list[0]
                excel_util.update_cell(item["index"], {
                    'H': parent_opt_info.CusOptNum,
                    'I': parent_opt_info.OptLabel,
                    'J': parent_opt_info.OptDef,
                    'K': parent_opt_info.OptType,
                    'L': "0",
                    'M': "0",
                    'N': parent_opt_info.NextCnttLabel,
                    'O': parent_opt_info.NextCnttDef,
                    'P': parent_opt_info.NextCnttSpbEnb,
                    'S': parent_opt_info.CnttGetCon,
                    'T': parent_opt_info.CnttSetCon,
                    'U': parent_opt_info.CnttCancel,
                    'V': parent_opt_info.CnttGetConvert,
                    'W': parent_opt_info.CnttSetConvert,
                    'X': parent_opt_info.CnttTableGet,
                    'Y': parent_opt_info.CnttTableSet
                })
        elif item["type"] == "update" and item["index"] not in deleted_rows:
            if isinstance(item['opt_info'], CusOptInfo):
                excel_util.update_cell(item["index"], {
                    'H': item["opt_info"].CusOptNum,
                    'I': item["opt_info"].OptLabel,
                    'J': item["opt_info"].OptDef,
                    'K': item["opt_info"].OptType,
                    'L': item["opt_info"].OptOrder,
                    'M': item["opt_info"].OptSupbit,
                    'N': item["opt_info"].NextCnttLabel,
                    'O': item["opt_info"].NextCnttDef,
                    'P': item["opt_info"].NextCnttSpbEnb,
                    'S': item["opt_info"].CnttGetCon,
                    'T': item["opt_info"].CnttSetCon,
                    'U': item["opt_info"].CnttCancel,
                    'V': item["opt_info"].CnttGetConvert,
                    'W': item["opt_info"].CnttSetConvert,
                    'X': item["opt_info"].CnttTableGet,
                    'Y': item["opt_info"].CnttTableSet
                })
            elif isinstance(item['opt_info'], CusOptCnttInfo):
                excel_util.update_cell(item["index"],{
                    'B': item["opt_info"].Level,
                    'C': item["opt_info"].Label,
                    'D': item["opt_info"].Definit,
                    'E': item["opt_info"].Main,
                    'F': item["opt_info"].Secondary,
                    'G': item["opt_info"].Position,
                    'Q': item["opt_info"].link_type,
                    'R': item["opt_info"].eco_wallet
                })
                # 父级目录占位行的选项信息
                parent_opt_info = item['opt_info'].cus_opt_info_list[0]
                excel_util.update_cell(item["index"], {
                    'H': parent_opt_info.CusOptNum,
                    'I': parent_opt_info.OptLabel,
                    'J': parent_opt_info.OptDef,
                    'K': parent_opt_info.OptType,
                    'L': "0",
                    'M': "0",
                    'N': parent_opt_info.NextCnttLabel,
                    'O': parent_opt_info.NextCnttDef,
                    'P': parent_opt_info.NextCnttSpbEnb,
                    'S': parent_opt_info.CnttGetCon,
                    'T': parent_opt_info.CnttSetCon,
                    'U': parent_opt_info.CnttCancel,
                    'V': parent_opt_info.CnttGetConvert,
                    'W': parent_opt_info.CnttSetConvert,
                    'X': parent_opt_info.CnttTableGet,
                    'Y': parent_opt_info.CnttTableSet
                })

    new_file = None
    if cstm_end_flag:
        new_file = excel_util.save()

    return new_file, excel_util

def read_cstm_data(path):
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)  # 直接解析为 Python 字典/列表

def recursive_append_opt_info(item, cus_opt_cntt_info_list, need_change_opt_info, req_list, change_before_cstm_path, cur_dir):
    '''
    对于增加子树情况采用递归的方式添加模块
    '''
    has_parent_cntt = True if item.get("children") else False
    item_name = item.get('opt_after_path').split('👍')[-1]
    item_hierachy_level = len(item.get('opt_after_path').split('👍')) - 2
    opt_after_path = list(filter(None, item["opt_after_path"].split("👍")))
    opt_property = item["opt_property"]
    # 判断当前模块是否存在
    is_exist = is_opt_exist(opt_after_path, cus_opt_cntt_info_list)

    if not is_exist:
        _, after_cus_opt_info_list, parent_cntt_info = get_change_opt_info(opt_after_path, cus_opt_cntt_info_list)
        after_cus_opt_info = CusOptInfo()
        # 如果存在父级则设置has_next_cntt为True
        if has_parent_cntt:
            after_cus_opt_info.has_next_cntt = True

        for key, value in opt_property.items():
            setattr(after_cus_opt_info, key, value)
        after_cus_opt_info.OptOrder = opt_property.get("order")
        if len(after_cus_opt_info_list) > 0:
            after_cus_opt_info.row_index = after_cus_opt_info_list[-1].row_index + 1
        else:
            after_cus_opt_info.row_index = 1

        # 判断是否是选项变更成画面
        opt_to_cntt_flag = False
        raw_opt_info = None
        if parent_cntt_info:
            for i, opt_info in enumerate(parent_cntt_info.cus_opt_info_list):
                if opt_info.OptLabel == item_name:
                    if item.get('is_cntt') and not opt_info.has_next_cntt:
                        opt_to_cntt_flag = True
                        raw_opt_info = opt_info
                    break

        if opt_to_cntt_flag:
            after_cus_opt_info = deepcopy(raw_opt_info)
            after_cus_opt_info.has_next_cntt = True
        else:
            # 填充模块meta 信息， 受信函数，送信函数信息
            after_cus_opt_info = fill_ctsm_info_by_rule(req_list, item_name, item_hierachy_level, after_cus_opt_info, change_before_cstm_path, cur_dir)

        need_change_opt_info.extend(
            modify_opt_info(after_cus_opt_info, after_cus_opt_info_list, parent_cntt_info, "add", cus_opt_cntt_info_list))

    if has_parent_cntt:
        return recursive_append_opt_info(item.get("children"), cus_opt_cntt_info_list, need_change_opt_info, req_list,
                                         change_before_cstm_path, cur_dir)
    else:
        return need_change_opt_info


def is_opt_exist(opt_path, cus_opt_cntt_info_list):
    '''
    判断当前模块在父级目录中是否存在
    这里是先去从父级目录中匹配到选项的宏定义函数名称然后根据宏定义函数名称去匹配到对应的容器
    然后根据容器去匹配到对应的选项
    '''
    if len(opt_path) > 1:
        # 预处理：创建字典加速查找
        # key 为容器标签， value 为容器
        container_map = {container.Label: container for container in cus_opt_cntt_info_list}
        # key 为宏定义函数名称， value 为容器
        container_def_map = {container.Definit: container for container in cus_opt_cntt_info_list}
        current_container = container_map.get(opt_path[0])
        is_match = True
        for idx, item in enumerate(opt_path[1:]):
            # key 为容器标签， value 为容器
            current_container_map = {container.OptLabel: container for container in current_container.cus_opt_info_list}
            # 根据容器标签从父级的cus_opt_info_list中匹配到对应的选项
            current_opt = current_container_map.get(item)
            if not current_opt:
                is_match = False
                return is_match
            # 根据选项的宏定义函数名称从容器中匹配到对应的容器 宏定义是唯一的，所以用宏定义匹配
            current_definition = current_opt.OptDef
            current_container = container_def_map.get(current_definition)
            if not current_container:
                is_match = False
                return is_match

        return is_match

def get_change_opt_info(opt_path, cus_opt_cntt_info_list):
    if not opt_path:
        return None, []

    # 获取配置中的映射字典
    item_name_map, title_map = CstmUtils.get_screen_hierarchy_maps()

    # 预处理：创建字典加速查找
    container_map = {container.Label: container for container in cus_opt_cntt_info_list}

    current_label = opt_path[0]
    item_name_map_vals = {v: k for k, v in item_name_map.items()}

    change_cus_opt_info = None
    change_cus_opt_info_list = []
    current_container = None
    # 遍历操作路径中的每个操作节点（从第二个元素开始）
    for step_idx, opt_label in enumerate(opt_path[1:], start=1):
        if current_label in item_name_map_vals:
            current_label = item_name_map_vals.get(current_label)

        if opt_label in title_map:
            opt_label = title_map.get(opt_label)

        # 获取当前层级的容器
        current_container = container_map.get(current_label)
        if not current_container:
            break
        # if step_idx == len(opt_path) - 1:
        change_cus_opt_info_list = current_container.cus_opt_info_list
        # 在容器中查找操作节点
        opt_info_map_opt_label = {opt.OptLabel: opt for opt in current_container.cus_opt_info_list}
        current_opt = opt_info_map_opt_label.get(opt_label)
        # 选项名称有可能和路径中的名称不一致，不一致时可能是由于cstm tool中OptLabel 和对应的 NextCnttLabel不一致导致的，因此添加下述逻辑
        if current_opt is None:
            opt_info_map_next_label = {opt.NextCnttLabel: opt for opt in current_container.cus_opt_info_list}
            current_opt = opt_info_map_next_label.get(opt_label)

        if not current_opt:
            break

        # 记录最终返回结果（如果是最后一个操作）
        # if step_idx == len(opt_path) - 1:
        change_cus_opt_info = current_opt

        # 更新下一个层级的容器标签
        current_label = current_opt.NextCnttLabel

        if not current_opt.has_next_cntt:
            break

    return change_cus_opt_info, change_cus_opt_info_list, current_container

def modify_opt_info(cus_opt_info, cus_parent_cntt_info_list, parent_cntt_info, change_type, cus_opt_cntt_info_list=None):
    """子级标签中同级的其他的Order是否需要依次变更"""
    cus_opt_info_list = parent_cntt_info.cus_opt_info_list

    if len(cus_opt_info_list) == 0:
        return []

    # 需要变更的子项
    need_change_opt_info_list = []
    # 遍历所有CusOptInfo对象,找到需要删除的行,再遍历同级选项在当前order后面的对应的order需要减一
    if change_type == "delete":
        current_order = int(cus_opt_info.OptOrder)
        need_change_opt_info_list.append({"type": "delete", "opt_info": cus_opt_info, "index": cus_opt_info.row_index})

        # 从后往前遍历列表，避免删除元素时影响索引
        for opt_info in reversed(cus_opt_info_list):
            # 检查Order值大于当前要删除的选项
            if isinstance(opt_info.OptOrder, int) or str(opt_info.OptOrder).isdigit():
                if int(opt_info.OptOrder) > current_order:
                    # 将Order值减1
                    opt_info.OptOrder = str(int(opt_info.OptOrder) - 1)
                    need_change_opt_info_list.append(
                        {"type": "update", "opt_info": opt_info, "index": opt_info.row_index})
                elif int(opt_info.OptOrder) == current_order:
                    # 如果Order值等于当前要删除的选项,则释放支持位
                    parent_cntt_info.release_supbit(opt_info.OptSupbit)
                    # 从parent_cntt_info的cus_opt_info_list 删除这个opt_info
                    parent_cntt_info.cus_opt_info_list.remove(opt_info)

        if len(parent_cntt_info.cus_opt_info_list) == 1 and parent_cntt_info.cus_opt_info_list[0].OptLabel == "-":
            # 画面下的选项已经全部删除, 只剩一个占位的，则把占位的删除，父级的next_cntt设置为-
            delete_opt_info = parent_cntt_info.cus_opt_info_list[0]
            need_change_opt_info_list.append({"type": "delete", "opt_info":delete_opt_info , "index": delete_opt_info.row_index})
            # 把父级的next_cntt 设置为 -
            parent_level = parent_cntt_info.Level
            parent_lable = parent_cntt_info.Label
            for cus_opt_cntt_info in cus_opt_cntt_info_list:
                if cus_opt_cntt_info.Level == parent_level - 1:
                    for cus_opt_info in cus_opt_cntt_info.cus_opt_info_list:
                        if cus_opt_info.NextCnttLabel == parent_lable:
                            cus_opt_info.NextCnttLabel = "-"
                            cus_opt_info.has_next_cntt = False
                            need_change_opt_info_list.append(
                                {"type": "update", "opt_info": cus_opt_info, "index": cus_opt_info.row_index})

    # 遍历所有CusOptInfo对象,找到需要删除的行,再遍历同级选项在当前order后面的对应的order需要加一
    if change_type == "add":
        # 如果是add的情况需要使用深拷贝，否则会影响到原来的list里面的order的判断，delete的情况则不需要
        cus_opt_info = copy.deepcopy(cus_opt_info)
        current_order = int(cus_opt_info.OptOrder)

        # 找到等于current_order的同级项的row_index
        insert_row_index = -1

        opt_exist = False

        for i, opt_info in enumerate(cus_opt_info_list):
            # 一般情况下，current_order是在cus_opt_info_list的最后一个元素的OptOrder后面的，因为追加前不应该存在这个项
            if str(opt_info.OptOrder) == str(current_order):
                insert_row_index = opt_info.row_index
                if opt_info.OptLabel == cus_opt_info.OptLabel:
                    opt_exist = True
                    need_change_opt_info_list.append(
                        {"type": "update", "opt_info": cus_opt_info, "index": insert_row_index})
                    if cus_opt_info.has_next_cntt and not opt_info.has_next_cntt:
                        # 选项变更成画面
                        # 添加展位行及画面信息
                        need_change_opt_info_list = add_placeholder_cntt(
                            cus_opt_info, cus_opt_info_list, parent_cntt_info, cus_opt_cntt_info_list, need_change_opt_info_list
                        )
                        # 更新 cus_opt_cntt_info_list中的信息
                        target_cntt_idx, target_opt_idx = update_cus_opt_cntt_info_list(parent_cntt_info.Definit, cus_opt_info, cus_opt_cntt_info_list)
                        cus_opt_cntt_info_list[target_cntt_idx].cus_opt_info_list[target_opt_idx].has_next_cntt = True
                        cus_opt_cntt_info_list[target_cntt_idx].cus_opt_info_list[target_opt_idx].NextCnttLabel = cus_opt_info.OptLabel
                        cus_opt_cntt_info_list[target_cntt_idx].cus_opt_info_list[target_opt_idx].NextCnttDef = cus_opt_info.OptDef

                break

        if opt_exist:
            # 需要添加的选项已经存在了，直接返回
            return need_change_opt_info_list

        # 如果没有找到等于current_order的项，则在末尾添加
        if insert_row_index == -1:
            if cus_opt_info_list[-1].OptOrder == -1:
                insert_row_index = cus_opt_info_list[-1].row_index
            else:
                insert_row_index = cus_opt_info_list[-1].row_index + 1

        # 更新Order值
        filtered_list = [item for item in reversed(cus_opt_info_list) if item.OptLabel == cus_opt_info.OptLabel]
        for opt_info in cus_opt_info_list:
            if isinstance(opt_info.OptOrder, int) or str(opt_info.OptOrder).isdigit():
                # 如果待添加的目录已经存在了就不添加到need_change_opt_info_list了
                if not filtered_list:
                    if int(opt_info.OptOrder) >= current_order:
                        # 筛选出已有选项中order 大于 当前order的选项，并更新这些选项的order信息（order被挤下去了）
                        opt_info.OptOrder = str(int(opt_info.OptOrder) + 1)
                        need_change_opt_info_list.append(
                            {"type": "update", "opt_info": opt_info, "index": opt_info.row_index})

        if parent_cntt_info.used_supbits:
            new_opt_supbit = parent_cntt_info.get_next_available_supbit()
        else:
            for opt_info in cus_opt_info_list:
                if isinstance(opt_info.OptSupbit, int) or str(opt_info.OptSupbit).isdigit():
                    parent_cntt_info.used_supbits.append(int(opt_info.OptSupbit))
            new_opt_supbit = parent_cntt_info.get_next_available_supbit()

        # 把需要变更的支持位赋值给cus_opt_info,把row_index赋值给cus_opt_info,表示新添加的一行
        cus_opt_info.OptSupbit = str(new_opt_supbit)
        cus_opt_info.row_index = insert_row_index

        # 把需要变更的行数更新到parent_cntt_info.lines_num, 从0开始不需要加1
        cus_opt_info.CusOptNum = parent_cntt_info.lines_num
        parent_cntt_info.lines_num += 1

        # 把需要变更的行加入到need_change_opt_info中
        # 对于has_next_cnnt = True 的opt 如果已经存在了就不要再加了
        if not filtered_list:
            need_change_opt_info_list.append({"type": "add", "opt_info": cus_opt_info, "index": insert_row_index})
            # 不能把cus_opt_info加入到parent_cntt_info的cus_opt_info_list中，会导致add的时候索引依次增加，实际此时还没写入
        else:
            return need_change_opt_info_list

        # 如果新增的opt has_next_cntt属性为True，还需要新增一行占位行
        if cus_opt_info.has_next_cntt:
            # 添加画面及占位画面信息
            need_change_opt_info_list = add_placeholder_cntt(
                cus_opt_info, cus_opt_info_list, parent_cntt_info, cus_opt_cntt_info_list, need_change_opt_info_list
            )
            # 把插入的画面作为子项插入到对应位置
            for opt_info in cus_opt_cntt_info_list:
                if opt_info.Label == parent_cntt_info.Label:
                    opt_info.cus_opt_info_list.append(cus_opt_info)
                    break

    return need_change_opt_info_list


def add_placeholder_cntt(cus_opt_info, cus_opt_info_list, parent_cntt_info, cus_opt_cntt_info_list, need_change_opt_info_list):
    '''
    添加画面及占位画面信息
    :param cus_opt_info:
    :param cus_opt_info_list:
    :param parent_cntt_info:
    :param cus_opt_cntt_info_list:
    :param need_change_opt_info_list:
    :return:
    '''
    # 寻找与待插入画面最近的父级画面子项
    last_has_next_cntt = None
    for opt_info in reversed(cus_opt_info_list):
        if opt_info.has_next_cntt:
            last_has_next_cntt = opt_info
            break

    latest_cntt = None
    if last_has_next_cntt:
        latest_label_name = last_has_next_cntt.OptLabel
        # 得到插入画面的row index
        # 从cus_opt_cntt_info_list中匹配到Label属性值为latest_label_name的元素
        for opt_info in cus_opt_cntt_info_list:
            if opt_info.Label == latest_label_name:
                latest_cntt = opt_info
                break

    new_parent_row_index = latest_cntt.cus_opt_info_list[-1].row_index if latest_cntt else (
        cus_opt_info_list[-1].row_index)
    # 初始化一个画面对象
    new_parent_cntt_info = CusOptCnttInfo()
    new_parent_cntt_info.Level = latest_cntt.Level if latest_cntt else (parent_cntt_info.Level + 1)
    new_parent_cntt_info.Label = cus_opt_info.OptLabel
    new_parent_cntt_info.Definit = cus_opt_info.OptDef
    new_parent_cntt_info.Main = parent_cntt_info.Definit  # 上一阶层画面的宏定义信息
    new_parent_cntt_info.Secondary = cus_opt_info.OptDef  # 上一阶层所在选项的宏定义信息
    new_parent_cntt_info.Position = latest_cntt.Position if latest_cntt else parent_cntt_info.Position
    new_parent_cntt_info.parent_label = cus_opt_info.OptLabel
    new_parent_cntt_info.link_type = latest_cntt.link_type if latest_cntt else parent_cntt_info.link_type
    new_parent_cntt_info.eco_wallet = latest_cntt.eco_wallet if latest_cntt else parent_cntt_info.eco_wallet
    new_parent_cntt_info.lines_num = 0
    new_parent_cntt_info.cus_opt_info_list = []

    # 创建一个占位的子项
    placeholder_cus_opt_info = CusOptInfo()
    if latest_cntt is not None:
        for key, value in latest_cntt.cus_opt_info_list[0].__dict__.items():
            setattr(placeholder_cus_opt_info, key, value)
    else:
        for key, value in cus_opt_info_list[0].__dict__.items():
            setattr(placeholder_cus_opt_info, key, value)

    placeholder_cus_opt_info.row_index = new_parent_row_index
    placeholder_cus_opt_info.OptOrder = -1
    # 向画面中添加占位子项
    new_parent_cntt_info.cus_opt_info_list.append(placeholder_cus_opt_info)
    new_parent_cntt_info.lines_num = len(new_parent_cntt_info.cus_opt_info_list)

    # 维护cus_opt_cntt_info_list，把new_parent_cntt_info加入到cus_opt_cntt_info_list中
    cus_opt_cntt_info_list.append(new_parent_cntt_info)

    # 添加占位的画面项
    need_change_opt_info_list.append(
        {"type": "add", "opt_info": new_parent_cntt_info, "index": new_parent_row_index})

    return need_change_opt_info_list

def update_cus_opt_cntt_info_list(cntt_def, cus_opt_info,cus_opt_cntt_info_list):
    '''
    更新内存cus_opt_cntt_info_list 中的属性信息
    :param cntt_def:
    :param cus_opt_info:
    :param cus_opt_cntt_info_list:
    :return:
    '''
    target_cntt_idx = 0
    for idx, cntt in enumerate(cus_opt_cntt_info_list):
        if cntt.Definit == cntt_def:
            target_cntt_idx = idx
            break

    target_opt_idx = 0
    for idx, opt in enumerate(cus_opt_cntt_info_list[target_cntt_idx].cus_opt_info_list):
        if opt.OptLabel == cus_opt_info.OptLabel:
            target_opt_idx = idx
            break

    return target_cntt_idx, target_opt_idx


if __name__ == "__main__":
    # 变更
    # change_summary = "警報音量の切替Type BⅠ→AⅠに変更 (QA対応)"
    # scl_path = r"D:\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # after_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # base_cstm = r"D:\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # # generate_cstm_json_tree(change_summary, scl_path, before_cstm, after_cstm)
    # cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)

    # # # 选项位置变更
    # change_summary = "第0階層⇒第一階層へ移動\n1500Wコンセント,2400Wコンセント,7200Wコンセント"
    # scl_path = r"D:\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # after_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # base_cstm = r"D:\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)


    # 选项追加
    # change_summary = "「表示シーン拡張」を追加"
    # scl_path = r"D:\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # after_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-04-A-C0.xlsx"
    # base_cstm = r"D:\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)

    # # 画面追加
    # change_summary = "・表示設定/メータタイプ設定/ECOに「燃費」を追加"
    # change_summary = "・表示設定/メータタイプ設定/ECOに「燃費」を追加-单个画面追加"
    # scl_path = r"D:\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-01-A-C0.xlsx"
    # after_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-02-A-C0.xlsx"
    # base_cstm = r"D:\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)


    # # 画面削除
    # change_summary = "警報音量の階層修正"
    # scl_path = r"D:\tdd_input\SCL\SCL_TEST.xlsx"
    # before_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx"
    # after_cstm = r"D:\tdd_input\原始式样书\MET-G_CSTMLST-CSTD-A0-04-A-C0.xlsx"
    # base_cstm = r"D:\tdd_input\CSTM Tool配置文件-演示.xlsm"
    # cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)


    # 面企划
    change_summary = "Power Tailgateに音量を追加\n上記に伴い、階層を変更(ON/OFFを第二階層へ移動）"
    scl_path = r"D:\面企划差分文件\SCL差分文档\SCL_MET-G_CSTMLST-CSTD-2-16-A-C0 .xlsx"
    before_cstm = r"D:\面企划差分文件\原始式样\MET-G_CSTMLST-CSTD-2-15-A-C0 .xlsx"
    after_cstm = r"D:\面企划差分文件\原始式样\MET-G_CSTMLST-CSTD-2-16-A-C0 .xlsx"
    base_cstm = r"D:\面企划差分文件\Customize_tool4.xlsm"
    cstm_data_to_excel(change_summary, scl_path, before_cstm, after_cstm, base_cstm)