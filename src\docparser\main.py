#!/usr/bin/env python
"""
DocParser - Document Parsing Tool

Main entry point for the document parsing tool.
"""

import os
import sys
import argparse
import logging
import json
import traceback
from typing import Dict, Any, List

from docparser.core.document_manager import DocumentManager
from docparser.utils.logging_utils import configure_logging
from docparser.utils.error_handler import (
    DocParserError, format_error_response, format_success_response
)

# Configure logging
logger = logging.getLogger('docparser')


def parse_args() -> argparse.Namespace:
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Document Parser Tool')
    
    parser.add_argument('file', nargs='?', help='Path to the document file to parse')
    parser.add_argument('-o', '--output', help='Path to save the output JSON file')
    parser.add_argument('-p', '--plugins', help='Directory containing plugins')
    parser.add_argument('-r', '--rules', help='Directory containing rules')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('-q', '--quiet', action='store_true', help='Suppress all output except errors')
    parser.add_argument('--log-file', help='Path to save log file')
    parser.add_argument('--text-only', action='store_true', help='Extract only text objects')
    parser.add_argument('--tables-only', action='store_true', help='Extract only table objects')
    parser.add_argument('--pictures-only', action='store_true', help='Extract only picture objects')
    parser.add_argument('--graphics-only', action='store_true', help='Extract only graphic objects')
    parser.add_argument('--stats', action='store_true', help='Show document statistics')
    parser.add_argument('--json-response', action='store_true', help='Output response as JSON')
    parser.add_argument('--export-rules', help='Export rules to JSON file')
    parser.add_argument('--disable-rule', action='append', help='Disable specific rule by ID (can be used multiple times)')
    parser.add_argument('--enable-rule', action='append', help='Enable specific rule by ID (can be used multiple times)')
    parser.add_argument('--list-rules', action='store_true', help='List available rules')
    
    return parser.parse_args()

def setup_logging(args: argparse.Namespace) -> None:
    """
    Set up logging based on command line arguments.
    
    Args:
        args: Command line arguments
    """
    log_level = logging.WARNING
    if args.quiet:
        log_level = logging.ERROR
    elif args.verbose:
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO
    
    configure_logging(log_level=log_level, log_file=args.log_file)

def print_document_summary(document_data: Dict[str, Any]) -> None:
    """
    Print document summary to console.
    
    Args:
        document_data: Document data
    """
    print("\nDocument Summary:")
    print(f"  File: {os.path.basename(document_data.get('file_path', 'Unknown'))}")
    print(f"  Type: {document_data.get('file_type', 'Unknown')}")
    print(f"  Text Objects: {len(document_data.get('text_objects', []))}")
    print(f"  Table Objects: {len(document_data.get('table_objects', []))}")
    print(f"  Picture Objects: {len(document_data.get('picture_objects', []))}")
    print(f"  Graphic Objects: {len(document_data.get('graphic_objects', []))}")
    
    print("\nUse --output option to save full results to a JSON file.")

def print_document_statistics(stats: List[Dict[str, int]]) -> None:
    """
    Print document statistics to console.
    
    Args:
        stats: Document statistics
    """
    for stat, index in stats:
        print(f"\nDocument Statistics of index {index}:")
        print(f"  Pages: {stat['page_count']}")
        print(f"  Text Objects: {stat['text_count']}")
        print(f"  Table Objects: {stat['table_count']}")
        print(f"  Picture Objects: {stat['picture_count']}")
        print(f"  Graphic Objects: {stat['graphic_count']}")
        print(f"  Total Objects: {stat['total_objects']}")

def output_json_response(response: Dict[str, Any]) -> None:
    """
    Output response as JSON to console.
    
    Args:
        response: Response data
    """
    print(json.dumps(response, indent=2))

def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code (0 for success, non-zero for error)
    """
    args = parse_args()
    
    # Set up logging
    setup_logging(args)
    
    try:
        # Create document manager
        doc_manager = DocumentManager(plugins_dir=args.plugins, rules_dir=args.rules)
        
        # Handle rule-specific operations
        if args.list_rules:
            rules = doc_manager.rule_engine.get_all_rules()
            if not args.quiet:
                print("\nAvailable Rules:")
                for rule in sorted(rules, key=lambda r: r.get_rule_priority(), reverse=True):
                    status = "Enabled" if rule.is_enabled() else "Disabled"
                    print(f"  {rule.get_rule_id()} ({status}, Priority: {rule.get_rule_priority()}) - {rule.get_rule_name()}")
                    if args.verbose:
                        print(f"    Target: {rule.get_rule_target()}")
                        print(f"    Description: {rule.get_rule_description()}")
                        print()
            
            if args.json_response:
                rules_data = []
                for rule in rules:
                    if hasattr(rule, 'to_json'):
                        rules_data.append(rule.to_json())
                    else:
                        rules_data.append({
                            'id': rule.get_rule_id(),
                            'name': rule.get_rule_name(),
                            'description': rule.get_rule_description(),
                            'priority': rule.get_rule_priority(),
                            'target': rule.get_rule_target(),
                            'enabled': rule.is_enabled()
                        })
                output_json_response(format_success_response({'rules': rules_data}))
            
            return 0
        
        # Enable/disable specific rules
        if args.enable_rule:
            for rule_id in args.enable_rule:
                if doc_manager.rule_engine.enable_rule(rule_id):
                    logger.info(f"Enabled rule: {rule_id}")
                else:
                    logger.warning(f"Rule not found: {rule_id}")
        
        if args.disable_rule:
            for rule_id in args.disable_rule:
                if doc_manager.rule_engine.disable_rule(rule_id):
                    logger.info(f"Disabled rule: {rule_id}")
                else:
                    logger.warning(f"Rule not found: {rule_id}")
        
        # Export rules if requested
        if args.export_rules:
            if not args.export_rules.endswith('.json'):
                args.export_rules += '.json'
            
            doc_manager.rule_engine.export_rules_to_json(args.export_rules)
            logger.info(f"Rules exported to: {args.export_rules}")
            
            if not args.file:
                return 0
        
        # Check if file path is provided
        if not args.file:
            logger.error("No file path provided")
            if not args.quiet:
                print("Error: No file path provided. Use --help for usage information.", file=sys.stderr)
            return 1
        
        # Parse document
        logger.info(f"Parsing document: {args.file}")
        document_data = doc_manager.parse_document(args.file)
        
        # Show document statistics
        if args.stats:
            stats = doc_manager.get_document_statistics()
            if not args.json_response and not args.quiet:
                print_document_statistics(stats)
        
        # Extract specific object types if requested
        result = {}
        if args.text_only:
            result['text_objects'] = doc_manager.get_text_objects()
        elif args.tables_only:
            result['table_objects'] = doc_manager.get_table_objects()
        elif args.pictures_only:
            result['picture_objects'] = doc_manager.get_picture_objects()
        elif args.graphics_only:
            result['graphic_objects'] = doc_manager.get_graphic_objects()
        else:
            result = document_data
        
        # Save output to file if specified
        if args.output:
            if not args.output.endswith('.json'):
                args.output += '.json'
            
            doc_manager.export_to_json(args.output)
            logger.info(f"Output saved to: {args.output}")
        elif not args.quiet and not args.json_response:
            # Print summary to console
            print_document_summary(document_data)
        
        # Output JSON response if requested
        if args.json_response:
            response = format_success_response(result)
            if args.stats:
                response['statistics'] = doc_manager.get_document_statistics()
            output_json_response(response)
        
        return 0
        
    except DocParserError as e:
        logger.error(f"{e.__class__.__name__}: {e.message}")
        if args.json_response:
            output_json_response(format_error_response(e))
        elif not args.quiet:
            print(f"Error: {e.message}", file=sys.stderr)
        return 1
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if args.verbose:
            logger.debug(traceback.format_exc())
        
        if args.json_response:
            output_json_response(format_error_response(e))
        elif not args.quiet:
            print(f"Error: {str(e)}", file=sys.stderr)
            if args.verbose:
                traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
