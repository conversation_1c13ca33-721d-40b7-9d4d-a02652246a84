# SDW工作流编码规范

## 1. 文件结构与命名

### 1.1 目录结构
每个工作流应遵循以下目录结构:
```
workflow_name/
├── __init__.py         # 导出公共接口
├── config.yaml         # 工作流配置文件
├── models.py           # 数据模型定义
├── workflow_name.py    # 主要工作流实现
├── README.md           # 工作流说明文档
└── utils/              # (可选)工作流特定的工具函数
```

### 1.2 命名约定
- 目录名称：使用蛇形命名法(snake_case)，如`requirement_cs`、`module_domain_test`
- 文件名称：使用蛇形命名法(snake_case)，如`requirement_cs.py`、`models.py`
- 类名：使用大驼峰命名法(PascalCase)，并以`Workflow`结尾，如`RequirementCSWorkflow`
- 方法和函数：使用蛇形命名法(snake_case)，如`process_requirement_analysis`
- 常量：使用大写蛇形命名法(UPPER_SNAKE_CASE)，如`DEFAULT_OUTPUT_FILE`

## 2. 工作流实现规范

### 2.1 继承基类
所有工作流必须继承自`BaseWorkflow`基类：
```python
from sdw_agent.service import BaseWorkflow, register_workflow

@register_workflow("workflow_name")
class MyWorkflow(BaseWorkflow):
    def __init__(self, config_path=None):
        super().__init__(config_path)
        # 初始化代码
```

### 2.2 必须实现的方法
每个工作流必须实现以下方法：

1. `execute`方法：工作流的核心逻辑
```python
def execute(self, *args, **kwargs) -> WorkflowResult:
    """
    执行工作流核心逻辑
    
    Args:
        根据需要定义参数
        
    Returns:
        WorkflowResult: 工作流执行结果
    """
    # 核心逻辑实现
    return WorkflowResult(
        status=WorkflowStatus.SUCCESS,
        message="执行成功消息",
        data={"key": "value"}  # 可选的返回数据
    )
```

2. `validate_input`方法：验证输入参数
```python
def validate_input(self, *args, **kwargs) -> bool:
    """
    验证输入参数
    
    Args:
        根据需要定义参数
        
    Returns:
        bool: 验证是否通过
    """
    # 验证逻辑
    return True  # 或 False
```

### 2.3 推荐实现的方法
建议实现以下方法以提高代码可维护性：

1. `pre_execute`方法：执行前准备工作
2. `post_execute`方法：执行后清理工作
3. `register_config_model`方法：注册配置模型

### 2.4 日志规范
使用`self.logger`而不是直接使用`logger`：

```python
# 正确的做法
self.logger.info("开始执行XX操作")

# 错误的做法
logger.info("开始执行XX操作")
```

日志级别使用规范：
- `debug`：详细的调试信息
- `info`：普通信息，表示正常的程序执行
- `warning`：警告信息，不会导致程序失败但需要注意
- `error`：错误信息，会导致部分功能失败
- `critical`：严重错误，会导致程序无法继续执行

### 2.5 异常处理
使用异常处理来管理错误：

```python
try:
    # 可能出错的代码
    result = self.parse_file(file_path)
except FileNotFoundError:
    self.logger.error(f"文件不存在: {file_path}")
    return WorkflowResult(
        status=WorkflowStatus.FAILED,
        message="文件不存在",
        error=f"文件不存在: {file_path}"
    )
except Exception as e:
    self.logger.exception(f"解析文件失败: {str(e)}")
    return WorkflowResult(
        status=WorkflowStatus.FAILED,
        message="解析文件失败",
        error=str(e)
    )
```

## 3. 配置管理规范

### 3.1 配置文件格式
使用YAML格式编写配置文件，包含以下基本结构：

```yaml
# 基本配置
name: "工作流名称"
description: "工作流描述"
version: "1.0.0"
author: "作者"

# 模块特定配置
module_specific:
  setting1: value1
  setting2: value2
```

### 3.2 配置访问
通过`self.config`访问配置值，使用默认值防止配置缺失：

```python
# 获取配置值，提供默认值
timeout = self.config.get("module_specific", {}).get("timeout", 30)
```

### 3.3 配置验证
使用Pydantic模型验证配置：

```python
# 在models.py中
class ConfigModel(BaseModel):
    name: str
    timeout: int = Field(default=30, ge=1, le=3600)
    
# 在工作流中
def register_config_model(self):
    from sdw_agent.service.workflow_config import WorkflowConfigManager
    config_manager = WorkflowConfigManager(workflow_name="my_workflow")
    config_manager.register_schema("my_workflow", ConfigModel)
```

## 4. 数据模型规范

### 4.1 使用Pydantic模型
为工作流中使用的数据结构定义Pydantic模型：

```python
# 在models.py中
from pydantic import BaseModel, Field

class InputData(BaseModel):
    file_path: str
    options: Dict[str, Any] = Field(default_factory=dict)
    
class OutputData(BaseModel):
    result: List[Dict[str, Any]]
    summary: str
```

### 4.2 返回结果标准化
统一使用`WorkflowResult`封装返回结果：

```python
return WorkflowResult(
    status=WorkflowStatus.SUCCESS,  # 或FAILED, WARNING等
    message="执行成功",
    data={
        "output_file": "path/to/file.xlsx",
        "summary": "处理了100条记录"
    }
)
```

## 5. 代码文档规范

### 5.1 文件头注释
每个Python文件开头应包含如下注释：

```python
"""
模块名称

V字对应：
x.x 对应的V字阶段
x. 对应的V字项目

模块简介和主要功能说明...

主要功能：
1. 功能1说明
2. 功能2说明
...
"""
```

### 5.2 函数和方法注释
使用Google风格的文档字符串：

```python
def process_file(self, file_path: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    处理文件的函数描述
    
    Args:
        file_path: 文件路径说明
        options: 选项参数说明，默认为None
        
    Returns:
        Dict[str, Any]: 返回结果说明
        
    Raises:
        FileNotFoundError: 当文件不存在时
        ValueError: 当参数无效时
    """
    # 函数实现
```

### 5.3 类注释
类级别的文档字符串：

```python
class FileProcessor:
    """
    文件处理器类
    
    负责处理各种格式的文件，提供转换、解析等功能。
    主要用于XX场景。
    """
```

## 6. 测试规范

### 6.1 单元测试
每个工作流应编写单元测试，放在`tests`目录下：

```
workflow_name/
├── tests/
│   ├── __init__.py
│   ├── test_workflow.py    # 测试主工作流
│   └── test_utils.py       # 测试工具函数
```

### 6.2 测试用例
使用pytest编写测试用例：

```python
def test_validate_input():
    workflow = MyWorkflow()
    # 测试有效输入
    assert workflow.validate_input("valid_file.xlsx", "output_dir")
    # 测试无效输入
    assert not workflow.validate_input("not_exists.xlsx", "output_dir")
```

### 6.3 模拟数据
使用固定的测试数据集：

```python
# 在tests/conftest.py中
@pytest.fixture
def sample_data():
    return {
        "input": {...},
        "expected_output": {...}
    }
```

## 7. 代码风格规范

### 7.1 代码格式化
使用Black进行代码格式化，配置如下：
- 行长度：88字符
- 字符串引号：双引号优先

### 7.2 导入顺序
按照以下顺序组织导入语句：
1. 标准库导入
2. 相关第三方库导入
3. 本地应用/库特定导入

```python
# 标准库
import os
import pathlib
from typing import Dict, List, Any

# 第三方库
import pandas as pd
from loguru import logger
from pydantic import BaseModel

# 本地应用
from sdw_agent.service import BaseWorkflow
from .models import InputModel, OutputModel
```

### 7.3 行间距规范
- 类之间空两行
- 方法之间空一行
- 逻辑相关的代码块之间不空行
- 逻辑无关的代码块之间空一行

## 8. 协作开发规范

### 8.1 分支管理
- `main`: 主分支，保持稳定
- `develop`: 开发分支
- `feature/xxx`: 功能分支
- `bugfix/xxx`: 修复分支

### 8.2 提交规范
提交消息格式：
```
<类型>: <简短描述>

<详细描述>
```

类型包括：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码风格变更
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变更

### 8.3 代码评审
所有合并到主分支的代码必须经过至少一次代码评审。
评审重点：
- 功能正确性
- 代码可读性
- 架构设计
- 性能考虑
- 安全性

## 9. 性能优化建议

### 9.1 资源密集型操作
对于资源密集型操作，考虑以下优化：
- 分批处理大数据集
- 使用生成器处理大文件
- 并行处理独立任务

```python
# 分批处理示例
def process_large_dataset(self, data):
    batch_size = 1000
    for i in range(0, len(data), batch_size):
        batch = data[i:i+batch_size]
        self._process_batch(batch)
```

### 9.2 缓存机制
对于重复计算，考虑使用缓存：

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def expensive_calculation(self, input_data):
    # 复杂计算
    return result
```

## 10. 安全性考虑

### 10.1 输入验证
验证所有外部输入：

```python
def process_user_input(self, user_input: str):
    # 验证输入长度
    if len(user_input) > MAX_INPUT_LENGTH:
        raise ValueError("输入超过最大长度限制")
        
    # 验证输入格式
    if not self._is_valid_format(user_input):
        raise ValueError("输入格式无效")
```

### 10.2 敏感信息处理
不要在日志中记录敏感信息：

```python
# 错误的做法
self.logger.info(f"处理用户数据，密码: {password}")

# 正确的做法
self.logger.info(f"处理用户数据，用户ID: {user_id}")
```

### 10.3 文件操作安全
使用安全的文件操作方式：

```python
# 使用pathlib而非字符串拼接
file_path = pathlib.Path(safe_directory) / safe_filename

# 验证路径不会超出预期目录
if not str(file_path.resolve()).startswith(str(safe_directory)):
    raise ValueError("无效的文件路径")
```