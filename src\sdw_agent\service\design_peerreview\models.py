

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

@dataclass
class BasicDesignInput:
    """工作流输入模型"""

    # 初始化逻辑
    def __init__(self, dir_path="", peer_review_type=0x01):
        self.dir_path = dir_path
        self.peer_review_type = peer_review_type
    # examples=["D:/path/"]
    dir_path: str
    # examples=["0x01：基本设计 0x02：详细设计 0x04：coding review"]
    peer_review_type: int



@dataclass
class CheckRangeIninfo:
    def __init__(self):
        self.range_address = ""
        self.range_not_included = []
        self.check_direction = "down"
        self.check_direction_enable = False
    """工作流输入模型"""
    # examples=["A1:C6"]
    range_address: str
    # examples=["A1:A1","C:C","1:1"，'A1:C6']
    range_not_included:List[str]
    # examples=["0x01：基本设计 0x02：详细设计 0x04：coding review"]
    check_direction_enable: bool
    # examples=["right,down"]
    check_direction: str


class ConfirmFormat(BaseModel):
    """
    提取变更内容
    """
    confirm_result: str = Field(
        description="擅长根据【错误列表】，总结出错误原因，输出一句话精简的错误原因")
