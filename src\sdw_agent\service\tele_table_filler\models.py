"""
数据模型定义模块

V字对应：
4.4 对应的V字阶段
64. 对应的V字项目

模块简介：定义工作流中使用的数据结构，使用Pydantic模型。

主要功能：
1. 定义输入配置模型
2. 定义输出结果模型
3. 定义RAG查询结果模型
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

class ConfigModel(BaseModel):
    """
    工作流配置模型
    """
    rag_chunk_line: int = Field(default=10, ge=1, le=100, description="RAG分块行数")
    rag_max_retries: int = Field(default=2, ge=0, le=5, description="RAG查询重试次数")
    batch_size: int = Field(default=50, ge=1, le=1000, description="批量处理大小")
    cache_enabled: bool = Field(default=True, description="是否启用缓存")
    fuzzy_match: bool = Field(default=True, description="是否启用模糊匹配")
    truncate_dash_count: int = Field(default=3, ge=0, le=10, description="截断'-'次数")
    match_strategy: str = Field(default="truncate", description="匹配策略")
    debug_enabled: bool = Field(default=True, description="是否启用调试")

class InputModel(BaseModel):
    """
    输入数据模型
    """
    file_path: str = Field(..., description="Excel文件路径")
    source_dir: str = Field(..., description="RAG源目录")
    config: Optional[Dict[str, Any]] = Field(default=None, description="自定义配置")

class OutputModel(BaseModel):
    """
    输出数据模型
    """
    filled_path: str = Field(..., description="填充后的文件路径")
    report_path: str = Field(..., description="调试报告路径")
    filled_rows: int = Field(..., description="填充的行数")
    status: str = Field(..., description="处理状态")