"""
RAM干涉工作流辅助工具

提供代码分析、中断表处理等辅助功能
"""

from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path

from loguru import logger

from sdw_agent.util.extract_c_function_calling import CFunctionCallAnalyzer
from sdw_agent.util.extract_code_util import CodeAnalyzer
from sdw_agent.util.file_base_util import create_text_file_link
from sdw_agent.util.git_util import get_git_structured_diff

from sdw_agent.service.ram_interfere_service.models import GlobalVarChangeInfo
from sdw_agent.util.excel.core import ExcelUtil

# 定义调度器任务名称常量
SCHEDULER_TASK_INTERRUPT = "vd_g_SchdlrMainTask"


def get_interrupt_info(schdlr_rglr_tasks: Set[str], interrupt_table: Dict[str, int], func_name: str) -> Tu<PERSON>[
    Optional[str], int]:
    """
    获取函数的中断信息
    
    Args:
        schdlr_rglr_tasks: 调度任务集合
        interrupt_table: 中断表
        func_name: 函数名
        
    Returns:
        Tuple: (中断函数名, 中断地址)
    """
    # 调试日志，记录函数名
    logger.debug(f"检查函数中断信息: {func_name}")

    # 如果函数名在调度任务集合中找到，则返回调度任务的中断信息
    if func_name in schdlr_rglr_tasks:
        result = (SCHEDULER_TASK_INTERRUPT, interrupt_table.get(SCHEDULER_TASK_INTERRUPT, 0))
        logger.info(f"在调度任务中找到函数: {func_name} -> {result}")
        return result
    # 如果函数名在中断表中直接找到，则返回其中断信息
    elif func_name in interrupt_table:
        result = (func_name, interrupt_table.get(func_name, 0))
        logger.info(f"在中断表中找到函数: {func_name} -> {result}")
        return result
    # 如果函数名既不在调度任务中也不在中断表中，则返回默认值
    else:
        logger.debug(f"未找到函数的中断信息: {func_name}")
        return None, 0


def get_interrupt_table_func(
        schdlr_rglr_tasks: Set[str],
        interrupt_table: Dict[str, int],
        repo_path: str,
        func_name: str,
        _visited: Optional[Set[str]] = None,
        _func_cache: Optional[Dict[str, List[str]]] = None
) -> Dict[str, Any]:
    """
    获取函数的中断表信息（优化版：只返回最终中断源）
    
    Args:
        schdlr_rglr_tasks: 调度任务集合
        interrupt_table: 中断表
        repo_path: 仓库路径
        func_name: 函数名
        _visited: 已访问的函数集合（用于防止无限递归）
        _func_cache: 函数调用缓存
        
    Returns:
        Dict[str, Any]: 中断信息
    """
    # 初始化参数
    if _visited is None:
        _visited = set()
    if _func_cache is None:
        _func_cache = {}

    # 防止无限递归
    if func_name in _visited:
        return {}
    _visited.add(func_name)

    try:
        # 1. 首先检查当前函数是否直接在中断表中
        interrupt_info = get_interrupt_info(schdlr_rglr_tasks, interrupt_table, func_name)
        if interrupt_info[0] is not None:
            logger.info(f"找到函数的直接中断: {func_name} -> {interrupt_info}")
            return {
                "interrupt_func": interrupt_info[0],
                "interrupt_addr": interrupt_info[1]
            }

        # 2. 如果不在中断表中，查找调用它的函数
        if func_name not in _func_cache:
            analyzer = CFunctionCallAnalyzer(
                root_path=repo_path,
                target_function=func_name,
                debug=False
            )
            _func_cache[func_name] = analyzer.get_caller_names()

        calling_funcs = [f for f in _func_cache[func_name] if f != func_name and f not in _visited]

        if not calling_funcs:
            logger.debug(f"函数 {func_name} 没有找到调用者，可能是入口函数")
            return {}

        # 3. 递归查找第一个有效的中断源（广度优先，找到即返回）
        for calling_func in calling_funcs:
            result = get_interrupt_table_func(
                schdlr_rglr_tasks,
                interrupt_table,
                repo_path,
                calling_func,
                _visited.copy(),  # 使用副本避免影响其他分支
                _func_cache
            )
            if result:  # 找到中断源就立即返回
                logger.info(f"通过调用链找到函数 {func_name} 的中断: {result}")
                return result

        return {}

    except Exception as e:
        logger.error(f"获取函数中断表信息失败: {func_name}, 错误: {str(e)}")
        return {}


def get_interrupt_table_func_batch(
        schdlr_rglr_tasks: Set[str],
        interrupt_table: Dict[str, int],
        repo_path: str,
        func_names: set[str]
) -> Dict[str, Dict[str, Any]]:
    """
    批量获取函数的中断表信息
    
    Args:
        schdlr_rglr_tasks: 调度任务集合
        interrupt_table: 中断表
        repo_path: 仓库路径
        func_names: 函数名列表
        
    Returns:
        Dict[str, Dict[str, Any]]: 函数到中断信息列表的映射
    """
    # 初始化结果字典和函数缓存
    results = {}
    _func_cache = {}

    # 遍历函数名列表
    for func_name in func_names:
        # 忽略空的函数名
        if not func_name:
            continue

        # 调用get_interrupt_table_func函数获取每个函数的中断信息，并存储在结果字典中
        interrupt_info = get_interrupt_table_func(
            schdlr_rglr_tasks,
            interrupt_table,
            repo_path,
            func_name,
            None,
            _func_cache
        )
        if not interrupt_info:
            interrupt_info = {
                "interrupt_func": SCHEDULER_TASK_INTERRUPT,
                "interrupt_addr": interrupt_table.get(SCHEDULER_TASK_INTERRUPT)
            }

        results[func_name] = interrupt_info

    # 返回包含所有函数中断信息的字典
    return results


def get_diff_code(repo_path, commit_id, compared_commit_id=None):
    """
    根据commit获取变更代码及所在文件的全量代码

    参数:
    - repo_path: 仓库路径
    - commit_id: 需要比较的commit ID
    - compared_commit_id: 可选的比较对象commit ID，默认为None，如果提供，则比较两个commit之间的差异

    返回:
    - code_diffs: 包含变更代码和全量代码的列表，每个元素对应一个文件
    """
    # 获取结构化的diff信息
    diffs = get_git_structured_diff(repo_path, commit_id, compared_commit_id)
    code_diffs = []

    # 遍历每个文件的diff信息
    for file_path, diff_info in diffs.items():
        added_lines = []
        full_code = []
        hunks = diff_info.get("diff", [])

        # 遍历文件中的每个hunk
        for hunk in hunks:
            content = hunk.get('content', [])
            if hunk['type'] == 'ab':
                # 公共部分，加入全量代码
                full_code.extend(content)
            elif hunk['type'] == 'b':
                # 新增代码，加入新增列表和全量代码
                added_lines.extend(content)
                full_code.extend(content)
            else:
                # 其他类型（如 a: 删除）仅用于构建全量代码
                full_code.extend(content)

        # 将文件路径、新增代码和全量代码打包成字典，添加到结果列表中
        code_diffs.append({
            "file_path": diff_info["file_path"],
            "added_lines": added_lines,
            "full_code": full_code,
            "line_num": diff_info.get("changed_lines", {}).get("added", [])
        })

    return code_diffs


def search_global_var_from_diff(
        repo_path: str,
        global_vars_info: Dict[str, Tuple[List[str], str]],
        code_diff: List[Dict[str, Any]]
) -> List[GlobalVarChangeInfo]:
    """
    从代码差异中查找全局变量变更
    
    Args:
        repo_path: 仓库路径
        global_vars_info: 全局变量信息
        code_diff: 代码差异
        
    Returns:
        List[GlobalVarChangeInfo]: 全局变量变更信息列表
    """
    # 日志记录开始查找全局变量变更
    logger.info("从代码差异中查找全局变量变更")
    # 初始化用于存储变更的全局变量列表
    changed_vars = []

    # 遍历代码差异中的每个文件信息
    for file_info in code_diff:
        # 获取文件路径、新增行和行号
        file_path = file_info.get('file_path')
        added_lines = file_info.get('added_lines', [])
        line_nums = file_info.get('line_num', [])

        # 如果文件路径不存在或不在全局变量信息中，或没有相关的全局变量信息，则跳过
        if not file_path or file_path not in global_vars_info or not global_vars_info[file_path]:
            continue

        # 获取当前文件的全局变量和完整内容
        file_vars, full_content = global_vars_info[file_path]

        # 遍历当前文件中的每个变量
        for var_name in file_vars:
            # 检查变量是否在新增行中
            for index, line in enumerate(added_lines):
                # 如果变量名不在当前行，则跳过
                if var_name not in line:
                    continue

                try:
                    # 获取当前行的行号
                    line_num = line_nums[index]
                    # 根据行号找到变更所在的函数
                    code_analyzer = CodeAnalyzer()
                    change_func = code_analyzer.find_function_by_line(Path(repo_path) / file_path, line_num)
                    called_func = code_analyzer.find_all_functions(repo_path, var_name)
                    # 将变量变更信息添加到列表中
                    changed_vars.append(GlobalVarChangeInfo(
                        name=var_name,
                        content=line,
                        line=line_num,
                        file_path=file_path,
                        change_func=change_func,
                        called_func=called_func
                    ))

                except Exception as e:
                    # 记录处理变量变更时的错误
                    logger.error(f"处理变量变更失败: {var_name}, 错误: {str(e)}")

    # 返回全局变量变更信息列表
    return changed_vars


def search_global_var(
        repo_path: str,
        commit_id: str,
        compared_commit_id: Optional[str] = None
) -> List[GlobalVarChangeInfo]:
    """
    搜索全局变量变更
    
    Args:
        repo_path: 仓库路径
        commit_id: 提交ID
        compared_commit_id: 对比提交ID
        
    Returns:
        List[GlobalVarChangeInfo]: 变更的全局变量列表
    """
    try:
        # 开始搜索全局变量变更的日志记录
        logger.info("开始搜索全局变量变更")

        # 获取代码差异
        code_diff = get_diff_code(repo_path, commit_id, compared_commit_id)
        # 如果没有代码差异，则直接返回空列表
        if not code_diff:
            return []

        # 提取全局变量信息
        global_vars_info = {}
        for file_diff in code_diff:
            # 获取文件路径
            file_path = file_diff.get('file_path')
            if file_path:
                # 获取文件的完整代码
                full_code = file_diff.get('full_code', [])
                # 从完整代码中搜索全局变量
                global_vars = CodeAnalyzer().search_global_vars_name(full_code)
                # 将全局变量信息和代码内容保存到字典中
                global_vars_info[file_path] = (global_vars, '\n'.join(full_code))

        # 查找修改的全局变量
        changed_vars = search_global_var_from_diff(
            repo_path, global_vars_info, code_diff
        )

        # 搜索完成后记录日志
        logger.info(f"搜索全局变量变更完成，找到{len(changed_vars)}个变更")
        # 返回变更的全局变量列表
        return changed_vars

    except Exception as e:
        # 搜索全局变量变更失败时记录错误日志
        logger.error(f"搜索全局变量变更失败: {e}")
        # 返回空列表
        return []


def gen_ram_interfere_data(
        schdlr_rglr_tasks: Set[str],
        interrupt_table: Dict[str, int],
        repo_path: str,
        global_vars_info: List[GlobalVarChangeInfo]
) -> List[List[str]]:
    """
    生成RAM干涉数据
    
    Args:
        schdlr_rglr_tasks: 调度任务集合
        interrupt_table: 中断表
        repo_path: 仓库路径
        global_vars_info: 全局变量变更信息
        
    Returns:
        List[List[str]]: RAM干涉数据
    """
    results = []

    # 收集需要查找中断表信息的函数
    func_names = []
    for var in global_vars_info:
        func_names.extend([item.get("function_name") for item in var.called_func])
    func_names = set(func_names)
    # 批量查找中断表信息
    interrupt_results = get_interrupt_table_func_batch(
        schdlr_rglr_tasks, interrupt_table, repo_path, func_names
    )

    # 获取中断信息并生成结果行
    for var_info in global_vars_info:
        # 提取变量信息
        name = var_info.name
        change_func = var_info.change_func
        file_path = var_info.file_path
        line = var_info.line
        content = var_info.content
        called_func = var_info.called_func
        text_file_link = create_text_file_link(str(Path(repo_path) / file_path), line, "vscode")
        # 构建基础结果行
        result_row = [
            name,  # 变量名
            "",  # 中断函数（待填充）
            "",  # 中断地址（待填充）
            file_path,  # 文件路径
            change_func or "变更变量的调用函数未找到",  # 变更函数
            {"url": text_file_link, "text": str(line)},  # 行号
            content,  # 内容
            "",  # 备注
            ""  # 结果
        ]

        # 填充中断信息（如果存在）
        if change_func and change_func in interrupt_results:
            interrupt_info = interrupt_results[change_func]
            result_row[1] = interrupt_info.get("interrupt_func", "")
            result_row[2] = str(interrupt_info.get("interrupt_addr", ""))

        else:
            result_row[1] = "中断函数未找到"

        chk_result = get_chk_result(interrupt_results, change_func, called_func)
        for item in chk_result:
            result_row_copy = result_row.copy()
            result_row_copy[7] = item[0]
            result_row_copy[8] = item[1]
            # 添加到结果列表
            results.append(result_row_copy)
    return results


def get_chk_result(interrupt_results, change_func, called_func):
    """
    检查RAM干涉结果
    
    Args:
        interrupt_results: 中断结果字典
        change_func: 变更函数名
        called_func: 调用函数列表
        
    Returns:
        List[Tuple[str, str]]: 检查结果列表，每项为(检查类型, 结果状态)
    """
    logger.info(f"{change_func} is in interrupt table")

    # 提取所有调用函数的函数名
    called_func_names = [func.get("function_name") for func in called_func if func.get("function_name")]

    # 如果called_func只包含change_func，返回OK
    if len(called_func_names) == 1 and called_func_names[0] == change_func:
        return [("-", "OK")]

    # 获取change_func的中断优先级
    change_func_priority = None
    if change_func in interrupt_results:
        change_func_priority = int(interrupt_results[change_func].get("interrupt_addr"), 16)  # 转换16进制

    # 确定change_func自身的读写操作类型
    change_func_is_write = False
    change_func_is_read = False

    for func_info in called_func:
        if func_info.get("function_name") == change_func:
            line_content = func_info.get("line_content", "")
            change_func_is_write = "=" in line_content and not ("==" in line_content or "!=" in line_content)
            change_func_is_read = not change_func_is_write
            break

    # 分析其他函数的中断优先级和操作类型
    has_higher_priority_write = False
    has_lower_priority_write = False
    has_lower_priority_multi_read = False
    has_lower_priority_read_write = False
    has_higher_priority_read = False
    has_lower_priority_multi_write = False
    result = []

    # 统计各函数的读写次数
    func_read_counts = {}
    func_write_counts = {}

    # 先统计各函数的读写次数
    for func_info in called_func:
        func_name = func_info.get("function_name")
        if not func_name or func_name == change_func:
            continue

        line_content = func_info.get("line_content", "")
        is_write = "=" in line_content and not ("==" in line_content or "!=" in line_content)

        if is_write:
            func_write_counts[func_name] = func_write_counts.get(func_name, 0) + 1
        else:
            func_read_counts[func_name] = func_read_counts.get(func_name, 0) + 1

    # 分析每个函数与change_func的关系
    for func_name in set(func_read_counts.keys()) | set(func_write_counts.keys()):
        # 获取函数的中断优先级
        func_priority = None
        if func_name in interrupt_results:
            func_priority = int(interrupt_results[func_name].get("interrupt_addr"), 16)  # 转换16进制

        # 比较优先级（中断值越大优先级越高，为上位）
        if change_func_priority is not None and func_priority is not None:
            read_count = func_read_counts.get(func_name, 0)
            write_count = func_write_counts.get(func_name, 0)

            if change_func_priority < func_priority:  # change_func为上位，func_name为下位
                if write_count > 0:
                    has_lower_priority_write = True
                if read_count >= 2:
                    has_lower_priority_multi_read = True
                if read_count > 0 and write_count > 0:
                    has_lower_priority_read_write = True
                if write_count >= 2:
                    has_lower_priority_multi_write = True

            elif change_func_priority > func_priority:  # change_func为下位，func_name为上位
                if write_count > 0:
                    has_higher_priority_write = True
                if read_count > 0:
                    has_higher_priority_read = True

    logger.info(f"change_func_is_write: {change_func_is_write}, change_func_is_read: {change_func_is_read}")
    logger.info(f"has_higher_priority_write: {has_higher_priority_write}")
    logger.info(f"has_lower_priority_write: {has_lower_priority_write}")
    logger.info(f"has_lower_priority_multi_read: {has_lower_priority_multi_read}")
    logger.info(f"has_lower_priority_read_write: {has_lower_priority_read_write}")
    logger.info(f"has_higher_priority_read: {has_higher_priority_read}")
    logger.info(f"has_lower_priority_multi_write: {has_lower_priority_multi_write}")

    # 根据条件确定chk类型
    # chk1: 上位写入 + 下位写入
    if change_func_is_write and has_lower_priority_write:
        result.append(("chk1", "NG"))

    # chk3: 上位写入 + 下位多次读取
    if change_func_is_write and has_lower_priority_multi_read:
        result.append(("chk3", "NG"))

    # chk5: 上位写入 + 下位读写
    if change_func_is_write and has_lower_priority_read_write:
        result.append(("chk5", "NG"))

    # chk6: 上位读取 + 下位多次写入
    if change_func_is_read and has_lower_priority_multi_write:
        result.append(("chk6", "NG"))

    # 默认情况
    if not result:
        result.append(("-", "OK"))

    return result


class RAMInterfereExcelUtil(ExcelUtil):
    """
    RAM干涉服务专用Excel工具类，继承自ExcelUtil
    
    提供RAM干涉数据的Excel操作功能，包括：
    1. 基于模板创建工作表
    2. 填充干涉数据
    3. 应用特定样式
    4. 处理特殊列格式
    5. 自动化报告生成
    """

    def __init__(self, output_file: str, template_file: Optional[str],
                 excel_style: Optional[Dict[str, Any]], engine: str = "win32com"):
        """
        初始化RAM干涉Excel工具

        Args:
            output_file: Excel文件保存路径
            template_file: 模板文件路径
            excel_style: Excel样式配置
            engine: 操作引擎类型（默认win32com保护宏和图片）
        """
        # 调用父类初始化方法
        super().__init__(template_file, engine=engine, auto_create=False)

        # 设置文件路径
        self.template_file = Path(template_file)
        self.output_file = Path(output_file)

        # 设置样式配置
        self.excel_style = excel_style or {}
        self._init_style_config()

        # 记录初始化信息
        logger.info(f"初始化RAM干涉Excel工具 - 输出: {self.output_file}, 模板: {self.template_file}")

    def _init_style_config(self) -> None:
        """
        初始化样式配置
        该方法用于初始化Excel样式配置，包括默认工作表名称和数据开始的行号和列号
        """
        # 设置默认工作表名称，如果配置中没有则使用预定义的名称
        self.default_sheet = self.excel_style.get("default_sheet", "干渉CS（実施日DD.MM.YY）")
        # 设置数据开始的行号，如果配置中没有则使用默认值21
        self.data_start_row = self.excel_style.get("data_start_row", 21)
        # 设置数据开始的列号，如果配置中没有则使用默认值2
        self.data_start_col = self.excel_style.get("data_start_col", 2)

        # 记录调试信息，说明样式配置的默认工作表名称和数据起始行号
        logger.debug(f"样式配置 - 默认工作表: {self.default_sheet}, 数据起始行: {self.data_start_row}")

    def save_interfere_data(self, interfere_data: List[List[str]]) -> None:
        """
        保存干涉数据到Excel文件

        Args:
            interfere_data: 包含干涉数据的列表
        """
        try:
            # 检查模板文件是否存在
            if not self.template_file.exists():
                raise FileNotFoundError(f"模板文件 {self.template_file} 不存在")

            logger.info(f"使用模板文件: {self.template_file}")

            # 填充数据到工作表
            self._fill_interfere_data(interfere_data)

            # 保存到输出文件
            self.save(str(self.output_file))

            logger.info(f"RAM干涉数据已保存到 {self.output_file}")

        except Exception as e:
            logger.error(f"保存Excel文件失败: {str(e)}")
            raise

    def _fill_interfere_data(self, interfere_data: List[List[str]]) -> None:
        """
        填充干涉数据到工作表

        Args:
            interfere_data: 干涉数据列表
        """
        try:
            # 写入数据到指定工作表
            for i, row_data in enumerate(interfere_data):
                row_num = self.data_start_row + i
                for j, cell_value in enumerate(row_data, start=self.data_start_col):
                    # 检查是否为超链接格式
                    if isinstance(cell_value, dict) and 'url' in cell_value:
                        # 创建超链接
                        url = cell_value['url']
                        display_text = cell_value.get('text', url)
                        self.write_hyperlink(self.default_sheet, row_num, j, url, display_text)
                    else:
                        # 普通内容
                        self.write_cell(self.default_sheet, row_num, j, cell_value, value_must_be_safe=True)

            logger.info(f"已填充 {len(interfere_data)} 行数据到工作表 {self.default_sheet}")

        except Exception as e:
            logger.error(f"填充数据失败: {str(e)}")
            raise
