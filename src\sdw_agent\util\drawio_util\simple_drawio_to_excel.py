#!/usr/bin/env python3
"""
简化版 Draw.io 导出到 Excel 工具
不依赖 drawio-desktop，使用其他方法将 drawio 内容插入 Excel
"""

import os
import base64
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any
import xml.etree.ElementTree as ET

from loguru import logger
from PIL import Image, ImageDraw, ImageFont
import io

from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.util.drawio_util.drawio_structure_reader import DrawioStructureReader


class SimpleDrawioToExcelExporter:
    """简化版 Draw.io 到 Excel 导出器"""
    
    def __init__(self):
        """初始化导出器"""
        pass
    
    def extract_embedded_images(self, drawio_file: str) -> list:
        """
        从 Draw.io 文件中提取嵌入的图像
        
        Args:
            drawio_file: Draw.io 文件路径
            
        Returns:
            提取的图像列表
        """
        try:
            tree = ET.parse(drawio_file)
            root = tree.getroot()
            
            images = []
            
            # 查找所有包含图像数据的元素
            for elem in root.iter():
                # 检查是否有 base64 编码的图像数据
                if elem.text and elem.text.startswith('data:image/'):
                    try:
                        # 解析 data URL
                        header, data = elem.text.split(',', 1)
                        image_data = base64.b64decode(data)
                        
                        # 创建 PIL 图像
                        img = Image.open(io.BytesIO(image_data))
                        images.append(img)
                        
                    except Exception as e:
                        logger.warning(f"解析嵌入图像失败: {e}")
            
            return images
            
        except Exception as e:
            logger.error(f"提取嵌入图像失败: {e}")
            return []
    
    def create_structure_diagram(self, drawio_file: str, output_image: str) -> bool:
        """
        根据 Draw.io 结构创建简化的结构图
        
        Args:
            drawio_file: Draw.io 文件路径
            output_image: 输出图像路径
            
        Returns:
            是否成功创建
        """
        try:
            # 1. 解析 Draw.io 结构
            reader = DrawioStructureReader(drawio_file)
            if not reader.parse():
                logger.error("解析 Draw.io 文件失败")
                return False
            
            # 2. 创建图像
            img_width = 1200
            img_height = 800
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 3. 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 12)
                title_font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
            
            # 4. 绘制标题
            title = f"Draw.io 结构图 ({len(reader.modules)} 个模块)"
            draw.text((20, 20), title, fill='black', font=title_font)
            
            # 5. 绘制模块结构
            y_offset = 60
            x_offset = 20
            
            # 按层级绘制模块
            for level in range(5):  # 最多显示5层
                level_modules = [m for m in reader.modules.values() if m.level == level]
                if not level_modules:
                    continue
                
                # 绘制层级标题
                level_title = f"Level {level} ({len(level_modules)} 模块)"
                draw.text((x_offset, y_offset), level_title, fill='blue', font=font)
                y_offset += 25
                
                # 绘制模块
                for i, module in enumerate(level_modules[:20]):  # 最多显示20个模块
                    module_text = f"• {module.name}"
                    if module.path:
                        module_text += f" ({module.path})"
                    
                    # 限制文本长度
                    if len(module_text) > 80:
                        module_text = module_text[:77] + "..."
                    
                    draw.text((x_offset + 20, y_offset), module_text, fill='black', font=font)
                    y_offset += 20
                    
                    if y_offset > img_height - 50:
                        break
                
                y_offset += 10
                if y_offset > img_height - 50:
                    break
            
            # 6. 添加统计信息
            stats = reader.get_statistics()
            stats_text = [
                f"总模块数: {stats['total_modules']}",
                f"根模块数: {stats['root_modules']}",
                f"最大层级: {stats['max_level']}",
                f"小文本框: {stats['small_textboxes']}",
                f"常规模块: {stats['regular_modules']}"
            ]
            
            stats_y = img_height - 120
            for stat in stats_text:
                draw.text((img_width - 250, stats_y), stat, fill='gray', font=font)
                stats_y += 20
            
            # 7. 保存图像
            img.save(output_image, 'PNG')
            logger.success(f"结构图已保存: {output_image}")
            return True
            
        except Exception as e:
            logger.error(f"创建结构图失败: {e}")
            return False
    
    def insert_drawio_structure_to_excel(self,
                                       drawio_file: str,
                                       excel_file: str,
                                       sheet_name: str = "Draw.io结构",
                                       include_structure_image: bool = True,
                                       include_module_list: bool = True) -> Dict[str, Any]:
        """
        将 Draw.io 结构信息插入到 Excel 中
        
        Args:
            drawio_file: Draw.io 文件路径
            excel_file: Excel 文件路径
            sheet_name: 工作表名称
            include_structure_image: 是否包含结构图像
            include_module_list: 是否包含模块列表
            
        Returns:
            操作结果
        """
        try:
            # 1. 解析 Draw.io 文件
            reader = DrawioStructureReader(drawio_file)
            if not reader.parse():
                return {
                    "success": False,
                    "message": "解析 Draw.io 文件失败"
                }
            
            with ExcelUtil(excel_file, auto_create=True) as excel:
                # 确保工作表存在
                if sheet_name not in excel.get_sheet_names():
                    excel.create_sheet(sheet_name)
                
                current_row = 1
                
                # 2. 添加标题
                excel.write_cell(sheet_name, current_row, 1, "Draw.io 结构分析")
                title_style = CellStyle(
                    font_size=16,
                    font_bold=True,
                    alignment_horizontal="center"
                )
                excel.set_cell_style(sheet_name, current_row, 1, title_style)
                current_row += 2
                
                # 3. 添加统计信息
                stats = reader.get_statistics()
                excel.write_cell(sheet_name, current_row, 1, "统计信息:")
                excel.set_cell_style(sheet_name, current_row, 1, CellStyle(font_bold=True))
                current_row += 1
                
                stats_data = [
                    ["总模块数", stats['total_modules']],
                    ["根模块数", stats['root_modules']],
                    ["最大层级", stats['max_level']],
                    ["小文本框", stats['small_textboxes']],
                    ["常规模块", stats['regular_modules']]
                ]
                
                for stat_name, stat_value in stats_data:
                    excel.write_cell(sheet_name, current_row, 1, stat_name)
                    excel.write_cell(sheet_name, current_row, 2, stat_value)
                    current_row += 1
                
                current_row += 2
                
                # 4. 添加结构图像（如果需要）
                if include_structure_image:
                    with tempfile.TemporaryDirectory() as temp_dir:
                        structure_image = os.path.join(temp_dir, "structure.png")
                        if self.create_structure_diagram(drawio_file, structure_image):
                            # 使用现有的图像插入功能
                            from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import insert_image_to_excel
                            
                            # 获取工作表对象
                            if hasattr(excel, '_get_worksheet'):
                                ws = excel._get_worksheet(sheet_name)
                                if ws:
                                    insert_image_to_excel(ws, structure_image, current_row, 1)
                                    current_row += 25  # 为图像预留空间
                
                # 5. 添加模块列表（如果需要）
                if include_module_list:
                    excel.write_cell(sheet_name, current_row, 1, "模块列表:")
                    excel.set_cell_style(sheet_name, current_row, 1, CellStyle(font_bold=True))
                    current_row += 1
                    
                    # 表头
                    headers = ["模块名称", "路径", "层级", "X坐标", "Y坐标", "宽度", "高度"]
                    for col, header in enumerate(headers, 1):
                        excel.write_cell(sheet_name, current_row, col, header)
                        excel.set_cell_style(sheet_name, current_row, col, CellStyle(font_bold=True))
                    current_row += 1
                    
                    # 模块数据
                    for module in reader.modules.values():
                        row_data = [
                            module.name,
                            module.path,
                            module.level,
                            round(module.x, 2),
                            round(module.y, 2),
                            round(module.width, 2),
                            round(module.height, 2)
                        ]
                        
                        for col, value in enumerate(row_data, 1):
                            excel.write_cell(sheet_name, current_row, col, value)
                        current_row += 1
                
                excel.save()
                
                return {
                    "success": True,
                    "message": f"Draw.io 结构已成功插入到 Excel",
                    "excel_file": excel_file,
                    "sheet_name": sheet_name,
                    "modules_count": len(reader.modules)
                }
                
        except Exception as e:
            error_msg = f"插入 Draw.io 结构到 Excel 时发生异常: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "excel_file": excel_file
            }


def demo_simple_drawio_to_excel():
    """演示简化版 Draw.io 到 Excel 导出"""
    print("🎯 简化版 Draw.io 导出到 Excel 演示")
    print("=" * 50)
    
    from sdw_agent.service.template_manager import template_manager
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    # 2. 创建导出器
    exporter = SimpleDrawioToExcelExporter()
    
    # 3. 导出到 Excel
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    excel_file = output_dir / "drawio_structure_analysis.xlsx"
    
    result = exporter.insert_drawio_structure_to_excel(
        drawio_file=drawio_file,
        excel_file=str(excel_file),
        sheet_name="架构分析",
        include_structure_image=True,
        include_module_list=True
    )
    
    # 4. 显示结果
    print(f"📊 导出结果:")
    print(f"   成功: {result['success']}")
    print(f"   消息: {result['message']}")
    if result['success']:
        print(f"   Excel文件: {result['excel_file']}")
        print(f"   工作表: {result['sheet_name']}")
        print(f"   模块数量: {result['modules_count']}")


if __name__ == "__main__":
    demo_simple_drawio_to_excel()
