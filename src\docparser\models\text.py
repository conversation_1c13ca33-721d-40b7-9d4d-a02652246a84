from .base_object import BaseObject
from .layout import LayoutObject
from .position import Position, Context
from .style import StyleObject
from .coordinate import CoordinateObject
import logging
# from kotei_i18n.api import _


class CharObject(BaseObject):
    def __init__(self, char, style):
        self.char = char
        self.style = style

    def is_same(self, char_obj) -> bool:
        return self.char == char_obj.char and self.style == char_obj.style


class TextObject(BaseObject):
    """文本对象"""
    def __init__(self):
        self._type = "text"
        self._text = ''  # 完整内容
        self._style = StyleObject()  # 文本样式
        self._layout = LayoutObject()  # 文本布局对象
        self._coordinate = CoordinateObject()  # 文本的坐标
        self._runs = []  # 文本片段对象列表，所有内容连起来就是完整内容
        self._data_id = None  # 唯一标识
        self._position = Position()

    def to_dict(self):
        """
        将 TextObject 对象转换为字典
        """
        return {
            "type": self._type,
            "text": self._text,
            "style": self._style.to_dict(),  # 调用 StyleObject 的 to_dict 方法
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
            "runs": [run.to_dict() for run in self._runs],  # 递归转换 RunObject 列表
            "data_id": self._data_id,
            "position": self._position.to_dict()
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TextObject 实例
        """
        obj = cls()
        obj._type = data.get("type", "text")
        obj._text = data.get("text", '')
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 恢复 StyleObject
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        obj._runs = [RunObject.from_dict(run) for run in data.get("runs", [])]  # 恢复 RunObject 列表
        obj._data_id = data.get("data_id", None)
        obj._position = Position.from_dict(data.get("position", {}))
        return obj

    def __repr__(self):
        return f'{self.__class__.__name__}()[TEXT="{self._text}"]'

    # def get_data(self):
    #     """精简化输出支持"""
    #     data = {
    #         'type': 'text',
    #         'content': self.text,
    #         'parent_content': self.layout.parent_content,
    #         "chapter": "",
    #         "title": "",
    #         "desc": "",
    #         "data_id": self.data_id,
    #         "index": -1
    #     }
    #
    #     if hasattr(self.layout, "is_chapter_title"):
    #         if self.layout.is_chapter_title:
    #             chapter = self.layout.chapter_id
    #             title = self.text.split(chapter, maxsplit=1)[1].strip(getattr(self, "customized_sep", " "))
    #             title = self.lstrip_special_char(title)
    #             data["chapter"] = chapter
    #             data["title"] = title
    #             logging.info(f'start to translate text 1: {_("这段文本是章节标题，章节号:")}')
    #             data["desc"] = _("这段文本是章节标题，章节号:") + chapter + "," + _("标题为:") + title
    #         else:
    #             logging.info(f'start to translate text 2: {_("这段文本是纯内容，不是章节标题，没有章节号。")}')
    #             data["desc"] = _("这段文本是纯内容，不是章节标题，没有章节号。")
    #         data["index"] = self.index if hasattr(self, "index") else -1
    #     else:
    #         logging.info(f'start to translate text 3: {_("这段文本是纯内容，不是章节标题，没有章节号。")}')
    #         # 普通文本
    #         data["desc"] = _("这段文本是纯内容，不是章节标题，没有章节号。")
    #
    #     if self.coordinate.desc:
    #         data["coord"] = self.coordinate.desc
    #     else:
    #         data["page_num"] = self.layout.page_id
    #
    #     return data

    @staticmethod
    def lstrip_special_char(title):
        """ 去除title左边的特殊字符 """
        built_delims = ["、", "，", ".", " "]
        for sep in built_delims:
            title = title.lstrip(sep)
        return title

    @property
    def data_id(self):
        return self._data_id

    @data_id.setter
    def data_id(self, new_value):
        assert type(new_value) == int
        self._data_id = new_value

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, new_value):
        assert type(new_value) == str
        self._text = new_value

    @property
    def runs(self):
        return self._runs

    @runs.setter
    def runs(self, new_value):
        assert type(new_value) == list
        self._runs = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value

    @property
    def position(self):
        return self._position

    @position.setter
    def position(self, new_value):
        assert isinstance(new_value, Position)
        self._position = new_value

    def get_chars(self):
        """ 获取文本对象中的每个字及样式
            去除整行文字的前后连续空格，保留中间的空格和样式
        """
        # 去除整行文字的前后连续空格，保留中间的空格和样式
        full_text = ''.join(str(run.text) for run in self.runs).strip()
        data_list = []
        current_index = 0  # 用于跟踪 full_text 的匹配位置
        for run in self.runs:
            for char in str(run.text):
                # 跳过前后被去掉的空格
                if current_index >= len(full_text):
                    break
                # 如果字符匹配 full_text 中的字符，则添加到结果列表
                if char == full_text[current_index]:
                    data_list.append(CharObject(char, run.style))
                    current_index += 1
        return data_list

    def get_chapter_id(self):
        """ 获取文本对象章节号字符串，
            返回章节id字符串 或者 空（无章节号）
        """
        return getattr(self.layout, "chapter_id", "")

    def get_context(self) -> Context:
        res = Context()
        current_index = 0
        parent = self.layout.parent_ref
        if not parent or isinstance(parent, str):
            return res
        from docparser.models.document import DocumentBlockObject
        while not isinstance(parent, DocumentBlockObject):
            parent = parent.layout.parent_ref
        texts = parent.texts
        if not texts or not isinstance(texts, list):
            # texts 为空或不可遍历，直接返回空 Context
            return res
        for index,text in enumerate(texts):
            if text.data_id == self._data_id:
                current_index = index
        if current_index - 1 >= 0:
            res.pre_context = texts[current_index - 1].text
        if current_index + 1 < len(texts):
            res.next_context = texts[current_index + 1].text
        return res


class RunObject(BaseObject):
    """文本片段对象"""

    def __init__(self):
        self._text = ''  # 完整内容
        self._style = StyleObject()  # 节段文本样式对象
        self._layout = LayoutObject()  # 文本片段布局对象
        self._coordinate = CoordinateObject()  # 文本的坐标
        self._type = ''  # 文本片段类型：text（普通文本）；br（分页标识）；

    def to_dict(self):
        """
        将 RunObject 对象转换为字典
        """
        return {
            "text": self._text,
            "style": self._style.to_dict(),  # 调用 StyleObject 的 to_dict 方法
            "layout": self._layout.to_dict(),  # 调用 LayoutObject 的 to_dict 方法
            "coordinate": self._coordinate.to_dict(),  # 调用 CoordinateObject 的 to_dict 方法
            "type": self._type,
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 RunObject 实例
        """
        obj = cls()
        obj._text = data.get("text", '')
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 恢复 StyleObject
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 恢复 LayoutObject
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 恢复 CoordinateObject
        obj._type = data.get("type", '')
        return obj

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, new_value):
        assert type(new_value) == str
        self._text = new_value

    @property
    def type(self):
        return self._type

    @type.setter
    def type(self, new_value):
        assert type(new_value) == str
        self._type = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value
