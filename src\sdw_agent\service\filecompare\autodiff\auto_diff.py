import subprocess
import os
import sys
import shutil
from diffconfig import Config
import diff2excel as d2e

def exec_command(command):
    exec_result = subprocess.run(command, capture_output=True)
    if exec_result.returncode != 0:
        print(exec_result.stderr.decode())
        result = None
    else:
        result = exec_result.stdout.decode().strip()
    
    return result
    
def get_commit_info(commitid):
    result = exec_command([
        'git log',
        commitid,
        -1,
        '--format=%H'        
    ])
    
    return result

def get_commit_diff(start, end):
    os.environ['AD_GIT_COMMITID_PREVIOUS'] = start
    os.environ['AD_GIT_COMMITID'] = end
    print ('check...', start,'vs', end)
    config = Config(end)
    if not config.is_finished(start):
        commitmsg = exec_command([
            'git',
            'log',
            end,
            '-1',
            '--pretty=%B'
        ])
        config.set_commitmsg(commitmsg)
        
        commitdate = exec_command([
            'git',
            'log',
            end,
            '-1',
            '--date=short',
            '--format=%cd'
        ])
        config.set_commitdate(commitdate)
        
        author = exec_command([
            'git',
            'log',
            end,
            '-1',
            '--pretty=%an'
        ])
        
        config.set_author(author)    
        config.save()

        result = exec_command([
            'git',
            'difftool',
            start,
            end
        ])
        
        config.load()    
        d2e.create_diff(config)
        config.save()
        
        print ('Create', start,'vs', end, 'finished!')
    else:
        print ('Diff exist', start,'vs', end, '!')
    
    return config

def get_commit_list2(start_commit, end_commit):
    commit_result = exec_command([
        'git',
        'log',
         f'{start_commit}..{end_commit}',
         '--pretty=%H'
    ])
    
    return commit_result.splitlines()

def get_commit_list(start_commit):
    commit_result = exec_command([
        'git',
        'log',
         start_commit,
         '-2',
         '--pretty=%H'
    ])
    
    return commit_result.splitlines()
    
if  __name__ == "__main__":
    argc = len(sys.argv)
    
    if argc > 2:
        commit_list = get_commit_list2(start_commit=sys.argv[1], end_commit=sys.argv[2])
        commit_size = len(commit_list)
        result_list = []
        index = 0
        while index < (commit_size - 1) :
            result = get_commit_diff(commit_list[index+1], commit_list[index])
            result_list.append(result)
            index += 1
        result = get_commit_diff(sys.argv[1], commit_list[index])
        result_list.append(result)
        
        d2e.create_list(result_list, sys.argv[1], sys.argv[2])
        
    elif argc == 2:
        list = get_commit_list(sys.argv[1])
        get_commit_diff(list[1], list[0])
                