import traceback

from fastapi import APIRouter, HTTPException

from sdw_agent.model.request_model import RamGlobalVarRequest
from sdw_agent.model.response_model import RamGlobalVarResponse
from sdw_agent.service.ram_design_global_var.search_global_var import search_global_var

router = APIRouter(prefix="/api/sdw/ram_global", tags=["RAM设计书作成"])


@router.post("/match_global_var",
             summary="RAM设计书作成",
             description="",
             response_description="",
             response_model=RamGlobalVarResponse)
async def match_global_var(request: RamGlobalVarRequest):
    """
    Ram 设计书作成
    从变更代码中解析出全局变量及变量声明
    """
    try:
        key_source = request.keySource
        comp_list_path = key_source.uri
        repository_path = request.repo.repo_path
        commit_id = request.repo.commit_id
        compared_commit_id = request.repo.compared_commit_id
        output_path = search_global_var(repository_path, comp_list_path, commit_id, compared_commit_id)

        return {
            "code": 0,
            "msg": "RAM设计成果物输出成果",
            "data": {
                "keyList": output_path
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))