"""
RAM确认工作流

V字对应：
4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証

该模块提供RAM确认功能，根据Gerrit Diff文档获取变更的全局变量并确认初始化时机。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量初始化位置检索，并确认初始化时机
3. 生成RAM确认报告
"""

from pathlib import Path
from typing import Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow
from sdw_agent.service.ram_confirm_service.models import RAMConfirmConfigModel, RepositoryInfo
from sdw_agent.service.ram_confirm_service.utils import search_global_var, gen_ram_confirm_data
from sdw_agent.service.ram_confirm_service.excel_util import RAMConfirmExcelUtil
from sdw_agent.service.template_manager import template_manager
from sdw_agent.util.extract_code_util import InterruptAndSchedulerExtractor
from sdw_agent.util.file_base_util import gen_output_path


@register_workflow("ram_confirm")
class RAMConfirmWorkflow(BaseWorkflow):
    """RAM确认工作流"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化RAM确认工作流
        
        Args:
            config_path: 配置文件路径，可选
        """
        super().__init__(config_path)
        self.logger.info("RAM确认工作流初始化完成")

    def execute(self, repo_info: RepositoryInfo) -> WorkflowResult:
        """
        执行RAM确认工作流核心逻辑
        
        Args:
            repo_info: 代码仓库信息
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.logger.info("开始执行RAM确认工作流")

            # 1. 搜索全局变量变更
            global_vars_info = self._search_global_variable_changes(repo_info)
            self.logger.info(f"步骤1. 搜索全局变量变更: 找到 {len(global_vars_info)} 个变量")
            if global_vars_info:

                # 2.获取函数调度信息
                regular_task = self._get_regular_task(repo_info.repo_path)
                self.logger.info(f"步骤2.获取函数调度信息: {regular_task}")

                # 3. 生成RAM确认数据
                ram_confirm_data = self._generate_ram_confirm_data(repo_info.repo_path, global_vars_info, regular_task)
                self.logger.info(f"步骤3. 生成RAM确认数据: {len(ram_confirm_data)} 条记录")
            else:
                ram_confirm_data = []

            # 4. 保存到Excel文件
            output_file = self._save_to_excel(ram_confirm_data)
            self.logger.info(f"步骤4. 保存到Excel文件: {output_file}")

            # 5. 构建返回结果
            return self._create_success_result(output_file, ram_confirm_data, global_vars_info)

        except Exception as e:
            self.logger.exception(f"RAM确认工作流执行失败: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message="RAM确认工作流执行失败",
                error=str(e)
            )

    def validate_input(self, repo_info: RepositoryInfo) -> bool:
        """
        验证输入参数
        
        Args:
            repo_info: 代码仓库信息
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证仓库路径
            repo_path = Path(repo_info.repo_path)
            if not repo_path.exists():
                self.logger.error(f"仓库路径不存在: {repo_info.repo_path}")
                return False

            # 验证提交ID格式
            if not repo_info.commit_id or len(repo_info.commit_id) < 7:
                self.logger.error(f"提交ID格式无效: {repo_info.commit_id}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def _get_regular_task(self, repo_path):
        """
        获取指定仓库路径下的常规任务信息

        参数:
            repo_path (str): 仓库路径，用于提取任务详情

        返回:
            dict: 包含任务名称和对应任务位的字典，格式为 {task_name: task_bit}
                  如果无法提取有效任务位，则使用默认值 "-"
        """
        # 从配置中获取仓库扩展名和默认常规任务配置
        repo_extensions = self.config.get("io", {}).get("input", {}).get(
            "repo_extensions"
        )
        default_regular_task = self.config.get("io", {}).get("input", {}).get(
            "default_regular_task", {}
        )

        # 初始化常规任务字典，先用默认配置填充
        regular_task = {}
        regular_task.update(default_regular_task)

        # 提取仓库中的调度器常规任务详情
        regular_task_detail = InterruptAndSchedulerExtractor(repo_path,
                                                             repo_extensions).get_scheduler_regular_task_detail()

        # 遍历任务详情，从任务位字符串中提取包含"MS"的部分作为任务位
        for task_name, task_bit in regular_task_detail.items():
            regular_task[task_name] = next((item for item in task_bit.split("_") if "MS" in item), "-")

        return regular_task


    def _search_global_variable_changes(self, repo_info: RepositoryInfo) -> list:
        """
        搜索全局变量变更

        该函数通过调用search_global_var工具函数，对比两个提交版本之间的全局变量变化情况。

        参数:
            repo_info (RepositoryInfo): 仓库信息对象，包含仓库路径、当前提交ID和对比提交ID等信息

        返回值:
            list: 全局变量变更的搜索结果列表
        """
        # 记录搜索全局变量变更的日志信息
        self.logger.info("步骤1: 搜索全局变量变更")
        return search_global_var(
            repo_info.repo_path,
            repo_info.commit_id,
            repo_info.compared_commit_id
        )


    def _generate_ram_confirm_data(self, repo_path: str, global_vars_info: list, regular_task: dict) -> list:
        """
        生成RAM确认数据

        Args:
            repo_path (str): 仓库路径
            global_vars_info (list): 全局变量信息列表
            regular_task (dict): 定期任务配置字典

        Returns:
            list: 生成的RAM确认数据列表
        """
        # 记录生成RAM确认数据的日志信息
        self.logger.info("步骤2: 生成RAM确认数据")
        # 获取作者信息，如果配置中没有则使用默认值"SDW-Team"
        author = self.config.get("author", "SDW-Team")
        # 调用gen_ram_confirm_data函数生成RAM确认数据并返回结果
        return gen_ram_confirm_data(repo_path, global_vars_info, author, regular_task)


    def _save_to_excel(self, ram_confirm_data: list) -> str:
        """
        将RAM确认数据保存到Excel文件中

        Args:
            ram_confirm_data (list): RAM确认数据列表，包含需要写入Excel的数据

        Returns:
            str: 生成的Excel文件的完整路径

        Raises:
            Exception: 当保存Excel文件过程中发生错误时抛出异常
        """
        self.logger.info("步骤3: 保存到Excel文件")

        # 获取输出文件的路径
        output_dir = self.config.get("io", {}).get("output", {}).get(
            "default_output_dir"
        )

        # 获取报告的基础名称，如果没有配置，则使用默认值"【Orca#2】RAM確認表"
        report_base_name = self.config.get("io", {}).get("output", {}).get(
            "report_base_name", "【Orca#2】RAM確認表"
        )

        output_file = gen_output_path(output_dir, report_base_name)

        # 获取模板文件的路径，用于生成报告的格式
        template_file = template_manager.get_template_path("ram_confirm_file")
        # 获取Excel报告的样式配置
        excel_style = self.config.get("processing", {}).get("excel_output", {})

        # 创建Excel工具实例并保存数据
        excel_util = RAMConfirmExcelUtil(output_file=output_file, template_file=template_file, excel_style=excel_style)

        # 使用Excel工具将数据保存到文件
        try:
            with excel_util:
                excel_util.save_ram_confirm_data(ram_confirm_data)

        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {e}")
            raise

        return output_file


    @staticmethod
    def _create_success_result(output_file: str, ram_confirm_data: list,
                               global_vars_info: list) -> WorkflowResult:
        """
        创建RAM确认工作流成功结果对象

        Args:
            output_file (str): 输出文件路径
            ram_confirm_data (list): RAM确认数据列表
            global_vars_info (list): 全局变量信息列表

        Returns:
            WorkflowResult: 包含成功状态、消息和相关数据的工作流结果对象
        """
        # 构造包含执行结果信息的WorkflowResult对象
        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message="RAM确认工作流执行成功",
            data={
                "output_file": output_file,
                "ram_confirm_data": ram_confirm_data,
                "statistics": {
                    "total_global_vars": len(global_vars_info),
                    "confirm_records": len(ram_confirm_data)
                },
                "global_vars_info": global_vars_info
            }
        )


    @staticmethod
    def register_config_model():
        """
        注册配置模型用于验证

        该函数通过WorkflowConfigManager注册RAM确认流程的配置模型，
        用于后续的配置验证和管理。

        参数:
            无

        返回值:
            无

        重要代码块说明:
            - 导入WorkflowConfigManager配置管理器
            - 创建RAM确认流程的配置管理实例
            - 注册RAM确认配置模型到配置管理器
        """
        from sdw_agent.service.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager(workflow_name="ram_confirm")
        config_manager.register_schema("ram_confirm", RAMConfirmConfigModel)



def do_ram_confirm(repo_info: RepositoryInfo) -> str:
    """
    执行RAM确认检查（兼容旧接口）
    
    Args:
        repo_info: 代码仓库信息
        
    Returns:
        str: 输出文件路径
        
    Raises:
        Exception: 当执行失败时
    """
    # 创建RAM确认工作流实例并执行
    workflow = RAMConfirmWorkflow()
    result = workflow.execute(repo_info)

    # 根据执行结果状态返回相应结果
    if result.status == WorkflowStatus.SUCCESS:
        return result.data["output_file"]
    raise Exception(result.message)

