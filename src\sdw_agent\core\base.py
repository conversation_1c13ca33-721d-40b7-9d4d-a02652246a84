"""基础抽象类和接口定义"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import traceback

# 导入LLM和提示词相关模块
from .llm import LLMManager, LLMMessage
from .prompt import PromptManager, AgentPromptBuilder


class StepType(Enum):
    """步骤类型枚举"""
    AGENT = "Agent"  # AI智能分析
    TOOL = "Tool"    # 工具调用


class StepStatus(Enum):
    """步骤执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class StepInput:
    """步骤输入数据结构"""
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    
@dataclass
class StepOutput:
    """步骤输出数据结构"""
    data: Dict[str, Any]
    status: StepStatus
    message: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class StepResult:
    """步骤执行结果"""
    step_id: str
    step_name: str
    status: StepStatus
    output: Optional[StepOutput] = None
    error: Optional[str] = None
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    duration: Optional[float] = None  # 秒
    
    def complete(self):
        """标记步骤完成"""
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()


class BaseStep(ABC):
    """所有步骤的基类"""
    
    def __init__(self, 
                 step_id: str,
                 name: str,
                 step_type: StepType,
                 description: str = "",
                 dependencies: List[str] = None):
        self.step_id = step_id
        self.name = name
        self.step_type = step_type
        self.description = description
        self.dependencies = dependencies or []
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    @abstractmethod
    def validate_input(self, input_data: StepInput) -> bool:
        """验证输入数据是否符合要求"""
        pass
    
    @abstractmethod
    def process(self, input_data: StepInput) -> StepOutput:
        """执行步骤的核心处理逻辑"""
        pass
    
    def execute(self, input_data: StepInput) -> StepResult:
        """执行步骤的标准流程"""
        result = StepResult(
            step_id=self.step_id,
            step_name=self.name,
            status=StepStatus.RUNNING
        )
        
        try:
            # 记录开始执行
            self.logger.info(f"开始执行步骤: {self.name} (ID: {self.step_id})")
            
            # 验证输入
            if not self.validate_input(input_data):
                raise ValueError("输入数据验证失败")
            
            # 执行处理
            output = self.process(input_data)
            
            # 设置结果
            result.output = output
            result.status = output.status
            
            # 记录成功
            self.logger.info(f"步骤执行成功: {self.name}")
            
        except Exception as e:
            # 记录错误
            error_msg = f"步骤执行失败: {str(e)}\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            
            result.status = StepStatus.FAILED
            result.error = error_msg
            result.output = StepOutput(
                data={},
                status=StepStatus.FAILED,
                error=error_msg
            )
        
        finally:
            result.complete()
            
        return result
    
    def __repr__(self):
        return f"{self.__class__.__name__}(id={self.step_id}, name={self.name})"


class AgentStep(BaseStep):
    """Agent类型步骤的基类"""
    
    def __init__(self, 
                 *args, 
                 agent_type: str = "default",
                 llm_manager: Optional[LLMManager] = None,
                 prompt_manager: Optional[PromptManager] = None,
                 llm_client_name: Optional[str] = None,
                 **kwargs):
        """
        初始化Agent步骤
        
        Args:
            agent_type: Agent类型，用于选择合适的提示词模板
            llm_manager: LLM管理器实例
            prompt_manager: 提示词管理器实例
            llm_client_name: 要使用的LLM客户端名称
        """
        super().__init__(*args, step_type=StepType.AGENT, **kwargs)
        self.agent_type = agent_type
        self.llm_manager = llm_manager
        self.prompt_manager = prompt_manager
        self.llm_client_name = llm_client_name
        
        # 如果提供了提示词管理器，创建提示词构建器
        if self.prompt_manager:
            self.prompt_builder = AgentPromptBuilder(self.prompt_manager)
        else:
            self.prompt_builder = None
    
    def build_messages(self, data: Dict[str, Any], task_type: str) -> List[LLMMessage]:
        """
        构建LLM消息列表
        
        Args:
            data: 输入数据
            task_type: 任务类型
            
        Returns:
            消息列表
        """
        messages = []
        
        # 添加系统提示词
        if self.prompt_builder:
            system_prompt = self.prompt_builder.build_system_prompt(self.agent_type)
            messages.append(LLMMessage(role="system", content=system_prompt))
            
            # 添加用户提示词
            user_prompt = self.prompt_builder.build_user_prompt(
                self.agent_type, 
                task_type, 
                **data
            )
            messages.append(LLMMessage(role="user", content=user_prompt))
        else:
            # 如果没有提示词构建器，使用简单的默认提示词
            system_content = f"你是一个专业的{self.agent_type}助手，请帮助用户完成{task_type}任务。"
            messages.append(LLMMessage(role="system", content=system_content))
            
            # 构建用户消息
            user_content = f"请执行{task_type}任务，输入数据如下:\n\n{data}\n\n请分析以上数据并给出结果。"
            messages.append(LLMMessage(role="user", content=user_content))
            
        return messages
    
    def call_llm(self, messages: List[LLMMessage], **kwargs) -> str:
        """
        调用LLM获取响应
        
        Args:
            messages: 消息列表
            **kwargs: 其他参数
            
        Returns:
            LLM响应内容
        """
        if not self.llm_manager:
            raise ValueError("未提供LLM管理器，无法调用LLM")
            
        response = self.llm_manager.call(
            messages, 
            client_name=self.llm_client_name,
            **kwargs
        )
        
        return response.content
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """
        解析LLM响应
        
        Args:
            response: LLM响应内容
            
        Returns:
            解析后的结果数据
        """
        # 默认实现直接返回响应内容
        # 子类可以重写此方法以实现特定的解析逻辑
        return {"result": response}
        
    @abstractmethod
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """AI分析逻辑 - 子类必须实现"""
        pass
    
    def process(self, input_data: StepInput) -> StepOutput:
        """Agent的标准处理流程"""
        try:
            # 执行AI分析
            result_data = self.analyze(input_data.data)
            
            return StepOutput(
                data=result_data,
                status=StepStatus.SUCCESS,
                message=f"{self.name}分析完成"
            )
        except Exception as e:
            raise RuntimeError(f"Agent分析失败: {str(e)}")


class ToolStep(BaseStep):
    """Tool类型步骤的基类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, step_type=StepType.TOOL, **kwargs)
        
    @abstractmethod
    def call_tool(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具逻辑 - 子类必须实现"""
        pass
    
    def process(self, input_data: StepInput) -> StepOutput:
        """Tool的标准处理流程"""
        try:
            # 调用工具
            result_data = self.call_tool(input_data.data)
            
            return StepOutput(
                data=result_data,
                status=StepStatus.SUCCESS,
                message=f"{self.name}执行完成"
            )
        except Exception as e:
            raise RuntimeError(f"工具调用失败: {str(e)}") 