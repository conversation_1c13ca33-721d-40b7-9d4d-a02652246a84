"""
dspwrnctl Parameter设定书工作流路由 - 生成dspwrnctl Parameter设定书
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any

from sdw_agent.api.core.database import get_db
from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo

from sdw_agent.service.warning_code_gen_check.workflow_warning_code_check import \
    gen_and_check_all_warning_code_service
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_gen import gen_all_warning_code_service

# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api/sdw/warning_code_book", tags=["dspwrnctl Parameter设定书工作流"])


# 定义请求和响应模型
class WarningCodeGenerate(BaseModel):
    """请求模型"""
    pre_sample_book_url: str = ""  # 变更前告警式样书路径
    after_sample_book_url: str = ""  # 变更后告警式样书路径
    code_gen_tools_url: str = ""  # 告警代码生成工具地址
    relate_book_folder: str = ""  # 关联式样书文件夹
    adas_book_path: str = ""  # ADAS式样书路径
    compare_tools_url: str = "" # 比较工具文件
    meter_popup_book_path: str = ""  # meter popup式样书路径


class ExampleResponse(BaseModel):
    """示例响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


# 示例API端点
@router.post("/getWarningDspCode",
             summary="dspwrnctl Parameter设定书生成接口",
             description="这是一个dspwrnctl Parameter设定书生成接口API，",
             response_model=ExampleResponse)
async def get_WarningCode_process(request: WarningCodeGenerate, db: Session = Depends(get_db)):
    """
    示例处理函数
    
    Args:
        request: 请求参数
        db: 数据库会话
        
    Returns:
        处理结果
    """
    try:
        # 这里可以添加具体的业务逻辑
        warning_change_info = WarningChangeInfo(
            pre_sample_book_url=request.pre_sample_book_url,
            after_sample_book_url=request.after_sample_book_url,
            code_gen_tools_url=request.code_gen_tools_url,
            compare_tools_url= request.compare_tools_url,  # 如果不需要可以留空
            relate_book_folder=request.relate_book_folder,
            adas_book_path=request.adas_book_path,
            meter_interface_popup_url=request.meter_popup_book_path,  # 如果不需要可以留空
        )
        result = gen_all_warning_code_service(warning_change_info)

        return {
            "code": 0,
            "msg": "处理成功",
            "data": {"warning_code_file_path": result.after_code_tools_url,
                     "compare_tools_path" : result.compare_tools_url}
        }

    except Exception as e:
        return {
            "code": 1,
            "msg": f"处理失败: {str(e)}",
            "data": {}
        }


# 定义请求和响应模型
class WarningCodeCheck(BaseModel):
    """请求模型"""
    pre_sample_book_url: str = ""  # 变更前告警式样书路径
    after_sample_book_url: str = ""  # 变更后告警式样书路径
    code_gen_tools_url: str = ""  # 生成工具路径
    # after_code_gen_tools_url: str = ""  # 变更后生成代码地址
    code_check_file_url: str = ""  # 代码检查清单文件地址
    code_folder_url: str = ""  # 本地代码仓
    relate_book_folder: str = ""  # 关联式样书文件夹
    adas_book_path: str = ""  # ADAS式样书路径


class ExampleResponse(BaseModel):
    """示例响应模型"""
    code: int = 0
    msg: str = ""
    data: Dict[str, Any]


# 示例API端点
@router.post("/checkWarningCode",
             summary="dspwrnctl Parameter设定书检查接口",
             description="这是一个告警代码生成和检查接口API，",
             response_model=ExampleResponse)
async def check_warning_code_process(request: WarningCodeCheck, db: Session = Depends(get_db)):
    """
    示例处理函数

    Args:
        request: 请求参数
        db: 数据库会话

    Returns:
        处理结果
    """
    try:
        # 这里可以添加具体的业务逻辑
        warning_change_info = WarningChangeInfo(
            pre_sample_book_url=request.pre_sample_book_url,
            after_sample_book_url=request.after_sample_book_url,
            code_gen_tools_url=request.code_gen_tools_url,
            relate_book_folder=request.relate_book_folder,
            code_folder=request.code_folder_url,
            check_file_path=request.code_check_file_url,
            adas_book_path=request.adas_book_path,
        )
        result = gen_and_check_all_warning_code_service(warning_change_info)

        return {
            "code": 0,
            "msg": "处理成功",
            "data": {"warning_code_check_path": result}
        }

    except Exception as e:
        return {
            "code": 1,
            "msg": f"处理失败: {str(e)}",
            "data": {}
        }


@router.get("/status",
            summary="获取工作流状态",
            description="获取当前工作流的状态信息",
            response_model=ExampleResponse)
async def get_workflow_status():
    """获取工作流状态"""
    return {
        "code": 0,
        "msg": "查询成功",
        "data": {
            "workflow_name": "示例工作流",
            "status": "active",
            "version": "1.0.0"
        }
    }
