name: "selfchceck and code review"
description: "代码提交前自我检查和代码审查"
version: "1.0.0"
author: "junyu_li"


gerrit:
  url: http://************:8080 #"http://************:8085" "http://************:8080"
  username: "cr_robot"
  password: "ocsa@2024!"

ftp_host: "************"
ftp_user: "ai"
ftp_password: "dnkt$88"
ftp_remote_dir: "/gerrit23/"  # /gerrit19/
ftp_remote_dir_dscscc: "/gerrit23/dscscc/"

openai:
  base_url: "http://*************:11434/v1/"
  api_key: "ollama"


codereviewpoint:
  0: 
    name: 1.分支条件
    rule: >
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      条件分支中是否有遗漏？(可以处理任何条件吗？）
      如果条件判断很复杂，请使用真值表和流程图进行验证。
      if 语句的 else 侧和 switch 语句中是否有遗漏？
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  1:
    name: 1.分支条件
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      计数器是否在无需条件的情况下，错误地使用比较符（小于、大于、大于等于、小于等于）进行分支判断？
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  2:
    name: 1.分支条件
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      分支条件中是否有错误？
      ＜和≤、＞和≥中是否有错误，导致多处理一次或处理不足？
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  3:
    name: 2.类型转换
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      当将变量或数值强制转换为不同类型的变量时，是否会因原始变量的值导致后续处理出错？
      在审查代码时，需特别注意无符号与有符号类型的转换
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  4:
    name: 2.类型转换
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      当将变量或数值强制转换为不同类型的变量时，是否会导致数据丢失？
      在执行可能导致数据长度缩短的转换操作时，请特别注意并进行代码审查。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  5: 
    name: 3.计数器保持值
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      确认计数器保持值的合理性以防止溢出。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  6:
    name: 4.影响确认
    rule: >  
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      对已修改的变量、修改后的常量或表格是否存在影响？请通过 GREP 检查影响。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  7:
    name: 5.初期値
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      是否从以下方面检查了变量的初始值
      变量是否初始化，初始值是否有效（包括自动变量）？
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  8:
    name: 6.数据
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      如果使用特殊数据类型（例如 BCD 类型），则必须使用专用 FUNC。
      如果没有专用 FUNC，则应确保操作的左右类型相匹配
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  9:
    name: 6.数据
    rule: >   
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      考虑到编译器的优化，不应执行可能被判定为无效的代码。但如果无法避免，可采取添加 volatile 声明等措施。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  10:
    name: 7.函数声明
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      检查函数声明和内部函数实体的声明是否相同
      例如 static void vd_****(void)；

      void vd_****(void) ← 忘记添加 static
      } *注意 QAC 不会生成警告
      ・・・・
      }
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  11:
    name: 8.检查拼写错误
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      确认是否存在拼写错误
      在以下情况下，应以存在拼写错误为前提进行检查，因为即使出现拼写错误也可能不会导致编译错误，因此必须人工手动检查：
      复制并粘贴类似函数后修改的情况（主要发生在移植时）
      修正编码规范违反内容的情况
      修改标签名（变量名、函数名等）的情况
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  12:
    name: 9.中断处理
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      确保成对禁用和允许中断，在所有分支条件下都不会过多或不足.
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  13:
    name: 10.命名规则
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      "RAM 符号应根据编码标准使用易于理解的符号。
      * 注意安全带安装状态等项目，ECU 功能和活动状态是不同的。例如）SeatbeltUnfastend 为 TRUE 并发出警告"。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  14:
    name: 11.命名规则
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      定义 I/F 的返回值时，应定义一个可识别的名称。
      (不要只使用 YES、NO 等，而应使用 BELT_UNBUCKLED_YES 和 BELT_UNBUCKLED_NO）。
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------
  15: 
    name: 12.地址引用
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      当地址值被引用并用于处理时，原则上禁止定义地址值以外的任何内容（Null、0xFF 等）。
      当定义 ROM 大小或其他使用资源效率方面时，应包含防护处理或其他机制，以防止意外使用
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------  
  16: 
    name: 13.变量的目的
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      检查变量是否具有多重含义和目的。
      如果为减少内存而不可避免地设计了具有多种用途的实现，则应提取变量的 R/W 位置，并无遗漏地进行影响验证
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------  
  17: 
    name: 14.使用函数指针表
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      使用函数指针表进行函数调用时，应包含一种机制来检查调用者和注册者的表大小是否匹配。
      (例如，行结束（NULL）检查、使用 sizeof(arr) 进行大小检查）
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------  
  18: 
    name: 15.变量的类型（大小）
    rule: > 
      请针对文件逐行进行确认,以防止遗漏。基于以下观点确认代码：
      变量的类型（大小）是否满足要分配的最大值？
      在赋值操作结果时，变量的大小是否能够赋值操作结果的最大值？
      *当源代码被分流，而分流目的地没有发生源代码变化时（因为使用条件可能反而会超过最大值），应注意这一点
      -------------------------
      按照以下格式输出评价：
      结论：(存在/不存在)
      理由：(为什么得出该结论的理由)
      -------------------------  
# 19: >
