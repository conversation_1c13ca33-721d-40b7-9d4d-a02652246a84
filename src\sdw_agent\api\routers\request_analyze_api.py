"""
V字对应：
1.3 要件分析
2. 要求仕様読み合わせ（客先、部内、チーム内）

变更点理解、影响分析写入要件一览表 API

优化特性：
1. 增强错误处理和异常管理
2. 改进类型安全和数据验证
3. 优化代码结构和可维护性
4. 增强日志记录
5. 添加API文档
6. 重构为工作流架构
"""
from typing import Any

from fastapi import APIRouter, HTTPException, status
from loguru import logger
from pydantic import BaseModel

from sdw_agent.service.request_analyze import (
    RequestAnalyzeWorkflow,
    TaskInfo,
    WriteEpicRequest
)

# 创建路由器
router = APIRouter(prefix="/api/sdw/design", tags=["1.3 要件分析", "2. 要求仕様読み合わせ（客先、部内、チーム内）"])


# API响应模型
class ClassifyScoreResponse(BaseModel):
    """分类打分响应"""
    code: int = 0
    msg: str = ""
    data: Any = None


class AnalysisResponse(BaseModel):
    """分析响应模型"""
    code: int = 0
    msg: str = ""
    data: Any = None


# API端点
@router.post(
    "/classify_by_guideline",
    summary="基于guideline对变更需求进行分类打分",
    description="根据提供的需求信息和guideline，对变更需求进行分类打分",
    response_model=ClassifyScoreResponse,
    responses={
        200: {"description": "成功返回分类打分结果"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def classify_score_req(request: TaskInfo):
    """
    基于guideline对变更需求进行分类打分

    使用RequestAnalyzeWorkflow执行分类打分工作流
    """
    try:
        # 创建工作流实例
        workflow = RequestAnalyzeWorkflow()

        # 执行分类打分工作流
        result = workflow.execute_classify(request)

        if result.status.value == "成功":
            logger.info(f"成功对AR票 {request.ar_no} 进行分类打分")
            return ClassifyScoreResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"分类打分失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"分类打分失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分类打分失败: {str(e)}"
        )


@router.post(
    "/optimize_by_guideline",
    summary="基于guideline分类对应的内容结合变更需求的描述进行润色",
    description="根据提供的变更需求和选择的分类，生成优化后的变更点理解和影响分析，并写入到要件一览表",
    response_model=AnalysisResponse,
    responses={
        200: {"description": "成功返回优化后的分析结果"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def optimize_by_guideline(request: WriteEpicRequest):
    """
    基于guideline分类对应的内容结合变更需求的描述进行润色

    使用RequestAnalyzeWorkflow执行内容优化工作流
    """
    try:
        # 创建工作流实例
        workflow = RequestAnalyzeWorkflow()

        # 执行内容优化工作流
        result = workflow.execute_optimize(request)

        if result.status.value == "成功":
            logger.info(f"成功写入要件一览表: {request.keySource.uri}")
            return AnalysisResponse(
                code=0,
                msg=result.message,
                data=result.data
            )
        else:
            logger.error(f"内容优化失败: {result.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.message
            )

    except ValueError as e:
        logger.error(f"请求参数错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"生成分析结果失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成分析结果失败: {str(e)}"
        )


