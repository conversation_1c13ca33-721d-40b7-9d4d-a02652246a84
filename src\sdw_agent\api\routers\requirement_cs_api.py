"""
需求CS确认API路由器

V字对应：
1.3 要件分析
3. RequirementCS確認

该模块提供需求分析和CS确认相关的API接口，用于根据要件一览表填写CheckListSheet。
主要功能包括读取需求列表文件和CS文件，进行需求分析和确认。
"""

import logging
import os
from typing import Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field, field_validator

from sdw_agent.model.response_model import SourceInfo
from sdw_agent.util.excel_util import read_excel

# 配置日志记录器
logger = logging.getLogger(__name__)

# 创建API路由器，设置前缀和标签
router = APIRouter(prefix="/api/sdw/design", tags=["1.3 要件分析", "3. RequirementCS確認"])


class RequirementCSRequest(BaseModel):
    """需求CS确认请求模型"""
    req_list_file: SourceInfo = Field(..., description="需求列表文件信息")
    cs_file: SourceInfo = Field(..., description="CS文件信息")

    @field_validator('req_list_file')
    @classmethod
    def validate_req_list_file(cls, v):
        """验证需求列表文件信息"""
        if not v or not v.uri:
            raise ValueError("需求列表文件URI不能为空")
        return v

    @field_validator('cs_file')
    @classmethod
    def validate_cs_file(cls, v):
        """验证CS文件信息"""
        if not v or not v.uri:
            raise ValueError("CS文件URI不能为空")
        return v


class ResultFile(BaseModel):
    """结果文件模型"""
    result_file: str = Field(..., examples=['C:/xxx/xx.xlsx'], description="结果文件路径")

    @field_validator('result_file')
    @classmethod
    def validate_result_file_path(cls, v):
        """验证结果文件路径格式"""
        if not v or not v.strip():
            raise ValueError("结果文件路径不能为空")
        return v.strip()


class ResultResponse(BaseModel):
    """API响应模型"""
    code: int = Field(0, description="状态码，0表示成功，非0表示失败")
    msg: str = Field("", description="状态消息，成功时为空，失败时包含错误信息")
    data: Optional[ResultFile] = Field(None, description="结果文件信息，仅在成功时返回")


@router.post(
    "/requirement_cs",
    summary="根据要件一览表填写CheckListSheet",
    description="根据输入的需求列表文件和CS文件，进行需求分析并生成CheckListSheet",
    response_model=ResultResponse,
    responses={
        200: {"description": "成功返回CheckListSheet文件路径"},
        400: {"description": "请求参数错误或文件验证失败"},
        404: {"description": "指定的文件不存在"},
        422: {"description": "请求数据格式错误"},
        500: {"description": "服务器内部错误"}
    }
)
async def requirement_cs_handler(request: RequirementCSRequest) -> ResultResponse:
    """
    需求CS确认处理接口

    根据提供的需求列表文件和CS文件，进行需求分析和确认，生成对应的CheckListSheet文件。

    Args:
        request: 包含需求列表文件和CS文件信息的请求对象

    Returns:
        ResultResponse: 包含处理结果和生成文件路径的响应对象

    Raises:
        HTTPException: 当请求参数无效、文件不存在或处理过程中发生错误时抛出
    """
    try:
        # 记录请求信息
        logger.info(f"开始处理需求CS确认请求")
        logger.info(f"需求列表文件URI: {request.req_list_file.uri}")
        logger.info(f"CS文件URI: {request.cs_file.uri}")

        # 验证输入文件信息
        if not request.req_list_file.uri:
            logger.error("需求列表文件URI为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="需求列表文件URI不能为空"
            )

        if not request.cs_file.uri:
            logger.error("CS文件URI为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="CS文件URI不能为空"
            )

        # 标准化文件路径（将反斜杠替换为正斜杠）
        req_list_file_path = request.req_list_file.uri.replace('\\', '/')
        cs_file_path = request.cs_file.uri.replace('\\', '/')

        # 验证文件是否存在
        if not os.path.exists(req_list_file_path):
            logger.error(f"需求列表文件不存在: {req_list_file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"需求列表文件不存在: {req_list_file_path}"
            )

        if not os.path.exists(cs_file_path):
            logger.error(f"CS文件不存在: {cs_file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CS文件不存在: {cs_file_path}"
            )

        # 读取需求列表Excel文件
        logger.info(f"开始读取需求列表文件: {req_list_file_path}")
        try:
            hyperlinks, df = read_excel(
                req_list_file_path,
                header=4,  # 从第5行开始读取数据（0-based索引）
                sheet_name="要件一覧"  # 指定工作表名称
            )
            logger.info(f"成功读取需求列表文件，数据行数: {len(df) if df is not None else 0}")

        except FileNotFoundError:
            logger.error(f"需求列表文件未找到: {req_list_file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="需求列表文件未找到"
            )
        except Exception as e:
            logger.error(f"读取需求列表文件失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"读取需求列表文件失败: {str(e)}"
            )

        # TODO: 在这里添加实际的业务逻辑
        # 1. 分析需求列表数据
        # 2. 处理CS文件
        # 3. 根据需求分析结果生成CheckListSheet
        # 4. 保存生成的文件并返回路径

        # 当前返回CS文件路径作为占位符
        result_file_path = cs_file_path

        # 验证生成的结果文件路径
        if not result_file_path:
            logger.error("生成的结果文件路径为空")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="生成结果文件失败"
            )

        # 构建成功响应
        response_data = ResultFile(result_file=result_file_path)

        logger.info(f"需求CS确认处理成功，结果文件: {result_file_path}")

        return ResultResponse(
            code=0,
            msg="处理成功",
            data=response_data
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as ve:
        # 处理数据验证错误
        logger.error(f"数据验证错误: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"数据验证失败: {str(ve)}"
        )
    except Exception as e:
        # 处理其他未预期的错误
        logger.error(f"需求CS确认处理过程中发生未预期错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误，请稍后重试"
        )
