"""
多级表头提取器 - 解决Excel合并单元格表头读取问题

新增功能说明：
==============

1. read_excel_with_multi_level_headers() 方法
   - 替代简单的 pd.read_excel(header=4) 调用
   - 正确处理第三行和第四行合并单元格的情况
   - 生成合理的多级表头列名

2. read_excel_smart() 静态方法
   - 便捷的静态方法，无需创建实例
   - 直接替代 pd.read_excel(header=4) 调用
   - 支持自定义分隔符和列范围

3. read_excel_compatible() 静态方法
   - 完全兼容原有 read_excel 函数接口
   - 可以直接替换现有代码中的 read_excel 调用
   - 返回 (hyperlinks, df) 元组保持接口一致

使用示例：
=========

# 方式1：直接替代 pd.read_excel(header=4)
df = MultiLevelHeaderExtractor.read_excel_smart(
    file_path="data.xlsx",
    sheet_name="要件一覧",
    header_start_row=1,
    header_end_row=4,
    data_start_row=5
)

# 方式2：兼容原有接口
hyperlinks, df = MultiLevelHeaderExtractor.read_excel_compatible(
    file_path="data.xlsx",
    header=4,
    sheet_name="要件一覧"
)

# 方式3：高级用法
extractor = MultiLevelHeaderExtractor("data.xlsx", "要件一覧")
df = extractor.read_excel_with_multi_level_headers(
    header_start_row=1,
    header_end_row=4,
    data_start_row=5,
    separator=" - "  # 自定义分隔符
)
"""

import pandas as pd
from openpyxl import load_workbook
from typing import List, Dict, Tuple, Optional


class MultiLevelHeaderExtractor:
    """
    通用的多级表头提取器
    支持从Excel文件中提取复杂的多级表头结构
    """

    def __init__(self, file_path: str, sheet_name: str = None):
        """
        初始化提取器
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，如果为None则使用第一个工作表
        """
        self.file_path = file_path
        # data_only 只读取值,不取公式
        self.wb = load_workbook(file_path, data_only=True)

        if sheet_name is None:
            self.sheet_name = self.wb.sheetnames[0]
        else:
            self.sheet_name = sheet_name

        self.ws = self.wb[self.sheet_name]

    def extract_headers(self,
                        start_row: int,
                        end_row: int,
                        start_col: int = 1,
                        end_col: Optional[int] = None,
                        exclude_hidden_rows: bool = True) -> Dict:
        """
        提取多级表头
        
        Args:
            start_row: 表头开始行（1-based）
            end_row: 表头结束行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            
        Returns:
            包含表头信息的字典
        """
        if end_col is None:
            end_col = self.ws.max_column

        # 检查隐藏行
        visible_rows = []
        for row in range(start_row, end_row + 1):
            if not exclude_hidden_rows or not self._is_row_hidden(row):
                visible_rows.append(row)

        # 提取表头数据
        header_data = self._extract_raw_header_data(visible_rows, start_col, end_col)

        # 处理合并单元格
        merged_header_data = self._process_merged_cells(header_data, visible_rows, start_col, end_col)

        # 构建层次结构
        hierarchical_headers = self._build_hierarchical_structure(merged_header_data)

        # 生成平铺的列名
        flat_columns = self._generate_flat_columns(hierarchical_headers)

        return {
            'raw_data': header_data,
            'merged_data': merged_header_data,
            'hierarchical': hierarchical_headers,
            'flat_columns': flat_columns,
            'visible_rows': visible_rows,
            'column_range': (start_col, end_col)
        }

    def _is_row_hidden(self, row_num: int) -> bool:
        """检查行是否隐藏"""
        try:
            row_dimension = self.ws.row_dimensions.get(row_num)
            return row_dimension and row_dimension.hidden
        except:
            return False

    def _extract_raw_header_data(self, rows: List[int], start_col: int, end_col: int) -> List[List]:
        """提取原始表头数据"""
        header_data = []
        for row in rows:
            row_data = []
            for col in range(start_col, end_col + 1):
                cell = self.ws.cell(row=row, column=col)
                value = cell.value if cell.value is not None else ""
                # 清理换行符和多余空格
                if isinstance(value, str):
                    value = value.replace('\n', ' ').strip()
                row_data.append(value)
            header_data.append(row_data)
        return header_data

    def _process_merged_cells(self, header_data: List[List], rows: List[int], start_col: int, end_col: int) -> List[
        List]:
        """处理合并单元格"""
        merged_data = [row[:] for row in header_data]  # 深拷贝

        # 获取所有合并单元格范围
        merged_ranges = []
        for merged_range in self.ws.merged_cells.ranges:
            # 检查合并范围是否在我们的表头区域内
            if (merged_range.min_row >= min(rows) and
                    merged_range.max_row <= max(rows) and
                    merged_range.min_col >= start_col and
                    merged_range.max_col <= end_col):
                merged_ranges.append(merged_range)

        # 处理每个合并单元格
        for merged_range in merged_ranges:
            # 获取合并单元格的值
            top_left_cell = self.ws.cell(row=merged_range.min_row, column=merged_range.min_col)
            merged_value = top_left_cell.value if top_left_cell.value is not None else ""
            if isinstance(merged_value, str):
                merged_value = merged_value.replace('\n', ' ').strip()

            # 将值填充到合并范围内的所有单元格
            for row in range(merged_range.min_row, merged_range.max_row + 1):
                if row in rows:
                    row_idx = rows.index(row)
                    for col in range(merged_range.min_col, merged_range.max_col + 1):
                        if start_col <= col <= end_col:
                            col_idx = col - start_col
                            merged_data[row_idx][col_idx] = merged_value

        return merged_data

    def _build_hierarchical_structure(self, merged_data: List[List]) -> List[Dict]:
        """构建层次结构"""
        if not merged_data:
            return []

        num_cols = len(merged_data[0])
        hierarchical = []

        for col_idx in range(num_cols):
            column_hierarchy = []
            for row_data in merged_data:
                if col_idx < len(row_data):
                    value = row_data[col_idx]
                    if value:  # 只添加非空值
                        column_hierarchy.append(value)

            hierarchical.append({
                'column_index': col_idx,
                'hierarchy': column_hierarchy,
                'levels': len(column_hierarchy)
            })

        return hierarchical

    def _generate_flat_columns(self, hierarchical_headers: List[Dict], separator: str = ' | ') -> List[str]:
        """生成平铺的列名"""
        flat_columns = []

        for col_info in hierarchical_headers:
            hierarchy = col_info['hierarchy']
            if hierarchy:
                # 将层次结构连接成单一列名
                # 过滤掉重复的值和空值
                unique_parts = []
                for part in hierarchy:
                    if part and str(part).strip() and (
                            not unique_parts or str(part).strip() != str(unique_parts[-1]).strip()):
                        unique_parts.append(str(part).strip())

                if unique_parts:
                    column_name = separator.join(unique_parts)
                else:
                    column_name = f"Column_{col_info['column_index'] + 1}"
            else:
                column_name = f"Column_{col_info['column_index'] + 1}"

            flat_columns.append(column_name)

        return flat_columns

    def get_column_mapping(self, header_info: Dict) -> Dict[str, int]:
        """
        获取列名到列索引的映射

        Args:
            header_info: extract_headers返回的表头信息

        Returns:
            列名到列索引的映射字典
        """
        mapping = {}
        for i, col_name in enumerate(header_info['flat_columns']):
            mapping[col_name] = i
        return mapping

    def find_columns_by_keyword(self, header_info: Dict, keyword: str, case_sensitive: bool = False) -> List[
        Tuple[int, str]]:
        """
        根据关键词查找列

        Args:
            header_info: extract_headers返回的表头信息
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写

        Returns:
            匹配的列索引和列名的列表
        """
        matches = []
        search_keyword = keyword if case_sensitive else keyword.lower()

        for i, col_name in enumerate(header_info['flat_columns']):
            search_text = col_name if case_sensitive else col_name.lower()
            if search_keyword in search_text:
                matches.append((i, col_name))

        return matches

    def auto_detect_header_rows(self, keyword: str, max_search_rows: int = 20) -> Tuple[Optional[int], Optional[int]]:
        """
        自动检测包含关键词的表头行

        Args:
            keyword: 搜索关键词
            max_search_rows: 最大搜索行数

        Returns:
            (表头开始行, 表头结束行) 的元组，如果未找到则返回 (None, None)
        """
        for row in range(1, min(max_search_rows + 1, self.ws.max_row + 1)):
            for col in range(1, min(20, self.ws.max_column + 1)):  # 只搜索前20列
                cell = self.ws.cell(row=row, column=col)
                if cell.value and keyword in str(cell.value):
                    # 找到关键词，尝试确定表头范围
                    header_start = row

                    # 向下搜索，找到表头结束行
                    header_end = row
                    for check_row in range(row + 1, min(row + 5, self.ws.max_row + 1)):
                        has_content = False
                        for check_col in range(1, min(20, self.ws.max_column + 1)):
                            check_cell = self.ws.cell(row=check_row, column=check_col)
                            if check_cell.value and str(check_cell.value).strip():
                                has_content = True
                                break

                        if has_content:
                            header_end = check_row
                        else:
                            break

                    return header_start, header_end

        return None, None

    def clean_dataframe(self, df: pd.DataFrame, remove_duplicates: bool = True,
                        remove_empty: bool = True) -> pd.DataFrame:
        """
        清洗DataFrame数据

        Args:
            df: 要清洗的DataFrame
            remove_duplicates: 是否移除重复行和列
            remove_empty: 是否移除空行和列

        Returns:
            清洗后的DataFrame
        """
        df_cleaned = df.copy()

        if remove_empty:
            # 移除全为空的行和列
            df_cleaned = df_cleaned.dropna(how='all').dropna(axis=1, how='all')

        if remove_duplicates:
            # 移除重复的行
            df_cleaned = df_cleaned.drop_duplicates()
            # 移除重复的列
            df_cleaned = df_cleaned.loc[:, ~df_cleaned.T.duplicated()]

        return df_cleaned

    def filter_by_column_pattern(self, df: pd.DataFrame, column_name: str,
                                 pattern: str, keep_matches: bool = True) -> pd.DataFrame:
        """
        根据列的正则表达式模式过滤数据

        Args:
            df: 要过滤的DataFrame
            column_name: 列名
            pattern: 正则表达式模式
            keep_matches: True保留匹配的行，False保留不匹配的行

        Returns:
            过滤后的DataFrame
        """
        if column_name not in df.columns:
            raise KeyError(f"列 '{column_name}' 不存在")

        # 转换为字符串
        df[column_name] = df[column_name].astype(str)

        # 应用正则表达式过滤
        if keep_matches:
            mask = df[column_name].str.fullmatch(pattern, na=False)
        else:
            mask = ~df[column_name].str.fullmatch(pattern, na=False)

        return df[mask]

    def to_dict_by_key(self, df: pd.DataFrame, key_column: str) -> Dict:
        """
        将DataFrame转换为以指定列为键的字典

        Args:
            df: 要转换的DataFrame
            key_column: 作为键的列名

        Returns:
            {key_value: row_dict, ...} 格式的字典
        """
        if key_column not in df.columns:
            raise KeyError(f"键列 '{key_column}' 不存在")

        return {item[key_column]: item for item in df.to_dict(orient='records')}

    def extract_data_with_headers(self,
                                  header_start_row: int,
                                  header_end_row: int,
                                  data_start_row: int,
                                  start_col: int = 1,
                                  end_col: Optional[int] = None,
                                  exclude_hidden_rows: bool = False,
                                  max_empty_rows: int = 3) -> Tuple[pd.DataFrame, Dict]:
        """
        提取带有多级表头的数据

        Args:
            header_start_row: 表头开始行
            header_end_row: 表头结束行
            data_start_row: 数据开始行
            start_col: 开始列
            end_col: 结束列
            exclude_hidden_rows: 是否排除隐藏行
            max_empty_rows: 连续空行数量阈值，超过此数量认为数据结束

        Returns:
            元组：(DataFrame, 表头信息字典)
        """
        # 提取表头
        header_info = self.extract_headers(
            header_start_row, header_end_row, start_col, end_col, exclude_hidden_rows
        )

        # 确定实际的列范围
        actual_end_col = header_info['column_range'][1]

        # 读取数据
        data_rows = []
        for row in range(data_start_row, self.ws.max_row + 1):
            if not exclude_hidden_rows or not self._is_row_hidden(row):
                row_data = []
                for col in range(start_col, actual_end_col + 1):
                    cell = self.ws.cell(row=row, column=col)
                    value = cell.value
                    row_data.append(value)
                data_rows.append(row_data)

                # 如果连续多行都是空的，可能已经到了数据末尾
                if all(v is None or v == "" for v in row_data):
                    # 检查接下来几行是否也是空的
                    empty_count = 0
                    for check_row in range(row + 1, min(row + 5, self.ws.max_row + 1)):
                        check_data = [self.ws.cell(row=check_row, column=col).value
                                      for col in range(start_col, actual_end_col + 1)]
                        if all(v is None or v == "" for v in check_data):
                            empty_count += 1
                        else:
                            break
                    if empty_count >= max_empty_rows:  # 连续空数据，认为到了末尾
                        break

        # 移除末尾的空行
        while data_rows and all(v is None or v == "" for v in data_rows[-1]):
            data_rows.pop()

        # 创建DataFrame
        df = pd.DataFrame(data_rows, columns=header_info['flat_columns'])

        return df, header_info

    def read_excel_with_multi_level_headers(self,
                                            header_start_row: int,
                                            header_end_row: int,
                                            data_start_row: int,
                                            start_col: int = 1,
                                            end_col: Optional[int] = None,
                                            exclude_hidden_rows: bool = True,
                                            max_empty_rows: int = 3,
                                            separator: str = ' | ') -> pd.DataFrame:
        """
        使用多级表头提取器读取Excel数据，替代简单的header参数方式

        这个方法专门用来解决当使用header=4等参数时，遇到第三行和第四行合并单元格
        导致列名找不到的问题。

        Args:
            header_start_row: 表头开始行（1-based）
            header_end_row: 表头结束行（1-based）
            data_start_row: 数据开始行（1-based）
            start_col: 开始列（1-based）
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            max_empty_rows: 连续空行数量阈值，超过此数量认为数据结束
            separator: 多级表头连接符

        Returns:
            处理好的DataFrame，列名为合并后的多级表头

        Example:
            # 替代 pd.read_excel(file_path, header=4)
            extractor = MultiLevelHeaderExtractor(file_path, sheet_name)
            df = extractor.read_excel_with_multi_level_headers(
                header_start_row=1,  # 从第1行开始的表头
                header_end_row=4,    # 到第4行结束的表头
                data_start_row=5     # 从第5行开始的数据
            )
        """
        # 提取带有多级表头的数据
        df, header_info = self.extract_data_with_headers(
            header_start_row=header_start_row,
            header_end_row=header_end_row,
            data_start_row=data_start_row,
            start_col=start_col,
            end_col=end_col,
            exclude_hidden_rows=exclude_hidden_rows,
            max_empty_rows=max_empty_rows
        )

        # 如果需要自定义分隔符，重新生成列名
        if separator != ' | ':
            header_info = self.extract_headers(
                header_start_row, header_end_row, start_col, end_col, exclude_hidden_rows
            )
            flat_columns = self._generate_flat_columns(header_info['hierarchical'], separator)
            df.columns = flat_columns

        return df

    @staticmethod
    def read_excel_smart(file_path: str,
                         sheet_name: str = None,
                         header_start_row: int = 1,
                         header_end_row: int = 4,
                         data_start_row: int = 5,
                         start_col: int = 1,
                         end_col: Optional[int] = None,
                         exclude_hidden_rows: bool = True,
                         separator: str = ' | ') -> pd.DataFrame:
        """
        静态方法：智能读取Excel文件，自动处理多级表头和合并单元格

        这是一个便捷方法，可以直接替代 pd.read_excel(file_path, header=4) 这样的调用

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，如果为None则使用第一个工作表
            header_start_row: 表头开始行（1-based），默认第1行
            header_end_row: 表头结束行（1-based），默认第4行
            data_start_row: 数据开始行（1-based），默认第5行
            start_col: 开始列（1-based），默认第1列
            end_col: 结束列（1-based），如果为None则自动检测
            exclude_hidden_rows: 是否排除隐藏行
            separator: 多级表头连接符

        Returns:
            处理好的DataFrame

        Example:
            # 替代 pd.read_excel(file_path, header=4)
            df = MultiLevelHeaderExtractor.read_excel_smart(
                file_path="data.xlsx",
                sheet_name="要件一覧",
                header_start_row=1,
                header_end_row=4,
                data_start_row=5
            )
        """
        extractor = MultiLevelHeaderExtractor(file_path, sheet_name)
        return extractor.read_excel_with_multi_level_headers(
            header_start_row=header_start_row,
            header_end_row=header_end_row,
            data_start_row=data_start_row,
            start_col=start_col,
            end_col=end_col,
            exclude_hidden_rows=exclude_hidden_rows,
            separator=separator
        )

    def save_to_excel(self, df: pd.DataFrame, output_path: str, sheet_name: str = "Sheet1"):
        """
        将处理后的数据保存到Excel文件

        Args:
            df: 要保存的DataFrame
            output_path: 输出文件路径
            sheet_name: 工作表名称
        """
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        print(f"数据已保存到: {output_path}")

    def print_header_analysis(self, header_info: Dict):
        """
        打印表头分析结果

        Args:
            header_info: extract_headers返回的表头信息
        """
        print("=== 表头分析结果 ===")
        print(f"可见行: {header_info['visible_rows']}")
        print(f"列范围: {header_info['column_range']}")
        print(f"总列数: {len(header_info['flat_columns'])}")

        print("\n=== 层次结构示例（前10列）===")
        for i, col_info in enumerate(header_info['hierarchical'][:10]):
            print(f"列{col_info['column_index'] + 1}: {col_info['hierarchy']}")

        print("\n=== 平铺列名示例（前10列）===")
        for i, col_name in enumerate(header_info['flat_columns'][:10]):
            print(f"列{i + 1}: {col_name}")


# 使用示例和测试函数
def test_extractor():
    """测试函数"""
    file_path = "C:/tdd_input/R4小_Epic v047_要件分析.xlsx"
    sheet_name = "要件一览"

    extractor = MultiLevelHeaderExtractor(file_path, sheet_name)

    # 提取完整数据
    print("\n=== 提取完整数据 ===")
    df, _ = extractor.extract_data_with_headers(
        header_start_row=3,
        header_end_row=5,
        data_start_row=6,
        start_col=1,
        end_col=100,  # 只看前10列
        exclude_hidden_rows=True
    )
    content = []
    for idx, row in df.iterrows():
        if str(row.get('ARチケットNO')).upper() != 'NAN':
            ar_link = ""

            # 准备数据
            row_data = {
                "row_idx": idx,
                "ar_no": str(row.get('ARチケットNO')),
                "ar_title": str(row.get('ARチケットタイトル')),
                "ar_link": ar_link,
                "epic_name": str(row.get('エピック名')),
                "req_change_content": str(row.get('概要')),
                "p_no": str(row.get('要件チケットNO') or row.get('要件No.')),
                "step_name": str(row.get("対応イベント")),  # 项目阶段名
                "req_file_name": str(row.get("ARチケットタイトル")),  # 要件式样名
                "if_check": str(row.get("HMI组 | 对应要否"))
            }
            content.append(row_data)


def test_smart_read():
    """测试智能读取方法"""
    file_path = "C:/tdd_input/R4小_Epic v047_要件分析.xlsx"
    sheet_name = "要件一览"

    print("=== 测试智能读取方法 ===")

    # 使用新的智能读取方法，替代 pd.read_excel(header=4)
    df = MultiLevelHeaderExtractor.read_excel_smart(
        file_path=file_path,
        sheet_name=sheet_name,
        header_start_row=1,  # 从第1行开始的表头
        header_end_row=4,  # 到第4行结束的表头
        data_start_row=5  # 从第5行开始的数据
    )


if __name__ == "__main__":
    test_extractor()
    print("\n" + "=" * 50 + "\n")
    # test_smart_read()
