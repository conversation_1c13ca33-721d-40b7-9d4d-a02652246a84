# -*- coding: utf-8 -*-
"""
@File    : position.py
<AUTHOR> zhenp
@Date    : 2025-05-30 16:53
@Desc    : Description of the file
"""


# AI generation start
class Position:
    def __init__(self):
        self._x = .0  # X-coordinate
        self._y = .0  # Y-coordinate
        self._width = .0  # Width
        self._height = .0  # Height

    @classmethod
    def from_dict(cls, data: dict) -> "Position":
        """Create a Position instance from a dictionary."""
        obj = cls()
        obj._x = data.get("x", .0)
        obj._y = data.get("y", .0)
        obj._width = data.get("width", .0)
        obj._height = data.get("height", .0)
        return obj

    def to_dict(self) -> dict:
        """Convert the Position instance to a dictionary."""
        return {
            "x": self._x,
            "y": self._y,
            "width": self._width,
            "height": self._height
        }

    # Getter and setter methods for _x
    @property
    def x(self):
        return self._x

    @x.setter
    def x(self, value):
        assert isinstance(value, (float, int)), "x must be a number (float or int)"
        self._x = float(value)

    # Getter and setter methods for _y
    @property
    def y(self):
        return self._y

    @y.setter
    def y(self, value):
        assert isinstance(value, (float, int)), "y must be a number (float or int)"
        self._y = float(value)

    # Getter and setter methods for _width
    @property
    def width(self):
        return self._width

    @width.setter
    def width(self, value):
        assert isinstance(value, (float, int)), "width must be a number (float or int)"
        self._width = float(value)

    # Getter and setter methods for _height
    @property
    def height(self):
        return self._height

    @height.setter
    def height(self, value):
        assert isinstance(value, (float, int)), "height must be a number (float or int)"
        self._height = float(value)


# AI generation end

class Context:
    def __init__(self):
        self.header_list = ""
        self.current_row = ""
        self.cell_header = ""
        self.pre_context = ""
        self.next_context = ""

    header_list: str  # table
    current_row: str  # table
    cell_header: str  # table
    pre_context: str  # text and table
    next_context: str  # text and table

    def to_dict(self):
        """
        将 TableBaseObject 对象转换为字典
        """
        return {
            "header_list": self.header_list,
            "current_row": self.current_row,
            "cell_header": self.cell_header,
            "pre_context": self.pre_context,
            "next_context": self.next_context,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Context":
        obj = cls()
        obj.header_list = data.get("header_list", '')
        obj.current_row = data.get("current_row", '')
        obj.cell_header = data.get("cell_header", '')
        obj.pre_context = data.get("pre_context", '')
        obj.next_context = data.get("next_context", '')
        return obj
