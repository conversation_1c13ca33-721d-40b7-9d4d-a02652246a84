"""
文件名: func_book_sheet_oprate_util.py

功能:
该文件包含一系列用于操作 Excel 工作簿和工作表的工具函数，包括清理工作表区域、复制工作表、填充函数信息、生成函数式样书等功能。
"""
import os
import time
from typing import List
from datetime import datetime

import xlwings as xw
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
import requests
from loguru import logger


from sdw_agent.config.env import ENV
from sdw_agent.service.func_analyze_book.models import FunctionDetailData
from sdw_agent.service.func_analyze_book.util.config_manager import config_manager_func_analyze_book


def clear_worksheet_area(ws, start_row, start_col, fill_type, end_row=None, end_col=None):
    """
    清除工作表中指定区域的内容

    :param ws: 工作表对象
    :param start_row: 起始行号 (从1开始)
    :param start_col: 起始列号 (从1开始)
    :param end_row: 结束行号 (可选，默认为工作表最大行)
    :param end_col: 结束列号 (可选，默认为工作表最大列)
    """
    # 设置默认结束位置
    if end_row is None:
        end_row = ws.max_row
    if end_col is None:
        end_col = ws.max_column

    # 验证参数
    if start_row < 1 or start_col < 1 or end_row < start_row or end_col < start_col:
        raise ValueError("无效的区域参数")

    # 清除单元格内容
    for row in range(start_row, end_row + 1):
        for col in range(start_col, end_col + 1):
            try:
                cell = ws.cell(row=row, column=col)

                # 清除值
                cell.value = fill_type

                # 清除公式
                if cell.data_type == 'f':
                    cell.data_type = 'n'
            except:
                continue


def copy_sheet_and_save(wb, source_sheet_name, func_list):
    """
    复制Excel文件中的一个工作表并保存为新文件

    参数:
    input_file (str): 输入Excel文件路径
    source_sheet_name (str): 源工作表名称
    new_sheet_name (str): 新工作表名称
    output_file (str): 输出Excel文件路径
    """
    try:
        for func in func_list:
            # 获取源工作表
            source_sheet = wb[source_sheet_name]

            # 创建副本工作表
            new_sheet = wb.copy_worksheet(source_sheet)

            # 设置新工作表名称
            new_sheet.title = func.func_name
            # copy_sheet_content(source_sheet, new_sheet)
            # # 将工作表添加到工作簿
            # wb._add_sheet(new_sheet)

        return

    except Exception as e:
        logger.info(f"发生错误: {e}")
        return False


def find_rows_with_text_in_column(sheet, column_index, target_text):
    """
    在Excel工作表的指定列中查找包含特定文本的所有行

    参数:
        file_path: Excel文件路径
        sheet_name: 工作表名称
        column_index: 列索引（从1开始，如1表示A列，2表示B列）
        target_text: 要查找的目标文本

    返回:
        包含目标文本的行索引列表（行号从1开始）
    """

    ws = sheet

    # 遍历指定列的所有行，查找包含目标文本的单元格
    for row_idx, row in enumerate(ws.iter_rows(min_col=1, max_col=column_index, values_only=True), 1):
        cell_value = " ".join(str(cell) if cell is not None else "" for cell in row)
        if cell_value is not None and target_text in str(cell_value):
            return row_idx

    logger.info(f"未找到包含'{target_text}'的行")
    return None


def clear_cells_of_func_change(wb):
    """
    清理工作簿中指定工作表的单元格内容。

    功能说明：
    - 查找工作表 "関数一覧" 中 "関数定義名" 所在的行。
    - 从 "関数定義名" 的下一行开始，清空 F 列之前的所有单元格（C、D、E、F 列）。
    - 遇到空行时停止处理。

    参数:
        wb (openpyxl.Workbook): 工作簿对象。

    返回:
        None
    """
    # 获取指定工作表
    if '関数一覧' in wb.sheetnames:
        ws = wb['関数一覧']
    else:
        logger.info("错误：未找到工作表'関数一覧'")
        return

    # 查找"関数定義名"所在的行
    function_def_row = None
    for row_num, row in enumerate(ws.iter_rows(min_row=1, max_row=ws.max_row, values_only=True), 1):
        row_str = " ".join(str(cell) if cell is not None else "" for cell in row)
        if row and '関数定義名' in row_str:  # 假设在A列查找
            function_def_row = row_num
            break

    if not function_def_row:
        logger.error("错误：未找到'関数定義名'所在的行")
        return

    logger.info(f"找到'関数定義名'在第{function_def_row}行")

    # 从関数定義名的下一行开始处理
    current_row = function_def_row + 2

    # 处理下面的非空行，直到遇到空行
    while current_row <= ws.max_row:
        # 检查当前行是否为空行（所有单元格都为空）
        is_empty = all(cell.value is None for cell in ws[current_row])

        if is_empty:
            break  # 遇到空行则停止处理

        # 清空F列之前的所有单元格（A-E列）
        for col_letter in ['C', 'D', 'E', 'F']:
            ws[f'{col_letter}{current_row}'] = None

        current_row += 1

    logger.info(f"已清空{function_def_row + 1}行到{current_row - 1}行中F列之前的单元格")
    return


def fill_function_info_to_sheet(wb, function_list):
    """
    将函数信息填充到Excel工作表'ab'中

    参数:
        wb: openpyxl工作簿对象
        function_list: 包含函数信息的列表，每个元素是一个字典
        file_path: 原文件路径，用于生成输出文件名
    """
    # 创建或获取工作表ab
    if '関数一覧' not in wb.sheetnames:
        raise Exception("工作表'関数一覧'不存在，请检查")
    else:
        ws_func_overview = wb['関数一覧']
        title_index = find_rows_with_text_in_column(ws_func_overview, 7, "No")

    # 填充函数信息到工作表ab
    for i, func in enumerate(function_list):  #
        ws_func_overview[f'B{i + title_index + 1}'] = "■"
        ws_func_overview[f'C{i + title_index + 1}'] = i + 1
        ws_func_overview[f'D{i + title_index + 1}'] = os.path.basename(func.func_file_name)
        ws_func_overview[f'E{i + title_index + 1}'] = func.func_name
        ws_func_overview[f'F{i + title_index + 1}'] = func.func_desc

    logger.info(f"已将{len(function_list)}条函数信息填充到工作表")


def write_func_overview_sheet(wb, func_change_info_list):
    """
     将函数详细信息写入工作表并生成图表。

     功能说明：
     - 遍历函数变更信息列表，将每个函数的详细信息写入对应的工作表。
     - 生成函数调用关系图表并插入到工作表中。

     参数:
         wb (openpyxl.Workbook): 工作簿对象。
         func_change_info_list (list): 包含函数变更信息的列表。
         filtered_names (list): 过滤后的工作表名称列表。

     返回:
         list: 包含生成图表的临时文件路径列表。
     """
    try:
        clear_cells_of_func_change(wb)
        fill_function_info_to_sheet(wb, func_change_info_list)

    except Exception as e:
        logger.error(f"发生错误: {e}")
        raise e
    return


def column_letter_to_number(column_letter: str) -> int:
    """
    将 Excel 列字母转换为列数字索引

    :param column_letter: Excel 列字母（如 "A", "B", "AA"）
    :return: 列数字索引（从1开始）
    """
    # 确保输入是大写字母
    column_letter = column_letter.upper()

    # 初始化结果
    column_number = 0

    # 从右向左处理每个字母
    for i, char in enumerate(reversed(column_letter)):
        # 计算字母的数值（A=1, B=2, ..., Z=26）
        char_value = ord(char) - ord('A') + 1

        # 计算位置权重（26的i次方）
        position_weight = 26 ** i

        # 累加结果
        column_number += char_value * position_weight

    return column_number


def create_func_process_picture(uml_code):
    """
     根据 UML 代码生成函数调用关系图。

     功能说明：
     - 发送 POST 请求到 PlantUML 服务，生成函数调用关系图。
     - 返回生成的图片 URL。

     参数:
         uml_code (str): UML 代码。

     返回:
         str: 图片 URL。
     """
    # post_url = ENV.config.plant_uml.post_url
    post_url = config_manager_func_analyze_book.config['plant_uml']['post_url']
    ret_pic = None
    retry_num = 0

    try:
        while ret_pic is None and retry_num < 3:

            # 发送 POST 请求
            response = requests.post(
                post_url,
                data={"text": uml_code}
            )
            if response.status_code == 200:
                # 提取响应中的图片 URL
                html_content = response.text
                # 简单解析图片 URL（实际可能需要更复杂的 HTML 解析）
                start_index = html_content.find('/png/')
                if start_index != -1:
                    end_index = html_content.find('"', start_index)
                    if end_index != -1:
                        ret_pic = f"http://172.30.19.19:9001{html_content[start_index:end_index]}"
                        logger.info(f"生成的图片 URL: {ret_pic}")
                else:
                    logger.error("未找到图片 URL，可能 UML 代码有错误")
                    logger.error("响应内容:", html_content)
                    retry_num += 1
                    time.sleep(2)  # 增加重试延时，比如2秒
            else:
                logger.error(f"请求失败: {response.status_code}")
                logger.error("错误信息:", response.text)
                retry_num += 1
                time.sleep(2)  # 增加重试延时，比如2秒
    except Exception as e:
        logger.error(f"发生错误: {e}")
        retry_num += 1
        time.sleep(2)  # 增加重试延时，比如2秒

    return ret_pic


def download_plantuml_image(url, save_path, resize=(200, 200)):
    """
    下载 PlantUML 图片，支持调整尺寸
    :param url: 图片 URL（如 http://xxx/png/xxx）
    :param save_path: 临时保存路径
    :param resize: (宽度, 高度)，默认缩放为 200x200（可选）
    :return: 成功返回保存路径，失败返回 None
    """
    try:
        # 1. 下载图片
        response = requests.get(url, timeout=15)
        response.raise_for_status()  # 检查 HTTP 错误

        # 2. 保存到临时文件
        with open(save_path, 'wb') as f:
            f.write(response.content)

        # 3. 调整图片尺寸（可选）
        # if resize:
        #     img = PILImage.open(save_path)
        #     img = img.resize(resize, PILImage.LANCZOS)  # 高质量缩放
        #     img.save(save_path)

        return save_path
    except Exception as e:
        logger.error(f"图片下载失败: {str(e)}")
        # 清理临时文件
        if os.path.exists(save_path):
            os.remove(save_path)
        return None


def insert_image_to_excel(ws, image_path, row=100, column=10):
    """
    将图片插入 Excel 指定单元格
    :param excel_path: Excel 文件路径
    :param image_path: 图片本地路径
    :param row: 目标行（从 1 开始）
    :param column: 目标列（从 1 开始，1=A 列，2=B 列...）
    """
    try:

        # 2. 插入图片到指定单元格
        img = Image(image_path)
        target_cell = ws.cell(row=row, column=column)  # 定位单元格
        img.anchor = target_cell.coordinate  # 图片锚点绑定到单元格
        ws.add_image(img)

        # 3. 调整行高（可选，避免图片被截断）
        ws.row_dimensions[row].height = 150  # 设置行高为 150（根据图片大小调整）
        # # 调整列宽（可选）
        # ws.column_dimensions[target_cell.column_letter].width = 30

        # 4. 保存文件
        logger.success(f"✅ 图片已插入到 Excel 第 {row} 行，第 {column} 列！")
    except Exception as e:
        logger.error(f"❌ Excel 操作失败: {str(e)}")


def fill_func_detail_cell(ws, func):
    """
    将函数详细信息填充到工作表的指定单元格。

    功能说明：
    - 根据函数信息填充工作表的 I/F 名、功能描述、公开范围等字段。
    - 清理工作表中旧数据并插入新数据。
    - 生成函数调用关系图表并插入到工作表中。

    参数:
        ws (openpyxl.Worksheet): 工作表对象。
        func (FunctionDetailData): 函数详细信息对象。

    返回:
        str: 临时图片路径。
    """
    IF_name_index = find_rows_with_text_in_column(ws, 4, "I/F名")
    ws.cell(row=IF_name_index, column=7).value = func.func_name
    func_desc_index = find_rows_with_text_in_column(ws, 4, "機能")
    ws.cell(row=func_desc_index, column=7).value = func.func_desc
    func_type_index = find_rows_with_text_in_column(ws, 4, "公開範囲")
    ws.cell(row=func_type_index, column=7).value = func.functionType

    # 入参
    func_param_index = find_rows_with_text_in_column(ws, 4, "引数") + 1
    # 返回值
    func_return_index = find_rows_with_text_in_column(ws, 4, "戻り値") + 1

    clear_worksheet_area(ws, func_param_index, column_letter_to_number("G"), None, end_row=func_return_index - 2,
                         end_col=column_letter_to_number("Q"))

    if func.parameters:
        for i, item in enumerate(func.parameters):
            ws.cell(row=func_param_index + i, column=column_letter_to_number("K")).value = item.name
            ws.cell(row=func_param_index + i, column=column_letter_to_number("I")).value = item.type
            ws.cell(row=func_param_index + i, column=column_letter_to_number("G")).value = item.direction
            ws.cell(row=func_param_index + i, column=column_letter_to_number("Q")).value = item.desc
    else:
        ws.cell(row=func_param_index, column=column_letter_to_number("K")).value = "-"
        ws.cell(row=func_param_index, column=column_letter_to_number("I")).value = "-"
        ws.cell(row=func_param_index, column=column_letter_to_number("G")).value = "-"
        ws.cell(row=func_param_index, column=column_letter_to_number("Q")).value = "-"

    ws.cell(row=func_return_index,
            column=column_letter_to_number("G")).value = func.return_type if func.return_type not in ["", None] else "-"
    ws.cell(row=func_return_index,
            column=column_letter_to_number("K")).value = func.return_var if func.return_var not in ["", None] else "-"
    ws.cell(row=func_return_index,
            column=column_letter_to_number("Q")).value = func.return_desc if func.return_desc not in ["", None] else "-"

    # 调用关系
    func_called_index = find_rows_with_text_in_column(ws, 4, "使用IF") + 1
    # 全局变量
    func_RAM_index = find_rows_with_text_in_column(ws, 4, "使用RAM") + 1
    # 局部变量
    func_variable_index = find_rows_with_text_in_column(ws, 4, "ローカル変数") + 1
    # 制約事項
    func_limit_index = find_rows_with_text_in_column(ws, 4, "制約事項") + 1

    clear_worksheet_area(ws, func_called_index, column_letter_to_number("G"), None, end_row=func_RAM_index - 2,
                         end_col=column_letter_to_number("BD"))
    clear_worksheet_area(ws, func_RAM_index, column_letter_to_number("G"), None, end_row=func_variable_index - 2,
                         end_col=column_letter_to_number("BD"))
    clear_worksheet_area(ws, func_variable_index, column_letter_to_number("G"), None, end_row=func_limit_index - 2,
                         end_col=column_letter_to_number("BD"))

    for i, item in enumerate(func.func_parents):
        ws.cell(row=func_called_index + i, column=column_letter_to_number("AB")).value = func.func_parents[item]["mean"]
        ws.cell(row=func_called_index + i, column=column_letter_to_number("G")).value = func.func_parents[item][
            "module"]
        ws.cell(row=func_called_index + i, column=column_letter_to_number("P")).value = item
        ws.cell(row=func_called_index + i, column=column_letter_to_number("K")).value = \
            func.func_parents[item]["file"].split("\\")[-1]

    # 全局变量
    func_RAM_index = find_rows_with_text_in_column(ws, 4, "使用RAM") + 1
    # 局部变量

    for i, item in enumerate(func.allVariables["global_variables"]):
        ws.cell(row=func_RAM_index + i, column=column_letter_to_number("M")).value = item["name"]
        ws.cell(row=func_RAM_index + i, column=column_letter_to_number("AB")).value = item["desc"]
        ws.cell(row=func_RAM_index + i, column=column_letter_to_number("J")).value = item["type"]
        open_type = "globle" if item["name"].split("_")[1] == "g" else "static"
        ws.cell(row=func_RAM_index + i, column=column_letter_to_number("G")).value = open_type

    # 局部变量
    func_variable_index = find_rows_with_text_in_column(ws, 4, "ローカル変数") + 1
    # 制約事項

    for i, item in enumerate(func.allVariables["local_variables"]):
        ws.cell(row=func_variable_index + i, column=column_letter_to_number("K")).value = item["name"]
        ws.cell(row=func_variable_index + i, column=column_letter_to_number("G")).value = item["type"]
        ws.cell(row=func_variable_index + i, column=column_letter_to_number("Q")).value = item["desc"]

    # 制約事項
    func_process_index = find_rows_with_text_in_column(ws, 4, "関数設計") + 10
    ws.cell(row=func_process_index, column=column_letter_to_number("K")).value = func.func_process

    pic_url = create_func_process_picture(func.func_process)

    target_row = func_process_index + 2  # 目标行（第 100 行）
    target_column = 11  # 目标列（A 列，可改为 2=B 列等）

    current_time = datetime.now()
    timestamp = current_time.strftime("%Y%m%d_%H%M%S")

    output_data_path = os.path.normpath(ENV.config.output_data_path)
    if not os.path.exists(output_data_path):
        os.makedirs(output_data_path)
    temp_image_path = os.path.join(output_data_path,
                                  f"temp_image{timestamp}.png")

    # 2. 下载图片（若 URL 有效）
    if pic_url:
        downloaded_path = download_plantuml_image(
            url=pic_url,
            save_path=temp_image_path,
            # resize=(800, 800)  # 缩放
        )
        if downloaded_path:
            # 3. 插入到 Excel
            insert_image_to_excel(
                ws=ws,
                image_path=downloaded_path,
                row=target_row,
                column=target_column
            )
        else:
            logger.error("⚠️ 图片下载失败，无法插入 Excel。")
    else:
        logger.error("⚠️ 图片 URL 为空，请检查。")

    return temp_image_path


def copy_sheet(book_url, s_sheet, func_list: List[FunctionDetailData]):
    """
    复制工作簿中的工作表并保存为新文件。

    功能说明：
    - 打开工作簿并获取源工作表。
    - 遍历函数列表，为每个函数创建一个新的工作表。
    - 保存新工作簿并返回保存路径。

    参数:
        book_url (str): 工作簿路径。
        s_sheet (str): 源工作表名称。
        func_list (List[FunctionDetailData]): 函数列表，每个元素包含函数的详细信息。

    返回:
        tuple: 新工作簿路径和索引位置映射。
    """
    # 打开工作簿（若已打开可省略）
    try:
        # 打开工作簿（后台运行，不显示界面）
        app = xw.App(visible=False, add_book=False)
        wb = app.books.open(book_url)

        # 获取源工作表
        sheet_a = wb.sheets[s_sheet]

        # 遍历并复制工作表
        for func in func_list:
            # 复制工作表（在源工作表之前插入）
            try:
                sheet_a.api.Copy(Before=sheet_a.api)
                new_sheet_idx = sheet_a.api.Index
                logger.info(f"复制工作表 {sheet_a.name} 到 {new_sheet_idx}")
                # 获取新创建的工作表（Excel 索引从 1 开始）
                # new_sheet_idx = sheet_a.api.Index - 1
                new_sheet = wb.sheets[new_sheet_idx - 2]
                new_sheet.name = func.func_name[:30]
                # 打印所有工作表的名称
                logger.debug("工作表名称列表：")
                # for sheet in wb.sheets:

                index_location_tag = ["引数", "戻り値", "使用IF", "使用RAM", "ローカル変数", "制約事項"]
                index_location_tag_new = []
                index_location_map = {}

                # 在第search_col列中查找search_value
                start_row = None
                max_row = new_sheet.cells.last_cell.row  # 获取最后一行

                for row in range(1, max_row + 1):
                    cell_value = new_sheet.range((row, 4)).value
                    if cell_value in index_location_tag:
                        index_location_map[cell_value] = row + 1
                        index_location_tag_new.append(cell_value)
                        if len(index_location_tag) == len(index_location_tag_new):
                            break

                index_location_insert = {
                    "引数": max(0,
                                len(func.parameters) - (index_location_map["戻り値"] - index_location_map["引数"] - 1)),
                    "戻り値": max(0,
                                  len(func.parameters) - (index_location_map["使用IF"] - index_location_map["戻り値"])),
                    "使用IF": max(0, len(func.func_parents) - (
                            index_location_map["使用RAM"] - index_location_map["使用IF"] - 1)),
                    "使用RAM": max(0, len(func.allVariables["global_variables"]) - (
                            index_location_map["ローカル変数"] - index_location_map["使用RAM"] - 2)),
                    "ローカル変数": max(0, len(func.allVariables["local_variables"]) - (
                            index_location_map["制約事項"] - index_location_map["ローカル変数"] - 2)),
                }

                # 在第start_row行之前插入num_rows行
                for i in range(len(index_location_tag) - 1):
                    if index_location_insert[index_location_tag[i]] <= 0:
                        continue
                    start_row = index_location_map[index_location_tag[i]]
                    for j in range(i + 1, len(index_location_tag)):
                        index_location_map[index_location_tag[j]] = index_location_map[index_location_tag[j]] + \
                                                                    index_location_insert[index_location_tag[i]]
                    new_sheet.range(
                        f'{start_row + 1}:{start_row + index_location_insert[index_location_tag[i]]}').api.EntireRow.Insert(
                        Shift=xw.constants.InsertShiftDirection.xlShiftDown,
                        CopyOrigin=6  # xlFormatFromRightOrBelow
                    )

                # 重命名新工作表

            except Exception as e:
                logger.error(f"复制工作表{func.func_name}时发生错误: {e}")
                continue

        # 保存并关闭工作簿
        # 拼接文件名
        output_data_path = os.path.normpath(ENV.config.output_data_path)
        if not os.path.exists(output_data_path):
            os.makedirs(output_data_path)
        new_sheet_book = os.path.join(output_data_path,
                                      f"{os.path.splitext(os.path.basename(book_url))[0]}_new.xlsx")
        wb.save(new_sheet_book)
        wb.close()
        app.quit()  # 退出 Excel 应用

        logger.debug(f"成功复制 {len(func_list)} 个工作表")
        return new_sheet_book, index_location_map

    except Exception as e:
        logger.error(f"错误: {e}")
        # 确保异常发生时也能关闭资源
        if 'wb' in locals() and wb:
            wb.close()
        if 'app' in locals() and app:
            app.quit()
        return False


def write_func_detail_sheet(wb, func_change_info_list):
    """
    将功能详细信息写入工作表并生成图表

    参数:
        wb (openpyxl.Workbook): Excel工作簿对象
        func_change_info_list (list): 包含功能变更信息的列表，每个元素为包含功能信息的对象
        filtered_names (list): 过滤后的名称列表（当前未使用）

    返回:
        list: 包含生成图表对象的列表，每个元素对应一个功能的工作表图表

    异常:
        捕获所有异常并重新抛出，同时打印错误信息
    """
    try:
        # 清理原表
        temp_pic_list = []
        for func in func_change_info_list:
            # 获取源工作表
            temp_pic = fill_func_detail_cell(wb[func.func_name[:30]], func)
            temp_pic_list.append(temp_pic)
        return temp_pic_list

    except Exception as e:
        logger.error(f"发生错误: {e}")
        raise e


def write_func_book_sheet(func_book_path, func_change_info_list):
    """将函数变更信息写入Excel工作簿并生成最终的函数式样书

    Args:
        func_book_path (str): 源Excel模板文件的路径
        func_change_info_list (List): 包含函数变更信息的对象列表，每个对象应包含函数名称和相关信息

    Returns:
        str: 生成的函数式样书的保存路径
    """
    try:
        # 加载工作簿
        wb = load_workbook(func_book_path)
        need_sheet_names = ['表紙（承認用）', '関数一覧']
        # 检查源工作表是否存在
        if need_sheet_names[1] not in wb.sheetnames:
            logger.error(f"错误: 工作表 '{need_sheet_names[1]}' 不存在于文件 '{func_book_path}' 中")
            raise Exception(f"错误: 工作表 '{need_sheet_names[1]}' 不存在于文件 '{func_book_path}' 中")

        # 将排除列表转换为小写（不区分大小写）
        exclude_set = {name.strip().lower() for name in need_sheet_names}

        # 筛选工作表名称
        filtered_names = [name for name in wb.sheetnames if name.strip().lower() not in exclude_set]
        wb.close()

        new_sheet_book, _ = copy_sheet(func_book_path, filtered_names[0], func_change_info_list)

        wb = load_workbook(new_sheet_book)
        write_func_overview_sheet(wb, func_change_info_list)

        temp_pic_list = write_func_detail_sheet(wb, func_change_info_list)

        # 删除多余的工作表
        need_sheet = [item.func_name[0:30] for item in func_change_info_list if item.func_name]
        need_sheet.extend(["表紙（承認用）", "関数一覧"])
        logger.debug(f"需要保留的工作表：{need_sheet}")
        for sheet_name in [sheetname for sheetname in wb.sheetnames if sheetname not in need_sheet]:
            wb.remove(wb[sheet_name])
            logger.debug(f"已删除工作表 '{sheet_name}'")
        current_time = datetime.now()
        # 格式化时间戳：年_月_日_时_分_秒
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")
        # 拼接文件名
        output_data_path = os.path.normpath(ENV.config.output_data_path)
        if not os.path.exists(output_data_path):
            os.makedirs(output_data_path)
        ret_book = os.path.join(output_data_path, f"function_analysis_book_{timestamp}.xlsx")
        wb.save(ret_book)

        # for temp_pic in temp_pic_list:
        #     if os.path.exists(temp_pic):
        #         os.remove(temp_pic)

        target_row = 5  # 目标行
        columns = [chr(i) for i in range(ord('B'), ord('F') + 1)]  # 列范围
        copy_styles_between_rows(ret_book, "関数一覧", target_row, columns)

        logger.success(f"函数式样书保存成功，保存路径为: {ret_book}")
        return ret_book
    except Exception as e:
        logger.error(f"发生错误: {e}")
        raise e
    return


def copy_styles_between_rows(file_path, sheet_name, source_row, columns):
    """
    使用 xlwings 将指定行的单元格样式复制到目标行。

    :param file_path: Excel 文件路径
    :param sheet_name: 页签名称
    :param source_rows: 源行列表，例如 [6, 7]
    :param target_row: 目标行，例如 5
    :param columns: 要复制的列范围，例如 'A:G'
    """
    # 打开 Excel 文件
    app = xw.App(visible=False)  # 设置为不可见
    wb = xw.Book(file_path)
    sheet = wb.sheets[sheet_name]
    last_row = min(sheet.api.UsedRange.Rows.Count, 10)
    sheet.range("A5:G20").api.WrapText = True
    sheet.range("A5:G20").api.HorizontalAlignment = -4131
    sheet.range("A5:G20").api.VerticalAlignment = -4108
    try:
        for i in range(source_row + 1, last_row):
            for col in columns:
                # 获取源单元格和目标单元格
                source_cell = sheet.range(f"{col}{source_row}")
                target_cell = sheet.range(f"{col}{i}")

                # 复制样式
                target_cell.api.Interior.Color = source_cell.api.Interior.Color
                target_cell.api.Borders.Weight = source_cell.api.Borders.Weight
                target_cell.api.Borders.Color = source_cell.api.Borders.Color

    finally:
        # 保存并关闭工作簿
        wb.save()
        wb.close()
        app.quit()
