#!/usr/bin/env python3
"""
测试使用 Playwright 导出 Draw.io 图像
"""

import os
import urllib.parse
from pathlib import Path
from loguru import logger

from sdw_agent.service.template_manager import template_manager


def test_playwright_export():
    """测试 Playwright 导出功能"""
    print("🎯 测试 Playwright 导出 Draw.io")
    print("=" * 50)
    
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        print("❌ Playwright 未安装，请运行: poetry add playwright && poetry run playwright install chromium")
        return
    
    # 1. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 模板文件")
        return
    
    print(f"📁 使用文件: {drawio_file}")
    
    # 2. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        print(f"📄 文件大小: {len(xml_content)} 字符")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 3. URL 编码
    encoded_content = urllib.parse.quote(xml_content)
    
    # 4. 构建 draw.io URL
    drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=diagram#R{encoded_content}"
    
    print(f"🌐 Draw.io URL 长度: {len(drawio_url)} 字符")
    
    # 5. 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    output_png = output_dir / "playwright_export_test.png"
    
    # 6. 使用 Playwright 截图
    try:
        print("🚀 启动 Playwright...")
        
        with sync_playwright() as p:
            # 启动浏览器
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 设置视口大小
            page.set_viewport_size({"width": 1920, "height": 1080})
            
            print("🌐 正在加载 Draw.io...")
            
            # 访问页面
            page.goto(drawio_url, timeout=60000)  # 60秒超时
            
            print("⏳ 等待页面加载...")
            
            # 等待页面加载完成
            page.wait_for_timeout(8000)  # 等待 8 秒
            
            # 尝试等待特定元素
            try:
                # 等待图表容器出现
                page.wait_for_selector(".geDiagramContainer", timeout=15000)
                print("✅ 检测到图表容器")
            except:
                print("⚠️  未检测到图表容器，继续尝试截图")
            
            # 尝试等待 SVG 元素
            try:
                page.wait_for_selector("svg", timeout=10000)
                print("✅ 检测到 SVG 元素")
            except:
                print("⚠️  未检测到 SVG 元素，继续尝试截图")
            
            print(f"📸 正在截图到: {output_png}")
            
            # 截图
            page.screenshot(path=str(output_png), full_page=True, type="png")
            
            browser.close()
        
        # 7. 验证结果
        if output_png.exists() and output_png.stat().st_size > 0:
            print(f"✅ Playwright 导出成功!")
            print(f"📁 输出文件: {output_png}")
            print(f"📊 文件大小: {output_png.stat().st_size} 字节")
            
            # 8. 插入到 Excel
            try:
                from sdw_agent.util.excel.core import ExcelUtil, CellStyle
                from openpyxl.drawing.image import Image as OpenpyxlImage
                
                excel_file = output_dir / "playwright_export_test.xlsx"
                
                with ExcelUtil(str(excel_file), auto_create=True) as excel:
                    sheet_name = "Playwright导出测试"
                    
                    if sheet_name not in excel.get_sheet_names():
                        excel.create_sheet(sheet_name)
                    
                    # 添加标题
                    excel.write_cell(sheet_name, 1, 1, "Draw.io Playwright 导出测试")
                    title_style = CellStyle(
                        font_size=16,
                        font_bold=True,
                        alignment_horizontal="center"
                    )
                    excel.set_cell_style(sheet_name, 1, 1, title_style)
                    
                    # 插入图像
                    if hasattr(excel, '_get_worksheet'):
                        ws = excel._get_worksheet(sheet_name)
                        if ws:
                            img = OpenpyxlImage(str(output_png))
                            
                            # 调整大小
                            max_width = 1400
                            if img.width > max_width:
                                scale_factor = max_width / img.width
                                img.width = max_width
                                img.height = int(img.height * scale_factor)
                            
                            # 设置位置
                            target_cell = ws.cell(row=3, column=1)
                            img.anchor = target_cell.coordinate
                            ws.add_image(img)
                            
                            print(f"✅ 图像已插入 Excel: {excel_file}")
                            print(f"📊 图像尺寸: {img.width} x {img.height}")
                    
                    excel.save()
                
                print(f"\n🎉 测试完成！请查看以下文件:")
                print(f"   📸 PNG图像: {output_png}")
                print(f"   📊 Excel文件: {excel_file}")
                
            except Exception as e:
                print(f"⚠️  Excel 插入失败: {e}")
                print(f"   但 PNG 图像已成功生成: {output_png}")
                
        else:
            print("❌ 截图文件未生成或为空")
            
    except Exception as e:
        print(f"❌ Playwright 导出失败: {e}")
        logger.error(f"Playwright 导出异常: {e}")


def test_simple_drawio():
    """测试简单的 Draw.io 内容"""
    print("\n🧪 测试简单 Draw.io 内容")
    print("-" * 40)
    
    try:
        from playwright.sync_api import sync_playwright
    except ImportError:
        print("❌ Playwright 未安装")
        return
    
    # 创建简单的 Draw.io XML
    simple_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="22.1.16">
  <diagram name="Page-1" id="test">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Hello World" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="340" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Test Box" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="340" y="380" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>'''
    
    # URL 编码
    encoded_content = urllib.parse.quote(simple_xml)
    drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=simple#R{encoded_content}"
    
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    output_png = output_dir / "simple_playwright_test.png"
    
    try:
        print("🚀 测试简单内容...")
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.set_viewport_size({"width": 1200, "height": 800})
            
            page.goto(drawio_url, timeout=30000)
            page.wait_for_timeout(5000)
            
            # 截图
            page.screenshot(path=str(output_png), full_page=True, type="png")
            browser.close()
        
        if output_png.exists() and output_png.stat().st_size > 0:
            print(f"✅ 简单测试成功: {output_png}")
            print(f"📊 文件大小: {output_png.stat().st_size} 字节")
        else:
            print("❌ 简单测试失败")
            
    except Exception as e:
        print(f"❌ 简单测试异常: {e}")


if __name__ == "__main__":
    test_playwright_export()
    test_simple_drawio()
