"""
RAM干涉工作流

V字对应：
3.3.46. RAM干渉チェック実施＆結果検証

该模块提供RAM干涉检查功能，分析代码中的全局变量变更和RAM干涉情况。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量位置检索
3. RAM干涉确认
4. 生成RAM干涉检查报告
"""

from pathlib import Path
from typing import Optional

from sdw_agent.service import BaseWorkflow, WorkflowResult, WorkflowStatus, register_workflow

from sdw_agent.service.ram_interfere_service.models import RAMInterfereConfigModel, \
    RepositoryInfo
from sdw_agent.service.ram_interfere_service.utils import search_global_var, \
    gen_ram_interfere_data, RAMInterfereExcelUtil
from sdw_agent.service.template_manager import template_manager
from sdw_agent.util.extract_code_util import InterruptAndSchedulerExtractor
from sdw_agent.util.file_base_util import gen_output_path


@register_workflow("ram_interfere")
class RAMInterfereWorkflow(BaseWorkflow):
    """
    RAM干涉工作流类
    
    提供RAM干涉检查功能，分析代码中的全局变量变更和RAM干涉情况。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化RAM干涉工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        self.register_config_model()

    @staticmethod
    def register_config_model():
        """
        注册配置模型用于验证

        本函数通过调用WorkflowConfigManager来注册一个名为"ram_interfere"的配置模型
        这是为了确保所有与"ram_interfere"相关的配置都符合预定义的结构和类型
        """
        # 导入WorkflowConfigManager类，用于管理和验证配置模型
        from sdw_agent.service.workflow_config import WorkflowConfigManager

        # 创建WorkflowConfigManager实例，指定workflow名称为"ram_interfere"
        config_manager = WorkflowConfigManager(workflow_name="ram_interfere")

        # 注册"ram_interfere"配置模型，以便后续可以用来验证配置的正确性
        config_manager.register_schema("ram_interfere", RAMInterfereConfigModel)

    def validate_input(self, repo_info: RepositoryInfo) -> bool:
        """
        验证输入参数
        
        Args:
            repo_info: 代码仓库信息

        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证仓库路径
            repo_path = Path(repo_info.repo_path)
            if not repo_path.exists():
                self.logger.error(f"仓库路径不存在: {repo_info.repo_path}")
                return False

            # 验证是否为Git仓库
            git_dir = repo_path / ".git"
            if not git_dir.exists():
                self.logger.error(f"不是有效的Git仓库: {repo_info.repo_path}")
                return False

            # 验证提交ID格式
            if not repo_info.commit_id or len(repo_info.commit_id) < 7:
                self.logger.error(f"无效的提交ID: {repo_info.commit_id}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def execute(self, repo_info: RepositoryInfo) -> WorkflowResult:
        """
        执行工作流核心逻辑
        
        Args:
            repo_info: 代码仓库信息
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            # 1. 搜索全局变量变更
            global_vars_info = self._search_global_variable_changes(repo_info)
            self.logger.info(f"步骤1. 搜索全局变量变更: global_vars_info:{global_vars_info}")
            if global_vars_info:
                # 2. 获取中断表和调度任务信息
                interrupt_table, schdlr_rglr_tasks = self._get_interrupt_and_scheduler_info(repo_info)
                self.logger.info(
                    f"步骤2. 获取中断表和调度任务信息:interrupt_table:{interrupt_table}, schdlr_rglr_tasks:{schdlr_rglr_tasks}")

                # 3. 生成RAM干涉数据
                ram_interfere_data = self._generate_ram_interfere_data(
                    schdlr_rglr_tasks, interrupt_table, repo_info, global_vars_info
                )
                self.logger.info(f"步骤3. 生成RAM干涉数据: ram_interfere_data:{ram_interfere_data}")
            else:
                ram_interfere_data = []
            # 4. 生成并保存输出文件
            output_file = self._generate_and_save_report(ram_interfere_data)

            # 5. 返回成功结果
            return self._create_success_result(
                output_file, ram_interfere_data, global_vars_info
            )

        except Exception as e:
            return self._create_error_result(e)

    def _get_interrupt_and_scheduler_info(self, repo_info: RepositoryInfo):
        """
        获取中断表和调度任务信息

        此函数旨在从给定的仓库信息中提取中断表和调度任务的相关信息它首先记录了函数执行的过程，
        然后根据配置信息获取仓库扩展名列表，最后调用另一个函数来获取具体的中断表和调度任务信息

        参数:
        repo_info (RepositoryInfo): 包含仓库路径等信息的数据结构

        返回:
        返回值是从仓库路径和扩展名列表中提取的中断表和调度任务信息
        """
        # 记录获取中断表和调度任务信息的步骤
        self.logger.info("步骤2: 获取中断表和调度任务信息")

        # 从配置中获取仓库扩展名列表，如果没有配置，则使用默认列表
        repo_extensions = self.config.get("io", {}).get("input", {}).get(
            "repo_extensions", [".c", ".h", ".prm"]
        )
        extractor = InterruptAndSchedulerExtractor(repo_info.repo_path, repo_extensions)
        # 调用函数获取中断表和调度任务信息，并返回结果
        return extractor.extract()

    def _search_global_variable_changes(self, repo_info: RepositoryInfo):
        """
        搜索全局变量变更

        本函数旨在分析指定仓库的提交记录，以识别全局变量的变更情况

        参数:
        - repo_info (RepositoryInfo): 包含仓库路径和比较用的提交ID的信息对象

        返回:
        - 全局变量变更的搜索结果，具体类型取决于search_global_var函数的实现
        """
        # 记录搜索全局变量变更的步骤
        self.logger.info("步骤1: 搜索全局变量变更")

        # 调用search_global_var函数执行全局变量变更的搜索
        # 需要提供仓库路径以及两个提交ID用于比较
        return search_global_var(
            repo_info.repo_path,
            repo_info.commit_id,
            repo_info.compared_commit_id
        )

    def _generate_ram_interfere_data(self, schdlr_rglr_tasks, interrupt_table, repo_info, global_vars_info):
        """
        生成RAM干涉数据

        该方法旨在通过分析调度器任务、中断表、仓库信息和全局变量信息来生成RAM干涉数据
        这对于评估和优化系统性能至关重要

        参数:
        schdlr_rglr_tasks (list): 调度器常规任务列表，用于分析任务间的相互影响
        interrupt_table (dict): 中断表，包含中断相关的详细信息
        repo_info (object): 仓库信息对象，提供了访问仓库路径等信息的方法
        global_vars_info (dict): 全局变量信息，收集了所有全局变量的详细资料

        返回:
        RAM干涉数据，具体格式和内容取决于gen_ram_interfere_data函数的实现
        """
        # 记录生成RAM干涉数据的过程开始
        self.logger.info("步骤3: 生成RAM干涉数据")
        # 调用底层函数gen_ram_interfere_data来实际生成RAM干涉数据
        return gen_ram_interfere_data(
            schdlr_rglr_tasks,
            interrupt_table,
            repo_info.repo_path,
            global_vars_info
        )

    def _generate_and_save_report(self, ram_interfere_data):
        """
        生成并保存输出文件
        该函数根据输入的RAM干涉数据生成一个Excel报告，并按照指定的路径和格式保存
        """
        # 记录生成输出文件的步骤
        self.logger.info("步骤4: 生成输出文件")

        # 获取输出文件的路径
        output_dir = self.config.get("io", {}).get("output", {}).get(
            "default_output_dir"
        )

        # 获取报告的基础名称，如果没有配置，则使用默认值"RAM干涉"
        report_base_name = self.config.get("io", {}).get("output", {}).get(
            "report_base_name", "RAM干涉"
        )

        output_file = gen_output_path(output_dir, report_base_name)
        # 获取模板文件的路径，用于生成报告的格式
        template_file = template_manager.get_template_path("ram_interfere_file")
        # 获取Excel报告的样式配置
        excel_style = self.config.get("processing", {}).get("excel_output", {})

        # 记录保存到Excel文件的步骤
        self.logger.info("步骤5: 保存到Excel文件")

        # 初始化Excel工具类，用于生成和保存报告
        excel_util = RAMInterfereExcelUtil(output_file=output_file, template_file=template_file,
                                           excel_style=excel_style)

        # 使用上下文管理器确保文件被正确处理并关闭
        with excel_util:
            # 保存RAM干涉数据到Excel文件中
            excel_util.save_interfere_data(ram_interfere_data)

        # 返回输出文件的路径，
        return output_file

    @staticmethod
    def _create_success_result(output_file, ram_interfere_data, global_vars_info):
        """
        创建成功结果

        此函数用于生成一个表示RAM干涉检查成功的结果对象它总结了检查过程中的关键信息，
        包括全局变量的数量、RAM干涉记录、中断表大小以及调度器任务，并将这些信息封装
        在一个WorkflowResult对象中

        参数:
        - output_file: 输出文件的路径，用于进一步的处理或记录
        - ram_interfere_data: RAM干涉数据，记录了干涉检查的相关信息
        - global_vars_info: 全局变量信息列表，包含了所有被分析的全局变量的详细信息


        返回:
        返回一个WorkflowResult对象，包含RAM干涉检查的成功状态、提示消息以及详细数据
        """
        # 创建统计信息字典，总结全局变量数量、RAM干涉记录、中断表大小和调度器任务数量
        statistics = {
            "total_global_vars": len(global_vars_info),
            "interfere_records": len(ram_interfere_data)
        }

        # 返回WorkflowResult对象，包含成功状态、提示消息和详细数据
        return WorkflowResult(
            status=WorkflowStatus.SUCCESS,
            message=f"RAM干涉检查完成，共处理{len(global_vars_info)}个全局变量",
            data={
                "output_file": output_file,
                "ram_interfere_data": ram_interfere_data,
                "statistics": statistics,
                "global_vars_info": [var.dict() for var in global_vars_info]
            }
        )

    def _create_error_result(self, error):
        """
        创建错误结果

        当RAM干涉检查执行失败时调用此方法来生成一个错误的结果对象

        参数:
        error (Exception): 捕获到的异常对象，用于记录错误详情

        返回:
        WorkflowResult: 包含错误信息的结果对象，用于指示工作流执行失败
        """
        # 记录错误日志
        self.logger.exception(f"执行RAM干涉检查失败: {str(error)}")
        # 创建并返回WorkflowResult对象，指示工作流因RAM干涉检查失败
        return WorkflowResult(
            status=WorkflowStatus.FAILED,
            message="RAM干涉检查执行失败",
            error=str(error)
        )

    def post_execute(self, result: WorkflowResult) -> None:
        """
        执行后清理工作
        
        Args:
            result: 执行结果
        """
        # 根据执行结果状态进行日志记录
        try:
            if result.status == WorkflowStatus.SUCCESS:
                # 如果执行成功，记录成功信息和输出文件
                self.logger.info(f"RAM干涉检查成功完成，输出文件: {result.data.get('output_file')}")
            else:
                # 如果执行失败，记录失败原因
                self.logger.error(f"RAM干涉检查失败: {result.message}")

        except Exception as e:
            # 异常情况下，记录清理失败的错误信息
            self.logger.error(f"执行后清理失败: {str(e)}")


def do_ram_interfere(repo_info: RepositoryInfo) -> str:
    """
    执行RAM干涉检查（兼容旧接口）
    
    Args:
        repo_info: 代码仓库信息
        
    Returns:
        str: 输出文件路径
        
    Raises:
        Exception: 当执行失败时
    """
    # 创建RAM干涉检查工作流实例
    workflow = RAMInterfereWorkflow()

    # 执行RAM干涉检查工作流
    result = workflow.run(repo_info)

    # 根据工作流执行结果，返回输出文件路径或抛出异常
    if result.status == WorkflowStatus.SUCCESS:
        return result.data["output_file"]
    elif result.status == WorkflowStatus.WARNING:
        return ""
    else:
        raise Exception(result.message)
