"""汽车软件开发AI辅助工作流自动化系统 - 示例使用脚本"""
import os
import sys
import json
import yaml
import logging
from pathlib import Path
from pprint import pprint

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from sdw_agent.core import WorkflowEngine, WorkflowScheduler
from sdw_agent.steps import create_all_steps
from main import AutoWorkflowSystem
from sdw_agent.core.workflow import WorkflowDefinition
from sdw_agent.core.nlp_planner import TaskPlanner
from sdw_agent.core.llm import LLMManager, LLMMessage
from sdw_agent.core.prompt import PromptManager, PromptTemplate
from sdw_agent.steps.dev_steps import RequirementAnalysisLLMAgent, DesignGenerationLLMAgent, CodeReviewLLMAgent


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def example_1_basic_usage():
    """示例1：基本使用"""
    print("\n=== 示例1：基本使用 ===")
    
    # 创建系统实例
    system = AutoWorkflowSystem()
    
    # 准备输入数据
    initial_data = {
        "scl_file": "examples/data/sample_scl.xlsx",
        "requirements_list": "examples/data/sample_requirements.xlsx",
        "change_request": "CHG-2024-001"
    }
    
    # 执行开发阶段工作流
    print("执行开发阶段工作流...")
    result = system.execute_workflow("dev_workflow", initial_data)
    
    # 打印结果摘要
    print(f"执行状态: {result['status']}")
    print(f"总步骤数: {result['summary']['total_steps']}")
    print(f"成功步骤: {result['summary']['successful']}")
    print(f"失败步骤: {result['summary']['failed']}")
    print(f"执行时间: {result['duration']:.2f}秒")


def example_2_nlp_query():
    """示例2：自然语言查询"""
    print("\n=== 示例2：自然语言查询 ===")
    
    # 创建系统实例
    system = AutoWorkflowSystem()
    
    # 示例查询
    queries = [
        "给出变更需求CHG-2024-002的设计标准CS与法规确认结果",
        "生成变更点CHG-2024-003的通信检查测试用例",
        "对commit id abc123的代码进行审查"
    ]
    
    # 处理每个查询
    for i, query in enumerate(queries):
        print(f"\n查询 {i+1}: {query}")
        
        # 处理查询
        result = system.process_query(query)
        
        # 打印结果
        print(f"执行状态: {result['status']}")
        print(f"执行时间: {result['duration']:.2f}秒")
        print(f"执行步骤: {', '.join(result['steps'].keys())}")


def example_3_conditional_execution():
    """示例3：条件执行"""
    print("\n=== 示例3：条件执行 ===")
    
    # 创建系统实例
    system = AutoWorkflowSystem()
    
    # 准备输入数据
    initial_data = {
        "initial_data": {
            "scl_file": "examples/data/sample_scl.xlsx",
            "requirements_list": "examples/data/sample_requirements.xlsx",
            "change_request": "CHG-2024-004",
            "communication_change": True  # 通信相关变更标志
        }
    }
    
    # 定义条件
    conditions = {
        "dev_2": lambda data: True,  # 总是执行法规确认
        "dev_6": lambda data: data.get("initial_data", {}).get("communication_change", False),  # 有通信变更时才执行
        "dev_9": lambda data: data.get("initial_data", {}).get("communication_change", False)   # 有通信变更时才执行
    }
    
    # 条件执行
    print("条件执行工作流...")
    result = system.scheduler.schedule_conditional("dev_workflow", conditions, initial_data)
    
    # 打印结果
    print(f"执行状态: {result.status.value}")
    print("执行的步骤:")
    for step_id, step_result in result.step_results.items():
        print(f"  - {step_id}: {step_result.status.value}")


def example_4_custom_workflow():
    """示例4：自定义工作流"""
    print("\n=== 示例4：自定义工作流 ===")
    
    # 创建引擎实例
    engine = WorkflowEngine()
    
    # 注册所有步骤
    steps = create_all_steps()
    for step in steps:
        engine.register_step(step)
    
    # 创建自定义工作流
    custom_workflow = WorkflowDefinition(
        workflow_id="communication_review",
        name="通信与代码审查工作流",
        description="专注于通信检查和代码审查的自定义工作流",
        steps=[
            "dev_6",   # 通信故障安全CS
            "dev_9",   # CAN入出力一览确认
            "dev_15",  # 编码标准确认
            "dev_18",  # 代码审查
            "test_5"   # 通信检查
        ]
    )
    
    # 注册工作流
    engine.register_workflow(custom_workflow)
    
    # 创建执行计划
    plan = engine.create_execution_plan("communication_review")
    
    # 打印执行计划
    print("自定义工作流执行计划:")
    for i, layer in enumerate(plan.parallel_groups):
        print(f"  层 {i+1}: {', '.join(layer)}")
    
    # 执行工作流
    print("\n执行自定义工作流...")
    result = engine.execute_workflow(plan)
    
    # 打印结果
    print(f"执行状态: {result.status.value}")
    print(f"执行步骤数: {len(result.step_results)}")
    print(f"执行时间: {result.duration:.2f}秒")


def example_5_save_load_results():
    """示例5：保存和加载结果"""
    print("\n=== 示例5：保存和加载结果 ===")
    
    # 创建系统实例
    system = AutoWorkflowSystem()
    
    # 执行查询
    query = "给出变更需求CHG-2024-005的设计标准CS"
    print(f"执行查询: {query}")
    result = system.process_query(query)
    
    # 保存结果到JSON文件
    output_file = "examples/output/query_result.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 将结果转换为可序列化的格式
    serializable_result = {
        "query": query,
        "status": result["status"],
        "duration": result["duration"],
        "steps": {}
    }
    
    for step_id, step_data in result["steps"].items():
        serializable_result["steps"][step_id] = {
            "name": step_data["name"],
            "status": step_data["status"],
            "duration": step_data["duration"],
            "output": step_data["output"]
        }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(serializable_result, f, ensure_ascii=False, indent=2)
    
    print(f"结果已保存到: {output_file}")
    
    # 加载结果
    print("\n加载保存的结果:")
    with open(output_file, 'r', encoding='utf-8') as f:
        loaded_result = json.load(f)
    
    print(f"查询: {loaded_result['query']}")
    print(f"状态: {loaded_result['status']}")
    print(f"执行步骤: {', '.join(loaded_result['steps'].keys())}")


def example_workflow_execution():
    """示例：直接执行工作流"""
    # 加载配置
    config = load_config("config/system.yaml")
    workflows = load_config("config/workflows.yaml")
    
    # 创建工作流引擎
    engine = WorkflowEngine(config, workflows)
    
    # 执行工作流
    input_data = {
        "initial_data": {
            "scl_file": "/path/to/scl.txt",
            "requirements_list": ["REQ-001", "REQ-002"],
            "code_files": ["/path/to/code1.c", "/path/to/code2.c"]
        }
    }
    
    result = engine.execute_workflow("dev_workflow", input_data)
    
    # 打印结果
    print(f"工作流执行结果: {result.status}")
    for step_id, step_result in result.step_results.items():
        print(f"  步骤 {step_id}: {step_result.status}")


def example_nlp_query():
    """示例：使用自然语言查询"""
    # 加载配置
    config = load_config("config/system.yaml")
    workflows = load_config("config/workflows.yaml")
    
    # 创建任务规划器
    planner = TaskPlanner(config, workflows)
    
    # 执行自然语言查询
    query = "我需要分析一个新的刹车控制需求并生成设计文档"
    plan = planner.plan_from_query(query)
    
    # 打印计划
    print(f"查询: {query}")
    print(f"生成的计划: {plan.workflow_id}")
    print(f"步骤: {', '.join(plan.steps)}")
    
    # 执行计划
    engine = WorkflowEngine(config, workflows)
    result = engine.execute_plan(plan)
    
    # 打印结果
    print(f"计划执行结果: {result.status}")


def example_llm_usage():
    """示例：使用LLM"""
    # 加载配置
    config = load_config("config/system.yaml")
    
    # 创建LLM管理器
    llm_manager = LLMManager.from_config(config["llm"])
    
    # 创建消息
    messages = [
        LLMMessage(role="system", content="你是一个汽车软件开发助手。"),
        LLMMessage(role="user", content="请解释一下AUTOSAR架构的主要组成部分。")
    ]
    
    # 调用LLM
    response = llm_manager.call(messages)
    
    # 打印结果
    print("LLM响应:")
    print(response.content)


def example_prompt_usage():
    """示例：使用提示词模板"""
    # 创建提示词管理器
    prompt_manager = PromptManager()
    
    # 注册模板
    prompt_manager.register_template(
        "requirement_analysis",
        """
        分析以下汽车软件需求:
        
        {{requirement_text}}
        
        请提取关键功能点和约束条件。
        """
    )
    
    # 渲染模板
    prompt = prompt_manager.render_template(
        "requirement_analysis",
        requirement_text="实现一个基于摄像头的自动泊车系统，需要支持平行泊车和垂直泊车两种模式。"
    )
    
    # 打印结果
    print("渲染后的提示词:")
    print(prompt)


def example_agent_step():
    """示例：使用Agent步骤"""
    # 加载配置
    config = load_config("config/system.yaml")
    
    # 创建LLM管理器和提示词管理器
    llm_manager = LLMManager.from_config(config["llm"])
    prompt_manager = PromptManager()
    
    # 从配置中加载默认模板
    for template_id, template_content in config["prompt"]["default_templates"].items():
        prompt_manager.register_template(template_id, template_content)
    
    # 创建需求分析步骤
    requirement_step = RequirementAnalysisLLMAgent(
        llm_manager=llm_manager,
        prompt_manager=prompt_manager
    )
    
    # 执行需求分析
    from sdw_agent.core.base import StepInput
    input_data = StepInput(
        data={
            "requirement_text": """
            开发一个汽车自适应巡航控制(ACC)系统，具有以下功能：
            1. 能够自动调整车速以保持与前车的安全距离
            2. 支持30-150km/h的速度范围
            3. 提供三级距离设置（近、中、远）
            4. 在紧急情况下能够自动刹车减速
            5. 与车辆CAN总线集成
            6. 符合ISO 26262 ASIL-C安全等级要求
            """
        }
    )
    
    result = requirement_step.execute(input_data)
    
    # 打印结果
    print("需求分析结果:")
    print(result.status)
    if result.output:
        for key, value in result.output.data.items():
            print(f"  {key}: {value}")
    
    # 如果需求分析成功，继续进行设计生成
    if result.status.value == "success":
        design_step = DesignGenerationLLMAgent(
            llm_manager=llm_manager,
            prompt_manager=prompt_manager
        )
        
        design_input = StepInput(data=result.output.data)
        design_result = design_step.execute(design_input)
        
        # 打印设计结果
        print("\n设计生成结果:")
        print(design_result.status)
        if design_result.output:
            for key, value in design_result.output.data.items():
                print(f"  {key}: {value[:100]}..." if isinstance(value, str) and len(value) > 100 else f"  {key}: {value}")


if __name__ == "__main__":
    setup_logging()
    
    # 创建必要的目录
    os.makedirs("examples/data", exist_ok=True)
    os.makedirs("examples/output", exist_ok=True)
    
    # 选择要运行的示例
    example = "agent"  # workflow, nlp, llm, prompt, agent
    
    if example == "workflow":
        example_workflow_execution()
    elif example == "nlp":
        example_nlp_query()
    elif example == "llm":
        example_llm_usage()
    elif example == "prompt":
        example_prompt_usage()
    elif example == "agent":
        example_agent_step()
    else:
        print(f"未知示例: {example}") 