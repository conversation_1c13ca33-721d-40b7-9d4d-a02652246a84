"""
要件导入文档工具类

提供要件一览表处理、Jira信息获取、Check Sheet分析等功能。
从 import_doc_util.py 重构而来，专注于工具类功能。
"""

import json
from typing import List, Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from pydantic import BaseModel, Field

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.util import update_excel_cells_util
from sdw_agent.util.excel.multi_level_header_extractor import MultiLevelHeaderExtractor
from sdw_agent.util.excel_util import read_excel
from sdw_agent.util.jira_util import check_jira_health, get_epic_info, get_project_info
from sdw_agent.util.markdown_util import dict_list_to_markdown_table


class CheckSheetFormat(BaseModel):
    """
    是否实施响应结果
    """
    if_impl: bool = Field(description="是否需要实施")
    not_impl_reason: str = Field(description="实施否的理由")


class CheckFormat(BaseModel):
    """
    是否实施响应结果,批量
    """
    item_id: int = Field(description="检查项的序号")
    if_impl: bool = Field(description="是否需要实施")
    not_impl_reason: str = Field(description="实施否的理由， 当需要实施的时候，返回空")


class BatchResponse(BaseModel):
    """批量响应结果"""
    data: List[CheckFormat]


class ImportDocUtil:
    """
    要件导入文档工具类
    
    提供要件一览表处理、Jira信息获取、Check Sheet分析等功能。
    """

    def __init__(self):
        """初始化工具类"""
        self.logger = logger.bind(name="ImportDocUtil")

    def get_task_content(self, key_source) -> List[Dict[str, Any]]:
        """
        获取任务内容（标准表头）
        
        Args:
            key_source: 包含文件路径的源对象
            
        Returns:
            List[Dict[str, Any]]: 任务内容列表
        """
        file_path = key_source.uri
        
        # 读取Excel文件
        hyperlinks, df = read_excel(
            file_path.replace('\\', '/'),
            header=4,
            sheet_name="要件一览"
        )
        
        # 移除列名中的空格
        df.columns = df.columns.str.replace(' ', '')

        # 构建任务内容
        content = []
        for idx, row in df.iterrows():
            if str(row.get('ARチケットNO')).upper() != 'NAN':
                ar_link = ""
                if (hyperlinks[idx].get('ARチケットNOリンク') and 
                    hyperlinks[idx].get('ARチケットNOリンク') != "" and 
                    str(row.get('ARチケットNO')).upper() != 'NAN'):
                    ar_link = hyperlinks[idx].get('ARチケットNOリンク') + str(row.get('ARチケットNO'))

                # 准备数据
                row_data = {
                    "row_idx": idx,
                    "ar_no": str(row.get('ARチケットNO')),
                    "ar_title": str(row.get('ARチケットタイトル')),
                    "ar_link": ar_link,
                    "epic_name": str(row.get('エピック名')),
                    "req_change_content": str(row.get('概要')),
                    "p_no": str(row.get('要件チケットNO') or row.get('要件No.')),
                    "step_name": str(row.get("対応イベント")),
                    "req_file_name": str(row.get("ARチケットタイトル")),
                    "if_check": str(row.get("对应要否"))
                }
                content.append(row_data)
                
        self.logger.info(f"提取到 {len(content)} 个任务项")
        return content

    def get_task_content_multi_level_header(self, key_source) -> List[Dict[str, Any]]:
        """
        获取任务内容（多级表头）
        
        Args:
            key_source: 包含文件路径的源对象
            
        Returns:
            List[Dict[str, Any]]: 任务内容列表
        """
        file_path = key_source.uri
        sheet_name = "要件一览"
        extractor = MultiLevelHeaderExtractor(file_path, sheet_name)

        # 提取完整数据
        self.logger.info("提取多级表头数据")
        df, _ = extractor.extract_data_with_headers(
            header_start_row=3,
            header_end_row=5,
            data_start_row=6,
            start_col=1,
            end_col=100,
            exclude_hidden_rows=False
        )
        
        content = []
        for idx, row in df.iterrows():
            if str(row.get('ARチケットNO')).upper() != 'NAN':
                # 准备数据
                row_data = {
                    "row_idx": idx,
                    "ar_no": str(row.get('ARチケットNO')),
                    "ar_title": str(row.get('ARチケットタイトル')),
                    "ar_link": "",
                    "epic_name": str(row.get('エピック名')),
                    "req_change_content": str(row.get('概要')),
                    "p_no": str(row.get('要件チケットNO') or row.get('要件No.')),
                    "step_name": str(row.get("対応イベント")),
                    "req_file_name": str(row.get("ARチケットタイトル")),
                    "if_check": str(row.get("HMI组 | 对应要否"))
                }
                content.append(row_data)
                
        self.logger.info(f"提取到 {len(content)} 个任务项（多级表头）")
        return content

    def make_jira_change_summary(self, jira_no: str, change_summary: str, p_key: str) -> str:
        """
        获取需求变更信息
        
        Args:
            jira_no: 需求变更【Jira】编号
            change_summary: 需求变更概要
            p_key: P票编号
            
        Returns:
            str: 需求变更信息
        """
        requirement_info = ""

        # 检查 Jira 服务是否可用
        jira_available = check_jira_health() and ENV.config.jira.enable
        self.logger.info(f"Jira可用性: {jira_available}")

        if jira_available and jira_no:
            try:
                # 从Jira获取变更描述信息
                result = get_epic_info(jira_no, change_summary)
                self.logger.info(f"找到关联的epic票的变更描述信息：\n{json.dumps(result, indent=4, ensure_ascii=False)}")
                requirement_info += f'jira_ar票信息\ntitle:{result["title"]}\ndesc:{result["desc"]}'
            except Exception as e:
                self.logger.error(f"获取Epic信息失败: {str(e)}")
                jira_available = False

        if jira_available and p_key and p_key.startswith("P"):
            try:
                # 从Jira获取P票变更描述信息
                p_change_info = get_project_info(p_key)
                self.logger.info(f"找到关联的p票{p_key}的变更描述信息：\n{json.dumps(p_change_info, indent=4, ensure_ascii=False)}")
                requirement_info += f'jira_p票信息\nchange_type:{p_change_info["change_type"]}\nchange_content:{p_change_info["change_content"]}'
            except Exception as e:
                self.logger.error(f"获取P票信息失败: {str(e)}")

        # 如果 Jira 不可用或获取信息失败，直接使用 change_summary
        if not requirement_info and change_summary:
            self.logger.info(f"使用change_summary作为变更描述信息: {change_summary}")
            requirement_info = f"{change_summary}"
            
        return requirement_info

    def split_check_sheet_list(self, check_list: List[Dict[str, Any]], batch_size: int) -> List[List[Dict[str, Any]]]:
        """
        将check_sheet_list按batch_size进行分组
        最后一组如果少于一半就合并到前一组，否则单独为一组
        
        Args:
            check_list: 检查项列表
            batch_size: 批次大小
            
        Returns:
            List[List[Dict[str, Any]]]: 分组后的批次列表
        """
        if not check_list:
            return []

        batches = []
        for i in range(0, len(check_list), batch_size):
            batch = check_list[i:i + batch_size]
            batches.append(batch)

        # 处理最后一组：如果少于一半就合并到前一组
        if len(batches) > 1:
            last_batch = batches[-1]
            if len(last_batch) < batch_size / 2:
                # 合并到前一组
                batches[-2].extend(last_batch)
                batches.pop()  # 移除最后一组

        self.logger.info(f"将 {len(check_list)} 个项目分为 {len(batches)} 批处理")
        return batches

    def process_check_sheet(self,
                           ar_no: str,
                           ar_summary: str,
                           p_no: str,
                           check_sheet_list: List[Dict[str, Any]],
                           check_sheet_file_path: str,
                           target_sheet_name: str) -> Dict[int, Dict[str, str]]:
        """
        处理Check Sheet，分析每个检查项是否需要实施

        Args:
            ar_no: AR编号
            ar_summary: AR摘要
            p_no: P编号
            check_sheet_list: 检查项列表
            check_sheet_file_path: check sheet文件路径
            target_sheet_name: 目标工作表名称

        Returns:
            Dict[int, Dict[str, str]]: 需要重写的单元格数据，格式为 {行号: {'o': '要/否', 's': '理由'}}
        """
        # 1. 根据要件一览表的jira信息构建变更概要
        requirement_info = self.make_jira_change_summary(ar_no, ar_summary, p_no)
        self.logger.info(f"获取到变更概要信息，长度: {len(requirement_info)}")

        # 获取配置
        judge_batch: bool = ENV.config.design_check_sheet.judge_batch
        judge_length = ENV.config.design_check_sheet.judge_length

        # 存储需要重写的单元格数据
        need_rewrite = {}

        if judge_batch:
            # 批量处理
            batches = self.split_check_sheet_list(check_sheet_list, judge_length)
            self.logger.info(f"总共 {len(check_sheet_list)} 个项目，分为 {len(batches)} 批处理，每批最多 {judge_length} 个项目")

            for batch_index, batch in enumerate(batches):
                self.logger.info(f"处理第 {batch_index + 1}/{len(batches)} 批，共 {len(batch)} 个项目")

                # 为当前批次生成markdown表格
                markdown_table = dict_list_to_markdown_table(batch,
                                                           ["major_category", "middle_category", "minor_category",
                                                            "purpose", "regulation"])

                template = ChatPromptTemplate(
                    [
                        ("user", ENV.prompt.design_check_sheet_prompts.check_sheet_analyze_multi),
                    ],
                    template_format="mustache"
                )
                invoke_data = {
                    "requirement_info": requirement_info,
                    "markdown_table": markdown_table
                }

                # 对于批量处理，使用BatchResponse格式
                resp: BatchResponse = get_ai_message_with_structured_output(
                    template,
                    invoke_data,
                    BatchResponse,
                    llm_model=None
                )

                # 处理批量响应结果
                # 将批量响应结果映射到对应的行号
                for i, item in enumerate(batch):
                    if i < len(resp.data):
                        check_result: CheckFormat = resp.data[i]
                        if check_result.if_impl:
                            need_rewrite[item['row_number']] = {'o': "要", 's': "-"}
                        else:
                            need_rewrite[item['row_number']] = {'o': "否", 's': check_result.not_impl_reason}
                    else:
                        # 如果响应数据不足，使用默认值
                        need_rewrite[item['row_number']] = {"o": "否", "s": "批量处理响应数据不足"}
        else:
            # 单个处理
            self.logger.info(f"使用单个处理模式，共 {len(check_sheet_list)} 个项目")
            for item in check_sheet_list:
                # 要件的每一个check项
                markdown_table = dict_list_to_markdown_table([item],
                                                           ["major_category", "middle_category", "minor_category",
                                                            "purpose", "regulation"])

                template = ChatPromptTemplate(
                    [
                        ("user", ENV.prompt.design_check_sheet_prompts.check_sheet_analyze),
                    ],
                    template_format="mustache"
                )
                invoke_data = {
                    "requirement_info": requirement_info,
                    "markdown_table": markdown_table
                }

                resp: CheckSheetFormat = get_ai_message_with_structured_output(
                    template,
                    invoke_data,
                    CheckSheetFormat,
                    llm_model=None
                )

                if resp.if_impl:
                    need_rewrite[item['row_number']] = {'o': "要", 's': "-"}
                else:
                    need_rewrite[item['row_number']] = {'o': "否", 's': resp.not_impl_reason}

        self.logger.info(f"分析完成，需要更新 {len(need_rewrite)} 个单元格")
        return need_rewrite

    def update_check_sheet(self,
                          need_rewrite: Dict[int, Dict[str, str]],
                          check_sheet_file_path: str,
                          target_sheet_name: str) -> bool:
        """
        更新Check Sheet文件

        Args:
            need_rewrite: 需要重写的单元格数据
            check_sheet_file_path: check sheet文件路径
            target_sheet_name: 目标工作表名称

        Returns:
            bool: 更新是否成功
        """
        try:
            update_excel_cells_util.update_excel_cells(check_sheet_file_path, target_sheet_name, need_rewrite)
            self.logger.info(f"成功更新Excel文件: {check_sheet_file_path}, 工作表: {target_sheet_name}")
            return True
        except Exception as e:
            self.logger.error(f"更新Excel文件失败: {str(e)}")
            return False


def get_task_content(key_source):
    file_path = key_source.uri
    # 这里实现获取任务列表的逻辑
    hyperlinks, df = read_excel(
        file_path.replace('\\', '/'),
        header=4,
        sheet_name="要件一览"
    )
    # 移除列名中的空格（包括前后空格和中间空格）
    df.columns = df.columns.str.replace(' ', '')

    # 根据指定TaskInfo structure 构建response
    content = []

    for idx, row in df.iterrows():
        if str(row.get('ARチケットNO')).upper() != 'NAN':
            ar_link = ""
            if hyperlinks[idx].get('ARチケットNOリンク') and hyperlinks[idx].get('ARチケットNOリンク') != "" and str(
                    row.get('ARチケットNO')).upper() != 'NAN':
                ar_link = hyperlinks[idx].get('ARチケットNOリンク') + str(row.get('ARチケットNO'))

            # 准备数据
            row_data = {
                "row_idx": idx,
                "ar_no": str(row.get('ARチケットNO')),
                "ar_title": str(row.get('ARチケットタイトル')),
                "ar_link": ar_link,
                "epic_name": str(row.get('エピック名')),
                "req_change_content": str(row.get('概要')),
                "p_no": str(row.get('要件チケットNO') or row.get('要件No.')),
                "step_name": str(row.get("対応イベント")),  # 项目阶段名
                "req_file_name": str(row.get("ARチケットタイトル")),  # 要件式样名
                "if_check": str(row.get("对应要否"))
            }
            content.append(row_data)
    return content