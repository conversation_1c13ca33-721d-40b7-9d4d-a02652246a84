"""NLP规划器单元测试"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sdw_agent.core import (
    NLPQueryParser,
    TaskPlanner,
    WorkflowEngine,
    WorkflowDefinition,
    QueryIntent
)


class TestNLPQueryParser(unittest.TestCase):
    """NLP查询解析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = NLPQueryParser()
        
    def test_extract_entities(self):
        """测试实体提取"""
        # 测试变更需求提取
        query1 = "给出变更需求ABC-123的设计标准CS"
        entities1 = self.parser._extract_entities(query1)
        self.assertEqual(entities1.get("change_request"), "ABC-123")
        
        # 测试文件路径提取
        query2 = "检查文件 src/main.cpp 的代码规范"
        entities2 = self.parser._extract_entities(query2)
        self.assertIn("src/main.cpp", entities2.get("file_paths", []))
        
        # 测试Commit ID提取
        query3 = "分析commit id: a1b2c3d4e5f6的代码变更"
        entities3 = self.parser._extract_entities(query3)
        self.assertEqual(entities3.get("commit_id"), "a1b2c3d4e5f6")
        
    def test_identify_intent_and_steps(self):
        """测试意图识别和步骤确定"""
        # 测试设计标准意图
        query1 = "生成设计标准CS"
        intent_type1, steps1, confidence1 = self.parser._identify_intent_and_steps(query1)
        self.assertEqual(intent_type1, "design_standard")
        self.assertIn("dev_3", steps1)
        
        # 测试测试用例意图
        query2 = "创建通信检查测试用例"
        intent_type2, steps2, confidence2 = self.parser._identify_intent_and_steps(query2)
        self.assertEqual(intent_type2, "test_case")
        self.assertIn("test_5", steps2)
        
        # 测试代码审查意图
        query3 = "进行代码审查"
        intent_type3, steps3, confidence3 = self.parser._identify_intent_and_steps(query3)
        self.assertEqual(intent_type3, "code_review")
        
        # 测试完整流程意图
        query4 = "执行完整的开发流程"
        intent_type4, steps4, confidence4 = self.parser._identify_intent_and_steps(query4)
        self.assertEqual(intent_type4, "full_process")
        self.assertIn("all", steps4)
        
    def test_parse_query(self):
        """测试完整查询解析"""
        query = "给出变更需求XYZ-789的设计标准CS与法规确认结果"
        intent = self.parser.parse_query(query)
        
        # 验证解析结果
        self.assertEqual(intent.intent_type, "design_standard")
        self.assertEqual(intent.entities.get("change_request"), "XYZ-789")
        self.assertTrue(any(step in intent.required_steps for step in ["dev_3", "dev_5", "dev_12"]))
        self.assertGreater(intent.confidence, 0.5)


class TestTaskPlanner(unittest.TestCase):
    """任务规划器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建工作流引擎
        self.engine = WorkflowEngine()
        
        # 创建模拟步骤
        for step_id in ["dev_1", "dev_2", "dev_3", "dev_4", "dev_5", "test_1", "test_2"]:
            mock_step = MagicMock()
            mock_step.step_id = step_id
            mock_step.dependencies = []
            if step_id == "dev_3":
                mock_step.dependencies = ["dev_1", "dev_2"]
            elif step_id == "dev_5":
                mock_step.dependencies = ["dev_3"]
            self.engine.register_step(mock_step)
            
        # 创建测试工作流
        dev_workflow = WorkflowDefinition(
            workflow_id="dev_workflow",
            name="开发工作流",
            description="开发阶段工作流",
            steps=["dev_1", "dev_2", "dev_3", "dev_4", "dev_5"]
        )
        
        test_workflow = WorkflowDefinition(
            workflow_id="test_workflow",
            name="测试工作流",
            description="测试阶段工作流",
            steps=["test_1", "test_2"]
        )
        
        # 注册工作流
        self.engine.register_workflow(dev_workflow)
        self.engine.register_workflow(test_workflow)
        
        # 创建任务规划器
        self.planner = TaskPlanner(self.engine)
        
    @patch.object(NLPQueryParser, 'parse_query')
    def test_plan_from_query(self, mock_parse_query):
        """测试从查询生成计划"""
        # 模拟NLP解析结果
        mock_intent = QueryIntent(
            intent_type="design_standard",
            entities={"change_request": "TEST-123"},
            required_steps=["dev_3", "dev_5"],
            confidence=0.8
        )
        mock_parse_query.return_value = mock_intent
        
        # 测试计划生成
        plan = self.planner.plan_from_query("生成设计标准CS")
        
        # 验证计划
        self.assertEqual(plan.workflow_id, "dev_workflow")
        self.assertEqual(plan.intent, mock_intent)
        # 验证步骤扩展（应该包含依赖步骤）
        self.assertIn("dev_1", plan.selected_steps)
        self.assertIn("dev_2", plan.selected_steps)
        self.assertIn("dev_3", plan.selected_steps)
        self.assertIn("dev_5", plan.selected_steps)
        
    def test_expand_steps_with_dependencies(self):
        """测试步骤依赖扩展"""
        # 测试单个步骤的依赖扩展
        expanded1 = self.planner._expand_steps_with_dependencies(["dev_5"])
        self.assertIn("dev_1", expanded1)
        self.assertIn("dev_2", expanded1)
        self.assertIn("dev_3", expanded1)
        self.assertIn("dev_5", expanded1)
        
        # 测试"all"关键字
        expanded2 = self.planner._expand_steps_with_dependencies(["all"])
        self.assertEqual(len(expanded2), len(self.engine.steps_registry))
        
    def test_determine_workflow(self):
        """测试工作流确定"""
        # 测试设计标准意图
        intent1 = QueryIntent(
            intent_type="design_standard",
            entities={},
            required_steps=[],
            confidence=0.8
        )
        self.assertEqual(self.planner._determine_workflow(intent1), "dev_workflow")
        
        # 测试测试用例意图
        intent2 = QueryIntent(
            intent_type="test_case",
            entities={},
            required_steps=[],
            confidence=0.8
        )
        self.assertEqual(self.planner._determine_workflow(intent2), "test_workflow")
        
        # 测试未知意图
        intent3 = QueryIntent(
            intent_type="unknown",
            entities={},
            required_steps=[],
            confidence=0.5
        )
        self.assertEqual(self.planner._determine_workflow(intent3), "custom_workflow")


if __name__ == "__main__":
    unittest.main() 