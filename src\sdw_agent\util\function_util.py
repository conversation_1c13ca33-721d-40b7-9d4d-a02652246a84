"""
function_util.py
函数处理工具类

提供与函数体提取、分析相关的实用方法
"""
import os
import re
from multiprocessing import Pool, cpu_count


def extract_function_body(file_path, target_function):
    """
    从源文件中提取目标函数的完整函数体

    参数:
       file_path (str): 源文件的路径
       target_function (str): 需要提取的函数名

    返回:
       str: 包含函数体的字符串，如果未找到函数或出现错误则返回错误信息
    """
    if not os.path.exists(file_path):
        print(f"{file_path}")
        return f"错误：源文件 {file_path} 不存在"

    # # 读取文件内容
    # with open(file_path, 'r', encoding='utf-8') as f:
    #     lines = f.readlines()

    encodings = ['utf-8', 'latin-1', 'gbk', 'gb2312', 'big5']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
        except UnicodeDecodeError:
            continue

    # 构建函数声明的正则表达式（允许空格和换行差异）
    # 处理函数声明可能跨多行的情况
    func_decl_pattern = re.compile(
        re.escape(target_function).replace(r'\ ', r'\s+') + r'\s*\{*',
        re.VERBOSE | re.DOTALL
    )

    # 查找函数开始位置
    start_line = -1
    content = ''.join(lines)
    match = func_decl_pattern.search(content)
    if not match:
        return f"错误：未找到函数 {target_function}"

    # 定位函数开始的行号
    start_pos = match.start()
    for i, line in enumerate(lines):
        # if start_pos >= len(''.join(lines[:i])) and start_pos < len(''.join(lines[:i + 1])) and ("{" in line or "{" in lines[i+1]):
        if target_function in line and ("{" in line or "{" in lines[i + 1]):
            start_line = i
            break

    # 跟踪括号平衡，提取函数体
    if start_line == -1:
        return "错误：无法定位函数起始位置"

    brace_balance = 0
    function_body = []
    in_function = False

    for i in range(start_line, len(lines)):
        line = lines[i]
        # 计算当前行的括号变化
        brace_balance += line.count('{') - line.count('}')

        if not in_function:
            # 找到函数开始的行
            if '{' in line:
                in_function = True
                function_body.append(line)
        else:
            function_body.append(line)
            # 当括号平衡时，函数体结束
            if brace_balance == 0:
                break

    return f"{target_function}\n" + ''.join(function_body)


def find_file_path(directory, filename):
    """
    在指定路径中查找文件的完整路径。

    参数:
        directory (str): 要搜索的目录路径。
        filename (str): 要查找的文件名。

    返回:
        str: 文件的完整路径，如果未找到则返回 None。
    """
    for root, dirs, files in os.walk(directory):
        if filename in files:
            return os.path.join(root, filename)
    return None


def find_macro_definition(folder_path, macro_name):
    """
    在文件夹中查找指定宏定义的原名。

    参数:
        folder_path (str): 代码文件夹路径。
        macro_name (str): 要查找的宏名称。

    返回:
        list: 包含找到的原函数名的列表。
    """
    # 存储找到的原函数名
    results = []

    # 正则表达式匹配宏定义
    # pattern = re.compile(rf"#define\s+{macro_name}\s*\(.*?\)\s*\((.*?)\)")
    # pattern = re.compile(rf"#define\s+{macro_name}\s*\(.*?\)\s*\(\(.*?\)\s*([a-zA-Z_][a-zA-Z0-9_]*)")
    # pattern = re.compile(rf"#define\s+{macro_name}\s*\(.*?\)\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*)")
    pattern = re.compile(
        rf"#define\s+{macro_name}\s*\(.*?\)\s*(.*)"
    )


    # 遍历文件夹中的所有文件
    for root, _, files in os.walk(folder_path):
        if len(results) > 0:
            break
        for file in files:
            # 只处理 .c 或 .h 文件
            if file.endswith(('.h')):
                file_path = os.path.join(root, file)
                try:
                    # 打开文件并读取内容
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        # 查找匹配的宏定义
                        match = pattern.search(content)
                        if match:
                            # 提取原函数名

                            # 提取宏定义内容部分
                            macro_content = match.group(1)

                            # 正则表达式提取函数名
                            function_pattern = re.compile(r"[a-zA-Z_][a-zA-Z0-9_]*\s*\(")
                            function_matches = function_pattern.findall(macro_content)

                            # 清理函数名（去掉括号）
                            function_names = [func[:-1].strip() for func in function_matches]

                            print(f"宏定义内容: {macro_content}")
                            print(f"匹配的函数名: {function_names}")

                            # original_name = match.group(1)
                            results.append(function_names)
                            break
                except Exception as e:
                    print(f"无法读取文件 {file_path}: {e}")

    return results[0] if results else ""


def extract_called_functions(function_body, variables):
    """
    从函数体中提取调用的外部函数名称。
    参数：
        - function_body: 给定的函数体（字符串）。
        - variables: 全局和局部变量列表，用于排除干扰。
    返回：
        - 外部函数名的集合。
    """
    # 匹配函数调用的正则表达式，例如 func_name(...)
    pattern = r'\b([a-zA-Z_]\w*)\s*\('
    matches = re.findall(pattern, function_body)

    # 排除变量和 C 语言关键字
    c_keywords = {"if", "while", "for", "switch", "return", "sizeof"}
    called_functions = {func for func in matches if func not in variables and func not in c_keywords}
    return called_functions


def find_function_definition(func_name, folder_path):
    """
    在指定文件夹中查找函数定义及其文件路径。
    参数：
        - func_name: 要查找的函数名。
        - folder_path: 文件夹路径。
    返回：
        - (file_path, function_body) 或 (None, None) 如果未找到。
    """
    func_pattern = re.compile(rf'\b{func_name}\b\s*\([^)]*\)\s*{{')  # 匹配函数定义
    # func_pattern = re.compile(rf'\b([a-zA-Z_][a-zA-Z0-9_*\s]+)\s+{func_name}\s*\([^)]*\)\s*{{(.*?)}}', re.DOTALL)

    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith(('.c')):  # 只搜索 C 和头文件
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        match = func_pattern.search(content)
                        if match:
                            return file_path, find_function_body_in_file_content(func_name, content)
                except UnicodeDecodeError:
                    # 如果文件编码不正确，跳过该文件
                    continue
    return None, None


def find_function_body_in_file_content(func_name, file_content):
    """
    从文件内容中提取指定函数的函数体。
    参数：
        - func_name: 函数名。
        - file_content: 文件内容。
    返回：
        - 函数体字符串或 None。
    """
    # 匹配函数定义及其函数体
    func_pattern = re.compile(
        rf'\b([a-zA-Z_][a-zA-Z0-9_*\s]+)\s+{func_name}\s*\([^)]*\)\s*{{(.*?)}}',
        re.DOTALL  # 跨行匹配
    )
    match = func_pattern.search(file_content)
    if match:
        print(match.group(0))
        return match.group(0)  # 返回完整的函数体
    return None


def process_function(func_name_folder_path):
    """
    多进程任务：查找函数定义并提取函数体。
    参数：
        - func_name_folder_path: (函数名, 文件夹路径) 元组。
    返回：
        - (func_name, {"file_path": file_path, "function_body": function_body}) 或 (func_name, None)。
    """
    func_name, folder_path = func_name_folder_path
    file_path, function_body = find_function_definition(func_name, folder_path)
    if file_path and function_body:
        return func_name, {
            "file": file_path,
            "function_body": function_body
        }
    return func_name, None



