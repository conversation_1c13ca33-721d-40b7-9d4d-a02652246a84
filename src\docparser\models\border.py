# -*- coding: utf-8 -*-
from .base_object import BaseObject


class BorderObject(BaseObject):
    """边框对象"""

    def __init__(self):
        self._border_color = ''  # 边框颜色
        self._border_style = ''  # 边框样式

    def to_dict(self):
        """
        将 LayoutObject 对象转换为字典
        """
        return {
            "border_color": self._border_color,
            "border_style": self._border_style,
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 LayoutObject 实例
        """
        obj = cls()
        obj._border_color = data.get("border_color", "")
        obj._border_style = data.get("border_style", "")
        return obj

    @property
    def border_color(self):
        return self._border_color

    @border_color.setter
    def border_color(self, new_value):
        self._border_color = new_value

    @property
    def border_style(self):
        return self._border_style

    @border_style.setter
    def border_style(self, new_value):
        self._border_style = new_value

class CellBorderObject(BaseObject):
    """边框对象"""

    def __init__(self):
        self._border_left = BorderObject()  # 左边框
        self._border_top = BorderObject()  # 顶边框
        self._border_right = BorderObject()  # 右边框
        self._border_bottom = BorderObject()  # 底边框

    # AI generation start
    def to_dict(self):
        """Convert CellBorderObject to a dictionary."""
        return {
            "border_left": self._border_left.to_dict(),
            "border_top": self._border_top.to_dict(),
            "border_right": self._border_right.to_dict(),
            "border_bottom": self._border_bottom.to_dict(),
        }

    @classmethod
    def from_dict(cls, data):
        """Create CellBorderObject instance from a dictionary."""
        obj = cls()
        obj._border_left = BorderObject.from_dict(data.get("border_left", {}))
        obj._border_top = BorderObject.from_dict(data.get("border_top", {}))
        obj._border_right = BorderObject.from_dict(data.get("border_right", {}))
        obj._border_bottom = BorderObject.from_dict(data.get("border_bottom", {}))
        return obj

    # AI generation end
    @property
    def border_left(self):
        return self._border_left

    @border_left.setter
    def border_left(self, new_value):
        assert type(new_value) == BorderObject
        self._border_left = new_value

    @property
    def border_top(self):
        return self._border_top

    @border_top.setter
    def border_top(self, new_value):
        assert type(new_value) == BorderObject
        self._border_top = new_value

    @property
    def border_right(self):
        return self._border_right

    @border_right.setter
    def border_right(self, new_value):
        assert type(new_value) == BorderObject
        self._border_right = new_value

    @property
    def border_bottom(self):
        return self._border_bottom

    @border_bottom.setter
    def border_bottom(self, new_value):
        assert type(new_value) == BorderObject
        self._border_bottom = new_value

