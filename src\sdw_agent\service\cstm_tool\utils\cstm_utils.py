import re

import tiktoken
from langchain_core.prompts import BasePromptTemplate

from sdw_agent.service.cstm_tool.utils.config_manager import config_manager


class CstmUtils:
    '''
    cstm 工具类
    '''

    @staticmethod
    def extract_row_index(cell_coordinate: str):
        '''
        从单元格坐标中抽取行索引
        :param cell_coordinate:
        :return:
        '''
        # 使用正则表达式提取数字部分
        row_index = re.search(r'\d+', cell_coordinate)
        if row_index:
            row_index = int(row_index.group())
        else:
            row_index = None
        return row_index

    @staticmethod
    def trans_col_name(col_name):
        """
        将字母表示的列名转换成列索引
        :param col_name:
        :return:
        """
        row_idx = CstmUtils.extract_row_index(col_name)
        if row_idx:
            # 将col_name 中的数字替换成''
            col_name = re.sub(r'\d+', '', col_name)

        col_idx = 0
        for char in col_name:
            col_idx = col_idx * 26 + (ord(char.upper()) - ord('A') + 1)

        return row_idx, col_idx

    @staticmethod
    def clean_title(title_text: str) -> str:
        """
        清洗标题文本，移除引号、★符号和【#数字】这样的标记

        参数:
            title_text: 原始标题文本

        返回: 清洗后的标题文本
        """
        if not title_text:
            return ""
        # 移除引号
        title_text = title_text.replace('"', '').replace('"', '')
        # 移除★符号
        title_text = title_text.replace('★', '')
        # 移除【#数字】格式的标记
        title_text = re.sub(r'【#\d+】', '', title_text)
        # 移除可能的多余空格
        title_text = title_text.strip()
        return title_text

    @staticmethod
    def get_screen_hierarchy_maps():
        """
        从配置文件中获取屏幕层级映射字典

        Returns:
            tuple: (item_name_map, title_map)
        """
        screen_hierarchy_config = config_manager.config.get('workflow_config', {}).get('screen_hierarchy', {})
        item_name_map = screen_hierarchy_config.get('item_name_map', {})
        title_map = screen_hierarchy_config.get('title_map', {})
        return item_name_map, title_map

    @staticmethod
    def count_tokens_for_langchain(template: BasePromptTemplate, param_dict):
        """
        Count the number of tokens for each parameter in the parameter dictionary and the rendered prompt.

        Args:
            template (PromptTemplate): The LangChain PromptTemplate object.
            param_dict (dict): A dictionary containing the parameters to be filled into the template.

        Returns:
            dict: A dictionary where the keys are the parameter names and 'prompt', and the values are the corresponding token counts.
        """
        token_count_dict = {}
        # Iterate through each key-value pair in the parameter dictionary
        for k, v in param_dict.items():
            # Count the number of tokens in the value
            token_length = len(tiktoken.encoding_for_model('gpt-4o').encode(v))
            # Store the token count in the dictionary
            token_count_dict[k] = token_length

        # Render the prompt with the provided parameters
        prompt = '\n'.join([mes.content[0]['text'] for mes in template.invoke(param_dict).messages])
        # Count the number of tokens in the rendered prompt
        token_length = len(tiktoken.encoding_for_model('gpt-4o').encode(prompt))
        # Store the token count for the prompt in the dictionary
        token_count_dict['prompt'] = token_length
        return token_count_dict