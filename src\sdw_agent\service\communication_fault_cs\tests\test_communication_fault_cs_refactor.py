"""
通信故障安全CS工作流重构版本测试

测试重构后的工作流是否正常工作
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock

from sdw_agent.service.communication_fault_cs import (
    CommunicationFaultCSWorkflow,
    InputDataModel,
    SourceType,
    MatchedSignalModel
)
from sdw_agent.service import WorkflowStatus


class TestCommunicationFaultCSWorkflow:
    """通信故障安全CS工作流测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.workflow = CommunicationFaultCSWorkflow()
    
    def test_workflow_initialization(self):
        """测试工作流初始化"""
        assert self.workflow is not None
        assert self.workflow.name == "CommunicationFaultCSWorkflow"
        assert hasattr(self.workflow, 'max_workers')
        assert hasattr(self.workflow, 'default_case_sensitive')
    
    def test_input_validation_bit_assign_valid(self):
        """测试BitAssign输入验证 - 有效输入"""
        # 创建临时测试文件
        test_file = "test_bitassign.xls"
        with open(test_file, 'w') as f:
            f.write("test")
        
        try:
            input_data = InputDataModel(
                source_type=SourceType.BIT_ASSIGN,
                commit_id="1234567890abcdef",
                bit_assign_path=test_file
            )
            
            result = self.workflow.validate_input(input_data)
            assert result is True
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
    
    def test_input_validation_bit_assign_missing_file(self):
        """测试BitAssign输入验证 - 文件不存在"""
        input_data = InputDataModel(
            source_type=SourceType.BIT_ASSIGN,
            commit_id="1234567890abcdef",
            bit_assign_path="nonexistent_file.xls"
        )
        
        result = self.workflow.validate_input(input_data)
        assert result is False
    
    def test_input_validation_spi_json_valid(self):
        """测试SPI JSON输入验证 - 有效输入"""
        # 创建临时测试文件
        can_file = "test_can.json"
        ctrl_file = "test_ctrl.json"
        
        with open(can_file, 'w') as f:
            f.write('{"Message": {}}')
        with open(ctrl_file, 'w') as f:
            f.write('{"Message": {}}')
        
        try:
            input_data = InputDataModel(
                source_type=SourceType.SPI_JSON,
                commit_id="1234567890abcdef",
                can_json_path=can_file,
                ctrl_json_path=ctrl_file
            )
            
            result = self.workflow.validate_input(input_data)
            assert result is True
        finally:
            # 清理测试文件
            for file in [can_file, ctrl_file]:
                if os.path.exists(file):
                    os.remove(file)
    
    def test_input_validation_invalid_commit_id(self):
        """测试输入验证 - 无效的commit_id"""
        input_data = InputDataModel(
            source_type=SourceType.BIT_ASSIGN,
            commit_id="123",  # 太短
            bit_assign_path="test.xls"
        )
        
        result = self.workflow.validate_input(input_data)
        assert result is False
    
    def test_create_search_patterns(self):
        """测试搜索模式创建"""
        patterns = self.workflow._create_search_patterns("TestSignal")
        
        assert len(patterns) > 0
        assert any("TestSignal" in pattern for pattern in patterns)
        assert any(r"\b" in pattern for pattern in patterns)  # 单词边界
        assert any("=" in pattern for pattern in patterns)    # 赋值语句
    
    def test_is_comment_line(self):
        """测试注释行判断"""
        assert self.workflow._is_comment_line("// This is a comment") is True
        assert self.workflow._is_comment_line("/* Block comment */") is True
        assert self.workflow._is_comment_line("* Inside block comment") is True
        assert self.workflow._is_comment_line("# Python comment") is True
        assert self.workflow._is_comment_line("int variable = 5;") is False
    
    def test_get_column_index(self):
        """测试列索引获取"""
        headers = ["Col1", "Col2", "Data Label", "Col4"]
        
        index = self.workflow._get_column_index(headers, "Data Label")
        assert index == 2
        
        with pytest.raises(ValueError):
            self.workflow._get_column_index(headers, "NonExistent")
    
    @patch('sdw_agent.service.communication_fault_cs.workflow.get_diff_code_from_gerrit')
    @patch('sdw_agent.service.communication_fault_cs.workflow.ENV')
    def test_get_code_changes_from_gerrit(self, mock_env, mock_get_diff):
        """测试从Gerrit获取代码变更"""
        # 模拟ENV配置
        mock_env.config.gerrit.host = "gerrit.example.com"
        mock_env.config.gerrit.username = "user"
        mock_env.config.gerrit.password = "pass"
        
        # 模拟Gerrit返回数据
        mock_get_diff.return_value = {
            "test.c": {
                "changed_lines": {"added": [10, 11]},
                "diff": {"content": [{"b": ["line1", "line2"]}]},
                "change_id": "change123",
                "commit_id": "commit123"
            }
        }
        
        input_data = InputDataModel(
            source_type=SourceType.BIT_ASSIGN,
            commit_id="commit123",
            bit_assign_path="test.xls"
        )
        
        result = self.workflow._get_code_changes(input_data)
        
        assert len(result) == 1
        assert result[0]["file_name"] == "test.c"
        assert result[0]["commit_id"] == "commit123"
        assert result[0]["added_line_num"] == [10, 11]
    
    def test_matched_signal_model_creation(self):
        """测试匹配信号模型创建"""
        match = MatchedSignalModel(
            commit_id="commit123",
            file_path="/path/to/file.c",
            file_name="file.c",
            line_number=10,
            line_content="TestSignal = 1;",
            frame_id="0x123",
            matched_signal="TestSignal",
            init_value=0
        )
        
        assert match.commit_id == "commit123"
        assert match.file_name == "file.c"
        assert match.matched_signal == "TestSignal"
        assert match.frame_id == "0x123"
    
    def test_generate_summary(self):
        """测试摘要生成"""
        from sdw_agent.service.communication_fault_cs.models import OutputDataModel
        
        output_data = OutputDataModel(
            total_files_processed=5,
            total_matches_found=10,
            can_matches=[]
        )
        
        summary = self.workflow._generate_summary(output_data)
        
        assert "处理文件数: 5" in summary
        assert "找到匹配总数: 10" in summary
        assert "处理时间: 1.50秒" in summary
    
    @patch('sdw_agent.service.communication_fault_cs.workflow.save_df_to_excel')
    @patch('sdw_agent.service.communication_fault_cs.workflow.get_output_excel_path')
    def test_generate_output_file(self, mock_get_path, mock_save_excel):
        """测试输出文件生成"""
        mock_get_path.return_value = "/path/to/output.xlsx"
        
        input_data = InputDataModel(
            source_type=SourceType.BIT_ASSIGN,
            commit_id="commit123",
            bit_assign_path="test.xls"
        )
        
        can_matches = [
            MatchedSignalModel(
                commit_id="commit123",
                file_path="/path/to/file.c",
                file_name="file.c",
                line_number=10,
                line_content="TestSignal = 1;",
                frame_id="0x123",
                matched_signal="TestSignal",
                init_value=0
            )
        ]
        
        result = self.workflow._generate_output_file(input_data, can_matches, None)
        
        assert result == "/path/to/output.xlsx"
        mock_save_excel.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
