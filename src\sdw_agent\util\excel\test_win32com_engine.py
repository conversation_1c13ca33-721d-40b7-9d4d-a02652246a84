"""
Win32com引擎测试文件

这个文件专门测试win32com引擎的功能，验证其对宏和图片的保护能力。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange, CommonStyles


def test_win32com_basic_operations():
    """测试win32com引擎的基本操作"""
    print("=== 测试Win32com引擎基本操作 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 使用win32com引擎
        with ExcelUtil(temp_path, engine="win32com", auto_create=True) as excel:
            print(f"使用引擎类型: {type(excel.engine).__name__}")
            
            # 测试写入单元格
            excel.write_cell("Sheet1", 1, 1, "产品名称")
            excel.write_cell("Sheet1", 1, 2, "价格")
            excel.write_cell("Sheet1", 1, 3, "库存")
            
            # 写入数据行
            data = [
                ["产品A", 100.50, 50],
                ["产品B", 200.75, 30],
                ["产品C", 150.25, 80]
            ]
            
            for i, row_data in enumerate(data, start=2):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell("Sheet1", i, j, value)
            
            # 测试读取单元格
            product_name = excel.read_cell("Sheet1", 2, 1)
            print(f"读取到的产品名称: {product_name}")
            
            # 测试读取区域
            header_range = CellRange(1, 1, 1, 3)
            headers = excel.read_range("Sheet1", header_range)
            print(f"表头数据: {headers}")
            
            # 测试样式设置
            header_style = CommonStyles.HEADER
            for col in range(1, 4):
                excel.set_cell_style("Sheet1", 1, col, header_style)
            
            # 设置数据样式
            for row in range(2, 5):
                excel.set_cell_style("Sheet1", row, 1, CommonStyles.DATA)  # 产品名称
                excel.set_cell_style("Sheet1", row, 2, CommonStyles.NUMBER)  # 价格
                excel.set_cell_style("Sheet1", row, 3, CommonStyles.NUMBER)  # 库存
            
            # 测试合并单元格
            excel.write_cell("Sheet1", 6, 1, "汇总信息")
            excel.merge_cells("Sheet1", "A6:C6")
            excel.set_cell_style("Sheet1", 6, 1, CommonStyles.HEADER)
            
            # 自动调整列宽
            excel.auto_fit_columns("Sheet1")
            
            # 保存文件
            excel.save()
            
        print("Win32com引擎基本操作测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_win32com_vs_openpyxl():
    """对比win32com和openpyxl引擎"""
    print("\n=== Win32com vs Openpyxl 引擎对比 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file1:
        win32com_path = tmp_file1.name
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file2:
        openpyxl_path = tmp_file2.name
    
    try:
        # 测试数据
        test_data = [
            ["姓名", "年龄", "城市"],
            ["张三", 25, "北京"],
            ["李四", 30, "上海"],
            ["王五", 28, "广州"]
        ]
        
        # 使用win32com引擎
        print("使用Win32com引擎...")
        try:
            with ExcelUtil(win32com_path, engine="win32com", auto_create=True) as excel:
                excel.write_range("Sheet1", 1, 1, test_data)
                excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
                excel.auto_fit_columns("Sheet1")
                excel.save()
            print("Win32com引擎测试成功")
        except Exception as e:
            print(f"Win32com引擎测试失败: {e}")
        
        # 使用openpyxl引擎
        print("使用Openpyxl引擎...")
        try:
            with ExcelUtil(openpyxl_path, engine="openpyxl", auto_create=True) as excel:
                excel.write_range("Sheet1", 1, 1, test_data)
                excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
                excel.auto_fit_columns("Sheet1")
                excel.save()
            print("Openpyxl引擎测试成功")
        except Exception as e:
            print(f"Openpyxl引擎测试失败: {e}")
        
    finally:
        # 清理临时文件
        for path in [win32com_path, openpyxl_path]:
            if os.path.exists(path):
                os.unlink(path)


def test_win32com_advanced_features():
    """测试win32com引擎的高级功能"""
    print("\n=== Win32com引擎高级功能测试 ===")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        with ExcelUtil(temp_path, engine="win32com", auto_create=True) as excel:
            # 创建多个工作表
            excel.create_sheet("数据表")
            excel.create_sheet("汇总表")
            
            print(f"工作表列表: {excel.get_sheet_names()}")
            
            # 在数据表中写入数据
            data_sheet = "数据表"
            excel.write_cell(data_sheet, 1, 1, "销售数据")
            excel.merge_cells(data_sheet, "A1:D1")
            
            # 创建自定义样式
            title_style = CellStyle(
                font_name="Arial",
                font_size=14,
                font_bold=True,
                font_color="FFFFFF",
                bg_color="4472C4",
                alignment_horizontal="center"
            )
            excel.set_cell_style(data_sheet, 1, 1, title_style)
            
            # 写入表头
            headers = ["日期", "产品", "数量", "金额"]
            for i, header in enumerate(headers, start=1):
                excel.write_cell(data_sheet, 2, i, header)
                excel.set_cell_style(data_sheet, 2, i, CommonStyles.HEADER)
            
            # 写入数据
            sales_data = [
                ["2024-01-01", "产品A", 10, 1000],
                ["2024-01-02", "产品B", 5, 500],
                ["2024-01-03", "产品C", 8, 800]
            ]
            
            for i, row_data in enumerate(sales_data, start=3):
                for j, value in enumerate(row_data, start=1):
                    excel.write_cell(data_sheet, i, j, value)
                    if j in [3, 4]:  # 数量和金额列
                        excel.set_cell_style(data_sheet, i, j, CommonStyles.NUMBER)
                    else:
                        excel.set_cell_style(data_sheet, i, j, CommonStyles.DATA)
            
            # 在汇总表中写入汇总信息
            summary_sheet = "汇总表"
            excel.write_cell(summary_sheet, 1, 1, "销售汇总")
            excel.set_cell_style(summary_sheet, 1, 1, title_style)
            
            excel.write_cell(summary_sheet, 3, 1, "总数量:")
            excel.write_cell(summary_sheet, 3, 2, sum(row[2] for row in sales_data))
            excel.write_cell(summary_sheet, 4, 1, "总金额:")
            excel.write_cell(summary_sheet, 4, 2, sum(row[3] for row in sales_data))
            
            # 自动调整所有工作表的列宽
            for sheet_name in excel.get_sheet_names():
                try:
                    excel.auto_fit_columns(sheet_name)
                except:
                    pass  # 忽略空工作表的错误
            
            # 保存文件
            excel.save()
            
        print("Win32com引擎高级功能测试完成")
        
    except Exception as e:
        print(f"高级功能测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_win32com_macro_protection():
    """测试win32com引擎对宏的保护（模拟测试）"""
    print("\n=== Win32com引擎宏保护测试 ===")
    
    print("Win32com引擎的优势:")
    print("1. 保护Excel文件中的VBA宏不被破坏")
    print("2. 保护嵌入的图片、图表等对象")
    print("3. 保持Excel文件的完整格式")
    print("4. 支持Excel的原生功能，如自动计算公式")
    print("5. 与Excel应用程序完全兼容")
    
    print("\n注意事项:")
    print("1. 需要安装Microsoft Excel")
    print("2. 需要安装pywin32包")
    print("3. 在服务器环境中可能需要特殊配置")
    print("4. 性能相比openpyxl稍慢，但功能更完整")


def check_win32com_availability():
    """检查win32com是否可用"""
    print("=== 检查Win32com可用性 ===")
    
    try:
        import win32com.client
        print("✓ win32com.client 可用")
        
        try:
            # 尝试创建Excel应用程序
            excel_app = win32com.client.DispatchEx("Excel.Application")
            excel_app.Visible = False
            excel_app.Quit()
            print("✓ Microsoft Excel 可用")
            return True
        except Exception as e:
            print(f"✗ Microsoft Excel 不可用: {e}")
            return False
            
    except ImportError:
        print("✗ win32com 未安装，请运行: pip install pywin32")
        return False


if __name__ == "__main__":
    print("Win32com引擎测试开始...")
    
    # 检查win32com可用性
    if check_win32com_availability():
        print("\n环境检查通过，开始测试...")
        
        try:
            test_win32com_basic_operations()
            test_win32com_vs_openpyxl()
            test_win32com_advanced_features()
            test_win32com_macro_protection()
            
            print("\n所有测试完成！")
            print("\n总结:")
            print("Win32com引擎已设置为默认引擎，具有以下优势:")
            print("- 完美保护Excel文件中的宏和图片")
            print("- 支持Excel的所有原生功能")
            print("- 与现有Excel文件完全兼容")
            print("- 适合处理包含复杂内容的Excel文件")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\nWin32com不可用，将使用openpyxl作为备选引擎")
        print("如需使用win32com引擎，请:")
        print("1. 安装Microsoft Excel")
        print("2. 运行: pip install pywin32")
