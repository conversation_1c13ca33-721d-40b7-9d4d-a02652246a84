from langchain_core.messages import AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from tiktoken import get_encoding
from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.design_peerreview.models import ConfirmFormat

def llm_peer_review(error_list):
    # 创建一个聊天提示模板，用于生成变更摘要
    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt.peer_review_prompt)
        ],
        template_format="mustache",
    )
    invoke_data = {
        "error_list": error_list
    }

    resp: AIMessage = get_ai_message_with_structured_output(
        template,
        invoke_data,
        ConfirmFormat,
        llm_model=None
    )
    return resp.confirm_result
