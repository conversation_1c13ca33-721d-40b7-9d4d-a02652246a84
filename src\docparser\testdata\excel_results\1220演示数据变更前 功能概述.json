{"_data_id": 2, "_elements": [{"_cell_list": "", "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 1, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.功能目标", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.1、提升驾驶安全性和舒适性，减少驾驶员操作负担，最终实现部分或完全自动驾驶。", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.功能目标\n1.1、提升驾驶安全性和舒适性，减少驾驶员操作负担，最终实现部分或完全自动驾驶。", "_type": "text"}, {"_cell_list": "", "_coordinate": {"_bottom": "", "_desc": "B6", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 2, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B6", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "2.预期用户", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "B7", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#EF939F", "_font_family": "宋体", "_font_size": 11.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "2.1、驾驶员：需要更安全、更舒适的驾驶体验\n2.2、车队管理人员：提高运营效率和安全性", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "2.预期用户\n2.1、驾驶员：需要更安全、更舒适的驾驶体验\n2.2、车队管理人员：提高运营效率和安全性", "_type": "text"}], "_file_name": "1220演示数据变更前.xlsx", "_footer": [], "_graphics": [], "_header": [], "_layouts": [], "_name": "功能概述", "_pictures": [], "_settings": [], "_styles": [], "_tables": [], "_texts": [{"_cell_list": "", "_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 1, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B3", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.功能目标", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "B4", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.1、提升驾驶安全性和舒适性，减少驾驶员操作负担，最终实现部分或完全自动驾驶。", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "1.功能目标\n1.1、提升驾驶安全性和舒适性，减少驾驶员操作负担，最终实现部分或完全自动驾驶。", "_type": "text"}, {"_cell_list": "", "_coordinate": {"_bottom": "", "_desc": "B6", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_data_id": 2, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_runs": [{"_coordinate": {"_bottom": "", "_desc": "B6", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "2.预期用户", "_type": ""}, {"_coordinate": {"_bottom": "", "_desc": "B7", "_left": "", "_relative": null, "_right": "", "_top": ""}, "_layout": {"_next_ref": "", "_page_id": "", "_parent_content": "", "_parent_ref": "", "_prev_ref": ""}, "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#EF939F", "_font_family": "宋体", "_font_size": 11.0, "_font_style": {"_bold": false, "_italic": false, "_normal": true, "_strikeout": false, "_underline": false}}, "_text": "2.1、驾驶员：需要更安全、更舒适的驾驶体验\n2.2、车队管理人员：提高运营效率和安全性", "_type": ""}], "_style": {"_background_color": "#FFFFFF", "_background_style": "", "_font_color": "#404040", "_font_family": "Sim<PERSON>un", "_font_size": 12.0, "_font_style": {"_bold": true, "_italic": false, "_normal": false, "_strikeout": false, "_underline": false}}, "_text": "2.预期用户\n2.1、驾驶员：需要更安全、更舒适的驾驶体验\n2.2、车队管理人员：提高运营效率和安全性", "_type": "text"}], "_timing_texts": [], "_timing_waves": [], "_type": "block"}