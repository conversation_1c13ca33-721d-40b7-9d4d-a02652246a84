from typing import Union
from collections import defaultdict
from pathlib import Path

from anytree import Node, RenderTree
from loguru import logger

from sdw_agent.util.local_file_read_save import read_json, save_pkl


def ingest_testcases(file_path: Union[str, Path], save_path: Union[str, Path]):
    """
    解析测试用例文件，并对文件内容做标准化整合
    Args:
        file_path: 测试用例文件路径
    Return:
        str: 标准化整合后内容的保存地址
    """

    logger.info(f"开始解析测试用例文件：{file_path}")

    try:
        # 开始解析测试用例文件
        data = read_json(file_path)
        logger.info(f"解析测试用例文件完成")

        # 将测试用例文件处理成标准格式
        logger.info(f"开始将解析后的测试用例文件进行标准化")
        data_tree = build_and_save_tree(data, file_path)
        logger.info(f"解析后的测试用例文件标准化完成")

        # 将处理后的标准文件保存
        logger.info(f"开始保存处理后的标准文件")
        save_pkl(data_tree, save_path)

        logger.info(f"测试用例文件解析并标准化完成，已保存于：{save_path}")
        # 返回保存地址
        return save_path
    except Exception as e:
        logger.error(f"测试用例文件处理失败: {str(e)}")
        return None


def build_and_save_tree(data, doc_name):
    """
    构建树结构并保存为离线文件

    参数:
        data: 输入数据
        doc_name: 作为根节点值的文档名
    """
    # 创建根节点
    root = Node(doc_name)

    # 创建节点缓存字典
    node_cache = {doc_name: root}

    # 重新整合测试用例解析结果
    data_dict = defaultdict(list)
    for one_data in data:
        if one_data["示例"] == "说明" or one_data["示例"] == "Please Insert above this line":
            continue
        sheet_name = one_data.get('sheet_name')
        data_dict[sheet_name].append(one_data)

    # 遍历每个sheet
    for sheet_name, data_list in data_dict.items():
        if sheet_name not in node_cache:
            node_cache[sheet_name] = Node(sheet_name, parent=root)
        current_parent = node_cache[sheet_name]
        for item in data_list:
            # 解析当前条目
            function = item.get("模块名称", "")
            path_parts = item["确认点"].replace('\n', '').split('-')
            value = item

            # 确保function节点存在
            if function:
                function = function.replace('\n', '')
                function_path = f"{sheet_name}/{function}"
                if function_path not in node_cache:
                    current_parent = node_cache[sheet_name]
                    node_cache[function_path] = Node(function, parent=current_parent)
            else:
                function_path = sheet_name

            # 当前父节点初始化为function节点
            current_parent = node_cache[function_path]
            current_path = function_path

            # 逐层构建路径节点
            for part in path_parts[:-1]:
                current_path += f"/{part}"
                if current_path not in node_cache:
                    node_cache[current_path] = Node(part, parent=current_parent)
                current_parent = node_cache[current_path]

            # 创建叶子节点并设置值
            leaf_name = path_parts[-1]
            leaf_path = current_path + f"/{leaf_name}"
            if leaf_path not in node_cache:
                node_cache[leaf_path] = Node(leaf_name, parent=current_parent, value=[value])
            else:
                try:
                    if not hasattr(node_cache[leaf_path], "value"):
                        node_cache[leaf_path].value = []
                    node_cache[leaf_path].value.append(value)
                except Exception as e:
                    logger.error(f"{e}")

    return root


if __name__ == '__main__':
    # 测试数据
    test_path = '../../../../data/testcase_data.json'
    test_json = read_json(test_path)

    # 构建树并保存
    root = build_and_save_tree(test_json, test_path)
    # 打印树结构
    for pre, _, node in RenderTree(root):
        value_str = f" = {node.value}" if hasattr(node, 'value') else ""
        print(f"{pre}{node.name}{value_str}")

    # save_path = ingest_testcases(test_path,r"D:\bbb\TESTAgent\data\testcases_.pkl")
    #
    # # 保存测试
    # from sdw_agent.util.local_file_read_save import read_pkl
    #
    # read_res = read_pkl(save_path)
    # # 打印树结构
    # for pre, _, node in RenderTree(read_res):
    #     value_str = f" = {node.value}" if hasattr(node, 'value') else ""
    #     print(f"{pre}{node.name}{value_str}")
    # print(save_path)
