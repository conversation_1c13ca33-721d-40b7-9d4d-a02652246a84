# ExcelUtil使用指南

## 快速开始

### 基本使用（推荐）

```python
from sdw_agent.util.excel.core import ExcelUtil, CommonStyles

# 默认使用win32com引擎，保护宏和图片
with ExcelUtil("report.xlsx", auto_create=True) as excel:
    # 写入数据
    excel.write_cell("Sheet1", 1, 1, "产品名称")
    excel.write_cell("Sheet1", 1, 2, "销售额")
    
    # 设置样式
    excel.set_cell_style("Sheet1", 1, 1, CommonStyles.HEADER)
    excel.set_cell_style("Sheet1", 1, 2, CommonStyles.HEADER)
    
    # 保存文件
    excel.save()
```

## 引擎选择指南

### 🏆 Win32com引擎（默认，推荐）

**适用场景：**
- ✅ 处理包含VBA宏的Excel文件
- ✅ 处理包含图片、图表的Excel文件
- ✅ 企业级应用，需要完整兼容性
- ✅ 需要保持Excel文件的完整性

**使用方法：**
```python
# 默认就是win32com引擎
excel = ExcelUtil("file.xlsx")

# 或者显式指定
excel = ExcelUtil("file.xlsx", engine="win32com")
```

**环境要求：**
- Microsoft Excel已安装
- pywin32包：`pip install pywin32`

### 🐍 Openpyxl引擎

**适用场景：**
- ✅ 创建简单的数据表格
- ✅ 服务器环境，没有安装Excel
- ✅ 纯Python环境
- ❌ 不适合处理包含宏的文件

**使用方法：**
```python
excel = ExcelUtil("file.xlsx", engine="openpyxl")
```

### 📊 Pandas引擎

**适用场景：**
- ✅ 只需要读取Excel数据
- ✅ 数据分析和处理
- ❌ 不支持写入操作

**使用方法：**
```python
excel = ExcelUtil("file.xlsx", engine="pandas")
data = excel.read_range("Sheet1", "A1:C10")
```

## 常用操作示例

### 1. 数据写入

```python
with ExcelUtil("data.xlsx") as excel:
    # 单个单元格
    excel.write_cell("Sheet1", 1, 1, "标题")
    
    # 批量数据
    data = [
        ["姓名", "年龄", "部门"],
        ["张三", 28, "技术部"],
        ["李四", 32, "销售部"]
    ]
    excel.write_range("Sheet1", 1, 1, data)
    
    # DataFrame
    import pandas as pd
    df = pd.DataFrame({"A": [1, 2], "B": [3, 4]})
    excel.write_dataframe("Sheet1", df, start_row=1)
```

### 2. 数据读取

```python
with ExcelUtil("data.xlsx") as excel:
    # 单个单元格
    value = excel.read_cell("Sheet1", 1, 1)
    
    # 区域数据
    data = excel.read_range("Sheet1", "A1:C3")
    
    # 读取为DataFrame
    df = excel.read_range_as_dataframe("Sheet1", "A1:C10")
```

### 3. 样式设置

```python
from sdw_agent.util.excel.core import CellStyle

with ExcelUtil("styled.xlsx") as excel:
    # 使用预定义样式
    excel.set_cell_style("Sheet1", 1, 1, CommonStyles.HEADER)
    excel.set_cell_style("Sheet1", 2, 1, CommonStyles.DATA)
    
    # 自定义样式
    custom_style = CellStyle(
        font_bold=True,
        font_color="FFFFFF",
        bg_color="4472C4",
        alignment_horizontal="center"
    )
    excel.set_cell_style("Sheet1", 1, 1, custom_style)
    
    # 区域样式
    excel.set_range_style("Sheet1", "A1:C1", CommonStyles.HEADER)
```

### 4. 工作表管理

```python
with ExcelUtil("workbook.xlsx") as excel:
    # 获取工作表列表
    sheets = excel.get_sheet_names()
    
    # 创建新工作表
    excel.create_sheet("数据表")
    
    # 模糊匹配工作表名
    sheet_name = excel.get_sheet_name_fuzzy("数据")  # 匹配"数据表"
    
    # 复制工作表
    excel.copy_sheet("Sheet1", "Sheet1_备份")
```

### 5. 高级功能

```python
with ExcelUtil("advanced.xlsx") as excel:
    # 合并单元格
    excel.merge_cells("Sheet1", "A1:C1")
    
    # 自动调整列宽
    excel.auto_fit_columns("Sheet1")
    
    # 指定列调整
    excel.auto_fit_columns("Sheet1", ["A", "B", "C"])
```

## 业务扩展示例

### 创建业务专用类

```python
class SalesReportExcel(ExcelUtil):
    """销售报表专用Excel工具"""
    
    def __init__(self, file_path: str):
        # 使用win32com引擎保护宏和图片
        super().__init__(file_path, engine="win32com")
        
        # 定义业务专用样式
        self.title_style = CellStyle(
            font_size=16,
            font_bold=True,
            bg_color="4472C4",
            font_color="FFFFFF",
            alignment_horizontal="center"
        )
    
    def create_report_template(self):
        """创建报表模板"""
        # 写入标题
        self.write_cell("Sheet1", 1, 1, "销售报表")
        self.merge_cells("Sheet1", "A1:E1")
        self.set_cell_style("Sheet1", 1, 1, self.title_style)
        
        # 写入表头
        headers = ["日期", "产品", "数量", "单价", "总额"]
        for i, header in enumerate(headers, 1):
            self.write_cell("Sheet1", 3, i, header)
            self.set_cell_style("Sheet1", 3, i, CommonStyles.HEADER)
    
    def add_sales_data(self, sales_data):
        """添加销售数据"""
        for i, record in enumerate(sales_data, start=4):
            self.write_cell("Sheet1", i, 1, record["date"])
            self.write_cell("Sheet1", i, 2, record["product"])
            self.write_cell("Sheet1", i, 3, record["quantity"])
            self.write_cell("Sheet1", i, 4, record["price"])
            self.write_cell("Sheet1", i, 5, record["total"])

# 使用业务类
with SalesReportExcel("sales_report.xlsx") as excel:
    excel.create_report_template()
    excel.add_sales_data([
        {"date": "2024-01-01", "product": "产品A", "quantity": 10, "price": 100, "total": 1000}
    ])
    excel.save()
```

## 错误处理

### 引擎不可用处理

```python
try:
    excel = ExcelUtil("file.xlsx", engine="win32com")
except Exception as e:
    print(f"Win32com不可用: {e}")
    print("使用openpyxl引擎作为备选")
    excel = ExcelUtil("file.xlsx", engine="openpyxl")
```

### 文件操作错误处理

```python
try:
    with ExcelUtil("file.xlsx") as excel:
        excel.write_cell("Sheet1", 1, 1, "数据")
        excel.save()
except FileNotFoundError:
    print("文件不存在")
except PermissionError:
    print("文件被其他程序占用")
except Exception as e:
    print(f"操作失败: {e}")
```

## 性能优化建议

### 1. 批量操作

```python
# 推荐：批量写入
data = [["A", "B"], [1, 2], [3, 4]]
excel.write_range("Sheet1", 1, 1, data)

# 避免：逐个写入
# for i, row in enumerate(data):
#     for j, value in enumerate(row):
#         excel.write_cell("Sheet1", i+1, j+1, value)
```

### 2. 引擎选择

```python
# 大量数据读取：使用pandas引擎
excel = ExcelUtil("big_data.xlsx", engine="pandas")
df = excel.read_range_as_dataframe("Sheet1", "A1:Z1000")

# 复杂操作：使用win32com引擎
excel = ExcelUtil("complex.xlsx", engine="win32com")
```

## 最佳实践

1. **默认使用win32com引擎**：保护宏和图片
2. **使用上下文管理器**：确保资源正确释放
3. **批量操作**：提高性能
4. **错误处理**：处理引擎不可用的情况
5. **业务封装**：创建专用的业务类

## 常见问题

**Q: win32com引擎报错怎么办？**
A: 确保安装了Excel和pywin32，或使用openpyxl引擎

**Q: 如何处理大文件？**
A: 使用pandas引擎读取，win32com或openpyxl引擎写入

**Q: 如何保护现有的宏？**
A: 必须使用win32com引擎，openpyxl可能破坏宏

**Q: 服务器环境如何使用？**
A: 如果没有Excel，使用openpyxl引擎
