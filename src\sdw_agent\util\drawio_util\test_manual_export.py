#!/usr/bin/env python3
"""
测试手动导出功能
"""

import os
from pathlib import Path
from loguru import logger

def test_manual_export():
    """测试手动导出功能"""
    print("🎯 测试手动导出功能")
    print("=" * 50)
    
    # 1. 提示用户手动创建截图
    print("📋 请按照以下步骤操作:")
    print("1. 在浏览器中打开 https://app.diagrams.net/")
    print("2. 打开你的 drawio 文件")
    print("3. 点击 File -> Export as -> PNG")
    print("4. 下载 PNG 文件")
    print("5. 将文件重命名为 manual_screenshot.png")
    print("6. 将文件放到 output 目录下")
    print()
    
    # 2. 检查文件是否存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    screenshot_path = output_dir / "manual_screenshot.png"
    
    if not screenshot_path.exists():
        print(f"❌ 截图文件不存在: {screenshot_path}")
        print("💡 请先按照上述步骤创建截图文件，然后重新运行此脚本")
        return
    
    print(f"✅ 找到截图文件: {screenshot_path}")
    
    # 3. 插入到 Excel
    try:
        from sdw_agent.util.excel.core import ExcelUtil, CellStyle
        from openpyxl.drawing.image import Image as OpenpyxlImage
        
        excel_file = output_dir / "manual_test_result.xlsx"
        
        with ExcelUtil(str(excel_file), auto_create=True) as excel:
            sheet_name = "架构图"
            
            # 确保工作表存在
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            # 添加标题
            excel.write_cell(sheet_name, 1, 1, "系统架构图（手动截图版本）")
            title_style = CellStyle(
                font_size=16,
                font_bold=True,
                alignment_horizontal="center"
            )
            excel.set_cell_style(sheet_name, 1, 1, title_style)
            
            # 插入图像
            if hasattr(excel, '_get_worksheet'):
                ws = excel._get_worksheet(sheet_name)
                if ws:
                    img = OpenpyxlImage(str(screenshot_path))
                    
                    # 调整图像大小
                    max_width = 1400
                    max_height = 1000
                    
                    if img.width > max_width:
                        scale_factor = max_width / img.width
                        img.width = max_width
                        img.height = int(img.height * scale_factor)
                    
                    if img.height > max_height:
                        scale_factor = max_height / img.height
                        img.height = max_height
                        img.width = int(img.width * scale_factor)
                    
                    # 设置图像位置
                    target_cell = ws.cell(row=3, column=1)
                    img.anchor = target_cell.coordinate
                    
                    # 添加图像
                    ws.add_image(img)
                    
                    # 调整行高
                    ws.row_dimensions[3].height = max(ws.row_dimensions[3].height or 15, 
                                                     img.height * 0.75)
                    
                    logger.success(f"图像已插入到 Excel: {excel_file}")
                    print(f"✅ 图像已成功插入到 Excel")
                    print(f"📁 Excel文件: {excel_file}")
                    print(f"📊 图像尺寸: {img.width} x {img.height}")
                    
            excel.save()
            
        print(f"\n🎉 测试完成！请打开 Excel 文件查看结果。")
        
    except Exception as e:
        logger.error(f"插入图像失败: {str(e)}")
        print(f"❌ 插入图像失败: {str(e)}")


if __name__ == "__main__":
    test_manual_export()
