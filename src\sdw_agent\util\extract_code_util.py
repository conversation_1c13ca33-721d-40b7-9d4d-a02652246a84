import mmap
import os
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any

from loguru import logger


class ConstFinder:
    def __init__(self, search_paths, max_depth=10):
        self.search_paths = search_paths
        self.max_depth = max_depth
        self.file_cache = {}  # 文件内容缓存
        self.constant_cache = {}  # 常量值缓存

        # 优化的正则表达式模式
        self.include_pattern = re.compile(
            r'^\s*#include\s+(?:["<]([^">]+)[">]|\{([^}]+)})',
            re.MULTILINE
        )
        # 改进的define模式，更好地处理注释
        self.define_pattern = re.compile(
            r'^\s*#define\s+(\w+)\s+([^/\n]+?)(?:\s*(?://.*|/\*.*?\*/))?$',
            re.MULTILINE
        )
        self.const_pattern = re.compile(
            r'^\s*(?:static\s+)?const\s+\w+\s+(\w+)\s*=\s*([^;/]+?)(?:\s*(?://.*|/\*.*?\*/))?\s*;',
            re.MULTILINE
        )
        self.enum_pattern = re.compile(r'enum\s*(?:\w+\s*)?\{([^}]*)}', re.DOTALL)
        self.enum_value_pattern = re.compile(r'(\w+)\s*=\s*([^,}/]+?)(?:\s*(?://.*|/\*.*?\*/))?(?=\s*[,}])')
        self.bitwise_pattern = re.compile(r'(\w+)\s*([|&^])\s*(\w+)')

    def find_constant(self, filename, const_name):
        """查找常量定义的主入口"""
        cache_key = f"{filename}:{const_name}"
        if cache_key in self.constant_cache:
            return self.constant_cache[cache_key]

        visited_files = set()
        result = self._find_in_file(filename, const_name, visited_files, 0)
        self.constant_cache[cache_key] = result
        return result

    def _get_file_content(self, filename):
        """获取文件内容，带缓存"""
        if filename in self.file_cache:
            return self.file_cache[filename]

        try:
            with open(filename, 'r', encoding='utf-8', errors='replace') as file:
                content = file.read()
                self.file_cache[filename] = content
                return content
        except Exception as e:
            logger.warning(f"读取文件失败: {filename}, 错误: {e}")
            return None

    def _resolve_expression(self, expression, filename, visited_files, depth):
        """递归解析表达式（支持位运算、嵌套宏等）"""
        if not expression:
            return None

        # 清理注释和多余空格
        expression = self._clean_expression(expression)

        # 处理括号包围的表达式
        if expression.startswith('(') and expression.endswith(')'):
            expression = expression[1:-1].strip()

        # 再次清理可能的后缀
        expression = expression.rstrip('ULul')

        # 尝试直接转换为数字
        try:
            if expression.startswith('0x') or expression.startswith('0X'):
                return int(expression, 16)
            elif expression.startswith('0') and len(expression) > 1 and expression.isdigit():
                return int(expression, 8)
            else:
                return int(expression)
        except ValueError:
            pass

        # 处理位运算表达式
        bitwise_match = self.bitwise_pattern.search(expression)
        if bitwise_match:
            left, operator, right = bitwise_match.groups()
            left_value = self._find_in_file(filename, left, visited_files, depth + 1)
            right_value = self._find_in_file(filename, right, visited_files, depth + 1)

            if left_value is not None and right_value is not None:
                try:
                    left_int = int(left_value) if isinstance(left_value, str) else left_value
                    right_int = int(right_value) if isinstance(right_value, str) else right_value

                    if operator == '|':
                        return left_int | right_int
                    elif operator == '&':
                        return left_int & right_int
                    elif operator == '^':
                        return left_int ^ right_int
                except (ValueError, TypeError):
                    logger.warning(f"无法解析位运算表达式：{expression}")
                    return expression

        # 尝试作为宏名称递归查找
        result = self._find_in_file(filename, expression, visited_files, depth + 1)
        return result if result is not None else expression

    @staticmethod
    def _clean_expression(expression):
        """清理表达式中的注释和多余字符"""
        # 移除行尾注释 /* ... */
        expression = re.sub(r'/\*.*?\*/', '', expression).strip()

        # 移除行尾注释 // ...
        expression = re.sub(r'//.*$', '', expression).strip()

        # 移除多余的空格和制表符
        expression = re.sub(r'\s+', ' ', expression).strip()

        return expression

    def _find_in_file(self, filename, const_name, visited_files, depth):
        """在文件中查找常量定义"""
        # 防止无限递归和循环包含
        if depth > self.max_depth:
            logger.warning(f"达到最大递归深度 {self.max_depth}，停止查找: {const_name}")
            return None

        abs_filename = str(Path(filename).resolve()) if Path(filename).exists() else filename
        if abs_filename in visited_files:
            return None
        visited_files.add(abs_filename)

        try:
            content = self._get_file_content(filename)
            if not content:
                return None

            # 优化：先快速检查是否包含目标常量名
            if const_name not in content:
                return self._search_includes(content, const_name, visited_files, depth)

            # 统一的搜索策略
            result = self._search_definitions(content, const_name, filename, visited_files, depth)
            if result is not None:
                return result

            # 如果在当前文件未找到，搜索包含文件
            return self._search_includes(content, const_name, visited_files, depth)

        except Exception as e:
            logger.error(f"查找常量时发生错误: {filename}, 常量: {const_name}, 错误: {e}")
            return None
        finally:
            visited_files.discard(abs_filename)

    def _search_definitions(self, content, const_name, filename, visited_files, depth):
        """统一搜索各种类型的定义"""
        # 定义搜索策略：(模式, 处理函数)
        search_strategies = [
            (self.define_pattern, self._handle_simple_match),
            (self.const_pattern, self._handle_simple_match),
            (self.enum_pattern, self._handle_enum_match)
        ]

        for pattern, handler in search_strategies:
            result = handler(pattern, content, const_name, filename, visited_files, depth)
            if result is not None:
                return result

        return None

    def _handle_simple_match(self, pattern, content, const_name, filename, visited_files, depth):
        """处理简单的名称-值匹配"""
        for match in pattern.finditer(content):
            name, value = match.groups()
            if name == const_name:
                return self._resolve_expression(value, filename, visited_files, depth)
        return None

    def _handle_enum_match(self, pattern, content, const_name, filename, visited_files, depth):
        """处理枚举定义匹配"""
        for enum_match in pattern.finditer(content):
            enum_content = enum_match.group(1)
            for value_match in self.enum_value_pattern.finditer(enum_content):
                name, value = value_match.groups()
                if name == const_name:
                    return self._resolve_expression(value, filename, visited_files, depth)
        return None

    def _search_includes(self, content, const_name, visited_files, depth):
        """搜索包含文件"""
        for include_match in self.include_pattern.finditer(content):
            include_file = include_match.group(1) or include_match.group(2)
            if include_file:
                found_path = self._find_include_file(include_file)
                if found_path:
                    result = self._find_in_file(str(found_path), const_name, visited_files, depth + 1)
                    if result is not None:
                        return result
        return None

    def _find_include_file(self, include_file):
        """查找包含文件的路径"""
        search_path = Path(self.search_paths)

        # 优先在当前目录查找
        direct_path = search_path / include_file
        if direct_path.exists():
            return direct_path

        # 递归查找
        for file_path in search_path.rglob(include_file):
            if file_path.is_file():
                return file_path
        return None

    def clear_cache(self):
        """清空缓存"""
        self.file_cache.clear()
        self.constant_cache.clear()

    def get_cache_stats(self):
        """获取缓存统计信息"""
        return {
            'file_cache_size': len(self.file_cache),
            'constant_cache_size': len(self.constant_cache)
        }


class InterruptAndSchedulerExtractor:
    """
    中断表和调度任务信息提取器
    用于从代码仓库中提取中断处理程序和调度任务相关信息
    """

    def __init__(self, repo_path: str, repo_extensions: List[str]):
        """
        初始化提取器

        Args:
            repo_path: 仓库路径
            repo_extensions: 文件扩展名列表
        """
        self.repo_path = repo_path
        self.repo_extensions = repo_extensions
        self.repo_path_obj = Path(repo_path)

        # 预编译正则表达式
        self.table_pattern = re.compile(
            r"const\s+(ST_INT_HNDLR_IVT)\s+(\w+)\s*\[\s*(\w+)\s*]\s*=\s*\{(?:[^}]*?\{.*?})*.*?};",
            re.DOTALL
        )

        self.entry_pattern = re.compile(
            r"\{\s*(?:&|\(void\s*\*\))?(\w+|NULL)\s*,\s*\(U2\)(\w+)\s*,\s*\(U1\)(\w+)\s*,\s*\(U1\)(\w+)\s*}\s*,?\s*(?:/\*\s*(.*?)\s*\*/)?",
            re.DOTALL
        )
        self.scheduler_tasks_pattern = r'const\s+ST_SCHDLR_RGLR\s+st_gp_SCHDLR_RGLR_TASK\s*\[\s*\]\s*=\s*\{((?:[^{}]*(?:\{(?:[^{}]*)\}[^{}]*)*)*)\}\s*;'
        self.scheduler_tasks_func_pattern = r'''
            (?:\&\s*(vd_g_[a-zA-Z_]\w*)\b)      # &vd_g_xxx格式
            |(?:[A-Z0-9_]+\s*\(\s*(vd_g_[a-zA-Z_]\w*)\s*\))  # 宏包装格式
        '''
        self.scheduler_tasks_func_detail = r'''\{\s*(?:\&\s*(vd_g_[a-zA-Z_]\w*)|\&\s*[A-Za-z0-9_]+\s*\(\s*([a-zA-Z_]\w*)\s*\))
                                               \s*,\s*\([A-Za-z0-9]+\)\s*(SCHDLR_TASKBIT_[A-Za-z0-9_]+)\s*\}'''

    # 通用辅助方法
    @staticmethod
    def _read_file_content(file: Path) -> str:
        """
        读取文件内容，优先使用内存映射

        参数:
            file: 文件路径

        返回:
            文件内容字符串
        """
        try:
            with open(file, 'r', encoding='utf-8', errors='replace') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    return mmapped_file.read().decode('utf-8', errors='replace')
        except (OSError, ValueError):
            with open(file, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()

    def extract(self) -> Tuple[Dict[str, int], Set[str]]:
        """
        获取中断表和调度任务信息

        Returns:
            Tuple[Dict[str, int], Set[str]]: 中断表字典和调度任务集合
        """
        logger.info("开始获取中断表和调度任务信息")

        # 查找目标文件
        target_files = []
        for ext in self.repo_extensions:
            target_files.extend(self.repo_path_obj.rglob(f"*{ext}"))

        if not target_files:
            logger.warning(f"未找到匹配的文件，扩展名: {self.repo_extensions}")
            return {}, set()

        interrupt_table = {}
        scheduler_regular = []

        for file in target_files:
            content = self._read_file_content(file)
            if not interrupt_table:
                interrupt_table = self.parse_interrupt_table_details(content, str(file))

            if not scheduler_regular:
                scheduler_regular = self.get_scheduler_regular_task(content)
            if interrupt_table and scheduler_regular:
                break

        logger.info("获取中断表和调度任务信息完成")
        interrupt_table_dict = self.get_interrupt_table_details(interrupt_table)

        return interrupt_table_dict, set(scheduler_regular)

    def get_interrupt_table_details(self, interrupt_table: dict) -> Dict[str, int]:
        """
        获取中断表的详细信息。

        该函数根据给定的中断表信息，处理并返回一个字典，
        其中包含中断处理程序与其对应的常量值。

        参数:
        - interrupt_table: 中断表信息，包含中断表的条目和文件路径。

        返回:
        - interrupt_table_dict: 一个字典，键为中断处理程序，值为对应的常量值。
        """
        logger.info("get_interrupt_table_details start")

        # 检查中断表是否存在
        if not interrupt_table:
            raise ValueError("Interrupt table details not found.")

        # 获取中断表中的所有条目
        entries = interrupt_table["entries"]
        logger.debug(f"Processing {len(entries)} interrupt entries.")

        # 预处理：收集所有唯一的irq_num
        unique_irq_nums = set()
        for entry in entries:
            irq_num = entry.get("irq_num")
            if irq_num:
                unique_irq_nums.add(irq_num)

        # 批量获取常量值 - 使用单线程避免文件冲突
        file_path = interrupt_table["file_path"]
        irq_num_dict = self._batch_get_constant_values(file_path, unique_irq_nums)

        # 快速构建最终字典
        interrupt_table_dict = {}
        for entry in entries:
            handler = entry.get("handler")
            irq_num = entry.get("irq_num")

            # 根据条件更新最终字典
            if handler and irq_num and irq_num in irq_num_dict:
                interrupt_table_dict[handler] = irq_num_dict[irq_num]
            elif handler is None or irq_num is None:
                logger.warning(f"Skipping entry: missing 'handler' or 'irq_num'")

        logger.info("get_interrupt_table_details end")
        return interrupt_table_dict

    def _batch_get_constant_values(self, file_path: str, irq_nums: set) -> Dict[str, str]:
        """
        批量获取常量值，避免重复文件读取。

        此函数的目的是减少在获取多个中断号（irq_nums）的常量值时对文件的重复读取。
        通过使用ConstFinder工具，可以在一次文件读取中获取所有需要的常量值，从而提高效率。

        参数:
        - file_path: 包含常量定义的文件路径。
        - irq_nums: 一个包含多个中断号的列表或迭代器。

        返回:
        - irq_num_dict: 一个字典，键为中断号，值为对应的常量值（以十六进制字符串形式）。
        """
        # 初始化一个空字典，用于存储中断号和对应的常量值
        irq_num_dict = {}

        # 创建一个ConstFinder实例，复用文件读取
        finder = ConstFinder(self.repo_path)

        # 遍历中断号列表，获取每个中断号的常量值
        for irq_num in irq_nums:
            try:
                # 使用ConstFinder实例查找常量值
                value = finder.find_constant(file_path, irq_num)
                # 如果找到的值不为None，则将其转换为十六进制字符串并存入字典
                if value is not None:
                    irq_num_dict[irq_num] = hex(value)
            except Exception as e:
                # 如果在查找常量值过程中发生异常，记录错误日志
                logger.error(f"Failed to get constant value for irq_num={irq_num}: {e}")

        # 返回包含中断号和常量值的字典
        return irq_num_dict

    def parse_interrupt_table_details(self, content: str, file: str) -> dict:
        """
        解析中断向量表的详细信息。

        本函数通过正则表达式搜索给定内容中的中断向量表信息，并解析这些信息成一个结构化的字典。
        如果没有找到匹配的表或者必要的信息不完整，则返回None。

        参数:
        - content: 字符串，代表要解析的内容。
        - file: 字符串，代表解析内容所属的文件路径。

        返回:
        - 字典，包含中断向量表的类型、名称、大小、条目列表和文件路径；如果没有找到表或信息不完整，则返回None。
        """
        # 搜索内容以找到中断向量表
        table_match = self.table_pattern.search(content)
        if not table_match:
            return {}

        # 获取中断向量表的完整定义
        full_definition = table_match.group(0)
        entries = []

        # 遍历中断向量表中的每个条目
        for entry_match in self.entry_pattern.finditer(full_definition):
            handler, priority, irq_num, irq_level, comment = entry_match.groups()

            # 跳过不完整的条目或指定为NULL的处理程序
            if None in (handler, priority, irq_num, irq_level) or handler.strip() == 'NULL':
                continue

            # 将条目信息添加到列表中
            entries.append({
                'handler': handler.strip(),
                'priority': priority.strip(),
                'irq_num': irq_num.strip(),
                'irq_level': irq_level.strip(),
                'comment': comment.strip() if comment else None
            })

        # 返回结构化的中断向量表信息
        return {
            'type': table_match.group(1),
            'name': table_match.group(2),
            'size': table_match.group(3),
            'entries': entries,
            'file_path': file
        }

    def get_scheduler_regular_task_detail(self) -> Dict[str, str]:
        """
        从指定的代码内容中提取调度器相关的任务函数以及对应任务。

        参数:
        - content (str): C文件内容

        返回:
        - dict: 包含调度器任务函数名的列表。
        """
        # 查找目标文件
        target_files = []
        for ext in self.repo_extensions:
            target_files.extend(self.repo_path_obj.rglob(f"*{ext}"))

        for file in target_files:
            content = self._read_file_content(file)
            # 尝试从当前文件中提取调度器任务
            scheduler_tasks = self.extract_scheduler_tasks(content)
            # 如果找到了调度器任务，则提取函数名并结束循环
            if scheduler_tasks:
                return self.extract_regular_task_detail(scheduler_tasks)
        # 返回所有找到的调度器任务函数名
        return {}

    def extract_regular_task_detail(self, code_text: str) -> Dict[str, str]:
        """
        精确提取C代码中的函数名，排除宏定义等干扰

        参数:
            code_text: 包含C代码的字符串
        返回:
            去重后的纯函数名列表
        """
        # 匹配两种主要函数名格式:
        # 1. &function_name
        # 2. MACRO_WRAPPER(function_name)

        # 使用正则表达式查找所有匹配的函数名
        matches = re.findall(self.scheduler_tasks_func_detail, code_text, re.VERBOSE)
        result = {}
        for match in matches:
            # 获取函数名（两种格式中的任意一种会匹配成功）
            func_name = match[0] or match[1]
            task_bit = match[2]
            result[func_name] = task_bit
        return result

    def get_scheduler_regular_task(self, content: str) -> List[str]:
        """
        从指定的代码内容中提取调度器相关的任务函数名。

        参数:
        - content (str): C文件内容

        返回:
        - list: 包含调度器任务函数名的列表。
        """
        # 尝试从当前文件中提取调度器任务
        scheduler_tasks = self.extract_scheduler_tasks(content)
        # 如果找到了调度器任务，则提取函数名并结束循环
        if scheduler_tasks:
            return self.extract_function_names(scheduler_tasks)
        # 返回所有找到的调度器任务函数名
        return []

    def extract_function_names(self, code_text: str) -> List[str]:
        """
        精确提取C代码中的函数名，排除宏定义等干扰

        参数:
            code_text: 包含C代码的字符串
        返回:
            去重后的纯函数名列表
        """
        # 匹配两种主要函数名格式:
        # 1. &function_name
        # 2. MACRO_WRAPPER(function_name)

        # 使用正则表达式查找所有匹配的函数名
        matches = re.findall(self.scheduler_tasks_func_pattern, code_text, re.VERBOSE)

        # 合并匹配结果并去重
        function_names = []
        seen = set()
        for group in matches:
            for name in group:
                if name and name not in seen:
                    seen.add(name)
                    function_names.append(name)

        # 返回去重后的函数名列表
        return function_names

    def extract_scheduler_tasks(self, content: str):
        """
        从C文件中提取const ST_SCHDLR_RGLR st_gp_SCHDLR_RGLR_TASK[]定义的内容

        参数:
            content (str): C文件内容
        返回:
            str: 匹配到的括号内的内容，如果没有匹配到则返回None
        """
        # 正则表达式匹配模式

        # 执行匹配
        match = re.search(self.scheduler_tasks_pattern, content, re.DOTALL)

        # 如果找到匹配的内容，则返回括号内的部分
        if match:
            return match.group(1).strip()
        # 如果没有找到匹配的内容，则返回None
        return None


class CodeAnalyzer:
    """
    C语言代码分析工具类
    提供在C源代码中查找函数、全局变量、定位特定行所属函数等功能
    """

    def __init__(self, file_suffix=(".c", ".h", ".prm")):
        """
        初始化C代码分析器
        """
        self.function_range_pattern = re.compile(
            r'(?:^|\n)\s*(?:[\w*]+\s+)+(?!if|while|for|switch|catch)(\w+)\s*\([^)]*\)\s*\{',
            re.MULTILINE
        )

        # 全局变量相关正则表达式
        self.s_var_pattern = re.compile(r'^[\w\s()]*_s\w+$')
        self.static_var_pattern = re.compile(
            r'^\s*static\s+'  # static关键字
            r'(?:(const|volatile|register)\s+)?'  # 可选修饰符
            r'((?:struct|enum|union)\s+\w+|[a-zA-Z_][\w\s*]*)'  # 类型
            r'\s+'
            r'([\w\s()]*_s\w+)'  # 变量名
            r'(\s*\[[^]]*])?'  # 可选数组维度
            r'\s*(?:=\s*([^;]+))?\s*;'  # 可选初始化值
            r'(\s*//.*)?'  # 可选注释
        )
        self.file_suffix = file_suffix

    # 函数查找相关方法
    def find_function_by_line(self, c_file_path: str, line_number: int) -> str:
        """
        查找指定行号所属的函数名

        参数:
            c_file_path: C源文件路径
            line_number: 要查找的行号(1-based)

        返回:
            包含该行的函数名，若不在函数内则返回空字符串
        """
        # 读取文件内容
        content = self._read_file_content(Path(c_file_path))

        # 提取所有函数的范围信息
        functions = self._extract_function_ranges(content)

        # 遍历函数列表，查找包含指定行号的函数
        for item in functions:
            if item["start_line"] <= line_number <= item["end_line"]:
                return item["name"]
        return ""

    def find_all_functions(self, root_path: str, target_snippet: str) -> List[Dict[str, Any]]:
        """
        在指定路径下并行查找包含目标代码片段的所有函数

        参数:
            root_path: 根目录路径
            target_snippet: 要查找的目标代码片段

        返回:
            包含匹配函数信息的列表
        """
        return self.use_thread(root_path, target_snippet, self._find_functions_optimized)

    def use_thread(self, root_path: str, target_snippet, execut_func):
        """
        使用多线程在指定目录中查找包含目标代码片段的文件

        参数:
            root_path (str): 要搜索的根目录路径
            target_snippet: 要搜索的条件
            execut_func: 执行文件处理的函数，接收文件路径和目标片段作为参数

        返回:
            list: 包含所有找到的结果列表
        """
        root_path_obj = Path(root_path)
        # 收集所有符合条件的文件
        target_files = [
            file for file in root_path_obj.rglob("*")
            if file.suffix.lower() in [suffix.lower() for suffix in self.file_suffix]
        ]

        result = []
        # 动态设置线程池大小
        max_workers = min(32, (os.cpu_count() or 1) + 4)

        # 使用线程池并行处理文件
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(execut_func, file, target_snippet): file
                for file in target_files
            }
            for future in as_completed(future_to_file):
                file = future_to_file[future]
                try:
                    result.extend(future.result())
                except Exception as e:
                    logger.warning(f"处理文件 {file} 时出错: {e}")
        return result

    def _find_functions_optimized(self, file: Path, target_snippet: str) -> List[Dict[str, Any]]:
        """
        优化版函数匹配器 - 返回函数名和匹配行内容

        参数:
            file: 文件路径
            target_snippet: 目标代码片段

        返回:
            匹配结果列表
        """
        try:
            content = self._read_file_content(file)
            if not content or target_snippet not in content:
                return []

            lines = content.split('\n')
            functions = self._extract_function_ranges(content)

            return self._find_matches_in_functions(functions, lines, target_snippet, str(file))

        except Exception as e:
            logger.warning(f"处理文件 {file} 时出错: {e}")
            return []

    def _extract_function_ranges(self, content: str) -> List[Dict[str, Any]]:
        """
        提取所有函数的名称和范围

        参数:
            content: 文件内容

        返回:
            函数信息列表
        """
        functions = []
        for match in self.function_range_pattern.finditer(content):
            func_name = match.group(1)
            start_pos = match.start()
            end_pos = self._find_function_end(content, match.end() - 1)

            if end_pos:
                functions.append({
                    'name': func_name,
                    'start_line': content[:start_pos].count('\n'),
                    'end_line': content[:end_pos].count('\n'),
                    'content': content[start_pos:end_pos + 1]
                })

        return functions

    @staticmethod
    def _find_matches_in_functions(functions: List[Dict[str, Any]], lines: List[str],
                                   target_snippet: str, file_path: str) -> List[Dict[str, Any]]:
        """
        在函数中查找匹配的行

        参数:
            functions: 函数列表
            lines: 代码行列表
            target_snippet: 目标代码片段
            file_path: 文件路径

        返回:
            匹配结果列表
        """
        results = []

        for func in functions:
            if target_snippet not in func['content']:
                continue

            # 在函数范围内查找匹配行
            for line_idx in range(func['start_line'], func['end_line'] + 1):
                if line_idx < len(lines) and target_snippet in lines[line_idx]:
                    results.append({
                        'function_name': func['name'],
                        'line_number': line_idx + 1,
                        'line_content': lines[line_idx].strip(),
                        'file_path': file_path
                    })

        return results

    # 全局变量查找相关方法
    def search_global_vars_name(self, full_code: List[str]) -> List[str]:
        """
        从完整代码中搜索全局变量名称

        参数:
            full_code: 代码行列表

        返回:
            全局变量名称列表
        """
        logger.info("search_global_vars_name start")
        global_vars = []
        for idx, line in enumerate(full_code):
            if not line.strip() or line.strip().startswith('//'):
                continue

            match = self.static_var_pattern.match(line)
            if match:
                var_name = match.group(3).strip()  # 修复：应使用group(3)而不是group(2)
                if not self.s_var_pattern.match(var_name):
                    continue
                global_vars.append(var_name)

        return global_vars

    def search_global_vars(self, full_code: List[str]) -> List[Dict[str, Any]]:
        """
        从完整代码中搜索全局变量信息

        参数:
            full_code: 代码行列表

        返回:
            包含完整变量信息的字典列表
        """
        logger.info("search_global_vars start")
        global_vars = []

        for idx, line in enumerate(full_code):
            line_stripped = line.strip()
            if not line_stripped or line_stripped.startswith('//'):
                continue

            match = self.static_var_pattern.match(line)
            if match:
                var_info = self._extract_variable_info(match, idx, line)
                if var_info and self.s_var_pattern.match(var_info['name']):
                    global_vars.append(var_info)
                    logger.debug(f"找到全局变量: {var_info['name']}, 类型: {var_info['type']}")

        logger.info(f"search_global_vars 完成，共找到 {len(global_vars)} 个全局变量")
        return global_vars

    @staticmethod
    def _extract_variable_info(match: re.Match, line_index: int, line_content: str) -> Optional[Dict[str, Any]]:
        """
        从匹配结果中提取变量信息

        参数:
            match: 正则表达式匹配结果
            line_index: 行号索引
            line_content: 行内容

        返回:
            变量信息字典或None
        """
        try:
            modifier = match.group(1) or ""  # 修饰符
            var_type = match.group(2).strip()  # 变量类型
            var_name = match.group(3).strip()  # 变量名
            array_dim = match.group(4) or ""  # 数组维度
            init_value = match.group(5).strip() if match.group(5) else ""  # 初始化值
            comment = match.group(6).strip() if match.group(6) else ""  # 注释

            # 构建完整类型
            full_type = var_type
            if modifier:
                full_type = f"{modifier} {full_type}"
            if array_dim:
                full_type += array_dim

            # 清理注释（移除//前缀）
            if comment.startswith('//'):
                comment = comment[2:].strip()

            # 构建变量信息字典
            return {
                'name': var_name,
                'type': full_type,
                'base_type': var_type,
                'modifier': modifier,
                'array_dimension': array_dim.strip() if array_dim else "",
                'initial_value': init_value,
                'comment': comment,
                'line_number': line_index + 1,
                'line_content': line_content.strip(),
                'storage_class': 'static'
            }
        except Exception as e:
            logger.warning(f"提取变量信息时出错: {e}")
            return None

    # 通用辅助方法
    @staticmethod
    def _read_file_content(file: Path) -> str:
        """
        读取文件内容，优先使用内存映射

        参数:
            file: 文件路径

        返回:
            文件内容字符串
        """
        try:
            with open(file, 'r', encoding='utf-8', errors='replace') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    return mmapped_file.read().decode('utf-8', errors='replace')
        except (OSError, ValueError):
            with open(file, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()

    @staticmethod
    def _find_function_end(content: str, start_pos: int) -> Optional[int]:
        """
        快速找到函数结束位置

        参数:
            content: 文件内容
            start_pos: 起始位置

        返回:
            函数结束位置索引，未找到返回None
        """
        brace_count = 0
        for pos in range(start_pos, len(content)):
            char = content[pos]
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return pos
        return None

    def get_content_from_code(self, pattern_data, code_path):
        """
        从指定的代码文件中提取包含特定模式的内容。

        参数:
        pattern_data: 包含待搜索模式的列表。
        code_path: 代码文件的根路径。
        file_suffix: 文件类型。

        返回:
        一个字典，包含每个文件中匹配的函数信息。
        """
        # 初始化一个空字典来存储匹配到的数据
        match_data = {}
        match_file = {}

        # 将多个模式合并为一个正则表达式，提高效率
        combined_pattern = re.compile("|".join(re.escape(p) for p in pattern_data))

        find_result = self.use_thread(code_path, combined_pattern, self._find_combined_pattern)
        for item in find_result:
            match_data[item["file_name"]] = item["match_content"]
            match_file[item["file_name"]] = item["file_content"]

        # 返回匹配到的数据
        return match_data, match_file

    def _find_combined_pattern(self, file: Path, combined_pattern) -> List[Dict[str, Any]]:
        """
        优化版函数匹配器 - 返回函数名和匹配行内容

        参数:
            file: 文件路径
            combined_pattern: 目标代码片段

        返回:
            匹配结果列表
        """
        try:
            content = self._read_file_content(file)
            lines = content.split('\n')
            file_name = file.name
            result = []
            match_data = {
                "file_name": file_name,
                "file_content": content,
                "match_content": []
            }
            for line_num, line in enumerate(lines, 1):
                # 在当前行中查找匹配的模式
                line_content = line.strip()
                # 检查行是否以 /* 或 * 开头表示注释
                if line_content.startswith("/*") or line_content.startswith("*"):
                    continue  # 跳过注释行
                match = combined_pattern.search(line)
                if match:
                    # 获取匹配到的函数名
                    matched_pattern = match.group(0)
                    # 将匹配到的信息添加到结果字典中
                    match_data["match_content"].append({
                        "matched_pattern": matched_pattern,
                        "line_num": line_num,
                        "file_path": str(file),
                        "line": line_content
                    })
            if match_data["match_content"]:
                result.append(match_data)
            return result
        except Exception as e:
            logger.warning(f"处理文件 {file} 时出错: {e}")
            return []
