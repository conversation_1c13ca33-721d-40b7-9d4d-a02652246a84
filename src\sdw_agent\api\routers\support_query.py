#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V字对应：
4.2 结合检查
58. 負荷確認（机上、実施）＆結果検証

@File    : support_query.py
@Time    : 2025/7/16 15:21
<AUTHOR> <PERSON><PERSON>
@Version : 1.0
@Desc    : workflow58的接口，
           Auto Action 模块并非是V字流58号样例的实现，而是根据需求更改的辅助查询模块。
           
           提供基于用户输入的智能文档查询和建议生成功能。
           该模块能够根据用户的查询内容，自动匹配企业内部知识库，结合大模型理解能力，
           生成结构化的MarkDown格式操作指引，并返回相关文件路径供人工核对。
"""
from fastapi import APIRouter, HTTPException

from pydantic import BaseModel
from typing import Optional, List

from sdw_agent.service.auto_action.extract_info import get_suggestion_from_files
from sdw_agent.service.auto_action.workflow import main

router = APIRouter(prefix="/api/sdw/dev58", tags=["4.2 結合檢查","5 负荷確認的自动化查询"])

class LLMWithFilesRequest(BaseModel):
    user_input: str

class LLMWithFilesResponse(BaseModel):
    code: int = 0
    msg: str = ""
    md_path: Optional[str] = None
    file_paths: Optional[List[str]] = None

@router.post("/run_llm_with_files",
             summary="根据用户询问生成大模型输出和相关文件路径",
             description="输入用户询问，返回大模型输出的markdown文件路径和相关文件路径",
             response_model=LLMWithFilesResponse)
async def run_llm_with_files_api(request: LLMWithFilesRequest):
    try:
        if not request.user_input:
            raise HTTPException(status_code=400, detail="用户输入不能为空")
        result = await main(request.user_input)
        file_paths = result.data.file_paths
        md_path = result.data.md_path
        print(file_paths)
        return LLMWithFilesResponse(
            code=0,
            msg="大模型输出和相关文件路径生成成功",
            md_path=md_path,
            file_paths=file_paths
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

