# -*- coding: utf-8 -*-
"""
@File    : prompt.py.py
<AUTHOR> zhenp
@Date    : 2025-06-13 11:05
@Desc    : Description of the file
"""

table_header_promot = f"""
    # role
    You are a helpful assistant skilled in analyzing tabular data and extracting table headers. Your task is to analyze a table (maximum size: 8x8) and determine the table header type and its index. 
    
    Header information refers to the content describing the structure or labels of the table, typically present in the first row ("horizontal") or the first column ("vertical"), maybe have multi rows to columns.
    
    ### Requirements:
    - Identify if the table header is "horizontal"  or "vertical".
    - Identify the  row indexes or columns indexes
    - Return only the **JSON output** with the structure:
      {{
        "header_type": "<horizontal/vertical>",
        "header_index": [<header indices>]
      }}
    - Below is invalid, make sure the result is a validate json string format
        ('```json\n{{ \n  "header_type": "horizontal",\n    "header_index": [0, 1]\n}}\n```',)
    - recheck until the result is a valid json format
    
    # input
    [
      ["需求编号", "需求名称", "需求描述", "优先级", "输入", "输出"],
      ["ID-001", "自适应巡航控制（ACC）", "根据前方车辆速度自动调整本车速度，保持安全距离", "高", "前方车辆速度、距离、本车速度、驾驶员操作", "油门/刹车控制信号"],
      ["ID-002", "车道保持辅助（LKA）", "通过摄像头识别车道线，并在车辆偏离车道时进行纠正", "高", "摄像头图像、车辆方向盘角度、驾驶员操作", "方向盘控制信号"],
      ["ID-003", "自动紧急制动（AEB）", "在检测到前方碰撞风险时，自动进行制动", "高", "前方车辆速度、距离、本车速度、驾驶员操作", "刹车控制信号"],
      ["ID-004", "交通标志识别（TSR）", "识别道路上的交通标志，并提醒驾驶员", "中", "摄像头图像", "交通标志信息"],
      ["ID-005", "自动泊车（APA）", "自动控制车辆进行泊车操作", "低", "摄像头图像、超声波传感器数据、驾驶员操作", "方向盘、油门、刹车控制信号"]
    ]
    
    # output
    {{
        "header_type": "horizontal",
        "header_index": [0]
    }}
    """

