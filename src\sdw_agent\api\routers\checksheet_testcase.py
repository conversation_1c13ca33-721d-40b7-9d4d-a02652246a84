"""
CheckSheet测试用例API

V字对应：
2.3 结合检查仕样
24. CheckList IV 确认&结果检证

模块简介和主要功能说明：
提供CheckSheet测试用例对齐工作流的REST API接口，支持从测试用例中匹配CheckSheet内容并自动填写。
使用向量检索技术实现智能化的测试用例匹配和状态判断。

主要功能：
1. 从CheckSheet中提取测试用例信息
2. 使用Azure OpenAI嵌入模型进行语义相似度匹配
3. 自动判断测试用例状态（OK/NG/O）
4. 生成包含填写结果的Excel文件
5. 支持多个工作表的批量处理

@File    : cs4_24.py
@Time    : 2025/7/16 11:22
<AUTHOR> Shiwei Wu
@Version : 1.0
@Desc    : workflow24的接口，传入checksheet和testcase文件，最终输出一个基于checksheet填写的新文件
"""
from fastapi import APIRouter, HTTPException

from pydantic import BaseModel
from sdw_agent.service.checksheet_validation.workflow import main
from sdw_agent.model.request_model import ChangePointImportRequest
from fastapi.responses import FileResponse
from typing import Optional
import os
import shutil

router = APIRouter(prefix="/api/sdw/cs24", tags=["2.3 結合檢查仕樣", "2. checksheet IV 確認&結果檢證"])

UPLOAD_DIR = os.path.join(os.path.expanduser("~"), ".sdw", "data")
os.makedirs(UPLOAD_DIR, exist_ok=True)

class ChecksheetRequest(BaseModel):
    checksheet_path: str
    target_filename: list[str]


class ChecksheetResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: Optional[str] = None

@router.post("/run_checksheet",
             summary="Checksheet对齐",
             description="对指定checksheet和目标embedding文件进行检索和写入，返回生成的Excel文件路径",
             response_description="返回生成的Excel文件路径",
             response_model=ChecksheetResponse)
async def run_checksheet_api(request: ChecksheetRequest):
    try:
        if not request.checksheet_path or not request.target_filename:
            raise HTTPException(status_code=400, detail="参数不能为空")
        
        base, ext = os.path.splitext(request.checksheet_path)
        output_path = base + '_output.xlsx'
        
        result = await main(
        #output_path = await run_checksheet_with_target(
            request.checksheet_path,
            request.target_filename[0],
            #embeddings_cache_dir=r"D:/test_file/lightrag 3/lightrag/embeddings_cache"
            embeddings_cache_dir=r"D:/test_file/test_embedding",
            output_path = output_path
        )
        print(result)
        return ChecksheetResponse(
            code=200,
            msg="Checksheet对齐成功",
            data=result.data.get("output_path")
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Checksheet对齐失败: {str(e)}")
