# Bug提交工作流配置

# 基本配置
name: "tele_table_filler"
description: "Excel表格填充工作流，使用RAG从规格书提取数据填充特定列"
version: "1.0.0"
author: "Lingyun_Qin"

# 输入输出配置
io:
  # 输入文件格式
  input:
    excel_extensions: [".xlsx", ".xls"]  # 测试用例文件支持的格式
    source_dir: "./source"  # RAG源文件默认目录（可通过输入覆盖）

  # 输出文件配置
  output:
    default_output_dir: "./output"  # 默认输出目录
    output_filename: "filled_results.xlsx" # 输出文件名
    debug_dir: "./output/debug"  # 调试报告目录
    error_dir: "./output/error"  # 错误文件目录

# 处理参数
processing:
  # RAG配置
  rag:
    chunk_line: 10  # RAG分块行数
    max_retries: 2  # RAG查询重试次数
    batch_size: 50  # 批量处理大小
    cache_enabled: true  # 是否启用缓存
    fuzzy_match: true  # 是否启用模糊匹配
    truncate_dash_count: 3  # 截断'-'次数
    match_strategy: "truncate"  # 匹配策略
    cache_base_dir: "D:\\kb\\rag_cache"

  # 调试配置
  debug:
    enabled: true  # 是否启用调试模式

# LLM/RAG配置（如果涉及LLM扩展，可在此配置；当前为RAG专用）
llm:
  model: "gpt4o"  # 如果未来集成LLM，可指定模型（当前未用）
  system_prompt: |
    你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} - {level} - {message}"