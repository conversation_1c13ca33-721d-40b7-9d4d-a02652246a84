# DocParser - Document Parsing Tool

A comprehensive document parsing tool that extracts structured data from Word and Excel documents. The tool parses documents into text, table, picture, and graphic objects, preserving all information and formatting.

## Features

- **Complete Document Parsing**: Extracts 100% of document information without loss
- **Multiple Document Types**: Supports Word (.doc, .docx) and Excel (.xls, .xlsx) documents
- **Object-Based Parsing**: Parses documents into text, table, picture, and graphic objects
- **Plugin Architecture**: Extensible through plugins for custom processing
- **Rule Engine**: Transform document objects based on configurable rules
- **JSON Rule Configuration**: Define rules in JSON for easy configuration
- **Clear Interface Contracts**: Well-defined interfaces for all components
- **Reusable Components**: Modular design for maximum reusability
- **Loose Coupling**: Components are decoupled for flexible deployment
- **Comprehensive Validation**: Built-in validation for parsed data
- **Centralized Error Handling**: Robust error handling with detailed error reporting

## Architecture

The document parser is built with a clean, modular architecture:

```
docparser/
├── core/               # Core components
│   ├── base_parser.py  # Base parser class
│   ├── parser_factory.py # Factory for creating parsers
│   ├── plugin_manager.py # Plugin management
│   ├── document_manager.py # Main document manager
│   └── rule_engine.py  # Rule engine for document transformation
├── interfaces/         # Interface definitions
│   ├── document_interface.py # Document parsing interface
│   ├── plugin_interface.py # Plugin interface
│   └── rule_interface.py # Rule interface
├── models/             # Data models
│   └── document_objects.py # Document object models
├── parsers/            # Document type parsers
│   ├── word_parser.py  # Word document parser
│   └── excel_parser.py # Excel document parser
├── plugins/            # Plugin implementations
│   └── text_analyzer_plugin.py # Sample text analyzer plugin
├── rules/              # Rule implementations
│   ├── dynamic_rule.py # Dynamic rule implementation
│   ├── example_rules.py # Example rule implementations
│   └── example_rules.json # Example JSON rule definitions
├── utils/              # Utility functions
│   ├── logging_utils.py # Logging utilities
│   └── error_handler.py # Centralized error handling
├── __init__.py         # Package initialization
├── main.py             # Command-line interface
├── requirements.txt    # Dependencies
└── setup.py            # Package setup script
```

## Installation

1. Clone the repository:
   ```
   git clone https://************/astri/RI-SDW-III/ArcMind/Agents/docparser.git
   cd docparser
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Command Line Interface

Parse a document and save the output to a JSON file:

```
python -m docparser.main document.docx -o output.json
```

Show document statistics:

```
python -m docparser.main document.xlsx --stats
```

Extract only specific object types:

```
python -m docparser.main document.docx --text-only
python -m docparser.main document.xlsx --tables-only
```

Use plugins:

```
python -m docparser.main document.docx -p /path/to/plugins
```

Use rules:

```
python -m docparser.main document.docx -r /path/to/rules
```

List available rules:

```
python -m docparser.main --list-rules
```

Enable or disable specific rules:

```
python -m docparser.main document.docx --enable-rule header_rule --disable-rule table_rule
```

Export rules to JSON:

```
python -m docparser.main --export-rules rules.json
```

Enable verbose output for debugging:

```
python -m docparser.main document.docx -v
```

Output response as JSON:

```
python -m docparser.main document.docx --json-response
```

Save logs to a file:

```
python -m docparser.main document.docx --log-file logs/parser.log
```

### Python API

```python
from docparser import DocumentManager
from docparser.parsers.word_parser import WordParser
from docparser.parsers.excel.excel_parser import ExcelParser
from docparser.core.parser_factory import ParserFactory

# Register parsers
ParserFactory.register_parser(WordParser)
ParserFactory.register_parser(ExcelParser)

# Create document manager with plugins and rules
doc_manager = DocumentManager(
   plugins_dir="plugins",
   rules_dir="rules"
)

# Parse document
document_data = doc_manager.parse_document('document.docx')

# Get specific object types (with rules applied)
text_objects = doc_manager.get_text_objects()
table_objects = doc_manager.get_table_objects()
picture_objects = doc_manager.get_picture_objects()
graphic_objects = doc_manager.get_graphic_objects()

# Get document statistics
stats = doc_manager.get_document_statistics()
print(f"Document has {stats['text_count']} text objects and {stats['table_count']} tables")

# Export to JSON
doc_manager.export_to_json('output.json')
```

## Creating Plugins

Create a new plugin by implementing the `PluginInterface` or one of its specialized subclasses:

```python
from docparser.interfaces.plugin_interface import TextProcessorPlugin

class MyTextPlugin(TextProcessorPlugin):
    def get_plugin_name(self) -> str:
        return "MyTextPlugin"
    
    def get_plugin_version(self) -> str:
        return "1.0.0"
    
    def get_supported_document_types(self) -> list:
        return ["docx", "xlsx"]
    
    def process_document(self, document_data):
        # Process document data
        return document_data
    
    def process_text_objects(self, text_objects):
        # Process text objects
        return text_objects
    
    def validate_document(self, document_data):
        # Validate document data
        return True
```

## Using Rules

DocParser includes a powerful rule engine that allows you to transform document objects based on conditions. Rules can be defined in Python code or in JSON configuration files.

### Command Line Interface for Rules

Use rules from a directory:

```
python -m docparser.main document.docx -r /path/to/rules
```

List available rules:

```
python -m docparser.main --list-rules
```

Enable or disable specific rules:

```
python -m docparser.main document.docx --enable-rule header_text_rule --disable-rule table_caption_rule
```

Export rules to a JSON file:

```
python -m docparser.main --export-rules rules.json
```

### Creating Rules in Python

Create a new rule by implementing the `Rule` interface or one of its specialized subclasses:

```python
from docparser.interfaces.rule_interface import TextRule

class MyHeaderRule(TextRule):
    def get_rule_id(self) -> str:
        return "my_header_rule"
    
    def get_rule_name(self) -> str:
        return "My Header Rule"
    
    def get_rule_description(self) -> str:
        return "Identifies headers in text objects"
    
    def get_rule_priority(self) -> int:
        return 100  # Higher priority rules are applied first
    
    def get_rule_condition(self):
        def condition(text_obj):
            # Return True if the rule should be applied
            return 'font_size' in text_obj and text_obj['font_size'] >= 14
        return condition
    
    def apply_rule(self, text_obj):
        # Transform the object
        result = text_obj.copy()
        result['is_header'] = True
        return result
    
    def is_enabled(self) -> bool:
        return True
    
    def enable(self) -> None:
        pass
    
    def disable(self) -> None:
        pass
```

### Creating Rules in JSON

Rules can also be defined in JSON files for easier configuration:

```json
{
  "id": "json_header_rule",
  "name": "JSON Header Rule",
  "description": "Identifies headers in text objects",
  "priority": 95,
  "target": "text",
  "enabled": true,
  "condition": {
    "type": "field_equals",
    "field": "style",
    "value": "Heading1"
  },
  "transform": {
    "type": "set_field",
    "field": "is_header",
    "value": true
  }
}
```

### Python API for Rules

```python
from docparser import DocumentManager
from docparser.rules.dynamic_rule import DynamicTextRule

# Create document manager with rules directory
doc_manager = DocumentManager(rules_dir="rules")

# Register a rule programmatically
header_rule = DynamicTextRule(
    rule_id="header_rule",
    name="Header Rule",
    description="Identifies headers",
    priority=100
)

# Set condition and transform functions
header_rule.set_condition(lambda obj: obj.get('font_size', 0) >= 14)
header_rule.set_transform(lambda obj: {**obj, 'is_header': True})

# Register rule
doc_manager.register_rule(header_rule)

# Parse document with rules applied
document_data = doc_manager.parse_document('document.docx')

# Get text objects with rules applied
text_objects = doc_manager.get_text_objects()
```

## Error Handling

DocParser includes a comprehensive error handling system that provides:

- **Centralized Error Management**: All errors are handled through a unified system
- **Custom Exception Hierarchy**: Specific exception types for different error scenarios
- **Error Codes**: Numeric error codes for easy identification and handling
- **Detailed Error Messages**: Clear and informative error messages
- **Logging Integration**: Errors are automatically logged with appropriate severity levels
- **JSON Error Responses**: Structured error responses for API usage

### Error Types

| Error Code | Error Type | Description |
|------------|------------|-------------|
| 1000 | DocumentNotFoundError | Document file not found |
| 1001 | UnsupportedDocumentTypeError | Document type not supported |
| 1100 | ParsingError | Error parsing document content |
| 1200 | PluginError | Error in plugin processing |
| 1300 | ValidationError | Document validation failed |
| 1400 | ExportError | Error exporting document data |

### Error Handling in Code

```python
from docparser.utils.error_handler import handle_error, try_except_decorator

# Using the error handling decorator
@handle_error
def my_function():
    # Function code here
    pass

# Using specific error code decorator
@try_except_decorator(1100)  # ParsingError
def parse_content():
    # Parsing code here
    pass

# Using error raising function
from docparser.utils.error_handler import raise_error
raise_error(1000, "Custom error message")
```

## Dependencies

- python-docx: For parsing Word documents
- openpyxl: For parsing Excel documents
- Pillow: For image processing
- pywin32: For Windows COM integration (Windows only)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Package

```commandline

build_package.bat

```