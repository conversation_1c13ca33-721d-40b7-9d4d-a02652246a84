"""
Excel Operation Utilities
Excel操作工具类
"""
from typing import Dict, List, Tuple
from loguru import logger
import xlwings as xw

from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo


class ExcelOperationUtils:
    """Excel操作工具类"""

    @staticmethod
    def copy_data_between_workbooks(source_ws, target_ws, source_col=("B", "Z"), target_col=("B", "Z"), row_offset=0):
        """
        将源工作簿的指定 Sheet 的数据复制到目标工作簿的指定 Sheet。
        只复制没有删除线的行，其他行往上移动。
        """
        try:
            # 获取源 Sheet 的最后一个单元格的行号
            last_cell = source_ws.used_range.last_cell
            total_rows = last_cell.row
            logger.info(f"源 Sheet 总行数: {total_rows}")

            # 逐行检查并复制没有删除线的数据
            target_row = 5 + row_offset  # 目标起始行
            
            for source_row in range(5, total_rows + 1):
                # 检查当前行是否有删除线
                has_strikethrough = False
                
                # 检查源列范围内的单元格是否有删除线
                for col_ord in range(ord(source_col[0]), ord(source_col[1]) + 1):
                    col_letter = chr(col_ord)
                    cell = source_ws.range(f"{col_letter}{source_row}")
                    
                    # 检查字体是否有删除线
                    if hasattr(cell.api.Font, 'Strikethrough') and cell.api.Font.Strikethrough:
                        has_strikethrough = True
                        break
                
                # 如果没有删除线，复制这一行
                if not has_strikethrough:
                    # 读取源行数据
                    source_range = f'{source_col[0]}{source_row}:{source_col[1]}{source_row}'
                    source_data = source_ws.range(source_range).value
                    
                    # 写入目标行
                    target_range = f'{target_col[0]}{target_row}:{target_col[1]}{target_row}'
                    target_ws.range(target_range).value = source_data
                    
                    logger.debug(f"复制行 {source_row} 到目标行 {target_row}")
                    target_row += 1  # 目标行递增
                else:
                    logger.debug(f"跳过有删除线的行: {source_row}")

            logger.info(f"数据已成功从源工作簿复制到目标工作簿，共复制 {target_row - 5 - row_offset} 行")

        except Exception as e:
            logger.error(f"复制数据时发生错误: {e}")
            raise

    @staticmethod
    def add_warning_ids_to_function(wb, warning_change_info: WarningChangeInfo) -> None:
        """
        将警告ID添加到函数工作表
        
        Args:
            wb: 工作簿对象
            warning_change_info: 警告变更信息
        """
        try:
            # 获取工作表
            ws = wb.sheets["函数"]

            # 提取新增的警告ID
            warning_ids = []
            for key in warning_change_info.change_row_result.keys():
                try:
                    value = warning_change_info.change_row_result[key]
                    if value["比较结果"] != "新增":
                        continue
                    key_int = int(key)
                    warning_ids.append(key_int)
                except (ValueError, KeyError):
                    logger.warning(f"无法转换 key: {key} 为整数，跳过")
                    continue

            # 写入警告ID到工作表
            ExcelOperationUtils._write_warning_ids_to_sheet(
                ws, warning_ids, warning_change_info.change_row_result
            )

        except Exception as e:
            logger.error(f"添加警告ID到函数失败: {str(e)}")
            raise

    @staticmethod
    def _write_warning_ids_to_sheet(ws, warning_ids: List[int], change_row_result: Dict) -> None:
        """
        将警告ID写入工作表
        
        Args:
            ws: 工作表对象
            warning_ids: 警告ID列表
            change_row_result: 变更行结果
        """
        try:
            # 获取 A 列的所有值
            last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row
            a_column_values = ws.range(f"A1:A{last_row}").value

            # 遍历 WARNING_id 列表
            for key_int in warning_ids:
                warning_id = f"WARNING_{key_int:04d}"

                if warning_id not in a_column_values:
                    # 如果 WARNING_id 不在 A 列中，则添加到 A 列的最后
                    last_row += 1
                    ws.range(f"A{last_row}").value = warning_id
                    ws.range(f"B{last_row}").value = "请先填写对应函数/D1L1通知/电源状态等信息再运行本程序！"
                    ws.range(f"C{last_row}").value = "0"
                    ws.range(f"D{last_row}").value = "+B"
                    logger.info(f"添加 函数页面 WARNING_id: {warning_id} 到 A{last_row}")
                    ExcelOperationUtils.set_cell_background_color(ws, last_row, 1, end_row=last_row, end_col=4)

                # 处理双通道警告
                warning_id_b = f"{warning_id}_B"
                if (change_row_result[key_int]["dual_channel"] == "Y" and
                        warning_id_b not in a_column_values):
                    last_row += 1
                    ws.range(f"A{last_row}").value = warning_id_b
                    ws.range(f"B{last_row}").value = "请先填写对应函数/D1L1通知/电源状态等信息再运行本程序！"
                    ws.range(f"C{last_row}").value = "0"
                    ws.range(f"D{last_row}").value = "+B"
                    ExcelOperationUtils.set_cell_background_color(ws, last_row, 1, end_row=last_row, end_col=4)
                    logger.info(f"函数页面 添加双通道 WARNING_id: {warning_id_b} 到 A{last_row}")

            logger.info("警告ID函数处理完成！")

        except Exception as e:
            logger.error(f"写入警告ID到工作表失败: {str(e)}")
            raise

    @staticmethod
    def search_warning_id_in_interface_popup(popup_list, warning_change_info: WarningChangeInfo):
        """
        在接口弹窗文件中搜索警告ID并更新字典
        
        Args:
            popup_dict: 要更新的字典，格式为 {warning_id: None}
            warning_change_info: 警告变更信息
            
        Returns:
            dict: 更新后的字典，格式为 {warning_id: (No., Message No)}
        """
        try:
            popup_dict = {item: None for item in popup_list}
            if warning_change_info.meter_interface_popup_url == "" or not popup_dict:
                logger.warning(f"没有meter_interface_popup文件 {warning_change_info.meter_interface_popup_url} 或popup_dict为空")
                return popup_dict

            # 打开Excel文件
            app = xw.App(visible=False)
            app.display_alerts = False
            wb = app.books.open(warning_change_info.meter_interface_popup_url)
            
            try:
                # 获取WarningMessage_Interface页签
                ws = wb.sheets["WarningMessage_Interface"]
                
                # 读取第一行作为表头
                header_row = ws.range("A1").expand("right").value
                if not isinstance(header_row, list):
                    header_row = [header_row]
                
                # 找到No.和Message No列的索引
                no_col_index = None
                message_no_col_index = None
                
                for i, header in enumerate(header_row):
                    if header == "No.":
                        no_col_index = i + 1  # xlwings列索引从1开始
                    elif header == "Message No":
                        message_no_col_index = i + 1
                    elif header == "CONTDISP_B_No":
                        cont_no_col_index = i
                
                if no_col_index is None or message_no_col_index is None:
                    logger.error("未找到No.或Message No列")
                    return popup_dict
                
                # 获取数据范围
                last_row = ws.used_range.last_cell.row
                
                # 遍历每一行数据（从第2行开始，跳过表头）
                for row_num in range(2, last_row + 1):
                    # 读取整行数据
                    row_data = ws.range(f"A{row_num}:{chr(64 + len(header_row))}{row_num}").value
                    if not isinstance(row_data, list):
                        row_data = [row_data]
                    
                    # 检查这一行是否包含popup_dict中的任何key
                    cell_value = row_data[cont_no_col_index]
                    if cell_value is None:
                        continue

                    cell_str = str(cell_value).strip()
                    if not cell_str:
                        continue

                    # 解析单元格内容，可能是单个数字或多个数字用逗号/空格分隔
                    numbers = ExcelOperationUtils._parse_numbers_from_cell(cell_str)

                    # 检查是否有匹配的warning_id
                    for warning_id in popup_dict.keys():
                        if warning_id in numbers:
                            # 获取No.和Message No的值
                            no_value = row_data[no_col_index - 1] if no_col_index <= len(row_data) else None
                            message_no_value = row_data[message_no_col_index - 1] if message_no_col_index <= len(row_data) else None

                            # 更新popup_dict
                            popup_dict[warning_id] = (no_value, message_no_value)
                            logger.info(f"找到警告ID {warning_id}，No.: {no_value}, Message No: {message_no_value}")
                
                return popup_dict
                
            finally:
                wb.close()
                app.quit()

        except Exception as e:
            logger.error(f"搜索警告ID在接口弹窗失败: {str(e)}")
            return popup_dict

    @staticmethod
    def _parse_numbers_from_cell(cell_str: str) -> List[int]:
        """
        从单元格字符串中解析数字
        
        Args:
            cell_str: 单元格字符串内容
            
        Returns:
            List[int]: 解析出的数字列表
        """
        import re
        
        numbers = []
        try:
            # 使用正则表达式提取所有数字
            number_matches = re.findall(r'\d+', cell_str)
            numbers = [int(match) for match in number_matches]
        except (ValueError, TypeError):
            logger.warning(f"无法解析单元格内容: {cell_str}")
        
        return numbers

    @staticmethod
    def add_item_to_interface_popup(wb, warning_change_info: WarningChangeInfo) -> None:
        """
        添加项目到接口弹窗工作表
        
        Args:
            wb: 工作簿对象
            warning_change_info: 警告变更信息
        """
        try:
            # 获取工作表
            ws = wb.sheets["CONTDISP（MET）"]
            column = "A"

            # 获取指定列的所有值
            last_row = ws.range(f'{column}{ws.cells.last_cell.row}').end('up').row
            column_values = ws.range(f"{column}1:{column}{last_row}").value

            # 确保只包含新增的键
            filtered_keys = [
                key for key, value in warning_change_info.change_row_result.items()
                if value["比较结果"] == "新增"
            ]

            # 找出符合条件的值
            matching_values = []
            for cell_value in column_values:
                if isinstance(cell_value, (int, float)):
                    if int(cell_value) in filtered_keys:
                        matching_values.append(cell_value)

            popip_dict = ExcelOperationUtils.search_warning_id_in_interface_popup(matching_values, warning_change_info)

            # 写入到弹窗工作表
            popup_ws = wb.sheets["Interface_Popup"]
            # 标红
            ExcelOperationUtils._write_item_to_popup_sheet(popup_ws, popip_dict)

        except Exception as e:
            logger.error(f"添加项目到接口弹窗失败: {str(e)}")
            raise

    @staticmethod
    def _write_item_to_popup_sheet(ws, popip_dict: Dict[int, Tuple[str, str]], column: str = "B") -> None:
        """
        将项目写入弹窗工作表
        
        Args:
            ws: 工作表对象
            values_to_check: 要检查的值列表
            column: 目标列
        """
        try:
            # 获取指定列的所有值
            last_row = ws.range(f'{column}{ws.cells.last_cell.row}').end('up').row
            column_values = ws.range(f"{column}1:{column}{last_row}").value

            for value_to_check in popip_dict.keys():
                # 检查是否存在指定值
                if value_to_check in column_values:
                    logger.info(f"{value_to_check} 已存在于 interface_popup 页签的 {column} 列，跳过操作。")
                else:
                    # 如果不存在，则追加到列的最底下
                    ExcelOperationUtils.set_cell_background_color(ws, last_row + 1, ord(column) - ord('A') + 1, end_row=last_row + 1,
                                                                  end_col=ord(column) - ord('A') + 5)
                    ws.range(f"{column}{last_row + 1}").value = value_to_check
                    last_row = last_row + 1
                    logger.info(f"已将 {value_to_check} 添加到 interface_popup 页签的 {column} 列的第 {last_row} 行。")
                    value_to_check_key = int(value_to_check)
                    if popip_dict[value_to_check_key] is None:
                        continue

                    next_column = chr(ord(column) + 1)
                    ws.range(f"{next_column}{last_row}").value = popip_dict[value_to_check_key][0]
                    ws.range(f"{next_column + 1}{last_row}").value = popip_dict[value_to_check_key][1]
                    ws.range(f"{next_column + 2}{last_row}").value = "○"

        except Exception as e:
            logger.error(f"写入项目到弹窗工作表失败: {str(e)}")
            raise

    @staticmethod
    def update_warning_for_value(wb, sheet_name: str, target_value: int = 1276,
                                 target_column: str = "Q", update_value: str = "×") -> None:
        """
        更新工作表中1276的警告信息
        
        Args:
            wb: 工作簿对象
            sheet_name: 工作表名称
            target_value: 目标值
            target_column: 目标列
            update_value: 更新值
        """
        try:
            # 获取工作表
            ws = wb.sheets[sheet_name]

            # 获取 A 列的最后一个非空单元格的行号
            last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row

            # 遍历 A 列，查找目标值
            for row in range(1, last_row + 1):
                cell_value = ws.range(f"A{row}").value
                try:
                    if int(cell_value) == int(target_value):
                        # 更新对应行的目标列值
                        ws.range(f"{target_column}{row}").value = update_value
                        logger.debug(f"更新第 {row} 行的 {target_column} 列为 {update_value}")
                except (ValueError, TypeError):
                    continue

            logger.info("警告值更新完成！")

        except Exception as e:
            logger.error(f"更新警告值失败: {str(e)}")
            raise

    @staticmethod
    def set_cell_background_color(ws, start_row, start_col, end_row=None, end_col=None, color=(255, 0, 0)):
        """
        设置指定单元格或范围的背景颜色

        Args:
            ws: xlwings 工作表对象
            start_row: 起始行号（从 1 开始）
            start_col: 起始列号（从 1 开始）
            end_row: 结束行号（可选，默认为 start_row）
            end_col: 结束列号（可选，默认为 start_col）
            color: 背景颜色，RGB 格式的元组，例如 (255, 0, 0) 表示红色
        """
        try:
            # 如果结束行列未指定，则默认为单个单元格
            end_row = end_row or start_row
            end_col = end_col or start_col

            # 定义单元格范围
            cell_range = ws.range((start_row, start_col), (end_row, end_col))

            # 设置背景颜色
            cell_range.color = color
            logger.info(
                f"已将第 {start_row} 行，第 {start_col} 列到第 {end_row} 行，第 {end_col} 列的背景色设置为 {color}")
        except Exception as e:
            logger.error(f"设置背景颜色时发生错误: {e}")


