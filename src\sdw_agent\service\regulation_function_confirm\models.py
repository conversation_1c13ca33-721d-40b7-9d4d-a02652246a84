from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field

class RegulationConfigModel(BaseModel):
    """法规评估配置模型"""

    # 基础配置
    name: str = Field(default="法规评估")
    description: str = Field(default="分析软件变更内容与法规功能的关联性")
    version: str = Field(default="1.0.0")
    author: str = Field(default="SDW-Team")

    # 输入输出配置
    io: Dict[str, Any] = Field(default_factory=dict)

    # 处理参数
    processing: Dict[str, Any] = Field(default_factory=dict)

    # LLM配置
    llm: Dict[str, Any] = Field(default_factory=dict)

    # 嵌入配置
    embedding: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        extra = "allow"

class RegulationCategory(str, Enum):
    """法规分类枚举"""
    FULL_EVALUATION = "①法規機能の全評価"      # 法规功能全评估
    INDIRECT_IMPACT = "②変更点と法規機能の全評価"  # 变更点与法规功能全评估
    PRIORITY_ADJUSTMENT = "③優先度調停の組合せ評価"  # 优先度调解组合评估
    DISPLAY_CONDITION = "④法規機能の全表示条件全評価"  # 法规功能全显示条件全评估
    DISPLAY_INSPECTION = "⑤法規機能の画面検査"      # 法规功能画面检查
    NO_EVALUATION = "⑥追加評価不要"            # 无需追加评估

class RegulationImpact(str, Enum):
    """变更影响枚举"""
    YES = "有"   # 有变更影响
    NO = "無"    # 无变更影响

class RegulationInputData(BaseModel):
    """输入数据模型"""
    ReqDetail: str  # 要件内容
    ChgDetail: str  # 变更内容

    class Config:
        example = {
            "ReqDetail": "实现新的法规显示逻辑",
            "ChgDetail": "修改了法规显示条件判断算法"
        }

class ClassificationResult(BaseModel):
    """分类结果模型"""
    ClassifiedResult: RegulationCategory  # 分类结果
    is_changed: RegulationImpact         # 变更影响
    similarity_score: Optional[float] = None  # 可选字段
    relevant_regulation: Optional[str] = None # 可选字段

class ServiceResult(BaseModel):
    """服务返回结果"""
    success: bool
    modified_file: Optional[str] = None
    results: Optional[List[ClassificationResult]] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class AzureConfigModel(BaseModel):
    """Azure OpenAI服务配置"""
    openai_api_key: str = Field(..., description="Azure OpenAI API密钥")
    embedding_api_version: str = Field(..., description="嵌入向量API版本")
    openai_endpoint: str = Field(..., description="API端点")
    embedding_deployment: str = Field(..., description="嵌入向量模型部署名称")
    openai_deployment: str = Field(..., description="LLM模型部署名称")
    openai_api_version: str = Field(..., description="LLM API版本")

class IOConfigModel(BaseModel):
    """输入输出配置"""
    excel_extensions: List[str] = Field(default=[",xlsx", ".xls", "xlsm"], description="支持的Excel文件扩展名")
    kb_file_path: str = Field(..., description="法规知识库文件路径")

class LLMConfigModel(BaseModel):
    """LLM配置"""
    system_prompt: str = Field(..., description="系统提示词")
    temperature: float = Field(default=0.7, ge=0.0, le=1.0, description="温度参数")
    max_tokens: int = Field(default=1024, ge=1, le=4096, description="最大输出tokens")

class ProcessingConfigModel(BaseModel):
    """处理参数配置"""
    class EmbeddingConfig(BaseModel):
        """嵌入向量处理配置"""
        batch_size: int = Field(default=5, ge=1, le=100, description="嵌入向量生成批处理大小")

    class SimilarityConfig(BaseModel):
        """相似度计算配置"""
        threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="相似度阈值")

    embedding: EmbeddingConfig = Field(default=EmbeddingConfig())
    similarity: SimilarityConfig = Field(default=SimilarityConfig())

class RegulationFunctionConfigModel(BaseModel):
    """完整工作流配置"""
    name: str = Field(..., description="配置名称")
    description: str = Field(..., description="配置描述")
    version: str = Field(..., description="配置版本")
    author: str = Field(..., description="配置作者")

    azure: AzureConfigModel = Field(..., description="Azure OpenAI服务配置")
    io: IOConfigModel = Field(..., description="输入输出配置")
    llm: LLMConfigModel = Field(..., description="LLM配置")
    processing: ProcessingConfigModel = Field(..., description="处理参数")

    class Config:
        extra = "allow"  # 允许其他扩展配置
        arbitrary_types_allowed = True
        validate_assignment = True
