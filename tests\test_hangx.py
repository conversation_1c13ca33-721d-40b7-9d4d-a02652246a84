import httpx

from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.checkpoint.serde.types import Update
from loguru import logger

from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
from sdw_agent.service.func_analyze_book import gen_function_book_multi_commit_service
from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo
from sdw_agent.service.warning_code_gen_check.util.excel_validation_util import ExcelValidationUtils
from sdw_agent.service.warning_code_gen_check.util.warning_change_info_util import gen_warning_list_change_info
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_check import \
    gen_and_check_all_warning_code_service
from sdw_agent.service.warning_code_gen_check.workflow_warning_code_gen import gen_all_warning_code_service


def test_get_functionbook():
    """
    测试函数式样书接口。
    发送真实的 HTTP POST 请求到指定的 FastAPI 路由。
    """
    # 定义接口地址
    BASE_URL = "http://127.0.0.1:8001/api/sdw/function_book/getFunctionbook"

    # 定义请求数据
    payload = {
        "commit_id_after": "87b270a38a1076888f463f9e03fa0d62388fc102",
        "commit_id_before": "cb0e1e57dddd50ca67fb040ea3c40a94a7e19fab",
        "git_code_path": r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\mte_code\gfx_agl_met",
        "component_book_path": r"C:\Users\<USER>\Downloads\684D_Component_List (1) 3.xlsx",
        "git_branch": r"develop"
    }

    # 发送 POST 请求
    try:
        response = httpx.post(BASE_URL, json=payload, timeout=600)

        # 打印请求结果
        print("Status Code:", response.status_code)
        print("Response Body:", response.text)

        # 简单验证响应
        if response.status_code == 200:
            print("POST 请求成功")
        else:
            print("POST 请求失败，HTTP 状态码:", response.status_code)

    except Exception as e:
        print("请求过程中出现错误:", str(e))


def test_get_warningcodebook():
    """
    测试函数式样书接口。
    发送真实的 HTTP POST 请求到指定的 FastAPI 路由。
    """
    # 定义接口地址
    BASE_URL = "http://127.0.0.1:8001/api/sdw/warning_code_book/getWarningDspCode"
    pre_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v110.xlsx"
    after_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v218.xlsx"
    code_gen_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\19PFv3_Warning_Codebuild_1.0.3.xlsm"
    pre_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"
    after_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"

    code_folder = r"C:\workspace\cr7_app_global"
    check_file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\【补足】警告工具生成checklist_v1.0.2.xlsx"
    relate_book_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书"
    adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"

    # 定义请求数据

    # 请求数据
    request_data = {
        "pre_sample_book_url": pre_sample_book_url,
        "after_sample_book_url": after_sample_book_url,
        "code_gen_tools_url": code_gen_tools_url,
        "relate_book_folder": relate_book_folder,
        "adas_book_path": adas_book_path
    }

    # 发送 POST 请求
    try:
        response = httpx.post(BASE_URL, json=request_data, timeout=600)

        # 打印请求结果
        print("Status Code:", response.status_code)
        print("Response Body:", response.text)

        # 简单验证响应
        if response.status_code == 200:
            print("POST 请求成功")
        else:
            print("POST 请求失败，HTTP 状态码:", response.status_code)

    except Exception as e:
        print("请求过程中出现错误:", str(e))


def test_check_warningcodebook():
    """
    测试函数式样书接口。
    发送真实的 HTTP POST 请求到指定的 FastAPI 路由。
    """
    # 定义接口地址
    BASE_URL = "http://127.0.0.1:8001/api/sdw/warning_code_book/checkWarningCode"
    pre_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v110.xlsx"
    after_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v218.xlsx"
    code_gen_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\19PFv3_Warning_Codebuild_1.0.3.xlsm"
    pre_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"
    after_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"

    code_folder = r"C:\workspace\cr7_app_global"
    check_file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\【补足】警告工具生成checklist_v1.0.2.xlsx"
    relate_book_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书"
    adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"

    # 定义请求数据
    request_data = {
        "pre_sample_book_url": pre_sample_book_url,
        "after_sample_book_url": after_sample_book_url,
        "code_gen_tools_url": code_gen_tools_url,
        "code_check_file_url": check_file_path,
        "code_folder_url": code_folder,
        "relate_book_folder": relate_book_folder,
        "adas_book_path": adas_book_path
    }
    # 发送 POST 请求
    try:
        response = httpx.post(BASE_URL, json=request_data, timeout=600)

        # 打印请求结果
        print("Status Code:", response.status_code)
        print("Response Body:", response.text)

        # 简单验证响应
        if response.status_code == 200:
            print("POST 请求成功")
        else:
            print("POST 请求失败，HTTP 状态码:", response.status_code)

    except Exception as e:
        print("请求过程中出现错误:", str(e))


def test_gen_and_check_all_warning_code_service():
    if __name__ == "__main__":
        pre_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v110.xlsx"
        after_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v218.xlsx"
        code_gen_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\19PFv3_Warning_Codebuild_1.0.3.xlsm"
        compare_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\Compare_CONTDISP_OldvsNew.xlsm"
        pre_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"
        after_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"

        code_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\Roc_Doc\01REQ\0101Model\step2\cr7_app_global"
        check_file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\【补足】警告工具生成checklist_v1.0.2.xlsx"
        relate_book_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书"
        adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"
        # 创建 WarningChangeInfo 实例
        warning_change_info = WarningChangeInfo(
            pre_sample_book_url=pre_sample_book_url,
            after_sample_book_url=after_sample_book_url,
            code_gen_tools_url=code_gen_tools_url,
            compare_tools_url=compare_tools_url,  # 如果不需要可以留空
            pre_code_tools_url=pre_code_tools_url,  # 将被函数填充
            after_code_tools_url=after_code_tools_url,  # 将被函数填充
            relate_book_folder=relate_book_folder,
            code_folder=code_folder,
            check_file_path=check_file_path,
            adas_book_path=adas_book_path
        )

        gen_and_check_all_warning_code_service(warning_change_info)


def gen_new_warning():
    pre_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v110.xlsx"
    after_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v218.xlsx"
    code_gen_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\19PFv3_Warning_Codebuild_1.0.3.xlsm"
    compare_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\Compare_CONTDISP_OldvsNew.xlsm"
    pre_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"
    after_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"

    code_folder = r"C:\workspace\cr7_app_global"
    check_file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\【补足】警告工具生成checklist_v1.0.2.xlsx"
    relate_book_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书"
    adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"
    # 创建 WarningChangeInfo 实例
    warning_change_info = WarningChangeInfo(
        pre_sample_book_url=pre_sample_book_url,
        after_sample_book_url=after_sample_book_url,
        code_gen_tools_url=code_gen_tools_url,
        compare_tools_url=compare_tools_url,  # 如果不需要可以留空
        pre_code_tools_url=pre_code_tools_url,  # 将被函数填充
        after_code_tools_url=after_code_tools_url,  # 将被函数填充
        relate_book_folder=relate_book_folder,
        code_folder=code_folder,
        check_file_path=check_file_path,
        adas_book_path=adas_book_path
    )
    updated_warning_change_info = gen_all_warning_code_service(warning_change_info)
    print(updated_warning_change_info)
    pass


def parse_sheet_to_dict_with_row_number(ws):
    """
    解析指定工作簿的指定页签，将每一行解析成一个以行号为键的字典，处理合并单元格的情况。

    :param wb: xlwings.Book 已打开的工作簿对象。
    :param sheet_name: 页签名称，默认为 "要件变更内容"。
    :return: 包含每一行数据的字典，格式为 {行号: {键值对}}。
    """
    try:
        # 获取工作表

        # 获取第一行作为键名
        header_row = ws.range("A1").expand("right").value
        if not header_row:
            raise ValueError(f"页签 的第一行为空，无法解析键名。")

        # 获取 A 列的最后一行，考虑合并单元格的情况
        last_row = get_actual_last_row(ws)

        # 获取数据范围
        data_range = ws.range(f"A1:{chr(64 + len(header_row))}{last_row}").value

        # 初始化结果字典
        result = {}

        # 遍历数据区域的每一行（跳过第一行）
        for row_idx, row_data in enumerate(data_range[1:], start=2):  # 从第2行开始
            row_dict = {}
            for col_idx, cell_value in enumerate(row_data):
                # 如果单元格为空且是合并单元格，获取合并单元格的值
                if cell_value is None:
                    cell_value = ws.range((row_idx, col_idx + 1)).merge_area.value
                row_dict[header_row[col_idx]] = cell_value
            result[row_idx] = row_dict

        return result

    except Exception as e:
        return {}


def get_actual_last_row(ws):
    """
    获取工作表的实际最后一行，考虑合并单元格的情况。

    :param ws: xlwings.Worksheet 工作表对象。
    :return: 实际最后一行的行号。
    """
    try:
        # 获取 UsedRange 的最后一行
        used_range_last_row = ws.api.UsedRange.Rows.Count

        # 遍历所有单元格，检查合并单元格的范围
        max_row = 1
        for row in range(1, used_range_last_row + 1):
            for col in range(1, ws.api.UsedRange.Columns.Count + 1):
                cell = ws.range((row, col))
                # 如果单元格是合并单元格，获取合并区域的最后一行
                if cell.api.MergeCells:
                    merge_area_last_row = cell.api.MergeArea.Row + cell.api.MergeArea.Rows.Count - 1
                    max_row = max(max_row, merge_area_last_row)
                else:
                    max_row = max(max_row, row)

        return max_row

    except Exception as e:
        print(f"获取实际最后一行时发生错误: {e}")
        return 1


from pydantic import BaseModel, Field


class RequirementChangeContentFormat(BaseModel):
    """
    LLM响应格式定义
    """
    contdsip_id: str = Field(description="用于记录CONTDSIP-NO")
    contdsip_change_desc: str = Field(description="用于记录CONTDSIP变更内容的描述")
    need_parse_contdsip: str = Field(description="用于记录是否需要处理这个CONTDSIP-NO")


class RequirementChangeContentList(BaseModel):
    """
    LLM响应格式定义
    """
    requirement_change_content_list: list[RequirementChangeContentFormat] = Field(
        description="用于记录CONTDSIP变更内容的列表")


def llm_requirement_change_content(requirement_change_content):
    # 创建一个聊天提示模板，用于生成变更摘要
    template = ChatPromptTemplate(
        [
            ("user", ENV.prompt.requirement_change_content_prompts.change_content_desc_prompts)
        ],
        template_format="mustache",
    )
    invoke_data = {
        "change_content": requirement_change_content["概要"],
        "warning_content_list": [],
        "lang": "中文"
    }

    resp: AIMessage = get_ai_message_with_structured_output(
        template,
        invoke_data,
        RequirementChangeContentList,
        llm_model=None
    )
    result = resp.requirement_change_content_list
    return result


def insert_rows_and_fill_data(ws, row_number, row_data):
    """
    在指定行号下方插入 len(row_data) - 1 行，并将 row_data 的数据依次填入每一行的 A 列。

    :param ws: xlwings.Worksheet 工作表对象。
    :param row_number: 插入行的起始位置（行号）。
    :param row_data: 要填入的数据列表。
    """
    try:
        # 计算需要插入的行数
        num_rows_to_insert = len(row_data) - 1

        if num_rows_to_insert <= 0:
            print("无需插入行，数据长度不足。")
            return

        # 插入行
        ws.api.Rows(row_number + 1).Insert(Shift=1, CopyOrigin=1)  # 插入一行
        for _ in range(num_rows_to_insert - 1):  # 插入剩余行
            ws.api.Rows(row_number + 2).Insert(Shift=1, CopyOrigin=1)

        # 填充数据到 A 列
        for i, value in enumerate(row_data):
            ws.range(f"A{row_number + i}").value = value.contdsip_id
            ws.range(f"E{row_number + i}").value = value.contdsip_change_desc
            ws.range(f"F{row_number + i}").value = value.need_parse_contdsip

        print(f"在第 {row_number} 行下方插入 {num_rows_to_insert} 行，并填充数据完成。")

    except Exception as e:
        print(f"插入行并填充数据时发生错误: {e}")


def gen_requirement_change_content(ws, requirement_change_content_dict):
    # 遍历字典并调用 parse_a 函数
    offset = 0
    for row_number, row_data in requirement_change_content_dict.items():
        print(f"正在处理第 {row_number} 行数据...")
        requirement_change_content_list = llm_requirement_change_content(row_data)
        if len(requirement_change_content_list) > 1:
            insert_rows_and_fill_data(ws, row_number + offset, requirement_change_content_list)
            offset = offset + len(requirement_change_content_list) - 1
        elif len(requirement_change_content_list) == 1:
            ws.range(f"A{row_number + offset}").value = requirement_change_content_list[0].contdsip_id
            ws.range(f"E{row_number + offset}").value = requirement_change_content_list[0].contdsip_change_desc
            ws.range(f"F{row_number + offset}").value = requirement_change_content_list[0].need_parse_contdsip
    return


def test_gen_warning_list_change_info():
    # 示例调用
    pre_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v110.xlsx"
    after_sample_book_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-M_CONTDISP-CSTD-0050-C2-v218.xlsx"
    compare_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\Compare_CONTDISP_OldvsNew.xlsm"
    folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书"
    adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"

    # 创建 WarningChangeInfo 实例
    warning_change_info = WarningChangeInfo(
        pre_sample_book_url=pre_sample_book_url,
        after_sample_book_url=after_sample_book_url,
        compare_tools_url=compare_tools_url,
        adas_book_path=adas_book_path,
        relate_book_folder=folder,
    )

    # 调用函数
    result = gen_warning_list_change_info(warning_change_info)
    print(result)


import os
import tempfile
from typing import Tuple

# 模拟导入
from sdw_agent.service.warning_code_gen_check.util.code_analysis_util import CodeAnalysisUtils
from sdw_agent.service.warning_code_gen_check.models import WarningChangeInfo


class MockWarningChangeInfo:
    """模拟 WarningChangeInfo 类"""

    def __init__(self, after_warning_num=100):
        self.after_warning_num = after_warning_num


def create_test_file(content: str, filename: str = "wrndtcfg.c") -> str:
    """创建测试文件"""
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, filename)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    return temp_dir


def test_case_1_normal_function():
    """测试用例1: 正常的函数代码（ID递增）"""
    print("测试用例1: 正常的函数代码")
    # code_folder = r"C:\workspace\cr7_app_global"
    code_folder = r"C:\workspace\cr7_app_global\mainline\ren_rcargen3\src\IpcApplication\Disp\dsp_app\dsp_wrndtctcore"
    warning_info = None
    # 创建正常的函数代码
    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)
        expected = (False, "函数代码检查通过")

        if result == expected:
            print("✓ 测试用例1 通过")
        else:
            print(f"✗ 测试用例1 失败: 期望 {expected}, 实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例1 异常: {e}")
    finally:
        # 清理临时文件
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def test_case_2_id_not_incremental():
    """测试用例2: ID不递增的函数代码"""
    print("测试用例2: ID不递增的函数代码")

    # 创建ID不递增的函数代码
    function_code = """
void vd_g_WrndtcfgGetReq00(U4* u4_p_req) {
    *u4_p_req |= (U4)u1_GETREQ_ID1() << 0;
    *u4_p_req |= (U4)u1_GETREQ_ID3() << 1;  // ID跳跃，不连续
    *u4_p_req |= (U4)u1_GETREQ_ID2() << 2;  // ID回退
    u4_p_req++;
}
"""

    code_folder = create_test_file(function_code)
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 应该返回错误
        if result[0] == True and "ID没有递增" in result[1]:
            print("✓ 测试用例2 通过")
        else:
            print(f"✗ 测试用例2 失败: 期望错误结果，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例2 异常: {e}")
    finally:
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def test_case_3_file_not_found():
    """测试用例3: 文件不存在"""
    print("测试用例3: 文件不存在")

    # 使用不存在的文件夹
    code_folder = "/nonexistent/path"
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 应该返回错误
        if result[0] == True and "检查失败" in result[1]:
            print("✓ 测试用例3 通过")
        else:
            print(f"✗ 测试用例3 失败: 期望错误结果，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例3 异常: {e}")


def test_case_4_function_not_found():
    """测试用例4: 函数不存在"""
    print("测试用例4: 函数不存在")

    # 创建不包含目标函数的文件
    function_code = """
void other_function(void) {
    // 其他函数
}
"""

    code_folder = create_test_file(function_code)
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 应该返回错误
        if result[0] == True and "检查失败" in result[1]:
            print("✓ 测试用例4 通过")
        else:
            print(f"✗ 测试用例4 失败: 期望错误结果，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例4 异常: {e}")
    finally:
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def test_case_5_shift_not_incremental():
    """测试用例5: 偏移量不递增"""
    print("测试用例5: 偏移量不递增")

    # 创建偏移量不递增的函数代码
    function_code = """
void vd_g_WrndtcfgGetReq00(U4* u4_p_req) {
    *u4_p_req |= (U4)u1_GETREQ_ID1() << 0;
    *u4_p_req |= (U4)u1_GETREQ_ID2() << 2;  // 偏移量跳跃
    *u4_p_req |= (U4)u1_GETREQ_ID3() << 1;  // 偏移量回退
}
"""

    code_folder = create_test_file(function_code)
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 应该返回错误
        if result[0] == True and "ID没有递增" in result[1]:
            print("✓ 测试用例5 通过")
        else:
            print(f"✗ 测试用例5 失败: 期望错误结果，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例5 异常: {e}")
    finally:
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def test_case_6_empty_function():
    """测试用例6: 空函数"""
    print("测试用例6: 空函数")

    # 创建空函数
    function_code = """
void vd_g_WrndtcfgGetReq00(U4* u4_p_req) {
    // 空函数
}
"""

    code_folder = create_test_file(function_code)
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 空函数应该通过检查（没有ID递增问题）
        if result[0] == False:
            print("✓ 测试用例6 通过")
        else:
            print(f"✗ 测试用例6 失败: 期望通过检查，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例6 异常: {e}")
    finally:
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def test_case_7_complex_function():
    """测试用例7: 复杂函数（多个块）"""
    print("测试用例7: 复杂函数（多个块）")

    # 创建包含多个u4_p_req++块的函数
    function_code = """
void vd_g_WrndtcfgGetReq00(U4* u4_p_req) {
    // 第一个块
    *u4_p_req |= (U4)u1_GETREQ_ID1() << 0;
    *u4_p_req |= (U4)u1_GETREQ_ID2() << 1;
    *u4_p_req |= (U4)u1_GETREQ_ID3() << 2;
    u4_p_req++;

    // 第二个块
    *u4_p_req |= (U4)u1_GETREQ_ID4() << 0;
    *u4_p_req |= (U4)u1_GETREQ_ID5() << 1;
    u4_p_req++;

    // 第三个块
    *u4_p_req |= (U4)u1_GETREQ_ID6() << 0;
}
"""

    code_folder = create_test_file(function_code)
    warning_info = MockWarningChangeInfo()

    try:
        result = CodeAnalysisUtils.is_wrndtcfggetreq_code_error(code_folder, warning_info)

        # 应该通过检查
        if result[0] == False:
            print("✓ 测试用例7 通过")
        else:
            print(f"✗ 测试用例7 失败: 期望通过检查，实际 {result}")
    except Exception as e:
        print(f"✗ 测试用例7 异常: {e}")
    finally:
        import shutil
        shutil.rmtree(code_folder, ignore_errors=True)


def run_all_tests():
    """运行所有测试用例"""
    print("=" * 50)
    print("开始测试 is_wrndtcfggetreq_code_error 方法")
    print("=" * 50)

    test_cases = [
        test_case_1_normal_function,
        # test_case_2_id_not_incremental,
        # test_case_3_file_not_found,
        # test_case_4_function_not_found,
        # test_case_5_shift_not_incremental,
        # test_case_6_empty_function,
        # test_case_7_complex_function,
    ]

    passed = 0
    total = len(test_cases)

    for test_case in test_cases:
        try:
            test_case()
            passed += 1
        except Exception as e:
            print(f"✗ {test_case.__name__} 执行失败: {e}")
        print("-" * 30)

    print("=" * 50)
    print(f"测试完成: {passed}/{total} 个测试用例通过")
    print("=" * 50)


def test_check_dspmnscrl_msg_max_length():
    ExcelValidationUtils.check_dspmnscrl_msg_max_length(r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3.xlsm")


def generate_function_calls(commit_ids, code_path, component_book_url):
    """
    根据 commit_id 列表直接调用 gen_function_book_multi_commit_service 函数。

    :param commit_ids: 提供的 commit ID 列表
    :param code_path: 代码路径
    :param component_book_url: 组件书路径
    :return: None
    """
    for i in range(len(commit_ids) - 1):
        commit_id_after = commit_ids[i]
        commit_id_before = commit_ids[i + 1]
        print(f"生成函数分析书: {commit_id_after} - {commit_id_before}")
        try:
            ret = gen_function_book_multi_commit_service(code_path, commit_id_after, commit_id_before, component_book_url)
            logger.success(f"函数分析书生成成功 变更后{commit_id_after}  变更前: {commit_id_before}  函数式样书:{ret}")
        except Exception as e:
            print(f"函数分析书生成失败: {e}")


# 提供的 commit ID 列表
commit_ids = [
    "7d5e5e8a9910c95fc97ec51da47ff5b8fd6db7a6",
    "c1224e880b25f6a96fb05d402526f25628d7a1bc",
    # "7c159e231f1c21e0f4d18cc074b7b5f2e7e6e516",
    # "d16d3015a203470c3818c416a8a810b680406386",
    # "87b270a38a1076888f463f9e03fa0d62388fc102",
    # "cb0e1e57dddd50ca67fb040ea3c40a94a7e19fab",
    # "c4134b2a254868ca7197e262fd0af18845b478a5",
    # "54b961267f0796499e8c3b4586ebd5d15c0c6d68",
    # "4c8234e9dfcbb699695ecd23ef8e9f45de816688",
    # "5df7f098dab99566938f0d1fd67a28c13e164fd6",
    # "fbe01db5564611fb1c7395e08b759344d4e5cd2f",
    # "a89c5b8c24a1417b722e59aca5aa3e29c980231e",
    # "254563b2207bd60efdc578d0a070adad4841c2bf",
    # "7f8809f285d5cda961cc26e277b207df5a9ce13b",
    # "87b9fcb922032e44342d37fe8fbc3f9e94ce8c37",
    # "8e65ad0d3378ef033f3ec6ef88240916e02bb78f",
    # "8a62d147b5c93628417b1c4117fac214ebe82bcf",
    # "3b5ce56c08e68a0b62139cd8d243a64b80baaca2",
    # "9af326226edd628d5084efe59293d4ecb4c105d4",
    # "f29aff16cd8c290b8de5ff499ca69378713e3570",
    # "167c133cfe7da2430485f7ca8605dd205e34dfa2",
    # "5a46da4f832c6864b05fdb7d101101f3c45ff347",
    # "49d6bd9c9b4e264ce8b869bb50bf51ebb147adc2",
    # "942161e5923e3ab704c32507f18e9b2cdb9b9be6",
    # "3e3e5a68185690115354509f9f337fa76043a6f1",
    # "497ad5357cadd3fd504c42405c82d56b2f6e3316",
    # "50bcacb45622155c68b2fa63be3ff7a84638c2e6",
    # "fdbe52fee1b605cdaad94d4ffc637a7a0bd76170",
    # "d3a68955f6e2277b3f2b060aab5c2a9282dd1e30",
    # "18807dd12d038074e636372dc20fc410c578930f",
    # "001445e60555c81be4d2160820c6a21baddd6075",
    # "f10a7d993a494e7d26a8142fbed7509892ce7f2d",
    # "076a4b6343ee5429c7affeb49871337201ce85d4",
    # "4d4b722413dd9db5df6cb3b55b3af35e08ea3f5d",
    # "f6865cf8b41b26fc2ed4f77099e435d17dac6e3e",
    # "e1b4a39466c33cbc077d47b021ce96f7db05dae6",
    # "4cc28c4ef9effc0911f429ba42c3bb4e5f74e53a",
    # "f83ecde4c7679dd90fc02036b617c7d4aa5e20aa",
    # "ba9a50f3e1c703632500ee56b05942323ef7fd1a",
    # "ef9e62189d4384b221354dd364b763d4af872537",
    # "126fa21ef92d8586ad8d1a77ef0b9ddc6cf1c443",
    # "b7833e66c8cf3857853a3c78d7f8ffe4195e223c",
    # "8799349859d37fa7199ab304750b65d03328dba5",
    # "9f2eb6b03da621d6a4a3d3b5208ff2643b0d6126",
    # "1847340f109de5de9174d169f7e0711a1f6a1f82",
    # "cd5edf9c88bf30a16409c0a58759c62d9c75378b",
    # "93e40dd7cd0afdfc2f624fe270b0feabcbe4b955",
    # "83d1c01b9325e8dace56c81125fa03d398f95643",
    # "35412729eb4191f33c251264b75e1d15db42f7bb",
    # "5e10d30cf0ec117f01ce323c4707761b44ef286c"
]

# 代码路径和组件书路径
code_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\mte_code\gfx_agl_met"
component_book_url = r"C:\Users\<USER>\Downloads\684D_Component_List (1) 3.xlsx"


def gen_new_warning_meter_interface():
    pre_sample_book_url = r"C:\Users\<USER>\Downloads\MET-M_CONTDISP-CSTD-0057-C2.xlsx"
    after_sample_book_url = r"C:\Users\<USER>\Downloads\MET-M_CONTDISP-CSTD-0060-C2.xlsx"
    code_gen_tools_url = r"C:\Users\<USER>\Downloads\19PFv3_Warning_Codebuild_1.0.2 2.xlsm"
    compare_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\【补足】Compare_CONTDISP_0057vs0060 - 副本 (2) - 副本.xlsm"
    pre_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"
    meter_interface_popup_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\Meter_Interface_document_Popup.xlsm"
    after_code_tools_url = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\new_19PFv3_Warning_Codebuild_1.0.3 - 副本.xlsm"

    code_folder = r"C:\workspace\cr7_app_global"
    check_file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\【补足】警告工具生成checklist_v1.0.2.xlsx"
    relate_book_folder = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\制御仕様"
    adas_book_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\MET-G_TACCSWSG2-CSTD-A0-01-D-C0.xlsx"
    # 创建 WarningChangeInfo 实例
    warning_change_info = WarningChangeInfo(
        pre_sample_book_url=pre_sample_book_url,
        after_sample_book_url=after_sample_book_url,
        code_gen_tools_url=code_gen_tools_url,
        compare_tools_url=compare_tools_url,  # 如果不需要可以留空
        pre_code_tools_url=pre_code_tools_url,  # 将被函数填充
        after_code_tools_url=after_code_tools_url,  # 将被函数填充
        relate_book_folder=relate_book_folder,
        code_folder=code_folder,
        check_file_path=check_file_path,
        adas_book_path=adas_book_path,
        meter_interface_popup_url=meter_interface_popup_url,
    )
    updated_warning_change_info = gen_all_warning_code_service(warning_change_info)
    print(updated_warning_change_info)
    pass


def test_get_check_result_book():
    """
    测试开发环境检查工作流接口。
    发送真实的 HTTP POST 请求到指定的 FastAPI 路由。
    """
    # 定义接口地址
    BASE_URL = "http://127.0.0.1:8001/api/sdw/dev_env_check/getCheckResultBook"

    # 定义请求数据
    payload = {
        "pre_pack_path": r"/Orca3/263D/release-build/20230529193152-v3.2.1/263D-v321-20230529.zip",
        "after_pack_path": r"/Orca3/263D/release-build/20230620094633-v3.2.3/263D-v323-20230620.zip",
        "pre_manifest_path": r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release4.2.0.xml",
        "after_manifest_path": r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\manifest\release6.1.0.xml",
        "pre_jenkins_script_path": r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-22-06.xml",
        "after_jenkins_script_path": r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\开发环境确认\jenkins\configOutput2024-01-05_16-24-02.xml"
    }

    # 发送 POST 请求
    try:
        response = httpx.post(BASE_URL, json=payload, timeout=600)

        # 打印请求结果
        print("Status Code:", response.status_code)
        print("Response Body:", response.text)

        # 简单验证响应
        if response.status_code == 200:
            print("POST 请求成功")
        else:
            print("POST 请求失败，HTTP 状态码:", response.status_code)

    except Exception as e:
        print("请求过程中出现错误:", str(e))

# 调用测试函数
if __name__ == "__main__":
    # run_all_tests()
    # gen_new_warning_meter_interface()
    # test_gen_warning_list_change_info()
    # gen_new_warning()

    # test_gen_and_check_all_warning_code_service()
    #
    # test_check_dspmnscrl_msg_max_length()

    # 调用函数生成代码
    # generate_function_calls(commit_ids, code_path, component_book_url)

    test_get_check_result_book()
    # gen_new_warning()
    # 打开工作簿
    # file_path = r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\Compare_CONTDISP_OldvsNew测试.xlsm"
    # app = xw.App(visible=True)  # 设置为 True 显示 Excel 窗口
    # wb = app.books.open(file_path)
    # ws = wb.sheets["要件变更内容"]
    #
    # # 调用解析函数
    # parsed_data = parse_sheet_to_dict_with_row_number(ws)
    # gen_requirement_change_content(ws, parsed_data)
    # # 打印解析结果
    # for row_number, row_dict in parsed_data.items():
    #     print(f"{row_number}: {row_dict}")
    #
    # wb.save()
    # # 关闭工作簿
    # wb.close()
    # app.quit()

    # import win32com.client
    #
    #
    # def click_activex_button():
    #     # 强制创建新的 Excel 实例
    #     excel = win32com.client.DispatchEx("Excel.Application")
    #     excel.Visible = True  # 显示 Excel 窗口
    #     # wb = excel.Workbooks.Open(r"C:\Users\<USER>\Desktop\SVN_NEW\xh\7月需求\告警代码生成\测试式样书\old_19PFv3_Warning_Codebuild_1.0.3.xlsm")  # 替换为你的文件路径
    #     wb = excel.Workbooks.Open(r"C:\Users\<USER>\Downloads\new_19PFv3_Warning_Codebuild_1.0.2 2.xlsm",
    #                               ReadOnly=False,
    #                               UpdateLinks = 0)  # 替换为你的文件路径
    #
    #     # 激活按钮所在的工作表
    #     sheet = wb.Worksheets("CONTDISP (源)")  # 替换为按钮所在的工作表名称
    #     sheet.Activate()
    #     excel.Application.Run("sheet26.CommandButton1_Click")
    #     # # 遍历工作表中的所有 OLEObjects（ActiveX 控件）
    #     # for ole_obj in sheet.OLEObjects():
    #     #     if ole_obj.Name == "CommandButton1":  # 替换为按钮名称
    #     #         ole_obj.Object.Click()  # 模拟点击按钮
    #     #         print("按钮已点击！")
    #     #         break
    #     # else:
    #     #     print("未找到指定的按钮！")
    #
    #     # 关闭工作簿（根据需要保存更改）
    #     wb.Close(SaveChanges=True)
    #     excel.Quit()
    #
    #
    # click_activex_button()
