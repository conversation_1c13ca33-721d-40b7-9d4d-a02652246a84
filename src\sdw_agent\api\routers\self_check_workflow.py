"""
自检工作流API路由
"""
from fastapi import APIRouter
from loguru import logger
from sdw_agent.model.response_model import FileCompareResponse, FileCompareRequest,DnCodecheckResponse
from sdw_agent.model.request_model import DnCodecheckRequest
from sdw_agent.service.filecompare.filecompare_implement import FileCompareProcessor
from sdw_agent.service.filecompare.filecompare_codediff_generator import FileCompareCodediff
from sdw_agent.service.DN_codecheck.dn_codecheck import CodeCheckerWorkflow
from sdw_agent.service.cs_codereview.cs_codereview import CodeReviewWorkflow
from sdw_agent.service import WorkflowStatus
from pydantic import BaseModel
from typing import Optional

router = APIRouter(prefix="/api/sdw/self-check", tags=["自检工作流"])


class CsCodeReviewResponse(BaseModel):
    code: int = 0
    msg: str = ""
    data: str


class CsCodeReviewRequest(BaseModel):
    commit_id: str


class FileCompare_git_Request(BaseModel):
    input_path: str
    commit_id: str
    compared_commit_id: Optional[str]


@router.post("/dn_codecheck",
             summary="基于DN编码规范检查提交的代码",
             description="基于DNK编码规则逐条检查代码，生成最终检查报告",
             response_description="",
             response_model=DnCodecheckResponse)
async def dn_codecheck(request: DnCodecheckRequest):
    try:
        workflow = CodeCheckerWorkflow()
        result = workflow.run(request.commit_id)
        if result.status == WorkflowStatus.SUCCESS:
            file_path = result.data.get("file_path", "")
            return DnCodecheckResponse(
                code=0,
                msg="DN编码规范检查成功",
                data=file_path
            )
        else:
            logger.error(f"设计基准Check Sheet分析失败: {result.message}")
            return DnCodecheckResponse(
                code=1,
                msg="DN编码规范检查失败",
                data=""
            )
    except Exception as e:
        logger.error(f"DN 编码规范检查失败: {str(e)}")
        return DnCodecheckResponse(
            code=1,
            msg=f"DN 编码规范检查失败: {str(e)}",
            data=""
        )



@router.post("/filecompare",
             summary="代码差分成果物AI填写",
             description="基于代码差分生成差分成果物AI版，开发确认需求与代码照合",
             response_description="",
             response_model=FileCompareResponse)
async def filecompare_generator(request: FileCompareRequest):
    try:
        file_path = request.input_path
        if not file_path:
            return FileCompareResponse(
                code=1,
                msg="失败：文件路径不能为空",
                data=[]
            )
        if isinstance(file_path, list):
            if not all(file_path):
                return FileCompareResponse(
                    code=1,
                    msg="失败：文件路径列表中包含空值",
                    data=[]
                )
        elif isinstance(file_path, str):
            if not file_path.strip():
                return FileCompareResponse(
                    code=1,
                    msg="失败：文件路径不能为空",
                    data=[]
                )
        else:
            return FileCompareResponse(
                code=1,
                msg="失败：文件路径格式错误",
                data=[]
            )
        workflow = FileCompareProcessor()
        result = workflow.run(file_path)
        if result.status == WorkflowStatus.SUCCESS:
            output_paths = result.data.get("file_path", "")
            return FileCompareResponse(
                code=0,
                msg="代码差分生成成功",
                data=output_paths
            )
        else:
            return FileCompareResponse(
                code=1,
                msg="代码差分生成失败",
                data=[]
            )
    except Exception as e:
        logger.error(f"代码差分生成失败: {str(e)}")
        return FileCompareResponse(
            code=1,
            msg=f"代码差分生成失败: {str(e)}",
            data=[]
        )

@router.post("/filecompare_git",
             summary="代码生成代码差分成果物",
             description="基于代码差分生成差分成果物AI版，开发确认需求与代码照合",
             response_description="",
             response_model=FileCompareResponse)
async def filecompare_generator2(request: FileCompare_git_Request):
    try:
        code_path = request.input_path
        version1 = request.commit_id
        version2 = request.compared_commit_id
        file_compare = FileCompareCodediff()

        # 执行 FileCompare 流程
        env_check_result = file_compare.validate_input(code_path, version1, version2)
        if not env_check_result:
            return FileCompareResponse(
                    code=1,
                    msg="请检查依赖工具autodiff工具手顺配置环境。http://************/technical-department/graphics-tools/autodiff",
                    data=[]
                    )

        result = file_compare.execute(code_path, version1, version2)
        if result.status == WorkflowStatus.SUCCESS:
            file_path = result.data.get("file_path", "")

        print(file_path)
        workflow = FileCompareProcessor()
        result = workflow.run(file_path)
        if result.status == WorkflowStatus.SUCCESS:
            output_paths = result.data.get("file_path", "")
            return FileCompareResponse(
                code=0,
                msg="代码差分生成成功",
                data=output_paths
            )
        else:
            return FileCompareResponse(
                code=1,
                msg="代码差分生成失败",
                data=[]
            )
    except Exception as e:
        logger.error(f"代码差分生成失败: {str(e)}")
        return FileCompareResponse(
            code=1,
            msg=f"代码差分生成失败: {str(e)}",
            data=[]
        )


@router.post("/cs_codereview",
             summary="自动生成自检和代码评审报告",
             description="基于Git差分代码和预定义的代码自检审核观点，通过AI模型自动生成审核观点检查报告。",
             response_description="",
             response_model=CsCodeReviewResponse)
async def cs_codereview(request: CsCodeReviewRequest):
    try:
        workflow = CodeReviewWorkflow()
        result = workflow.run(request.commit_id)
        if result.status == WorkflowStatus.SUCCESS:
            file_path = result.data.get("file_path", "")
            return CsCodeReviewResponse(
                code=0,
                msg="自检和代码评审报告生成成功",
                data=file_path
            )

    except Exception as e:
        logger.error(f"自检和代码评审报告生成失败: {str(e)}")
        return CsCodeReviewResponse(
            code=1,
            msg=f"自检和代码评审报告生成失败: {str(e)}",
            data=""
        )
