import re
import traceback

import pandas as pd
from loguru import logger

__all__ = [
    'nodes_to_markdown',
    'extract_from_markdown',
    'markdown_to_pandas',
    'data_to_markdown_table',
    'convert_to_markdown',
    'dict_list_to_markdown_table'
]


def nodes_to_markdown(nodes: list, reorder=False):
    """
    将nodes列表转换成markdown表格
    :param nodes:
    :param reorder: 是否需要重新排序，例如name, description放在前面
    :return:
    """
    if not nodes:
        return ""

    # 处理节点数据，移除UUID和source_file，并调整列顺序
    processed_nodes = []
    if reorder:
        for node in nodes:
            processed_node = {k.strip(): v for k, v in node.items() if k.strip() not in ["UUID", "source_file"]}
            if "name" in processed_node and "description" in processed_node:
                processed_node = {k: processed_node[k] for k in
                                  ["name", "description"] + [key for key in processed_node if
                                                             key not in ["name", "description"]]}
            processed_nodes.append(processed_node)
    else:
        processed_nodes = nodes

    # 获取所有属性名称
    headers = list(processed_nodes[0].keys())

    # 构建Markdown表格头部
    markdown = "| " + " | ".join(headers) + " |\n"
    markdown += "|" + " --- |" * len(headers) + "\n"

    # 构建表格内容
    for node in processed_nodes:
        row = [str(node.get(header, "")).replace("\n", "<br />") for header in headers]
        markdown += "| " + " | ".join(row) + " |\n"

    return markdown


def data_to_markdown_table(data, str_list):
    table_data = []
    for sublist, str_value in zip(data, str_list):
        for item in sublist:
            name = item.get('name', '')
            if "\n" in item.get('description', ''):
                description: str = item.get('description', '').strip()
                description = description.replace("\n", " ")
            else:
                description = item.get('description', '')
            if "\n" in item.get('content', ''):
                content = item.get('content', '').split("\n")[0] + "..."
            else:
                content = item.get('content', '')
            table_data.append([str_value, name, description])

    if not table_data:
        return ""
    headers = ["component_name", "interface_name", "description"]
    markdown = "| " + " | ".join(headers) + " |\n"
    markdown += "| " + " | ".join(["---"] * len(headers)) + " |\n"
    for row in table_data:
        markdown += "| " + " | ".join(map(str, row)) + " |\n"
    return markdown


def extract_from_markdown(content: str, formats: list[str] | str):
    """
    从markdown文本中提取 特定格式 内容
    :param content: 文本内容
    :param formats: 格式，例如 plantuml, csv, json等
    :return:
    """
    # 使用正则表达式查找PlantUML代码块
    if isinstance(formats, str):
        formats = [formats]

    for format in formats:
        pattern = f"```{format}" + r"\n(.*?)```"
        blocks = re.findall(pattern, content, re.DOTALL)
        if len(blocks) > 0:
            content = blocks[0]

    return content


def markdown_to_pandas(markdown_text) -> pd.DataFrame:
    try:
        # 去除字符串首尾的换行符
        markdown_table = markdown_text.strip()

        # 按行分割表格
        rows = markdown_table.split('\n')

        # 初始化一个空列表来存储DataFrame的数据
        data = []

        # 遍历每一行
        for row in rows:
            # 去除行首尾的空格和管道符
            row = row.strip().strip('|')

            # 按管道符分割行，得到列数据
            cells = row.split('|')

            # 去除每个单元格首尾的空格
            cleaned_cells = [cell.strip() for cell in cells]

            # 将处理后的行添加到数据列表中
            data.append(cleaned_cells)

            # 假设第一行是表头
        if data:
            columns = data[0]
            data = data[2:]  # 移除表头与---

            # 创建DataFrame
            df = pd.DataFrame(data, columns=columns)

            return df
        else:
            return pd.DataFrame()  # 返回一个空的DataFrame
    except Exception as e:
        logger.error(traceback.format_exc())


def convert_to_markdown(data):
    """将 list[dict] 转换为 Markdown 表格"""
    # 提取所有可能的列名（动态适应不同键）
    headers = list({key for d in data for key in d.keys()})
    headers.sort()  # 可选：按字母顺序排序

    # 生成表头行和分隔线
    header_row = "| " + " | ".join(headers) + " |"
    separator = "| " + " | ".join(["---"] * len(headers)) + " |"

    # 生成数据行
    rows = []
    for item in data:
        row = "| " + " | ".join(str(item.get(key, "")) for key in headers) + " |"
        rows.append(row)

    # 拼接完整表格
    markdown_table = "\n".join([header_row, separator] + rows)
    return markdown_table


def dict_list_to_markdown_table(data_list: list[dict], fields: list[str]) -> str:
    """
    将字典列表转换为 Markdown 表格，只显示指定的字段

    :param data_list: 字典列表，每个字典代表表格的一行数据
    :param fields: 字段列表，指定要显示的字段名称，这些字段将作为表格的列
    :return: Markdown 格式的表格字符串

    示例:
        data = [
            {"name": "张三", "age": 25, "city": "北京", "job": "工程师"},
            {"name": "李四", "age": 30, "city": "上海", "job": "设计师"}
        ]
        fields = ["name", "age", "city"]
        result = dict_list_to_markdown_table(data, fields)
        # 返回:
        # | name | age | city |
        # | --- | --- | --- |
        # | 张三 | 25 | 北京 |
        # | 李四 | 30 | 上海 |
    """
    if not data_list:
        return ""

    if not fields:
        return ""

    # 生成表头行
    header_row = "| " + " | ".join(fields) + " |"

    # 生成分隔线
    separator = "| " + " | ".join(["---"] * len(fields)) + " |"

    # 生成数据行
    rows = []
    for item in data_list:
        # 获取每个字段的值，如果字段不存在则使用空字符串
        row_values = []
        for field in fields:
            value = str(item.get(field, ""))
            # 处理换行符，将其替换为 <br /> 以在 Markdown 中正确显示
            value = value.replace("\n", "<br />")
            row_values.append(value)

        row = "| " + " | ".join(row_values) + " |"
        rows.append(row)

    # 拼接完整表格
    markdown_table = "\n".join([header_row, separator] + rows)
    return markdown_table


class TestMdParser:
    """
    test agent用于简单读取MD文件信息
    """

    def __init__(self):
        pass

    def data_load(self, path):
        with open(path, "r", encoding="utf-8") as f:
            return f.read()


if __name__ == '__main__':
    content = """| No | 系统     | Layer          | 组件    | 组件和该功能的关系描述 | 该组件需要修改的内容描述                                                                                                                                                                                                                                                                                                                                  |
| -- | -------- | -------------- | ------- | ---------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1  | CortexR7 | IpcApplication | Dsp_App | CSTM画面阶层配置       | 逻辑调整：CSTM MENU画面的3个Item（1500Wコンセント、2400Wコンセント、7200Wコンセント）在0阶层配置中删除；<br />在1阶层的[车辆设置]画面添加；<br />接口变更：3个Item（1500Wコンセント、2400Wコンセント、7200Wコンセント）从0阶层变更为1阶层，<br />时序图中画面显示的阶层的入参发生变更，及0阶层和1阶层画面的配置发生变更，**不涉及接口本身的变更**。 |
"""
    affected_component_df = markdown_to_pandas(content)
    logger.info(list(affected_component_df['组件']))
