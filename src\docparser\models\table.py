import copy
from datetime import datetime
from typing import List
import logging
# from kotei_i18n.api import _
from .base_object import BaseObject
from .border import CellBorderObject
from .comment import CommentObject
from .coordinate import CoordinateObject
from .layout import LayoutObject
from .position import Position, Context
from .style import StyleObject
from .text import TextObject
from .picture import PictureObject
from .graphic import GraphicObject

class TableBaseObject(BaseObject):

    def __init__(self):
        self._layout = LayoutObject()  # 布局信息
        self._style = StyleObject()  # 样式
        self._border = CellBorderObject()  # 边框
        self._coordinate = CoordinateObject()  # 坐标
        self._data_id = None  # 唯一标识


    def to_dict(self):
        """
        将 TableBaseObject 对象转换为字典
        """
        return {
            "layout": self._layout.to_dict(),
            "style": self._style.to_dict(),
            "border": self._border.to_dict(),
            "coordinate": self._coordinate.to_dict(),
            "data_id": self._data_id,
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TableBaseObject 实例
        """
        obj = cls()
        obj._layout = LayoutObject.from_dict(data.get("layout", {}))  # 调用 LayoutObject 的 from_dict 方法
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 调用 StyleObject 的 from_dict 方法
        obj._border = CellBorderObject.from_dict(data.get("border", {}))  # 调用 CellBorderObject 的 from_dict 方法
        obj._coordinate = CoordinateObject.from_dict(data.get("coordinate", {}))  # 调用 CoordinateObject 的 from_dict 方法
        obj._data_id = data.get("data_id", '')
        return obj

    @property
    def data_id(self):
        return self._data_id

    @data_id.setter
    def data_id(self, new_value):
        if not new_value:
            new_value = 0
        assert type(new_value) == int
        self._data_id = new_value

    @property
    def layout(self):
        return self._layout

    @layout.setter
    def layout(self, new_value):
        assert isinstance(new_value, LayoutObject)
        self._layout = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value

    @property
    def border(self):
        return self._border

    @border.setter
    def border(self, new_value):
        assert isinstance(new_value, CellBorderObject)
        self._border = new_value

    @property
    def coordinate(self):
        return self._coordinate

    @coordinate.setter
    def coordinate(self, new_value):
        assert isinstance(new_value, CoordinateObject)
        self._coordinate = new_value

    def format_row(self, row_obj, sep_col=''):
        """格式化表格数据
        :param row_obj: 一维列表
        :param sep_col: 列分隔符
        :return: [str] 返回格式化的字符串
        """
        row = []
        for cell in row_obj.cells:
            if cell:
                row.append(str(cell.text))
        return sep_col.join(row)


def check_specific_style(run):
    style_obj = run.style
    if style_obj.font_family or style_obj.font_size or style_obj.font_color not in ["", "#000000"] or style_obj.background_style or style_obj.background_color:
        specific_style = True
    elif not style_obj.font_style.normal:
        specific_style = True
    else:
        specific_style = False
    return specific_style


def get_text_obj_runs_style(text_obj, runs_style_obj):
    last_style = None
    for run in text_obj.runs:
        if check_specific_style(run):
            run_style_dict = run.to_dict()
            if isinstance(run_style_dict["text"], datetime):
                run_style_dict["text"] = run_style_dict["text"].isoformat()  # 将 datetime 转换为 ISO 格式字符串
            this_style = run_style_dict["style"]
            if this_style == last_style:
                last_run_style_dict = runs_style_obj[-1]
                last_run_style_dict["text"] += run_style_dict["text"]
            else:
                runs_style_obj.append(run_style_dict)
            last_style = this_style
        else:
            last_style = {}


class TableObject(TableBaseObject):
    """ 表格对象 """

    def __init__(self):
        super().__init__()
        self._type = "table"
        self._rows: list[RowObject] = []  # 表格行对象
        self._head_type = 'horizontal'  # 表格表头类型  horizontal：水平 ; vertical: 垂直
        self._head_list = []  # 表格表头所在下标 horizontal时 保存行下标； vertical时保存的列下标
        self._last_coordinate = ''  # 未知作用

    def to_dict(self):
        """
        将 TableObject 对象转换为字典
        """
        res = {
            "type": self._type,
            "rows": [row.to_dict() for row in self._rows],  # 转换每个行对象为字典
            "head_type": self._head_type,
            "head_list": self._head_list,
            **super().to_dict()  # 合并父类的部分属性
        }
        if self._last_coordinate:
            res["last_coordinate"] = self._last_coordinate
        return res

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 TableObject 实例
        """
        obj = cls()
        base_obj = super(TableObject, cls).from_dict(data)

        # Copy attributes initialized by the superclass
        obj._layout = base_obj._layout
        obj._style = base_obj._style
        obj._border = base_obj._border
        obj._coordinate = base_obj._coordinate
        obj._data_id = base_obj._data_id
        obj._type = data.get("type", "table")
        obj._rows = [RowObject.from_dict(row_data) for row_data in data.get("rows", [])]  # 恢复行对象列表
        obj._head_type = data.get("head_type", 'horizontal')
        obj._head_list = data.get("head_list", [])
        if data.get("last_coordinate", ''):
            obj._last_coordinate = data.get("last_coordinate", '')
        return obj

    def __repr__(self):
        return f'{self.__class__.__name__}()[{str(self)}]'

    def __str__(self):
        table_content = ''
        for row in self._rows:
            table_content += self.format_row(row, '\t')
            break
        return table_content

    # def get_data(self):
    #     """精简化输出支持"""
    #     table_data = []
    #     merged_cells = []
    #     annotations = []
    #     runs_styles = []
    #     images = []
    #     for rid, row in enumerate(self.rows):
    #         row_data = []
    #         for cid, cell in enumerate(row.cells):
    #             shapes = []
    #             runs_style_obj = []
    #
    #             for item in cell.content:
    #                 if isinstance(item, (PictureObject, GraphicObject)):
    #                     shapes.append(item)
    #                 elif isinstance(item, TextObject):
    #                     get_text_obj_runs_style(item, runs_style_obj)
    #             content = cell.text
    #             row_data.append(content)
    #             annotation = {"content": cell.comment.text, "row": rid, 'column': cid}
    #             annotations.append(annotation)
    #             if shapes:
    #                 image_dict = {"cell_content": [shape.get_data() for shape in shapes], "row": rid, 'column': cid}
    #                 images.append(image_dict)
    #             if runs_style_obj:
    #                 runs_style = {"run_style": runs_style_obj, "row": rid, 'column': cid}
    #                 runs_styles.append(runs_style)
    #             # 获取有合并的单元格信息
    #             if cell.merged_ranges:
    #                 mr = cell.merged_ranges
    #                 if hasattr(cell, "relative_merged_ranges") and cell.relative_merged_ranges:
    #                     mr = cell.relative_merged_ranges
    #                 merged_cells.append({
    #                     "type": "cell",
    #                     "row_index": rid,
    #                     "col_index": cid,
    #                     "merged_range": mr
    #                 })
    #         table_data.append(row_data)
    #     row_cnt = len(self.rows)
    #     col_cnt = len(self.rows[0].cells)
    #     sub_string = ""
    #     for i in self.head_list:
    #         sub_string += _("表头是第%d行、") % (i + 1)
    #
    #     logging.info(f'start to translate table desc 4: {_("表格有%d行，%d列，%s") % (row_cnt, col_cnt, sub_string[:-1])}')
    #     data = {
    #         'type': 'table',
    #         'content': table_data,
    #         'annotations': annotations,
    #         'runs_style': runs_styles,
    #         'images': images,
    #         'parent_content': self.layout.parent_content,
    #         "row_cnt": row_cnt,
    #         "col_cnt": col_cnt,
    #         "head": self.head_list,
    #         'head_type': self.head_type,
    #         "desc": _("表格有%d行，%d列，%s") % (row_cnt, col_cnt, sub_string[:-1]),
    #         "data_id": self.data_id,
    #         "merged_cells": merged_cells,
    #         "index": self.index if hasattr(self, "index") else -1
    #     }
    #     if self.coordinate.desc:
    #         data["coord"] = self.coordinate.desc
    #     else:
    #         data["page_num"] = self.layout.page_id
    #     return data

    @property
    def rows(self):
        return self._rows

    @rows.setter
    def rows(self, new_value):
        assert type(new_value) == list
        self._rows = new_value

    @property
    def head_type(self):
        return self._head_type

    @head_type.setter
    def head_type(self, new_value):
        assert new_value in ['horizontal', 'vertical']
        self._head_type = new_value

    @property
    def head_list(self):
        return self._head_list

    @head_list.setter
    def head_list(self, new_value):
        assert type(new_value) == list
        self._head_list = new_value

    def get_heads(self):
        """
        获取cell对象对应的head列表
        :return:
        """
        if not self.head_list or not self.rows:
            return []
        heads = []
        if self.head_type == "horizontal":
            for index in self.head_list:
                heads.append(self.rows[index].cells)
        else:
            for index in self.head_list:
                head = []
                for row in self.rows:
                    head.append(row.cells[index])
                heads.append(head)
        return heads

    @staticmethod
    def _get_title(curr_ref):
        if type(curr_ref) == TextObject:
            return curr_ref.text
        elif hasattr(curr_ref, "layout") and curr_ref.layout.prev_ref:
            return TableObject._get_title(curr_ref.layout.prev_ref)
        else:
            return ""

    def get_title(self):
        return TableObject._get_title(self.layout.prev_ref)

    def get_chapter_id(self):
        """ 获取表格对象章节号字符串，
            返回章节id字符串 或者 空（无章节号）
        """
        return getattr(self.layout, "chapter_id", "")

    def is_same_pos(self, table_obj) -> bool:
        """ 判定两个表格对象的位置坐标是否相同 """
        return self.coordinate.desc == table_obj.coordinate.desc

    def get_merged_ranges(self, include_cell_obj=False):
        """
        获取表格内所有存在单元格合并的范围
        :param include_cell_obj: bool类型，False时仅以列表形式返回合并范围元组
            True时返回字典，key为合并范围元组，value为该合并范围内的CellObject对象
        :param return: list or dict
        """
        temp_dict = {}
        row_num = len(self.rows)
        if row_num == 0:  # 空表格
            return {} if include_cell_obj else []
        col_num = len(self.rows[0].cells)
        for r in range(row_num):
            for c in range(col_num):
                cell_obj = self.rows[r].cells[c]
                cell_merge_range = cell_obj.merged_ranges
                # 单元格不存在合并，则跳过
                if not cell_merge_range:
                    continue
                # 存在时，则存入哈希中
                cur_merge_range = tuple(cell_merge_range)
                if cur_merge_range not in temp_dict:
                    temp_dict[cur_merge_range] = [cell_obj]
                else:
                    temp_dict[cur_merge_range].append(cell_obj)
        if not include_cell_obj:
            return list(temp_dict.keys())
        return temp_dict

    def get_col_list(self, col_name=None, col_index=None):
        """
        根据表头内容或者列索引，获取对应列的对象列表
        :param col_name: str类型，列的表头内容
        :param col_index: int类型，列的索引
        return: list
        """
        result = []
        if col_name:
            head_list = self.get_heads()
            head_content_dict = dict()
            for head in head_list:
                for index, item in enumerate(head):
                    if index not in head_content_dict:
                        head_content_dict[index] = str(item.text).strip()
                    else:
                        head_content_dict[index] += str(item.text).strip()
            head_content_list = list(head_content_dict.values())
            for item_index, head_content in enumerate(head_content_list):
                if col_name.strip() in head_content:
                    col_index = item_index
                    break
        if col_index is not None:
            if self.head_type == 'horizontal':
                for r in self.rows:
                    result.append(r.cells[col_index])
            else:
                result.extend(self.rows[col_index].cells)
        return result

class RowObject(TableBaseObject):
    """ 表格行对象 """

    def __init__(self):
        super().__init__()
        self._type = "table_row"
        self._row_index = ''  # 行序号
        self._cells: list[CellObject] = []  # 表格列对象

    def to_dict(self):
        """
        将 RowObject 对象转换为字典
        """
        return {
            "type": self._type,
            "row_index": self._row_index,
            "cells": [cell.to_dict() for cell in self._cells],  # 递归调用 CellObject 的 to_dict 方法
            **super().to_dict()  # 合并父类的部分属性
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 RowObject 实例
        """
        obj = cls()
        base_obj = super(RowObject, cls).from_dict(data)

        # Copy attributes initialized by the superclass
        obj._layout = base_obj._layout
        obj._style = base_obj._style
        obj._border = base_obj._border
        obj._coordinate = base_obj._coordinate
        obj._data_id = base_obj._data_id
        obj._type = data.get("type", "table_row")
        obj._row_index = data.get("row_index", '')
        obj._cells = [CellObject.from_dict(cell_data) for cell_data in data.get("cells", [])]  # 递归恢复 CellObject 的列表

        return obj

    def __repr__(self):
        return f'{self.__class__.__name__}()[{str(self)}]'

    def __str__(self):
        return self.format_row(self, '\t')

    @property
    def row_index(self):
        return self._row_index

    @row_index.setter
    def row_index(self, new_value):
        assert type(new_value) == int
        self._row_index = new_value

    @property
    def cells(self):
        return self._cells

    @cells.setter
    def cells(self, new_value):
        assert type(new_value) == list
        self._cells = new_value
        if new_value:
            self.coordinate.desc = new_value[0].coordinate.desc

    @property
    def position(self):
        first_position = copy.copy(self._cells[0].position)
        for pos in self._cells[1:]:
            first_position.width = first_position.width + pos.position.width
            first_position.height = first_position.height + pos.position.height
        return first_position



class CellObject(TableBaseObject):
    """ 表格的单元格对象 """

    def __init__(self):
        super().__init__()
        self._type = "table_cell"
        self._text = ""  # 内容
        self._row_index = ''  # 行序号
        self._col_index = ''  # 列序号
        self._comment = CommentObject()  # 批注
        self._content = []  # 单元格的内容对象列表
        self._merged_ranges = []  # 合并单元格的范围 [0,0,1,3]
        self._style = StyleObject()  # 样式
        self._position = Position()
        self._is_merged = False
        self._is_merged_start = True

    def to_dict(self):
        """
        将 CellObject 对象转换为字典
        """
        return {
            "type": self._type,
            "text": self._text,
            "row_index": self._row_index,
            "col_index": self._col_index,
            "comment": self._comment.to_dict(),  # 调用 CommentObject 的 to_dict 方法
            "content": [item.to_dict() for item in self._content],  # 递归转换内容对象列表
            "merged_ranges": self._merged_ranges,
            "style": self._style.to_dict(),  # 调用 StyleObject 的 to_dict 方法
            "position": self._position.to_dict(),
            **super().to_dict()  # 合并父类的部分属性
        }

    @classmethod
    def from_dict(cls, data):
        """
        从字典创建 CellObject 实例
        """
        obj = cls()
        base_obj = super(CellObject, cls).from_dict(data)

        # Copy attributes initialized by the superclass
        obj._layout = base_obj._layout
        obj._style = base_obj._style
        obj._border = base_obj._border
        obj._coordinate = base_obj._coordinate
        obj._data_id = base_obj._data_id
        obj._type = data.get("type", "table_cell")
        obj._text = data.get("text", "")
        obj._row_index = data.get("row_index", '')
        obj._col_index = data.get("col_index", '')
        obj._comment = CommentObject.from_dict(data.get("comment", {}))  # 调用 CommentObject 的 from_dict 方法
        obj._content = [TextObject.from_dict(e) if e.get("type") == "text"
                         else PictureObject.from_dict(e) if e.get("type") == "picture"
                         else GraphicObject.from_dict(e) if e.get("type") == "graphic"
                         else e for e in data.get("content", [])]# 递归恢复内容对象列表
        obj._merged_ranges = data.get("merged_ranges", [])
        obj._style = StyleObject.from_dict(data.get("style", {}))  # 调用 StyleObject 的 from_dict 方法
        obj._position = Position.from_dict(data.get("position", {}))
        return obj

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, new_value):
        assert type(new_value) == str
        self._text = new_value

    @property
    def row_index(self):
        return self._row_index

    @row_index.setter
    def row_index(self, new_value):
        assert type(new_value) == int
        self._row_index = new_value

    @property
    def col_index(self):
        return self._col_index

    @col_index.setter
    def col_index(self, new_value):
        assert type(new_value) == int
        self._col_index = new_value

    @property
    def comment(self):
        return self._comment

    @comment.setter
    def comment(self, new_value):
        assert isinstance(new_value, CommentObject)
        self._comment = new_value

    @property
    def content(self):
        return self._content

    @content.setter
    def content(self, new_value):
        assert type(new_value) == list
        self._content = new_value

    @property
    def merged_ranges(self):
        return self._merged_ranges

    @merged_ranges.setter
    def merged_ranges(self, new_value):
        self._merged_ranges = new_value

    @property
    def style(self):
        return self._style

    @style.setter
    def style(self, new_value):
        assert isinstance(new_value, StyleObject)
        self._style = new_value

    @property
    def position(self):
        return self._position

    @position.setter
    def position(self, new_value):
        assert isinstance(new_value, Position)
        self._position = new_value

    def get_heads(self):
        """
        获取cell对象对应的head列表
        :param cell:CellObject对象
        :return:
        """
        # 获取当前单元格所在的表格对象
        row_obj = self.layout.parent_ref
        if row_obj is None:
            return []
        table_ = row_obj.layout.parent_ref
        if table_ is None:
            return []
        if not table_.head_list or not table_.rows:
            return []
        heads = []
        if table_.head_type == "horizontal":
            for index in table_.head_list:
                heads.append(table_.rows[index].cells[self.col_index])
        else:
            for index in table_.head_list:
                heads.append(table_.rows[self.row_index].cells[index])
        return heads

    def get_context(self) -> Context:
        """
        获取cell对象对应的上下文信息(cell所在行，cell所在table的表头和第一行内容的第一列)
        :param cell:CellObject对象
        :return:
        """
        res = Context()
        # 获取当前单元格所在的表格对象
        row_obj = self.layout.parent_ref
        if row_obj is None:
            return res
        res.current_row = "|".join(cell.text for cell in row_obj.cells)
        table_ = row_obj.layout.parent_ref
        if table_ is None:
            return res
        # 默认取第一行为表头
        header_row = table_.rows[0]

        headers = [cell.text for cell in header_row.cells]
        res.header_list = "|".join(headers)
        res.cell_header = headers[int(self.col_index)]
        row_index = int(self.row_index)
        if row_index + 1 < len(table_.rows):
            next_row = table_.rows[row_index + 1]
            res.next_context = "|".join(cell.text for cell in next_row.cells)
        if row_index - 1 > 0:
            pre_row = table_.rows[row_index - 1]
            res.pre_context = "|".join(cell.text for cell in pre_row.cells)
        return res


