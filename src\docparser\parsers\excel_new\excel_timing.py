# -*- coding: utf-8 -*-
"""
@File    : parse_exec_by_config.py
<AUTHOR> 董根虎
@Date    : 2024-07-08 09:18
@Desc    : 时序图 excel解析差分

"""
import logging
import os
import pythoncom
import pywintypes

from docparser.common.base import is_merged
from docparser.models.document import DocumentBlockObject
from docparser.models.text import TextObject
from docparser.models.timing import TimingWaveObject, TimingDataObject, TimingTextObject, TimingTextDataObject
from docparser.parsers.excel.excel_standard import ExcelStandardParser
from docparser.parsers.excel.utils import get_cell_value
from xlwings.utils import column_to_number

class ExcelTimingParser(ExcelStandardParser):

    def __init__(self):
        super().__init__()
        self._sheet_name = "仕様"  # 需要解析的sheet名称（固定）, 以及表格的开始标记
        self._identify_table_border_style = ['thin', 'thick', 'medium']
        self._openpyxl_wb = None
        self._win32com_wb = None
        self._excel_app = None

    def _parse_shapes(self, file_path, exclude_type_list=[]):
        """ 解析图形 """
        return super()._parse_shapes(file_path, [])

    def _parse_sheet(self, sheet, shapes):
        """ 解析一个worksheet中的数据
            :params sheet: Worksheet对象
            :params shapes: shape字典列表
        """
        sheet_name = sheet.title
        wb_with_pywin32 = self._excel_app
        ws_with_pywin32 = wb_with_pywin32.Sheets[sheet.title]
        logging.info(f"parse_excel start sheet_name: {sheet_name}")
        if sheet_name != self._sheet_name:
            # return super()._parse_sheet(sheet, shapes)
            return None
        # 实例化一个sheet对象
        block = DocumentBlockObject()
        block.file_name = os.path.basename(self._file_path)
        # 时序图对象数据列表 + 对应的数据纵向范围
        timing_wave, tim_wave_ranges = self._parse_timing_wave_line(sheet, block, ws_with_pywin32)
        timing_text, tim_text_ranges = self._parse_timing_text_line(sheet, block, ws_with_pywin32)
        text_ranges = self._get_text_ranges(sheet, tim_wave_ranges + tim_text_ranges)
        # 解析当前sheet的图片信息
        pictures = self._parse_picture(sheet_name, block, shapes)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_picture {len(pictures)}")
        # 解析当前sheet的图形信息
        graphics = self._parse_graphic(sheet_name, block, shapes)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_graphic {len(graphics)}")
        # 解析当前sheet的表格外的对象信息
        texts = self._parse_text_single_cell(sheet, block, [], text_ranges, ws_with_pywin32)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_text {len(texts)}")

        block._name = sheet_name
        block.add_text(texts)
        block.add_timing_wave(timing_wave)
        block.add_timing_text(timing_text)
        block.add_graphic(graphics)
        block.add_picture(pictures)
        self._assign_ref(block)
        logging.info(f"parse_excel end sheet_name: {sheet_name}")
        return block
    def _parse_timing_wave_line(self, sheet, block, ws_with_pywin32):
        """
        解析时序图线路数据。

        本函数通过识别电子表格中的特定标记单元格，解析出时序图的布局和数据信息。
        参数:
        - sheet: 工作表对象，代表了要解析的电子表格。
        - block: 布局块对象，表示时序图所属的父级布局块。
        - ws_with_pywin32: 使用了pywin32库的工作表对象，用于获取一些特定的样式信息。

        返回值:
        - tims: 一个包含TimingWaveObject对象的列表，代表解析出的时序图数据。
        - tim_ranges: 一个包含时序图数据范围的列表，每个范围由两个坐标组成。
        """
        # 初始化时序图范围和时序图对象列表
        tim_ranges = []
        tims = []

        # 先查找所有的关键标记单元格
        marker_cells = []
        for idx, row in enumerate(sheet.iter_rows(values_only=False), 1):
            cell = sheet.cell(idx, 2)
            if self._is_wave_start_marker(cell, sheet):
                marker_cells.append((cell.row, cell.column))

        # 输出日志信息，记录找到的标记单元格
        logging.info(f"parse_timing_wave_line marker_cells : {marker_cells}")

        # 获取最大列数，用于后续遍历
        max_column = sheet.max_column

        # 遍历每个标记单元格，解析时序图数据
        for m in marker_cells:
            # 一个marker标记一个TimingWaveObject对象
            tim = TimingWaveObject()
            tims.append(tim)
            tim.layout.parent_ref = block

            # 解析标记单元格的位置，确定时序图数据的范围
            marker_row, marker_col = m
            tim_ranges.append(((marker_row - 5, max_column), (marker_row, max_column)))

            # 获取时序图的1和0状态的平均值
            tim.one_mean = get_cell_value(sheet.cell(marker_row - 2, marker_col - 1), sheet, False)
            tim.zero_mean = get_cell_value(sheet.cell(marker_row, marker_col - 1), sheet, False)

            # 通过标记单元格计算出每行的范围的文本数据
            for row in range(marker_row - 5, marker_row + 1):
                cell_ = sheet.cell(row, 1)
                v = get_cell_value(cell_, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(cell_, ws_with_pywin32)
                    # 构建文本对象
                    t = TextObject()
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = cell_.coordinate
                    tim.content.append(t)

            # 设置时序图的名称和坐标
            if tim.content:
                tim.name = tim.content[0].text
                tim.coordinate.desc = tim.content[0].coordinate.desc

            # 通过标记单元格计算出每行的时序图数据
            for col in range(2, max_column + 1):
                # 获取当前单元格和其下方单元格的底部边框样式
                cell = sheet.cell(marker_row, col)
                bottom_style_ = cell.border.bottom.style
                if not bottom_style_:
                    bottom_style_ = sheet.cell(cell.row + 1, cell.column).border.top.style

                # 获取当前单元格和其上方单元格的顶部边框样式
                cell = sheet.cell(marker_row - 2, col)
                top_style_ = cell.border.top.style
                if not top_style_ and cell.row > 1:
                    top_style_ = sheet.cell(cell.row - 1, cell.column).border.bottom.style

                # 创建时序数据对象
                td = TimingDataObject()
                td.layout.parent_ref = tim
                td.coordinate.desc = cell.coordinate  # 坐标可能不准确
                td.index = col - 2

                # 根据边框样式确定时序数据的值
                if bottom_style_ in self._identify_table_border_style:
                    td.data = 0
                if top_style_ in self._identify_table_border_style:
                    td.data = 1

                # 将时序数据添加到时序图对象中
                tim.data.append(td)

        # 返回解析出的时序图数据和范围
        return tims, tim_ranges

    def _parse_timing_text_line(self, sheet, block, ws_with_pywin32):
        """
        解析时序图文本行。

        从给定的工作表中提取时序图文本数据和对应的范围。该函数通过识别特定的标记单元格来定位和构建时序图对象。

        参数:
        - sheet: 工作表对象，包含时序图数据。
        - block: 时序图对象数据的父引用。
        - ws_with_pywin32: 使用pywin32库的工作表对象，用于获取单元格样式。

        返回:
        - tims: 一个包含TimingTextObject对象的列表，表示提取的时序图文本数据。
        - tim_ranges: 一个包含时序图数据范围的列表，每个范围表示为对角坐标。
        """
        # 初始化时序图范围和时序图对象列表
        tim_ranges = []
        tims = []

        # 先查找所有的关键标记单元格
        marker_cells = []
        for idx, row in enumerate(sheet.iter_rows(values_only=False), 1):
            cell = sheet.cell(idx, 2)
            if self._is_text_start_marker(cell, sheet):
                marker_cells.append((cell.row, cell.column))

        # 日志记录找到的标记单元格
        logging.info(f"parse_timing_text_line marker_cells : {marker_cells}")

        # 获取工作表的最大行数和列数
        max_row = sheet.max_row
        max_column = sheet.max_column

        # 遍历每个标记单元格来构建时序图对象
        for m in marker_cells:
            # 一个marker标记一个TimingTextObject对象
            tim = TimingTextObject()
            tims.append(tim)
            tim.layout.parent_ref = block

            # 解析标记单元格的位置
            marker_row, marker_col = m
            # 添加时序图对象的范围
            tim_ranges.append(((marker_row, max_column), (marker_row, max_column)))

            # 通过标记单元格计算出每行的范围的文本数据
            for col in range(marker_col, max_column + 1):
                cell_ = sheet.cell(marker_row, col)
                v = get_cell_value(cell_, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(cell_, ws_with_pywin32)
                    # 构建文本对象
                    t = TimingTextDataObject()
                    if sheet.merged_cells:
                        for r in sheet.merged_cells.ranges:
                            if r.min_row <= cell_.row <= r.max_row and r.min_col <= cell_.column <= r.max_col:
                                t.length = r.max_col - r.min_col + 1
                                break
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = cell_.coordinate
                    tim.data.append(t)

            # 设置唯一标识： 取值第一列的内容
            text_key_cell = sheet.cell(marker_row, 1)
            tim.name = get_cell_value(text_key_cell, sheet, False)
            tim.coordinate.desc = text_key_cell.coordinate

            # 处理合并单元格的情况
            if not tim.name and is_merged(sheet.cell(marker_row, marker_col), sheet):
                for r in sheet.merged_cells.ranges:
                    if r.min_row <= marker_row <= r.max_row and r.min_col <= marker_col <= r.max_col:
                        for row in range(r.min_row, r.max_row):
                            text_key_cell = sheet.cell(row, 1)
                            v = get_cell_value(text_key_cell, sheet, False)
                            if v:
                                tim.name = v
                                tim.coordinate.desc = text_key_cell.coordinate
                                break
                    if tim.name:
                        break

            # 构建时序图对象的内容文本
            if text_key_cell:
                v = get_cell_value(text_key_cell, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(text_key_cell, ws_with_pywin32)
                    # 构建文本对象
                    t = TextObject()
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = text_key_cell.coordinate
                    tim.content.append(t)
        # 返回时序图对象列表和范围
        return tims, tim_ranges

    def _get_text_ranges(self, sheet, timing_ranges):
        max_row = sheet.max_row
        max_column = sheet.max_column

        # 从timing_ranges 中查找最小行，最大行
        timing_min_row = 1
        timing_max_row = 1
        for tr in timing_ranges:
            (min_row, _), (marker_row, _) = tr
            if timing_min_row == 1:
                timing_min_row = min_row
            if min_row < timing_min_row:
                timing_min_row = min_row
            if marker_row > timing_max_row:
                timing_max_row = marker_row

        # 计算出文本对象的范围（除了时序图数据范围的内容）
        text_ranges = set()
        for x in range(1, timing_min_row):
            for y in range(1, max_column):
                text_ranges.add((x, y))
        for x in range(timing_max_row + 1, max_row):
            for y in range(1, max_column):
                text_ranges.add((x, y))
        # 新增两个特殊单元格
        y = column_to_number("DH")
        text_ranges.add((54, y))
        text_ranges.add((56, y))
        return text_ranges

    def _is_wave_start_marker(self, cell, sheet):
        """
        单元格是否拥有行波形图行的开始标记
        内容为空，B列，无上边框，有下边框，非合並單元格
        """
        value = get_cell_value(cell, sheet, False)
        style_top = cell.border.top.style
        style_bottom = cell.border.bottom.style
        if not style_bottom and cell.row < sheet.max_row:
            style_bottom = sheet.cell(cell.row + 1, cell.column).border.top.style
        next_cell = sheet.cell(cell.row + 1, cell.column)
        return (not value
                and 2 == cell.column
                and (not style_top or style_top not in self._identify_table_border_style)
                and (style_bottom in self._identify_table_border_style)
                and not is_merged(cell, sheet)
                and not next_cell.value)

    def _is_text_start_marker(self, cell, sheet):
        """
        单元格是否拥有行文本行的开始标记
        内容不为空，B列，有上边框
        """
        value = get_cell_value(cell, sheet, False)
        style_top = cell.border.top.style
        if not style_top and cell.row > 1:
            style_top = sheet.cell(cell.row - 1, cell.column).border.bottom.style
        return (value
                and cell.row > 1
                and 2 == cell.column
                and (style_top in self._identify_table_border_style))

    def parse_document(self) -> (list[DocumentBlockObject]):
        # # 使用win32com解析图形+图片
        shapes = self._parse_shapes(self._file_path)
        workbook = self._openpyxl_wb
        blocks = []
        for n in workbook.sheetnames:
            sheet_data = self._parse_sheet(workbook[n], shapes)
            if sheet_data:
                blocks.append(sheet_data)
        return blocks
