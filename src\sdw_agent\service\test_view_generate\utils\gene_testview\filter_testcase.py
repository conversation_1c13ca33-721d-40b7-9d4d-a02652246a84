import json
import os
import MeCab
import time
import asyncio
import random
from typing import List, Dict, Any

from anytree import PreOrderIter

from sdw_agent.util.local_file_read_save import read_pkl
from sdw_agent.llm.openai_qwen import OpenAIQwen


def _get_clean_node(node):
    has_value = hasattr(node, "value")
    has_children = bool(node.children)
    # 只有children
    if not has_value and has_children:
        return {child.name: _get_clean_node(child) for child in node.children}
    # 只有value
    if has_value and not has_children:
        return node.value
    # 两者都有
    if has_value and has_children:
        if isinstance(node.value, dict):
            merged = node.value.copy()
            merged.update(
                {child.name: _get_clean_node(child) for child in node.children}
            )
            return merged
        else:
            return [
                node.value,
                {child.name: _get_clean_node(child)
                 for child in node.children},
            ]
    return None


def _tokenize(text):
    tagger = MeCab.Tagger("-Owakati")
    return set(tagger.parse(text).strip().split())


def get_node_info(data, function_name):
    # 根据功能点名称获取测试用例和父节点，兄弟节点信息
    # 关键字匹配
    target_node = None
    node_name = [node.name for node in PreOrderIter(data) if node.depth == 2]
    print(node_name)
    # temp=[]
    target_key = "".join(c for c in function_name if c.isalpha() or c.isspace())
    for node in PreOrderIter(data):
        node_key = "".join(c for c in node.name if c.isalpha() or c.isspace())
        if node.depth == 2 and (_tokenize(target_key) & _tokenize(node_key)):
            # temp.append(node.name)
            # print(temp)
            target_node = node
            break
    # 大模型筛选
    # filterclass = FilterTestCase()
    # class_res = asyncio.run(filterclass.match_function_name(function_name, node_name))
    # print(class_res)

    if not target_node:
        return {
            "function": {},
            "father": {},
            "sibling_function_point": {},
        }

    parent_info = {}
    if target_node.parent:
        parent_info = {target_node.parent.name: _get_clean_node(target_node.parent)}

    sibling_info = {}
    if target_node.parent:
        for sibling in target_node.parent.children:
            if sibling != target_node:
                sibling_info[sibling.name] = _get_clean_node(sibling)

    target_info = {target_node.name: _get_clean_node(target_node)}

    return {
        "function": target_info,
        "father": parent_info,
        "sibling_function_point": sibling_info,
    }


def _find_related_nodes_info(node_info, target_name):
    # 在sibling_info中查找与target_name相关的节点

    related_nodes = {}

    # 提取目标节点的关键信息（去掉特殊字符和数字）
    target_key = "".join(c for c in target_name if c.isalpha() or c.isspace())

    for sibling_name, sibling_content in node_info["sibling_function_point"].items():
        # 提取同级节点的关键信息
        sibling_key = "".join(
            c for c in sibling_name if c.isalpha() or c.isspace())

        # 如果两个节点名称有共同的关键词，认为是相关节点
        if _tokenize(target_key) & _tokenize(sibling_key):
            related_nodes[sibling_name] = sibling_content

    return related_nodes


def process_node_info(node_info):
    # 处理get_node_info的返回值，添加相关节点信息

    # 获取目标节点名称
    target_name = list(node_info["function"].keys())[0]

    # 查找相关节点
    related_nodes = _find_related_nodes_info(node_info, target_name)

    # 只返回指定的三个字段
    return {
        "function": node_info["function"],
        "father": [],
        "same_level": related_nodes,
    }


def flatten_node_info(node_info: dict) -> dict:
    def collect_leaf_dicts(obj):
        result = []
        if isinstance(obj, dict):
            for v in obj.values():
                result.extend(collect_leaf_dicts(v))
        elif isinstance(obj, list):
            for item in obj:
                result.extend(collect_leaf_dicts(item))
        else:
            # 非dict/list，忽略
            pass
        # 如果本身就是一个叶子dict（即所有value都不是dict/list）
        if isinstance(obj, dict) and all(not isinstance(v, (dict, list)) for v in obj.values()):
            result.append(obj)
        return result

    flat = {}
    for k, v in node_info.items():
        flat[k] = collect_leaf_dicts(v)
    return flat


class FilterTestCase:
    def __init__(self):
        self.llm = OpenAIQwen(
            base_url="http://172.30.19.113:11400/v1", api_key="sk-xxx"
        )
        self.batch_size = 20  # Number of test cases per batch

    def _split_into_batches(self, test_case: Dict[str, Any]) -> List[Dict[str, Any]]:

        batches = []
        function = test_case.get("function", [])
        # father = test_case.get("father", [])
        related_function_point = test_case.get("same_level", [])

        for i in range(0, len(related_function_point), self.batch_size):
            batch = {
                "function": function,  # Keep function intact
                # "father": father[i:i + self.batch_size],
                "same_level": related_function_point[i:i + self.batch_size]
            }
            batches.append(batch)

        return batches

    # @retry(wait=wait_random_exponential(min=1, max=60),stop=stop_after_attempt(3))
    async def testcase_extract(self, test_case):

        # Split data into batches
        batches = self._split_into_batches(test_case)
        all_results = []

        for batch in batches:
            token_size = len(str(batch))
            print(f"Batch token size: {token_size}")
            # 拆分prompt为system和user部分
            system_message = "你是一名汽车项目测试人员，熟悉的了解汽车的每个功能模块。"
            user_message = '''
                <requirement>
                    1. 理解<input>中的\"function\"表下的确认点，分析测试用例想要测试的功能，从\"same_level\"表中筛选出确认点与其相关联的测试用例 
                    2. 需要通过\"same_level\"表中的"确认点"，选出为确认点为表示类别的测试用例，其他类别的测试用例确保不要选择，只需要返回10个最相关联的测试用例的示例编号
                    3. 必须严格按照<output_structure>格式返回一个列表，包含测试用例的示例编号，只需要返回10个最相关联的测试用例的示例编号
                    4. 列表中不要包含\"function\"表中的示例编号，只包含\"same_level\"表中的示例编号，只需要返回10个最相关联的测试用例的示例编号
                    5. [关联分析] 
                    - 解析"function"表中测试用例的功能描述
                    - 识别其涉及的显示要素（位置/时机/效果/状态）
                    - 在"same_level"表中定位具有相同显示要素的测试用例

                    6. [确认点过滤]
                    - 严格应用知识库的分类标准
                    - 仅保留"确认点"字段为"表示类"的测试用例
                    - 排除所有其他类别（内部计算/迁移/通信/异常/其他）
                    7. 只需要返回10个最相关联的测试用例的示例编号
                </requirement>
                <knowledge>
                    1. 在input中有两个list，第一个表为\"function\"，该表里面的内容为需要使用的测试用例集，第二个表为\"same_level\"，是待筛选的测试用例集

                    2. 其他类别（严格排除）
                    ❌ 内部计算：涉及信号处理算法
                    ❌ 迁移类：画面切换逻辑
                    ❌ 通信类：CAN信号交互
                    ❌ 异常类：电源/CAN异常处理
                    ❌ 其他：未归类测试点

                    3. 测试用例分为6个类别：内部计算类、表示类、画面迁移类、CAN出力类、异常系（CAN/电源电压）类、其他类；
                    *内部计算类：指数据在程序内部处理的逻辑，例如：仪表车速=实际车速/车辆补正率+指示偏差，输入是实际车速，经过程序内部处理后，输出仪表车速；
                    *表示类：指仪表的画面显示，包括是否显示，显示的时机，显示的位置，颜色，效果等；
                    *画面迁移类：指显示从一个画面切换到另一个画面的过程，可以是整个仪表画面的切换，例如大中断，也可以是仪表局部画面切换；
                    *CAN出力类：指仪表在工作过程中，需要对外发送当前仪表的状态CAN信号，例如：车速值，ODO值，按键是否按下，需要考虑出力值，出力时机，出力周期是否正确；
                    *异常系（CAN/电源电压）类：指CAN或者电压发生异常时，仪表处理逻辑，例如CAN信号途绝后，仪表的动作；BAT，IGN重启后，仪表的动作；
                    *其他类：指上述以外的测试观点；
                </knowledge>
                <input>
                    {input}
                </input>
                <output_structure>
                    ['示例'：[1, 2, 3]]
                </output_structure>
                <example>
                    ['示例'：[1, 2, 3, 5, 6 ,7]]
                </example>
                <language>
                    请使用中文输出。
                </language>
                '''.replace("{input}", json.dumps(batch, ensure_ascii=False))
            # 拼接system和user内容
            prompt = system_message + "\n" + user_message
            for _ in range(5):
                print(f"第{_}次调用")
                response_res = await self.llm.generate(prompt)
                if len(response_res) != 0:
                    all_results.append(response_res)
                    break
                else:
                    print("大模型调用失败，等待10s")
                    time.sleep(10)
                    # Combine all batch results
        return all_results

    async def match_function_name(self, function, node_names):

        prompt = """
            <instruction>
            你是一名资深汽车项目研发工程师，熟悉整车各功能模块的架构与测试规范。请基于以下要求进行精准匹配：
            </instruction>

            <task>
            1. 任务解析：分析测试用例名称{function}的含义
            2. 匹配逻辑：在给定的节点名称列表{node_names}中找出与{function}最相关的节点名称
            3. 输出要求：返回匹配度最高的节点名称及其索引位置
            </task>

            <constraints>
            ❗️输出必须满足：
            1. 
            `value`字段必须与`node_names`中的某一项**完全一致**
            2. 禁止返回列表外的任何内容（如解释性文本或新生成的名称）
            3. 禁止添加额外字段或修改JSON结构
            4. 即使认为所有选项都不相关，也必须返回列表中**最接近**的一项
            </constraints>

            """
        message_input = prompt.replace("{{function}}", json.dumps(function, ensure_ascii=False))
        message_input = prompt.replace("{{node_names}}", json.dumps(node_names, ensure_ascii=False))
        response_res = await self.llm.generate(message_input)
        for _ in range(5):
            print(f"第{_}次调用")
            response_res = await self.llm.generate(message_input)
            if len(response_res) != 0:
                # all_results.append(response_res)
                break
            else:
                print("大模型调用失败，等待10s")
                time.sleep(10)
                # Combine all batch results
        return response_res


def filter_cases_by_numbers(numbers, data, key='same_level'):
    number_set = set(str(n) for n in numbers)
    cases = data.get(key, [])
    filtered = [case for case in cases if str(case.get('示例')) in number_set]
    data['same_level'] = filtered
    return data


async def get_filtered_testcase(pkl, function):
    # 读取.pkl文件
    data = read_pkl(pkl)
    # 根据function找出匹配的测试用例和父节点，兄弟节点用例
    node_info = get_node_info(data, function)
    # print(node_info)
    # 找出相关联的兄弟节点
    result = process_node_info(node_info)
    # 格式转换
    flat_result = flatten_node_info(result)
    if len(flat_result['same_level']) != 0:
        filterclass = FilterTestCase()
        class_res = await filterclass.testcase_extract(flat_result)
        new_list = []
        final_list = []
        for item in class_res:
            for num in item.get('示例', []):
                new_list.append(num)
        unique_list = sorted(set(new_list))
        if len(unique_list) >= 10:
            final_list = random.sample(unique_list, 10)
        else:
            final_list = unique_list
        res = filter_cases_by_numbers(final_list, flat_result, key='same_level')
        return res
    return flat_result


if __name__ == "__main__":
    # result = read_pkl("../data/testcases_.pkl")
    # for pre, _, node in RenderTree(result):
    #     value_str = f" = {node.value}" if hasattr(node, 'value') else ""
    #     print(f"{pre}{node.name}{value_str}")
    # print(result)
    result = get_filtered_testcase(pkl="../data/testcases_.pkl", function='1500W')

    output_dir = "./data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_file = os.path.join(
        output_dir, "filtered_testcase.json")
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"\n结果已保存至: {output_file}")