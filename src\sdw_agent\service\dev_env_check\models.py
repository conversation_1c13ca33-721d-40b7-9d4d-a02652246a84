"""
Warning Code Generation Service Models
警告代码生成服务数据模型
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


@dataclass
class PackageFileInfo:
    """软件包文件信息"""
    name: str
    path: str
    size: int
    modified_time: str
    checksum: str = ""


@dataclass
class FTPConfig:
    """FTP连接配置"""
    host: str
    user: str
    password: str
    port: int = 21


@dataclass
class EnvCheckSSHConfig:
    """SSH连接配置"""
    hostname: str
    port: int = 22
    username: str = ""
    password: str = ""
    private_key_path: str = ""
    timeout: int = 30
    connect_timeout: int = 10


@dataclass
class PackageFileCompareInfo:
    """软件包文件信息"""
    pre_pack_path: str = ""
    after_pack_path: str = ""
    pre_package_save_path: str = ""
    after_package_save_path: str = ""
    output_save_path: str = ""
    pre_manifest_path: str = ""
    after_manifest_path: str = ""
    pre_jenkins_script_path: str = ""
    after_jenkins_script_path: str = ""


@dataclass
class CompareResult:
    """对比结果"""
    bin_compare_excel_path: str = ""
    manifest_compare_excel_path: str = ""
    jenkins_script_compare_excel_path: str = ""
    genetate_status: bool = False