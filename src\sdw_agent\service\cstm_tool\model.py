"""
CSTM 配置工具工作流

V字对应：
2.1 基本設計 ソフトウェア設計書作成（I/F含む） cstm 配置工具自动化生成

模块简介和主要功能说明：
用户从要件一览表中指定待分析的变更点，自动匹配相关SCL文件中与变更点相关的内容，解析变更前后CSTM原始式样书
从式样书中定位到与变更点相关的选项信息，通过对比变更前后式样书内容，得到CSTM 变更类型，根据变更内容自动更新CSTM配置工具文件
（目前支持：选项属性变更，选项位置变更，选项新增，选项删除，画面追加，画面消除场景）

主要功能：
1. 根据用户所选变更点从SCL文件中匹配出相关变更内容
2. 解析CSTM 原始式样书输出结构化文件
3. 对比分析变更前后原始式样书，分析变更类型
4. 自动化更新CSTM 配置工具表，并生成更新后的代码
"""
import os
import traceback
from dataclasses import dataclass
from loguru import logger
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field, field_validator

from sdw_agent.service.cstm_tool.utils.cstm_utils import CstmUtils
from sdw_agent.service.cstm_tool.utils.config_manager import config_manager


class ConfigModel(BaseModel):
    """工作流配置模型"""
    name: str = Field("通信故障安全CS工作流", description="工作流名称")
    description: str = Field("分析代码变更中的CAN信号匹配", description="工作流描述")
    version: str = Field("1.0.0", description="版本号")
    author: str = Field("qiliang_zhou", description="作者")

    # 处理配置
    max_workers: int = Field(10, ge=1, le=20, description="最大并发线程数")
    default_case_sensitive: bool = Field(True, description="默认是否区分大小写")

    # 输出配置
    output_format: str = Field("xlsx", description="输出文件格式")
    include_summary: bool = Field(True, description="是否包含摘要信息")

    # Gerrit配置（从环境变量获取）
    gerrit_enabled: bool = Field(True, description="是否启用Gerrit集成")

    # 文件处理配置
    supported_file_extensions: List[str] = Field(
        default=[".c", ".cpp", ".h", ".hpp"],
        description="支持的文件扩展名"
    )

    @field_validator('max_workers')
    @classmethod
    def validate_max_workers(cls, v):
        """验证最大工作线程数"""
        if v < 1 or v > 20:
            raise ValueError("最大工作线程数必须在1-20之间")
        return v


class InputDataModel(BaseModel):
    """工作流输入数据模型"""
    # 要件一览表概要信息  变更点描述
    change_summary: str = Field(..., description="要件一览表概要")
    # SCl 文件路径
    scl_path: str = Field(..., description="SCL 文件路径")
    # 原始式样书路径
    before_cstm: str = Field(..., description="变更前原始式样书路径")
    after_cstm: str = Field(..., description="变更后原始式样书路径")
    # base cstm 配置工具
    base_cstm: str = Field(..., description="base cstm 配置工具路径")


    @field_validator('scl_path')
    @classmethod
    def validate_scl_path(cls, v):
        """验证scl_path文件路径"""
        if not os.path.exists(v):
            raise ValueError("scl 路径不存在")
        return v

    @field_validator('before_cstm')
    @classmethod
    def validate_before_cstm(cls, v):
        """验证before_cstm文件路径"""
        if not os.path.exists(v):
            raise ValueError("变更前原始式样书路径不存在")
        return v

    @field_validator('after_cstm')
    @classmethod
    def validate_after_cstm(cls, v):
        """验证after_cstm文件路径"""
        if not os.path.exists(v):
            raise ValueError("变更后原始式样书路径不存在")
        return v

    @field_validator('base_cstm')
    @classmethod
    def validate_base_cstm(cls, v):
        """验证base_cstm文件路径"""
        if not os.path.exists(v):
            raise ValueError("变更前cstm tool文件路径不存在")
        return v


# 方案一: 构造类来表示Excel表格中的数据
class CusOptInfo:
    # 表示选项信息
    def __init__(self):
        self.CusOptNum = 0  # H列: 每一个子选项id号， 从0开始
        self.OptLabel = ""  # I列: 选项标签，用于显示
        self.OptDef = ""  # J列: 选项定义，用于生成宏定义名称
        self.OptType = ""  # K列: 选项类型
        self.OptOrder = ""  # L列: 选项顺序，用于计算支持位值，值n对应2^(n-1)
        self.OptSupbit = ""  # M列: 支持位顺序，用于确定支持位的生成顺序

        # 下一个内容相关的字段
        self.NextCnttLabel = ""  # N列: 下一个内容的标签
        self.NextCnttDef = ""  # O列: 下一个内容的定义
        self.NextCnttSpbEnb = ""  # P列: 下一个内容的使能标志

        # 生成函数相关的字段
        self.CnttGetCon = ""  # S列: 获取内容的函数
        self.CnttSetCon = ""  # T列: 设置内容的函数
        self.CnttCancel = ""  # U列: 取消函数接口
        self.CnttGetConvert = ""  # V列: 获取设定值数量
        self.CnttSetConvert = ""  # W列: 发送设定值数量
        self.CnttTableGet = ""  # X列: 获取设定值表
        self.CnttTableSet = ""  # Y列: 发送设定值表

        # 自定义的字段
        self.row_index = -1  # 记录当前行的索引
        self.has_next_cntt = False  # 是否存在下一个内容


class CusOptCnttInfo:
    # 1-6阶层主数据结构，包含内容和选项的综合信息
    def __init__(self):
        self.Level = ""  # B列: 阶层
        self.Label = ""  # C列: 画面名称
        self.Definit = ""  # D列: 宏定义
        self.Main = ""  # E列: 上一阶层画面ID
        self.Secondary = ""  # F列: 上一阶层位置
        self.Position = ""  # G列: 第0阶层位置
        # self.parent_label = ""  # 添加父级标签属性，用于处理车辆支持位
        self.used_supbits = []  # 已使用的支持位集合

        self.link_type = ""  # Q列: 链接类型
        self.eco_wallet = ""  # R列: ECOWALLET

        # self.CusOptInfo = [CusOptInfo() for _ in range(50)]  # 预分配50个选项
        self.cus_opt_info_list = []  # 存储CusOptInfo的列表

        self.lines_num = 0  # 存储行数, 如果有add的这里会加1

        self.logger = logger.bind(name="CusOptCnttInfo")

    def get_next_available_supbit(self):
        """获取下一个可用的支持位"""
        # 过滤掉0值，从1开始计算
        opt_supbit_list = [x for x in self.used_supbits if x > 0]
        opt_supbit_list.sort()
        new_opt_supbit = 1
        if len(opt_supbit_list) != 0:
            for i in range(1, len(opt_supbit_list) + 1):
                if i not in opt_supbit_list:
                    new_opt_supbit = i
                    break
            if new_opt_supbit == 1:
                new_opt_supbit = opt_supbit_list[-1] + 1
        self.used_supbits.append(new_opt_supbit)
        return new_opt_supbit

    def release_supbit(self, supbit):
        """释放一个支持位"""
        try:
            supbit_value = int(supbit)
            if supbit_value in self.used_supbits:
                self.used_supbits.remove(supbit_value)
        except (ValueError, TypeError) as e:
            self.logger.error(f"释放支持位失败，支持位不是整数: {traceback.format_exc()}")
            raise ValueError(
                # f"{gettext('画面释放支持位失败:')}{self.Label}','{gettext('支持位不是整数:')}{supbit}"
                f"画面释放支持位失败:{self.Label},支持位不是整数:{supbit}"
            )


class ChangeSummaryFormat(BaseModel):
    """
    提取变更内容
    """
    change_content_no: int = Field(
        description="遍历变更内容列表每一个value值与变更内容概述语义匹配，输出变更内容列表中最接近变更内容概述内容的一个值在变更内容列表中的key")
    think_content: str = Field(
        description="输出你的思考过程")


class CstmMetaInfo:
    '''
    阶层num、起始行、结束行、起始列、结束列
    '''
    def __init__(self, start_row, end_row, start_column, end_column, level_num, sheet, origin_sheet):
        self.level_num = level_num
        self.start_row = start_row
        self.end_row = end_row
        self.start_column = start_column
        self.end_column = end_column
        self.resolve_sheet = sheet
        self.origin_sheet = origin_sheet


class Cstmcnttinfo:
    '''
    解析原始式样书时使用
    记录画面信息
    '''
    def __init__(self, cnttname):
        # CSTM画面名
        self.cstm_level = 0  # 1：画面阶层
        self.cnttname = cnttname  # 2：画面名
        self.cnttname_bf = cnttname  # 画面名变更前的名称
        self.optname = []  # 3：选项名
        self.optname_bf = []  # 选项名变更前的名称
        self.optcoordinate = [[], []]  # 4：选项坐标
        self.opttype = []  # 5：选项type
        self.cntt_pre = ''  # 6：上一阶层画面
        self.opt_pre = ''  # 7：上一阶层选项
        self.nextcntt = []  # 8：下一阶层画面
        self.SpdEnblJdg = []  # 9：走行可否
        self.id = []  # 10:待更新选项列表
        self.updata_type = []  # 11：更新方式（新增/变更/删除）
        self.updata_cnttname = ''  # 12：更新画面名用
        self.coordinate = None  # 13: 单元格定位使用
        self.enter_sw = []  # 14: 有効Enter-SW操作
        self.opt_selec_supplement = []  # 15 項目選択時の補足説明
        self.on_off = []  # 16 ON/OFF 設定表示
        self.linked_excel_name = []  # 17 19PFver3適用仕様書
        self.opt_coordinates = []  # 18 记录选项的坐标位置
        self.col_range = []     #19 记录画面的起始列
        self.row_range = []     #20 记录画面的起始行
        self.cntt_name_updated_flag = False  #22 记录画面名称是否存在删除线，若有表示被更新
        self.opt_name_updated_flag = []  #23 记录选项名称是否存在删除线，若有表示被更新
        self.uuid = ""          # 21 画面唯一标志符



class MacroDefFormat(BaseModel):
    """
    调用大模型生成宏定义函数的时候 结构化输出
    """
    result: str = Field(description="输出宏定义内容")


class HasSimilarTextFormat(BaseModel):
    """
    调用大模型判断指定文本中是否包含语义相近词汇  结构化输出
    """
    result: str = Field(description="输出文本片段中是否包含指定词汇或者与指定词汇含义相近的文本片段，如果包含则输出True, 否则输出False.")


# 映射字典已移动到配置文件中，通过config_manager读取
@dataclass
class ScreenHierarchyItem:
    """Screen hierarchy item data class for ECO fuel economy graph information"""
    function_name: str  # 功能 - Function name
    display_order: str  # 表示順序 - Display order
    hierarchy_level: str  # 階層 - Hierarchy level
    title: str  # タイトル - Title
    # icon: str = field(repr=False)  # アイコン - Icon (excluded from CSV export)
    item_name: str  # 項目 - Item name
    enter_sw_operation: str  # 有効Enter-SW操作 - Valid Enter-SW operation
    item_description: str  # 項目選択時の補足説明 - Item selection supplementary explanation
    on_off_display: str  # ON/OFF設定表示 - ON/OFF setting display
    switch_type: str  # 切替Type - Switch type
    operable_while_driving: str  # 走行中操作可能 - Operable while driving
    # display_init: str  # 表示設定初期化 - Display setting initialization
    # default_value: str  # 初期値 - Default value
    # meter_eeprom: str  # メータEEPROM記憶 - Meter EEPROM memory
    spec_document: str  # 19PFver3適用仕様書 - Specification document
    path: str = ""  # 完整的层级路径
    name_has_changed: bool = False  # 名称是否变更
    bf_opt_name: str = ""  # 变更前的选项名称

    @classmethod
    def from_row(cls, row: dict, previous_items = None):
        """Create an object from a table row"""
        # Map original headers to class field names
        header_to_field = {
            'opt_name': 'function_name',
            'opt_index': 'display_order',
            'cstm_level': 'hierarchy_level',
            'belong_cntt': 'title',
            'enter_sw': 'enter_sw_operation',
            'opt_select_supplement': 'item_description',
            'on_off': 'on_off_display',
            'opt_type': 'switch_type',
            'spd_enbl_jdg': 'operable_while_driving',
            'linked_excel': 'spec_document',
            'opt_name_updated_flag':'name_has_changed',
            'bf_opt_name': 'bf_opt_name'
        }

        # Create empty data dictionary with default empty strings
        data = {field: "" for field in header_to_field.values()}

        # Fill in the data from the dict
        for key, value in row.items():
            value = str(value)
            field = header_to_field.get(key)
            if field:
                if key in ['opt_name', 'belong_cntt']:
                    value = CstmUtils.clean_title(value)
                if key == 'opt_name':
                    data['item_name'] = value.strip()

                data[field] = value.strip()

        # 创建实例
        instance = cls(**data)

        # 'function_name', 'icon', 'display_init', 'default_value', and 'meter_eeprom'
        # 计算完整路径
        instance.path = ScreenHierarchyItem.calculate_item_path(instance, previous_items)

        return instance

    @classmethod
    def calculate_item_path(cls, current_item, previous_items = None) -> str:
        """
        计算当前项目的完整层级路径

        参数:
            current_item: 当前项目
            previous_items: 之前的项目列表

        返回:
            str: 完整的层级路径，格式为：👍Menu👍path1👍path2👍...👍target_item
        """
        
        if not previous_items:
            previous_items = []

        # 从配置文件中获取映射字典
        screen_hierarchy_config = config_manager.config.get('workflow_config', {}).get('screen_hierarchy', {})
        item_name_map = screen_hierarchy_config.get('item_name_map', {})
        title_map = screen_hierarchy_config.get('title_map', {})

        # 获取当前项目的层级数字
        current_level = current_item.hierarchy_level
        current_level = int(current_level)

        # 初始化路径部分列表
        # path_parts = ['Menu']  # 第0层固定为Menu
        path_parts = []  # 第0层固定为Menu

        # 从之前的项目中找到父级路径
        for prev_item in reversed(previous_items):
            # prev_level = int(level_map.get(prev_item.hierarchy_level, '0'))
            prev_level = int(prev_item.hierarchy_level)

            # 如果找到直接父级
            if prev_level == max(current_level - 1, 0):
                # 获取父级的路径部分
                parent_parts = prev_item.path.split('👍')
                # 使用父级的路径部分
                path_parts = parent_parts
                break

        # 添加当前项目的标题（如果不为空）
        if current_item.title and (not path_parts or current_item.title != path_parts[-1]):
            if current_item.title in title_map:
                path_parts.append(title_map[current_item.title])
            else:
                path_parts.append(current_item.title)

        # 添加当前项目的名称（如果不为空且与标题不同）
        # if current_item.item_name and current_item.item_name != current_item.title:
        if current_item.item_name:
            if current_item.item_name in item_name_map:
                path_parts.append(item_name_map[current_item.item_name])
            else:
                path_parts.append(current_item.item_name)

        # 用👍连接所有路径部分
        return '👍'.join(path_parts)