# 要件设计书照合实施工作流

## 概述

要件设计书照合实施工作流用于检查Excel文件中要件内容对应的设计书信息完整性，生成详细的检查报告。

## 功能特性

- **Excel数据提取**: 从指定工作表提取要件内容和设计书信息
- **完整性检查**: 检查设计书信息是否完整（非空、长度符合要求等）
- **智能停止**: 连续空行时自动停止检查，避免无效处理
- **详细报告**: 生成包含汇总信息、检查结果和统计数据的Excel报告
- **样式美化**: 自动添加颜色标识和格式化，提高可读性
- **配置灵活**: 支持自定义检查规则和输出格式

## 工作流程

1. **输入验证**: 验证Excel文件路径和格式
2. **数据提取**: 从指定工作表提取要件和设计书数据
3. **完整性检查**: 按配置规则检查设计书信息
4. **结果导出**: 生成格式化的Excel检查报告
5. **统计汇总**: 提供详细的统计信息

## 配置说明

### 基础配置
- `target_sheet`: 目标工作表名称（默认：機能一覧と新規・変更内容）
- `header_start_row`: 表头开始行（默认：5）
- `data_start_row`: 数据开始行（默认：8）
- `req_content_column`: 要件内容列名（默认：要件の内容）
- `design_book_column`: 设计书列名（默认：パラメータ設計書）

### 检查规则
- `consecutive_empty_limit`: 连续空行停止限制（默认：5）
- `min_design_book_length`: 设计书信息最小长度（默认：3）

### 输出配置
- `output_subdir`: 输出子目录名（默认：check_results）
- `include_summary`: 是否包含汇总信息（默认：true）
- `add_styling`: 是否添加样式（默认：true）

## 使用示例

### 通过工作流注册表使用
```python
from sdw_agent.service import WorkflowRegistry
from sdw_agent.service.req_design_verify_workflow import ReqDesignVerifyInputModel
from sdw_agent.model.request_model import SourceInfo

# 创建输入数据
excel_source = SourceInfo(type="local", uri="/path/to/excel/file.xlsx")
input_data = ReqDesignVerifyInputModel(excel_source=excel_source)

# 执行工作流
workflow = WorkflowRegistry.create("req_design_verify")
result = workflow.run(input_data)
```

### 通过API接口使用
```bash
POST /api/sdw/requirement_design_verification/workflow
{
    "NewRegTemplateSource": {
        "type": "local",
        "uri": "/path/to/excel/file.xlsx"
    }
}
```

## 输出结果

### 检查报告包含
1. **汇总信息**: 检查时间、总行数、OK/NG数量、完整率等
2. **检查结果**: 每行的详细检查结果，包含行号、要件内容、设计书信息、检查状态
3. **统计信息**: 详细的统计数据和说明
4. **检查规则**: 检查规则的详细说明

### 颜色标识
- **绿色**: OK状态，设计书信息完整
- **红色**: NG状态，设计书信息缺失或不符合要求
- **灰色**: 表头和标题

## 错误处理

- 文件不存在或格式错误时抛出相应异常
- 工作表不存在时使用默认工作表
- 列名不匹配时记录警告并使用默认值
- 处理过程中的异常会被记录并向上传播

## 扩展性

- 支持自定义配置覆盖默认设置
- 可扩展检查规则和验证逻辑
- 支持自定义输出格式和样式
- 可集成到其他工作流中使用
