"""
@File    : cstm_tool_api.py
@Time    : 2025/7/18 09:34
<AUTHOR> qilian<PERSON>_<PERSON>hou
@Email   : <EMAIL>
@V字流程  : 2.1 基本設計 ソフトウェア設計書作成（I/F含む） cstm 配置工具自动化生成
@Desc    : 分析变更点变更前后原始式样书中的内容，自动化更新CSTM Tool要件并生成变更后的代码
"""

from typing import Dict, Any
from loguru import logger
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, model_validator, Field

from sdw_agent.model.request_model import SourceInfo
from sdw_agent.service import WorkflowStatus
from sdw_agent.service.cstm_tool.model import InputDataModel
from sdw_agent.service.cstm_tool.workflow import CstmToolWorkflow


class GenerateCSTMRequest(BaseModel):
    row_idx: int
    req_change_content: str
    epic: SourceInfo
    scl: SourceInfo
    before_req: SourceInfo
    after_req: SourceInfo
    base_cstm: SourceInfo

class CSTMToolResponse(BaseModel):
    """cstm tool响应模型"""
    code: int = Field(0, description="状态码，0表示成功，其他表示失败")
    msg: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")


logger.bind(name="CSTM_TOOL")

router = APIRouter(prefix="/api/sdw/cstm", tags=["cstm tool 配置工具"])


@router.post("/cstm_tool",
             summary="分析原始式样书，自动化更新CSTM Tool要件",
             description="",
             response_description="",
             response_model=CSTMToolResponse)
async def generate_cstm_tool(request: GenerateCSTMRequest):
    """获取要件一览表"""
    try:
        # 创建工作流实例
        workflow = CstmToolWorkflow()

        # scl 文件
        scl_path = request.scl.uri
        # 要件一览表概要
        change_summary = request.req_change_content
        # 变更前式样
        before_cstm = request.before_req.uri
        # 变更后式样
        after_cstm = request.after_req.uri
        # base cstm 配置工具
        base_cstm = request.base_cstm.uri

        # 准备输入数据
        input_data = InputDataModel(
            change_summary = change_summary,
            scl_path = scl_path,
            before_cstm = before_cstm,
            after_cstm = after_cstm,
            base_cstm = base_cstm
        )

        # 执行工作流
        result = workflow.run(input_data)
        # 处理结果
        if result.status == WorkflowStatus.SUCCESS:

            logger.success("cstm tool工作流执行成功")
            cstm_tool_path = result.data
            return {
                "code": 0,
                "msg": "CSTM Tool配置工具更新成功",
                "data": {
                    "keyList": cstm_tool_path
                }
            }
        else:
            logger.error(f"cstm tool工作流执行失败: {result.error}")
            return {
                "code": 500,
                "msg": "CSTM Tool配置工具更新失败",
                "data": {}
            }
    except Exception as e:
        logger.exception(f"cstm tool工作流执行过程中发生异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))