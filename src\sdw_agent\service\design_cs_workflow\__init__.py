"""
设计基准Check Sheet工作流

V字对应：
V2.1 对应的V字阶段
基本设计 对应的V字项目

设计基准Check Sheet工作流，用于分析Excel文件中的设计基准检查表数据。
主要功能包括Excel文件结构分析、数据提取、隐藏行处理等。

主要功能：
1. Excel文件结构分析
2. 表头映射和数据提取
3. 隐藏行和隐藏列检测
4. 数据过滤和导出
"""

from .design_cs_workflow import DesignCSWorkflow
from .models import (
    DesignCSInputModel,
    DesignCSOutputModel,
    DesignCSConfigModel,
    AnalysisResultModel
)

# 确保工作流被注册
# 通过导入工作流类，@register_workflow装饰器会自动执行注册

__all__ = [
    "DesignCSWorkflow",
    "DesignCSInputModel",
    "DesignCSOutputModel",
    "DesignCSConfigModel",
    "AnalysisResultModel"
]

# 提供便捷的工厂函数
def create_design_cs_workflow(config_path=None):
    """
    创建设计基准Check Sheet工作流实例

    Args:
        config_path: 可选的配置文件路径

    Returns:
        DesignCSWorkflow: 工作流实例
    """
    return DesignCSWorkflow(config_path=config_path)

def get_workflow_info():
    """
    获取工作流信息

    Returns:
        dict: 工作流基本信息
    """
    return {
        "name": "design_cs",
        "display_name": "设计基准Check Sheet工作流",
        "description": "分析Excel文件中的设计基准检查表数据",
        "version": "1.0.0",
        "input_model": DesignCSInputModel,
        "output_model": DesignCSOutputModel,
        "config_model": DesignCSConfigModel,
        "analysis_result_model": AnalysisResultModel
    }
