# IF整合性确认工作流

## V字对应
- 2.5.26 I/F整合性確認

## 概述

IF整合性确认工作流专门处理接口整合性确认任务。从I/F设计书中提取接口信息，从Git仓库获取代码，通过AI分析代码中接口使用与IF设计书的一致性，输出包含判定结果的整合性确认报告书。

## 主要功能

### 核心功能：接口整合性确认
- **IF设计书解析**: 读取Excel格式的IF设计书，提取接口定义信息
- **代码仓库克隆**: 从Git仓库克隆指定分支的代码
- **接口匹配搜索**: 在代码中搜索IF设计书中定义的接口调用
- **AI一致性分析**: 使用大模型分析接口调用参数的合理性
- **报告生成**: 生成包含判定结果的IF整合性确认报告书

### 分析流程
1. **解析IF设计书**: 从Excel文件中提取接口名称、参数类型、约束条件等信息
2. **克隆代码仓库**: 下载指定分支的源代码到本地
3. **搜索接口调用**: 根据接口名称在代码中查找所有调用位置
4. **AI一致性判定**: 结合接口定义和调用上下文，AI分析参数使用的合理性
5. **生成判定结果**: 输出OK/NG/Pending判定结果
6. **报告书生成**: 将结果整合到模板文件，生成最终报告

## 使用方法

### 基本使用

```python
from sdw_agent.service.if_integration_service import IFIntegrationWorkflow
from sdw_agent.service.if_integration_service.models import IFStylebookInfo, CloneRepoInfo

# 创建工作流实例
workflow = IFIntegrationWorkflow()

# 准备IF设计书信息
if_stylebook = IFStylebookInfo(
    uri="/path/to/if_design.xlsx",
    type="file"
)

# 准备代码仓库信息
clone_repo = CloneRepoInfo(
    repo_url="https://github.com/example/repo.git",
    branch="main",
    username="your_username",  # 可选
    password="your_password"   # 可选
)

# 执行整合性确认
result = workflow.run(if_stylebook, clone_repo)

print(f"执行状态: {result.status.value}")
print(f"输出文件: {result.data['output_file']}")
```

### 高级使用

```python
# 使用自定义配置
workflow = IFIntegrationWorkflow(config_path="/path/to/custom_config.yaml")

# 执行并获取详细结果
result = workflow.run(if_stylebook, clone_repo)

if result.status == WorkflowStatus.SUCCESS:
    # 获取详细统计信息
    stats = result.data['statistics']
    print(f"总接口数: {stats['total_interfaces']}")
    print(f"匹配接口数: {stats['matched_interfaces']}")
    print(f"OK判定: {stats['ok_count']}")
    print(f"NG判定: {stats['ng_count']}")
    print(f"Pending判定: {stats['pending_count']}")
    
    # 获取集成数据详情
    integration_data = result.data['integration_data']
    for sheet_name, data_list in integration_data.items():
        print(f"工作表 {sheet_name}: {len(data_list)} 条记录")
else:
    print(f"执行失败: {result.error}")
```

### 使用兼容接口

```python
from sdw_agent.service.if_integration_service import do_if_integration

# 使用旧版兼容接口
try:
    output_file = do_if_integration(if_stylebook, clone_repo)
    print(f"报告已生成: {output_file}")
except Exception as e:
    print(f"执行失败: {str(e)}")
```

### API接口使用

```python
import requests

# 通过API调用
response = requests.post("/api/sdw/integration/if_integration", json={
    "ifStylebook": {
        "uri": "/path/to/if_design.xlsx",
        "type": "file"
    },
    "cloneRepoInfo": {
        "repo_url": "https://github.com/example/repo.git",
        "branch": "main",
        "username": "your_username",  # 可选
        "password": "your_password"   # 可选
    }
})

if response.status_code == 200:
    result = response.json()
    print(f"API调用成功: {result}")
else:
    print(f"API调用失败: {response.text}")
```

## 输入参数说明

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `if_stylebook_obj` | IFStylebookInfo | 是 | IF设计书信息对象 |
| `clone_repo_info` | CloneRepoInfo | 是 | 代码仓库克隆信息 |

### IFStylebookInfo 字段

| 字段名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `uri` | str | 是 | - | IF设计书文件路径 |
| `type` | str | 否 | "file" | 文件类型标识 |

### CloneRepoInfo 字段

| 字段名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `repo_url` | str | 是 | - | Git仓库URL |
| `branch` | str | 是 | - | 分支名称 |
| `username` | str | 否 | None | 用户名（私有仓库需要） |
| `password` | str | 否 | None | 密码（私有仓库需要） |

## 配置说明

工作流配置文件 `config.yaml` 包含以下配置项：

```yaml
# 基本配置
name: "IF整合性确认"
description: "检查代码中接口使用与IF设计书的一致性"
version: "1.0.0"
author: "SDW-Team"

# 输入输出配置
io:
  input:
    if_stylebook_extensions: [".xlsx", ".xls"]  # 支持的IF设计书格式
  output:
    default_output_dir: "C:\\sdw_output"  # 默认输出目录
    report_base_name: "IF整合性CS兼結果報告書"  # 报告文件名基础
    template_file: "templates/IF整合性CS兼結果報告書_模板.xlsx"  # 报告模板

# 处理参数
processing:
  if_stylebook:
    sheet_name: "IF一覧"  # IF设计书工作表名
    if_name_keyword: "I/F名"  # 接口名称列关键字
  integration:
    status_values: ["OK", "NG", "Pending"]  # 可用的判定状态
    default_status: "Pending"  # 默认判定状态
  llm:
    max_workers: 5  # 并行处理的最大线程数
    timeout: 30  # LLM调用超时时间（秒）

# Excel输出样式配置
output_excel_style:
  default_sheet: "DEFAULT_IF"  # 默认工作表名
  data_start_row: 14  # 数据起始行
  special_cols: [7, 8, 9, 12]  # 特殊列（不合并单元格）
```

## 输入文件格式

### IF设计书文件
IF设计书应为Excel格式，包含"IF一覧"工作表，至少包含以下列：

| 列名 | 必需 | 描述 |
|------|------|------|
| `I/F名` | 是 | 接口名称 |
| `戻り値型` | 否 | 返回值类型 |
| `引数` | 否 | 参数信息 |
| `制約条件` | 否 | 约束条件 |
| `説明` | 否 | 接口说明 |

**示例格式：**
```
| I/F名        | 戻り値型 | 引数           | 制約条件      | 説明           |
|-------------|---------|---------------|-------------|---------------|
| func_add    | int     | int a, int b  | a>0, b>0    | 加法函数       |
| func_sub    | int     | int x, int y  | x>=y        | 减法函数       |
```

### 代码仓库
支持标准的Git仓库，包括：
- **HTTP/HTTPS协议**: `https://github.com/user/repo.git`
- **SSH协议**: `**************:user/repo.git`
- **私有仓库**: 需要提供用户名和密码
- **分支支持**: 支持任意有效的分支名称

## 输出文件格式

生成的IF整合性确认报告书为Excel格式，包含以下信息：

| 列名 | 描述 |
|------|------|
| `接口名称` | 从IF设计书提取的接口名 |
| `代码位置` | 接口调用在代码中的文件路径和行号 |
| `代码内容` | 接口调用的具体代码片段 |
| `判定结果` | OK/NG/Pending |
| `判定理由` | AI分析的详细理由 |
| `IF设计书名称` | 使用的IF设计书文件名 |
| `确认日期` | 分析执行的日期时间 |
| `确认者` | 默认为"DEV AGENT" |

**输出文件命名规则：**
```
IF整合性CS兼結果報告書_YYYYMMDD_HHMMSS.xlsx
```

## 错误处理

工作流包含完善的错误处理机制：

### 输入验证错误
```python
# IF设计书文件不存在
ValidationError: IF设计书文件不存在: /path/to/file.xlsx

# Git仓库URL格式错误
ValidationError: 不支持的仓库URL格式: invalid_url

# 分支名称为空
ValidationError: 分支名称不能为空
```

### 运行时错误
```python
# 代码克隆失败
RuntimeError: 克隆代码仓库失败: Authentication failed

# IF设计书解析失败
RuntimeError: IF设计书解析失败: 找不到工作表 'IF一覧'

# LLM调用失败
RuntimeError: AI分析失败: API调用超时
```

### 错误日志示例
```
2024-01-15 10:30:15 | ERROR | IF整合性确认执行失败: 克隆代码仓库失败
2024-01-15 10:30:15 | INFO  | 开始清理临时文件: /tmp/repo_clone_xxx
```

## 注意事项

### 环境要求
1. **Python版本**: 需要Python 3.8+
2. **依赖包**: 确保安装所有必需的依赖包
3. **Git工具**: 系统需要安装Git命令行工具
4. **网络连接**: 需要稳定的网络连接用于代码克隆和AI调用

### 文件要求
1. **IF设计书**: 必须是有效的Excel格式（.xlsx或.xls）
2. **工作表名称**: 必须包含"IF一覧"工作表
3. **列名匹配**: 列名必须包含"I/F名"关键字
4. **文件权限**: 确保对输入文件有读取权限，对输出目录有写入权限

### 仓库要求
1. **URL格式**: Git仓库URL必须是有效格式
2. **分支存在**: 指定的分支必须在仓库中存在
3. **访问权限**: 私有仓库需要提供有效的认证信息
4. **磁盘空间**: 确保有足够空间用于代码克隆

### 性能考虑
1. **大型仓库**: 大型代码仓库克隆可能需要较长时间
2. **接口数量**: 大量接口分析会增加处理时间
3. **并发限制**: 默认最大5个并发LLM调用，可通过配置调整
4. **内存使用**: 大文件处理可能消耗较多内存

## 性能优化

### 1. 并行处理优化
```python
# 配置文件中调整并发数
processing:
  llm:
    max_workers: 10  # 增加并发线程数（根据系统性能调整）
    timeout: 60      # 增加超时时间
```

### 2. 缓存机制
- **代码仓库缓存**: 避免重复克隆相同仓库
- **分析结果缓存**: 缓存相同接口的分析结果
- **模板文件缓存**: 缓存Excel模板以提高生成速度

### 3. 批量处理
```python
# 批量处理多个IF设计书
for if_file in if_files:
    result = workflow.run(
        IFStylebookInfo(uri=if_file),
        clone_repo_info
    )
```

### 4. 资源管理
- **临时文件清理**: 自动清理克隆的代码和临时文件
- **内存管理**: 及时释放大对象的内存
- **连接池**: 复用HTTP连接以提高效率

## 扩展功能

### 自定义模板
```python
# 使用自定义报告模板
workflow = IFIntegrationWorkflow()
workflow.config["io"]["output"]["template_file"] = "/path/to/custom_template.xlsx"
```

### 自定义判定逻辑
```python
# 修改LLM提示词
from sdw_agent.config.env import ENV
ENV.prompt.if_integration_prompt = "自定义的提示词模板..."
```

### 多仓库支持
```python
# 分析多个代码仓库
repos = [
    CloneRepoInfo(repo_url="repo1", branch="main"),
    CloneRepoInfo(repo_url="repo2", branch="develop")
]

for repo in repos:
    result = workflow.run(if_stylebook, repo)
```

### 结果合并
```python
# 合并多次分析的结果
results = []
for repo in repos:
    result = workflow.run(if_stylebook, repo)
    results.append(result.data)

# 自定义合并逻辑
merged_result = merge_integration_results(results)
```

## 故障排除

### 常见问题

**Q: 克隆代码仓库失败**
```
A: 检查网络连接、仓库URL格式、认证信息是否正确
   确保Git工具已正确安装并配置
```

**Q: IF设计书解析失败**
```
A: 检查Excel文件格式、工作表名称、列名是否符合要求
   确保文件未被其他程序占用
```

**Q: AI分析超时**
```
A: 增加timeout配置值，检查网络连接稳定性
   减少max_workers数量以降低并发压力
```

**Q: 输出文件生成失败**
```
A: 检查输出目录权限、磁盘空间是否充足
   确保模板文件存在且格式正确
```

### 调试模式
```python
# 启用详细日志
import logging
logging.getLogger("sdw_agent").setLevel(logging.DEBUG)

# 使用调试配置
workflow = IFIntegrationWorkflow()
workflow.logger.setLevel(logging.DEBUG)
```

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志和堆栈跟踪
2. 输入文件和配置信息
3. 系统环境和版本信息
4. 复现步骤和预期结果
