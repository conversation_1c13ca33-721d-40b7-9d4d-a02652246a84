"""
要件设计书照合实施Excel工具类

基于ExcelUtil提供要件设计书照合实施特定的Excel操作功能
"""

import os
from datetime import datetime
from typing import Dict, Any, List

from loguru import logger
from openpyxl.styles import Font, PatternFill, Alignment

# 移除ExcelUtil导入，使用轻量级实现避免不必要的Excel引擎初始化
from sdw_agent.util.excel.multi_level_header_extractor import MultiLevelHeaderExtractor


class ReqDesignVerifyExcelUtil:
    """要件设计书照合实施Excel工具类（轻量级实现，避免不必要的Excel引擎初始化）"""

    def __init__(self, file_path: str):
        """
        初始化要件设计书照合实施Excel工具类

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.logger = logger

    def extract_requirement_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        提取要件数据
        
        Args:
            config: 配置信息
            
        Returns:
            List[Dict[str, Any]]: 提取的要件数据列表
        """
        self.logger.info("开始提取要件数据")

        try:
            sheet_name = config.get('target_sheet', '機能一覧と新規・変更内容')
            extractor = MultiLevelHeaderExtractor(self.file_path, sheet_name)

            # 提取完整数据
            df, _ = extractor.extract_data_with_headers(
                header_start_row=config.get('header_start_row', 5),
                header_end_row=config.get('header_end_row', 7),
                data_start_row=config.get('data_start_row', 8),
                start_col=config.get('start_col', 1),
                end_col=config.get('end_col', 100),
                exclude_hidden_rows=config.get('exclude_hidden_rows', True)
            )

            content_list = []
            data_start_row = config.get('data_start_row', 8)

            # 获取实际的Excel行号映射
            actual_row_numbers = []
            for row_num in range(data_start_row, extractor.ws.max_row + 1):
                if not extractor._is_row_hidden(row_num):
                    actual_row_numbers.append(row_num)
                    if len(actual_row_numbers) >= len(df):
                        break

            req_content_column = config.get('req_content_column', '要件の内容')
            design_book_column = config.get('design_book_column', 'パラメータ設計書')

            for idx, row in df.iterrows():
                # 使用映射获取实际的Excel行号
                if idx < len(actual_row_numbers):
                    actual_excel_row = actual_row_numbers[idx]
                else:
                    actual_excel_row = data_start_row + idx

                # 准备数据
                row_data = {
                    "row_idx": actual_excel_row,
                    "req_info": str(row.get(req_content_column, '')),
                    "design_book": str(row.get(design_book_column, '')),
                }
                content_list.append(row_data)

            self.logger.info(f"成功提取 {len(content_list)} 行要件数据")
            return content_list

        except Exception as e:
            self.logger.error(f"提取要件数据失败: {e}")
            raise

    def check_design_book_completeness(self, data_list: List[Dict[str, Any]],
                                       config: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查设计书信息的完整性
        
        Args:
            data_list: 要件数据列表
            config: 配置信息
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        self.logger.info("开始检查设计书完整性")

        check_results = []
        statistics = {
            "total_rows": len(data_list),
            "skipped_rows": 0,
            "checked_rows": 0,
            "ok_count": 0,
            "ng_count": 0,
            "break_at_row": None
        }

        consecutive_empty_count = 0
        consecutive_empty_limit = config.get('consecutive_empty_limit', 5)
        min_design_book_length = config.get('min_design_book_length', 3)

        for item in data_list:
            row_idx = item.get("row_idx")
            req_info = item.get("req_info")
            design_book = item.get("design_book")

            # 检查req_info是否为空
            if req_info is None or str(req_info).strip() in ['None', '', 'nan']:
                consecutive_empty_count += 1
                statistics["skipped_rows"] += 1

                # 如果连续空行达到限制，则停止
                if consecutive_empty_count >= consecutive_empty_limit:
                    statistics["break_at_row"] = row_idx
                    self.logger.info(f"连续{consecutive_empty_limit}行要件内容为空，在第{row_idx}行停止处理")
                    break
                continue
            else:
                consecutive_empty_count = 0

            # 检查design_book
            statistics["checked_rows"] += 1
            design_book_str = str(design_book).strip() if design_book is not None else ""

            if (design_book is None or
                    design_book_str in ['None', '', 'nan', '-'] or
                    len(design_book_str) < min_design_book_length):
                check_status = "NG"
                statistics["ng_count"] += 1
            else:
                check_status = "OK"
                statistics["ok_count"] += 1

            check_results.append({
                "行号": row_idx,
                "要件内容": str(req_info),
                "设计书信息": str(design_book) if design_book is not None else "",
                "检查结果": check_status
            })

        self.logger.info(
            f"检查完成: 总计{statistics['checked_rows']}行，OK:{statistics['ok_count']}，NG:{statistics['ng_count']}")

        return {
            "check_results": check_results,
            "statistics": statistics
        }

    def export_check_results(self, check_data: Dict[str, Any], output_path: str,
                             config: Dict[str, Any]) -> str:
        """
        导出检查结果到Excel文件
        
        Args:
            check_data: 检查结果数据
            output_path: 输出文件路径
            config: 配置信息
            
        Returns:
            str: 实际的输出文件路径
        """
        self.logger.info("开始导出检查结果")

        try:
            check_results = check_data["check_results"]
            statistics = check_data["statistics"]

            # 生成输出文件名
            if not output_path.endswith('.xlsx'):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(output_path, f"要件设计书照合结果_{timestamp}.xlsx").replace("\\", "/")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 创建工作簿
            from openpyxl import Workbook
            wb = Workbook()
            wb.remove(wb.active)

            # 创建检查结果工作表
            ws_results = wb.create_sheet("检查结果")
            self._add_summary_section(ws_results, statistics, config)
            self._add_results_data(ws_results, check_results, config, start_row=15)

            # 创建统计信息工作表
            ws_stats = wb.create_sheet("统计信息")
            self._add_statistics_sheet(ws_stats, statistics, config)

            # 保存文件
            wb.save(output_path)

            self.logger.info(f"检查结果已导出到: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"导出检查结果失败: {e}")
            raise

    def _add_summary_section(self, ws, statistics: Dict[str, Any], config: Dict[str, Any]):
        """添加汇总信息到工作表顶部"""
        # 标题
        ws['A1'] = "要件设计书照合实施结果汇总"
        title_font_size = config.get('styles', {}).get('title_font_size', 16)
        ws['A1'].font = Font(size=title_font_size, bold=True)

        # 汇总信息
        summary_data = [
            ["检查时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["总行数", statistics["total_rows"]],
            ["检查行数", statistics["checked_rows"]],
            ["OK数量", statistics["ok_count"]],
            ["NG数量", statistics["ng_count"]],
            ["完整率",
             f"{statistics['ok_count']}/{statistics['checked_rows']} ({statistics['ok_count'] / statistics['checked_rows'] * 100:.1f}%)" if
             statistics['checked_rows'] > 0 else "0%"]
        ]

        if statistics.get("break_at_row"):
            summary_data.append(["提前结束", f"在第{statistics['break_at_row']}行因连续空行停止"])

        ok_color = config.get('styles', {}).get('ok_color', '90EE90')
        ng_color = config.get('styles', {}).get('ng_color', 'FFB6C1')

        for i, (key, value) in enumerate(summary_data, start=2):
            ws[f'A{i}'] = key
            ws[f'B{i}'] = value
            ws[f'A{i}'].font = Font(bold=True)

            # 为OK/NG数量添加颜色
            if key == "OK数量":
                ws[f'B{i}'].fill = PatternFill(start_color=ok_color, end_color=ok_color, fill_type="solid")
            elif key == "NG数量" and statistics["ng_count"] > 0:
                ws[f'B{i}'].fill = PatternFill(start_color=ng_color, end_color=ng_color, fill_type="solid")

        # 添加检查规则说明
        rule_start_row = len(summary_data) + 4
        ws[f'A{rule_start_row}'] = "检查规则说明:"
        header_font_size = config.get('styles', {}).get('header_font_size', 12)
        ws[f'A{rule_start_row}'].font = Font(bold=True, size=header_font_size)

        min_length = config.get('min_design_book_length', 3)
        rules = [
            "1. 要件内容为空的行会被跳过",
            f"2. 连续{config.get('consecutive_empty_limit', 5)}行要件内容为空时停止检查",
            "3. 设计书信息判断规则:",
            f"   - OK: 有内容且长度≥{min_length}字符",
            f"   - NG: 为空、'-'或长度<{min_length}字符"
        ]

        for i, rule in enumerate(rules, start=rule_start_row + 1):
            ws[f'A{i}'] = rule
            if rule.startswith("   "):
                ws[f'A{i}'].font = Font(italic=True)

    def _add_results_data(self, ws, check_results: List[Dict[str, Any]],
                          config: Dict[str, Any], start_row: int = 8):
        """添加检查结果数据"""
        # 添加表头
        headers = ["行号", "要件内容", "设计书信息", "检查结果"]
        header_color = config.get('styles', {}).get('header_color', 'D3D3D3')

        for col, header in enumerate(headers, start=1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color=header_color, end_color=header_color, fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # 添加数据
        ok_color = config.get('styles', {}).get('ok_color', '90EE90')
        ng_color = config.get('styles', {}).get('ng_color', 'FFB6C1')

        for row_idx, result in enumerate(check_results, start=start_row + 1):
            ws.cell(row=row_idx, column=1, value=result["行号"])
            ws.cell(row=row_idx, column=2, value=result["要件内容"])
            ws.cell(row=row_idx, column=3, value=result["设计书信息"])

            # 检查结果列添加颜色
            result_cell = ws.cell(row=row_idx, column=4, value=result["检查结果"])
            if result["检查结果"] == "OK":
                result_cell.fill = PatternFill(start_color=ok_color, end_color=ok_color, fill_type="solid")
            else:
                result_cell.fill = PatternFill(start_color=ng_color, end_color=ng_color, fill_type="solid")
            result_cell.alignment = Alignment(horizontal="center")

        # 调整列宽
        column_widths = config.get('output', {}).get('column_widths', {})
        ws.column_dimensions['A'].width = column_widths.get('row_number', 8)
        ws.column_dimensions['B'].width = column_widths.get('req_content', 50)
        ws.column_dimensions['C'].width = column_widths.get('design_book', 30)
        ws.column_dimensions['D'].width = column_widths.get('check_result', 12)

    def _add_statistics_sheet(self, ws, statistics: Dict[str, Any], config: Dict[str, Any]):
        """添加统计信息工作表"""
        ws['A1'] = "详细统计信息"
        ws['A1'].font = Font(size=14, bold=True)

        header_color = config.get('styles', {}).get('header_color', 'D3D3D3')
        min_length = config.get('min_design_book_length', 3)

        stats_data = [
            ["项目", "数值", "说明"],
            ["总行数", statistics["total_rows"], "Excel中读取的总行数"],
            ["跳过行数", statistics["skipped_rows"], "要件内容为空的行数"],
            ["检查行数", statistics["checked_rows"], "实际进行检查的行数"],
            ["OK数量", statistics["ok_count"], f"设计书信息完整的行数(长度≥{min_length}且非空)"],
            ["NG数量", statistics["ng_count"], f"设计书信息缺失的行数(为空、'-'或长度<{min_length})"],
            ["完整率",
             f"{statistics['ok_count']}/{statistics['checked_rows']} ({statistics['ok_count'] / statistics['checked_rows'] * 100:.1f}%)" if
             statistics['checked_rows'] > 0 else "0%", "设计书信息完整的比例"]
        ]

        if statistics.get("break_at_row"):
            stats_data.append(["提前结束行", statistics["break_at_row"], "因连续5行空要件内容而停止的行号"])

        for row_idx, row_data in enumerate(stats_data, start=3):
            for col_idx, value in enumerate(row_data, start=1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 3:  # 表头
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color=header_color, end_color=header_color, fill_type="solid")

        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 30

    def close(self):
        """关闭方法（兼容性方法，轻量级实现无需实际关闭操作）"""
        # 轻量级实现无需实际关闭操作，只是为了与ExcelUtil接口兼容
        pass

    def close(self):
        """关闭方法（兼容性方法，轻量级实现无需实际关闭操作）"""
        # 轻量级实现无需实际关闭操作
        pass
