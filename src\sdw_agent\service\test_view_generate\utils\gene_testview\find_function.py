"""发现变更点中相关信息"""
from typing import Dict
import asyncio
from collections import defaultdict

from loguru import logger
from sdw_agent.llm.openai_qwen import OpenAIQwen


class ChangeMessageExtractor:
    """从解析后的变更一览表中获取对应的变更信息，包括功能点、变更内容和变更类型、功能点路径"""

    def __init__(self, change_data: Dict):
        self.change_data = change_data
        self.llm = OpenAIQwen(base_url="http://172.30.19.113:11400/v1",
                              api_key="sk-xxx")

    async def function_extract(self):
        """从变更内容中获取功能点列表、变更内容和变更类型"""
        # 定义prompt
        find_prompt = """
                    <instruction>
                        你是一名汽车项目研发人员，熟悉的了解汽车的每个功能模块，现在需要你根据<input>中的功能变更信息，提取出这次变更所涉及到的功能项、变更内容、变更类型、功能点层级；
                    </instruction>
        
                    <requirement>
                        1. 必须严格按照json格式输出，不要输出其他内容；
                        2. 输出格式按照<output_structure>中的格式输出；
                        3. 变更类型change_type仅包含三种类型：add、update、delete；
                        4. 功能点层级function_path列表中，每个元素包含功能项function_point对应功能点变更前后的层级，层级用-拼接
                    </requirement>
        
                    <input>
                        {{input}}
                    </input>
        
                    <output_structure>
                        {
                            "function_point": ["功能点1", "功能点2", "功能点3"],
                            "change_content": "变更内容",
                            "change_type": "变更类型",
                            "function_path": [{"before": "父功能1-功能点1", "after": "父功能2-功能点1"}, {"before": "父功能1-功能点2", "after": "父功能2-功能点2"}, {"before": "父功能1-功能点3", "after": "父功能2-功能点3"}]
                        }
                    </output_structure>

            """
        message_content = find_prompt.replace("{{input}}", str(self.change_data))

        logger.info(f"待抽取变更点为：{self.change_data}")
        try:
            # 设置LLM输入并获取结果
            generate_response = await self.llm.generate(message_content)

            # 对LLM返回结果进行处理
            logger.info(f"抽取结果为：{generate_response}")

            # 对抽取结果进行业务逻辑处理
            logger.info("开始对抽取结果进行业务逻辑处理")
            deal_json = ChangeMessageExtractor.message_deal(generate_response)
            logger.info(f"基于业务逻辑整合的抽取结果为：{deal_json}")
            return deal_json
        except Exception as e:
            logger.error(f"需求提取失败: {str(e)}")
            return None

    @staticmethod
    def message_deal(data):
        """将大模型抽取后的信息进行后置处理"""
        # 如果功能点抽取结果为空，则抛出对应异常到前端
        if not data or not data.get('function_point'):
            logger.info("LLM抽取结果为空")
            return {}

        try:
            deal_data = {}
            function_point = data.get("function_point")
            function_path = data.get('function_path')
            if not function_path:
                deal_data['function_point'] = function_point[0]
                deal_data["function_path"] = {}
            else:
                before_path = [i.get('before') for i in function_path if i.get('before')]
                after_path = [i.get('after') for i in function_path if i.get('after')]
                # 如果功能点抽取不为空，则分别进行新增、修改、删除的处理
                change_type = data.get('change_type')
                if change_type == "add":
                    logger.info("此变更点为新增，开始新增逻辑处理")
                    # 判断功能点列表中获取到的功能点名称是否同级功能，是则随机选取一个，否则判断最小功能点
                    leaf_function, leaf_path = ChangeMessageExtractor.find_min_children_and_paths(function_point,
                                                                                                  after_path)
                    if not leaf_path or not leaf_function:
                        deal_data['function_point'] = ""
                        deal_data["function_path"] = {}
                    else:
                        deal_data['function_point'] = leaf_path[0][-2] if len(leaf_path[0]) > 1 else leaf_path[0][-1]
                        deal_data["function_path"] = function_path[
                            function_point.index(leaf_function[0])] if function_point.index(leaf_function[0]) < len(
                            function_path) else function_path[0]
                    logger.info("新增逻辑处理结束")
                else:
                    logger.info("此变更点为更新或删除，开始相关逻辑处理")
                    leaf_function, leaf_path = ChangeMessageExtractor.find_min_children_and_paths(function_point,
                                                                                                  before_path)
                    if not leaf_path or not leaf_function:
                        deal_data['function_point'] = ""
                        deal_data["function_path"] = {}
                    else:
                        deal_data['function_point'] = leaf_function[0]
                        deal_data["function_path"] = function_path[function_point.index(leaf_function[0])]
                    logger.info("更新或删除逻辑处理结束")
            deal_data['change_type'] = data.get('change_type')
            deal_data['change_content'] = data.get('change_content')
            return deal_data
        except Exception as e:
            return {}

    @staticmethod
    def find_min_children_and_paths(list_a, list_b):
        """
        找出A列表中最小孩子节点及其完整父子关系路径

        Args:
            list_a (list): 目标元素列表，如 ['1500Wコンセント', ...]
            list_b (list): 父子关系列表，如 ['父节点-子节点', ...]

        Returns:
            tuple: (最小孩子节点列表, 对应路径列表)
        """
        try:
            # 构建双向关系映射
            parent_to_children = defaultdict(list)
            child_to_parent = {}
            all_children = set()
            all_parents = set()

            for relation in list_b:
                if '-' in relation:
                    parent, child = relation.split('-', 1)
                    parent_to_children[parent].append(child)
                    child_to_parent[child] = parent
                    all_children.add(child)
                    all_parents.add(parent)

            # 识别最小孩子节点（叶子节点）
            leaf_nodes = all_children - all_parents  # 没有子节点的节点

            # 为A列表中的叶子节点构建路径
            min_children = []
            paths = []

            for item in list_a:
                if item in leaf_nodes:
                    # 向上回溯构建路径
                    current = item
                    path = [item]

                    while current in child_to_parent:
                        current = child_to_parent[current]
                        path.insert(0, current)  # 添加到路径开头

                    min_children.append(item)
                    paths.append(path)
            if len(min_children) == 0 or len(paths) == 0:
                if len(list_a) > 0:
                    return [list_a[0]], [list_b]
            return min_children, paths
        except Exception as e:
            logger.error(f"构建抽取出的变更功能点路径树失败：{str(e)}")
        return [], []


if __name__ == '__main__':
    one_test = {
        "対応イベント": "R2小",
        "ARチケットNO": "MET19PFV3-28447",
        "ARチケットNOリンク": "MET19PFV3-28447",
        "ARチケットタイトル": "【顧客要求_変更】MET-G_CSTMLST-CSTD-A0-05-A-C0",
        "エピック名": "MET-G_CSTMLST-CSTD_SoC R_メータタイプ設定　6dial-ViewにウィジェットON/OFF追加",
        "概要": "メータタイプ設定　6dial-ViewにウィジェットON/OFF追加",
        "要件チケットNO": "-",
        "要件チケットNOリンク": "-",
        "R核": "〇",
        "A核": "〇",
        "差分種別": "选项追加",
        "変更内容": "第三階層のメータータイプ設定の6dial-View画面にウィジェット機能を追加する",
        "要件需求文档名称": "MET_CSTM_メータータイプ設定の6dial-Viewにウィジェット機能.md",
        "变更内容所在的章节信息": "ALL",
        "SCL填写要": "×"
    }

    change_class = ChangeMessageExtractor(one_test)
    deal_res = asyncio.run(change_class.function_extract())
    # find_res = {'function_point': ['1500Wコンセント', '2400Wコンセント', '7200Wコンセント'],
    #             'change_content': '1500Wコンセントと2400Wコンセントと7200Wコンセントの3つの項目は第0階層から第一階層の車両設定画面に移動する。',
    #             'change_type': 'update',
    #             'function_path': [{'before': '第0階層-1500Wコンセント', 'after': '第一階層-1500Wコンセント'},
    #                               {'before': '第0階層-2400Wコンセント', 'after': '第一階層-2400Wコンセント'},
    #                               {'before': '第0階層-7200Wコンセント', 'after': '第一階層-7200Wコンセント'}]}
    # deal_res = change_class.message_deal(find_res)
    print(deal_res)
