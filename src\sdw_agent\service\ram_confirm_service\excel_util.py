"""
RAM确认Excel处理工具
"""

from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from sdw_agent.util.excel.core import ExcelUtil


class RAMConfirmExcelUtil(ExcelUtil):
    """
    RAM确认Excel处理工具类

    继承自ExcelUtil基类，提供RAM确认特定的Excel操作功能：
    1. 基于模板创建工作表
    2. 填充RAM确认数据
    3. 应用特定样式
    4. 自动化报告生成
    """

    DEFAULT_SHEET_NAME = "ｲﾍﾞﾝﾄ(DEFAULT)"
    DEFAULT_DATA_START_ROW = 6

    def __init__(self, output_file: str, template_file: Optional[str],
                 excel_style: Optional[Dict[str, Any]] = None, engine: str = "win32com"):
        """
        初始化RAM确认Excel工具

        Args:
            output_file: Excel文件保存路径
            template_file: 模板文件路径，如果为None则使用默认模板
            excel_style: Excel样式配置
            engine: 操作引擎类型（默认win32com保护宏和图片）
        """

        # 设置文件路径
        self.template_file = Path(template_file) if template_file else None
        self.output_file = Path(output_file)

        # 设置样式配置
        self.excel_style = excel_style or {}
        self._init_style_config()

        # 调用父类初始化方法
        super().__init__(str(self.template_file), engine=engine, auto_create=False)

        # 记录初始化信息
        logger.info(f"初始化RAM确认Excel工具 - 输出: {self.output_file}, 模板: {self.template_file}")

    def _init_style_config(self):
        """初始化样式配置"""
        # 设置默认工作表名称，如果配置中没有则使用预定义的名称
        self.default_sheet = self.excel_style.get("default_sheet", self.DEFAULT_SHEET_NAME)
        # 设置数据开始的行号，如果配置中没有则使用默认值6
        self.data_start_row = self.excel_style.get("data_start_row", self.DEFAULT_DATA_START_ROW)
        # 设置数据开始的列号，如果配置中没有则使用默认值[]
        self.data_cols = self.excel_style.get("data_cols", [])

        # 记录调试信息，说明样式配置的默认工作表名称和数据起始行号
        logger.info(f"样式配置 - 默认工作表: {self.default_sheet}, 数据起始行: {self.data_start_row}")

    def save_ram_confirm_data(self, confirm_data: List[List[str]]) -> None:
        """
        保存RAM确认数据到Excel文件

        Args:
            confirm_data: 确认数据列表，每个子列表代表一行数据

        Returns:
            None
        """
        logger.info(f"开始保存RAM确认数据到Excel: {self.output_file}")

        try:
            # 填充确认数据到Excel工作表
            self._fill_confirm_data(confirm_data)

            # 保存到输出文件
            self.save(str(self.output_file))

            logger.info(f"RAM确认数据已保存到: {self.output_file}")

        except FileNotFoundError as e:
            logger.error(f"模板文件缺失导致保存失败: {e}")
            raise
        except ValueError as e:
            logger.error(f"数据格式错误导致保存失败: {e}")
            raise
        except Exception as e:
            logger.error(f"保存RAM确认数据失败: {e}")
            raise

    def _fill_confirm_data(self, confirm_data: List[List[str]]) -> None:
        """
        将确认数据填充到Excel工作表中

        Args:
            confirm_data (List[List[str]]): 需要写入Excel的二维数据列表，每个子列表代表一行数据

        Returns:
            None: 无返回值

        Raises:
            TypeError: 当confirm_data不是列表类型或其中某行数据不是列表类型时抛出
            ValueError: 当数据列数与模板列数不匹配时抛出
        """
        expected_col_count = len(self.data_cols)
        if expected_col_count == 0:
            logger.warning("未设置 data_cols，跳过列数校验")

        # 写入数据到指定工作表
        for i, row_data in enumerate(confirm_data):
            row_num = self.data_start_row + i
            if 0 < expected_col_count != len(row_data):
                raise ValueError(f"行 {row_num} 的数据列数({len(row_data)})与模板列数({expected_col_count})不匹配")

            for j, cell_value in enumerate(row_data):
                col_index = self.data_cols[j]
                if isinstance(cell_value, dict) and 'url' in cell_value:
                    # 创建超链接
                    url = cell_value['url']
                    display_text = cell_value.get('text', url)
                    self.write_hyperlink(self.default_sheet, row_num, col_index, url, display_text)
                else:
                    # 普通内容
                    self.write_cell(self.default_sheet, row_num, col_index, cell_value, value_must_be_safe=True)

        logger.info(f"已填充 {len(confirm_data)} 行数据到工作表 {self.default_sheet}")


