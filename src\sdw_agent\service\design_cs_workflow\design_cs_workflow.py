"""
设计基准Check Sheet工作流

V字对应：
V2.1 对应的V字阶段
基本设计 对应的V字项目

设计基准Check Sheet工作流实现，用于分析Excel文件中的设计基准检查表数据。

主要功能：
1. Excel文件结构分析
2. 表头映射和数据提取
3. 隐藏行和隐藏列检测
4. 数据过滤和导出
"""

import os
from typing import Dict, List, Any, Optional

from sdw_agent.service import BaseWorkflow, register_workflow, WorkflowResult, WorkflowStatus
from sdw_agent.util.excel.design_cs_excel import DesignCSExcelUtil
from sdw_agent.util.import_doc_util import ImportDocUtil
from .models import (
    DesignCSInputModel,
    DesignCSOutputModel,
    DesignCSConfigModel,
    AnalysisResultModel
)


@register_workflow("design_cs")
class DesignCSWorkflow(BaseWorkflow):
    """
    设计基准Check Sheet工作流
    
    负责处理Excel文件中的设计基准检查表数据，提供数据提取、
    隐藏行处理、数据过滤等功能。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化设计基准Check Sheet工作流

        Args:
            config_path: 配置文件路径，如不提供则使用默认路径
        """
        super().__init__(config_path)
        self.excel_util = None
        self.import_doc_util = ImportDocUtil()

    def validate_input(self, input_data: DesignCSInputModel) -> bool:
        """
        验证输入参数
        
        Args:
            input_data: 输入数据模型
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证文件是否存在
            if not os.path.exists(input_data.file_path):
                self.logger.error(f"文件不存在: {input_data.file_path}")
                return False
            
            # 验证文件扩展名
            valid_extensions = ['.xlsx', '.xlsm', '.xls']
            file_ext = os.path.splitext(input_data.file_path)[1].lower()
            if file_ext not in valid_extensions:
                self.logger.error(f"不支持的文件格式: {file_ext}")
                return False
            
            # 验证行号逻辑
            header_row = input_data.get_header_row()
            start_row = input_data.get_start_row()
            if start_row <= header_row:
                self.logger.error(f"数据开始行号({start_row})必须大于表头行号({header_row})")
                return False
            
            self.logger.info("输入参数验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"输入验证失败: {str(e)}")
            return False

    def pre_execute(self, input_data: DesignCSInputModel) -> None:
        """
        执行前处理
        
        Args:
            input_data: 输入数据模型
        """
        super().pre_execute(input_data)
        self.logger.info(f"开始处理文件: {input_data.file_path}")
        
        # 加载配置
        config = self.config.get("module_specific", {})
        self.logger.info(f"使用配置: {config}")

    def execute(self, input_data: DesignCSInputModel) -> WorkflowResult:
        """
        执行工作流核心逻辑

        Args:
            input_data: 输入数据模型

        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            # 1. 创建Excel工具实例
            self.logger.info("初始化Excel工具")
            config = self.config.get("module_specific", {})
            engine = config.get("excel_processing", {}).get("engine", "openpyxl")

            self.excel_util = DesignCSExcelUtil(input_data.file_path, engine=engine)

            # 2. 分析Excel文件结构
            self.logger.info("开始分析Excel文件结构")
            structure_info = self.excel_util.analyze_excel_structure()

            # 3. 确定目标工作表
            target_sheet = self.excel_util.determine_target_sheet(input_data.target_sheet)
            if target_sheet is None:
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="未找到目标工作表",
                    error="目标工作表不存在或无法确定"
                )

            # 4. 分析工作表结构
            sheet_structure = self.excel_util.analyze_sheet_structure(target_sheet)
            if not sheet_structure.get("has_data", False):
                return WorkflowResult(
                    status=WorkflowStatus.FAILED,
                    message="工作表没有数据",
                    error="目标工作表为空或无法读取数据"
                )

            # 5. 分析隐藏行情况
            visible_rows, hidden_rows = self.excel_util.analyze_hidden_rows(
                target_sheet, input_data.get_start_row()
            )

            # 6. 检查列隐藏状态
            columns_hidden_status = self.excel_util.check_columns_hidden(
                target_sheet, config.get("hidden_processing", {}).get("target_columns", [13, 14, 15])
            )

            # 7. 提取表头并创建映射
            _, english_fields, header_mapping = self.excel_util.extract_header_mapping(
                target_sheet, input_data.get_header_row()
            )

            # 8. 提取数据
            max_rows = config.get("max_rows", 10000)
            data_list = self.excel_util.extract_data_from_worksheet(
                target_sheet, english_fields, input_data.get_start_row(),
                input_data.include_hidden_rows, max_rows
            )

            # 9. 应用过滤条件
            if input_data.filter_major_category or input_data.filter_middle_category:
                data_list = self.excel_util.filter_data(
                    data_list, input_data.filter_major_category, input_data.filter_middle_category
                )

            # 10. AI分析处理（如果启用）
            ai_analysis_result = None
            if input_data.enable_ai_analysis:
                self.logger.info("开始AI分析处理")
                try:
                    need_rewrite = self.import_doc_util.process_check_sheet(
                        ar_no=input_data.ar_no or "",
                        ar_summary=input_data.ar_summary or "",
                        p_no=input_data.p_no or "",
                        check_sheet_list=data_list,
                        check_sheet_file_path=input_data.file_path,
                        target_sheet_name=target_sheet
                    )

                    # 更新Excel文件
                    update_success = self.import_doc_util.update_check_sheet(
                        need_rewrite=need_rewrite,
                        check_sheet_file_path=input_data.file_path,
                        target_sheet_name=target_sheet
                    )

                    ai_analysis_result = {
                        "analyzed_items": len(need_rewrite),
                        "update_success": update_success,
                        "analysis_details": need_rewrite
                    }

                    self.logger.info(f"AI分析完成，分析了 {len(need_rewrite)} 个项目，更新结果: {update_success}")

                except Exception as e:
                    self.logger.error(f"AI分析处理失败: {str(e)}")
                    ai_analysis_result = {
                        "analyzed_items": 0,
                        "update_success": False,
                        "error": str(e)
                    }

            # 11. 构建分析结果
            summary = f"成功处理 {len(data_list)} 个项目，其中隐藏项目 {len(hidden_rows)} 个"
            if ai_analysis_result:
                summary += f"，AI分析了 {ai_analysis_result['analyzed_items']} 个项目"

            analysis_result = AnalysisResultModel(
                total_items=len(visible_rows) + len(hidden_rows),
                processed_items=len(data_list),
                hidden_items=len(hidden_rows),
                categories=self._analyze_categories(data_list),
                summary=summary
            )

            # 12. 构建输出结果
            processing_info = {
                "total_rows": len(visible_rows) + len(hidden_rows),
                "visible_rows": len(visible_rows),
                "hidden_rows": len(hidden_rows),
                "include_hidden": input_data.include_hidden_rows,
                "filters_applied": bool(input_data.filter_major_category or input_data.filter_middle_category),
                "ai_analysis_enabled": input_data.enable_ai_analysis
            }

            # 如果有AI分析结果，添加到处理信息中
            if ai_analysis_result:
                processing_info["ai_analysis"] = ai_analysis_result

            output_data = DesignCSOutputModel(
                analysis_result=analysis_result,
                extracted_data=data_list,
                metadata={
                    "header_mapping": header_mapping,
                    "columns_hidden_status": columns_hidden_status,
                    "structure_info": structure_info,
                    "sheet_structure": sheet_structure
                },
                processing_info=processing_info,
                file_path=input_data.file_path,
                sheet_name=target_sheet,
                success=True
            )

            self.logger.info(f"成功提取 {len(data_list)} 行数据")

            return WorkflowResult(
                status=WorkflowStatus.SUCCESS,
                message=f"成功处理Excel文件，提取 {len(data_list)} 行数据",
                data=output_data.model_dump()
            )

        except Exception as e:
            self.logger.exception(f"执行工作流时发生异常: {str(e)}")
            return WorkflowResult(
                status=WorkflowStatus.FAILED,
                message=f"工作流执行失败: {str(e)}",
                error=str(e)
            )

    def _analyze_categories(self, data_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        分析数据中的分类统计

        Args:
            data_list: 数据列表

        Returns:
            Dict[str, int]: 分类统计结果
        """
        categories = {}

        for item in data_list:
            major_category = item.get('major_category', '').strip()
            if major_category:
                categories[major_category] = categories.get(major_category, 0) + 1

        return categories

    def post_execute(self, result: WorkflowResult) -> None:
        """
        执行后处理

        Args:
            result: 工作流执行结果
        """
        super().post_execute(result)

        # 清理资源
        if hasattr(self, 'excel_util') and self.excel_util:
            try:
                self.excel_util.close()
            except:
                pass

        self.logger.info("资源清理完成")
