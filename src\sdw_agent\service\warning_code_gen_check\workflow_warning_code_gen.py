"""
Warning Code Generation Workflow
警告代码生成工作流
"""
import os
import traceback
from typing import Optional

import win32com
from loguru import logger

from sdw_agent.service import BaseWorkflow
from sdw_agent.service.warning_code_gen_check.models import (
    WarningCodeGenConfig, WarningCodeGenResult, WarningChangeInfo
)
from sdw_agent.service.warning_code_gen_check.util.warning_code_gen_util import WarningCodeGenUtils
from sdw_agent.service.warning_code_gen_check.util.excel_operation_util import ExcelOperationUtils
from sdw_agent.service.warning_code_gen_check.util.warning_change_info_util import gen_warning_list_change_info
from sdw_agent.util.excel_util import click_buttons_in_wb
import xlwings as xw


class WarningCodeGenWorkflow(BaseWorkflow):
    """警告代码生成工作流"""
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__(config_path)
        self.utils = WarningCodeGenUtils()
        self.excel_utils = ExcelOperationUtils()
    
    def execute(self, config: WarningChangeInfo):
        """
        执行警告代码生成工作流
        
        Args:
            config: 警告代码生成配置
            
        Returns:
            警告代码生成结果
        """
        try:
            self.logger.info("开始执行警告代码生成工作流")
            
            warning_change_info = config
            # 生成警告列表变更信息
            result = gen_warning_list_change_info(warning_change_info)
            self.logger.info(f"警告列表变更信息生成完成: {result}")
            
            # 生成所有警告列表代码
            self._generate_all_warning_list_code(warning_change_info)
            
            return warning_change_info
            
        except Exception as e:
            self.logger.error(f"警告代码生成工作流执行失败: {str(e)}")
            return WarningCodeGenResult(
                success=False,
                message=f"执行失败: {str(e)}",
                error_details=traceback.format_exc()
            )
    
    def _validate_config(self, config: WarningCodeGenConfig) -> None:
        """验证配置参数"""
        if not config.after_sample_book_url:
            raise ValueError("变更后告警式样书路径不能为空")
        if not config.code_gen_tools_url:
            raise ValueError("告警代码生成工具地址不能为空")
        if not os.path.exists(config.after_sample_book_url):
            raise FileNotFoundError(f"变更后告警式样书文件不存在: {config.after_sample_book_url}")
        if not os.path.exists(config.code_gen_tools_url):
            raise FileNotFoundError(f"告警代码生成工具文件不存在: {config.code_gen_tools_url}")
    
    def _create_warning_change_info(self, config: WarningCodeGenConfig) -> WarningChangeInfo:
        """创建警告变更信息对象"""
        return WarningChangeInfo(
            pre_sample_book_url=config.pre_sample_book_url,
            after_sample_book_url=config.after_sample_book_url,
            code_gen_tools_url=config.code_gen_tools_url,
            relate_book_folder=config.relate_book_folder,
            adas_book_path=config.adas_book_path
        )
    
    def _generate_all_warning_list_code(self, warning_change_info: WarningChangeInfo) -> None:
        """生成所有警告列表代码"""
        try:
            # 生成新版本代码
            warning_change_info.after_code_tools_url = self._generate_warning_list_sheet(
                warning_change_info,
                warning_change_info.after_sample_book_url,
                warning_change_info.code_gen_tools_url,
                "new"
            )
            self.logger.success(f"完成生成预警列表代码 新版本：{warning_change_info.after_code_tools_url}")

            if warning_change_info.check_file_path != "":
                # 生成老版本代码
                if warning_change_info.pre_sample_book_url:
                    warning_change_info.pre_code_tools_url = self._generate_warning_list_sheet(
                        warning_change_info,
                        warning_change_info.pre_sample_book_url,
                        warning_change_info.code_gen_tools_url,
                        "old"
                    )
                    self.logger.success(f"完成生成预警列表代码 老版本：{warning_change_info.pre_code_tools_url}")

        except Exception as e:
            self.logger.error(f"生成警告列表代码失败: {str(e)}")
            raise

    def save_and_close_excel(self, file_path):
        """
        保存并关闭指定的 Excel 工作簿窗口
        :param file_path: Excel 文件的完整路径
        """
        # 尝试获取已有的Excel实例
        excel = win32com.client.GetActiveObject("Excel.Application")

        # 保存所有工作簿
        for workbook in excel.Workbooks:
            try:
                workbook.Save()
            except Exception as e:
                print(f"保存工作簿 {workbook.Name} 时出错: {e}")

        # 关闭所有工作簿
        for workbook in excel.Workbooks:
            try:
                workbook.Close(SaveChanges=True)
            except Exception as e:
                print(f"关闭工作簿 {workbook.Name} 时出错: {e}")

        # 退出Excel
        excel.Quit()
        print("Excel已成功关闭并保存")

    def _generate_warning_list_sheet(self, warning_change_info: WarningChangeInfo,
                                   sample_book_url: str, code_tools_url: str, save_type: str) -> str:
        """生成警告列表工作表"""
        app = None
        wb_sample_book_wb = None
        wb_code_gen_tools_wb = None
        
        try:
            # 打开 Excel 应用程序
            app = xw.App(visible=False)
            app.display_alerts = False
            app.screen_updating = False

            # 打开工作簿
            wb_sample_book_wb = app.books.open(sample_book_url)
            wb_code_gen_tools_wb = app.books.open(code_tools_url,
                read_only=False,        # 编辑模式
                update_links=0,         # 不更新链接（0=不更新）
                ignore_read_only_recommended=True,  # 忽略只读建议
                notify=False,           # 不通知
                add_to_mru=False,       # 不添加到最近使用
                local=False,            # 不使用本地设置
                corrupt_load=0          # 正常加载)
            )
            
            # 处理源表
            self._process_source_sheet(wb_sample_book_wb, wb_code_gen_tools_wb)
            self.logger.info(f"完成拷贝{sample_book_url}到{code_tools_url}")

            # 添加警告ID到函数
            self.excel_utils.add_warning_ids_to_function(wb_code_gen_tools_wb, warning_change_info)

            # 处理警告属性
            if warning_change_info.change_row_result:
                WarningCodeGenUtils._process_warning_properties(wb_code_gen_tools_wb, warning_change_info.change_row_result)

            # 点击按钮
            click_buttons_in_wb(wb_code_gen_tools_wb, [
                "sheet26.CommandButton3_Click",
                # "sheet26.CommandButton1_Click"
            ])

            if wb_sample_book_wb is not None:
                wb_sample_book_wb.close()
            if wb_code_gen_tools_wb is not None:
                wb_code_gen_tools_wb.close()
            if app is not None:
                app.quit()

            try:
                import win32com.client as win32
                # 强制创建新的 Excel 实例
                excel = win32com.client.DispatchEx("Excel.Application")
                excel.Application.Timeout = 30000  # 设置超时时间为 300 秒
                excel.Visible = True  # 显示 Excel 窗口
                wb = excel.Workbooks.Open(code_tools_url,
                                          ReadOnly=False,
                                          UpdateLinks=0)  # 替换为你的文件路径
                # 激活按钮所在的工作表
                sheet = wb.Worksheets("CONTDISP (源)")  # 替换为按钮所在的工作表名称
                sheet.Activate()
                excel.Application.Run("sheet26.CommandButton1_Click")
                # 关闭工作簿（根据需要保存更改）
                wb.Close(SaveChanges=True)
                excel.Quit()
            except:
                traceback.print_exc()
                self.save_and_close_excel(code_tools_url)
                pass
                # try:
                #     # 关闭工作簿（根据需要保存更改）
                #     if excel is None:
                #         excel = win32com.client.DispatchEx("Excel.Application")
                #     wb.Close(SaveChanges=True)
                #     excel.Quit()
                # except:
                #     logger.warning("两次关闭工作簿失败")

            # 打开 Excel 应用程序
            app = xw.App(visible=False)
            app.display_alerts = False
            app.screen_updating = False

            # 打开工作簿
            wb_sample_book_wb = app.books.open(sample_book_url)
            wb_code_gen_tools_wb = app.books.open(code_tools_url,
                read_only=False,        # 编辑模式
                update_links=0,         # 不更新链接（0=不更新）
                ignore_read_only_recommended=True,  # 忽略只读建议
                notify=False,           # 不通知
                add_to_mru=False,       # 不添加到最近使用
                local=False,            # 不使用本地设置
                corrupt_load=0          # 正常加载)
            )

            # 解析工具列
            self.utils.parse_tools_column(
                wb_code_gen_tools_wb,
                "CONTDISP（MET）",
            )

            # 添加项目到接口弹窗
            self.excel_utils.add_item_to_interface_popup(wb_code_gen_tools_wb, warning_change_info)

            # 点击按钮
            click_buttons_in_wb(wb_code_gen_tools_wb, [
                "sheet11.CommandButton2_Click",
                # "sheet11.CommandButton1_Click",
                # "sheet10.CommandButton1_Click"
            ])

            # 生成新文件路径
            new_path = self._generate_output_path(code_tools_url, save_type)
            
            # 保存工作簿
            wb_code_gen_tools_wb.save(new_path)
            
            return new_path

        except Exception as e:
            self.logger.error(f"生成警告列表工作表失败: {str(e)}")
            traceback.print_exc()
            raise e
        finally:
            # 清理资源
            self._cleanup_excel_resources(wb_sample_book_wb, wb_code_gen_tools_wb, app)
    
    def _process_source_sheet(self, wb_sample_book_wb, wb_code_gen_tools_wb) -> None:
        """处理源表"""
        try:
            warning_sheet_name = "一覧"
            warning_codetool_sheet_name = "CONTDISP (源)"
            
            # 获取工作表
            ws_sample_book_url = wb_sample_book_wb.sheets[warning_sheet_name]
            ws_code_gen_tools_url = wb_code_gen_tools_wb.sheets[warning_codetool_sheet_name]

            # 点击按钮并复制数据
            click_buttons_in_wb(wb_code_gen_tools_wb, ["sheet26.CommandButton2_Click"])
            self.excel_utils.copy_data_between_workbooks(
                ws_sample_book_url, 
                ws_code_gen_tools_url, 
                target_col=("A", "Y"), 
                row_offset=-3
            )
            
            # 更新警告值
            self.excel_utils.update_warning_for_value(wb_code_gen_tools_wb, warning_codetool_sheet_name)
            
        except Exception as e:
            self.logger.error(f"处理源表失败: {str(e)}")
            raise
    
    def _generate_output_path(self, original_path: str, save_type: str) -> str:
        """生成输出文件路径"""
        directory, filename = os.path.split(original_path)
        new_filename = f"{save_type}_{filename}"
        return os.path.join(directory, new_filename)
    
    def _cleanup_excel_resources(self, wb_sample_book_wb, wb_code_gen_tools_wb, app) -> None:
        """清理Excel资源"""
        try:
            if wb_sample_book_wb is not None:
                wb_sample_book_wb.close()
            if wb_code_gen_tools_wb is not None:
                wb_code_gen_tools_wb.close()
            if app is not None:
                app.quit()
        except Exception as e:
            self.logger.warning(f"清理Excel资源时出现警告: {str(e)}")


# 兼容性接口
def gen_all_warning_code_service(warning_change_info: WarningChangeInfo) -> WarningChangeInfo:
    """
    生成所有警告代码服务（兼容性接口）
    
    Args:
        warning_change_info: 警告变更信息
        
    Returns:
        更新后的警告变更信息
    """
    workflow = WarningCodeGenWorkflow()
    
    result = workflow.execute(warning_change_info)
    
    return result
