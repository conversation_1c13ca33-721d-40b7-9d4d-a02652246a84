version: 0.1.3

common:
  debug: False
  path: C:/

input_data_path: "C:/sdw_input"
#成果物输出路径
output_data_path: "C:/sdw_output"

design_check_sheet:
  judge_batch: true
  judge_length: 5

# 选择的大模型
llm_model: "gpt-4o"

gerrit:
  host: "http://************:8080"
  username: "shuangshuang_chen"
  password: "dnkt$202507"

git_repository_url:
  - "http://************:8085/technical-department/Orca/Orca_Code/PF/Orca_CR7_2"

jira:
  enable: false  # 设置为false以避免启动时的连接问题
  server: 'https://aip01.dndev.net/jira'
  pat: 'NDgyMDMyODg5MzE3OopylC4KORqZ2e93c4mosqjL0x5K'
  cert_path: 'C:\tdd_input\Jira证书\dn-gd-user-20250630-1.p12'
  cert_password: '8Ys16WeN'

# 日志配置
logging:
  level: INFO
  dir: logs
  file_prefix: test_agent
  rotation: "1 day"
  retention: "7 days"
  json_format: false
  console_output: true

## 告警表检查配置
warning_code_check:
  msg_sheets: r"dspmnscrl_msg(\d*)\.prm"


autodiff:
    BcompPath: "D:\\develop_tools\\Beyond Compare 4\\BCompare.exe"
    IgnorePattern: [".*cr7-res\\.bin", ".*\\.kzb"]
    BinPattern: [".*\\.bat",".*\\.bin"]


## 输入表信息配置
test_agent_input_table:
  changepoints:
    name: data/changepoints.json
    sheet_name:
      - 要件一览
    header_index: 5
  testcases:
    name: data/testcase_data.json
    sheet_name:
      - カスタマイズ項目一覧
      - 車両設定
      - 表示設定
      - 胎压
      - T_SCREEN
    header_index: 7
  checklists:
    name: data/checklists.json
    sheet_name:
      - CSTM Checklist
    header_index: 3
  rules:
    name: data/rules.json
    sheet_name:
      - CSTM_AI规则
    header_index: 1
  test_tree:
    name: data/testcases_.pkl
