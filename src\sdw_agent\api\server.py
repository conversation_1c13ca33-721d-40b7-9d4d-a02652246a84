"""
SDW Agent API服务器主入口
"""
# 首先设置日志配置，必须在其他导入之前
from sdw_agent.config.env import ENV  # noqa

# 现在可以安全地导入其他模块
from fastapi import FastAPI
from loguru import logger

# 导入共享组件
from sdw_agent.api.core.config import setup_app_middleware, setup_docs_routes
from sdw_agent.api.routers import get_all_routers

# 创建FastAPI应用实例
app = FastAPI(
    title="SDW Agent API",
    description="SDW Agent 多工作流API服务",
    version="1.0.0",
    docs_url="",
    redoc_url=""
)

# 设置中间件和配置
setup_app_middleware(app)
setup_docs_routes(app)

# 自动注册所有路由
routers = get_all_routers()
for router in routers:
    app.include_router(router)

logger.info(f"✓ 总共注册了 {len(routers)} 个路由模块")


def main():
    """启动服务器"""
    import uvicorn

    # 配置uvicorn使用我们的日志配置
    uvicorn.run(
        app,
        # "server:app",
        host='0.0.0.0',
        port=8001,
        log_config=None,  # 禁用uvicorn的默认日志配置
        access_log=True,  # 启用访问日志，但会被我们的拦截器处理
        # reload=True,
    )


if __name__ == '__main__':
    main()
