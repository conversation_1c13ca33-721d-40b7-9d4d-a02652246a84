"""
要件设计书照合实施工作流数据模型
"""

from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

from sdw_agent.model.request_model import SourceInfo


class ReqDesignVerifyInputModel(BaseModel):
    """要件设计书照合实施工作流输入模型"""
    excel_source: SourceInfo = Field(description="Excel文件的SourceInfo")
    target_sheet: Optional[str] = Field(default="機能一覧と新規・変更内容", description="目标工作表名称")
    custom_config: Optional[Dict[str, Any]] = Field(default=None, description="自定义配置")


class ReqDesignVerifyOutputModel(BaseModel):
    """要件设计书照合实施工作流输出模型（简化版，与原有service保持一致）"""
    status: str = Field(description="处理状态")
    message: str = Field(description="处理消息")
    output_file: str = Field(description="输出文件路径")


class ReqDesignVerifyConfigModel(BaseModel):
    """要件设计书照合实施工作流配置模型"""
    target_sheet: str = Field(default="機能一覧と新規・変更内容", description="目标工作表名称")
    header_start_row: int = Field(default=5, description="表头开始行")
    header_end_row: int = Field(default=7, description="表头结束行")
    data_start_row: int = Field(default=8, description="数据开始行")
    start_col: int = Field(default=1, description="开始列")
    end_col: int = Field(default=100, description="结束列")
    exclude_hidden_rows: bool = Field(default=True, description="是否排除隐藏行")
    req_content_column: str = Field(default="要件の内容", description="要件内容列名")
    design_book_column: str = Field(default="パラメータ設計書", description="设计书列名")
    consecutive_empty_limit: int = Field(default=5, description="连续空行停止限制")
    min_design_book_length: int = Field(default=3, description="设计书信息最小长度")
    output_subdir: str = Field(default="check_results", description="输出子目录名")
    cleanup_temp_files: bool = Field(default=True, description="是否清理临时文件")
    temp_file_prefix: str = Field(default="temp_req_design_verify_", description="临时文件前缀")
