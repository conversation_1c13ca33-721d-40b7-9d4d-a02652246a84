import gc
import logging
import os
import re
import time
from base64 import b64encode
from datetime import datetime
from io import BytesIO
from pathlib import Path
from threading import Lock
from typing import Union, Dict, Tuple, List

import openpyxl
import psutil
import pythoncom
import pywintypes
import win32com
from PIL import ImageGrab, Image
import imagehash
from openpyxl.cell.rich_text import TextBlock, CellRichText
from openpyxl.cell.text import InlineFont
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from win32com.client import Dispatch, GetActiveObject
from openpyxl.cell import Cell

from docparser.models.table import TableObject
from docparser.models.layout import LayoutObject
from docparser.models.position import Position

logger = logging.getLogger('docparser.excel.utils')

WIN32_LOCK = Lock()

def get_excel_shapes_win32com(excel_file, exclude_type_list=[4]):
    """
    把所有的图形转换为图片保存在本地
    图片命名规范： {sheet.Name.replace(' ', '_')}_shape_{shape.ID}.png
    exclude_type_list: 排除的类型列表
    """
    results = []
    try:
        # 初始化 COM 库
        pythoncom.CoInitialize()
        # 启动Excel应用程序
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        # 禁用只读提示
        excel.DisplayAlerts = False

        # 打开Excel文件
        try:
            # 打开Excel文件
            wb = excel.Workbooks.Open(excel_file)
            if wb.ReadOnly:
                logging.info(f"The file {excel_file} is in use and can currently only be opened in read-only mode!")
            else:
                logging.info(f"The file {excel_file} can be normally opened for reading and writing")
        except pythoncom.com_error as e:
            logger.error(f"The file {excel_file} cannot be opened normally, it may be in use. Error message: {e}")

        # 遍历所有工作表
        for sheet in wb.Sheets:
            # Sheet 隐藏或者特别隐藏
            if sheet.Visible in [0, 2]:
                continue

            # 遍历工作表中的所有形状
            for idx, shape in enumerate(sheet.Shapes):
                # type 4: 点; 1: 形状; 17:文本内容; 6: 形状的分组 13: 图片 ....
                if shape.Type in exclude_type_list:
                    logging.info(f"img save continue, shape.Name: {shape.Name}")
                    continue

                # 判断图形图像是否在单元格内
                shape_in_cell = is_shape_in_cell(shape)
                # logging.info(f"shape_info {sheet.Name} {shape.ID} {shape.Type} {shape.Width} {shape.Height} "
                #              f" {shape.Top} {shape.Left}  "
                #              f" {shape.TopLeftCell.Address} {shape.TopLeftCell.Column} {shape.TopLeftCell.Row} "
                #              f" {shape.TopLeftCell.RowHeight} {shape.TopLeftCell.ColumnWidth}"
                #              f" {shape.Text if 'Text' in shape else ''} {shape.Name} {idx} ")
                position = Position()
                position.x = shape.Left
                position.y = shape.Top
                position.width = shape.Width
                position.height = shape.Height
                result = {"type": "shape", "sheet_name": sheet.Name,
                          "id": shape.ID, "shape_type": shape.Type, "name": shape.Name,
                          "width": int(shape.Width), "height": int(shape.Height),
                          "from_row": int(shape.TopLeftCell.Row), "from_col": int(shape.TopLeftCell.Column),
                          "top": int(shape.Top), "left": int(shape.Left),
                          "from_row_off": 0, "from_col_off": 0,
                          "to_col": 0, "to_col_off": 0, "to_row": 0, "to_row_off": 0,
                          "index": shape.TopLeftCell.Address.replace("$", ""),
                          "in_cell": shape_in_cell,
                          "position": position
                          }
                # 获取文本内容
                if shape.Type in [1, 17]:
                    try:
                        tt = shape.TextFrame2.TextRange.Text
                        result["content"] = shape.TextFrame2.TextRange.Text.strip('\r')
                        # parse_textbox_properties(shape)
                        # result["content_style"] =
                    except Exception as ex:
                        logger.error(f"not found com shape.TextFrame2 sheet_name: {result['sheet_name']}"
                                     f"id: {result['id']} name: {result['name']} "
                                     f"type: {result['shape_type']}, err : {ex}")
                # # 将形状导出为图片
                # output_path = os.path.join(output_folder, get_shapes_image_name(sheet.Name, shape.ID))
                try:
                    shape.CopyPicture(Appearance=1, Format=2)  # 复制形状为图片

                    # 从剪贴板获取图像
                    img = ImageGrab.grabclipboard()

                    msg = f"id:{result['id']} name:{result['name']} shape_type:{result['shape_type']}"
                    # 如果剪贴板中有图像，保存它
                    if isinstance(img, Image.Image):
                        # img.save(output_path)
                        # result["path"] = os.path.abspath(output_path)

                        # 计算图片的base64
                        # 假设你已经有一个 PIL.Image.Image 对象
                        # image = Image.open(output_path)
                        # 创建一个字节流对象
                        buffered = BytesIO()
                        # 将图像保存到字节流中
                        img.save(buffered, format="PNG")
                        # 获取字节流的字节数据
                        result["data"] = b64encode(buffered.getvalue()).decode()

                        # 计算图片的hash
                        img_hash = get_image_hash(img)
                        result["hash"] = img_hash[0]
                        # time.sleep(0.1)
                        # logging.info(f"Saved image: {msg}")
                    else:
                        logger.info(f"not get image from CopyPicture {msg}")
                except Exception as ex:
                    logger.error(f"img save error {ex}")
                results.append(result)
        # time.sleep(0.6)

    finally:
        # 关闭Excel文件
        wb.Close(SaveChanges=False)
        excel.Quit()
        # time.sleep(1)
        try:
            if pythoncom:
                pythoncom.CoUninitialize()
        except Exception as e:
            pass
            #logger.error(e)
    return results

def is_shape_in_cell(shape):
    """
    判断图形是否完全位于单元格内（矩形且不考虑旋转）。

    :param shape: Excel Shape 对象
    :return: bool 表示图形是否完全位于单元格内
    """
    try:
        # 获取图形的左上角单元格
        top_left_cell = shape.TopLeftCell

        # 获取图形的右下角单元格
        bottom_right_cell = shape.BottomRightCell

        # 判断左上角和右下角单元格是否是同一个
        if (top_left_cell.Row == bottom_right_cell.Row and
                top_left_cell.Column == bottom_right_cell.Column):
            return True  # 图形完全位于单元格内
        else:
            return False  # 图形跨越了多个单元格
    except Exception as e:
        # 默认返回 True
        logging.error(f"Error while checking shape position: {e}")
        return True

def get_image_hash(img):
    img_hash = imagehash.average_hash(img)
    return str(img_hash), img_hash.hash.tolist()

def get_theme_colors(wb: Workbook):
    """Gets theme colors from the workbook"""
    # see: https://groups.google.com/forum/#!topic/openpyxl-users/I0k3TfqNLrc
    from openpyxl.xml.functions import QName, fromstring
    xlmns = 'http://schemas.openxmlformats.org/drawingml/2006/main'
    root = fromstring(wb.loaded_theme)
    themeEl = root.find(QName(xlmns, 'themeElements').text)
    colorSchemes = themeEl.findall(QName(xlmns, 'clrScheme').text)
    firstColorScheme = colorSchemes[0]

    colors = []

    for c in ['lt1', 'dk1', 'lt2', 'dk2', 'accent1', 'accent2', 'accent3', 'accent4', 'accent5', 'accent6']:
        accent = firstColorScheme.find(QName(xlmns, c).text)

        if 'window' in accent.getchildren()[0].attrib['val']:
            colors.append(accent.getchildren()[0].attrib['lastClr'])
        else:
            colors.append(accent.getchildren()[0].attrib['val'])

    return colors

def get_cell_bg_color(cell_: Cell, ws_with_pywin32):
    """
    获取单元格背景色
    https://openpyxl.readthedocs.io/en/latest/_modules/openpyxl/styles/colors.html
    :param cell_: openpyxl.cell.cell.Cell对象
    :return: 十六进制的颜色值
    """
    cell = ws_with_pywin32.Range(cell_.coordinate)
    return int_color_to_hex(int(cell.Interior.Color))

def int_color_to_hex(val: int) -> str:
    """ 整形颜色值转为十六进制 """
    assert type(val) is int, "function 'int_color_to_hex' need int param."
    r = val & 0xFF
    g = (val >> 8) & 0xFF
    b = (val >> 16) & 0xFF
    return "#{:02X}{:02X}{:02X}".format(r, g, b)

def get_layout(parent_ref):
    """
    构建 layout对象
    :param parent_ref:
    :return:
    """
    layout = LayoutObject()
    layout._parent_ref = parent_ref
    return layout

def get_cell_value(cell, sheet, from_merged_cells=True):
    """
    解析excel单元格的内容
    :param cell:  Cell对象
    :param sheet: Worksheet对象
    :param from_merged_cells: 在合并单元格中， 是否从合并单元格中第一个单元格中取值
    :return:
    """
    v = format_cell_value_with_openpyxl(cell)
    if isinstance(v, datetime):
        v = v.isoformat()  # 将 datetime 转换为 ISO 格式字符串
    elif isinstance(v, TextBlock):
        v = v.text
    elif isinstance(v, CellRichText):
        v = str(v)
    elif v is None:
        v = ''
    if v == '' and from_merged_cells:
        v = find_in_merged_cells(cell.row, cell.column, sheet.merged_cells)
    return v

def format_cell_value_with_openpyxl(cell):
    """
    格式化单元格的百分比的值
    :param cell: openpyxl.cell.cell.Cell对象
    :return: str
    """
    if cell.number_format.endswith("%") and isinstance(cell.value, (float, int)):
        # 百分比数值
        decimal_places = cell.number_format.count("0") - 1
        format_value = round(cell.value * 100, 2)
        return f"%.{decimal_places}f%%" % format_value
    return cell.value

def find_in_merged_cells(row_, col_, merged_cells_):
    """
    从合并单元格中查找所属内容
    :param row_:
    :param col_:
    :param merged_cells_:
    :return:
    """
    if not merged_cells_:
        return ""
    for r in merged_cells_.ranges:
        if r.min_row <= row_ <= r.max_row and r.min_col <= col_ <= r.max_col:
            return r.start_cell.value
    return ""

def find_text_object(current_cell, texts):
    """检查当前单元格是否连续, 并返回TextObject对象 """
    if not texts:
        return None
    (curr_row, curr_col) = current_cell
    return next(
        (text_ for text_ in texts
         for prev_row, prev_col in text_._cell_list
         if (curr_row == prev_row and curr_col == prev_col + 1)
         or (curr_row == prev_row + 1 and curr_col == prev_col)),
        None
    )

def fix_openpyxl_descriptors_base_bug():
    """
    修改 openpyxl的bug, openpyxl Guid 正则扩展为支持大小写字母
    :return:
    """
    from openpyxl.descriptors.base import MatchPattern
    from openpyxl.comments.comment_sheet import CommentRecord
    from openpyxl.workbook.properties import FileVersion
    from openpyxl.workbook.views import CustomWorkbookView
    from openpyxl.chartsheet.custom import CustomChartsheetView

    class GuidFix(MatchPattern):
        # https://msdn.microsoft.com/en-us/library/dd946381(v=office.12).aspx
        pattern = r"{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}"

    CommentRecord.guid = GuidFix(allow_none=True)
    FileVersion.codeName = GuidFix(allow_none=True)
    CustomWorkbookView.guid = GuidFix(allow_none=True)
    CustomChartsheetView.guid = GuidFix()

def get_position(sheet: Worksheet) -> Dict[Tuple[int, int], Position]:
    total_width = .0
    width_dict = {}
    total_height = .0
    height_dict = {}
    cell_position_dict: Dict[Tuple[int, int], Position] = {}
    for col_idx in range(1, sheet.max_column + 1):
        # Convert column index to column letter
        column_letter = sheet.cell(row=1, column=col_idx).column_letter
        column_dimension = sheet.column_dimensions.get(column_letter, None)
        column_width = column_dimension.width if column_dimension else 8.38  # Default width = 8.43
        total_width += column_width if column_width is not None else .0
        width_dict[col_idx] = column_width

    for row_idx in range(1, sheet.max_row + 1):
        row_dimension = sheet.row_dimensions.get(row_idx, None)
        row_height = row_dimension.height if row_dimension else 13.5  # Default height = 15
        total_height += row_height if row_height is not None else .0
        height_dict[row_idx] = row_height

    accu_width = 0
    for col_idx in range(1, sheet.max_column + 1):
        accu_height = 0
        current_width = width_dict[col_idx]
        for row_idx in range(1, sheet.max_row + 1):
            current_height = height_dict[row_idx]
            position = Position()
            position.x = accu_width / total_width
            position.y = accu_height / total_height
            position.width = current_width / total_width
            position.height = current_height / total_height
            cell_position_dict[(row_idx, col_idx)] = position

            accu_height += current_height
        accu_width += current_width

    for merged_range in sheet.merged_cells.ranges:

        min_row = merged_range.min_row
        max_row = merged_range.max_row
        min_col = merged_range.min_col
        max_col = merged_range.max_col

        start_tuple = (min_row, min_col)
        start_position = cell_position_dict[start_tuple]

        for row in range(min_row+1, max_row + 1):  # +1 to include the end_row
            current_position = cell_position_dict[(row, min_col)]
            start_position.height = start_position.height + current_position.height
            current_position.height = 0
        for col in range(min_col+1, max_col + 1):  # +1 to include the end_col
            current_position = cell_position_dict[(min_row, col)]
            start_position.width = start_position.width + current_position.width
            current_position.width = 0
        for row in range(min_row + 1, max_row + 1):
            for col in range(min_col + 1, max_col + 1):
                cell_position_dict.get((row, col)).width = 0
                cell_position_dict.get((row, col)).height = 0

    return cell_position_dict

def int_color_to_hex(val: int) -> str:
    """ 整形颜色值转为十六进制 """
    assert type(val) is int, "function 'int_color_to_hex' need int param."
    r = val & 0xFF
    g = (val >> 8) & 0xFF
    b = (val >> 16) & 0xFF
    return "#{:02X}{:02X}{:02X}".format(r, g, b)