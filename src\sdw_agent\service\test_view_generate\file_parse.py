#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：ParseFile
@File    ：file_parse.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2025/6/16 16:55
@Desc    : 说明
"""
import os
import shutil
from loguru import logger
from sdw_agent.service.test_view_generate.utils.excel_util import ReadExcel
from sdw_agent.service.test_view_generate.config import SUB_OUTPUT
from sdw_agent.config.env import ENV

input_path = ENV.config.input_data_path

class FileParser:
    @staticmethod
    def parse_main(properties):
        logger.info("开始解析文档")
        testcase_path = properties.get('std_testcase_file_path')
        shutil.copy(testcase_path, os.path.join(input_path, os.path.join(SUB_OUTPUT, "testcase.xlsx")))
        try:
            FileParser.parse_excel("testcases", properties.get('std_testcase_file_path'))
        except Exception as e:
            logger.error(f"解析testcases文档失败: {e}")
            return False, "解析testcases文档失败"
        try:
            FileParser.parse_excel("rules", properties.get('test_rule_file_path'))
        except Exception as e:
            logger.error(f"解析rules文档失败: {e}")
            return False, "解析rules文档失败"
        try:
            FileParser.parse_excel("checklists", properties.get('knowledge_repo_path'))
        except Exception as e:
            logger.error(f"解析checklists文档失败: {e}")
            return False, "解析checklists文档失败"
        logger.info("解析文档完成")
        return True, "解析文档完成"

    @staticmethod
    def parse_excel(path_type, excel_path):
        """
        解析测试用例、rules、checklist等excel
        """
        return ReadExcel.parse_excel(path_type, excel_path)


if __name__ == '__main__':
    file_path = {
        "std_testcase_file_path": r"D:\TEST Agent工作相关-wxy\730test_file\test_view_generate\19PFv3_TestCase_Auto_MET-G_CSTMLST-CSTD-A0-03-A-C0.xlsx",
        "test_rule_file_path": r"D:\TEST Agent工作相关-wxy\730test_file\test_view_generate\共通化规则.xlsx",
        "knowledge_repo_path": r"D:\TEST Agent工作相关-wxy\730test_file\test_view_generate\Checklist.xlsx"
    }
    FileParser.parse_main(file_path)
    print("ok")
