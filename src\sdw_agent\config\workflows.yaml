# 汽车软件开发AI辅助系统 - 工作流配置
# 定义了系统中预定义的工作流

workflows:
  # 完整的V字开发流程
  full_workflow:
    id: full_workflow
    name: 完整V字开发流程
    description: 包含所有DEV和TEST阶段步骤的完整流程
    steps:
      # DEV阶段
      - dev_1   # 要求规格读取
      - dev_2   # 法规确认
      - dev_3   # 软件设计标准CS（需求分析）
      - dev_4   # 功能一览与变更内容制作
      - dev_5   # 软件设计标准CS（基本设计）
      - dev_6   # 通信故障安全CS
      - dev_7   # 软件设计书制作
      - dev_8   # I/F整合性确认
      - dev_9   # CAN入出力一览确认
      - dev_10  # 函数式样书制作
      - dev_11  # RAM设计书制作
      - dev_12  # 软件设计标准CS（详细设计）
      - dev_13  # 设计同行评审
      - dev_14  # 功能一览与变更内容更新
      - dev_15  # 编码标准确认&结果验证
      - dev_16  # 文件比较实施
      - dev_17  # 自我检查与代码审查视角CS
      - dev_18  # 代码审查
      - dev_19  # 软件设计标准CS（代码检查）
      # TEST阶段
      - test_1  # 检查表IV确认和结果验证
      - test_2  # 结合检查规格书审查
      - test_3  # 检查表V确认和结果验证
      - test_4  # 软件设计标准CS（耦合检查）
      - test_5  # 通信检查与结果验证
      - test_6  # 真实设备评估与结果验证

  # 仅DEV阶段流程
  dev_workflow:
    id: dev_workflow
    name: 开发阶段流程
    description: 仅包含DEV阶段的所有步骤
    steps:
      - dev_1
      - dev_2
      - dev_3
      - dev_4
      - dev_5
      - dev_6
      - dev_7
      - dev_8
      - dev_9
      - dev_10
      - dev_11
      - dev_12
      - dev_13
      - dev_14
      - dev_15
      - dev_16
      - dev_17
      - dev_18
      - dev_19

  # 仅TEST阶段流程
  test_workflow:
    id: test_workflow
    name: 测试阶段流程
    description: 仅包含TEST阶段的所有步骤
    steps:
      - test_1
      - test_2
      - test_3
      - test_4
      - test_5
      - test_6

  # 设计标准CS流程
  design_standard_workflow:
    id: design_standard_workflow
    name: 设计标准CS流程
    description: 包含所有设计标准CS相关步骤
    steps:
      - dev_1   # 要求规格读取
      - dev_2   # 法规确认
      - dev_3   # 软件设计标准CS（需求分析）
      - dev_5   # 软件设计标准CS（基本设计）
      - dev_12  # 软件设计标准CS（详细设计）
      - dev_19  # 软件设计标准CS（代码检查）
      - test_4  # 软件设计标准CS（耦合检查）

  # 通信相关流程
  communication_workflow:
    id: communication_workflow
    name: 通信检查流程
    description: 包含所有通信相关的步骤
    steps:
      - dev_6   # 通信故障安全CS
      - dev_9   # CAN入出力一览确认
      - test_5  # 通信检查与结果验证

  # 代码审查流程
  code_review_workflow:
    id: code_review_workflow
    name: 代码审查流程
    description: 代码审查相关步骤
    steps:
      - dev_15  # 编码标准确认
      - dev_16  # 文件比较实施
      - dev_17  # 自我检查
      - dev_18  # 代码审查
      - dev_19  # 代码检查

# 步骤元数据（用于快速查找）
step_metadata:
  # DEV阶段步骤
  dev_1:
    type: Agent
    name: 要求规格读取
    phase: DEV
    category: requirement
  
  dev_2:
    type: Agent
    name: 法规确认
    phase: DEV
    category: compliance
  
  dev_3:
    type: Agent
    name: 软件设计标准CS（需求分析）
    phase: DEV
    category: design_standard
  
  dev_4:
    type: Tool
    name: 功能一览与变更内容制作
    phase: DEV
    category: documentation
  
  dev_5:
    type: Agent
    name: 软件设计标准CS（基本设计）
    phase: DEV
    category: design_standard
  
  dev_6:
    type: Agent
    name: 通信故障安全CS
    phase: DEV
    category: communication
  
  dev_7:
    type: Tool
    name: 软件设计书制作
    phase: DEV
    category: documentation
  
  dev_8:
    type: Agent
    name: I/F整合性确认
    phase: DEV
    category: interface
  
  dev_9:
    type: Agent
    name: CAN入出力一览确认
    phase: DEV
    category: communication
  
  dev_10:
    type: Tool
    name: 函数式样书制作
    phase: DEV
    category: documentation
  
  dev_11:
    type: Tool
    name: RAM设计书制作
    phase: DEV
    category: documentation
  
  dev_12:
    type: Agent
    name: 软件设计标准CS（详细设计）
    phase: DEV
    category: design_standard
  
  dev_13:
    type: Agent
    name: 设计同行评审
    phase: DEV
    category: review
  
  dev_14:
    type: Tool
    name: 功能一览与变更内容更新
    phase: DEV
    category: documentation
  
  dev_15:
    type: Agent
    name: 编码标准确认&结果验证
    phase: DEV
    category: code_review
  
  dev_16:
    type: Agent
    name: 文件比较实施
    phase: DEV
    category: code_review
  
  dev_17:
    type: Tool
    name: 自我检查与代码审查视角CS
    phase: DEV
    category: code_review
  
  dev_18:
    type: Agent
    name: 代码审查
    phase: DEV
    category: code_review
  
  dev_19:
    type: Agent
    name: 软件设计标准CS（代码检查）
    phase: DEV
    category: design_standard
  
  # TEST阶段步骤
  test_1:
    type: Agent
    name: 检查表IV确认和结果验证
    phase: TEST
    category: checklist
  
  test_2:
    type: Agent
    name: 结合检查规格书审查
    phase: TEST
    category: test_review
  
  test_3:
    type: Agent
    name: 检查表V确认和结果验证
    phase: TEST
    category: checklist
  
  test_4:
    type: Agent
    name: 软件设计标准CS（耦合检查）
    phase: TEST
    category: design_standard
  
  test_5:
    type: Agent
    name: 通信检查与结果验证
    phase: TEST
    category: communication
  
  test_6:
    type: Tool
    name: 真实设备评估与结果验证
    phase: TEST
    category: device_test 