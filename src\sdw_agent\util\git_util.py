import os
import pathlib
import subprocess
import re
from loguru import logger
from urllib.parse import quote
from fastapi_babel import _

__all = [
    "get_git_raw_diff",
    "get_full_code_from_git",
    "get_git_structured_diff",
    "clone_repo_of_branch",
]

def get_full_code_diff_from_git(repo_path: str, commit_id: str, compared_commit_id: str = None):
    """
        获取本地仓库两个commit之间的diff内容。
        如果compared_commit_id未指定，则与commit_id的父提交比较。
        返回 (项目名称, 分支名, diff原始内容 包含整个文件的内容，包含未修改的内容)
        """
    # 验证repo_path是否为有效的git仓库
    git_dir = os.path.join(repo_path, ".git")
    if not os.path.isdir(repo_path) or not os.path.isdir(git_dir):
        raise ValueError(f"指定路径不是有效的git仓库: {repo_path}")

    _check_commit_id(repo_path, commit_id)

    # 获取项目名称（仓库目录名）
    project_name = os.path.basename(os.path.abspath(repo_path))

    # 获取父提交
    if not compared_commit_id:
        # 先查找commit_id的父提交
        cmd_parent = [
            "git", "-C", repo_path, "rev-list", "--parents", "-n", "1", commit_id
        ]
        result = subprocess.run(cmd_parent, capture_output=True, text=True, check=True)
        parent_line = result.stdout.strip()
        parts = parent_line.split()
        # parts[0] 是 commit_id, 后面是父提交id（可能没有）
        if len(parts) == 1:
            # 没有父提交，第一次提交
            compared_commit_id = None
        else:
            compared_commit_id = parts[1]

    # 检查compared_commit_id是否存在（如果有）
    if compared_commit_id:
        _check_commit_id(repo_path, compared_commit_id)

    # 获取 compared_commit_id 和 commit_id 两个提交之间的 diff
    if compared_commit_id:
        cmd_diff = [
            "git", "-C", repo_path, "diff", f"{compared_commit_id}", f"{commit_id}", "-U9999"
        ]
    else:
        # 第一次提交，与空树比较
        # 使用空树哈希(4b825dc...是Git的空树哈希)
        empty_tree = "4b825dc642cb6eb9a060e54bf8d69288fbee4904"
        cmd_diff = [
            "git", "-C", repo_path, "diff", empty_tree, commit_id, "-U9999"
        ]

    result = subprocess.run(cmd_diff, capture_output=True, text=True, check=True, encoding="utf-8", errors="replace")
    diff_content = result.stdout

    return project_name, diff_content

def get_git_raw_diff(repo_path: str, commit_id: str, compared_commit_id: str = None) -> tuple[str, str]:
    """
    获取本地仓库两个commit之间的diff内容。
    如果compared_commit_id未指定，则与commit_id的父提交比较。
    返回 (项目名称, 分支名, diff原始内容)
    注意：如果commit_id在多个分支上存在，只要commit_id和compared_commit_id相同，git diff 的内容是一样的，
    因为git diff 只比较这两个commit的内容，与分支无关。
    如果commit_id为仓库的第一次提交（没有父提交），则 compared_commit_id 不存在，此时 diff 为该提交的所有内容（与空树的diff）。
    """
    # 验证repo_path是否为有效的git仓库
    git_dir = os.path.join(repo_path, ".git")
    if not os.path.isdir(repo_path) or not os.path.isdir(git_dir):
        raise ValueError(f"指定路径不是有效的git仓库: {repo_path}")

    _check_commit_id(repo_path, commit_id)

    # 获取项目名称（仓库目录名）
    project_name = os.path.basename(os.path.abspath(repo_path))

    # 获取父提交
    if not compared_commit_id:
        # 先查找commit_id的父提交
        cmd_parent = [
            "git", "-C", repo_path, "rev-list", "--parents", "-n", "1", commit_id
        ]
        result = subprocess.run(cmd_parent, capture_output=True, text=True, check=True)
        parent_line = result.stdout.strip()
        parts = parent_line.split()
        # parts[0] 是 commit_id, 后面是父提交id（可能没有）
        if len(parts) == 1:
            # 没有父提交，第一次提交
            compared_commit_id = None
        else:
            compared_commit_id = parts[1]

    # 检查compared_commit_id是否存在（如果有）
    if compared_commit_id:
        _check_commit_id(repo_path, compared_commit_id)

    # 获取 compared_commit_id 和 commit_id 两个提交之间的 diff
    if compared_commit_id:
        cmd_diff = [
            "git", "-C", repo_path, "diff", f"{compared_commit_id}", f"{commit_id}"
        ]
    else:
        # 第一次提交，与空树比较
        cmd_diff = [
            "git", "-C", repo_path, "diff", f"{commit_id}^{{tree}}", f"{commit_id}"
        ]
        
    result = subprocess.run(cmd_diff, capture_output=True, text=True, check=True, encoding="utf-8", errors="replace")
    diff_content = result.stdout

    return project_name, diff_content

def _check_commit_id(repo_path: str, commit_id: str) -> bool:
    """
    检查commit_id是否存在于repo_path的git仓库中。
    """
    cmd_check_compared = ["git", "-C", repo_path, "cat-file", "-e", f"{commit_id}^{{commit}}"]
    try:
        subprocess.run(cmd_check_compared, capture_output=True, text=True, check=True)
        return True
    except subprocess.CalledProcessError:

        raise ValueError(f"指定commit_id在本地仓库不存在，请拉取正确目标分支代码: {commit_id}")

def _parse_unified_diff(diff_text: str, commit_id: str, project_name: str):
    """
    解析git diff输出，返回一个字典，键为文件路径，值为一个字典，包含以下键：
    - project: 项目名称
    - commit_id: 提交ID
    - file_path: 文件路径
    - diff: [
            {
                type: "a|b|ab",
                content: [行内容, ...]
            }
        ]
        a、b和ab，分别表示旧文件、新文件和旧和新文件的内容
    - changed_lines: 一个字典，包含三个键：added、deleted和common，分别表示添加、删除和 unchanged 的行数
    """
    result = {}
    diff_lines = diff_text.splitlines()
    i = 0
    
    while i < len(diff_lines):
        line = diff_lines[i]
        
        if line.startswith('diff --git'):
            i = _process_file_header(diff_lines, i, result, commit_id, project_name)
        elif line.startswith('@@'):
            i = _process_hunk(diff_lines, i, result)
        else:
            i += 1
            
    return result


def _process_file_header(diff_lines, i, result, commit_id, project_name):
    """处理文件头部信息"""
    line = diff_lines[i]
    m = re.match(r'diff --git a/(.*?) b/(.*)', line)
    if m:
        file_path = m.group(2)
        result[file_path] = {
            "project": project_name,
            "commit_id": commit_id,
            "file_path": file_path,
            "diff": [],
            "changed_lines": {"added": [], "deleted": [], "common": []}
        }
    return i + 1


def _process_hunk(diff_lines, i, result):
    """处理一个diff块"""
    line = diff_lines[i]
    current_file = _get_current_file(result)
    if not current_file:
        return i + 1
        
    m = re.match(r'@@ -(\d+),?\d* \+(\d+),?\d* @@', line)
    if not m:
        return i + 1
        
    old_line = int(m.group(1))
    new_line = int(m.group(2))
    i += 1
    
    return _process_hunk_lines(diff_lines, i, result, current_file, old_line, new_line)


def _get_current_file(result):
    """获取当前处理的文件路径"""
    if not result:
        return None
    return list(result.keys())[-1]


def _process_hunk_lines(diff_lines, i, result, current_file, old_line, new_line):
    """处理diff块中的行"""
    while i < len(diff_lines):
        line = diff_lines[i]

        if line.startswith('diff --git') or line.startswith('@@'):
            return i  # 让外层循环重新处理

        if line.startswith('-'):
            if result[current_file]["diff"] and result[current_file]["diff"][-1]['type']=='a':
                result[current_file]["diff"][-1]['content'].append(line[1:])
            else:
                result[current_file]["diff"].append({'type': 'a', 'content': [line[1:]]})
            result[current_file]["changed_lines"]["deleted"].append(old_line)
            old_line += 1
        elif line.startswith('+'):
            if result[current_file]["diff"] and result[current_file]["diff"][-1]['type']=='b':
                result[current_file]["diff"][-1]['content'].append(line[1:])
            else:
                result[current_file]["diff"].append({'type': 'b', 'content': [line[1:]]})
            result[current_file]["changed_lines"]["added"].append(new_line)
            new_line += 1
        elif line.startswith(' '):
            if result[current_file]["diff"] and result[current_file]["diff"][-1]['type']=='ab':
                result[current_file]["diff"][-1]['content'].append(line[1:])
            else:
                result[current_file]["diff"].append({'type': 'ab', 'content': [line[1:]]})
            result[current_file]["changed_lines"]["common"].append(new_line)
            old_line += 1
            new_line += 1

        i += 1

    return i


def get_git_structured_diff(repo_path: str, commit_id: str, compared_commit_id: str = None) -> dict:
    """
    从本地git仓库获取两个commit之间的diff内容，并解析为字典。
    如果compared_commit_id未指定，则与commit_id的父提交比较。
    获取到的code diff 为结构化格式
    """
    project_name, diff = get_full_code_diff_from_git(repo_path, commit_id, compared_commit_id)
    return _parse_unified_diff(diff, commit_id, project_name)


def build_auth_repo_url(repo_url: str, username: str, password: str) -> str:
    '''
    构建auth http repo url
    '''
    if not (username and password):
        return repo_url

    encoded_password = quote(password, safe='')
    if '@' in repo_url:
        protocol, rest = repo_url.split('://', 1)
        path = rest.split('@', 1)[1]
        repo_url = f"{protocol}://{username}:{encoded_password}@{path}"
    else:
        repo_url = repo_url.replace('://', f'://{username}:{encoded_password}@')
    if repo_url.endswith('.git'):
        repo_url = repo_url[:-4]
    return repo_url

def clone_repo(repo_url: str, branch: str, repo_local_path: str):
    '''
    克隆远程仓库到本地
    '''
    logger.info(f"克隆仓库: {repo_url}")
    git_clone_command = ['git', 'clone', '-b', branch, '--single-branch', repo_url, str(repo_local_path)]
    result = subprocess.run(git_clone_command, capture_output=True, text=True, timeout=300)
    if result.returncode != 0:
        error_msg = result.stderr if result.stderr else "未知错误"
        raise RuntimeError(f"克隆失败：{error_msg}")

def update_repo(repo_local_path: str, branch: str):
    '''
    更新本地仓库到指定分支的最新代码。
    如果分支不存在，则尝试创建并跟踪远程分支。
    '''
    fetch_command = ['git', '-C', str(repo_local_path), 'fetch', 'origin', branch]
    logger.info(f"拉取分支最新代码：{branch}")
    logger.info(' '.join(fetch_command))
    result = subprocess.run(fetch_command, capture_output=True, text=True, timeout=300)
    if result.returncode != 0:
        error_msg = result.stderr if result.stderr else f"拉取失败：{branch}"
        raise RuntimeError(error_msg)

    # 确保切换到指定分支
    checkout_command = ['git', '-C', str(repo_local_path), 'checkout', branch]
    logger.info(f"切换到分支: {branch}")
    logger.info(' '.join(checkout_command))
    checkout_result = subprocess.run(checkout_command, capture_output=True, text=True, timeout=60)
    if checkout_result.returncode != 0:
        # 分支不存在则尝试创建并跟踪远程分支
        logger.info(f"分支不存在，尝试创建并跟踪远程分支: {branch}")
        checkout_trace_command = ['git', '-C', str(repo_local_path), 'checkout', '-b', branch, f'origin/{branch}']
        logger.info(' '.join(checkout_trace_command))
        create_result = subprocess.run(checkout_trace_command, capture_output=True, text=True, timeout=60)
        if create_result.returncode != 0:
            error_msg = create_result.stderr if create_result.stderr else "未知错误"
            raise RuntimeError(f"切换或创建分支失败：{error_msg}")

    # 拉取最新代码
    pull_command = ['git', '-C', str(repo_local_path), 'pull', 'origin', branch]
    logger.info(f"拉取分支最新代码：{branch}")
    logger.info(' '.join(pull_command))
    pull_result = subprocess.run(pull_command, capture_output=True, text=True, timeout=120)
    if pull_result.returncode != 0:
        raise RuntimeError(f"警告: 更新分支失败:{pull_result.stderr}")

@logger.catch(reraise=True)
def clone_repo_of_branch(repo_url: str, branch: str, repo_local_path: str, username: str = "", password: str = ""):
    """
    克隆或拉取 Git 仓库，切换到指定分支
    """
    try:

        repo_local_path = str(pathlib.Path(repo_local_path))

        repo_url = build_auth_repo_url(repo_url, username, password)
        repo_name = repo_url.split('/')[-1]

        if repo_local_path.split('\\')[-1] != repo_name:
            repo_local_path = os.path.join(repo_local_path, repo_name)

        if not os.path.exists(repo_local_path):
            clone_repo(repo_url, branch, repo_local_path)
        else:
            update_repo(repo_local_path, branch)
        return repo_local_path
    except subprocess.TimeoutExpired:
        logger.info("发生错误: 操作超时，可能是网络问题或仓库过大")
        raise RuntimeError("操作超时，可能是网络问题或仓库过大")
    except Exception as e:
        logger.info(f"发生错误: {str(e)}")
        raise RuntimeError(f"克隆或更新仓库失败: {str(e)}")



if __name__ == "__main__":

    # 配置参数
    repo_url = "http://172.30.19.19:8085/technical-department/Orca/Orca_Code/PF/Orca_CR7_2"  # 替换为实际仓库URL
    username = "shuangshuang_chen"
    password = "dnkt$202507"
    branch = "develop_2.5_732D"  # 替换为目标分支名称
    repo_local_path = r"D:\dnkt_test"

    # 执行函数
    clone_repo_of_branch(repo_url, branch, repo_local_path, username, password)
