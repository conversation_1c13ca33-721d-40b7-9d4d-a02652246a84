"""工作流引擎单元测试"""
import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sdw_agent.core import (
    WorkflowEngine,
    WorkflowDefinition,
    StepType,
    StepStatus,
    StepInput,
    StepOutput,
    BaseStep
)


class MockStep(BaseStep):
    """测试用的模拟步骤"""
    
    def __init__(self, step_id, name, dependencies=None, should_fail=False):
        super().__init__(
            step_id=step_id,
            name=name,
            step_type=StepType.AGENT,
            dependencies=dependencies or []
        )
        self.should_fail = should_fail
        self.was_executed = False
        
    def validate_input(self, input_data):
        return True
        
    def process(self, input_data):
        self.was_executed = True
        if self.should_fail:
            raise RuntimeError(f"步骤 {self.step_id} 故意失败")
        return StepOutput(
            data={"step_id": self.step_id, "result": "success"},
            status=StepStatus.SUCCESS
        )


class TestWorkflowEngine(unittest.TestCase):
    """工作流引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 禁用日志
        logging.disable(logging.CRITICAL)
        
        # 创建引擎实例
        self.engine = WorkflowEngine(max_parallel_workers=2)
        
        # 创建测试步骤
        self.step_a = MockStep("step_a", "步骤A")
        self.step_b = MockStep("step_b", "步骤B", dependencies=["step_a"])
        self.step_c = MockStep("step_c", "步骤C", dependencies=["step_a"])
        self.step_d = MockStep("step_d", "步骤D", dependencies=["step_b", "step_c"])
        self.step_e = MockStep("step_e", "步骤E", should_fail=True)
        
        # 注册步骤
        for step in [self.step_a, self.step_b, self.step_c, self.step_d, self.step_e]:
            self.engine.register_step(step)
            
        # 创建测试工作流
        self.workflow = WorkflowDefinition(
            workflow_id="test_workflow",
            name="测试工作流",
            description="用于测试的工作流",
            steps=["step_a", "step_b", "step_c", "step_d"]
        )
        
        # 注册工作流
        self.engine.register_workflow(self.workflow)
        
    def tearDown(self):
        """测试后清理"""
        # 恢复日志
        logging.disable(logging.NOTSET)
        
    def test_step_registration(self):
        """测试步骤注册"""
        self.assertEqual(len(self.engine.steps_registry), 5)
        self.assertIn("step_a", self.engine.steps_registry)
        self.assertIn("step_b", self.engine.steps_registry)
        
    def test_workflow_registration(self):
        """测试工作流注册"""
        self.assertEqual(len(self.engine.workflows_registry), 1)
        self.assertIn("test_workflow", self.engine.workflows_registry)
        
    def test_execution_plan_creation(self):
        """测试执行计划创建"""
        plan = self.engine.create_execution_plan("test_workflow")
        
        # 验证步骤列表
        self.assertEqual(len(plan.steps_to_execute), 4)
        self.assertListEqual(plan.steps_to_execute, ["step_a", "step_b", "step_c", "step_d"])
        
        # 验证并行组
        self.assertEqual(len(plan.parallel_groups), 3)  # 应该有3层
        self.assertListEqual(plan.parallel_groups[0], ["step_a"])  # 第1层
        self.assertIn("step_b", plan.parallel_groups[1])  # 第2层包含B和C
        self.assertIn("step_c", plan.parallel_groups[1])  # 第2层包含B和C
        self.assertListEqual(plan.parallel_groups[2], ["step_d"])  # 第3层
        
    def test_workflow_execution_success(self):
        """测试工作流成功执行"""
        plan = self.engine.create_execution_plan("test_workflow")
        result = self.engine.execute_workflow(plan)
        
        # 验证执行结果
        self.assertEqual(result.status, StepStatus.SUCCESS)
        self.assertEqual(len(result.step_results), 4)
        
        # 验证每个步骤都被执行了
        self.assertTrue(self.step_a.was_executed)
        self.assertTrue(self.step_b.was_executed)
        self.assertTrue(self.step_c.was_executed)
        self.assertTrue(self.step_d.was_executed)
        
        # 验证步骤执行顺序（通过检查结果中的时间戳）
        a_end_time = result.step_results["step_a"].end_time
        b_end_time = result.step_results["step_b"].end_time
        c_end_time = result.step_results["step_c"].end_time
        d_end_time = result.step_results["step_d"].end_time
        
        self.assertLess(a_end_time, b_end_time)  # A在B之前完成
        self.assertLess(a_end_time, c_end_time)  # A在C之前完成
        self.assertLess(b_end_time, d_end_time)  # B在D之前完成
        self.assertLess(c_end_time, d_end_time)  # C在D之前完成
        
    def test_workflow_execution_failure(self):
        """测试工作流执行失败"""
        # 创建包含失败步骤的工作流
        fail_workflow = WorkflowDefinition(
            workflow_id="fail_workflow",
            name="失败测试工作流",
            description="包含失败步骤的工作流",
            steps=["step_a", "step_e"]
        )
        self.engine.register_workflow(fail_workflow)
        
        # 执行工作流
        plan = self.engine.create_execution_plan("fail_workflow")
        result = self.engine.execute_workflow(plan)
        
        # 验证执行结果
        self.assertEqual(result.status, StepStatus.FAILED)
        self.assertEqual(len(result.step_results), 2)
        self.assertEqual(result.step_results["step_a"].status, StepStatus.SUCCESS)
        self.assertEqual(result.step_results["step_e"].status, StepStatus.FAILED)
        
    def test_selective_execution(self):
        """测试选择性执行"""
        # 只执行部分步骤
        plan = self.engine.create_execution_plan("test_workflow", ["step_a", "step_b"])
        result = self.engine.execute_workflow(plan)
        
        # 验证只有选定的步骤被执行
        self.assertEqual(len(result.step_results), 2)
        self.assertTrue(self.step_a.was_executed)
        self.assertTrue(self.step_b.was_executed)
        self.assertFalse(self.step_c.was_executed)
        self.assertFalse(self.step_d.was_executed)
        
    def test_data_passing(self):
        """测试数据传递"""
        # 准备初始数据
        initial_data = {"test_key": "test_value"}
        
        # 执行工作流
        plan = self.engine.create_execution_plan("test_workflow")
        result = self.engine.execute_workflow(plan, initial_data)
        
        # 验证数据传递
        self.assertEqual(result.status, StepStatus.SUCCESS)
        
        # 在实际应用中，我们会检查步骤之间的数据传递
        # 这里我们只能验证工作流成功执行
        self.assertEqual(result.status, StepStatus.SUCCESS)


if __name__ == "__main__":
    unittest.main() 