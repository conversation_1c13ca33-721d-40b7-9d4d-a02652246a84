from sdw_agent.util.excel.core import CellStyle

bin_compare_excel_sheet = {
    "agl_app_b.txt": ("app", 2),
    "agl_app_a.txt": ("app", 1),
    "uboot.txt": ("boot1", 0),
    "kernel_a.txt": ("kernel", 1),
    "kernel_b.txt": ("kernel", 2),
    "kanzi_a.txt": ("kanzi", 1),
    "kanzi_b.txt": ("kanzi", 2),
    "rootfs_a.txt": ("rootfs", 1),
    "rootfs_b.txt": ("rootfs", 2),
    "com1.txt": ("com1", 0),
    "com2.txt": ("com2", 0),
    "user_a.txt": ("user_a", 1),
    "user_b.txt": ("user_a", 2),
    "user_a1.txt": ("user_a1", 1),
    "user_b1.txt": ("user_a1", 2),
    "custom.txt": ("custom", 0),
}

manifest_compare_file_map = {
    "agl_kanzi": "build.sh",
    "agl_srv": "AGL_srv_release.xml",
    "agl_kernel": "AGL_release.xml",
    "onclick_tool": "build_linux.sh"
}

manifest_compare_sheet_map = {
    "agl_kanzi": "子manifest比較-AGL_KANZI",
    "agl_srv": "子manifest比較-AGL Services",
    "agl_kernel": "子manifest比較-AGL kernel",
    "onclick_tool": "子manifest比較-OneClickBuild"
}

manifest_sub_map = {
    '''project path="AGL_KANZI"''': "agl_kanzi", '''<submanifest name="AGLKnl"''': "agl_kernel",
    '''<submanifest name="AGLSrv"''':"agl_srv",'''<submanifest name="AGL_Knl"''':"onclick_tool"}

spec_compare_excel_sheet_list = [
    "user_a",
    "user_a1",
]

# 定义git风格的样式
bin_compare_styles = {
    'header': CellStyle(
        font_name="Calibri",
        font_size=12,
        font_bold=True,
        bg_color="4472C4",
        font_color="FFFFFF",
        alignment_horizontal="center",
        alignment_vertical="center"
    ),
    'same': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_color="000000",
        bg_color="FFFFFF"
    ),
    'added': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="006600",
        bg_color="E6FFE6"
    ),
    'deleted': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="CC0000",
        bg_color="FFE6E6"
    ),
    'modified': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="FF6600",
        bg_color="FFF2E6"
    ),
    'line_number': CellStyle(
        font_name="Consolas",
        font_size=8,
        font_color="666666",
        bg_color="F5F5F5",
        alignment_horizontal="center"
    )
}

manifest_compare_styles = {
    'header': CellStyle(
        font_name="Calibri",
        font_size=12,
        font_bold=True,
        bg_color="E6E6FA",
        alignment_horizontal="center",
        alignment_vertical="center"
    ),
    'same': CellStyle(
        font_name="Calibri",
        font_size=11,
        font_color="000000",
        bg_color="FFFFFF"
    ),
    'added': CellStyle(
        font_name="Calibri",
        font_size=11,
        font_bold=True,
        font_color="FF0000",
        bg_color="FFE6E6"
    ),
    'deleted': CellStyle(
        font_name="Calibri",
        font_size=11,
        font_bold=True,
        font_color="FF0000",
        bg_color="FFE6E6"
    ),
    'modified': CellStyle(
        font_name="Calibri",
        font_size=11,
        font_bold=True,
        font_color="FF0000",
        bg_color="FFCCCC"
    )
}

# 定义样式 - 使用CellStyle类
jenkins_script_compare_styles = {
    'header': CellStyle(
        font_name="Calibri",
        font_size=12,
        font_bold=True,
        bg_color="4472C4",
        font_color="FFFFFF",
        alignment_horizontal="center",
        alignment_vertical="center"
    ),
    'same': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_color="000000",
        bg_color="FFFFFF"
    ),
    'added': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="006600",
        bg_color="E6FFE6"
    ),
    'deleted': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="CC0000",
        bg_color="FFE6E6"
    ),
    'modified': CellStyle(
        font_name="Consolas",
        font_size=9,
        font_bold=True,
        font_color="FF6600",
        bg_color="FFF2E6"
    ),
    'line_number': CellStyle(
        font_name="Consolas",
        font_size=8,
        font_color="666666",
        bg_color="F5F5F5",
        alignment_horizontal="center"
    )
}
