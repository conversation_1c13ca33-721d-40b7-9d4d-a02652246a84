"""
@File    : search_resolve_global_var.py
@Time    : 2025/7/15 19:13
<AUTHOR> qiliang.zhou
@Contact : <EMAIL>
@Desc    : 从变更文件中解析出所有定义的符合命名规则的全局变量及其定义
"""

import os
import pathlib
import re
from typing import Optional, List

def search_global_var_from_diff(global_vars_info, code_diff):
    """
    从变更代码中匹配出变更的全局变量
    Args:
        global_vars_info: 代码文件中定义的所有全局变量（{file_path: [var_dict, ...]}）
        code_diff: 变更的代码片段（[{file_path: ..., added_lines: ...}, ...]）
    Returns:
        {file_path: [变更的全局变量信息]}
    """
    changed_vars = {}
    for file_info in code_diff:
        file_path = file_info.get('file_path')
        added_lines = file_info.get('added_lines', [])
        if not file_path or file_path not in global_vars_info or not global_vars_info[file_path]:
            continue
        file_vars = global_vars_info[file_path]
        changed = {}
        # seen = set()
        for var in file_vars:
            var_name = var.get('name')
            if not var_name:
                continue
            # 如果变量名出现在新增行中，认为该变量被变更
            for line in added_lines:
                if var_name in line:
                    if var_name in changed:
                        changed[var_name]['changed_lines'].append(line)
                    else:
                        var['changed_lines'].append(line)
                        changed[var_name] = var
                    break
        if changed:
            changed_vars[file_path] = list(changed.values())
            # 也可以将结果存入 global_vars_info[file_path]['changed'] = changed
    return changed_vars


def extract_multiline_static_globals(full_code):
    """
    匹配多行 static 全局变量定义（支持多行初始化），变量名为合法C变量名。
    返回每个变量的 name, type, comment。
    """
    global_vars = []
    code_str = '\n'.join(full_code)
    multi_pattern = re.compile(
        r'static\s+'
        r'(?:(?:const|volatile|register)\s+)?'
        r'((?:struct|enum|union)\s+\w+|[a-zA-Z_][\w\s\*]*)'  # 类型
        r'\s+'
        r'([a-zA-Z_][\w]*)'  # 变量名
        r'\s*(=\s*\{.*?\};|=\s*[^;]+;|;)',  # 初始化（支持多行大括号），或无初始化
        re.DOTALL
    )
    lines = code_str.split('\n')
    line_offsets = []
    offset = 0
    for line in lines:
        line_offsets.append(offset)
        offset += len(line) + 1
    for m in multi_pattern.finditer(code_str):
        var_type = m.group(1).strip()
        var_name = m.group(2).strip()
        comment = ''
        start_idx = m.start()
        line_no = 0
        for i, off in enumerate(line_offsets):
            if off > start_idx:
                break
            line_no = i
        if line_no > 0:
            prev_line = lines[line_no - 1].strip()
            block_comment_match = re.match(r'/\*\s*(.*?)\s*\*/', prev_line)
            if block_comment_match:
                comment = block_comment_match.group(1).strip()
        if not comment:
            this_line = lines[line_no].strip()
            line_comment_match = re.search(r'//(.*)$', this_line)
            if line_comment_match:
                comment = line_comment_match.group(1).strip()
        if comment:
            comment = comment.replace('-', '')
        global_vars.append({
            'name': var_name,
            'type': var_type,
            'comment': comment,
            'const_name': [],
            'changed_lines': []
        })
    return global_vars

def extract_singleline_static_globals(full_code, existed_vars):
    """
    匹配单行 static 全局变量定义（变量名需符合 *_s* 命名规则），补充未被多行匹配覆盖的变量。
    返回每个变量的 name, type, comment, const_name。
    """
    global_vars = []
    s_var_pattern = re.compile(r'^[\w\s()]*_s[\w]+$')
    static_var_pattern = re.compile(
        r'^\s*static\s+'
        r'(?:(?:const|volatile|register)\s+)?'
        r'((?:struct|enum|union)\s+\w+|[a-zA-Z_][\w\s\*]*)'
        r'\s+'
        r'([\w\s()]*_s[\w]+)'
        r'(\s*\[[^\]]*\])?'
        r'\s*(?:=\s*[^;]+)?\s*;'
        r'(\s*//.*)?'
    )
    for idx, line in enumerate(full_code):
        if not line.strip() or line.strip().startswith('//'):
            continue
        match = static_var_pattern.match(line)
        if match:
            var_type = match.group(1).strip()
            var_name = match.group(2).strip()
            array_info = match.group(3) or ''
            comment = match.group(4).strip() if match.group(4) else ''
            if not comment and idx > 0:
                prev_line = full_code[idx - 1].strip()
                block_comment_match = re.match(r'/\*\s*(.*?)\s*\*/', prev_line)
                if block_comment_match:
                    comment = block_comment_match.group(1).strip()
            if comment:
                comment = comment.replace('-', '')
            if not s_var_pattern.match(var_name):
                continue
            full_type = (var_type + array_info).strip()
            const_name = []
            if array_info:
                const_name += re.findall(r'\[([A-Z_][A-Z0-9_]*)\]', array_info)
            init_match = re.search(r'=\s*([A-Z_][A-Z0-9_]*)', line)
            if init_match:
                const_name.append(init_match.group(1))
            # 避免重复添加
            if not any(v['name'] == var_name and v['type'] == full_type for v in existed_vars):
                global_vars.append({
                    'name': var_name,
                    'type': full_type,
                    'comment': comment,
                    'const_name': const_name,
                    'changed_lines':[]
                })
    return global_vars

def search_global_var_from_full_code(full_code):
    '''
    从变更代码中检索所有 static 全局变量，支持多行和单行，合并去重。
    '''
    multi_vars = extract_multiline_static_globals(full_code)
    single_vars = extract_singleline_static_globals(full_code, multi_vars)
    global_vars = multi_vars + single_vars
    return global_vars

def find_const_def(repo_path, global_vars_info):
    """
    查找全局变量定义时使用的常量定义 譬如 static U1 u1_s_tcsltn_string[TFTTCHAR_MESSAGE_SIZE];
    Args:
        repo_path: 仓库路径
        global_vars_info: 全局变量的信息及使用的文件路径

    Returns: [
        {
            const_name:,
            def:,
        },
        ...
    ]
    """
    # 抽取出全局变量的信息
    constants_info = extract_constant(global_vars_info)
    const_defs = {}
    for file_path, const_info in constants_info.items():
        if const_info:
            result = find_constant_definitions(str(pathlib.Path(repo_path)), str(pathlib.Path(file_path)), const_info)
            if result:
                const_defs = const_defs | result

    return const_defs

def extract_constant(global_vars_info):
    '''
    抽取全局变量定义时使用的常量
    '''
    constant_info = {}
    for file_path, global_vars in global_vars_info.items():
        const_names = []
        for global_var in global_vars:
            if global_var['const_name']:
                const_names.extend(global_var['const_name'])
        constant_info[file_path] = const_names

    return constant_info

def find_constant_definitions(repo_path: str, file_path: str, const_name: list) -> dict:
    """
    递归查找C文件及其include链中的常量定义
    :param repo_path: 项目根目录绝对路径
    :param file_path: C文件相对路径
    :param const_name: 需要查找的常量名列表
    :return: [{const_name: ..., def: [定义代码行]}]
    """
    found_defs = {}
    searched_files = set()

    def _search_file(abs_path, remaining_consts):
        if not os.path.isfile(abs_path) or abs_path in searched_files or not remaining_consts:
            return
        searched_files.add(abs_path)
        try:
            with open(abs_path, encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception:
            return

        # 1. 查找常量定义
        for const in list(remaining_consts):
            pattern = re.compile(rf'^\s*#define\s+{re.escape(const)}\b.*')
            for line in lines:
                if pattern.match(line):
                    def_info = line.rstrip('\n')
                    const_val = resolve_constant_value(line.rstrip('\n'))
                    found_defs[const] = {
                        'def': def_info,
                        'val': const_val
                    }
                    # found_defs[const] = [line.rstrip('\n')]
                    remaining_consts.remove(const)
                    break

        # 2. 如果还有没找到的常量，递归查找 include 文件
        if remaining_consts:
            include_pattern = re.compile(r'^\s*#include\s+[<"]([^">]+)[">]')
            for line in lines:
                m = include_pattern.match(line)
                if m:
                    inc_file = m.group(1)
                    # 只查找项目内的头文件
                    if inc_file.endswith('.h'):
                        inc_path = os.path.join(repo_path, inc_file)
                        if os.path.isfile(inc_path):
                            _search_file(inc_path, remaining_consts)
                        else:
                            # 递归查找 include 目录
                            for root, dirs, files in os.walk(repo_path):
                                if inc_file in files:
                                    _search_file(os.path.join(root, inc_file), remaining_consts)
                                    break

    abs_file_path = os.path.join(repo_path, file_path)
    _search_file(abs_file_path, set(const_name))

    # 组装结果
    result = {}
    for const in const_name:
        if const in found_defs:
            result[const] = found_defs[const]

    return result


def resolve_constant_value(line: str) -> Optional[str]:
    """
    从C语言宏定义中提取常量值
    Args:
        line: 单行宏定义代码（如 '#define TFTTCHAR_CFG_H_MAJOR (0)'）
    Returns:
        提取的初始值（如 '0'、'1+2'、'0x10'、'"abc"'）或 None
    """
    # 匹配 #define NAME value（value 可以有括号、表达式、字符串等）
    match = re.match(r'^\s*#define\s+\w+\s+(.+?)(\s*//.*)?$', line)
    if match:
        value = match.group(1).strip()
        # 去除外层括号（如 (0) 或 (1+2)）
        if value.startswith('(') and value.endswith(')'):
            value = value[1:-1].strip()
        return value
    return None

def replace_const_with_value(global_vars_info, const_defs):
    '''
    将全局变量定义中的常量替换为对应的值
    '''
    for file_path, global_vars in global_vars_info.items():
        for global_var in global_vars:
            if global_var['const_name']:
                for const_name in global_var['const_name']:
                    if const_name in const_defs:
                        global_var['type'] = global_var['type'].replace(const_name, const_defs[const_name]['val'])

    return global_vars_info


def find_global_variable_type(content: List[str], variable_name: str) -> Optional[str]:
    """
    在 C 代码行列表中查找指定 static 全局变量的类型，仅匹配 *_s* 命名规则。
    :param content: .c 文件内容，每行为一个字符串
    :param variable_name: 全局变量名称
    :return: 变量类型字符串（包括指针和数组类型），找不到返回 None
    """
    # 合并为单个字符串，去除注释
    code = "\n".join(content)
    code = re.sub(r'//.*', '', code)  # 单行注释
    code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)  # 多行注释
    lines = code.split('\n')

    # 变量名必须符合 *_s* 规则
    # 例如: xxx_sName, _sData, (xxx)_sValue
    s_var_pattern = re.compile(r'^[\w\s()]*_s[\w]+$')
    if not s_var_pattern.match(variable_name):
        return None

    # 只匹配 static 修饰的声明
    pattern = re.compile(
        rf'^\s*static\s+'
        r'(?:(?:const|volatile|register)\s+)?'  # 可选修饰符
        r'(?:(?:struct|enum|union)\s+)?'  # 可选复合类型
        r'([a-zA-Z_][\w\s\*]*)'  # 类型
        r'\s+'
        rf'{re.escape(variable_name)}'  # 变量名
        r'(\s*\[[^\]]*\])?'  # 可选数组
        r'\s*(?:=\s*[^;]+)?\s*;'  # 可选初始化
    )

    for line in lines:
        match = pattern.match(line)
        if match:
            var_type = match.group(1).strip()
            array_info = match.group(2) or ""
            return (var_type + array_info).strip()

    return None