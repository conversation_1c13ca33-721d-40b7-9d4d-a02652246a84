"""
Rule engine for document parsing.
"""

import os
import json
import importlib
import inspect
import logging
from typing import Any, Dict, List, Optional, Union, Type

from docparser.interfaces.rule_interface import Rule, TextRule, TableRule, PictureRule, GraphicRule, DocumentRule
from docparser.rules.dynamic_rule import DynamicRule, DynamicTextRule, DynamicTableRule, DynamicPictureRule, DynamicGraphicRule, DynamicDocumentRule

# Configure logging
logger = logging.getLogger('docparser')

class RuleEngine:
    """
    Rule engine for document parsing.
    Manages rules and applies them to document objects.
    """
    
    def __init__(self):
        """Initialize rule engine."""
        self._rules = []
    
    def register_rule(self, rule: Rule) -> None:
        """
        Register a rule.
        
        Args:
            rule: Rule instance
        """
        if not isinstance(rule, Rule):
            raise TypeError(f"Expected Rule instance, got {type(rule)}")
        
        self._rules.append(rule)
        logger.debug(f"Registered rule: {rule.get_rule_id()}")
    
    def get_all_rules(self) -> List[Rule]:
        """
        Get all registered rules.
        
        Returns:
            List of registered rules
        """
        return self._rules
    
    def get_rules_by_target(self, target: str) -> List[Rule]:
        """
        Get rules by target.
        
        Args:
            target: Rule target (e.g., 'text', 'table', 'picture', 'graphic', 'document')
            
        Returns:
            List of rules for the specified target
        """
        return [rule for rule in self._rules if rule.get_rule_target() == target and rule.is_enabled()]
    
    def get_rule_by_id(self, rule_id: str) -> Optional[Rule]:
        """
        Get rule by ID.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            Rule with the specified ID, or None if not found
        """
        for rule in self._rules:
            if rule.get_rule_id() == rule_id:
                return rule
        
        return None
    
    def enable_rule(self, rule_id: str) -> bool:
        """
        Enable rule.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            True if rule was enabled, False if rule was not found
        """
        rule = self.get_rule_by_id(rule_id)
        if rule:
            rule.enable()
            logger.debug(f"Enabled rule: {rule_id}")
            return True
        
        return False
    
    def disable_rule(self, rule_id: str) -> bool:
        """
        Disable rule.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            True if rule was disabled, False if rule was not found
        """
        rule = self.get_rule_by_id(rule_id)
        if rule:
            rule.disable()
            logger.debug(f"Disabled rule: {rule_id}")
            return True
        
        return False
    
    def load_rules_from_directory(self, directory: str) -> int:
        """
        Load rules from directory.
        
        Args:
            directory: Directory containing rule modules
            
        Returns:
            Number of rules loaded
        """
        if not os.path.exists(directory):
            logger.error(f"Directory not found: {directory}")
            return 0
        
        # Get Python files in directory
        rule_files = [f for f in os.listdir(directory) if f.endswith('.py') and f != '__init__.py']
        
        # Track number of rules loaded
        rules_loaded = 0
        
        # Import each module and look for rule classes
        for rule_file in rule_files:
            # Get module name
            module_name = os.path.splitext(rule_file)[0]
            
            try:
                # Import module
                module_path = f"docparser.rules.{module_name}"
                module = importlib.import_module(module_path)
                
                # Find rule classes in module
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, Rule) and 
                        obj != Rule and
                        obj != TextRule and
                        obj != TableRule and
                        obj != PictureRule and
                        obj != GraphicRule and
                        obj != DocumentRule and
                        obj != DynamicRule and
                        obj != DynamicTextRule and
                        obj != DynamicTableRule and
                        obj != DynamicPictureRule and
                        obj != DynamicGraphicRule and
                        obj != DynamicDocumentRule):
                        
                        # Create instance of rule class
                        rule = obj()
                        
                        # Register rule
                        self.register_rule(rule)
                        rules_loaded += 1
                        
                        logger.debug(f"Loaded rule: {rule.get_rule_id()} from {module_path}")
            
            except Exception as e:
                logger.error(f"Error loading rules from {module_path}: {e}")
        
        return rules_loaded
    
    def load_rules_from_json_file(self, file_path: str) -> int:
        """
        Load rules from JSON file.
        
        Args:
            file_path: Path to JSON file containing rules
            
        Returns:
            Number of rules loaded
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return 0
        
        # Track number of rules loaded
        rules_loaded = 0
        
        try:
            # Load JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            
            # Check if rules_data is a list
            if not isinstance(rules_data, list):
                rules_data = [rules_data]
            
            # Create rule instances from JSON data
            for rule_data in rules_data:
                try:
                    # Determine rule type based on target
                    target = rule_data.get('target', 'document')
                    
                    if target == 'text':
                        rule = DynamicTextRule.from_json(rule_data)
                    elif target == 'table':
                        rule = DynamicTableRule.from_json(rule_data)
                    elif target == 'picture':
                        rule = DynamicPictureRule.from_json(rule_data)
                    elif target == 'graphic':
                        rule = DynamicGraphicRule.from_json(rule_data)
                    else:
                        rule = DynamicDocumentRule.from_json(rule_data)
                    
                    # Register rule
                    self.register_rule(rule)
                    rules_loaded += 1
                    
                    logger.debug(f"Loaded rule: {rule.get_rule_id()} from {file_path}")
                
                except Exception as e:
                    logger.error(f"Error loading rule from JSON: {e}")
        
        except Exception as e:
            logger.error(f"Error loading rules from {file_path}: {e}")
        
        return rules_loaded
    
    def apply_rules_to_document(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply rules to document data.
        
        Args:
            document_data: Document data
            
        Returns:
            Modified document data
        """
        # Make a copy of the document data
        result = document_data.copy()
        
        # Get document rules
        document_rules = self.get_rules_by_target('document')
        
        # Apply document rules
        for rule in sorted(document_rules, key=lambda r: r.get_rule_priority(), reverse=True):
            # Check if rule condition is met
            if rule.get_rule_condition()(result):
                # Apply rule
                result = rule.apply_rule(result)
                logger.debug(f"Applied document rule: {rule.get_rule_id()}")
        
        # Apply rules to text objects
        if 'text_objects' in result:
            text_rules = self.get_rules_by_target('text')
            if text_rules:
                result['text_objects'] = self.apply_rules_to_objects(result['text_objects'], 'text')
        
        # Apply rules to table objects
        if 'table_objects' in result:
            table_rules = self.get_rules_by_target('table')
            if table_rules:
                result['table_objects'] = self.apply_rules_to_objects(result['table_objects'], 'table')
        
        # Apply rules to picture objects
        if 'picture_objects' in result:
            picture_rules = self.get_rules_by_target('picture')
            if picture_rules:
                result['picture_objects'] = self.apply_rules_to_objects(result['picture_objects'], 'picture')
        
        # Apply rules to graphic objects
        if 'graphic_objects' in result:
            graphic_rules = self.get_rules_by_target('graphic')
            if graphic_rules:
                result['graphic_objects'] = self.apply_rules_to_objects(result['graphic_objects'], 'graphic')
        
        return result
    
    def apply_rules_to_objects(self, objects: List[Dict[str, Any]], target: str) -> List[Dict[str, Any]]:
        """
        Apply rules to objects.
        
        Args:
            objects: List of objects
            target: Rule target (e.g., 'text', 'table', 'picture', 'graphic')
            
        Returns:
            Modified list of objects
        """
        # Get rules for target
        rules = self.get_rules_by_target(target)
        
        # If no rules, return objects unchanged
        if not rules:
            return objects
        
        # Make a copy of the objects
        result = objects.copy()
        
        # Apply rules to each object
        for i, obj in enumerate(result):
            # Apply each rule
            for rule in sorted(rules, key=lambda r: r.get_rule_priority(), reverse=True):
                # Check if rule condition is met
                if rule.get_rule_condition()(obj):
                    # Apply rule
                    result[i] = rule.apply_rule(obj)
                    logger.debug(f"Applied {target} rule: {rule.get_rule_id()}")
        
        return result
    
    def export_rules_to_json(self, file_path: str) -> None:
        """
        Export rules to JSON file.
        
        Args:
            file_path: Path to save the JSON output
        """
        # Convert rules to JSON
        rules_data = [rule.to_json() for rule in self._rules if hasattr(rule, 'to_json')]
        
        # Create directory if it doesn't exist
        output_dir = os.path.dirname(file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Save to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(rules_data, f, indent=2)
        
        logger.info(f"Exported {len(rules_data)} rules to {file_path}")
