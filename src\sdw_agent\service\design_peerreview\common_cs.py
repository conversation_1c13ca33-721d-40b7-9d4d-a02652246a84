import glob
import os

import pandas as pd
import xlwings as xw
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries, column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.utils import get_column_letter
from openpyxl.styles import PatternFill, Border, Side, Alignment
from pydantic import BaseModel, Field
import re
from sdw_agent.config.env import ENV
from sdw_agent.llm.llm_util import get_ai_message_with_structured_output
import difflib
from pathlib import Path
from sdw_agent.service.design_peerreview.models import CheckRangeIninfo
import win32com.client as win32
from win32com.client import constants as constest



def check_file_path(path, expeted_file_names):
    """
    检查设计评审结果路径，验证文件数量并生成文件路径
    Args:
        path: 输入路径
    Returns:
        dict: 包含检查结果的字典
    """

    try:
        # 检查路径是否存在
        if not os.path.exists(path):
            return {
                'success': False,
                'message': f"路径不存在: {path}",
                'error': True,
                'path': path
            }

        # 定义期望的文件关键字
        # expeted_file_names = ["CAN入出力", "IF整合性", "RAM設計書", "設計基準CS", "関数仕様書"]

        # 递归遍历所有文件夹，查找匹配的文件
        matched_files = []
        file_extensions = ['*.xlsx', '*.xls', '*.xlsm']

        for root, dirs, files in os.walk(path):
            for ext in file_extensions:
                for file in files:
                    if file.endswith(ext[1:]):  # 去掉通配符
                        file_path = os.path.join(root, file)
                        # 检查文件名是否包含期望的关键字
                        for keyword in expeted_file_names:
                            if keyword in file:
                                matched_files.append(file_path)
                                logger.info(f"找到匹配文件: {file_path}")
                                break  # 找到一个关键字就跳出内层循环

        # 去重并排序
        matched_files = list(set(matched_files))
        matched_files.sort()

        # 检查文件数量
        file_count = len(matched_files)
        if file_count != len(expeted_file_names):
            return {
                'success': False,
                'message': f"文件数量不正确，期望5个文件，实际找到{file_count}个文件",
                'error': True,
                'path': path,
                'file_count': file_count,
                'files': matched_files,
                'expected_keywords': expeted_file_names
            }

        logger.success(f"找到{file_count}个文件，路径验证通过")

        return {
            'success': True,
            'message': f"路径验证通过，找到{file_count}个文件",
            'error': False,
            'path': path,
            'file_count': file_count,
            'files': matched_files,
            'expected_keywords': expeted_file_names
        }

    except Exception as e:
        error_msg = f"检查设计评审结果路径时出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg,
            'error': True,
            'path': path
        }

def get_excel_columns_data(file_path,sheet_name,skip_rows,data_by_row_idx = None ,data_by_col_idx = None):
    try:
        # 读取整个工作表，跳过前 1 行（假设起始单元格是 B2）
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, skiprows=skip_rows)
        # 获取行数和列数
        rows, cols = df.shape
        data = []
        # 提取目标列（如 B 列，索引为 1） df.iloc[start_row:end_row, start_col:end_col]
        if data_by_row_idx is None: #获取某列数据
            data = df.iloc[:,data_by_col_idx].tolist()
        elif data_by_col_idx is None:#获取某列数据
            data = df.iloc[data_by_row_idx,:].tolist()

        return list(data)
    except Exception as e:
        error_msg = f"{file_path} 的文件 获取函数名sheet出现错误: {e}"
        logger.error(error_msg)
        return list()

def get_excel_sheet_names_data(file_path):
    try:
        # 读取整个工作表，跳过前 1 行（假设起始单元格是 B2）
        df = pd.ExcelFile(file_path)
        # 提取目标列（如 B 列，索引为 1）
        # 获取所有工作表名称
        sheet_names = df.sheet_names
        df.close()
        return list(sheet_names)
    except Exception as e:
        error_msg = f"{file_path} 的文件获取文件sheet名称过程中出现错误: {e}"
        logger.error(error_msg)


def get_vprocess_by_filename(dirPath,filepath,check_type:int):
    except_file_info = {
        #'設計レビュー開催'
        0x01: {
                'ベースソフト': 'ベースソフトの選定',
                 'oss': '',#'使用予定OSSピックアップ 【OSS管理プロセス】 ',
                 'DRBFM': '',#'DRBFMの実施',
                 'FTA': '',#'人命と安全に関わるFTAの実施',
                 '機能一覧と新規・変更内容': '機能一覧と新規・変更内容 （DR・QA_MD）作成',
                 '構造図': '',#'ソフトウェア構造図作成（DR・QA_MD構造、システム他）',
                 '他車種': '',#'他車種、標準ソフト、マイコン問題点一覧',
                 '設計・評価方針': '機能別設計・評価方針＆実施結果(DR･QA_MD)',
                 '設計基準CS': 'ソフトウェア 設計基準CS(基本設計)',
                 '通信フェイルセーフＣＳ': '通信フェイルセーフCS',
                 'IF一覧': 'ソフトウェア設計書作成（I/F含む）',
                 '要件⇔ソフトウェア設計書照合': '要件⇔ソフトウェア設計書照合実施'
        },
        #'設計ピアレビュー開催'
        0x02: {
                '_IF': 'I/F整合性確認',
                 'Transmit': 'CAN入出力一覧確認',
                 '関数一覧': '関数仕様書作成',
                 'データ構造': 'RAM設計書作成',
                 'ソフトウェア設計書⇔関数仕様書照合': 'ソフトウェア設計書 ⇔ 関数仕様書照合実施',
                 '設計基準CS': 'ソフトウェア 設計基準CS（詳細設計）',
                 '報告用': '最近の不具合横にらみ（設計）'
        },
        #'コードレビュー開催'
        0x04: {
                '機能一覧と新規・変更内容':'機能一覧と新規・変更内容 （DR・QA_MD）更新',
               'コーディング': '',#'コーディング基準確認＆結果検証',
               'warn': 'QAC解析実施＆結果検証',
               '警告一覧': 'CodeSonar解析実施&検証結果',
               'FileList':'ファイル比較実施（比較結果と変更内容の確認）',
               'コードレビュー観点CS': 'セルフチェック＆コードレビュー観点CS',
               '開発環境': '',#'開発環境設定手順書確認（Softune等）',
               'ICE': '',#'変更点ICEテスト実施＆結果検証',
               'コンパイラ': '',#'コンパイラ最適化検証(割り込み禁止区間ASM検証)',
               '干渉CS': 'RAM干渉チェック実施＆結果検証',
               r'設計文書\u3000⇔\u3000コード\u3000照合実施＆結果検証': '設計文書　⇔　コード　照合実施＆結果検証'
        }
    }
    check_result = ''
    check_range = check_type & 0x07
    rangelist = {}
    for check_range in [0x01,0x02,0x04] :
        if check_range & check_type:
            rangelist = mergeDict(rangelist, except_file_info[check_range])

            #优先确认文件夹名称在v字名字中
    check_filePath_valid = filepath[filepath.find(dirPath)+len(dirPath)+1:]
    if '~' in check_filePath_valid:
        return check_result
    sheet_names  = ""
    try:
        # 启动 Excel 应用（后台运行）
        app = xw.App(visible=False, add_book=False)
        # 打开 Excel 文件
        wb = app.books.open(filepath)
        sheet_names = str(wb.sheet_names)
        wb.close()
        app.quit()

        for file_filter, v_process_name in rangelist.items():
            if file_filter in sheet_names  and "warn" not in sheet_names and "ドメイン毎の警告一覧" in sheet_names:
                check_result = v_process_name
                break
            elif file_filter in sheet_names:
                check_result = v_process_name
                break
        return check_result
    except Exception as e:
        raise RuntimeError(f"检查过程中发生错误: {e}")


def getfilesheetname(file_path,sheet_name):
    try:
        name = ""
        # 加载工作簿
        wb =  load_workbook(filename=file_path)
        for temp_name in wb.sheetnames:
            if sheet_name in temp_name:
                name = temp_name
                break
        wb.close()
        return name
    except Exception as e:
        error_msg = f"{file_path} 的文件 获取函数名sheet出现错误: {e}"
        logger.error(error_msg)
        return ""

def check_v_process_name(dirpath:str,check_type:int):
    # 遍历文件夹获取所有文件
    file_info = {}
    dirpath = dirpath.replace("\\", "/")
    if os.path.isdir(dirpath):
        def generate_files(directory):
            path = Path(directory)
            for file in path.rglob('*.*'):
                if file.is_file():
                    yield str(file)

        for file in generate_files(dirpath):
            file = file.replace("\\", "/")
            # 确认文件所属V字
            ret = get_vprocess_by_filename(os.path.dirname(file), file, check_type)
            file_basename = os.path.basename(file)
            file_info[file] = {}
            file_info[file]["文件路径"] = os.path.dirname(file)
            file_info[file]["文件名"] = file_basename
            file_info[file]["所属V字项目"] = ret
            if ret == "":
                file_info[file]["备注"] = '找不到所属V字，需要人工check'
    else:
        file_basename = os.path.basename(dirpath)
        # 确认文件所属V字
        ret = get_vprocess_by_filename(os.path.dirname(dirpath), dirpath, check_type)
        file_info[dirpath] = {}
        file_info[dirpath]["文件路径"] = os.path.dirname(dirpath)
        file_info[dirpath]["文件名"] = file_basename
        file_info[dirpath]["所属V字项目"] = ret
        if ret == "":
            file_info[dirpath]["备注"] = '找不到所属V字，需要人工check'
    return file_info

def check_images(file, sheet_name=None):
    """
    检查指定sheet是否包含图片

    Args:
        file: Excel文件路径
        sheet_name: sheet名称

    Returns:
        dict: 包含检查结果的字典
    """
    try:
        # 启动Excel应用（后台运行）
        app = xw.App(visible=False, add_book=False)
        wb = app.books.open(file)

        shapes = wb.sheets[sheet_name].api.Shapes
        has_images = shapes.Count > 0
        wb.close()
        app.quit()

        if has_images:
            return {'success': True}
        else:
            return {
                'success': False,
                'message': f"Sheet '{sheet_name}' 不包含图片"
            }

    except Exception as e:
        error_msg = f"检查Sheet '{sheet_name}' 图片时出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }



def check_empty_sheet(file, sheet_names=None, check_sheet=None, check_range=None, row_full_not_empty=False,
                      col_full_not_empty=False):
    """
    检查指定sheet_names列表中的sheet是否为空。
    如果指定了check_sheet和check_range，则只检查该sheet的指定区域是否为空。
    row_full_not_empty: 区域内有一行全不为空就返回True。
    col_full_not_empty: 区域内有一列全不为空就返回True。
    返回：
      - 如果是批量检查，返回包含错误信息的字典
      - 如果是区域检查，返回包含检查结果的字典
    """

    try:
        # 区域检查模式
        if check_sheet and check_range:
            # 用openpyxl读取区域
            wb = load_workbook(file._io)
            ws = wb[check_sheet]
            min_col, min_row, max_col, max_row = range_boundaries(check_range)
            region = list(
                ws.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col, values_only=True))
            empty_cells = []
            for r_idx, row in enumerate(region, start=min_row):
                for c_idx, cell in enumerate(row, start=min_col):
                    if cell is None or str(cell).strip() == "":
                        empty_cells.append(ws.cell(row=r_idx, column=c_idx).coordinate)
            msg = []
            # 行全不为空判断
            if row_full_not_empty:
                for row in region:
                    if all(cell is not None and str(cell).strip() != "" for cell in row):
                        logger.success(f"{check_sheet}的区域{check_range}存在一行所有单元格全不为空")
                        # return {'success': True, 'error': False}
                    elif all(cell is None or str(cell).strip() == "" for cell in row):
                        # 行全部为空，这是允许的情况
                        continue
                    else:
                        logger.error(f"{check_sheet}的区域{check_range}存在一行有空单元格")
                        msg.append(f"Sheet '{check_sheet}' 区域 {check_range} 存在一行有空单元格")
                if msg:
                    return {
                        'success': False,
                        'message': msg
                    }
                else:
                    return {'success': True}

            # 列全不为空判断
            if col_full_not_empty:
                for col_idx in range(len(region[0])):
                    if all(row[col_idx] is not None and str(row[col_idx]).strip() != "" for row in region):
                        logger.success(f"{check_sheet}的区域{check_range}存在所有单元格全不为空的一列")
                        return {'success': True}
                    else:
                        logger.error(f"{check_sheet}的区域{check_range}存在一列有空单元格")
                        msg.append(f"Sheet '{check_sheet}' 区域 {check_range} 存在一列有空单元格")
                return {
                    'success': False,
                    'message': msg
                }
            # 全不为空判断
            if not empty_cells:
                logger.success(f"{check_sheet}的区域{check_range}全部不为空")
                return {'success': True}
            else:
                logger.error(f"{check_sheet}的区域{check_range}存在空单元格")
                msg.append(f"Sheet '{check_sheet}' 区域 {check_range} 存在空单元格")

            if msg:
                return {
                    'success': False,
                    'message': msg
                }
            else:
                return {'success': True}

        # 批量检查模式
        dfs = {sheet: file.parse(sheet) for sheet in sheet_names}
        empty_sheets = []
        for sheet_name, df in dfs.items():
            if df.empty:
                # 如果文字为空，则检查是否有图片
                has_images = check_images(file, sheet_name)
                if has_images['success']:
                    logger.info("has images")
                    # 有图片不算空，不添加到空sheet列表
                else:
                    logger.warning(f"empty sheet: {sheet_name}")
                    empty_sheets.append(sheet_name)
            else:
                logger.info(f"not empty: {sheet_name}")
                # 有内容不算空，不添加到空sheet列表

        if empty_sheets:
            return {
                'success': False,
                'message': f"发现空表: {empty_sheets}"
            }
        else:
            return {'success': True}

    except Exception as e:
        error_msg = f"检查空表时出现错误: {e}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg
        }


def check_value_between_positions(df, search_name1, search_name2, exact_match=True):
    # 首先找到search_name1的位置
    if exact_match:
        mask = df.map(lambda x: str(x) == search_name1)
    else:
        mask = df.map(lambda x: search_name1 in str(x))
    positions1 = []
    for col in df.columns:
        rows_found = df[mask[col]].index.tolist()
        if rows_found:
            positions1.extend([(row, col) for row in rows_found])

    if not positions1:
        logger.error(f"未找到 '{search_name1}'")
        return

    # 然后找到search_name2的位置
    if exact_match:
        mask = df.map(lambda x: str(x) == search_name2)
    else:
        mask = df.map(lambda x: search_name2 in str(x))
    positions2 = []
    for col in df.columns:
        rows_found = df[mask[col]].index.tolist()
        if rows_found:
            positions2.extend([(row, col) for row in rows_found])

    if not positions2:
        logger.error(f"未找到 '{search_name2}'")
        return

    # 检查search_name1和search_name2之间的行是否有数据
    logger.info(f"检查 '{search_name1}' 和 '{search_name2}' 之间的行数据:")
    msg = []
    data_ok = False
    for pos1 in positions1:
        for pos2 in positions2:
            row1, col1 = pos1
            row2, col2 = pos2

            # 确定起始行和结束行
            start_row = min(row1, row2)
            end_row = max(row1, row2)

            logger.info(f"检查行 {start_row} 到 {end_row} 之间的数据:")

            # 检查中间的行
            for row_idx in range(start_row + 1, end_row):
                if row_idx < len(df):
                    row_data = df.iloc[row_idx]
                    has_data = any(pd.notna(val) and str(val).strip() != "" for val in row_data)
                    if has_data:
                        data_values = [val for val in row_data if pd.notna(val) and str(val).strip() != ""]
                        logger.success(f"行 {row_idx} 有数据: {data_values}")
                        data_ok = True
                    else:
                        logger.warning(f"行 {row_idx} 无数据")
                        msg.append(f"行 {row_idx} 无数据")
                else:
                    logger.warning(f"行 {row_idx} 超出DataFrame范围")
                    msg.append(f"行 {row_idx} 超出DataFrame范围")
    if data_ok == False:
        return {'success': False, 'message': msg}
    else:
        return {'success': True, 'message': "所有检查通过"}


def search_around_value(df, anchor_param, search_params, cols=(6, 8), rows=(2, 4), direction='top-left',
                        exact_match=False):
    """
    定位到参数A的位置，然后在给定范围内查找其他值，并判断其他值右侧是否有值

    参数:
    df: DataFrame对象
    anchor_param: 要定位的锚点参数A
    search_params: 要搜索的参数列表
    cols: 要搜索的列数范围，元组(min, max)
    rows: 要搜索的行数范围，元组(min, max)
    direction: 搜索方向，支持以下值：
        - 'top-left': 左上方向
        - 'top-right': 右上方向
        - 'bottom-left': 左下方向
        - 'bottom-right': 右下方向
        - 'top': 上方
        - 'bottom': 下方
        - 'left': 左方
        - 'right': 右方
    exact_match: 是否精确匹配
    """
    # 首先找到锚点参数A的位置
    if exact_match:
        mask = df.map(lambda x: str(x) == anchor_param)
    else:
        mask = df.map(lambda x: anchor_param in str(x))
    anchor_positions = []
    for col in df.columns:
        rows_found = df[mask[col]].index.tolist()
        if rows_found:
            anchor_positions.extend([(row, col) for row in rows_found])

    if not anchor_positions:
        logger.error(f"未找到锚点参数 '{anchor_param}'")
        return {'success': False, 'message': f"未找到锚点参数 '{anchor_param}'"}

    logger.info(f"找到锚点参数 '{anchor_param}' 的位置: {anchor_positions}")

    # 对每个锚点位置进行搜索
    all_results = []
    msg = []
    for anchor_row, anchor_col in anchor_positions:
        # 获取列索引
        col_idx = df.columns.get_loc(anchor_col)

        # 根据方向计算搜索范围
        if direction == 'top-left':
            start_col = max(0, col_idx - cols[1])
            end_col = max(0, col_idx - cols[0])
            start_row = max(0, anchor_row - rows[1])
            end_row = max(0, anchor_row - rows[0])
        elif direction == 'top-right':
            start_col = min(len(df.columns), col_idx + cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = max(0, anchor_row - rows[1])
            end_row = max(0, anchor_row - rows[0])
        elif direction == 'bottom-left':
            start_col = max(0, col_idx - cols[1])
            end_col = max(0, col_idx - cols[0])
            start_row = min(len(df), anchor_row + rows[0])
            end_row = min(len(df), anchor_row + rows[1])
        elif direction == 'bottom-right':
            start_col = min(len(df.columns), col_idx + cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = min(len(df), anchor_row + rows[0])
            end_row = min(len(df), anchor_row + rows[1])
        elif direction == 'top':
            start_col = max(0, col_idx - cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = max(0, anchor_row - rows[1])
            end_row = max(0, anchor_row - rows[0])
        elif direction == 'bottom':
            start_col = max(0, col_idx - cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = min(len(df), anchor_row + rows[0])
            end_row = min(len(df), anchor_row + rows[1])
        elif direction == 'left':
            start_col = max(0, col_idx - cols[1])
            end_col = max(0, col_idx - cols[0])
            start_row = max(0, anchor_row - rows[0])
            end_row = min(len(df), anchor_row + rows[1])
        elif direction == 'right':
            start_col = min(len(df.columns), col_idx + cols[0])
            end_col = min(len(df.columns), col_idx + cols[1])
            start_row = max(0, anchor_row - rows[0])
            end_row = min(len(df), anchor_row + rows[1])
        else:
            logger.error(f"无效的方向: {direction}")
            return {'success': False, 'message': f"无效的方向: {direction}"}

        logger.info(
            f"在锚点位置 ({anchor_row}, {anchor_col}) 的搜索范围: 行{start_row}-{end_row}, 列{start_col}-{end_col}")

        # 在指定范围内搜索所有参数
        search_area = df.iloc[start_row:end_row + 1, start_col:end_col + 1]

        for search_param in search_params:
            if exact_match:
                mask = search_area.map(lambda x: str(x) == search_param)
            else:
                mask = search_area.map(lambda x: search_param in str(x))

            if mask.any().any():
                found_positions = []
                for c in search_area.columns:
                    rows_found = search_area[mask[c]].index.tolist()
                    if rows_found:
                        found_positions.extend([(r, c) for r in rows_found])

                logger.success(f"在锚点 '{anchor_param}' 的{direction}方向找到参数 '{search_param}' 的位置:")
                for r, c in found_positions:
                    logger.info(f"  位置: 行 {r}, 列 {c}")

                    # 检查search_param右侧所有列是否有值
                    col_idx = df.columns.get_loc(c)
                    right_columns = df.iloc[r, col_idx + 1:]  # 获取右侧所有列
                    has_right_columns_value = any(pd.notna(val) and str(val).strip() != "" for val in right_columns)
                    if has_right_columns_value:
                        right_values = [val for val in right_columns if pd.notna(val) and str(val).strip() != ""]
                        logger.success(f"    右侧列有值: {right_values}")
                        all_results.append({
                            'param': search_param,
                            'position': (r, c),
                            'right_has_value': True,
                            'right_values': right_values
                        })
                    else:
                        logger.warning(f"    右侧列无值")
                        msg.append(f"参数 '{search_param}' 在位置 ({r}, {c}) 右侧列无值")
                        all_results.append({
                            'param': search_param,
                            'position': (r, c),
                            'right_has_value': False,
                            'right_values': []
                        })
            else:
                logger.warning(f"在锚点 '{anchor_param}' 的{direction}方向未找到参数 '{search_param}'")
                msg.append(f"在锚点 '{anchor_param}' 的{direction}方向未找到参数 '{search_param}'")

    # 返回结果
    if msg:
        return {
            'success': False,
            'message': msg
        }
    else:
        return {
            'success': True,
            'message': f"在锚点 '{anchor_param}' 周围找到的参数都有值"
        }

def index_to_excel_column(index):
    """
    将数字索引转换为Excel列名
    Args:
        index: 列索引（从0开始）
    Returns:
        str: Excel列名
    """
    result = ""
    while index >= 0:
        result = chr(65 + (index % 26)) + result
        index = index // 26 - 1
    return result

def check_columns_not_empty_or_dash(row, col_indices, col_names, excel_row_num, allow_dash=False):
    """
    检查指定列是否不为空或不为'-'

    Args:
        row: DataFrame的一行数据
        col_indices: 要检查的列索引列表
        col_names: 对应的列名列表
        excel_row_num: Excel行号
        allow_dash: 是否允许'-'值，True表示允许'-'，False表示不允许

    Returns:
        list: 空单元格的列表，如果没有空单元格则返回空列表
    """
    empty_cells = []

    for col_idx, col_name in zip(col_indices, col_names):
        value = str(row.iloc[col_idx]).strip() if pd.notna(row.iloc[col_idx]) else ""

        if allow_dash:
            # 允许'-'值，只检查是否为空
            if value == "":
                empty_cells.append(f"{col_name}{excel_row_num}")
        else:
            # 不允许'-'值，检查是否为空或为'-'
            if value == "" or value == "-":
                empty_cells.append(f"{col_name}{excel_row_num}")

    return empty_cells

def update_result(result_info,key,file_name=None,v_process_name=None,sheet_name=None,ng_info=None,blank_info=None,stamp_info=None,comment_info=None):
    if file_name is not None:
        result_info[key]["文件名"] = file_name
    if v_process_name is not None:
        result_info[key]["所属V字项目"] = v_process_name
    if sheet_name is not None:
        result_info[key]["Sheet名称"] = sheet_name
    if ng_info is not None:
        result_info[key]["确认是否有NG项"] = ng_info
    if blank_info is not None:
        result_info[key]["确认是否有空白项"] = blank_info
    if stamp_info is not None:
        result_info[key]["确认签字盖章是否OK"] = stamp_info
    if comment_info is not None:
        result_info[key]["备注"] = comment_info
    return result_info


def mergeDict(dict_dest,dict_src):
    """
    递归合并两个字典，嵌套字典会被深度合并。

    参数:
        dict_dest (dict): 第一个字典
        dict_src (dict): 第二个字典

    返回:
        merged(dict): 合并后的字典
    """
    merged = dict_dest.copy()  # 复制原始字典，避免修改原数据
    for key, value in dict_src.items():
        if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
            # 如果键存在且值是字典，递归合并
            #merged[key] = mergeDict(merged[key], value)
            for sub_key, sub_value in value.items():
                if sub_key in merged[key]:
                    if isinstance(sub_value, bool):
                        merged[key][sub_key] = merged[key][sub_key]&sub_value
                    elif isinstance(sub_value, str):
                        merged[key][sub_key] = merged[key][sub_key] + sub_value
                else:
                    merged[key][sub_key] = sub_value
        else:
            # 否则直接覆盖或添加
            merged[key] = value
    return merged
def check_pictures(file_path, check_sheet_name, cell_address = None,pictures_count:int=3):
    """
        检查印章或签名,使用 xlwings 检查指定单元格是否有图片。检测图片在单元格范围是否有数量一致

        :param file_path: Excel 文件路径
        :param sheet_name: 要检查的 Sheet 名称
        :param cell_address: 单元格地址（如 "A1:A2"），如果没有给默认检查整个表格范围
        :return: ["stamp":{"XXX单元格":"范围单元格处在没有印章和签名"}]
        """
    # 存储检测结果
    check_results = []
    results = {}
    try:
        # 启动 Excel 应用（后台运行）
        app = xw.App(visible=False, add_book=False)
        # 打开 Excel 文件
        wb = app.books.open(file_path)
        sheet_name = ''
        has_image_count = 0
        for sheet_name in wb.sheet_names:
            if check_sheet_name in sheet_name:
                logger.info(f"正在检查{sheet_name}这个sheet")
                sheet = wb.sheets[sheet_name]
                check_result = []
                # 检查图片
                for shape in sheet.api.Shapes:
                    # 检测是图片
                    if shape.Type != 13:
                        continue
                    if cell_address is None:
                        has_image_count = has_image_count+1
                    else:
                        used_range = sheet.range(cell_address)
                        # 获取图片的位置和大小
                        left = shape.Left
                        top = shape.Top
                        width = shape.Width
                        height = shape.Height

                        # 获取合并区域的边界
                        merged_left = used_range.left
                        merged_top = used_range.top
                        merged_width = used_range.width
                        merged_height = used_range.height

                        # 判断图片在合并单元格50%以上
                        # 计算重叠区域的坐标
                        overlap_left = max(left, merged_left)
                        overlap_top = max(top, merged_top)
                        overlap_right = min(left + width, merged_left + merged_width)
                        overlap_bottom = min(top + height, merged_top + merged_height)

                        # 计算重叠面积
                        eara_size = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)
                        # 如果没有重叠
                        if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
                            eara_size = 0
                        # 计算合并单元格的面积
                        merged_area_size = merged_width * merged_height
                        # 计算重叠面积
                        if (eara_size / merged_area_size) >= 0.5:
                            has_image_count = has_image_count+1
                logger.info(f"{sheet_name}检查完了，发现{has_image_count}/{pictures_count}图片")
                break

        # # 提取合并区域的文本
        # merged_text = str(start_cell.value) if start_cell.value else ""
        #
        # # 保存合并区域中的图片（需遍历 Shapes）
        # for shape in merged_area.Shapes:
        #     if shape.Type == 13:  # 13 表示图片类型
        #         image = shape.PictureFormat
        #         image_data = image.Export()
        #         img = Image.open(io.BytesIO(image_data))
        #         img.save(f"image_{shape.Name}.png")

        # 关闭工作簿并退出 Excel
        wb.close()
        app.quit()

        return has_image_count
    except Exception as e:
        raise RuntimeError(f"检查过程中发生错误: {e}")


def check_stamp(file_path, check_sheet_name, cell_address = None,ignore_col:str = None):
    """
    检查印章或签名,使用 xlwings 检查指定单元格是否有图片。

    :param file_path: Excel 文件路径
    :param sheet_name: 要检查的 Sheet 名称
    :param cell_address: 单元格地址（如 "A1:A2"），如果没有给默认检查整个表格范围
    :return: ["stamp":{"XXX单元格":"范围单元格处在没有印章和签名"}]
    """

    # 存储检测结果
    check_results = []
    results = {}
    text_to_check= ["承認","作成","審査","確認","検討","実施","検証","判定"]
    # 启动 Excel 应用（后台运行）
    app = xw.App(visible=False, add_book=False)
    try:
        # 打开 Excel 文件
        wb = app.books.open(file_path)
        sheet_name = ''
        for sheet_name in wb.sheet_names:
            if check_sheet_name in sheet_name and (f"Format変更履歴" not in sheet_name):
                logger.info(f"正在检查{sheet_name}这个sheet")
                sheet = wb.sheets[sheet_name]
                if cell_address is None:
                    # 获取所有已使用的单元格范围
                    used_range = sheet.used_range
                    all_cells = used_range.value
                else:
                    used_range = sheet.range(cell_address)
                    all_cells = used_range.value
                # 遍历所有单元格
                check_flag = False
                check_start_col_idx = 0
                check_end_col_idx = 0xFFFFFFFF
                check_start_row_idx = 0
                for row_idx, row in enumerate(used_range.rows):
                    line_value = row.value
                    line_text = ''.join(str(line_value))
                    line_image = False
                    #清洗整行数据数据确认是否都是空白
                    line_text = line_text.replace("\t","")
                    line_text = line_text.replace("\n","")
                    line_text = line_text.replace(" ","")
                    line_text = line_text.replace("None", "")
                    line_text = line_text.replace(",", "")

                    check_result = []
                    if (check_flag and (row_idx <= check_start_row_idx)) or (f"例" in line_text) :
                        continue
                    for col_idx, cell in enumerate(row):
                        if (check_flag and (col_idx < check_start_col_idx )) or ((ignore_col is not None) and (ignore_col == get_column_letter(cell.column))):
                            continue
                        if check_flag and col_idx >= check_end_col_idx:
                            break
                        # 定位到当前单元格
                        current_cell = sheet.range(cell.address)
                        # 检查是否是合并单元格
                        # 获取合并区域的范围
                        merged_area = current_cell.api.MergeArea
                        # 获取合并区域的起始单元格
                        start_cell = sheet.range(merged_area.Address.split(':')[0])
                        # 检查文字内容
                        has_text =  (start_cell.value is not None)

                        temp_cell_values = [one_word for one_word in str(start_cell.value).strip() if ord(one_word) > 127]
                        temp_cell_value = ""
                        if len(temp_cell_values) <= 3:
                             temp_cell_value = "".join(temp_cell_values)
                        if any(check_str in temp_cell_value for check_str in text_to_check) and check_flag == False:
                            #检测到当前行是承認","作成","審査","確認","検討相关标题行
                            # 获取第一个不为 ''/None 的索引
                            check_start_col_idx = next((i_text for i_text, v_text in enumerate(line_value) if
                                                    v_text != '' and v_text is not None), -1)
                            if r":" in merged_area.Address:
                                #获取合并单元格
                                end_cell = sheet.range(merged_area.Address.split(':')[1])
                                check_start_row_idx = row_idx + end_cell.row-start_cell.row
                            else:
                                check_start_row_idx = row_idx

                            check_flag = True
                            for find_end_idx in range(col_idx+1,len(line_value)+1):
                                next_cell  = current_cell.offset(0,find_end_idx-col_idx)
                                next_merged_area = next_cell.api.MergeArea
                                # 获取合并区域的起始单元格
                                if r":" in merged_area.Address :
                                    next_start_cell = sheet.range(next_merged_area.Address.split(':')[0])
                                else:
                                    next_start_cell  = next_cell
                                if next_start_cell.value is None:
                                    check_end_col_idx = find_end_idx
                                    break
                            #进下一行数据
                            break
                        # 从承認","作成","審査","確認","検討相关标题行下一行开始检测印章 或者文字
                        has_image = False
                        # 检查图片
                        if start_cell.value is None and check_flag:
                            for shape in sheet.api.Shapes:
                                #检测是图片
                                if shape.Type != 13:
                                    continue
                                # 获取图片的位置和大小
                                left = shape.Left
                                top = shape.Top
                                width = shape.Width
                                height = shape.Height

                                # 获取合并区域的边界
                                merged_left = merged_area.Left
                                merged_top = merged_area.Top
                                merged_width = merged_area.Width
                                merged_height = merged_area.Height

                                # 判断图片在合并单元格50%以上
                                # 计算重叠区域的坐标
                                overlap_left = max(left, merged_left)
                                overlap_top = max(top, merged_top)
                                overlap_right = min(left + width, merged_left + merged_width)
                                overlap_bottom = min(top + height, merged_top + merged_height)

                                # 计算重叠面积
                                eara_size =  (overlap_right - overlap_left) * (overlap_bottom - overlap_top)
                                # 如果没有重叠
                                if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
                                    eara_size =  0
                                # 计算合并单元格的面积
                                merged_area_size = merged_width * merged_height
                                # 计算重叠面积
                                if (eara_size / merged_area_size) >= 0.5:
                                    has_image = True
                                    line_image = True
                                    break
                            # 记录结果
                            if (has_text == False) and (has_image == False) and (merged_area.Address not in check_result):
                                check_result.append(f"{merged_area.Address}")

                    #检测到开始检测后整行空白或者整行没有图片跳出检测
                    if check_flag and (not line_image) and (line_text == '[]')  and cell_address is None and row_idx>check_start_row_idx+1:
                        break
                    check_results.extend(check_result)
                if len(check_results)>0:
                    logger.info(f"{sheet_name}的sheet发现{str(list(set(check_results)))}没有盖章单元格")
                    # check_results["message"] = list(set(check_result))
                    # check_results["确认签字盖章是否OK"] = True if (len(check_results)==0) else False
                    # results[sheet_name] = check_results
                    results = {sheet_name:{
                        "stamp_message":f"{sheet_name}的sheet发现{str(list(set(check_results)))}没有盖章",
                        f"确认签字盖章是否OK":bool(True if (len(check_results)==0) else False)
                        }
                    }
                else:
                    logger.info(f"{sheet_name}检查OK，没有问题")
                    # check_results["message"] = list(set(check_result))
                    # check_results["确认签字盖章是否OK"] = True if (len(check_results)==0) else False
                    # results[sheet_name] = check_results
                    results = {sheet_name: {
                        "stamp_message": f"{sheet_name}检查OK，没有问题",
                        f"确认签字盖章是否OK": bool(True if (len(check_results) == 0) else False)
                    }
                    }
        # # 提取合并区域的文本
        # merged_text = str(start_cell.value) if start_cell.value else ""
        #
        # # 保存合并区域中的图片（需遍历 Shapes）
        # for shape in merged_area.Shapes:
        #     if shape.Type == 13:  # 13 表示图片类型
        #         image = shape.PictureFormat
        #         image_data = image.Export()
        #         img = Image.open(io.BytesIO(image_data))
        #         img.save(f"image_{shape.Name}.png")

        # 关闭工作簿并退出 Excel
        wb.close()
        app.quit()

        return results
    except Exception as e:
        raise RuntimeError(f"检查过程中发生错误: {e}")

def check_blank_ng_na(file_path, check_sheet_name, check_info:CheckRangeIninfo,full_word_matching:bool = False,check_ng_flag:bool=True,check_direction:bool=True):
    """
    检查表格是否有空白项或者NG项。

    :param file_path: Excel 文件路径
    :param sheet_name: 要检查的 Sheet 名称
    :param  CheckRangeIninfo
    :        cell_address: 单元格地址（如 "A1:A2"），如果没有给默认检查整个表格范围
    :        not_included: 检测范围中需要排除检测范围的列（['A','J','AA']）
    :        check_direction_enable  :是否根据range进行检测
    :        directions: 检索方向，如果给定范围只有一行或者一列，根据方向检索，直到整行或者整列空白 退出检索
    :return: ["blank_ng":{"XXX单元格":"范围单元格空白或者标记NG项"}]
    """
    """
        检查指定 Excel 文件的指定工作表和单元格范围：
        1. 是否包含合并单元格
        2. 是否有空白项（空值或空字符串）
        3. 是否有 NG 项（值为 "NG" 的单元格）

        参数:
            file_path (str): Excel 文件路径
            sheet_name (str): 工作表名称
            cell_range (str): 单元格范围（例如 "A1:C5"）

        返回:
            dict: 包含合并单元格、空白项和 NG 项的列表
        """
    try:
        # 解析单元格范围（例如 "A1:C5"）
        start_cell, end_cell = check_info.range_address.split(":")
        start_col, start_row = coordinate_from_string(start_cell)
        end_col, end_row = coordinate_from_string(end_cell)

        #检测入参有效性
        if  start_row != end_row and start_col != end_col and check_direction:
            check_info.check_direction_enable = False

        # 加载工作簿和工作表
        wb = load_workbook(filename=file_path)
        ws = None

        # 初始化结果
        merged_cells = []
        empty_cells = []
        ng_cells = []
        sheet_name = ''
        for sheet_name in wb.sheetnames:
            if full_word_matching and check_sheet_name in wb.sheetnames:
                sheet_name = check_sheet_name
                ws = wb[sheet_name]
                break
            elif not full_word_matching and check_sheet_name in sheet_name and (f"Format変更履歴" not in sheet_name) and  (f"記入例" not in sheet_name ):
                ws = wb[sheet_name]
                break
            else:
                if full_word_matching:
                    break
        if ws is not None:
            # 检查合并单元格
            get_merge_cells = {}
            for merged_range in ws.merged_cells.ranges:
                # 解析合并范围（如 "A1:C3"）
                cur_start_cell, cur_end_cell = merged_range.coord.split(":")
                cur_start_col, cur_start_row = coordinate_from_string(cur_start_cell)
                cur_end_col, cur_end_row = coordinate_from_string(cur_end_cell)

                # 将列名转换为数字（如 "A" -> 1）
                cur_start_col_num = column_index_from_string(cur_start_col)
                cur_end_col_num = column_index_from_string(cur_end_col)

                for start_row_idx in range(int(cur_start_row),int(cur_end_row)+1):
                    for start_col_idx in range(int(cur_start_col_num),int(cur_end_col_num)+1):
                        get_merge_cells[f'{get_column_letter(start_col_idx)}{start_row_idx}'] = cur_start_cell

            #获取无需检测的单元格
            no_include_all_row = []
            no_include_all_colunm = []
            no_include_all_single_pos = []
            for no_include_cell in check_info.range_not_included:
                if ":" in no_include_cell:
                    # 解析合并范围（如 "A1:C3"）
                    # examples=["A1:A1","C:C","1:1"，'A1:C6']
                    no_start_cell, no_end_cell = no_include_cell.split(":")

                    is_start_letter = no_start_cell.isalpha() and no_start_cell.isupper()
                    is_end_letter= no_end_cell.isalpha() and no_end_cell.isupper()
                    is_start_dig = no_start_cell.isdigit()
                    is_end_dig = no_end_cell.isdigit()
                    if is_start_letter and is_end_letter:
                        #整列排除
                        no_start_cell_num = column_index_from_string(no_start_cell)
                        no_end_cell_num = column_index_from_string(no_end_cell)
                        for no_num_idx in range(no_start_cell_num,no_end_cell_num+1):
                            no_include_all_colunm.append(get_column_letter(no_num_idx))
                    elif is_start_dig and is_start_dig:
                        #整行排除
                        for no_num_idx in range(int(no_start_cell), int(no_end_cell) + 1):
                            no_include_all_row.append(str(no_num_idx))
                    else:
                        # rang("A1:C6")单元格
                        no_start_col, no_start_row = coordinate_from_string(no_start_cell)
                        no_end_col, no_end_row = coordinate_from_string(no_end_cell)
                        # 将列名转换为数字（如 "A" -> 1）
                        no_start_col_num = column_index_from_string(no_start_col)
                        no_end_col_num = column_index_from_string(no_end_col)
                        # 遍历指定范围内的所有单元格
                        for row in range(int(no_start_row), int(no_end_row) + 1):
                            for col in range(no_start_col_num, no_end_col_num + 1):
                                cell = ws.cell(row=row, column=col)
                                no_include_all_single_pos.append(f"{get_column_letter(cell.column)}{cell.row}")


            # 转换为列号（如 "A" -> 1）
            start_col_num = column_index_from_string(start_col)
            end_col_num = column_index_from_string(end_col)

            if check_info.check_direction == 'down':
                if check_info.check_direction_enable:
                    end_row = ws.max_row
            elif  check_info.check_direction == 'right':
                if check_info.check_direction_enable:
                    end_col_num = ws.max_column
                #如果向右检测，需要将行和列倒置
                start_row, start_col_num = start_col_num, start_row
                end_row, end_col_num = end_col_num, end_row

            # 遍历指定范围内的所有单元格
            for row in range(int(start_row), int(end_row) + 1):
                line_blank = True
                line_empty_cell = []
                line_ng_cell = []
                count_skip = 0
                for col in range(start_col_num, end_col_num + 1):
                    str_cell = f'{get_column_letter(col)}{row}'
                    cell = ws.cell(row=row, column=col)
                    if check_info.check_direction == 'right':
                        str_cell = f'{get_column_letter(row)}{col}'
                        cell = ws.cell(row=col, column=row)
                    if str_cell in get_merge_cells.keys():
                        temp_start_col_str, temp_start_row = coordinate_from_string(get_merge_cells[str_cell])
                        temp_start_col_num = column_index_from_string(temp_start_col_str)
                        cell = ws.cell(row=temp_start_row, column=temp_start_col_num)
                    cell_value = cell.value

                    #检测到是排除检测单元格跳过
                    if get_column_letter(cell.column) in no_include_all_colunm or\
                        str(cell.row) in no_include_all_row or \
                        str_cell  in  no_include_all_single_pos:
                        count_skip = count_skip+1
                        if cell_value is not None and str(cell_value).strip() != "":#  and str(cell_value).strip() != "-" and "=" != str(cell_value).strip()[0]:
                            line_blank = False
                    else:
                        text_value =str(cell_value).strip().upper()
                        check_text_value = "".join(re.findall(r'[A-Z]', text_value))
                        # 检查NG/空白项（None 或空字符串）
                        ng_status = False
                        if check_ng_flag:
                            ng_status = bool("NG" in str(cell_value).strip().upper() and "NG" == check_text_value)
                        if cell_value is None or str(cell_value).strip() == "":
                            line_empty_cell.append(f"{get_column_letter(cell.column)}{cell.row}")
                        # 检查 NG 项（区分大小写）
                        elif ng_status \
                                or (("NA" in str(cell_value).strip().upper()  or "N/A" in str(cell_value).strip().upper()) and "NA" == check_text_value) \
                                or ("未" == str(cell_value).strip().upper()):
                              #or ( "NA" in str(cell_value).strip())
                            line_ng_cell.append(f"{get_column_letter(cell.column)}{cell.row}")
                            line_blank = False
                        else:
                            line_blank = False

                #如果整行为空，退出检测
                if check_info.check_direction_enable and line_blank and row>start_row and (count_skip != (end_col_num + 1-start_col_num)):
                    break
                else:
                    if len(line_empty_cell):
                        empty_cells.extend(line_empty_cell)
                        empty_cells =(list(set(empty_cells)))
                    if len(line_ng_cell):
                        ng_cells.extend(line_ng_cell)
                        ng_cells =(list(set(ng_cells)))
            wb.close()
            return {
                sheet_name: {
                    "确认是否有空白项": bool(True if (len(empty_cells) == 0) else False),
                    "blank_message": (str(empty_cells) + f"这些单元格空白没有内容") if (len(empty_cells)) else "",
                    "确认没有NG项": bool(True if (len(ng_cells) == 0) else False),
                    "ng_message": (str(ng_cells) + f"这些单元格内容是NA或者NG") if (len(ng_cells)) else ""
                }
            }
        else:
            wb.close()
            return {check_sheet_name: {
                "确认内容填写是否异常": False,
                "content_message": f"{check_sheet_name} sheet在表格中找不到"
            }
            }
    except FileNotFoundError:
        raise FileNotFoundError(f"文件 {file_path} 未找到。")
    except KeyError:
        raise ValueError(f"工作表 {check_sheet_name} 不存在。")
    except Exception as e:
        raise RuntimeError(f"检查过程中发生错误: {e}")



def check_blank_ng_na_xls(file_path, check_sheet_name, check_info:CheckRangeIninfo,full_word_matching:bool = False,check_ng_flag:bool=True,check_direction:bool=True):
    """
        检查表格是否有空白项或者NG项。

        :param file_path: Excel 文件路径
        :param sheet_name: 要检查的 Sheet 名称
        :param  CheckRangeIninfo
        :        cell_address: 单元格地址（如 "A1:A2"），如果没有给默认检查整个表格范围
        :        not_included: 检测范围中需要排除检测范围的列（['A','J','AA']）
        :        check_direction_enable  :是否根据range进行检测
        :        directions: 检索方向，如果给定范围只有一行或者一列，根据方向检索，直到整行或者整列空白 退出检索
        :return: ["blank_ng":{"XXX单元格":"范围单元格空白或者标记NG项"}]
        """
    """
        检查指定 Excel 文件的指定工作表和单元格范围：
        1. 是否包含合并单元格
        2. 是否有空白项（空值或空字符串）
        3. 是否有 NG 项（值为 "NG" 的单元格）

        参数:
            file_path (str): Excel 文件路径
            sheet_name (str): 工作表名称
            cell_range (str): 单元格范围（例如 "A1:C5"）

        返回:
            dict: 包含合并单元格、空白项和 NG 项的列表
        """


    try:
        # 启动 Excel 应用（后台运行）
        app = xw.App(visible=False, add_book=False)
        # 解析单元格范围（例如 "A1:C5"）
        start_cell, end_cell = check_info.range_address.split(":")
        start_col, start_row = coordinate_from_string(start_cell)
        end_col, end_row = coordinate_from_string(end_cell)

        # 检测入参有效性
        if start_row != end_row and start_col != end_col and check_direction:
            check_info.check_direction_enable = False

        # 打开 Excel 文件
        wb = app.books.open(file_path)
        # 初始化结果
        merged_cells = []
        empty_cells = []
        ng_cells = []
        sheet_name = ''
        sheet = None
        for sheet_name in wb.sheet_names:
            if full_word_matching and check_sheet_name in wb.sheet_names:
                sheet_name = check_sheet_name
                sheet = wb.sheets[sheet_name]
                break
            elif not full_word_matching and check_sheet_name in sheet_name and (f"Format変更履歴" not in sheet_name) and  (f"記入例" not in sheet_name ):
                sheet = wb.sheets[sheet_name]
                break
            else:
                if full_word_matching:
                    break

        if sheet is not None:
            logger.info(f"正在检查{sheet_name}这个sheet")
            # 获取无需检测的单元格
            no_include_all_row = []
            no_include_all_colunm = []
            no_include_all_single_pos = []
            for no_include_cell in check_info.range_not_included:
                if ":" in no_include_cell:
                    # 解析合并范围（如 "A1:C3"）
                    # examples=["A1:A1","C:C","1:1"，'A1:C6']
                    no_start_cell, no_end_cell = no_include_cell.split(":")

                    is_start_letter = no_start_cell.isalpha() and no_start_cell.isupper()
                    is_end_letter = no_end_cell.isalpha() and no_end_cell.isupper()
                    is_start_dig = no_start_cell.isdigit()
                    is_end_dig = no_end_cell.isdigit()
                    if is_start_letter and is_end_letter:
                        # 整列排除
                        no_start_cell_num = column_index_from_string(no_start_cell)
                        no_end_cell_num = column_index_from_string(no_end_cell)
                        for no_num_idx in range(no_start_cell_num, no_end_cell_num + 1):
                            no_include_all_colunm.append(get_column_letter(no_num_idx))
                    elif is_start_dig and is_start_dig:
                        # 整行排除
                        for no_num_idx in range(int(no_start_cell), int(no_end_cell) + 1):
                            no_include_all_row.append(str(no_num_idx))
                    else:
                        # rang("A1:C6")单元格
                        no_start_col, no_start_row = coordinate_from_string(no_start_cell)
                        no_end_col, no_end_row = coordinate_from_string(no_end_cell)
                        # 将列名转换为数字（如 "A" -> 1）
                        no_start_col_num = column_index_from_string(no_start_col)
                        no_end_col_num = column_index_from_string(no_end_col)
                        # 遍历指定范围内的所有单元格
                        for row in range(int(no_start_row), int(no_end_row) + 1):
                            for col in range(no_start_col_num, no_end_col_num + 1):
                                str_cell = f'{get_column_letter(col)}{row}'
                                cell = sheet.range(str_cell)
                                no_include_all_single_pos.append(f"{get_column_letter(cell.column)}{cell.row}")

            # 转换为列号（如 "A" -> 1）
            start_col_num = column_index_from_string(start_col)
            end_col_num = column_index_from_string(end_col)

            if check_info.check_direction == 'down':
                if check_info.check_direction_enable:
                    end_row = sheet.used_range.rows.count
            elif check_info.check_direction == 'right':
                if check_info.check_direction_enable:
                    end_col_num = sheet.used_range.columns.count
                # 如果向右检测，需要将行和列倒置
                start_row, start_col_num = start_col_num, start_row
                end_row, end_col_num = end_col_num, end_row


            # 遍历指定范围内的所有单元格
            for row in range(int(start_row), int(end_row) + 1):
                line_blank = True
                line_empty_cell = []
                line_ng_cell = []
                count_skip = 0
                for col in range(start_col_num, end_col_num + 1):
                    str_cell = f'{get_column_letter(col)}{row}'
                    cell = sheet.range(str_cell)
                    if check_info.check_direction == 'right':
                        str_cell = f'{get_column_letter(row)}{col}'
                        cell = sheet.range(str_cell)

                    # 定位到当前单元格
                    current_cell = sheet.range(cell.address)
                    # 检查是否是合并单元格
                    # 获取合并区域的范围
                    merged_area = current_cell.api.MergeArea
                    # 获取合并区域的起始单元格
                    start_cell = sheet.range(merged_area.Address.split(':')[0])
                    cell_value = start_cell.value
                    # 检测到是排除检测单元格跳过
                    if get_column_letter(cell.column) in no_include_all_colunm or \
                            str(cell.row) in no_include_all_row or \
                            str_cell in no_include_all_single_pos:
                        count_skip = count_skip + 1
                        if cell_value is not None and str(cell_value).strip() != "":
                            line_blank = False
                    else:
                        text_value = str(cell_value).strip().upper()
                        check_text_value = "".join(re.findall(r'[A-Z]', text_value))
                        # 检查空白项（None 或空字符串）
                        # 检查NG/空白项（None 或空字符串）
                        ng_status = False
                        if check_ng_flag:
                            ng_status = bool("NG" in str(cell_value).strip().upper() and "NG" == check_text_value)
                        if cell_value is None or str(cell_value).strip() == "":
                            line_empty_cell.append(
                                f"{get_column_letter(cell.column)}{cell.row}")
                        # 检查 NG 项（区分大小写）
                        elif ng_status \
                                or (("NA" in str(cell_value).strip().upper() or "N/A" in str(cell_value).strip().upper() )  and "NA" == check_text_value) \
                                or ("未" == str(cell_value).strip().upper()):
                            # or ( "NA" in str(cell_value).strip())
                            line_ng_cell.append(
                                f"{get_column_letter(cell.column)}{cell.row}")
                            line_blank = False
                        else:
                            line_blank = False

                # 如果整行为空，退出检测
                if check_info.check_direction_enable and line_blank and row > start_row and (
                        count_skip != (end_col_num + 1 - start_col_num)):
                    break
                else:
                    if len(line_empty_cell):
                        empty_cells.extend(line_empty_cell)
                        empty_cells = (list(set(empty_cells)))
                    if len(line_ng_cell):
                        ng_cells.extend(line_ng_cell)
                        ng_cells = (list(set(ng_cells)))
                    # 关闭工作簿并退出 Excel
            wb.close()
            app.quit()
            return {
                sheet_name: {
                    "确认是否有空白项": bool(True if (len(empty_cells) == 0) else False),
                    "blank_message": (str(empty_cells) + f"这些单元格空白没有内容") if (len(empty_cells)) else "",
                    "确认没有NG项": bool(True if (len(ng_cells) == 0) else False),
                    "ng_message": (str(ng_cells) + f"这些单元格内容是NA或者NG") if (len(ng_cells)) else ""
                }
            }
        else:
            return {check_sheet_name: {
                "确认内容填写是否异常": False,
                "content_message": f"{check_sheet_name} sheet在表格中找不到"
            }
            }

        # # 提取合并区域的文本
        # merged_text = str(start_cell.value) if start_cell.value else ""
        #
        # # 保存合并区域中的图片（需遍历 Shapes）
        # for shape in merged_area.Shapes:
        #     if shape.Type == 13:  # 13 表示图片类型
        #         image = shape.PictureFormat
        #         image_data = image.Export()
        #         img = Image.open(io.BytesIO(image_data))
        #         img.save(f"image_{shape.Name}.png")




    except Exception as e:
        raise RuntimeError(f"检查过程中发生错误: {e}")


def check_cs_design_conditions(file_path,sheet_name = ""):
    # 检查空白和NG内容
    ng_results = {}
    check_ng_sheets_info = {
        "設計基準CS-要件分析": {"range_address": ["J:AA"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": ["M:M","V:V"]},
        "設計基準CS-基本設計": {"range_address": ["J:AA"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": ["M:M","V:V"]},

        "設計基準CS-詳細設計": {"range_address": ["J:AA"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": ["M:M","V:V"]},

        "設計基準CS-コードチェック": {"range_address": ["J:AA"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": ["M:M","V:V"]},

        "設計基準CS-結合検査": {"range_address": ["J:AA"],
                     "check_direction": 'down',
                     "check_direction_enable": False,
                     "range_not_included": ["M:M"]}
    }

    stamp_results = {}
    ng_results = {}
    stamp_sheets_name = ["使用時変更履歴"]
    for sheet_name in stamp_sheets_name:
        stamp_result = check_stamp(file_path, sheet_name)
        stamp_results = mergeDict(stamp_results, stamp_result)

    check_info = CheckRangeIninfo()
    for sheet_name,check_info_value in check_ng_sheets_info.items():
        range_addresses = check_info_value["range_address"]
        check_info.check_direction = check_info_value["check_direction"]
        check_info.check_direction_enable = check_info_value["check_direction_enable"]
        check_info.range_not_included = check_info_value["range_not_included"]
        titles = get_excel_columns_data(file_path, sheet_name, None, data_by_col_idx=9)
        temp_range_addresses = range_addresses
        range_addresses = []
        for range_addr in temp_range_addresses:
            for idx, title in enumerate(titles):
                if title == "該当":
                    range_addresses.append(range_addr.split(":")[0]+str(idx + 1) + ":" + range_addr.split(":")[1]+str(idx + 1))
        for range_addr in range_addresses:
            check_info.range_address = range_addr
            ng_result = check_blank_ng_na(file_path, sheet_name, check_info)
            ng_results = mergeDict(ng_results, ng_result)

    return mergeDict(ng_results, stamp_results)
