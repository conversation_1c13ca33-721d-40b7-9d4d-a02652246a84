import os
import re
import uuid
import traceback
from collections import defaultdict
from typing import Dict, <PERSON><PERSON>, Any, List

import openpyxl
import win32com.client as win32
from loguru import  logger

from sdw_agent.service.cstm_tool.model import CstmMetaInfo, Cstmcnttinfo


class ReqResolver:
    def __init__(self, req_path: str, config):
        self.req_path = req_path
        self.config = config
        self.sheet_name = self.config.get("req_config",{}).get('target_sheet', 'JP')
        self.start_row = self.config.get("req_config",{}).get('start_row', 8)
        self.start_col = self.config.get("req_config",{}).get('start_col', 2)
        self.max_level = self.config.get("req_config",{}).get('max_level', 7)
        self.logger = logger.bind(name="ReqResolver")

    def init_cstmlist(self):
        """
        初始化解析文件meta data 对象
        """
        # 解析出式样书中的结束行和结束列
        resolve_sheet, end_row, end_column = self.find_table_end()
        print(f"Table ends at row {end_row} and column {end_column}")
        try:
            excel = win32.Dispatch("Excel.Application")
            wb = excel.Workbooks.Open(
                os.path.abspath(self.req_path))
            ws = wb.Sheets(self.sheet_name)
        except Exception as e:
            traceback.print_exc()
            raise Exception(
                # f"{gettext('打开原始式样书失败：')}{file_uri}','{gettext('请确保已关闭该式样书文件')}"
                f"打开原始式样书失败：{self.req_path},请确保已关闭该式样书文件"
            )

        return CstmMetaInfo(self.start_row, end_row, self.start_col, end_column, self.max_level, resolve_sheet, ws)


    def find_table_end(self):
        """
        查找Excel表格中的结束行和列。
        当B列没有边框样式时，认为表格结束。

        参数:
            file_uri (str): Excel文件路径
            sheet_name (str): 要分析的工作表名称

        返回:
            tuple: (end_row, end_column) 表格结束的位置
        """
        wb = openpyxl.load_workbook(self.req_path)
        sheet = wb[self.sheet_name]

        # 对于模板文件 start_row是固定从第8行开始读
        end_row = None
        end_column = None

        # 定位出table的结束行，如果第B列的单元格没有样式表示table结束
        for row in range(self.start_row, sheet.max_row + 1):
            cell = sheet.cell(row=row, column=2)  # Check column B
            if cell.border.left.style is None and cell.border.right.style is None and \
                    cell.border.top.style is None and cell.border.bottom.style is None:
                end_row = row - 1
                break
        if end_row is None:
            end_row = sheet.max_row

            # 从sheet 最大列所有从右往左遍历，只遍历start_row行，如果单元格边框有样式则表示遍历到了table最大索引列
        for col in range(sheet.max_column, 0, -1):
            cell = sheet.cell(row=self.start_row, column=col)
            if cell.border.left.style is not None or cell.border.right.style is not None or \
                    cell.border.top.style is not None or cell.border.bottom.style is not None:
                end_column = col
                break

        return sheet, end_row, end_column

    def process_sheet(self, sheet_data):
        """主函数：处理Excel工作表并返回列映射字典
        参数:
            sheet_data: openpyxl Worksheet对象

        返回:
            dict: 包含{(序列名称, 列名称): 列字母}映射的字典
        """
        # 1. 查找"表示顺序"所在的行
        sequence_row = self.find_sequence_row(sheet_data)
        if sequence_row is None:
            print("警告：未找到包含'表示順序'的行")
            return {}

        # 2. 创建合并单元格映射字典
        merged_map = self.build_merged_map(sheet_data)

        # 3. 处理列数据并生成结果字典
        result_dict = self._process_columns(sheet_data, sequence_row, merged_map)

        return result_dict

    def find_sequence_row(self, sheet_data):
        """查找包含'表示顺序'关键字的行
        参数:
            sheet_data: openpyxl Worksheet对象
        返回:
            int: 包含关键字的行号，如果未找到则返回None
        """
        # 限制搜索范围（前100行），避免大文件的性能问题
        max_search_rows = min(100, sheet_data.max_row) if sheet_data.max_row > 0 else 100

        for row_index in range(1, max_search_rows + 1):
            for col_index in range(1, sheet_data.max_column + 1):
                cell = sheet_data.cell(row=row_index, column=col_index)

                # 跳过空单元格
                if cell.value is None:
                    continue

                # 检查单元格值是否包含目标关键字
                try:
                    cell_str = str(cell.value).strip()
                    if "表示順序" in cell_str:
                        return row_index
                except Exception as e:
                    # 处理可能的转换异常
                    print(f"处理单元格(R{row_index}C{col_index})时出错: {str(e)}")
                    continue

        # 未找到包含关键字的行
        return None

    def build_merged_map(self, sheet_data):
        """构建合并单元格映射字典
        创建字典记录每个单元格对应的合并区域主单元格位置
        参数:
            sheet_data: openpyxl Worksheet对象
        返回:
            dict: 键为(行, 列)元组，值为(主单元格行, 主单元格列)元组
        """
        merged_map = {}

        # 检查是否有合并单元格
        if not hasattr(sheet_data, 'merged_cells') or sheet_data.merged_cells is None:
            return merged_map

        try:
            # 遍历所有合并区域
            for merged_range in sheet_data.merged_cells.ranges:
                # 获取合并区域的边界
                min_row = merged_range.min_row
                min_col = merged_range.min_col
                max_row = merged_range.max_row
                max_col = merged_range.max_col

                # 遍历合并区域内的所有单元格
                for row in range(min_row, max_row + 1):
                    for col in range(min_col, max_col + 1):
                        # 记录合并区域主单元格位置
                        merged_map[(row, col)] = (min_row, min_col)
        except Exception as e:
            print(f"构建合并单元格映射时出错: {str(e)}")

        return merged_map

    def look_up_menucnttinfo(self, req_file_meta, cntt_list, index_dict):
        """
        解析Menu阶层信息
        :param req_file_meta:
        :param cntt_list:
        :param index_dict:
        :return:
        """

        # 创建MENU画面
        resolve_sheet = req_file_meta.resolve_sheet
        origin_sheet = req_file_meta.origin_sheet
        cntt_list.append(Cstmcnttinfo("Menu"))
        cntt_list[0].cntt_name_updated_flag = False
        cntt_list[0].uuid = str(uuid.uuid4())
        cntt_list[0].cntt_pre = 'Menu'
        cntt_list[0].opt_pre = 'NONSTS'
        cntt_list[0].col_range = [2, 14]
        cntt_list[0].row_range = [req_file_meta.start_row + 1, req_file_meta.end_row+1]
        linked_rol_index = self.get_linked_col_idx(req_file_meta, index_dict) # 获取关联式样列的索引
        self.logger.info(f"正在解析第0阶层画面信息...")

        # 遍历MENU画面选项
        for row in resolve_sheet.iter_rows(req_file_meta.start_row + 1, req_file_meta.end_row,
                                           req_file_meta.start_column + 2, req_file_meta.start_column + 2):
            if not cntt_list[0].coordinate:
                cntt_list[0].coordinate = row[0].coordinate
            for cell in row:
                if cell.value is not None and str(cell.fill.start_color.index) != '0':

                    if isinstance(cell.value, str):
                        optname, opt_has_strikethrough, bf_optname = self.extract_format_value(origin_sheet.Cells(cell.row, cell.column), need_bf=True)
                        optname = self.replace_special_chars(optname)
                        bf_optname = bf_optname.split('\n')[0].strip()
                        cntt_list[0].optname.append(optname)
                        cntt_list[0].optname_bf.append(bf_optname)
                        cntt_list[0].opt_name_updated_flag.append(opt_has_strikethrough)
                        cntt_list[0].SpdEnblJdg.append('可')
                    # 如果项目名不为空，记录单元格位置信息
                    cntt_list[0].opt_coordinates.append(cell.coordinate)
                    # 选项起始行
                    cntt_list[0].optcoordinate[0].append(cell.row)
                    # 选项结束行查找
                    for i in range(cell.row + 1, req_file_meta.end_row + 1):
                        # 对行进行遍历，如果单元格值为空则表示还在该画面范围内，如果单元格有值则说明进入了下一个画面范围
                        if resolve_sheet.cell(i, cell.column).value is not None or resolve_sheet.cell(i,cell.column).value == '0':
                            cntt_list[0].optcoordinate[1].append(i - 1)
                            break
                        elif i == req_file_meta.end_row:
                            cntt_list[0].optcoordinate[1].append(i)
                            break
                    # 根据画面起始行收集指定列信息
                    enter_sws = []
                    opt_selec_supps = []
                    on_offs = []
                    opttypes = []
                    enter_sw_col_index = self.get_column_idx(0, '有効Enter-SW操作', index_dict)
                    opt_type_col_index = self.get_column_idx(0, '切替\nType', index_dict)
                    opt_selec_supps_index = self.get_column_idx(0, '項目選択時の補足説明\n※7', index_dict)
                    on_off_index = self.get_column_idx(0, 'ON/OFF\n設定表示\n※19', index_dict)
                    for j in range(cntt_list[0].optcoordinate[0][-1], cntt_list[0].optcoordinate[1][-1] + 1):
                        # 有効Enter-SW操作 有可能多行
                        if resolve_sheet.cell(j, enter_sw_col_index).value is not None and \
                                str(resolve_sheet.cell(cell.row, enter_sw_col_index).fill.start_color.index) != '0':
                            enter_sw, _, _ = self.extract_format_value(origin_sheet.Cells(j, enter_sw_col_index))
                            enter_sws.append(enter_sw)
                            opt_type, _, _ = self.extract_format_value(origin_sheet.Cells(j, opt_type_col_index))
                            if opt_type is not None:
                                opttypes.append(opt_type)

                        # 項目選択時の補足説明 有可能多行
                        if resolve_sheet.cell(j, opt_selec_supps_index).value is not None and \
                                str(resolve_sheet.cell(cell.row, opt_selec_supps_index).fill.start_color.index) != '0':
                            selec_supp, _, _ = self.extract_format_value(req_file_meta.origin_sheet.Cells(j, opt_selec_supps_index))
                            selec_supp = self.replace_special_chars(selec_supp)
                            opt_selec_supps.append(selec_supp)

                        # ON/OFF 設定表示 可能包含多行记录
                        on_off_cel_1 = resolve_sheet.cell(j, on_off_index)
                        on_off_cel_2 = resolve_sheet.cell(j, on_off_index+1)
                        if on_off_cel_2.value is not None and on_off_cel_2.value != '-' and \
                                str(on_off_cel_2.fill.start_color.index) != '1':
                            # 如果 ON/OFF表示 列为有效值（非图片）
                            if on_off_cel_1.value is not None and on_off_cel_1.value != '-' and \
                                    str(on_off_cel_1.fill.start_color.index) != '1':
                                on_off_1, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_index))
                                on_off_2, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_index + 1))
                                on_offs.append(on_off_1 + '/' + on_off_2)
                            else:
                                on_off_2, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_index + 1))
                                on_offs.append(on_off_2)

                    cntt_list[0].enter_sw.append(enter_sws)
                    cntt_list[0].opt_selec_supplement.append(opt_selec_supps)
                    cntt_list[0].on_off.append(on_offs)
                    cntt_list[0].opttype.append(opttypes)

                # 关联式样 19PFver3適用仕様書
                if resolve_sheet.cell(cell.row, linked_rol_index).value not in [None, '-']:
                    linked_excel_name, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, linked_rol_index))
                    cntt_list[0].linked_excel_name.append(linked_excel_name.split('\n'))
                else:
                    cntt_list[0].linked_excel_name.append(None)

        # 更新0阶层，MENU画面各选项的下阶层画面
        self.lookup_nextcntt(0, 0, cntt_list, resolve_sheet, req_file_meta, index_dict)

        return cntt_list

    def look_up_othercnttinfo(self, req_file_meta, cntt_list, index_dict):
        """
        解析除MENU外的其它画面信息
        :param req_file_meta:
        :param cntt_list:
        :param index_dict:
        :return:
        """
        import uuid

        resolve_sheet = req_file_meta.resolve_sheet
        origin_sheet = req_file_meta.origin_sheet
        self.logger.info(f"原始式样书的索引字典 ： {index_dict}")
        img_label_cntt = []
        linked_rol_index = self.get_linked_col_idx(req_file_meta, index_dict) # 获取关联式样列的索引
        # 走行中操作可能列索引
        running_operate_allow_col_index = index_dict.get(('走行中操作可能', '走行中操作可能'), req_file_meta.end_column - 9)

        for i in range(1, req_file_meta.level_num):
            self.logger.info(f'正在解析第{i}阶层画面信息...')

            col_diff = self.process_map_difference(index_dict, '項目')
            current_level_name = self.find_keys_by_value(self.LEVEL_MAP_SAMPLE_BOOK, str(i))
            last_level_name = self.find_keys_by_value(self.LEVEL_MAP_SAMPLE_BOOK, str(i - 1))

            if current_level_name:
                current_level_name = current_level_name[0]
            if last_level_name:
                last_level_name = last_level_name[0]
            col_name = '項目\n（タイトル）' if i == 0 else '項目'
            opt_name_format_col_index = index_dict.get((current_level_name, '項目')) # 当前阶层的选项列索引
            last_selec_supp_col_index = index_dict.get((last_level_name, '項目選択時の補足説明\n※7')) # 上一阶层的 項目選択時の補足説明
            hierarchy_first_col_idx = index_dict.get((current_level_name, 'タイトル')) # 当前阶层的第一列所在索引
            cell_selec_supp_col_index = index_dict.get((current_level_name, '項目選択時の補足説明\n※7'))
            on_off_cell_index = index_dict.get((current_level_name, 'ON/OFF\n設定表示\n※19'))
            opt_type_col_index = index_dict.get((current_level_name, '切替\nType'))

            for row in resolve_sheet.iter_rows(req_file_meta.start_row + 1, req_file_meta.end_row,
                                               hierarchy_first_col_idx, hierarchy_first_col_idx):
                for cell in row:
                    if (cell.value is not None and cell.value.strip() != "-") or \
                            ((cell.value is None or cell.value.strip() != "-") and
                             resolve_sheet.cell(cell.row, last_selec_supp_col_index).value is not None and
                             resolve_sheet.cell(cell.row, opt_name_format_col_index).value is not None):

                        cnttname_format, has_strikethrough, bf_cnttname = self.extract_format_value(origin_sheet.Cells(cell.row, cell.column), need_bf=True)
                        cnttname_format = self.replace_special_chars(cnttname_format)

                        bf_cnttname = self.replace_special_chars(bf_cnttname)

                        # 选项名称
                        opt_name, opt_has_strikethrough, bf_opt_name = self.extract_format_value(origin_sheet.Cells(cell.row, opt_name_format_col_index), need_bf=True)
                        last_selec_supp, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, last_selec_supp_col_index))
                        if (cnttname_format != '-' and cnttname_format.strip() != "") or \
                                (cnttname_format.strip() == "" and last_selec_supp not in [None, ""] and opt_name not in [None, ""]):
                            # 有可能画面描述只有一张图片，没有文本内容
                            if cnttname_format.strip() == "":
                                cntt_list.append(Cstmcnttinfo(f"画面_{len(img_label_cntt)}"))
                                img_label_cntt.append(cell.coordinate)
                            else:
                                cntt_list.append(Cstmcnttinfo(cnttname_format))  # 2：画面名

                            cntt_list[-1].has_strikethrough = has_strikethrough
                            cntt_list[-1].cnttname_bf = bf_cnttname
                            cntt_list[-1].uuid = str(uuid.uuid4())
                            cntt_list[-1].cstm_level = i  # 1：画面阶层
                            cntt_list[-1].coordinate = cell.coordinate  # 画面起始单元格坐标

                            cntt_list[-1].col_range.append(cell.column)
                            cntt_list[-1].col_range.append(cell.column + col_diff - 1)
                            cntt_list[-1].row_range.append(resolve_sheet.cell(cell.row, cell.column).row)  # 画面起始行
                            self.lookup_cntt_end_row(cell, cntt_list, resolve_sheet, req_file_meta) # 计算画面的结束行

                            if opt_name is not None and str(resolve_sheet.cell(cell.row, opt_name_format_col_index).fill.start_color.index) != '1':
                                # 如果项目名不为空，记录单元格位置信息
                                cntt_list[-1].opt_coordinates.append(
                                    resolve_sheet.cell(cell.row, opt_name_format_col_index).coordinate)

                                # 项目列 不为空  选项名称
                                optname = self.replace_special_chars(opt_name)
                                bf_opt_name = self.replace_special_chars(bf_opt_name)

                                cntt_list[-1].optname.append(optname)
                                cntt_list[-1].opt_name_updated_flag.append(opt_has_strikethrough)
                                cntt_list[-1].optname_bf.append(bf_opt_name)

                                # 定位选项的开始，结束行
                                self.lookup_optcoordinate(cell, cntt_list, resolve_sheet, req_file_meta, i, index_dict)
                                opt_start_row = cntt_list[-1].optcoordinate[0][-1]
                                opt_end_row = cntt_list[-1].optcoordinate[1][-1]

                                # 遍历选项起始行
                                selec_supps = []
                                on_offs = []

                                for j in range(opt_start_row, opt_end_row + 1):
                                    # 項目選択時の補足説明 可能包含多行记录
                                    cell_selec_supp = resolve_sheet.cell(j, cell_selec_supp_col_index)
                                    if cell_selec_supp.value is not None and cell_selec_supp.value.strip() != "" and cell_selec_supp.value.strip() != "-":
                                        selec_supp, _, _ = self.extract_format_value(origin_sheet.Cells(j, cell_selec_supp_col_index))
                                        selec_supp = self.replace_special_chars(selec_supp)
                                        selec_supps.append(selec_supp)

                                    # ON/OFF 設定表示 可能包含多行记录
                                    on_off_cell_1, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_cell_index))
                                    on_off_cell_2, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_cell_index + 1))
                                    if on_off_cell_2 is not None and on_off_cell_2 != '-' and on_off_cell_2.strip() != "":
                                        # 如果 ON/OFF表示 列为有效值（非图片）
                                        if on_off_cell_1 is not None and on_off_cell_1 != '-' and on_off_cell_1.strip() != "":
                                            on_offs.append([on_off_cell_1, on_off_cell_2])
                                        else:
                                            # ON/OFF表示列 有可能为'-' 或者是一张图片
                                            on_offs.append(["", on_off_cell_2])

                                cntt_list[len(cntt_list) - 1].opt_selec_supplement.append(selec_supps)
                                cntt_list[len(cntt_list) - 1].on_off.append(on_offs)

                                # 走行中操作可能
                                if resolve_sheet.cell(cell.row, running_operate_allow_col_index).value is not None:
                                    spdjudge_sts, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, running_operate_allow_col_index))
                                    spdjudge_sts = spdjudge_sts.split('\n')
                                    cntt_list[-1].SpdEnblJdg.append(spdjudge_sts[-1])  # 9：走行可否
                                else:
                                    running_enable = self.get_merged_cell_value_by_all_sheet(resolve_sheet, cell.row, running_operate_allow_col_index)
                                    cntt_list[-1].SpdEnblJdg.append(running_enable)  # 9：走行可否

                                # 切替 Type
                                if resolve_sheet.cell(cell.row, opt_type_col_index).value is not None:
                                    opt_type, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, opt_type_col_index))
                                    # 5：选项type
                                    cntt_list[-1].opttype.append(self.replacechar(opt_type, '\n'))
                                else:
                                    # 5：选项type
                                    if cntt_list[-1].opttype:
                                        cntt_list[-1].opttype.append(self.replacechar(cntt_list[-1].opttype[-1], '\n'))
                                    else:
                                        cntt_list[-1].opttype.append(self.replacechar("-", '\n'))
                                # 关联式样 19PFver3適用仕様書
                                # 如果当前选项为子项，没有下一阶层画面 则获取当前画面对应的关联式样名称
                                if resolve_sheet.cell(cell.row, linked_rol_index).value not in [None, '-']:
                                    linked_excel_name, _, _ = self.extract_format_value(
                                        origin_sheet.Cells(cell.row, linked_rol_index)
                                    )
                                    cntt_list[-1].linked_excel_name.append(linked_excel_name.split('\n'))
                                else:
                                    cntt_list[-1].linked_excel_name.append(None)
                            else:
                                cntt_list[-1].optcoordinate[0].append(cell.row)  # 4：选项坐标
                                cntt_list[-1].optcoordinate[1].append(cell.row)  # 4：选项坐标

                    else:
                        # 遍历行的时候 画面名称列 タイトル 为空，但是项目列不为空
                        opt_type_col_index = index_dict.get((current_level_name, '切替\nType'))

                        opt_name_format, opt_has_strikethrough, bf_opt_name = self.extract_format_value(origin_sheet.Cells(cell.row, opt_name_format_col_index), need_bf=True)
                        opt_type, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, opt_type_col_index))
                        if (opt_name_format is not None and opt_name_format != "") or (opt_name_format == "" and opt_type != ""):

                            # 如果项目名不为空，记录单元格位置信息
                            cntt_list[-1].opt_coordinates.append(resolve_sheet.cell(cell.row, opt_name_format_col_index).coordinate)

                            # 定位选项的起始行位置
                            self.lookup_optcoordinate(cell, cntt_list, resolve_sheet, req_file_meta, i, index_dict)
                            opt_start_row = cntt_list[-1].optcoordinate[0][-1]
                            opt_end_row = cntt_list[-1].optcoordinate[1][-1]

                            # 项目
                            if opt_name_format != "":
                                optname = self.replace_special_chars(opt_name_format)
                            else:
                                optname = f"选项_{len(cntt_list[-1].optname)}".split('\n')
                                optname = " ".join(optname).strip()

                            bf_opt_name = self.replace_special_chars(bf_opt_name)

                            cntt_list[-1].optname.append(optname)
                            cntt_list[-1].optname_bf.append(bf_opt_name)
                            cntt_list[-1].opt_name_updated_flag.append(opt_has_strikethrough)

                            # 根据选项起始行
                            selec_supps = []
                            on_offs = []
                            for j in range(opt_start_row, opt_end_row + 1):
                                # 項目選択時の補足説明 可能包含多行记录
                                cell_selec_supp = resolve_sheet.cell(j, cell_selec_supp_col_index)

                                if cell_selec_supp.value is not None and str(
                                        cell_selec_supp.fill.start_color.index) != '1':
                                    selec_supp, _, _ = self.extract_format_value(origin_sheet.Cells(j, cell_selec_supp_col_index))
                                    selec_supp = self.replace_special_chars(selec_supp)
                                    selec_supps.append(selec_supp)

                                # ON/OFF 設定表示 可能包含多行记录
                                on_off_cell_1, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_cell_index))
                                on_off_cell_2, _, _ = self.extract_format_value(origin_sheet.Cells(j, on_off_cell_index + 1))
                                if on_off_cell_2 is not None and on_off_cell_2 != '-':
                                    # 如果 ON/OFF表示 列为有效值（非图片）
                                    if on_off_cell_1 is not None and on_off_cell_1 != '-':
                                        on_offs.append([on_off_cell_1, on_off_cell_2])
                                    else:
                                        # ON/OFF表示列 有可能为'-' 或者是一张图片
                                        on_offs.append(["", on_off_cell_2])

                            cntt_list[-1].opt_selec_supplement.append(selec_supps)
                            cntt_list[-1].on_off.append(on_offs)

                            # 走行中操作可能
                            if resolve_sheet.cell(cell.row, running_operate_allow_col_index).value is not None:
                                spdjudge_sts, _, _ = self.extract_format_value(origin_sheet.Cells(cell.row, running_operate_allow_col_index))
                                spdjudge_sts = spdjudge_sts.split('\n')
                                cntt_list[-1].SpdEnblJdg.append(spdjudge_sts[-1])
                            else:
                                cntt_list[-1].SpdEnblJdg.append(cntt_list[-1].SpdEnblJdg[0])

                            # 切替type
                            if resolve_sheet.cell(cell.row, opt_type_col_index).value is not None:
                                cntt_list[-1].opttype.append(self.replacechar(opt_type, '\n'))
                            else:
                                if cntt_list[-1].opttype:
                                    cntt_list[-1].opttype.append(self.replacechar(cntt_list[-1].opttype[-1], '\n'))
                                else:
                                    cntt_list[-1].opttype.append(self.replacechar('-', '\n'))

                            # 关联式样
                            if resolve_sheet.cell(cell.row, linked_rol_index).value not in [None, '-']:
                                linked_excel_name, _, _ = self.extract_format_value(
                                    origin_sheet.Cells(cell.row, linked_rol_index)
                                )
                                cntt_list[-1].linked_excel_name.append(linked_excel_name.split('\n'))
                            elif resolve_sheet.cell(cell.row, linked_rol_index).value in [None, '-'] and cntt_list[-1].linked_excel_name:
                                # 如果当前单元格没有关联式样，但是上一个选项有关联式样，则使用上一个选项的关联式样
                                cntt_list[-1].linked_excel_name.append(cntt_list[-1].linked_excel_name[-1])
                            else:
                                cntt_list[-1].linked_excel_name.append(None)

        # 计算当前画面的上一阶层 画面名称及选项名称
        for i in range(1, len(cntt_list)):
            for j in range(0, len(cntt_list)):
                for k in range(0, len(cntt_list[j].optname)):
                    try:
                        if (cntt_list[i].cstm_level - 1 == cntt_list[j].cstm_level) and (
                                cntt_list[j].optcoordinate[0][k] <= cntt_list[i].optcoordinate[0][0] <=
                                cntt_list[j].optcoordinate[1][k]):
                            cntt_list[i].cntt_pre = cntt_list[j].cnttname
                            cntt_list[i].opt_pre = cntt_list[j].optname[k]
                    except Exception as e:
                        traceback.print_exc()
        # 遍历除0阶层所有画面所有选项，查找对应的下阶层画面
        for i in range(1, len(cntt_list)):
            self.lookup_nextcntt(i, cntt_list[i].cstm_level, cntt_list, resolve_sheet, req_file_meta, index_dict)

    def trans_obj_to_json(self, cntt_list):
        """
        将Cstmcnttinfo对象列表转换为JSON格式的数据结构
        每个画面(cntt)包含其基本信息(名称、层级、坐标等)和选项列表
        选项列表中的每个选项包含其属性(名称、类型、坐标等)和子选项信息
        """
        json_list = []
        for i in cntt_list:
            cntt_name = i.cnttname.replace('"', '')
            # 画面的元数据信息
            json_data = {
                "cntt_name": cntt_name,
                "uuid": i.uuid,
                "cntt_pre": i.cntt_pre.replace('"', ''),  # 上阶层画面
                "opt_pre": i.opt_pre.replace('"', ''),  # 上阶层选项
                "cstm_level": i.cstm_level,  # 画面阶层,
                "coordinate": i.coordinate,  # 画面单元格定位
                "col_range": i.col_range,   # 画面的列范围
                "row_range": i.row_range,   # 画面的行范围
                "next_cntt": [i.replace('"', '') for i in i.nextcntt if i != '-'],  # 下阶层画面信息
                'opt_names': [], #画面包含的选项信息
                'cntt_name_updated_flag': i.cntt_name_updated_flag, #画面名称是否存在删除线 被修改
                'bf_cntt_name': i.cnttname_bf.replace('"', '') #画面名称变更前的名称
            }
            # 画面的选项信息
            for opt_index in range(0, len(i.optname)):
                is_parent = True if i.optname[opt_index] in i.nextcntt else False
                if is_parent:
                    opt_cnnt = list(
                        filter(lambda x: x.cnttname.replace('"', '') == i.optname[opt_index].replace('"', ''), cntt_list))
                else:
                    opt_cnnt = None
                json_data['opt_names'].append({
                    "opt_index": opt_index + 1,
                    "opt_name": i.optname[opt_index].replace('"', ''),
                    "opt_name_updated_flag": i.opt_name_updated_flag[opt_index], #选项名称是否存在删除线 被修改
                    "bf_opt_name": i.optname_bf[opt_index].replace('"', ''), #选项名称变更前的名称
                    "belong_cntt": cntt_name,
                    "on_off": i.on_off[opt_index] if i.on_off[opt_index] else None,
                    "opt_select_supplement": i.opt_selec_supplement[opt_index] if i.opt_selec_supplement[
                        opt_index] else None,
                    "opt_type": i.opttype[opt_index],  # 切替类型
                    "spd_enbl_jdg": i.SpdEnblJdg[opt_index],  # 走行可否
                    "linked_excel": i.linked_excel_name[opt_index],  # 关联式样名称
                    "start_row": i.optcoordinate[0][opt_index],  # 选项开始行
                    "end_row": i.optcoordinate[1][opt_index],  # 选项结束行
                    "coordinate": i.opt_coordinates[opt_index],
                    "cstm_level": i.cstm_level,  # 画面阶层
                    "is_parent": is_parent,
                    "next_opt_names": [i.replace('"', '') for i in opt_cnnt[0].optname if i != '-'] if is_parent and opt_cnnt else None
                })
                if cntt_name == 'Menu':
                    # 只有Menu中的选项有enter_sw
                    json_data['opt_names'][-1]['enter_sw'] = i.enter_sw[opt_index]  # 有効Enter-SW操作

            json_list.append(json_data)
        return json_list

    def get_linked_col_idx(self, req_file_meta, index_dict):
        """
        获取关联式样列的索引
        :param req_file_meta:
        :param index_dict: 原始式样书表头信息
        :return:
        """
        associated_sample_book_col_name_list = ['適用', '19PFver3適用仕様書']
        col_name_set = set(associated_sample_book_col_name_list)
        # 遍历字典的所有键值对
        rol_index = req_file_meta.end_column - 4
        for key, value in index_dict.items():
            # 确保键是元组类型且长度为2
            if isinstance(key, tuple) and len(key) == 2:
                n1, n2 = key
                # 检查元组元素是否相等且元素在列名列表中
                if n1 == n2 and n1 in col_name_set:
                    rol_index = value
                    break
        return rol_index

    def get_column_idx(self, level_id, col_name, index_dict):
        """
        根据阶层和列名获取列索引
        :param level_id:
        :param col_name:
        :param index_dict:
        :return:
        """
        level_name = self.find_keys_by_value(self.LEVEL_MAP_SAMPLE_BOOK, str(level_id))
        if level_name:
            level_name = level_name[0]
        return index_dict.get((level_name, col_name))


    def extract_format_value(self, cell, need_bf=False):
        """
        从单元格中提取未被删除线格式化的字符。

        遍历单元格中的每个字符，检查其字体是否设置了删除线格式。如果没有设置删除线，
        则将该字符添加到结果字符串中。此函数用于处理那些可能被部分格式化为删除线的字符串，
        以便后续处理或显示。

        参数:
        cell - 单元格对象，包含要提取的文本值和格式信息。
        need_bf - 是否需要变更前的值

        返回:
        format_value - 不包含删除线格式字符的字符串。
        """
        if cell is None:
            return None, False, ''
        # 获取单元格的值
        value = cell.Value
        # 如果值为空，则返回空字符串
        if value is None:
            return "", False, ''
        # 将单元格的值转换为字符串
        value_str = str(value)
        # 初始化用于存储非删除线字符的字符串
        format_value = ""
        # 遍历字符串中的每个字符
        has_strikethrough = False
        bf_val = ""
        for i in range(1, len(value_str) + 1):
            # 获取当前字符的字体属性
            char_font = cell.GetCharacters(i, 1).Font
            # 如果当前字符的字体没有设置删除线格式，则将其添加到结果字符串中
            if not char_font.Strikethrough:
                format_value += value_str[i - 1]
            else:
                bf_val += value_str[i - 1]
            # 如果当前字符的字体设置了删除线格式，则记录下来
            if char_font.Strikethrough:
                has_strikethrough = True

        # 如果需要返回变更前的值
        return format_value, has_strikethrough, bf_val

    def replace_special_chars(self, raw_text):
        """
        处理字符串中的特殊字符
        :param raw_text:
        :return:
        """
        text = re.sub(r'[★"【】#0-9]', '', raw_text)
        text = " ".join(text.split('\n')).rstrip(' ')
        return text.replace('\u2010', '-').replace('\u2011', '-')


    def lookup_nextcntt(self, cntt_id, level_id, cntt_list, sheet, req_file_meta, index_dict):
        """
        解析下一阶层画面信息
        查找某一画面下的所有选项对应的下阶层画面，即nextcntt
        :param cntt_id:
        :param level_id:
        :param cntt_list:
        :param sheet:
        :return:
        """
        origin_sheet = req_file_meta.origin_sheet
        try:
            current_level_name = self.find_keys_by_value(self.LEVEL_MAP_SAMPLE_BOOK, str(level_id+1))
            if current_level_name:
                current_level_name = current_level_name[0]

            hierarchy_first_col_idx = index_dict.get((current_level_name, 'タイトル'))  # 当前阶层的第一列所在索引
            opt_name_format_col_index = index_dict.get((current_level_name, '項目'))  # 当前阶层的选项列索引

            if hierarchy_first_col_idx:
                for index in range(0, len(cntt_list[cntt_id].optname)):
                    for row in range(cntt_list[cntt_id].optcoordinate[0][index], cntt_list[cntt_id].optcoordinate[1][index] + 1):
                        # タイトル 列
                        value,_, _ = self.extract_format_value(origin_sheet.Cells(row, hierarchy_first_col_idx))
                        value = self.replace_special_chars(value)
                        # 項目 列
                        value2,_, _ = self.extract_format_value(origin_sheet.Cells(cntt_list[cntt_id].optcoordinate[0][index], opt_name_format_col_index))
                        value2 = self.replace_special_chars(value2)
                        if value is not None and value.strip()!="" and value != '-':
                            cntt_list[cntt_id].nextcntt.append(value)
                            break
                        elif value2 is not None and value2.strip()!="" and value2 != '-':
                            value3,_, _ = self.extract_format_value(origin_sheet.Cells(
                                                cntt_list[cntt_id].optcoordinate[0][0], hierarchy_first_col_idx
                                            ))
                            value3 = self.replace_special_chars(value3)
                            cntt_list[cntt_id].nextcntt.append(value3)
                            break
                        if row == cntt_list[cntt_id].optcoordinate[1][index]:
                            cntt_list[cntt_id].nextcntt.append('-')
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"解析下一阶层画面信息时发生异常：{str(e)}")

    def lookup_cntt_end_row(self, cell, cntt_list, sheet, req_file_meta):
        """
        寻找画面的结束行信息
        :param cell:
        :param cntt_list:
        :param sheet:
        :param req_file_meta:
        :return:
        """
        for index in range(cell.row + 1, req_file_meta.end_row + 1):
            if sheet.cell(index, cell.column).value is not None or \
                    str(sheet.cell(index, cell.column).fill.start_color.index) == '1' or \
                    sheet.cell(index,cell.column).value == '-':
                cntt_list[-1].row_range.append(index-1)
                break
            elif index == req_file_meta.end_row:
                cntt_list[-1].row_range.append(index)
                break


    def get_merged_cell_value(self, sheet_data, row, col, merged_map):
        """获取单元格值，正确处理合并单元格情况
        参数:
            sheet_data: openpyxl Worksheet对象
            row: 行号
            col: 列号
            merged_map: 合并单元格映射字典
        返回:
            str: 单元格值，如果为空则返回None
        """
        try:
            # 检查是否是合并单元格
            if (row, col) in merged_map:
                # 如果是合并单元格，获取主单元格的值
                main_row, main_col = merged_map[(row, col)]
                cell = sheet_data.cell(row=main_row, column=main_col)
            else:
                # 非合并单元格直接获取值
                cell = sheet_data.cell(row=row, column=col)

            # 获取单元格值并处理不同类型
            value = cell.value

            # 处理空值
            if value is None:
                return None

            # 转换为字符串并去除首尾空格
            if isinstance(value, str):
                return value.strip()
            elif isinstance(value, (int, float)):
                return str(value)
            elif isinstance(value, bool):
                return "True" if value else "False"
            else:
                return str(value)

        except Exception as e:
            print(f"获取单元格(R{row}C{col})值时出错: {str(e)}")
            return None

    def add_to_result_dict(self, result_dict, sequence_name, column_name, col_index):
        """将列信息添加到结果字典

        参数:
            result_dict: 结果字典
            sequence_name: 序列名称
            column_name: 列名称
            col_index: 列索引（从1开始的整数）

        返回:
            bool: 是否成功添加
        """
        try:
            # 创建键：元组(序列名称, 列名称)
            key = (sequence_name, column_name)

            # 处理可能的键重复（创建唯一键）
            counter = 1
            while key in result_dict:
                counter += 1
                key = (f"{sequence_name}_{counter}", column_name)

            # 直接存储列索引（从1开始的整数）
            result_dict[key] = col_index

            return True

        except Exception as e:
            print(f"添加列到字典时出错(列{col_index}): {str(e)}")
            return False

    # 添加缺失的常量和辅助方法
    LEVEL_MAP_SAMPLE_BOOK = {
        '第０階層': '0',
        '第一階層': '1',
        '第二階層': '2',
        '第三階層': '3',
        '第四階層': '4',
        '第五階層': '5',
        '第六階層': '6',
        '第七階層': '7'
    }

    def process_map_difference(self, map_dict: Dict[Tuple[Any, Any], Any], b_value: Any = None) -> int:
        """
        处理键为 (a, b) 形式的字典，计算每个 b 下不同 a 的排序值之间的差值

        参数:
            map_dict: 输入字典，键为 (a, b) 元组形式
            b_value: 指定特定的 b 值进行处理（可选）

        返回:
            Dict[Any, List[Any]]: 每个 b 对应的差值列表
        """
        # 步骤1: 按 b 分组 a 和值
        b_groups = defaultdict(dict)  # b_value: {a_value: mapped_value}

        # 迭代字典中的所有键值对
        for (a, b), value in map_dict.items():
            # 如果指定了特定的 b 值，只处理该值
            if b_value is not None and b != b_value:
                continue

            # 确保只存储一个 a 的最新值
            b_groups[b][a] = value

        # 步骤2: 对每个 b 组的值进行排序并计算差值
        result_dict = {}

        for b, a_map in b_groups.items():
            # 获取排序后的值列表
            sorted_vals = self.sort_by_value(a_map)

            # 计算差值
            diffs = self.calculate_differences(sorted_vals)

            # 添加到结果
            result_dict[b] = diffs

        if result_dict[b] is None or len(result_dict[b]) == 0:
            #默认按19项目的13来
            return 13

        return int(result_dict[b][-1])

    def find_keys_by_value(self, mapping_dict, target_value):
        """根据值查找所有匹配的键"""
        return [key for key, value in mapping_dict.items() if value == target_value]

    def sort_by_value(self, a_map: Dict[Any, Any]) -> List[Any]:
        """
        根据映射值(value)进行排序（不是按键排序）
        参数:
            a_map: 映射字典 {a: value}
        返回:
            按值(value)排序后的值列表（不是键列表）
        """
        # 创建一个列表，包含(值, 键)元组
        value_key_pairs = [(value, key) for key, value in a_map.items()]

        # 尝试按数值类型排序
        try:
            # 使用数值排序
            sorted_pairs = sorted(
                value_key_pairs,
                key=lambda x: float(x[0]) if x[0] is not None else float('inf')
            )

            # 只返回排序后的值列表
            return [value for value, key in sorted_pairs]

        except (ValueError, TypeError):
            # 无法数值排序时使用字符串排序
            try:
                # 尝试转换为字符串排序
                sorted_pairs = sorted(
                    value_key_pairs,
                    key=lambda x: str(x[0]) if x[0] is not None else ''
                )
                return [value for value, key in sorted_pairs]

            except Exception:
                # 最终回退方案：按原始值排序
                sorted_vals = sorted(a_map.values(), key=lambda x: (x is None, str(x)))
                return sorted_vals

    def calculate_differences(self, values: List[Any]) -> List[Any]:
        """
        计算列表中相邻元素的差值

        自动处理各种数据类型（数值、日期等）
        """
        diffs = []

        for i in range(1, len(values)):
            # 尝试数值减法
            try:
                diffs.append(float(values[i]) - float(values[i - 1]))
            except (TypeError, ValueError):
                # 非数值类型处理：时间差或直接表示差值
                try:
                    # 尝试时间差计算（如果值是 datetime 对象）
                    if hasattr(values[i], '__sub__'):
                        diffs.append(values[i] - values[i - 1])
                    else:
                        # 其他类型生成差值描述
                        diffs.append(f"{values[i]} minus {values[i - 1]}")
                except Exception:
                    # 最终回退方案
                    diffs.append(f"Difference between position {i} and {i - 1}")

        return diffs

    def replacechar(self, name, strname):
        """工具函数  将name 中的子字符串替换成''"""
        if strname in name:
            name = name.replace(strname, "")
        return name

    def lookup_optcoordinate(self, cell, cntt_list, sheet, req_file_meta, level_id, index_dict):
        """
        选项在式样书中的起始/结束行计算
        :param cell:
        :param cntt_list:
        :param sheet:
        :return:
        """
        cntt_list[-1].optcoordinate[0].append(cell.row)

        current_level_name = self.find_keys_by_value(self.LEVEL_MAP_SAMPLE_BOOK, str(level_id))
        if current_level_name:
            current_level_name = current_level_name[0]
        opt_name_format_col_index = index_dict.get((current_level_name, '項目'))  # 当前阶层的选项列索引

        for index in range(cell.row + 1, req_file_meta.end_row + 2):
            if sheet.cell(index, opt_name_format_col_index).value is not None or str(
                    sheet.cell(index, opt_name_format_col_index).fill.start_color.index) == '1' or sheet.cell(index,
                                                                                                    cell.column).value == '-':
                cntt_list[-1].optcoordinate[1].append(index - 1)
                break
            elif index == req_file_meta.end_row+1:
                cntt_list[-1].optcoordinate[1].append(index - 1)
                break

    def get_merged_cell_value_by_all_sheet(self, ws, row, col):
        """
        获取合并单元格的值，输入为行列数字（从1开始）
        使用缓存机制提高频繁调用时的性能

        参数:
            ws: Worksheet对象，要操作的工作表
            row: 行号（整数，从1开始）
            col: 列号（整数，从1开始）

        返回:
            合并单元格的值（左上角单元格的值），如果不是合并单元格则返回该单元格的值
        """
        # 检查缓存中是否已有该工作表的合并单元格映射
        if not hasattr(self, '_merged_cells_cache') or not self._merged_cells_cache:
            self._merged_cells_cache = self._build_merged_cells_map(ws)

        merged_map = self._merged_cells_cache

        # 检查当前单元格是否在合并单元格映射中
        cell_key = (row, col)
        if cell_key in merged_map:
            # 获取合并区域左上角坐标
            return merged_map[cell_key]

        # 如果不是合并单元格，直接返回当前单元格的值
        return ws.cell(row, col).value

    def _build_merged_cells_map(self, ws):
        """构建合并单元格映射"""
        merged_map = {}
        for merged_range in ws.merged_cells.ranges:
            min_row, min_col, max_row, max_col = merged_range.bounds
            value = ws.cell(min_row, min_col).value
            for r in range(min_row, max_row + 1):
                for c in range(min_col, max_col + 1):
                    merged_map[(r, c)] = value
        return merged_map


    def _process_columns(self, sheet_data, sequence_row, merged_map):
        """处理所有列数据并生成结果字典
        参数:
            sheet_data: openpyxl Worksheet对象
            sequence_row: "表示顺序"所在的行号
            merged_map: 合并单元格映射字典
        返回:
            dict: 包含{(序列名称, 列名称): 列字母}映射的字典
        """
        result_dict = {}

        # 有效列计数和跳过计数
        valid_count = 0
        skipped_count = 0

        # 遍历所有列
        for col_index in range(1, sheet_data.max_column + 1):
            # 获取序列名称（从sequence_row行）
            sequence_name = self.get_merged_cell_value(
                sheet_data, sequence_row, col_index, merged_map
            )

            # 获取列名称（从sequence_row+1行）
            column_name = self.get_merged_cell_value(
                sheet_data, sequence_row + 1, col_index, merged_map
            )

            # 跳过无效列名称
            if not column_name or len(column_name.strip()) == 0:
                skipped_count += 1
                continue

            # 设置默认序列名称（如果为空）
            if not sequence_name or len(sequence_name.strip()) == 0:
                sequence_name = "表示顺序"

            # 创建唯一键并添加到字典
            if not self.add_to_result_dict(result_dict, sequence_name, column_name, col_index):
                skipped_count += 1

        # 打印处理结果统计
        print(f"已处理 {valid_count} 个有效列，跳过 {skipped_count} 个无效列")

        return result_dict