import os
import logging
from typing import Any, Dict, List, Optional, Union

from docparser.core.parser_factory import ParserFactory
from docparser.core.plugin_manager import PluginManager
from docparser.core.rule_engine import RuleEngine
from docparser.models.document_objects import Document
from docparser.models.document import DocumentObject
from docparser.models.text import TextObject
from docparser.parsers.excel.excel import ExcelParser
from docparser.parsers.word.word_parser import WordParser
from docparser.utils.error_handler import (
    handle_error, try_except_decorator, 
    DocumentNotFoundError, UnsupportedDocumentTypeError, 
    ParsingError, ExportError
)

# Configure logging
logger = logging.getLogger('docparser')

class DocumentManager:
    """
    Main class for document parsing operations.
    Serves as the primary entry point for the document parsing tool.
    """
    
    def __init__(self, plugins_dir: Optional[str] = None, rules_dir: Optional[str] = None):
        """
        Initialize document manager.
        
        Args:
            plugins_dir: Directory containing plugins (optional)
            rules_dir: Directory containing rules (optional)
        """
        self.parser_factory = ParserFactory
        self.plugin_manager = PluginManager()
        self.rule_engine = RuleEngine()
        self.document: DocumentObject = DocumentObject()
        
        # Load plugins if directory provided
        if plugins_dir:
            self.load_plugins(plugins_dir)
        
        # Load rules if directory provided
        if rules_dir:
            self.load_rules(rules_dir)

        self._registry_default_parser()
    
    def load_plugins(self, plugins_dir: str) -> None:
        """
        Load plugins from directory.
        
        Args:
            plugins_dir: Directory containing plugins
        """
        self.plugin_manager.discover_plugins(plugins_dir)
        logger.info(f"Loaded {len(self.plugin_manager.get_plugins())} plugins")
    
    def register_plugin(self, plugin) -> None:
        """
        Register a plugin.
        
        Args:
            plugin: Plugin instance
        """
        self.plugin_manager.register_plugin(plugin)
    
    def load_rules(self, rules_dir: str) -> None:
        """
        Load rules from directory.
        
        Args:
            rules_dir: Directory containing rules
        """
        # Load Python rules
        rules_loaded = self.rule_engine.load_rules_from_directory(rules_dir)
        
        # Look for JSON rules
        json_files = [f for f in os.listdir(rules_dir) if f.endswith('.json')]
        for json_file in json_files:
            json_path = os.path.join(rules_dir, json_file)
            rules_loaded += self.rule_engine.load_rules_from_json_file(json_path)
        
        logger.info(f"Loaded {rules_loaded} rules")
    
    def register_rule(self, rule) -> None:
        """
        Register a rule.
        
        Args:
            rule: Rule instance
        """
        self.rule_engine.register_rule(rule)
    
    @handle_error
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """
        Parse a document.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing parsed document data
            
        Raises:
            DocumentNotFoundError: If the file is not found
            UnsupportedDocumentTypeError: If the file type is not supported
            ParsingError: If there is an error parsing the document
        """
        if not os.path.exists(file_path):
            raise DocumentNotFoundError(f"File not found: {file_path}")
            
        # Create parser for file type
        parser = self.parser_factory.create_parser(file_path)
        
        # Register plugins with parser
        for plugin in self.plugin_manager.get_plugins():
            parser.register_plugin(plugin)
        
        # Parse document
        self.document = parser.parse_document(file_path)
        document_data = self.document.to_dict()

        # Apply rules to document
        if self.rule_engine.get_all_rules():
            logger.info("Applying rules to document")
            processed_data = self.rule_engine.apply_rules_to_document(document_data)
            # Update document with processed data
            self.document = DocumentObject.from_dict(processed_data)


        if self.plugin_manager.get_plugins():
            self.plugin_manager.apply_plugins_to_document(self.document)
        
        # # Store document
        # self.document = Document.from_dict(document_data)
        
        return self.document.to_output_dict()

    @handle_error
    def parse_document_object(self, file_path: str) -> 'DocumentObject':
        """
        Parse a document.

        Args:
            file_path: Path to the document file

        Returns:
            DocumentObject

        Raises:
            DocumentNotFoundError: If the file is not found
            UnsupportedDocumentTypeError: If the file type is not supported
            ParsingError: If there is an error parsing the document
        """
        if not os.path.exists(file_path):
            raise DocumentNotFoundError(f"File not found: {file_path}")

        # Create parser for file type
        parser = self.parser_factory.create_parser(file_path)

        # Register plugins with parser
        for plugin in self.plugin_manager.get_plugins():
            parser.register_plugin(plugin)

        # Parse document
        self.document = parser.parse_document(file_path)
        document_data = self.document.to_dict()

        # Apply rules to document
        if self.rule_engine.get_all_rules():
            logger.info("Applying rules to document")
            processed_data = self.rule_engine.apply_rules_to_document(document_data)
            # Update document with processed data
            self.document = DocumentObject.from_dict(processed_data)

        if self.plugin_manager.get_plugins():
            self.plugin_manager.apply_plugins_to_document(self.document)

        # # Store document
        # self.document = Document.from_dict(document_data)

        return self.document

    def get_text_objects(self) -> List[Dict[str, Any]]:
        """
        Get all text objects from the parsed document.
        
        Returns:
            List of text objects with their properties
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        text_objects = [obj.to_dict() for obj in self.document.text_objects]
        
        # Apply rules to text objects if needed
        if self.rule_engine.get_rules_by_target('text'):
            logger.debug("Applying rules to text objects")
            text_objects = self.rule_engine.apply_rules_to_objects(text_objects, 'text')
        
        return text_objects
    
    def get_table_objects(self) -> List[Dict[str, Any]]:
        """
        Get all table objects from the parsed document.
        
        Returns:
            List of table objects with their properties
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        table_objects = [obj.to_dict() for obj in self.document.table_objects]
        
        # Apply rules to table objects if needed
        if self.rule_engine.get_rules_by_target('table'):
            logger.debug("Applying rules to table objects")
            table_objects = self.rule_engine.apply_rules_to_objects(table_objects, 'table')
        
        return table_objects
    
    def get_picture_objects(self) -> List[Dict[str, Any]]:
        """
        Get all picture objects from the parsed document.
        
        Returns:
            List of picture objects with their properties
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        picture_objects = [obj.to_dict() for obj in self.document.picture_objects]
        
        # Apply rules to picture objects if needed
        if self.rule_engine.get_rules_by_target('picture'):
            logger.debug("Applying rules to picture objects")
            picture_objects = self.rule_engine.apply_rules_to_objects(picture_objects, 'picture')
        
        return picture_objects
    
    def get_graphic_objects(self) -> List[Dict[str, Any]]:
        """
        Get all graphic objects from the parsed document.
        
        Returns:
            List of graphic objects with their properties
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        graphic_objects = [obj.to_dict() for obj in self.document.graphic_objects]
        
        # Apply rules to graphic objects if needed
        if self.rule_engine.get_rules_by_target('graphic'):
            logger.debug("Applying rules to graphic objects")
            graphic_objects = self.rule_engine.apply_rules_to_objects(graphic_objects, 'graphic')
        
        return graphic_objects
    
    @try_except_decorator(1400)  # ExportError
    def export_to_json(self, output_path: str) -> None:
        """
        Export parsed document data to JSON format.
        
        Args:
            output_path: Path to save the JSON output
            
        Raises:
            ValueError: If no document has been parsed
            ExportError: If there is an error exporting the document
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        # Create directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        self.document.save_to_json(output_path)
        logger.info(f"Document exported to JSON: {output_path}")
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported file extensions
        """
        return self.parser_factory.get_supported_extensions()
    
    def get_document_metadata(self) -> Dict[str, Any]:
        """
        Get document metadata.
        
        Returns:
            Dictionary containing document metadata
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")
        
        return self.document.metadata
    
    def get_document_statistics(self) -> List[Dict[str, int]]:
        """
        Get document statistics.
        
        Returns:
            Dictionary containing document statistics
        """
        if not self.document:
            raise ValueError("No document parsed. Call parse_document() first.")

        res = []
        for doc in self.document.document:
            res.append({
            'page_count': doc.page_count,
            'text_count': len(doc.text_objects),
            'table_count': len(doc.table_objects),
            'picture_count': len(doc.picture_objects),
            'graphic_count': len(doc.graphic_objects),
            'total_objects': (len(doc.text_objects) +
                             len(doc.table_objects) +
                             len(doc.picture_objects) +
                             len(doc.graphic_objects))
        })
        
        return res

    def _registry_default_parser(self):
        try:
            self.parser_factory.register_parser(WordParser)
            self.parser_factory.register_parser(ExcelParser)
            logger.info("Default parsers registered successfully")
        except Exception as e:
            logger.error(f"Error registering default parsers: {e}")
            raise