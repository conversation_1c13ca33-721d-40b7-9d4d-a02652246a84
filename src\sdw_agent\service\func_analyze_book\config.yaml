# 函数分析书生成工作流配置
# Function Analysis Book Generation Workflow Configuration

# 基本信息
name: "函数分析书生成工作流"
description: "基于Git提交生成函数变更分析书的工作流服务"
version: "1.0.0"
author: "SDW-Team"

# V字对应
v_model:
  stage: "V2.2"
  phase: "详细设计"

# 处理参数
processing:
  # Git相关配置
  git:
    diff_timeout: 30  # Git差异分析超时时间（秒）
    max_diff_size: 10485760  # 最大差异文件大小（10MB）
    ignore_whitespace: true  # 是否忽略空白字符差异
    context_lines: 3  # 上下文行数
    
  # LLM相关配置
  llm:
    max_workers: 5  # 并行处理的最大线程数
    timeout: 30  # LLM调用超时时间（秒）
    retry_attempts: 3  # 重试次数
    retry_delay: 1  # 重试延迟（秒）
    max_tokens: 4096  # 最大token数
    temperature: 0.1  # 温度参数
    
  # 函数提取配置
  function:
    extract_timeout: 10  # 函数提取超时时间（秒）
    max_function_size: 1000  # 最大函数行数
    min_function_size: 3  # 最小函数行数
    include_static: true  # 是否包含静态函数
    include_inline: false  # 是否包含内联函数
    
  # 文件处理配置
  file:
    supported_extensions: [".c", ".cpp", ".h", ".hpp"]  # 支持的文件扩展名
    exclude_patterns:  # 排除的文件模式
      - "*/test/*"
      - "*/tests/*"
      - "*_test.c"
      - "*_test.cpp"
    max_file_size: 5242880  # 最大文件大小（5MB）
    encoding: "utf-8"  # 文件编码
    
  # 验证配置
  validation:
    check_git_repo: true  # 是否检查Git仓库有效性
    check_commit_exists: true  # 是否检查提交ID存在性
    check_file_exists: true  # 是否检查文件存在性
    validate_excel_format: true  # 是否验证Excel格式

# 组件配置
component:
  # 组件列表Excel配置
  excel:
    sheet_name: "SW-C List_R-Car"  # 组件列表工作表名称
    header_row: 1  # 表头行号
    data_start_row: 2  # 数据开始行号
    
  # 列映射配置
  column_mapping:
    component_name: "Component Name"  # 组件名称列
    domain_name: "Domain Name"  # 域名称列
    file_path: "File Path"  # 文件路径列
    description: "Description"  # 描述列
    
  # 组件匹配配置
  matching:
    fuzzy_threshold: 0.8  # 模糊匹配阈值
    case_sensitive: false  # 是否区分大小写
    exact_match_priority: true  # 精确匹配优先

# 函数分析配置
function_analysis:
  # 变更类型分类
  change_types:
    - "新增"  # 新增函数
    - "修改"  # 修改函数
    - "删除"  # 删除函数
    - "移动"  # 移动函数
    
  # 分析维度
  analysis_dimensions:
    - "功能描述"  # 函数功能描述
    - "参数变更"  # 参数变更分析
    - "返回值变更"  # 返回值变更分析
    - "逻辑变更"  # 逻辑变更分析
    - "性能影响"  # 性能影响分析
    
  # 描述生成配置
  description:
    max_length: 200  # 最大描述长度
    min_length: 20  # 最小描述长度
    language: "中文"  # 描述语言
    include_technical_terms: true  # 是否包含技术术语
    
  # 变更影响分析
  impact_analysis:
    analyze_dependencies: true  # 是否分析依赖关系
    analyze_callers: true  # 是否分析调用者
    analyze_callees: true  # 是否分析被调用者
    max_depth: 3  # 最大分析深度

# Excel输出配置
excel_output:
  # 模板配置
  template:
    file_path: "templates/func_book_sample.xlsx"  # 模板文件路径
    sheet_name: "函数分析"  # 工作表名称
    
  # 数据写入配置
  data_writing:
    start_row: 3  # 数据开始行
    start_column: 1  # 数据开始列
    header_style: true  # 是否应用表头样式
    auto_fit_columns: true  # 是否自动调整列宽
    
  # 列配置
  columns:
    - name: "序号"
      width: 8
      alignment: "center"
    - name: "组件名称"
      width: 20
      alignment: "left"
    - name: "文件名"
      width: 25
      alignment: "left"
    - name: "函数名"
      width: 30
      alignment: "left"
    - name: "变更类型"
      width: 12
      alignment: "center"
    - name: "函数描述"
      width: 40
      alignment: "left"
    - name: "变更内容"
      width: 50
      alignment: "left"
    - name: "影响分析"
      width: 30
      alignment: "left"
      
  # 样式配置
  styles:
    header:
      font_bold: true
      background_color: "D9E1F2"
      border: true
    data:
      font_size: 10
      wrap_text: true
      border: true
    change_type_colors:
      "新增": "C6EFCE"  # 绿色
      "修改": "FFEB9C"  # 黄色
      "删除": "FFC7CE"  # 红色
      "移动": "E1D5E7"  # 紫色

# 输出配置
output:
  # 默认输出目录
  default_output_dir: "C:\\sdw_output"
  
  # 文件命名规则
  file_naming:
    prefix: "函数分析书"  # 文件前缀
    include_timestamp: true  # 是否包含时间戳
    include_commit_id: true  # 是否包含提交ID
    timestamp_format: "%Y%m%d_%H%M%S"  # 时间戳格式
    
  # 文件格式配置
  formats:
    excel: true  # 是否生成Excel文件
    json: false  # 是否生成JSON文件
    csv: false  # 是否生成CSV文件
    
  # 压缩配置
  compression:
    enabled: false  # 是否启用压缩
    format: "zip"  # 压缩格式
    level: 6  # 压缩级别

# Gerrit集成配置
gerrit:
  # 连接配置
  connection:
    timeout: 30  # 连接超时时间（秒）
    verify_ssl: true  # 是否验证SSL证书
    max_retries: 3  # 最大重试次数
    
  # API配置
  api:
    version: "3.1"  # API版本
    change_detail_options:
      - "CURRENT_REVISION"
      - "CURRENT_FILES"
      - "DETAILED_ACCOUNTS"
    
  # 认证配置
  auth:
    method: "basic"  # 认证方式
    store_credentials: false  # 是否存储凭据


plant_uml:
  post_url: "http://************:9001/form"