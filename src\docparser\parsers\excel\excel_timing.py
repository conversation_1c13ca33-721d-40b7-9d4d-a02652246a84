# -*- coding: utf-8 -*-
"""
@File    : parse_exec_by_config.py
<AUTHOR> 董根虎
@Date    : 2024-07-08 09:18
@Desc    : 时序图 excel解析差分

"""
import logging
import os
import pythoncom
import pywintypes

from docparser.common.base import is_merged
from docparser.models.document import DocumentBlockObject
from docparser.models.text import TextObject
from docparser.models.timing import TimingWaveObject, TimingDataObject, TimingTextObject, TimingTextDataObject
from docparser.parsers.excel.excel_standard import ExcelStandardParser
from docparser.parsers.excel.utils import get_cell_value
from xlwings.utils import column_to_number

class ExcelTimingParser(ExcelStandardParser):

    def __init__(self):
        super().__init__()
        self._sheet_name = "仕様"  # 需要解析的sheet名称（固定）, 以及表格的开始标记
        self._identify_table_border_style = ['thin', 'thick', 'medium']

    def _parse_shapes(self, file_path, exclude_type_list=[]):
        """ 解析图形 """
        return super()._parse_shapes(file_path, [])

    def _parse_sheet(self, sheet, shapes):
        """ 解析一个worksheet中的数据
            :params sheet: Worksheet对象
            :params shapes: shape字典列表
        """
        try:
            excel_app = self.get_excel_app()
            wb_with_pywin32 = excel_app.Workbooks.Open(self._file_path)
            ws_with_pywin32 = wb_with_pywin32.Sheets[sheet.title]
        except (pythoncom.com_error, pywintypes.com_error):
            logging.error("操作office进程错误，请关闭office相关进程，重试")
            return DocumentBlockObject()
        sheet_name = sheet.title
        logging.info(f"parse_excel start sheet_name: {sheet_name}")
        if sheet_name != self._sheet_name:
            # return super()._parse_sheet(sheet, shapes)
            return None
        # 实例化一个sheet对象
        block = DocumentBlockObject()
        block.file_name = os.path.basename(self._file_path)
        # 时序图对象数据列表 + 对应的数据纵向范围
        timing_wave, tim_wave_ranges = self._parse_timing_wave_line(sheet, block, ws_with_pywin32)
        timing_text, tim_text_ranges = self._parse_timing_text_line(sheet, block, ws_with_pywin32)
        text_ranges = self._get_text_ranges(sheet, tim_wave_ranges + tim_text_ranges)
        # 解析当前sheet的图片信息
        pictures = self._parse_picture(sheet_name, block, shapes)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_picture {len(pictures)}")
        # 解析当前sheet的图形信息
        graphics = self._parse_graphic(sheet_name, block, shapes)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_graphic {len(graphics)}")
        # 解析当前sheet的表格外的对象信息
        texts = self._parse_text_single_cell(sheet, block, [], text_ranges, ws_with_pywin32)
        logging.info(f"parse_excel sheet_name: {sheet_name} _parse_text {len(texts)}")

        block._name = sheet_name
        block.add_text(texts)
        block.add_timing_wave(timing_wave)
        block.add_timing_text(timing_text)
        block.add_graphic(graphics)
        block.add_picture(pictures)
        self._assign_ref(block)
        logging.info(f"parse_excel end sheet_name: {sheet_name}")
        return block

    def _parse_timing_wave_line(self, sheet, block, ws_with_pywin32):
        """
        时序图对象数据 + 对应的数据范围
        """
        tim_ranges = []
        tims = []
        # 先查找所有的关键标记单元格
        marker_cells = []
        for idx, row in enumerate(sheet.iter_rows(values_only=False), 1):
            cell = sheet.cell(idx, 2)
            if self._is_wave_start_marker(cell, sheet):
                marker_cells.append((cell.row, cell.column))

        logging.info(f"parse_timing_wave_line marker_cells : {marker_cells}")
        max_column = sheet.max_column
        for m in marker_cells:
            # 一个marker标记一个TimingWaveObject对象
            tim = TimingWaveObject()
            tims.append(tim)
            tim.layout.parent_ref = block
            marker_row, marker_col = m
            tim_ranges.append(((marker_row - 5, max_column), (marker_row, max_column)))
            tim.one_mean = get_cell_value(sheet.cell(marker_row - 2, marker_col - 1), sheet, False)
            tim.zero_mean = get_cell_value(sheet.cell(marker_row, marker_col - 1), sheet, False)
            # 通过标记单元格计算出每行的范围的文本数据
            for row in range(marker_row - 5, marker_row + 1):
                cell_ = sheet.cell(row, 1)
                v = get_cell_value(cell_, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(cell_, ws_with_pywin32)
                    # 构建文本对象
                    t = TextObject()
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = cell_.coordinate
                    tim.content.append(t)
            if tim.content:
                tim.name = tim.content[0].text
                tim.coordinate.desc = tim.content[0].coordinate.desc

            # 通过标记单元格计算出每行的时序图数据
            for col in range(2, max_column + 1):
                cell = sheet.cell(marker_row, col)
                bottom_style_ = cell.border.bottom.style
                if not bottom_style_:
                    bottom_style_ = sheet.cell(cell.row + 1, cell.column).border.top.style

                cell = sheet.cell(marker_row - 2, col)
                top_style_ = cell.border.top.style
                if not top_style_ and cell.row > 1:
                    top_style_ = sheet.cell(cell.row - 1, cell.column).border.bottom.style

                td = TimingDataObject()
                td.layout.parent_ref = tim
                td.coordinate.desc = cell.coordinate  # 坐标可能不准确
                td.index = col - 2
                if bottom_style_ in self._identify_table_border_style:
                    td.data = 0
                if top_style_ in self._identify_table_border_style:
                    td.data = 1
                tim.data.append(td)
        return tims, tim_ranges

    def _parse_timing_text_line(self, sheet, block, ws_with_pywin32):
        """
        时序图对象数据 + 对应的数据范围
        """
        tim_ranges = []
        tims = []
        # 先查找所有的关键标记单元格
        marker_cells = []
        for idx, row in enumerate(sheet.iter_rows(values_only=False), 1):
            cell = sheet.cell(idx, 2)
            if self._is_text_start_marker(cell, sheet):
                marker_cells.append((cell.row, cell.column))

        logging.info(f"parse_timing_text_line marker_cells : {marker_cells}")
        max_row = sheet.max_row
        max_column = sheet.max_column
        for m in marker_cells:
            # 一个marker标记一个TimingTextObject对象
            tim = TimingTextObject()
            tims.append(tim)
            tim.layout.parent_ref = block
            marker_row, marker_col = m
            tim_ranges.append(((marker_row, max_column), (marker_row, max_column)))
            # 通过标记单元格计算出每行的范围的文本数据
            for col in range(marker_col, max_column + 1):
                cell_ = sheet.cell(marker_row, col)
                v = get_cell_value(cell_, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(cell_, ws_with_pywin32)
                    # 构建文本对象
                    t = TimingTextDataObject()
                    if sheet.merged_cells:
                        for r in sheet.merged_cells.ranges:
                            if r.min_row <= cell_.row <= r.max_row and r.min_col <= cell_.column <= r.max_col:
                                t.length = r.max_col - r.min_col + 1
                                break
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = cell_.coordinate
                    tim.data.append(t)
            # 设置唯一标识： 取值第一列的内容
            text_key_cell = sheet.cell(marker_row, 1)
            tim.name = get_cell_value(text_key_cell, sheet, False)
            tim.coordinate.desc = text_key_cell.coordinate
            if not tim.name and is_merged(sheet.cell(marker_row, marker_col), sheet):
                # 合并单元格的情况，则根据合并的范围遍历取值
                for r in sheet.merged_cells.ranges:
                    if r.min_row <= marker_row <= r.max_row and r.min_col <= marker_col <= r.max_col:
                        for row in range(r.min_row, r.max_row):
                            text_key_cell = sheet.cell(row, 1)
                            v = get_cell_value(text_key_cell, sheet, False)
                            if v:
                                tim.name = v
                                tim.coordinate.desc = text_key_cell.coordinate
                                break
                    if tim.name:
                        break
            if text_key_cell:
                v = get_cell_value(text_key_cell, sheet, False)
                if v:
                    # excel中的单元格文本组成
                    run_objs = self._get_text_cell(text_key_cell, ws_with_pywin32)
                    # 构建文本对象
                    t = TextObject()
                    t._text = v
                    t._style = run_objs[0].style if run_objs else None
                    t._runs = run_objs
                    t.coordinate.desc = text_key_cell.coordinate
                    tim.content.append(t)
        return tims, tim_ranges

    def _get_text_ranges(self, sheet, timing_ranges):
        max_row = sheet.max_row
        max_column = sheet.max_column

        # 从timing_ranges 中查找最小行，最大行
        timing_min_row = 1
        timing_max_row = 1
        for tr in timing_ranges:
            (min_row, _), (marker_row, _) = tr
            if timing_min_row == 1:
                timing_min_row = min_row
            if min_row < timing_min_row:
                timing_min_row = min_row
            if marker_row > timing_max_row:
                timing_max_row = marker_row

        # 计算出文本对象的范围（除了时序图数据范围的内容）
        text_ranges = set()
        for x in range(1, timing_min_row):
            for y in range(1, max_column):
                text_ranges.add((x, y))
        for x in range(timing_max_row + 1, max_row):
            for y in range(1, max_column):
                text_ranges.add((x, y))
        # 新增两个特殊单元格
        y = column_to_number("DH")
        text_ranges.add((54, y))
        text_ranges.add((56, y))
        return text_ranges

    def _is_wave_start_marker(self, cell, sheet):
        """
        单元格是否拥有行波形图行的开始标记
        内容为空，B列，无上边框，有下边框，非合並單元格
        """
        value = get_cell_value(cell, sheet, False)
        style_top = cell.border.top.style
        style_bottom = cell.border.bottom.style
        if not style_bottom and cell.row < sheet.max_row:
            style_bottom = sheet.cell(cell.row + 1, cell.column).border.top.style
        next_cell = sheet.cell(cell.row + 1, cell.column)
        return (not value
                and 2 == cell.column
                and (not style_top or style_top not in self._identify_table_border_style)
                and (style_bottom in self._identify_table_border_style)
                and not is_merged(cell, sheet)
                and not next_cell.value)

    def _is_text_start_marker(self, cell, sheet):
        """
        单元格是否拥有行文本行的开始标记
        内容不为空，B列，有上边框
        """
        value = get_cell_value(cell, sheet, False)
        style_top = cell.border.top.style
        if not style_top and cell.row > 1:
            style_top = sheet.cell(cell.row - 1, cell.column).border.bottom.style
        return (value
                and cell.row > 1
                and 2 == cell.column
                and (style_top in self._identify_table_border_style))

    def parse_document(self) -> (list[DocumentBlockObject]):
        # # 使用win32com解析图形+图片
        shapes = self._parse_shapes(self._file_path)
        workbook = self._workbook
        blocks = []
        for n in workbook.sheetnames:
            sheet_data = self._parse_sheet(workbook[n], shapes)
            if sheet_data:
                blocks.append(sheet_data)
        return blocks
