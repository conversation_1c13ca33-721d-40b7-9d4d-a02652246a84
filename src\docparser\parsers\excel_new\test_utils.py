
import unittest
from unittest.mock import patch
from datetime import datetime
from openpyxl.cell import Cell
from openpyxl.cell.rich_text import TextBlock, CellRichText

from docparser.models import PictureObject
# 假设被测函数在当前模块中定义
from docparser.parsers.excel_new.utils import get_cell_value, format_number_value, shape_to_object

import pytest
from unittest.mock import MagicMock
from docparser.parsers.excel_new.utils import format_number_value


def test_percentage_0_decimal_places():
    """
    TC02: 测试输入值为 int，number_format 为 "0%"
    """
    # 使用 MagicMock 构造一个模拟的 Cell 对象
    cell = MagicMock()
    cell.value = 0.79
    cell.number_format = "0%"

    # Act
    result = format_number_value(cell)

    # Assert
    assert result == "79%"


def test_non_percentage_format():
    """
    TC06: 测试非百分比格式，直接返回原始值
    """
    cell = MagicMock()
    cell.value = 123.45
    cell.number_format = "General"

    result = format_number_value(cell)
    assert result == 123.45

def test_percentage_with_multiple_zero_placeholders():
    """
    TC07: 测试多个 0 占位符时的格式化行为（例如 "0.00%"）
    """
    cell = MagicMock()
    cell.value = 0.1234
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result == "12.34%"


def test_percentage_with_rounding_up():
    """
    TC08: 测试百分比格式下四舍五入向上取整的情况
    """
    cell = MagicMock()
    cell.value = 0.789
    cell.number_format = "0.0%"

    result = format_number_value(cell)
    assert result == "78.9%"


def test_percentage_with_negative_value():
    """
    TC09: 测试负数百分比格式
    """
    cell = MagicMock()
    cell.value = -0.567
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result == "-56.70%"


def test_percentage_with_large_number():
    """
    TC10: 测试大数值的百分比格式
    """
    cell = MagicMock()
    cell.value = 123456.789
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result == "12345678.90%"


def test_percentage_with_zero_decimal_places_and_rounding():
    """
    TC11: 测试 0 位小数但需要四舍五入的情况
    """
    cell = MagicMock()
    cell.value = 0.999
    cell.number_format = "0%"

    result = format_number_value(cell)
    assert result == "100%"


def test_percentage_with_non_numeric_string():
    """
    TC12: 测试非数字字符串不触发百分比格式化
    """
    cell = MagicMock()
    cell.value = "abc"
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result == "abc"


def test_percentage_with_boolean_value():
    """
    TC13: 测试布尔类型不触发百分比格式化
    """
    cell = MagicMock()
    cell.value = True
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result is True


def test_percentage_with_none_value():
    """
    TC14: 测试 None 值不触发格式化
    """
    cell = MagicMock()
    cell.value = None
    cell.number_format = "0.00%"

    result = format_number_value(cell)
    assert result is None

def test_shape_to_object_full_data():
    """
    TC01: 测试完整 shape 数据映射到对象
    """
    shape = {
        "id": "s1",
        "name": "rectangle",
        "width": 100,
        "height": 200,
        "in_cell": False,
        "data": "sample text",
        "hash": "abc123",
        "index": "A1",
        "top": 10.5,
        "left": 20.3,
        "position": {"x": 0.1, "y": 0.2}
    }

    obj = PictureObject()
    shape_to_object(shape, obj)

    assert obj._id == "s1"
    assert obj._name == "rectangle"
    assert obj._width == 100
    assert obj._height == 200
    assert obj.px_width == 100
    assert obj.px_height == 200
    assert obj._in_cell is False
    assert obj._data == "sample text"
    assert obj._digest == "abc123"
    assert obj.coordinate.desc == "A1"
    assert obj.coordinate.top == "10.5"
    assert obj.coordinate.left == "20.3"
    assert obj._position == {"x": 0.1, "y": 0.2}

