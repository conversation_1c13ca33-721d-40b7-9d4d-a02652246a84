import requests
from openpyxl import load_workbook
import os
from typing import List, Tuple

# def query_model(content: str, test_prompt: str):
#     """提供prompt以及填充到prompt中的信息，组织LLM调用方法入参、获取结果并返回"""
#     # 组合成聊天提示模板
#     chat_prompt = ChatPromptTemplate.from_messages([
#     ("system", "你是一个专业的汽车电子质量工程师，精通日语和中文，请以专业严谨的态度回答我的问题"),
#     ("user", "{question}"),
# ])
#     invoke_dict = {
#         "question": prompt
#     }
#     llm_response: AIMessage = get_ai_message(chat_prompt, invoke_dict)
#     result = llm_response.content
#     return result

def call_azure_model(url, payload, headers=None, httpx=None):
    """Send a POST request to Azure's OpenAI service."""
    try:
        response = requests.post(url, json=payload, headers=headers)
        # 检查 HTTP 状态码
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Request or HTTP error occurred: {e}")
        return None

def query_model(content: str, test_prompt: str):
    """
    Call Azure OpenAI model to get user's intent.

    Args:
        content (str): The input message for intent prediction.

    Returns:
        str: The user's intent extracted by the model.
    """
    try:
        print(f"🌐 Analyzing users file")

        # Configuration for Azure model
        config = {
            "title": "gpt-4o",
            "model": "gpt-4o",
            "provider": "azure",
            "apiBase": "https://sdw-dev.openai.azure.com",
            "apiVersion": "2025-01-01-preview",
            "deployment": "gpt-4o",
            "contextLength": 32764,
            "apiKey": "dIAz3SnaOXxdmE02SyfW36TDVmCtlOxdO7IKDM2n1RpZZaFlGsmEJQQJ99BCACHYHv6XJ3w3AAABACOGEbzZ",
            "apiType": "azure",
            "systemMessage": test_prompt,
            "completionOptions": {
                "maxTokens": 16384
            }
        }

        # Construct the URL for Azure API
        url = f"{config['apiBase']}/openai/deployments/{config['deployment']}/chat/completions?api-version={config['apiVersion']}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {config['apiKey']}"
        }

        # Payload for the request
        payload = {
            "messages": [
                {"role": "system", "content": config["systemMessage"]},
                {"role": "user", "content": content}
            ],
            "max_tokens": config["completionOptions"]["maxTokens"]
        }

        # Call the Azure model
        response_data = call_azure_model(url, payload, headers)

        # Extract the generated response from the model
        if response_data and 'choices' in response_data:
            message_content = response_data['choices'][0]['message']['content']
            return message_content
        else:
            print(f"Incomplete response from Azure model: {response_data}")
            return None

    except Exception as e:
        print(f"Error occurred while querying the model: {e}")
        return None

def extract_text_from_excel(file_path):
    """从Excel文件中提取文字内容，跳过图片"""
    wb = load_workbook(file_path, data_only=True)  # data_only=True 只读取值，不读取公式
    text_content = []
    
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        sheet_text = []
        
        # 遍历所有单元格，只获取文字内容
        for row in ws.iter_rows(values_only=True):  # values_only=True 只获取值
            row_text = []
            for cell_value in row:
                if cell_value is not None:
                    # 转换为字符串并清理
                    cell_text = str(cell_value).strip()
                    if cell_text:
                        row_text.append(cell_text)
            if row_text:  # 只添加非空行
                sheet_text.append(' '.join(row_text))
        
        if sheet_text:
            text_content.append(f"=== {sheet_name} ===")
            text_content.extend(sheet_text)
    
    wb.close()
    return '\n'.join(text_content)

def get_suggestion_from_files(userinput: str, base_dir: str = None) -> Tuple[List[str], str]:
    """
    根据用户输入和指定目录，自动分析相关Excel内容并调用大模型生成建议。
    返回：relevant_file_paths（所有相关文件路径List），suggest_info_path（大模型输出md文件路径）
    """
    # 动态获取 base_dir，默认为当前脚本目录下的 '性能测试手顺'
    if base_dir is None:
        base_dir = os.path.join(os.path.dirname(__file__), '性能测试手顺')

    promptcontent = """
    ##角色：
    你是一个数据分析专家，现在根据用户输入，分析我们需要哪一个数据库。

    ##任务：
    现在需要你根据用户的输入分析我们需要什么数据库，只需要你返回对应的数据库名称。

    ##数据库名称：
    1-ROM&RAM测试
    2-Stack统计
    3-负荷测试相关资料
    4-启动时间测试

    ##样例1:
    input: 我需要测试ROM,你能告诉我操作方法吗？
    output: 1-ROM&RAM测试

    ##样例2:
    input: 如何操作stack?
    output: 2-Stack统计

    ##样例3:
    input: 时间启动测试
    output: 4-启动时间测试

    ##样例4:
    input: 世界上最大的国家是？
    output: 非功能需求

    ##需求:
    - 注意你的输出只能是完整的数据库名称：即包含数字-数据库名。
    - 你的输出中不能包含任何其他内容，有且仅有对应数据库名称
    - 如果用户输入和我们的数据库没有任何关系，则输出非功能需求
    - CANoe地图发送手顺相关内容也属于 3-负荷测试相关资料
    """

    decideKB = query_model(userinput, promptcontent)
    relevant_file_paths = []
    all_excel_content = ""

    if decideKB and decideKB != "非功能需求":
        path = os.path.join(base_dir, decideKB)
        excel_contents = {}
        if os.path.isdir(path):
            for fname in os.listdir(path):
                if fname.endswith((".xlsx", ".xls")) and not fname.startswith('~$'):
                    file_path = os.path.join(path, fname)
                    relevant_file_paths.append(file_path)
                    text_content = extract_text_from_excel(file_path)
                    if text_content:
                        excel_contents[fname] = text_content
            for filename, content in excel_contents.items():
                all_excel_content += f"\n--- {filename} ---\n"
                all_excel_content += content
                all_excel_content += "\n" + "-" * 50
    else:
        return [], "未找到相关数据库或为非功能需求"

    prompt_output = f"""
    ## 角色：
    你是一个数据分析专家，现在根据用户输入，结合我们提供的相关文档内容，指引用户进行最后的生成。

    ## 相关文档内容：
    {all_excel_content}

    ## 参考文件路径
    {'; '.join(relevant_file_paths)}

    ## 输出内容
    1. 你需要按步骤输出操作顺序方法
    2. 如果相关文档内容涉及到图片，你需要提示用户：注意操作步骤中有图片相关内容，请参照参考文档确认
    3. 你需要在最后提供参考文件路径

    ## 需求：
    - 注意你的输出必须是按照markdown的形式输出
    - 注意你的输出不能包含无关内容
    - 注意你的输出或者参考文档中涉及到图片，你需要在输出最后提示用户：
        注意操作步骤中有图片相关内容，请参照参考文档确认
    - 你需要在最后提供参考文件路径
    """

    suggest_info = query_model(userinput, prompt_output)
    
    # 保存大模型输出为 markdown 文件
    output_md_path = os.path.join(os.path.dirname(__file__), "output.md")
    with open(output_md_path, "w", encoding="utf-8") as f:
        f.write(suggest_info if suggest_info else "")
    print(f"大模型输出已保存为: {output_md_path}")
    
    return relevant_file_paths, output_md_path

# 示例调用
if __name__ == "__main__":
    userinput = "如何进行RAM测试?"
    relevant_file_path, suggest_info_path = get_suggestion_from_files(userinput)
    print("相关文件路径：", relevant_file_path)
    print("大模型输出路径：", suggest_info_path)