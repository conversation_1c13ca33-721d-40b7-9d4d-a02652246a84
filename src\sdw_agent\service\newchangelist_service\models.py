from typing import Any
from pydantic import BaseModel, Field

class ConfirmFormatNcl(BaseModel):
    """LLM确认结果模型"""
    confirm_result: str = Field(
        description="",
    )

class NclUpdateInput(BaseModel):
    """工作流输入模型"""
    file_path: str = Field(
        description="Excel文件路径",
        examples=["D:/path/to/file.xlsx"]
    )

class NclUpdateResultItem(BaseModel):
    """Excel处理结果项"""
    row: Any
    Display_size: str
    ソフトの変更内容: str
    変更対象コンポーネント: str
    新変定移植: str
    車両コード: str
    SQA完了状況: str
    行数: int
    変更範囲: str
    PF同時変更: str
    PF同時変更2: str