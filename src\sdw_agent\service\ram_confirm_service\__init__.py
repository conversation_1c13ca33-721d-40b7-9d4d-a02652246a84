"""
RAM确认 Workflow

V字对应：
4.2.54. RAM(初期化,S/R,+B保持)確認＆結果検証

该模块提供RAM确认功能，根据Gerrit Diff文档获取变更的全局变量并确认初始化时机。

主要功能：
1. 根据Gerrit Diff文档，获取变更的全局变量
2. 全局变量初始化位置检索，并确认初始化时机
3. 生成RAM确认报告
"""

from sdw_agent.service.ram_confirm_service.models import RAMConfirmRequest, RepositoryInfo
from sdw_agent.service.ram_confirm_service.workflow_ram_confirm import RAMConfirmWorkflow, do_ram_confirm

__all__ = ['RAMConfirmWorkflow', 'do_ram_confirm', 'RAMConfirmRequest', 'RepositoryInfo']

