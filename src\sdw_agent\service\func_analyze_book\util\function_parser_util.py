"""
文件名: func_file_parser_util.py

功能:
该文件包含一系列用于解析代码文件的工具函数和类，主要用于分析函数调用关系、查找函数定义和调用位置，以及处理多线程和多进程的并行分析任务。

主要功能:
1. 判断函数是否在头文件中声明。
2. 查找指定函数的调用位置及上下文。
3. 递归分析目录中的代码文件，提取函数调用信息。
4. 支持多线程和多进程并行处理，提高分析效率。
5. 解析外部函数调用，查找其定义并返回详细信息。

主要模块:
- `is_function_in_header`: 判断函数是否在头文件中声明。
- `FastFunctionCallAnalyzer`: 类，用于快速分析函数调用关系。
- `analyze_directory`: 在目录中递归查找函数调用信息（多线程实现）。
- `analyze_dir`: 在目录中递归查找函数调用信息（多进程实现）。
- `parse_external_functions`: 解析函数中调用的外部函数。
"""
import concurrent
from typing import List, Tuple, Dict, Set

import chardet
from loguru import logger

from sdw_agent.service.func_analyze_book.models import FunctionDeclaration, FunctionParameter, \
    FunctionDetailData
from sdw_agent.service.func_analyze_book.util.func_analyze_util import parse_func_variavle


# 功能1: 清理函数声明字符串
def clean_function_declaration(decl: str) -> str:
    """
    移除函数声明中的注释和多余空格
    """
    # 移除单行注释
    decl = re.sub(r'//.*', '', decl)
    # 移除多行注释
    decl = re.sub(r'/\*.*?\*/', '', decl, flags=re.DOTALL)
    # 压缩多个连续空格
    decl = re.sub(r'\s+', ' ', decl)
    # 去除首尾空格
    return decl.strip()


# 功能2: 提取函数名
def extract_function_name(clean_decl: str) -> Tuple[str, str]:
    """
    从清理后的函数声明中提取函数名和前缀
    返回元组 (函数名前缀, 函数名)
    """
    match = re.search(r'\b(\w+)\s*\(', clean_decl)
    if not match:
        raise ValueError(f"无法从声明中提取函数名: {clean_decl}")

    func_name = match.group(1)
    prefix = clean_decl.split(func_name)[0].strip()
    return prefix, func_name


# 功能3: 提取返回值类型
def extract_return_type(prefix: str) -> str:
    """
    从函数声明前缀中正确提取返回类型

    :param prefix: 函数声明前缀（函数名之前的部分）
    :return: 返回类型字符串
    """
    if not prefix:
        return None

    # 移除常见的修饰符（存储类说明符、类型限定符等）
    modifiers = ["static", "inline", "extern", "const", "volatile", "register", "restrict", "void"]

    # 处理带修饰符的情况
    for mod in modifiers:
        # 检查前缀是否以修饰符开头（后跟空格）
        if prefix.startswith(mod + " "):
            # 移除修饰符并递归处理剩余部分
            return extract_return_type(prefix[len(mod) + 1:])

    # 处理指针类型（如 char *）
    if prefix.endswith('*'):
        # 确保指针符号是类型的一部分
        return prefix.strip()

    # 处理多单词返回类型（如 unsigned int）
    if ' ' in prefix:
        # 尝试找到最后一个空格前的部分作为返回类型
        return prefix.rsplit(' ', 1)[0]
    if prefix.strip() == "void":
        return None

    return prefix.strip()


# 功能4: 提取参数列表字符串

def extract_parameters_str(clean_decl: str, func_name: str) -> str:
    """
    正确提取参数列表部分的字符串

    修复问题：正确处理返回类型和函数名的区分

    :param clean_decl: 清理后的函数声明字符串
    :param func_name: 函数名（不含返回类型）
    :return: 参数列表字符串
    """
    # 构建更精确的正则表达式：
    # 1. 匹配任何返回类型和修饰符
    # 2. 匹配函数名后紧跟括号
    # 3. 捕获括号内的内容
    pattern = rf"\b{re.escape(func_name)}\s*\(\s*([^)]*)\s*\)"

    match = re.search(pattern, clean_decl)
    if not match:
        # 如果无法匹配，尝试备选模式
        if "(" in clean_decl and ")" in clean_decl:
            return clean_decl.split("(", 1)[1].rsplit(")", 1)[0].strip()
        return ""

    params_str = match.group(1).strip()

    # 处理特殊空参数情况
    if params_str.lower() == "void":
        return ""  # 返回空字符串表示无参数

    return params_str


# 功能5: 分割参数列表
def split_parameters(params_str: str) -> List[str]:
    """
    将参数列表字符串分割为单个参数的字符串列表
    """
    # 使用逗号分隔，但排除括号内的逗号
    return [p.strip() for p in re.split(r',\s*(?![^()]*\))', params_str) if p.strip()]


# 功能6: 解析单个参数
def parse_single_parameter(param_str: str) -> Tuple[str, str]:
    """
    解析单个参数的字符串，返回 (参数类型, 参数名)
    """
    # 提取参数名（最后一个单词）
    match = re.search(r'(\w+)\s*$', param_str)
    if not match:
        raise ValueError(f"无法解析参数: {param_str}")

    name = match.group(1)
    # 提取类型部分（移除参数名后的剩余部分）
    type_part = param_str.replace(name, '').strip()
    # 清理类型部分（移除多余空格和星号周围的空格）
    type_part = re.sub(r'\s*\*\s*', '*', type_part)

    return type_part, name


# 功能7: 判断参数方向
def determine_parameter_direction(param_type: str) -> str:
    """
    根据参数类型判断参数方向 (in/out/inout)
    """
    # 指针类型且不是const通常表示输出
    if '*' in param_type:
        if 'const' in param_type:
            return "in"  # const指针，通常是输入
        return "out"  # 非const指针，通常是输出
    return "in"  # 非指针类型，通常是输入


# 功能8: 主解析函数
def parse_function_declaration(decl: str) -> FunctionDeclaration:
    """主函数：解析完整的函数声明"""
    # 1. 清理声明
    clean_decl = clean_function_declaration(decl)

    # 2. 提取函数名和前缀
    prefix, func_name = extract_function_name(clean_decl)

    # 3. 提取返回类型
    return_type = extract_return_type(prefix)

    # 4. 提取参数列表字符串
    params_str = extract_parameters_str(clean_decl, func_name)

    # 5. 处理参数
    parameters = []
    if params_str:
        # 分割参数列表
        param_list = split_parameters(params_str)
        for param_str in param_list:
            # 解析单个参数
            param_type, param_name = parse_single_parameter(param_str)
            # 判断参数方向
            direction = determine_parameter_direction(param_type)
            parameters.append(
                FunctionParameter(type=param_type, name=param_name, direction=direction)
            )

    return FunctionDeclaration(
        name=func_name,
        return_type=return_type,
        parameters=parameters
    )


def split_diff_blocks(diff_content: str) -> List[Tuple[str, str]]:
    """
    按照 @@ 行分割 Git Diff 内容
    :param diff_content: Git Diff 内容
    :return: 分割后的块列表，每个块是 (header, content) 元组
    """
    # 查找所有 @@ 开头的行（匹配范围行）
    range_lines = []
    for match in re.finditer(r'.*@@ .+? @@.+', diff_content, re.MULTILINE):
        start = match.start()
        end = match.end()
        full_line = diff_content[start:end]
        range_lines.append((start, end, full_line))

    # 如果没有找到范围行，返回整个内容
    if not range_lines:
        return [("", diff_content)]

    # 根据范围行位置分割内容
    blocks = []
    for i in range(len(range_lines)):
        start_pos = range_lines[i][0]
        end_pos = range_lines[i + 1][0] if i + 1 < len(range_lines) else len(diff_content)

        # 获取当前块的 header 和内容
        header_line = range_lines[i][2]
        content = diff_content[range_lines[i][1]:end_pos].strip()

        # 处理可能存在的上下文附加信息
        context_pos = header_line.find(" @@ ") + 4
        if context_pos > 0:
            context_info = header_line[context_pos:].strip()
            header = header_line[:context_pos].strip()
            content = context_info + "\n" + content
        else:
            header = header_line

        blocks.append(content)

    return blocks


def get_function_from_diff_block(diff_block: str) -> str:
    """
    从 Git Diff 块中提取变更对应的函数签名
    :param diff_block: 单个 Git Diff 块
    :return: 完整的函数签名，如 "int sensor_read(sensor_ctx_t *ctx, sensor_data_t *data)"
    """
    # 尝试从头部直接提取函数签名（最快、最准确的方式）
    header_match = re.search(r' [^@]+@@\s*(.*?)$', diff_block, re.MULTILINE)
    if header_match:
        function_signature = header_match.group(1)
        if re.search(r'(\w+)\s*\(', function_signature):  # 检查是否是函数签名
            return function_signature

    # 在代码内容中查找函数签名（更全面的方法）
    function_pattern = re.compile(
        r'^(?:[ +\-\s]*)'  # 允许diff标记（+, -, space）
        r'(?:(?:static|inline|volatile)\s+)?'  # 常见修饰符
        r'\b\w+[\w\s*]+\s+'  # 返回类型（允许指针等）
        r'([\w_]+)\s*'  # 函数名（主要捕获部分）
        r'\([^)]*\)'  # 参数列表
        r'(?=\s*$|\s*\{|\s*;)',  # 后面可以是空、{ 或 ;
        re.MULTILINE
    )

    signatures_found = set()
    # 查找所有可能的函数签名
    for match in function_pattern.finditer(diff_block):
        # 获取包含签名的完整行
        full_signature = diff_block.splitlines()[0].strip() if diff_block.splitlines() else ""
        if not full_signature:
            # 尝试通过上下文获取完整签名
            start = max(0, match.start() - 50)
            end = min(len(diff_block), match.end() + 50)
            context = diff_block[start:end]
            # 在上下文中提取完整行
            match_line = next((line for line in context.splitlines() if match.group() in line), "")
            if match_line:
                full_signature = re.sub(r'^\s*[+\-]?\s*', '', match_line).strip()
                func_name = re.compile(
                    r'@@[^@]+@@\s*'  # @@ 行前缀
                    r'.*?'  # 任意字符（非贪婪）
                    r'\b([a-zA-Z_]\w*)\b'  # 有效的函数名（捕获组）
                )

        if full_signature:
            signatures_found.add(full_signature)

    if signatures_found:
        # 优先返回最长的签名（通常更完整）
        return max(signatures_found, key=len)

    # 如果还未找到，尝试在块头中匹配
    header_pattern = re.search(r'@@[^@]+@@\s*(.*?)\s*\n', diff_block)
    if header_pattern:
        return header_pattern.group(1)

    # 最后手段：返回第一个包含括号的行
    bracket_line = next((line for line in diff_block.splitlines() if '(' in line), "")
    return re.sub(r'^\s*[+\-]\s*', '', bracket_line).strip()


def parse_git_diff(diff_content: str) -> List[str]:
    """
    解析 Git Diff 内容，提取变更的函数签名
    :param diff_content: Git Diff 内容
    :return: 变更的函数签名列表
    """
    # 识别函数签名的正则表达式
    function_pattern = re.compile(
        r'^\s*(?:(?:static|inline)\s+)?'  # 可能的前缀关键字
        r'\b([a-zA-Z_][\w\*\s]+\s+'  # 返回类型（至少包含一个空格）
        r'[a-zA-Z_]\w*\s*\([^)]*\))\s*'  # 函数名和参数列表
        r'(?:\{)?\s*$'  # 可能的左大括号
    )

    # 存储所有变更的函数签名
    modified_functions = set()

    # 分割 diff 内容为多个变更块
    diff_blocks = split_diff_blocks(diff_content)

    for block in diff_blocks:
        if not block.strip():
            continue

    for block in diff_blocks:
        func_name = get_function_from_diff_block(block)
        modified_functions.add(func_name)

    return list(modified_functions)


def extract_function_declarations(code_lines):
    """
    从代码行中提取函数声明，支持跨行和行内注释
    """
    declarations = []
    i = 0
    code_lines = code_lines.split('\n')
    lines_count = len(code_lines)

    while i < lines_count:
        line = code_lines[i].strip().strip("+")

        # 跳过空行和注释行
        if not line or line.startswith('//'):
            i += 1
            continue

        # 尝试匹配函数声明的起始部分
        multi_line_mode = r'''
            ^\s*                 # 行首空格
            ([a-zA-Z_*][a-zA-Z0-9_*\s]*?)  # 返回类型 (非贪婪匹配)
            \s+                  # 至少一个空格
            ([a-zA-Z_][a-zA-Z0-9_]*)       # 函数名
            \s*\(                # 左括号
        '''

        single_line_mode = '''
            ^\s*                 # 行首可能的空格
            ([a-zA-Z_*][a-zA-Z0-9_*\s]*?)         # 返回类型 (非贪婪匹配)
            \s+                  # 至少一个空格
            ([a-zA-Z_][a-zA-Z0-9_]*)                # 函数名
            \s*\(                # 左括号，允许括号前有空格
            ([^)]*?)             # 参数列表 (非贪婪匹配)
            \)                   # 右括号
            '''

        match_str_pattern = line.count('(') - line.count(')') <= 0

        if match_str_pattern:
            func_pattern = re.compile(single_line_mode, re.VERBOSE)
        else:
            func_pattern = re.compile(multi_line_mode, re.VERBOSE)

        match = func_pattern.search(line)

        if match:
            # 提取返回类型和函数名
            return_type = match.group(1).strip()
            # 检查函数名是否是关键字（如return, if等）
            if return_type.lower() in ['if', 'for', 'while', 'switch', 'return', 'case']:
                i += 1
                continue
        else:
            i += 1
            continue

        if not match_str_pattern:
            # 提取返回类型和函数名
            function_name = match.group(2)

            # 提取参数列表的起始位置
            param_start = match.end()
            param_text = line[param_start:]

            # 查找参数列表的结束位置
            brace_count = line.count('(') - line.count(')')  # 初始左括号
            param_lines = [param_text]
            i += 1

            # 继续处理后续行，直到找到匹配的右括号
            while i < lines_count:
                next_line = code_lines[i].strip()
                if not next_line:
                    i += 1
                    continue

                # 计算当前行的括号平衡
                brace_count += next_line.count('(') - next_line.count(')')
                param_lines.append(next_line.replace('+', ''))

                if brace_count <= 0:
                    break

                i += 1

            # 合并所有参数行
            full_param_text = ' '.join(param_lines)

            # 移除行内注释
            full_param_text = re.sub(r'//.*', '', full_param_text)

            # 提取参数列表（移除首尾括号）
            params = full_param_text.strip('{}').strip()

            # 构建完整的函数声明
            declaration = f"{return_type} {function_name}({params})"
            declarations.append(declaration)
        elif match:
            declaration = match.group(0)
            declarations.append(declaration)
        i += 1

    return declarations


def parse_diff_for_file(diff_content):
    """从diff内容中提取变更涉及的文件路径"""
    # 匹配diff中的文件路径（如--- a/drivers/sensor.c 或+++ b/drivers/sensor.c）
    file_pattern = re.compile(r'[+-]{3} [ab]/(.*)$', re.MULTILINE)
    match = file_pattern.search(diff_content)
    if match:
        return match.group(1)  # 返回文件路径（如drivers/sensor.c）
    return None


def remove_multiline_comments(code: str) -> str:
    """移除多行注释"""
    return re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)


def extract_local_variables(func_name, func_code, lines: List[str]) -> List[Dict]:
    """提取局部变量信息"""
    # 增强的局部变量模式
    local_pattern = re.compile(
        r'^\s*'  # 开头空格
        r'(?:(static|const|volatile|register)\s+)?'  # 存储类
        r'(?:(struct|enum|union)\s+)?'  # 复合类型
        r'([\w\s*]+)'  # 类型
        r'\s+'  # 空格
        r'(\w+)'  # 变量名
        r'(\s*\[[^\]]*\])?'  # 数组维度（允许任意非']'字符）
        r'\s*(?:=\s*[^;]+)?\s*;'  # 可选初始化
        r'(\s*//.*)?'  # 可选注释
    )

    variables = []

    for line in lines:
        # 跳过非变量声明行
        if is_non_variable_line(line):
            continue

        # 尝试匹配局部变量
        match = local_pattern.search(line)
        if match:
            var_info = build_variable_info(match)
            if var_info['name'] not in ['if', 'for', 'while', 'switch', 'return', 'case'] and var_info['type'] != '':
                variables.append(var_info)

    process_list_with_threads(func_name, func_code, variables)

    return variables


def is_non_variable_line(line: str) -> bool:
    """检查是否是非变量声明行"""
    return (
            not line.strip() or  # 空行
            line.strip() in ['{', '}'] or  # 大括号行
            line.strip().startswith('#')  # 预处理指令
    )


def build_variable_info(match) -> Dict:
    """构建变量信息字典"""
    storage = match.group(1) or ""
    compound_type = match.group(2) or ""
    base_type = match.group(3).strip()
    name = match.group(4)
    array_dim = match.group(5) or ""
    comment = match.group(6).strip() if match.group(6) else ""

    # 构建完整类型
    full_type = compound_type + " " + base_type if compound_type else base_type
    full_type += array_dim  # 添加数组维度
    full_type = re.sub(r'\s+', ' ', full_type).strip()  # 清理多余空格
    desc = ""

    return {
        "name": name,
        "type": full_type,
        "storage": storage,
        "comment": comment,
        "desc": desc
    }


def process_list_with_threads(func_name, func_code, variables):
    """
    使用多线程处理 data_list，每个元素传递给 parse_func_variable 函数。

    :param data_list: 每个元素是一个包含 (func_name, func_code, name) 的元组
    :return: 返回所有线程的结果列表
    """
    results = []

    # 定义一个任务函数，用于线程调用
    def task(param_info):
        desc = parse_func_variavle(func_name, func_code, param_info["name"])
        param_info["desc"] = desc
        return

    # 使用 ThreadPoolExecutor 进行多线程处理
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # 提交任务到线程池
        future_to_data = {executor.submit(task, item): item for item in variables}

        # 收集结果
        for future in concurrent.futures.as_completed(future_to_data):
            try:
                results.append(future.result())
            except Exception as e:
                logger.error(f"线程执行时发生错误: {e}")

    return results


def find_global_variable_type(file_path, variable_name):
    """
    在 .C 文件中查找指定全局变量的类型。
    :param file_path: .C 文件路径
    :param variable_name: 全局变量名称
    :return: 变量类型字符串（包括指针和数组类型）
    """
    # 读取文件内容
    with open(file_path, 'rb') as file:
        raw_data = file.read()
        result = chardet.detect(raw_data)
        encoding = result['encoding']  # 检测到的编码

    # 使用检测到的编码打开文件
    with open(file_path, 'r', encoding=encoding) as file:
        content = file.read()

    # 移除注释（单行和多行注释）
    content = re.sub(r'//.*', '', content)  # 移除单行注释
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)  # 移除多行注释

    # 移除预处理指令（如 #ifdef、#endif 等）
    content = re.sub(r'#.*', '', content)

    # 正则表达式匹配全局变量定义
    # 支持 static 修饰符、基本类型、指针类型和数组类型
    pattern = rf'\b(static\s+)?([a-zA-Z_][a-zA-Z0-9_*\s]+)\s+{variable_name}\s*(\[[^\]]*\])?\s*;'

    match = re.search(pattern, content)
    if match:
        variable_type = match.group(2).strip()  # 提取变量类型（忽略 static 修饰符）
        array_info = match.group(3)  # 提取数组信息（如果存在）

        if array_info:
            return f"{variable_type}{array_info}".strip()  # 返回数组类型
        return variable_type.strip()  # 返回普通类型或指针类型

    return None  # 如果没有找到变量定义，返回 None


def extract_global_variables(func_name, func_info: FunctionDetailData, lines: List[str], local_vars: Set[str]) -> List[
    Dict]:
    """
    提取全局变量信息（改进版）

    :param lines: 代码行列表
    :param local_vars: 局部变量名集合
    :return: 全局变量字典列表 [{"name": "...", "type": "...", "comment": "..."}]
    """
    # 增强的全局变量声明模式
    global_var_pattern = re.compile(
        r'^\s*'  # 开头空格
        r'(?:(static|extern|const|volatile|register)\s+)?'  # 存储类/限定符
        r'(?:(struct|enum|union)\s+)?'  # 复合类型关键字
        r'([\w\s*]+?)'  # 类型（可能包含空格和指针）
        r'\s+'  # 空格
        r'(\w+)'  # 变量名
        r'\s*(?:=\s*[^;]+)?\s*;'  # 可选初始化
        r'(\s*//.*)?'  # 可选注释
    )

    # C语言关键字集合
    keywords = {
        "break", "continue", "return", "if", "else", "for", "while",
        "do", "switch", "case", "default", "goto", "sizeof", "typedef",
        "enum", "struct", "union", "const", "volatile", "static",
        "extern", "register", "auto"
    }

    variables = []

    for line in lines:
        # 跳过非变量声明行
        if is_non_global_line(line):
            continue

        # 尝试匹配全局变量声明
        match = global_var_pattern.search(line)
        if match:
            storage = match.group(1) or ""
            compound = match.group(2) or ""
            var_type = match.group(3).strip()
            var_name = match.group(4)
            comment = match.group(5).strip() if match.group(5) else ""

            if var_name in local_vars or var_name in keywords:
                continue

            # 构建完整类型
            desc = parse_func_variavle(func_name, func_info.func_body, var_name)
            full_type = find_global_variable_type(func_info.func_file_name, var_name)

            # 添加到结果
            if var_name not in [item["name"] for item in variables]:
                variables.append({
                    "name": var_name,
                    "type": full_type,
                    "comment": comment,
                    "desc": desc
                })
                continue

        # 处理多变量声明 (int a, b;)
        multi_var_match = re.search(
            r'^\s*(?:(static|extern|const|volatile|register)\s+)?'
            r'(?:(struct|enum|union)\s+)?'
            r'([\w\s*]+?)\s+'
            r'([\w,\s]+)\s*;',
            line
        )

        if multi_var_match:
            storage = multi_var_match.group(1) or ""
            compound = multi_var_match.group(2) or ""
            var_type = multi_var_match.group(3).strip()
            var_names = multi_var_match.group(4).split(',')

            # 构建完整类型
            full_type = ""
            if compound:
                full_type += compound + " "
            full_type += var_type
            if storage:
                full_type = storage + " " + full_type

            # 添加每个变量
            for name in var_names:
                clean_name = name.strip()
                if clean_name:
                    variables.append({
                        "name": clean_name,
                        "type": full_type,
                        "comment": "",
                        "desc": desc
                    })

    return variables


def is_non_global_line(line: str) -> bool:
    """
    检查是否是非全局变量声明行

    :param line: 代码行
    :return: 如果是非全局变量行则返回 True
    """
    return (
            not line.strip() or  # 空行
            line.strip().startswith('//') or  # 注释行
            line.strip().startswith('#') or  # 预处理指令
            '=' in line and '==' not in line and not line.strip().endswith(';') or  # 赋值语句
            '(' in line and ')' in line and not line.strip().endswith(';')  # 函数调用
    )


def is_valid_global_variable(name: str, local_vars: Set[str], line: str) -> bool:
    """
    检查是否是有效的全局变量

    :param name: 变量名
    :param local_vars: 局部变量集合
    :param line: 所在行
    :return: 如果是有效全局变量则返回 True
    """
    # 排除局部变量
    if name in local_vars:
        return False

    # 排除关键字
    if name in ["return", "if", "else", "for", "while", "do", "switch", "case", "default", "break", "continue"]:
        return False

    # 排除函数调用
    if re.search(rf'\b{re.escape(name)}\s*\(', line):
        return False

    # 排除基本类型
    if name in ["int", "char", "float", "double", "void", "bool", "short", "long", "signed", "unsigned"]:
        return False

    # 排除常见宏定义
    if name.isupper():
        return False

    # 排除数字
    if name.isdigit():
        return False

    return True


def is_non_global_line(line: str) -> bool:
    """检查是否是非全局变量使用行"""
    return (
            not line.strip() or  # 空行
            line.strip().startswith('//')  # 注释行
    )


def is_valid_global_variable(name: str, local_vars: Set[str], line: str) -> bool:
    """检查是否是有效全局变量"""
    # 排除局部变量
    if name in local_vars:
        return False

    # 排除关键字
    if name in ["return", "if", "else", "for", "while", "do", "switch", "case"]:
        return False

    # 排除函数调用
    if re.search(rf'\b{re.escape(name)}\s*\(', line):
        return False

    # 排除基本类型
    if name in ["int", "char", "float", "double", "void", "bool", "short", "long"]:
        return False

    # 排除常见宏定义
    if name.isupper():
        return False

    return True


import re


def extract_local_variables_v2(function_body: str) -> list:
    """
    提取函数体中的局部变量，并返回详细信息的字典列表

    :param function_body: 字符串形式的函数体
    :return: 包含变量信息的字典列表
    """
    # 匹配局部变量声明的正则表达式
    variable_pattern = r"""
        (?P<storage>(?:const|static|unsigned|signed|volatile|extern|register|auto)\s+)*  # 可选存储修饰符
        (?P<type>(int|float|double|char|void|struct\s+\w+|bool|long|short)\s+            # 数据类型
        (?:\*+)?                                                                        # 可选指针符号
        )                                                                               # 捕获完整类型
        (?P<name>[a-zA-Z_]\w*)                                                          # 变量名
        (?:\s*=\s*(?P<desc>[^,;]+))?                                                   # 可选初始化或描述
        \s*(?:,|;|\n)                                                                   # 结束符（分号或逗号）
        (?:\s*(?P<comment>//.*|/\*.*?\*/))?                                            # 可选注释
    """

    # 提取所有符合声明的局部变量
    matches = re.finditer(variable_pattern, function_body, re.VERBOSE)

    # 存储提取的变量信息
    variables = []

    for match in matches:
        # 提取匹配的各部分
        storage = match.group("storage") or ""
        full_type = match.group("type").strip()
        name = match.group("name")
        desc = match.group("desc") or ""
        comment = match.group("comment") or ""

        # 去除多余空格
        storage = storage.strip()
        desc = desc.strip()
        comment = comment.strip()

        # 构建变量信息字典
        variable_info = {
            "name": name,
            "type": full_type,
            "storage": storage,
            "comment": comment,
            "desc": desc
        }

        # 添加到结果列表
        variables.append(variable_info)

    return variables


def enhance_variable_analysis(func_name, func_info: FunctionDetailData) -> Dict[str, List[Dict]]:
    """增强版变量分析，支持更多类型和注释"""
    # 结果字典
    result = {
        "local_variables": [],
        "global_variables": []
    }

    # 预处理：移除多行注释
    clean_code = remove_multiline_comments(func_info.func_body)
    lines = clean_code.split('\n')

    logger.debug("开始提取变量")

    # 1. 提取局部变量
    local_vars_list = extract_local_variables(func_name, func_info.func_body, lines)
    result["local_variables"] = local_vars_list

    logger.debug("完成提取局部变量")
    # 获取局部变量名集合
    local_var_names = {var["name"] for var in local_vars_list}

    # 2. 提取全局变量
    result["global_variables"] = extract_global_variables(func_name, func_info, lines, local_var_names)

    logger.debug("完成提取变量")
    return result


