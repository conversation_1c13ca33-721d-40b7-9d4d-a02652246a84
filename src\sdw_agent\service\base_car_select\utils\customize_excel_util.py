"""
自定义Excel工具类

继承自ExcelUtil，提供额外的Excel操作功能
"""

from typing import Tuple, Optional, Union
from loguru import logger

from sdw_agent.service.base_car_select.model import CarDocInfoModel
from sdw_agent.util.excel.core import ExcelUtil, CellStyle, CellRange
from sdw_agent.util.excel.core import OpenpyxlEngine, Win32comEngine, PandasEngine


class CustomizeExcelUtil(ExcelUtil):
    """
    自定义Excel工具类
    
    继承自ExcelUtil，提供额外的Excel操作功能，包括：
    1. 获取工作表的最大行索引和最大列索引
    2. 其他自定义功能
    """

    def __init__(self, file_path: str, start_row:int=1, start_col:int=1, engine: str = "win32com", auto_create: bool = True):
        """
        初始化自定义Excel工具
        
        Args:
            file_path: Excel文件路径
            engine: 操作引擎类型（"win32com", "openpyxl" 或 "pandas"）
            auto_create: 如果文件不存在是否自动创建
        """
        super().__init__(file_path, engine, auto_create)
        self.start_row = start_row
        self.start_col = start_col
        logger.info(f"初始化CustomizeExcelUtil - 文件: {file_path}, 引擎: {engine}")

    def get_sheet_max_dimensions(self, sheet_name: str) -> Tuple[int, int]:
        """
        获取指定工作表的最大行索引和最大列索引
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            Tuple[int, int]: (最大行索引, 最大列索引)
            
        Raises:
            ValueError: 当工作表不存在时
            NotImplementedError: 当引擎不支持该操作时
        """
        logger.info(f"获取工作表 '{sheet_name}' 的最大行列索引")
        
        # 检查工作表是否存在
        if sheet_name not in self.get_sheet_names():
            raise ValueError(f"工作表 '{sheet_name}' 不存在")
        
        try:
            if isinstance(self.engine, OpenpyxlEngine):
                return self._get_max_dimensions_openpyxl(sheet_name)
            elif isinstance(self.engine, Win32comEngine):
                return self._get_max_dimensions_win32com(sheet_name)
            elif isinstance(self.engine, PandasEngine):
                return self._get_max_dimensions_pandas(sheet_name)
            else:
                raise NotImplementedError(f"引擎 {type(self.engine)} 不支持获取最大行列索引")
                
        except Exception as e:
            logger.error(f"获取工作表 '{sheet_name}' 最大行列索引失败: {str(e)}")
            raise

    def _get_max_dimensions_openpyxl(self, sheet_name: str) -> Tuple[int, int]:
        """
        使用openpyxl引擎获取最大行列索引
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            Tuple[int, int]: (最大行索引, 最大列索引)
        """
        try:
            worksheet = self.engine.workbook[sheet_name]
            max_row = worksheet.max_row
            max_col = worksheet.max_column
            
            logger.debug(f"Openpyxl引擎 - 工作表 '{sheet_name}': 最大行={max_row}, 最大列={max_col}")
            return max_row, max_col
            
        except Exception as e:
            logger.error(f"Openpyxl引擎获取最大行列索引失败: {str(e)}")
            raise

    def _get_max_dimensions_win32com(self, sheet_name: str) -> Tuple[int, int]:
        """
        使用win32com引擎获取最大行列索引
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            Tuple[int, int]: (最大行索引, 最大列索引)
        """
        try:
            worksheet = self.engine._get_worksheet(sheet_name)
            
            # 使用UsedRange获取已使用的区域
            used_range = worksheet.UsedRange
            if used_range is None:
                logger.warning(f"工作表 '{sheet_name}' 没有已使用的区域")
                return 1, 1
            
            max_row = used_range.Rows.Count
            max_col = used_range.Columns.Count
            
            # 获取UsedRange的起始位置，计算实际的最大行列
            start_row = used_range.Row
            start_col = used_range.Column
            actual_max_row = start_row + max_row - 1
            actual_max_col = start_col + max_col - 1
            
            logger.debug(f"Win32com引擎 - 工作表 '{sheet_name}': 最大行={actual_max_row}, 最大列={actual_max_col}")
            return actual_max_row, actual_max_col
            
        except Exception as e:
            logger.error(f"Win32com引擎获取最大行列索引失败: {str(e)}")
            raise

    def _get_max_dimensions_pandas(self, sheet_name: str) -> Tuple[int, int]:
        """
        使用pandas引擎获取最大行列索引
        
        Args:
            sheet_name: 工作表名称
            
        Returns:
            Tuple[int, int]: (最大行索引, 最大列索引)
        """
        try:
            if sheet_name not in self.engine.data_cache:
                raise ValueError(f"工作表 '{sheet_name}' 不存在于pandas缓存中")
            
            df = self.engine.data_cache[sheet_name]
            max_row = len(df) + 1  # +1 因为pandas索引从0开始，Excel从1开始，且要包含表头
            max_col = len(df.columns)
            
            logger.debug(f"Pandas引擎 - 工作表 '{sheet_name}': 最大行={max_row}, 最大列={max_col}")
            return max_row, max_col
            
        except Exception as e:
            logger.error(f"Pandas引擎获取最大行列索引失败: {str(e)}")
            raise

    def get_sheet_data_dimensions(self, sheet_name: str, with_end_flag:bool = False, exclude_empty_rows: bool = True,
                                  exclude_empty_cols: bool = True) -> Tuple[int, int]:
        """
        获取工作表实际数据的行列范围（排除空行空列）
        
        Args:
            sheet_name: 工作表名称
            with_end_flag: 是否是以 end 来标记行结束
            exclude_empty_rows: 是否排除空行
            exclude_empty_cols: 是否排除空列
            
        Returns:
            Tuple[int, int]: (实际数据最大行索引, 实际数据最大列索引)
        """
        logger.info(f"获取工作表 '{sheet_name}' 的实际数据范围")
        
        # 先获取工作表的最大范围
        max_row, max_col = self.get_sheet_max_dimensions(sheet_name)
        
        if not exclude_empty_rows and not exclude_empty_cols:
            return max_row, max_col
        
        # 查找实际的数据边界
        actual_max_row = max_row
        actual_max_col = max_col
        
        try:
            # 从起始行向后查找空行
            if exclude_empty_rows:
                for row in range(self.start_row, max_row, 1):
                    row_has_data = False
                    for col in range(1, max_col + 1):
                        cell_value = self.read_cell(sheet_name, row, col)
                        if with_end_flag:
                            if cell_value is not None and str(cell_value).upper() == 'END':
                                actual_max_row = row
                                break
                        if cell_value is not None and str(cell_value).strip():
                            row_has_data = True
                            break
                    if not row_has_data:
                        actual_max_row = row
                        break

            logger.debug(f"工作表 '{sheet_name}' 实际数据范围: 行={actual_max_row}, 列={actual_max_col}")
            return actual_max_row, max_col
            
        except Exception as e:
            logger.error(f"获取实际数据范围失败: {str(e)}")
            # 发生错误时返回原始最大范围
            return max_row, max_col
