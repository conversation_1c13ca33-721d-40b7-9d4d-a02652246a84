#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：TestcaseRAG
@File    ：changepoint.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2025/4/28 15:36
@Desc    : 说明
"""
from loguru import logger
from sdw_agent.service.test_view_generate.utils.excel_util import ReadExcel


class Changepoint:
    @staticmethod
    def import_changepoint(file_path):
        try:
            path_type = "changepoints"
            save_path, res = ReadExcel.parse_excel(path_type, file_path)
            parsed_res = []
            for i in range(len(res)):
                item = res[i]
                item["id"] = i + 1
                item["changeNo"] = item["ARチケットNO"] if "ARチケットNO" in item else ""
                item["summary"] = item["概要"] if "概要" in item else ""
                item["requirementMd"] = item["要件需求文档名称"] if "要件需求文档名称" in item else ""
                parsed_res.append(item)
            return True, parsed_res
        except Exception as e:
            logger.error(f"解析变更点失败:{e}")
            return False, "解析变更点失败"


if __name__ == '__main__':
    file_path = r"D:\projects\DEV_Agent\src\sdw_agent\service\checklist\changepoint.xlsx"
    _, point_res = Changepoint.import_changepoint(file_path)
    print(point_res)
