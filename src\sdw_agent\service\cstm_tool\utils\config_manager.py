"""
CSTM工具配置管理器

提供单例模式的配置读取功能，用于读取config.yaml文件中的配置信息
"""

import os
import yaml
import threading
from typing import Any, Dict, Optional
from pathlib import Path


class ConfigManager:
    """
    CSTM工具配置管理器 - 单例模式

    用于读取和管理config.yaml文件中的配置信息
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化配置管理器"""
        if not hasattr(self, '_initialized'):
            self._config_data = None
            self._config_file_path = None
            self._last_modified = None
            self._initialized = True
            self._load_config()

    @property
    def config(self) -> Dict[str, Any]:
        """
        获取配置数据的属性

        Returns:
            Dict[str, Any]: 配置数据字典

        Examples:
            config_manager = ConfigManager()
            name = config_manager.config.get('name')
            output_path = config_manager.config.get('workflow_config', {}).get('output', {}).get('path')
        """
        self._check_and_reload()
        return self._config_data or {}

    def _get_config_file_path(self) -> str:
        """获取配置文件路径"""
        if self._config_file_path is None:
            # 获取当前文件所在目录的父目录，然后找到config.yaml
            current_dir = Path(__file__).parent
            config_path = current_dir.parent / "config.yaml"
            self._config_file_path = str(config_path.absolute())
        return self._config_file_path

    def _load_config(self) -> None:
        """加载配置文件"""
        config_path = self._get_config_file_path()

        try:
            if not os.path.exists(config_path):
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

            # 检查文件修改时间
            current_modified = os.path.getmtime(config_path)

            # 如果文件没有修改且已经加载过，则不重新加载
            if (self._config_data is not None and
                self._last_modified is not None and
                current_modified == self._last_modified):
                return

            # 读取YAML文件
            with open(config_path, 'r', encoding='utf-8') as file:
                self._config_data = yaml.safe_load(file)

            self._last_modified = current_modified

        except Exception as e:
            raise Exception(f"加载配置文件失败: {e}")

    def _check_and_reload(self) -> None:
        """检查文件是否修改，如果修改则重新加载"""
        config_path = self._get_config_file_path()

        if os.path.exists(config_path):
            current_modified = os.path.getmtime(config_path)
            if (self._last_modified is None or
                current_modified != self._last_modified):
                self._load_config()

    def reload_config(self) -> None:
        """强制重新加载配置文件"""
        with self._lock:
            self._config_data = None
            self._last_modified = None
            self._load_config()

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值（支持点号分隔的路径）

        Args:
            key_path: 配置键路径，使用点号分隔，如 'workflow_config.output.path'
            default: 默认值，当键不存在时返回

        Returns:
            配置值或默认值

        Examples:
            config_manager.get('name')  # 获取顶级键
            config_manager.get('workflow_config.output.path')  # 获取嵌套键
        """
        # 检查是否需要重新加载配置
        self._check_and_reload()

        # 按点号分割路径
        keys = key_path.split('.')
        current_data = self._config_data

        try:
            for key in keys:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    return default
            return current_data
        except (KeyError, TypeError):
            return default

    def get_scl_fields(self, process_newlines: bool = True) -> Dict[str, list]:
        """
        获取SCL字段配置，支持处理换行符

        Args:
            process_newlines: 是否处理字符串中的\\n转换为真正的换行符

        Returns:
            Dict[str, list]: SCL字段配置
        """
        scl_fields = self.get('workflow_config.scl_fields', {
            'fields': [],
            'field_keys': []
        })

        if process_newlines and 'fields' in scl_fields:
            # 处理fields中的\\n转换为真正的换行符
            processed_fields = []
            for field in scl_fields['fields']:
                if isinstance(field, str):
                    # 将\\n替换为真正的换行符
                    processed_field = field.replace('\\n', '\n')
                    processed_fields.append(processed_field)
                else:
                    processed_fields.append(field)
            scl_fields['fields'] = processed_fields

        return scl_fields


# 提供一个全局实例，方便直接使用
config_manager = ConfigManager()
