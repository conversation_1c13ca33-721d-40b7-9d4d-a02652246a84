# 新变表更新工作流配置

# 基本配置
name: "新变表更新"
description: "提供新变表更新功能，检查代码中新变表的更新内容"
version: "1.0.0"
author: "SDW-Team"

#用户配置
user_config:
  ncl_gerrit_url: "http://************:8080"
  ncl_gerrit_username: "cr_robot"
  ncl_gerrit_password: "ocsa@2024!"

# 处理参数
ncl_update:
  excel:
    start_row: 8
    columns:
      aw: 49  # AV列
      p: 16
    sheet_name: "機能一覧と新規・変更内容"
    default_values:
      display_size: "12.3"
      initial_status: "要help"
      vehicle_code: "-"
      sqa_status: "-"
      change_scope: "要help"
      pf_change: "無"
    field_mapping:
      Display_size: 16
      ソフトの変更内容: 17
      変更対象コンポーネント: 18
      新変定移植: 19
      車両コード: 20
      SQA完了状況: 21
      行数: 22
      変更範囲: 23
      PF同時変更: 25
      PF同時変更2: 26

# LLM配置
llm:
  system_prompt:
    - |-
      ## 变更后代码
      {{code_after}}

      ## 完整代码
      {{code_full}}

      ## 变更类型定义
      - 变更：非配置类代码修改（函数实现、逻辑流程等）
      - 定数：配置类修改（宏定义、常量定义等）
      - 注释修改不属于上述两类

      ## 判断规则
      1. 如果修改涉及函数体、变量声明、控制流程等核心逻辑，判定为"变更"
      2. 如果修改仅涉及宏定义、常量值、枚举值等配置项，判定为"定数"
      3. 如果修改仅涉及注释，则不属于有效变更

      ## 输出要求
      - 必须严格输出"变更"、"定数"或"无变更"三者之一
      - 不得输出解释性文字
      - 忽略注释修改的影响

      ## 示例
      修改前：#define MAX_LEN 10
      修改后：#define MAX_LEN 20 → 输出"定数"

      修改前：int func() { return 0; }
      修改后：int func() { return 1; } → 输出"变更"

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {message}" 