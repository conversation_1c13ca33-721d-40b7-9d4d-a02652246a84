# 汽车软件开发AI辅助工作流自动化系统架构文档

## 1. 系统概述

本系统是一个基于V字开发流程的汽车软件开发AI辅助工作流自动化系统，旨在通过AI技术和自动化工具提高汽车软件开发的效率和质量。

### 1.1 核心特性

- **自然语言交互**：用户可以通过自然语言描述需求，系统自动理解并执行相应的工作流
- **模块化设计**：每个开发步骤都是独立的模块，支持灵活组合和扩展
- **并行执行**：支持多个独立步骤的并行执行，提高处理效率
- **智能化处理**：通过AI Agent实现智能分析和决策
- **完整的V字流程**：覆盖从需求分析到测试验证的完整开发周期

## 2. 系统架构

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────┐
│                    用户接口层                             │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐ │
│  │ 自然语言输入 │  │ Web API      │  │ 命令行接口      │ │
│  └─────────────┘  └──────────────┘  └────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    任务规划层                             │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐ │
│  │ Query解析器  │  │ 意图识别      │  │ 任务规划器      │ │
│  └─────────────┘  └──────────────┘  └────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    工作流引擎层                           │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐ │
│  │ 工作流编排器 │  │ 执行调度器    │  │ 并行执行器      │ │
│  └─────────────┘  └──────────────┘  └────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    步骤执行层                             │
│  ┌─────────────────────────┐  ┌───────────────────────┐ │
│  │      DEV阶段步骤          │  │     TEST阶段步骤       │ │
│  │  ├─ Agent步骤 (AI分析)   │  │  ├─ Agent步骤         │ │
│  │  └─ Tool步骤 (工具调用)  │  │  └─ Tool步骤          │ │
│  └─────────────────────────┘  └───────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    数据存储层                             │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────┐ │
│  │ 配置存储     │  │ 执行结果存储  │  │ 日志存储        │ │
│  └─────────────┘  └──────────────┘  └────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 基础组件 (`src/core/base.py`)

- **BaseStep**: 所有步骤的抽象基类
- **AgentStep**: AI智能分析步骤的基类
- **ToolStep**: 工具调用步骤的基类
- **StepInput/StepOutput**: 步骤输入输出的标准数据结构

#### 2.2.2 工作流引擎 (`src/core/workflow.py`)

- **WorkflowEngine**: 核心执行引擎，管理步骤注册和工作流执行
- **WorkflowScheduler**: 支持条件执行和动态调度的调度器
- **WorkflowExecutionPlan**: 执行计划，包含依赖分析和并行组计算

#### 2.2.3 自然语言处理 (`src/core/nlp_planner.py`)

- **NLPQueryParser**: 解析用户的自然语言查询
- **TaskPlanner**: 根据查询生成执行计划

## 3. 步骤定义

### 3.1 DEV阶段步骤 (19个)

| ID | 步骤名称 | 类型 | 描述 | 依赖 |
|---|---------|------|------|------|
| dev_1 | 要求规格读取 | Agent | 分析SCL和要件一览表 | - |
| dev_2 | 法规确认 | Agent | 判定法规符合性 | dev_1 |
| dev_3 | 软件设计标准CS（需求分析） | Agent | 匹配设计库内容 | dev_1, dev_2 |
| dev_4 | 功能一览与变更内容制作 | Tool | 生成功能一览表 | dev_1, dev_2, dev_3 |
| dev_5 | 软件设计标准CS（基本设计） | Agent | 生成设计方针 | dev_3, dev_4 |
| dev_6 | 通信故障安全CS | Agent | 提取CAN信号信息 | dev_5 |
| dev_7 | 软件设计书制作 | Tool | 制作设计文档 | dev_5, dev_6 |
| dev_8 | I/F整合性确认 | Agent | 验证接口正确性 | dev_7 |
| dev_9 | CAN入出力一览确认 | Agent | 确认CAN IO | dev_6 |
| dev_10 | 函数式样书制作 | Tool | 生成函数文档 | dev_5 |
| dev_11 | RAM设计书制作 | Tool | 生成RAM设计 | dev_5 |
| dev_12 | 软件设计标准CS（详细设计） | Agent | 详细设计实施 | dev_5, dev_10, dev_11 |
| dev_13 | 设计同行评审 | Agent | 检查成果物完整性 | dev_12 |
| dev_14 | 功能一览与变更内容更新 | Tool | 更新功能表 | dev_13 |
| dev_15 | 编码标准确认 | Agent | 代码规则检查 | dev_12 |
| dev_16 | 文件比较实施 | Agent | 代码差分分析 | dev_15 |
| dev_17 | 自我检查 | Tool | Gerrit检查 | dev_16 |
| dev_18 | 代码审查 | Agent | AI代码审查 | dev_15, dev_17 |
| dev_19 | 软件设计标准CS（代码检查） | Agent | 代码标准检查 | dev_18 |

### 3.2 TEST阶段步骤 (6个)

| ID | 步骤名称 | 类型 | 描述 | 依赖 |
|---|---------|------|------|------|
| test_1 | 检查表IV确认 | Agent | Checklist4验证 | dev_19 |
| test_2 | 检查规格书审查 | Agent | 测试用例评审 | test_1 |
| test_3 | 检查表V确认 | Agent | Checklist5验证 | test_2 |
| test_4 | 软件设计标准CS（耦合检查） | Agent | 功能关联性检查 | test_3 |
| test_5 | 通信检查 | Agent | 生成通信测试用例 | dev_6, dev_9 |
| test_6 | 真实设备评估 | Tool | Bug提交和记录 | test_5 |

## 4. 工作流定义

### 4.1 预定义工作流

1. **full_workflow**: 完整V字开发流程
2. **dev_workflow**: 仅DEV阶段流程
3. **test_workflow**: 仅TEST阶段流程
4. **design_standard_workflow**: 设计标准CS相关流程
5. **communication_workflow**: 通信检查相关流程
6. **code_review_workflow**: 代码审查流程

### 4.2 工作流配置格式

```yaml
workflows:
  workflow_id:
    id: workflow_id
    name: 工作流名称
    description: 工作流描述
    steps:
      - step_id_1
      - step_id_2
      - ...
```

## 5. 自然语言理解

### 5.1 意图识别

系统支持以下意图类型：

- **design_standard**: 设计标准相关查询
- **test_case**: 测试用例相关查询
- **document**: 文档生成相关查询
- **code_review**: 代码审查相关查询
- **full_process**: 完整流程执行

### 5.2 实体提取

- 变更需求标识 (change_request)
- 文件路径 (file_paths)
- Commit ID (commit_id)

## 6. 执行流程

### 6.1 自然语言查询处理流程

```
用户输入 → Query解析 → 意图识别 → 实体提取 → 任务规划 → 步骤扩展 → 执行计划 → 并行/顺序执行 → 结果汇总
```

### 6.2 并行执行策略

- 自动分析步骤间的依赖关系
- 将无依赖关系的步骤分组并行执行
- 使用线程池控制并行度

## 7. 扩展性设计

### 7.1 添加新步骤

1. 继承 `AgentStep` 或 `ToolStep`
2. 实现必要的方法（`validate_input`, `analyze`/`call_tool`）
3. 在步骤注册表中注册新步骤

### 7.2 添加新工作流

1. 在 `workflows.yaml` 中定义新工作流
2. 指定包含的步骤和执行顺序

### 7.3 扩展NLP能力

1. 在 `intent_patterns` 中添加新的意图模式
2. 在 `step_keywords` 中添加步骤关键词映射

## 8. 配置管理

### 8.1 系统配置 (`config/system.yaml`)

- 引擎配置：并行度、超时、重试
- 日志配置：级别、格式、输出
- 存储配置：工作目录、输出目录
- AI配置：模型选择、API设置

### 8.2 工作流配置 (`config/workflows.yaml`)

- 预定义工作流定义
- 步骤元数据

## 9. 使用示例

### 9.1 自然语言查询

```python
system = AutoWorkflowSystem()
result = system.process_query("给出变更需求xxx的设计标准CS与法规确认结果")
```

### 9.2 直接执行工作流

```python
initial_data = {
    "scl_file": "path/to/scl.xlsx",
    "requirements_list": "path/to/requirements.xlsx"
}
result = system.execute_workflow("dev_workflow", initial_data)
```

### 9.3 条件执行

```python
conditions = {
    "dev_2": lambda data: True,  # 总是执行
    "test_5": lambda data: "communication_change" in data  # 条件执行
}
result = system.scheduler.schedule_conditional("full_workflow", conditions, data)
```

## 10. 部署建议

### 10.1 环境要求

- Python 3.8+
- 足够的内存用于并行执行
- AI API访问权限（如OpenAI）

### 10.2 性能优化

- 调整 `max_parallel_workers` 以优化并行度
- 使用缓存减少重复的AI调用
- 监控步骤执行时间，优化慢速步骤

### 10.3 安全考虑

- API密钥管理
- 数据加密存储
- 访问控制和审计日志

## 11. 未来展望

- 支持更多的自然语言模式
- 集成更多的开发工具
- 提供可视化的工作流设计器
- 支持分布式执行
- 增强的错误恢复机制 