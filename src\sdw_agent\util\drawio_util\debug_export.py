#!/usr/bin/env python3
"""
调试版本：检查 Draw.io 导出到 Excel 的每个步骤
"""

import os
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger
from sdw_agent.util.excel.core import ExcelUtil, CellStyle
from sdw_agent.service.template_manager import template_manager


def debug_export_step_by_step():
    """逐步调试导出过程"""
    print("🔍 调试 Draw.io 导出过程")
    print("=" * 50)
    
    # 1. 检查 Playwright
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装")
        return
    
    # 2. 获取 Draw.io 文件
    drawio_file = template_manager.get_template_path("block_diagram_file")
    if not drawio_file or not Path(drawio_file).exists():
        print("❌ 未找到 Draw.io 文件")
        return
    
    print(f"✅ Draw.io 文件: {drawio_file}")
    
    # 3. 读取文件内容
    try:
        with open(drawio_file, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        print(f"✅ 文件读取成功，大小: {len(xml_content)} 字符")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return
    
    # 4. 创建输出目录和文件
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    temp_png = output_dir / "debug_temp_export.png"
    excel_file = output_dir / "debug_export_test.xlsx"
    
    print(f"📁 临时 PNG: {temp_png}")
    print(f"📊 Excel 文件: {excel_file}")
    
    # 5. 使用 Playwright 导出
    try:
        encoded_content = urllib.parse.quote(xml_content)
        drawio_url = f"https://app.diagrams.net/?lightbox=1&edit=_blank&layers=1&nav=1&title=debug#R{encoded_content}"
        
        print(f"🌐 URL 长度: {len(drawio_url)} 字符")
        print("🚀 启动 Playwright...")
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.set_viewport_size({"width": 1920, "height": 1080})
            
            print("🌐 访问 draw.io...")
            page.goto(drawio_url, timeout=60000)
            
            print("⏳ 等待页面加载...")
            page.wait_for_timeout(8000)
            
            print("📸 开始截图...")
            page.screenshot(path=str(temp_png), full_page=True, type="png")
            browser.close()
        
        # 检查 PNG 文件
        if temp_png.exists():
            file_size = temp_png.stat().st_size
            print(f"✅ PNG 文件生成成功")
            print(f"📊 文件大小: {file_size} 字节")
            
            if file_size == 0:
                print("❌ PNG 文件为空")
                return
        else:
            print("❌ PNG 文件未生成")
            return
            
    except Exception as e:
        print(f"❌ Playwright 导出失败: {e}")
        return
    
    # 6. 测试 Excel 插入
    print("\n📊 测试 Excel 插入...")
    
    try:
        # 删除现有文件
        if excel_file.exists():
            excel_file.unlink()
        
        with ExcelUtil(str(excel_file), auto_create=True) as excel:
            sheet_name = "调试测试"
            
            print(f"📋 创建工作表: {sheet_name}")
            
            # 确保工作表存在
            if sheet_name not in excel.get_sheet_names():
                excel.create_sheet(sheet_name)
            
            # 添加标题
            excel.write_cell(sheet_name, 1, 1, "调试测试 - Draw.io 图表")
            title_style = CellStyle(
                font_size=16,
                font_bold=True,
                alignment_horizontal="center"
            )
            excel.set_cell_style(sheet_name, 1, 1, title_style)
            
            print("✅ 标题添加成功")
            
            # 尝试插入图像 - 方法1：openpyxl
            try:
                from openpyxl.drawing.image import Image as OpenpyxlImage
                
                if hasattr(excel, 'workbook') and excel.workbook:
                    if sheet_name in excel.workbook.sheetnames:
                        ws = excel.workbook[sheet_name]
                    else:
                        ws = excel.workbook.create_sheet(sheet_name)
                    
                    img = OpenpyxlImage(str(temp_png))
                    
                    # 调整大小
                    max_width = 1200
                    if img.width > max_width:
                        scale_factor = max_width / img.width
                        img.width = max_width
                        img.height = int(img.height * scale_factor)
                    
                    # 设置位置
                    target_cell = ws.cell(row=3, column=1)
                    img.anchor = target_cell.coordinate
                    ws.add_image(img)
                    
                    print(f"✅ openpyxl 方式插入成功")
                    print(f"📊 图像尺寸: {img.width} x {img.height}")
                    print(f"📍 位置: {target_cell.coordinate}")
                    
                else:
                    print("❌ 无法获取 openpyxl workbook")
                    
            except Exception as e:
                print(f"❌ openpyxl 方式失败: {e}")
                
                # 尝试方法2：win32com
                try:
                    from sdw_agent.service.func_analyze_book.util.func_book_sheet_oprate_util import insert_image_to_excel
                    
                    if hasattr(excel, 'worksheet') and excel.worksheet:
                        insert_image_to_excel(excel.worksheet, str(temp_png), 3, 1)
                        print("✅ win32com 方式插入成功")
                    else:
                        print("❌ 无法获取 win32com worksheet")
                        
                except Exception as e2:
                    print(f"❌ win32com 方式也失败: {e2}")
            
            # 保存文件
            excel.save()
            print("✅ Excel 文件保存成功")
    
    except Exception as e:
        print(f"❌ Excel 操作失败: {e}")
        return
    
    # 7. 最终检查
    print(f"\n🎯 最终检查:")
    print(f"   PNG 文件: {'✅' if temp_png.exists() else '❌'} {temp_png}")
    print(f"   Excel 文件: {'✅' if excel_file.exists() else '❌'} {excel_file}")
    
    if temp_png.exists():
        print(f"   PNG 大小: {temp_png.stat().st_size} 字节")
    
    if excel_file.exists():
        print(f"   Excel 大小: {excel_file.stat().st_size} 字节")
    
    print(f"\n💡 请手动打开以下文件检查:")
    print(f"   📸 PNG: {temp_png}")
    print(f"   📊 Excel: {excel_file}")
    
    # 不删除临时文件，方便检查
    print(f"\n📝 临时文件已保留用于检查")


if __name__ == "__main__":
    debug_export_step_by_step()
